{"version": 3, "sources": ["../file-saver/src/FileSaver.js", "dep:file-saver"], "sourcesContent": ["/*\n* FileSaver.js\n* A saveAs() FileSaver implementation.\n*\n* By <PERSON>, http://eligrey.com\n*\n* License : https://github.com/eligrey/FileSaver.js/blob/master/LICENSE.md (MIT)\n* source  : http://purl.eligrey.com/github/FileSaver.js\n*/\n\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nvar _global = typeof window === 'object' && window.window === window\n  ? window : typeof self === 'object' && self.self === self\n  ? self : typeof global === 'object' && global.global === global\n  ? global\n  : this\n\nfunction bom (blob, opts) {\n  if (typeof opts === 'undefined') opts = { autoBom: false }\n  else if (typeof opts !== 'object') {\n    console.warn('Deprecated: Expected third argument to be a object')\n    opts = { autoBom: !opts }\n  }\n\n  // prepend BOM for UTF-8 XML and text/* types (including HTML)\n  // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n  if (opts.autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n    return new Blob([String.fromCharCode(0xFEFF), blob], { type: blob.type })\n  }\n  return blob\n}\n\nfunction download (url, name, opts) {\n  var xhr = new XMLHttpRequest()\n  xhr.open('GET', url)\n  xhr.responseType = 'blob'\n  xhr.onload = function () {\n    saveAs(xhr.response, name, opts)\n  }\n  xhr.onerror = function () {\n    console.error('could not download file')\n  }\n  xhr.send()\n}\n\nfunction corsEnabled (url) {\n  var xhr = new XMLHttpRequest()\n  // use sync to avoid popup blocker\n  xhr.open('HEAD', url, false)\n  try {\n    xhr.send()\n  } catch (e) {}\n  return xhr.status >= 200 && xhr.status <= 299\n}\n\n// `a.click()` doesn't work for all browsers (#465)\nfunction click (node) {\n  try {\n    node.dispatchEvent(new MouseEvent('click'))\n  } catch (e) {\n    var evt = document.createEvent('MouseEvents')\n    evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80,\n                          20, false, false, false, false, 0, null)\n    node.dispatchEvent(evt)\n  }\n}\n\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nvar isMacOSWebView = _global.navigator && /Macintosh/.test(navigator.userAgent) && /AppleWebKit/.test(navigator.userAgent) && !/Safari/.test(navigator.userAgent)\n\nvar saveAs = _global.saveAs || (\n  // probably in some web worker\n  (typeof window !== 'object' || window !== _global)\n    ? function saveAs () { /* noop */ }\n\n  // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView\n  : ('download' in HTMLAnchorElement.prototype && !isMacOSWebView)\n  ? function saveAs (blob, name, opts) {\n    var URL = _global.URL || _global.webkitURL\n    var a = document.createElement('a')\n    name = name || blob.name || 'download'\n\n    a.download = name\n    a.rel = 'noopener' // tabnabbing\n\n    // TODO: detect chrome extensions & packaged apps\n    // a.target = '_blank'\n\n    if (typeof blob === 'string') {\n      // Support regular links\n      a.href = blob\n      if (a.origin !== location.origin) {\n        corsEnabled(a.href)\n          ? download(blob, name, opts)\n          : click(a, a.target = '_blank')\n      } else {\n        click(a)\n      }\n    } else {\n      // Support blobs\n      a.href = URL.createObjectURL(blob)\n      setTimeout(function () { URL.revokeObjectURL(a.href) }, 4E4) // 40s\n      setTimeout(function () { click(a) }, 0)\n    }\n  }\n\n  // Use msSaveOrOpenBlob as a second approach\n  : 'msSaveOrOpenBlob' in navigator\n  ? function saveAs (blob, name, opts) {\n    name = name || blob.name || 'download'\n\n    if (typeof blob === 'string') {\n      if (corsEnabled(blob)) {\n        download(blob, name, opts)\n      } else {\n        var a = document.createElement('a')\n        a.href = blob\n        a.target = '_blank'\n        setTimeout(function () { click(a) })\n      }\n    } else {\n      navigator.msSaveOrOpenBlob(bom(blob, opts), name)\n    }\n  }\n\n  // Fallback to using FileReader and a popup\n  : function saveAs (blob, name, opts, popup) {\n    // Open a popup immediately do go around popup blocker\n    // Mostly only available on user interaction and the fileReader is async so...\n    popup = popup || open('', '_blank')\n    if (popup) {\n      popup.document.title =\n      popup.document.body.innerText = 'downloading...'\n    }\n\n    if (typeof blob === 'string') return download(blob, name, opts)\n\n    var force = blob.type === 'application/octet-stream'\n    var isSafari = /constructor/i.test(_global.HTMLElement) || _global.safari\n    var isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent)\n\n    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) && typeof FileReader !== 'undefined') {\n      // Safari doesn't allow downloading of blob URLs\n      var reader = new FileReader()\n      reader.onloadend = function () {\n        var url = reader.result\n        url = isChromeIOS ? url : url.replace(/^data:[^;]*;/, 'data:attachment/file;')\n        if (popup) popup.location.href = url\n        else location = url\n        popup = null // reverse-tabnabbing #460\n      }\n      reader.readAsDataURL(blob)\n    } else {\n      var URL = _global.URL || _global.webkitURL\n      var url = URL.createObjectURL(blob)\n      if (popup) popup.location = url\n      else location.href = url\n      popup = null // reverse-tabnabbing #460\n      setTimeout(function () { URL.revokeObjectURL(url) }, 4E4) // 40s\n    }\n  }\n)\n\n_global.saveAs = saveAs.saveAs = saveAs\n\nif (typeof module !== 'undefined') {\n  module.exports = saveAs;\n}\n", "export default require(\"./node_modules/file-saver/dist/FileSaver.min.js\");"], "mappings": ";;;;;;;;;;;;;;;;;AAkBA,iBAAc,IAAM,IAAM;AAAA,eACJ,AAAhB,OAAO,MAAS,cAAa,KAAO,EAAE,SAAO,UACxB,AAAhB,OAAO,MAAS,YACvB,SAAQ,KAAK,uDACb,KAAO,EAAE,SAAS,CAAC,OAKjB,GAAK,WAAW,6EAA6E,KAAK,GAAK,QAClG,IAAI,KAAK,CAAA,UAA8B,KAAO,EAAE,MAAM,GAAK,UAE7D;;AAGT,iBAAmB,IAAK,IAAM,IAAM;AAClC,YAAI,KAAM,IAAI;AACd,WAAI,KAAK,OAAO,KAChB,GAAI,eAAe,QACnB,GAAI,SAAS,WAAY;AACvB,YAAO,GAAI,UAAU,IAAM;WAE7B,GAAI,UAAU,WAAY;AACxB,kBAAQ,MAAM;WAEhB,GAAI;;AAGN,iBAAsB,IAAK;AACzB,YAAI,KAAM,IAAI;AAEd,WAAI,KAAK,QAAQ,IAAjB;AACA,YAAI;AACF,aAAI;iBACG,IAAP;;AACF,eAAqB,OAAd,GAAI,UAA+B,OAAd,GAAI;;AAIlC,iBAAgB,IAAM;AACpB,YAAI;AACF,aAAK,cAAc,IAAI,WAAW;iBAC3B,IAAP;AACA,cAAI,KAAM,SAAS,YAAY;AAC/B,aAAI,eAAe,SAAnB,MAAA,MAAwC,QAAQ,GAAG,GAAG,GAAG,IACnC,IADtB,OAAA,OAAA,OAAA,OACsD,GAAG,OACzD,GAAK,cAAc;;;AAEtB,UAtDG,IAA4B,AAAlB,OAAO,UAAW,YAAY,OAAO,WAAW,SAC1D,SAAyB,AAAhB,OAAO,QAAS,YAAY,KAAK,SAAS,OACnD,OAAyB,AAAlB,OAAO,UAAW,YAAY,OAAO,WAAW,SACvD,SADO,QAyDP,IAAiB,EAAQ,aAAa,YAAY,KAAK,UAAU,cAAc,cAAc,KAAK,UAAU,cAAc,CAAC,SAAS,KAAK,UAAU,YAEnJ,IAAS,EAAQ,UAEA,CAAlB,OAAO,UAAW,YAAY,WAAW,IACtC,WAAmB;UAGpB,cAAc,kBAAkB,aAAa,CAAC,IAC/C,SAAiB,IAAM,IAAM,GAAM;AAAA,YAC/B,IAAM,EAAQ,OAAO,EAAQ,WAC7B,IAAI,SAAS,cAAc;AAC/B,aAAO,MAAQ,GAAK,QAAQ,YAE5B,EAAE,WAAW,IACb,EAAE,MAAM,YAKY,AAAhB,OAAO,MAAS,WAElB,GAAE,OAAO,IACL,EAAE,WAAW,SAAS,SAKxB,EAAM,KAJN,EAAY,EAAE,QACV,EAAS,IAAM,IAAM,KACrB,EAAM,GAAG,EAAE,SAAS,aAM1B,GAAE,OAAO,EAAI,gBAAgB,KAC7B,WAAW,WAAY;AAAE,YAAI,gBAAgB,EAAE;WAAS,MACxD,WAAW,WAAY;AAAE,YAAM;WAAM;UAKvC,sBAAsB,YACtB,SAAiB,IAAM,IAAM,GAAM;AAGnC,YAFA,KAAO,MAAQ,GAAK,QAAQ,YAER,AAAhB,OAAO,MAAS;AAUlB,oBAAU,iBAAiB,EAAI,IAAM,IAAO;iBATxC,EAAY;AACd,YAAS,IAAM,IAAM;aAChB;AACL,cAAI,IAAI,SAAS,cAAc;AAC/B,YAAE,OAAO,IACT,EAAE,SAAS,UACX,WAAW,WAAY;AAAE,cAAM;;;UAQnC,SAAiB,IAAM,IAAM,IAAM,IAAO;AAS1C,YANA,KAAQ,MAAS,KAAK,IAAI,WACtB,MACF,IAAM,SAAS,QACf,GAAM,SAAS,KAAK,YAAY,mBAGd,AAAhB,OAAO,MAAS;AAAU,iBAAO,EAAS,IAAM,IAAM;AAThB,YAWtC,IAAsB,AAAd,GAAK,SAAS,4BACtB,IAAW,eAAe,KAAK,EAAQ,gBAAgB,EAAQ,QAC/D,IAAc,eAAe,KAAK,UAAU;AAEhD,YAAK,MAAgB,KAAS,KAAa,MAAyC,AAAtB,OAAO,cAAe,aAAa;AAE/F,cAAI,IAAS,IAAI;AACjB,YAAO,YAAY,WAAY;AAC7B,gBAAI,KAAM,EAAO;AACjB,iBAAM,IAAc,KAAM,GAAI,QAAQ,gBAAgB,0BAClD,KAAO,GAAM,SAAS,OAAO,KAC5B,WAAW,IAChB,KAAQ;aAEV,EAAO,cAAc;eAChB;AAAA,cACD,IAAM,EAAQ,OAAO,EAAQ,WAC7B,IAAM,EAAI,gBAAgB;AAC1B,eAAO,GAAM,WAAW,IACvB,SAAS,OAAO,GACrB,KAAQ,MACR,WAAW,WAAY;AAAE,cAAI,gBAAgB;aAAQ;;;AAK3D,QAAQ,SAAS,EAAO,SAAS,GAEX,AAAlB,OAAO,UAAW,eACpB,QAAO,UAAU;;;;;;ACzKnB,IAAO,qBAAQ;", "names": []}