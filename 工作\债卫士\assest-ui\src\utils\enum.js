//人行征信字段数据
export const reportTable = {
  0:{ //基础段
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      name:'姓名',
      idType:'证件类型',
      idNum:'证件号码',
      cimoc:'客户资料维护机构代码',
      customerType:'客户资料类型',
      systemId:'系统编号',
  },
  1:{ //基本概况信息
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      sex:'性别',
      dob:'出生日期',
      nation:'国籍',
      houseAdd:'户籍地址',
      hhDist:'户籍所在地行政区划',
      cellPhone:'手机号码',
      email:'电子邮箱',
  },
  2:{ //账户基础段
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      acctNo:'账户合同号',
      acctType:'账户类型',
      name:'借款人姓名',
      idType:'借款人证件类型',
      idNum:'借款人证件号码',
      mngmtOrgCode:'业务管理机构代码',
  },
  3:{ //账户基本信息段
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      acctNo:'账户合同号',
      busiLines:'借贷业务大类',
      busiDtlLines:'借贷业务种类细分',
      openDate:'开户日期',
      cy:'币种',
      acctCredLine:'信用额度',
      loanAmt:'借款金额',
      flag:'分次放款标志',
      dueDate:'到期日期',
      repayMode:'还款方式',
      repayFreqcy:'还款频率',
      repayPrd:'还款期数',
      guarMode:'担保方式',
      othRepyGuarWay:'其他还款保证方式',
      assetTrandFlag:'资产转让标志',
      fundSou:'业务经营类型',
      loanForm:'贷款发放形式',
      creditId:'卡片标识号',
      firstHouLoanFlag:'是否为首套住房贷款',
      loanConCode:'贷款合同编号',
  },
  4:{ //初始债权说明段
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      acctNo:'账户合同号',
      initCredName:'初始债权人名称',
      initCredOrgNm:'初始债权人机构代码',
      origDbtCate:'原债务种类',
      initRpySts:'债权转移时的还款状态',
  },
  5:{ //非月度表现信息
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      acctNo:'账户合同号',
      acctStatus:'账户状态',
      acctBal:'余额',
      fiveCate:'五级分类',
      fiveCateAdjDate:'五级分类认定日期',
      remRepPrd:'剩余还款期数',
      rpyStatus:'当前还款状态',
      overdPrd:'当前逾期期数',
      totOverd:'当前逾期总额',
      latRpyAmt:'最近一次实际还款金额',
      latRpyDate:'最近一次实际还款日期',
      closeDate:'账户关闭日期',
  },
  6:{ //教育信息
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      eduLevel:'学历',
      acaDegree:'学位',
  },
  7:{ //职业信息
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      empStatus:'就业状态',
      cpnname:'单位名称',
      cpnType:'单位性质',
      industry:'单位所属行业',
      cpnAddr:'单位详细地址',
      cpnDist:'单位所在地行政区划',
      cpnTel:'单位电话',
      occupation:'职业',
      title:'职务',
      techTitle:'职称',
      workStartDate:'本单位工作起始年份',
  },
  8:{ //通讯信息
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      mailAddr:'通讯地址',
      mailPc:'通讯地邮编',
      mailDist:'通讯地行政区划',
  },
  9:{ //居住地址信息
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      resiStatus:'居住状况',
      resiAddr:'居住地详细地址',
      resiPc:'居住地邮编',
      resiDist:'居住地行政区划',
      homeTel:'住宅电话',
  },
  10:{ //婚姻信息
      caseId:'客户号（案件ID）',
      deptCode:'机构代码',
      corpCode:'法人机构',
      infoUpDate:'业务发生日期',
      mariStatus:'婚姻状况',
      spoName:'配偶姓名',
      spoIdType:'配偶证件类型',
      spoIdNum:'配偶证件号码',
      spoTel:'配偶联系电话',
      spsCmpyNm:'配偶工作单位',
  },
  11:undefined,
  12:undefined,
  13:undefined
}

// 排序字段
export const orderEnum = { ascending: 1, descending: 2, }

// 案件状态
export const caseStatusEnum = { 0: '未分配', 1: '已分配', 2: '停催', 3: '留案', 4: '退案', 5: '回收案件', 6: '案件结清' }

// 通话状态
export const callStatusEnum = { 0: '已接通', 1: '未接通', }

// 短信发送状态
export const sendStatusEnum = { 0: '已发送', 1: '发送失败', 2: '未发送', 3: '发送中' }

// 执行时间
export const executionTimeEnum = { 0: '每天执行', 1: '周一至周五执行' }

// 分配模式
export const allocatWayEnum = { 0: '快速分案', 1: '规则分案', 2: '共债分案', 3: '模板分案' }

// 分配模式
export const approveStatusEnum = { 0: '已同意', 1: '未同意', 2: '待处理', 4: '已退案关闭' }

// 物流状态
export const logisticsStatusEnum = { 0: '运输中', 1: '揽收', 2: '疑难', 3: '已签收', 4: '退签', 5: '派件', 6: '退回', 7: '转投', 10: '待清关', 11: '清关中', 12: '已清关', 13: '清关异常', 14: '已拒签' }

// 呼叫类型
export const callOutEnum = { callOutManual: '呼出', clickCallOut: '呼出', outbound: '呼出', inbound: '呼入' }

// 立项类型
export const erectNapeEnum = { 0: '项目立项', 1: '尽调方案', 2: '尽调实施', 3: '资产估值', 4: '初步评估', 5: '处置预案', 6: '尽调报告', }

// 禁用/启用
export const switchStatusEnum = { 0: '启用', 1: '禁用' }

// 是否
export const isNoEnum = { 0: '否', 1: '是' }

// 复检状态
export const recheckEnum = { 1: "正常", 2: "风险", }

// 质检状态
export const qualityStatusEnum = { 0: "未质检", 1: "已质检", 2: "已复检", }

// 录音类型
export const recordingTypeEnum = { 0: "沟通", 1: "客服", }
