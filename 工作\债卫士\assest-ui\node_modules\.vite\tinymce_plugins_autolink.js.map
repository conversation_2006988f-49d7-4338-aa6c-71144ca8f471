{"version": 3, "sources": ["../tinymce/plugins/autolink/plugin.js", "../tinymce/plugins/autolink/index.js", "dep:tinymce_plugins_autolink"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var checkRange = function (str, substr, start) {\n      return substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    };\n    var contains = function (str, substr) {\n      return str.indexOf(substr) !== -1;\n    };\n    var startsWith = function (str, prefix) {\n      return checkRange(str, prefix, 0);\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var link = function () {\n      return /(?:[A-Za-z][A-Za-z\\d.+-]{0,14}:\\/\\/(?:[-.~*+=!&;:'%@?^${}(),\\w]+@)?|www\\.|[-;:&=+$,.\\w]+@)[A-Za-z\\d-]+(?:\\.[A-Za-z\\d-]+)*(?::\\d+)?(?:\\/(?:[-.~*+=!;:'%@$(),\\/\\w]*[-~*+=%@$()\\/\\w])?)?(?:\\?(?:[-.~*+=!&;:'%@?^${}(),\\/\\w]+))?(?:#(?:[-.~*+=!&;:'%@?^${}(),\\/\\w]+))?/g;\n    };\n\n    var defaultLinkPattern = new RegExp('^' + link().source + '$', 'i');\n    var getAutoLinkPattern = function (editor) {\n      return editor.getParam('autolink_pattern', defaultLinkPattern);\n    };\n    var getDefaultLinkTarget = function (editor) {\n      return editor.getParam('default_link_target', false);\n    };\n    var getDefaultLinkProtocol = function (editor) {\n      return editor.getParam('link_default_protocol', 'http', 'string');\n    };\n\n    var rangeEqualsBracketOrSpace = function (rangeString) {\n      return /^[(\\[{ \\u00a0]$/.test(rangeString);\n    };\n    var isTextNode = function (node) {\n      return node.nodeType === 3;\n    };\n    var isElement = function (node) {\n      return node.nodeType === 1;\n    };\n    var handleBracket = function (editor) {\n      return parseCurrentLine(editor, -1);\n    };\n    var handleSpacebar = function (editor) {\n      return parseCurrentLine(editor, 0);\n    };\n    var handleEnter = function (editor) {\n      return parseCurrentLine(editor, -1);\n    };\n    var scopeIndex = function (container, index) {\n      if (index < 0) {\n        index = 0;\n      }\n      if (isTextNode(container)) {\n        var len = container.data.length;\n        if (index > len) {\n          index = len;\n        }\n      }\n      return index;\n    };\n    var setStart = function (rng, container, offset) {\n      if (!isElement(container) || container.hasChildNodes()) {\n        rng.setStart(container, scopeIndex(container, offset));\n      } else {\n        rng.setStartBefore(container);\n      }\n    };\n    var setEnd = function (rng, container, offset) {\n      if (!isElement(container) || container.hasChildNodes()) {\n        rng.setEnd(container, scopeIndex(container, offset));\n      } else {\n        rng.setEndAfter(container);\n      }\n    };\n    var hasProtocol = function (url) {\n      return /^([A-Za-z][A-Za-z\\d.+-]*:\\/\\/)|mailto:/.test(url);\n    };\n    var isPunctuation = function (char) {\n      return /[?!,.;:]/.test(char);\n    };\n    var parseCurrentLine = function (editor, endOffset) {\n      var end, endContainer, bookmark, text, prev, len, rngText;\n      var autoLinkPattern = getAutoLinkPattern(editor);\n      var defaultLinkTarget = getDefaultLinkTarget(editor);\n      if (editor.dom.getParent(editor.selection.getNode(), 'a[href]') !== null) {\n        return;\n      }\n      var rng = editor.selection.getRng().cloneRange();\n      if (rng.startOffset < 5) {\n        prev = rng.endContainer.previousSibling;\n        if (!prev) {\n          if (!rng.endContainer.firstChild || !rng.endContainer.firstChild.nextSibling) {\n            return;\n          }\n          prev = rng.endContainer.firstChild.nextSibling;\n        }\n        len = prev.length;\n        setStart(rng, prev, len);\n        setEnd(rng, prev, len);\n        if (rng.endOffset < 5) {\n          return;\n        }\n        end = rng.endOffset;\n        endContainer = prev;\n      } else {\n        endContainer = rng.endContainer;\n        if (!isTextNode(endContainer) && endContainer.firstChild) {\n          while (!isTextNode(endContainer) && endContainer.firstChild) {\n            endContainer = endContainer.firstChild;\n          }\n          if (isTextNode(endContainer)) {\n            setStart(rng, endContainer, 0);\n            setEnd(rng, endContainer, endContainer.nodeValue.length);\n          }\n        }\n        if (rng.endOffset === 1) {\n          end = 2;\n        } else {\n          end = rng.endOffset - 1 - endOffset;\n        }\n      }\n      var start = end;\n      do {\n        setStart(rng, endContainer, end >= 2 ? end - 2 : 0);\n        setEnd(rng, endContainer, end >= 1 ? end - 1 : 0);\n        end -= 1;\n        rngText = rng.toString();\n      } while (!rangeEqualsBracketOrSpace(rngText) && end - 2 >= 0);\n      if (rangeEqualsBracketOrSpace(rng.toString())) {\n        setStart(rng, endContainer, end);\n        setEnd(rng, endContainer, start);\n        end += 1;\n      } else if (rng.startOffset === 0) {\n        setStart(rng, endContainer, 0);\n        setEnd(rng, endContainer, start);\n      } else {\n        setStart(rng, endContainer, end);\n        setEnd(rng, endContainer, start);\n      }\n      text = rng.toString();\n      if (isPunctuation(text.charAt(text.length - 1))) {\n        setEnd(rng, endContainer, start - 1);\n      }\n      text = rng.toString().trim();\n      var matches = text.match(autoLinkPattern);\n      var protocol = getDefaultLinkProtocol(editor);\n      if (matches) {\n        var url = matches[0];\n        if (startsWith(url, 'www.')) {\n          url = protocol + '://' + url;\n        } else if (contains(url, '@') && !hasProtocol(url)) {\n          url = 'mailto:' + url;\n        }\n        bookmark = editor.selection.getBookmark();\n        editor.selection.setRng(rng);\n        editor.execCommand('createlink', false, url);\n        if (defaultLinkTarget !== false) {\n          editor.dom.setAttrib(editor.selection.getNode(), 'target', defaultLinkTarget);\n        }\n        editor.selection.moveToBookmark(bookmark);\n        editor.nodeChanged();\n      }\n    };\n    var setup = function (editor) {\n      var autoUrlDetectState;\n      editor.on('keydown', function (e) {\n        if (e.keyCode === 13) {\n          return handleEnter(editor);\n        }\n      });\n      if (global.browser.isIE()) {\n        editor.on('focus', function () {\n          if (!autoUrlDetectState) {\n            autoUrlDetectState = true;\n            try {\n              editor.execCommand('AutoUrlDetect', false, true);\n            } catch (ex) {\n            }\n          }\n        });\n        return;\n      }\n      editor.on('keypress', function (e) {\n        if (e.keyCode === 41 || e.keyCode === 93 || e.keyCode === 125) {\n          return handleBracket(editor);\n        }\n      });\n      editor.on('keyup', function (e) {\n        if (e.keyCode === 32) {\n          return handleSpacebar(editor);\n        }\n      });\n    };\n\n    function Plugin () {\n      global$1.add('autolink', function (editor) {\n        setup(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"autolink\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/autolink')\n//   ES2015:\n//     import 'tinymce/plugins/autolink'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/autolink/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,aAAa,SAAU,KAAK,QAAQ,OAAO;AAC7C,eAAO,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,YAAY;AAAA;AAEtG,UAAI,WAAW,SAAU,KAAK,QAAQ;AACpC,eAAO,IAAI,QAAQ,YAAY;AAAA;AAEjC,UAAI,aAAa,SAAU,KAAK,QAAQ;AACtC,eAAO,WAAW,KAAK,QAAQ;AAAA;AAGjC,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAGT,UAAI,qBAAqB,IAAI,OAAO,MAAM,OAAO,SAAS,KAAK;AAC/D,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS,oBAAoB;AAAA;AAE7C,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,OAAO,SAAS,uBAAuB;AAAA;AAEhD,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS,yBAAyB,QAAQ;AAAA;AAG1D,UAAI,4BAA4B,SAAU,aAAa;AACrD,eAAO,kBAAkB,KAAK;AAAA;AAEhC,UAAI,aAAa,SAAU,MAAM;AAC/B,eAAO,KAAK,aAAa;AAAA;AAE3B,UAAI,YAAY,SAAU,MAAM;AAC9B,eAAO,KAAK,aAAa;AAAA;AAE3B,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,iBAAiB,QAAQ;AAAA;AAElC,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,iBAAiB,QAAQ;AAAA;AAElC,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,iBAAiB,QAAQ;AAAA;AAElC,UAAI,aAAa,SAAU,WAAW,OAAO;AAC3C,YAAI,QAAQ,GAAG;AACb,kBAAQ;AAAA;AAEV,YAAI,WAAW,YAAY;AACzB,cAAI,MAAM,UAAU,KAAK;AACzB,cAAI,QAAQ,KAAK;AACf,oBAAQ;AAAA;AAAA;AAGZ,eAAO;AAAA;AAET,UAAI,WAAW,SAAU,KAAK,WAAW,QAAQ;AAC/C,YAAI,CAAC,UAAU,cAAc,UAAU,iBAAiB;AACtD,cAAI,SAAS,WAAW,WAAW,WAAW;AAAA,eACzC;AACL,cAAI,eAAe;AAAA;AAAA;AAGvB,UAAI,SAAS,SAAU,KAAK,WAAW,QAAQ;AAC7C,YAAI,CAAC,UAAU,cAAc,UAAU,iBAAiB;AACtD,cAAI,OAAO,WAAW,WAAW,WAAW;AAAA,eACvC;AACL,cAAI,YAAY;AAAA;AAAA;AAGpB,UAAI,cAAc,SAAU,KAAK;AAC/B,eAAO,yCAAyC,KAAK;AAAA;AAEvD,UAAI,gBAAgB,SAAU,MAAM;AAClC,eAAO,WAAW,KAAK;AAAA;AAEzB,UAAI,mBAAmB,SAAU,QAAQ,WAAW;AAClD,YAAI,KAAK,cAAc,UAAU,MAAM,MAAM,KAAK;AAClD,YAAI,kBAAkB,mBAAmB;AACzC,YAAI,oBAAoB,qBAAqB;AAC7C,YAAI,OAAO,IAAI,UAAU,OAAO,UAAU,WAAW,eAAe,MAAM;AACxE;AAAA;AAEF,YAAI,MAAM,OAAO,UAAU,SAAS;AACpC,YAAI,IAAI,cAAc,GAAG;AACvB,iBAAO,IAAI,aAAa;AACxB,cAAI,CAAC,MAAM;AACT,gBAAI,CAAC,IAAI,aAAa,cAAc,CAAC,IAAI,aAAa,WAAW,aAAa;AAC5E;AAAA;AAEF,mBAAO,IAAI,aAAa,WAAW;AAAA;AAErC,gBAAM,KAAK;AACX,mBAAS,KAAK,MAAM;AACpB,iBAAO,KAAK,MAAM;AAClB,cAAI,IAAI,YAAY,GAAG;AACrB;AAAA;AAEF,gBAAM,IAAI;AACV,yBAAe;AAAA,eACV;AACL,yBAAe,IAAI;AACnB,cAAI,CAAC,WAAW,iBAAiB,aAAa,YAAY;AACxD,mBAAO,CAAC,WAAW,iBAAiB,aAAa,YAAY;AAC3D,6BAAe,aAAa;AAAA;AAE9B,gBAAI,WAAW,eAAe;AAC5B,uBAAS,KAAK,cAAc;AAC5B,qBAAO,KAAK,cAAc,aAAa,UAAU;AAAA;AAAA;AAGrD,cAAI,IAAI,cAAc,GAAG;AACvB,kBAAM;AAAA,iBACD;AACL,kBAAM,IAAI,YAAY,IAAI;AAAA;AAAA;AAG9B,YAAI,QAAQ;AACZ,WAAG;AACD,mBAAS,KAAK,cAAc,OAAO,IAAI,MAAM,IAAI;AACjD,iBAAO,KAAK,cAAc,OAAO,IAAI,MAAM,IAAI;AAC/C,iBAAO;AACP,oBAAU,IAAI;AAAA,iBACP,CAAC,0BAA0B,YAAY,MAAM,KAAK;AAC3D,YAAI,0BAA0B,IAAI,aAAa;AAC7C,mBAAS,KAAK,cAAc;AAC5B,iBAAO,KAAK,cAAc;AAC1B,iBAAO;AAAA,mBACE,IAAI,gBAAgB,GAAG;AAChC,mBAAS,KAAK,cAAc;AAC5B,iBAAO,KAAK,cAAc;AAAA,eACrB;AACL,mBAAS,KAAK,cAAc;AAC5B,iBAAO,KAAK,cAAc;AAAA;AAE5B,eAAO,IAAI;AACX,YAAI,cAAc,KAAK,OAAO,KAAK,SAAS,KAAK;AAC/C,iBAAO,KAAK,cAAc,QAAQ;AAAA;AAEpC,eAAO,IAAI,WAAW;AACtB,YAAI,UAAU,KAAK,MAAM;AACzB,YAAI,WAAW,uBAAuB;AACtC,YAAI,SAAS;AACX,cAAI,MAAM,QAAQ;AAClB,cAAI,WAAW,KAAK,SAAS;AAC3B,kBAAM,WAAW,QAAQ;AAAA,qBAChB,SAAS,KAAK,QAAQ,CAAC,YAAY,MAAM;AAClD,kBAAM,YAAY;AAAA;AAEpB,qBAAW,OAAO,UAAU;AAC5B,iBAAO,UAAU,OAAO;AACxB,iBAAO,YAAY,cAAc,OAAO;AACxC,cAAI,sBAAsB,OAAO;AAC/B,mBAAO,IAAI,UAAU,OAAO,UAAU,WAAW,UAAU;AAAA;AAE7D,iBAAO,UAAU,eAAe;AAChC,iBAAO;AAAA;AAAA;AAGX,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI;AACJ,eAAO,GAAG,WAAW,SAAU,GAAG;AAChC,cAAI,EAAE,YAAY,IAAI;AACpB,mBAAO,YAAY;AAAA;AAAA;AAGvB,YAAI,OAAO,QAAQ,QAAQ;AACzB,iBAAO,GAAG,SAAS,WAAY;AAC7B,gBAAI,CAAC,oBAAoB;AACvB,mCAAqB;AACrB,kBAAI;AACF,uBAAO,YAAY,iBAAiB,OAAO;AAAA,uBACpC,IAAP;AAAA;AAAA;AAAA;AAIN;AAAA;AAEF,eAAO,GAAG,YAAY,SAAU,GAAG;AACjC,cAAI,EAAE,YAAY,MAAM,EAAE,YAAY,MAAM,EAAE,YAAY,KAAK;AAC7D,mBAAO,cAAc;AAAA;AAAA;AAGzB,eAAO,GAAG,SAAS,SAAU,GAAG;AAC9B,cAAI,EAAE,YAAY,IAAI;AACpB,mBAAO,eAAe;AAAA;AAAA;AAAA;AAK5B,wBAAmB;AACjB,iBAAS,IAAI,YAAY,SAAU,QAAQ;AACzC,gBAAM;AAAA;AAAA;AAIV;AAAA;AAAA;AAAA;;;AClNJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,mCAAQ;", "names": []}