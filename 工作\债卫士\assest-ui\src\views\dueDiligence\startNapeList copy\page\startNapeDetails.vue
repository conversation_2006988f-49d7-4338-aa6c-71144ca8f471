<template>
    <div class="app-container">
        <div><el-button type="primary">更新进度</el-button></div>
        <div class="step-schedule-circle-line">
            <div class="step-schedule-list">
                <div :class="`step-schedule-item ${index < stepList.length && 'active'}`"
                    v-for="(item, index) in stepList" :key="index">
                    <div class="title">{{ item.title }}</div>
                    <div class="circle-area">
                        <div class="circle"></div>
                    </div>
                    <div class="content pl20">
                        <div class="top">{{ item.name }}</div>
                        <div class="botton">{{ item.date }}</div>
                    </div>
                </div>
            </div>
            <div class="circle-line"></div>
        </div>
        <el-table :data="dataList" :loading="loading">
            <el-table-column label="资料名称">
                <template #default="{ row }">
                    <div class="title">
                        <h3 class="mb10">{{ row.title }}</h3>
                        <div>{{ row.fileName }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="createBy" label="上传人" />
            <el-table-column prop="createTime" width="320" label="上传时间" />
        </el-table>
    </div>
</template>

<script setup name="startNapeDetails">
const stepList = ref([
    { title: '初步评估', name: '111', date: '2024-02-22' },
    { title: '项目立项', name: '111', date: '2024-02-22' },
    { title: '立项审批', name: '111', date: '2024-02-22' },
    { title: '尽调方案', name: '111', date: '2024-02-22' },
    { title: '尽调实施', name: '111', date: '2024-02-22' },
    { title: '资产估值', name: '111', date: '2024-02-22' },
    { title: '处置预案', name: '111', date: '2024-02-22' },
    { title: '尽调报告', name: '111', date: '2024-02-22' },
])
const dataList = ref([
    { title: '初步估值资料', fileName: '资产评估表.pdf', createBy: '曲丽丽', createTime: '2024/1/15 12:00:21' },
    { title: '项目立项', fileName: '立项表-中信资产包.pdf', createBy: '曲丽丽', createTime: '2024/1/15 12:00:21' },
    { title: '尽调方案', fileName: '调查方案.pdf', createBy: '曲丽丽', createTime: '2024/1/15 12:00:21' },
    { title: '尽调实施', fileName: '资产评估表.pdf', createBy: '曲丽丽', createTime: '2024/1/15 12:00:21' },
    { title: '初步估值资料', fileName: '尽调实施.pdf', createBy: '曲丽丽', createTime: '2024/1/15 12:00:21' },
])
</script>

<style lang="scss" scoped>
h3{
    font-weight: bold;
}
.step-schedule-circle-line {
    position: relative;

    .circle-line {
        position: absolute;
        top: 30px;
        width: 87%;
        height: 6px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #DEE1E3;
    }

    .step-schedule-list {
        display: flex;
        margin: 20px 0;
        justify-content: space-between;

        .title {
            color: #333;
            padding-left: 20px;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .step-schedule-item {
            position: relative;
            text-align: center;
            flex: 1;

            &:last-of-type {
                &.active::after {
                    background-color: #fff;
                }
            }

            &.active::after {
                position: absolute;
                top: 40%;
                content: '';
                width: 100%;
                height: 8px;
                z-index: 9;
                transform: translate(0%, -50%);
                background-color: #409EFF;
            }

            &.active {
                color: #409EFF;

                .circle {
                    background-color: #409EFF;
                }
            }

        }

        .circle-area {
            position: absolute;
            top: 33px;
            left: 50%;
            width: 20px;
            height: 20px;
            z-index: 9999;
            transform: translate(0%, -50%);
            background-color: #fff;

            .circle {
                position: absolute;
                top: 50%;
                left: 50%;
                width: 10px;
                height: 10px;
                z-index: 99;
                border-radius: 50%;
                transform: translate(-50%, -50%);
                background-color: #666666;
            }
        }

        .content {
            font-size: 12px;

        }
    }
}
</style>