<template>
    <div class="data-big-screen">
        <DataScreenHeader />
        <div class="data-screen-overview-area">
            <DataScreenOverview title="电催团队机构概况" />
            <DataOverviewTotal />
            <DataScreenOverview title="法诉团队机构概况" />
        </div>
        <div class="data-screen-overview-area">
            <DataScreenOverview title="本月回款top10（万）">
                <template #default>
                    <Bar idName="repayment-bar" />
                </template>
            </DataScreenOverview>
            <DataScreenOverview title="批次规模统计">
                <template #default>
                    <Line idName="batch-bar" />
                </template>
            </DataScreenOverview>
            <DataScreenOverview title="本月回款top10（万）">
                <template #default>
                    <Bar />
                </template>
            </DataScreenOverview>
        </div>
        <div class="data-screen-overview-area">
            <div class="data-screen-overview-area-left">
                <DataScreenOverview title="本月委案金额占比">
                    <template #default>
                        <Pie idName="allowt-pie" />
                    </template>
                </DataScreenOverview>
                <DataScreenOverview title="本月回款/委案金额top10">
                    <template #default>
                        <Bar idName="this-repayment-bar" />
                    </template>
                </DataScreenOverview>
            </div>
            <div class="data-screen-overview-area-center" style="height: 100%;">
                <DataScreenOverview title="资产分布及回款统计">
                    <template #default>
                        <PaymentNum :num="num" />
                        <div class="text-center mt20">
                            <img style="width: 90%;height: 100%;" src="@/assets/images/dataBigScreen/china.png" alt="">
                        </div>
                    </template>
                </DataScreenOverview>
            </div>
            <div class="data-screen-overview-area-right">
                <DataScreenOverview title="本月立案量top10">
                    <template #default>
                        <Bar idName="fill-bar" />
                    </template>
                </DataScreenOverview>
                <DataScreenOverview title="本月立案量占比">
                    <template #default>
                        <Pie idName="fill-num-bar" />
                    </template>
                </DataScreenOverview>
            </div>
        </div>
    </div>
</template>

<script setup>
import DataOverviewTotal from './components/dataOverviewTotal.vue';
import DataScreenHeader from './components/dataScreenHeader.vue';
import DataScreenOverview from './components/dataScreenOverview.vue';
import PaymentNum from './components/paymentNum';
import Bar from './echarts/bar.vue';
import Line from './echarts/line.vue';
import Pie from './echarts/pie.vue';

const num = ref(14363)
const timeId = ref(null)
onMounted(() => {
    clearInterval(timeId.value)
    timeId.value = setInterval(() => {
        num.value++
    }, 3000)
})
onUnmounted(() => {
    clearInterval(timeId.value)
})
</script>

<style lang="scss" scoped>
.data-big-screen {
    position: relative;
    min-height: calc(100vh - 88px);
    background-color: #052463;

    &::after {
        position: absolute;
        content: '';
        width: 100%;
        height: 100%;
        top: 0;
        z-index: 1;
        background-image: url(@/assets/images/dataBigScreen/screen-bg.png);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;

    }

    .data-screen-overview-area {
        position: relative;
        display: grid;
        gap: 20px;
        z-index: 9;
        padding: 0 20px 0;
        grid-template-columns: 2fr 3fr 2fr;
    }
}
</style>