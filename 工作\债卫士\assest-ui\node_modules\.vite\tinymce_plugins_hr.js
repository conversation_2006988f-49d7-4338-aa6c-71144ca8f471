import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/hr/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/hr/plugin.js"() {
    (function() {
      "use strict";
      var global = tinymce.util.Tools.resolve("tinymce.PluginManager");
      var register$1 = function(editor) {
        editor.addCommand("InsertHorizontalRule", function() {
          editor.execCommand("mceInsertContent", false, "<hr />");
        });
      };
      var register = function(editor) {
        var onAction = function() {
          return editor.execCommand("InsertHorizontalRule");
        };
        editor.ui.registry.addButton("hr", {
          icon: "horizontal-rule",
          tooltip: "Horizontal line",
          onAction
        });
        editor.ui.registry.addMenuItem("hr", {
          icon: "horizontal-rule",
          text: "Horizontal line",
          onAction
        });
      };
      function Plugin() {
        global.add("hr", function(editor) {
          register$1(editor);
          register(editor);
        });
      }
      Plugin();
    })();
  }
});

// node_modules/tinymce/plugins/hr/index.js
var require_hr = __commonJS({
  "node_modules/tinymce/plugins/hr/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_hr
var tinymce_plugins_hr_default = require_hr();
export {
  tinymce_plugins_hr_default as default
};
//# sourceMappingURL=tinymce_plugins_hr.js.map
