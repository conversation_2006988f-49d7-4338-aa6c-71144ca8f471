import "./chunk-JGAWL3HB.js";
import "./chunk-UOUDGCKJ.js";
import {
  Fragment,
  computed,
  createVNode,
  defineComponent,
  onBeforeMount,
  onMounted,
  watch
} from "./chunk-MZ7ANLUJ.js";
import {
  ref
} from "./chunk-XLSXXTZ3.js";
import "./chunk-WC6BDPVA.js";

// node_modules/vue3-seamless-scroll/dist/vue3-seamless-scroll.es.js
function throttle(delay, noTrailing, callback, debounceMode) {
  var timeoutID;
  var cancelled = false;
  var lastExec = 0;
  function clearExistingTimeout() {
    if (timeoutID) {
      clearTimeout(timeoutID);
    }
  }
  function cancel() {
    clearExistingTimeout();
    cancelled = true;
  }
  if (typeof noTrailing !== "boolean") {
    debounceMode = callback;
    callback = noTrailing;
    noTrailing = void 0;
  }
  function wrapper() {
    for (var _len = arguments.length, arguments_ = new Array(_len), _key = 0; _key < _len; _key++) {
      arguments_[_key] = arguments[_key];
    }
    var self = this;
    var elapsed = Date.now() - lastExec;
    if (cancelled) {
      return;
    }
    function exec() {
      lastExec = Date.now();
      callback.apply(self, arguments_);
    }
    function clear() {
      timeoutID = void 0;
    }
    if (debounceMode && !timeoutID) {
      exec();
    }
    clearExistingTimeout();
    if (debounceMode === void 0 && elapsed > delay) {
      exec();
    } else if (noTrailing !== true) {
      timeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === void 0 ? delay - elapsed : delay);
    }
  }
  wrapper.cancel = cancel;
  return wrapper;
}
var props = {
  modelValue: {
    type: Boolean,
    default: true
  },
  list: {
    type: Array,
    required: true
  },
  step: {
    type: Number,
    default: 1
  },
  limitScrollNum: {
    type: Number,
    default: 1
  },
  hover: {
    type: Boolean,
    default: false
  },
  direction: {
    type: String,
    default: "up"
  },
  singleHeight: {
    type: Number,
    default: 0
  },
  singleWidth: {
    type: Number,
    default: 0
  },
  singleWaitTime: {
    type: Number,
    default: 1e3
  },
  isRemUnit: {
    type: Boolean,
    default: false
  },
  isWatch: {
    type: Boolean,
    default: true
  },
  delay: {
    type: Number,
    default: 0
  },
  ease: {
    type: [String, Object],
    default: "ease-in"
  },
  count: {
    type: Number,
    default: -1
  },
  copyNum: {
    type: Number,
    default: 1
  },
  wheel: {
    type: Boolean,
    default: false
  }
};
globalThis.window.cancelAnimationFrame = function() {
  return globalThis.window.cancelAnimationFrame || globalThis.window.webkitCancelAnimationFrame || globalThis.window.mozCancelAnimationFrame || globalThis.window.oCancelAnimationFrame || globalThis.window.msCancelAnimationFrame || function(id) {
    return globalThis.window.clearTimeout(id);
  };
}();
globalThis.window.requestAnimationFrame = function() {
  return globalThis.window.requestAnimationFrame || globalThis.window.webkitRequestAnimationFrame || globalThis.window.mozRequestAnimationFrame || globalThis.window.oRequestAnimationFrame || globalThis.window.msRequestAnimationFrame || function(callback) {
    return globalThis.window.setTimeout(callback, 1e3 / 60);
  };
}();
function dataWarm(modelValue) {
  if (typeof modelValue !== "boolean" && modelValue.length > 100) {
    console.warn(`\u6570\u636E\u8FBE\u5230\u4E86${modelValue.length}\u6761\u6709\u70B9\u591A\u54E6~,\u53EF\u80FD\u4F1A\u9020\u6210\u90E8\u5206\u8001\u65E7\u6D4F\u89C8\u5668\u5361\u987F\u3002`);
  }
}
var Vue3SeamlessScroll = defineComponent({
  name: "vue3-seamless-scroll",
  inheritAttrs: false,
  props,
  emits: ["stop", "count"],
  setup(props2, {
    slots,
    emit,
    attrs
  }) {
    const scrollRef = ref(null);
    const slotListRef = ref(null);
    const realBoxRef = ref(null);
    const reqFrame = ref(null);
    const singleWaitTimeout = ref(null);
    const realBoxWidth = ref(0);
    const realBoxHeight = ref(0);
    const xPos = ref(0);
    const yPos = ref(0);
    const isHover = ref(false);
    const _count = ref(0);
    const isScroll = computed(() => props2.list.length >= props2.limitScrollNum);
    const realBoxStyle = computed(() => {
      return {
        width: realBoxWidth.value ? `${realBoxWidth.value}px` : "auto",
        transform: `translate(${xPos.value}px,${yPos.value}px)`,
        transition: `all ${typeof props2.ease === "string" ? props2.ease : "cubic-bezier(" + props2.ease.x1 + "," + props2.ease.y1 + "," + props2.ease.x2 + "," + props2.ease.y2 + ")"} ${props2.delay}ms`,
        overflow: "hidden"
      };
    });
    const isHorizontal = computed(() => props2.direction == "left" || props2.direction == "right");
    const floatStyle = computed(() => {
      return isHorizontal.value ? {
        float: "left",
        overflow: "hidden"
      } : {
        overflow: "hidden"
      };
    });
    const baseFontSize = computed(() => {
      return props2.isRemUnit ? parseInt(globalThis.window.getComputedStyle(globalThis.document.documentElement, null).fontSize) : 1;
    });
    const realSingleStopWidth = computed(() => props2.singleWidth * baseFontSize.value);
    const realSingleStopHeight = computed(() => props2.singleHeight * baseFontSize.value);
    const step = computed(() => {
      let singleStep;
      let _step = props2.step;
      if (isHorizontal.value) {
        singleStep = realSingleStopWidth.value;
      } else {
        singleStep = realSingleStopHeight.value;
      }
      if (singleStep > 0 && singleStep % _step > 0) {
        console.error("\u5982\u679C\u8BBE\u7F6E\u4E86\u5355\u6B65\u6EDA\u52A8,step\u9700\u662F\u5355\u6B65\u5927\u5C0F\u7684\u7EA6\u6570,\u5426\u5219\u65E0\u6CD5\u4FDD\u8BC1\u5355\u6B65\u6EDA\u52A8\u7ED3\u675F\u7684\u4F4D\u7F6E\u662F\u5426\u51C6\u786E\u3002~~~~~");
      }
      return _step;
    });
    function cancle() {
      cancelAnimationFrame(reqFrame.value);
      reqFrame.value = null;
    }
    function animation(_direction, _step, isWheel) {
      reqFrame.value = requestAnimationFrame(function() {
        const h = realBoxHeight.value / 2;
        const w = realBoxWidth.value / 2;
        if (_direction === "up") {
          if (Math.abs(yPos.value) >= h) {
            yPos.value = 0;
            _count.value += 1;
            emit("count", _count.value);
          }
          yPos.value -= _step;
        } else if (_direction === "down") {
          if (yPos.value >= 0) {
            yPos.value = h * -1;
            _count.value += 1;
            emit("count", _count.value);
          }
          yPos.value += _step;
        } else if (_direction === "left") {
          if (Math.abs(xPos.value) >= w) {
            xPos.value = 0;
            _count.value += 1;
            emit("count", _count.value);
          }
          xPos.value -= _step;
        } else if (_direction === "right") {
          if (xPos.value >= 0) {
            xPos.value = w * -1;
            _count.value += 1;
            emit("count", _count.value);
          }
          xPos.value += _step;
        }
        if (isWheel) {
          return;
        }
        let {
          singleWaitTime
        } = props2;
        if (singleWaitTimeout.value) {
          clearTimeout(singleWaitTimeout.value);
        }
        if (!!realSingleStopHeight.value) {
          if (Math.abs(yPos.value) % realSingleStopHeight.value < _step) {
            singleWaitTimeout.value = setTimeout(() => {
              move();
            }, singleWaitTime);
          } else {
            move();
          }
        } else if (!!realSingleStopWidth.value) {
          if (Math.abs(xPos.value) % realSingleStopWidth.value < _step) {
            singleWaitTimeout.value = setTimeout(() => {
              move();
            }, singleWaitTime);
          } else {
            move();
          }
        } else {
          move();
        }
      });
    }
    function move() {
      cancle();
      if (isHover.value || !isScroll.value || _count.value === props2.count) {
        emit("stop", _count.value);
        _count.value = 0;
        return;
      }
      animation(props2.direction, step.value, false);
    }
    function initMove() {
      dataWarm(props2.list);
      if (isHorizontal.value) {
        let slotListWidth = slotListRef.value.offsetWidth;
        slotListWidth = slotListWidth * 2 + 1;
        realBoxWidth.value = slotListWidth;
      }
      if (isScroll.value) {
        realBoxHeight.value = realBoxRef.value.offsetHeight;
        if (props2.modelValue) {
          move();
        }
      } else {
        cancle();
        yPos.value = xPos.value = 0;
      }
    }
    function startMove() {
      isHover.value = false;
      move();
    }
    function stopMove() {
      isHover.value = true;
      if (singleWaitTimeout.value) {
        clearTimeout(singleWaitTimeout.value);
      }
      cancle();
    }
    const hoverStop = computed(() => props2.hover && props2.modelValue && isScroll.value);
    const throttleFunc = throttle(30, (e) => {
      cancle();
      const singleHeight = !!realSingleStopHeight.value ? realSingleStopHeight.value : 15;
      if (e.deltaY < 0) {
        animation("down", singleHeight, true);
      }
      if (e.deltaY > 0) {
        animation("up", singleHeight, true);
      }
    });
    const onWheel = (e) => {
      throttleFunc(e);
    };
    function reset() {
      cancle();
      isHover.value = false;
      initMove();
    }
    watch(() => props2.list, () => {
      if (props2.isWatch) {
        reset();
      }
    }, {
      deep: true
    });
    watch(() => props2.modelValue, (newValue) => {
      if (newValue) {
        startMove();
      } else {
        stopMove();
      }
    });
    watch(() => props2.count, (newValue) => {
      if (newValue !== 0) {
        startMove();
      }
    });
    onBeforeMount(() => {
      cancle();
      clearTimeout(singleWaitTimeout.value);
    });
    onMounted(() => {
      initMove();
    });
    const {
      default: $default,
      html
    } = slots;
    const copyNum = new Array(props2.copyNum).fill(null);
    const getHtml = () => {
      return createVNode(Fragment, null, [createVNode("div", {
        "ref": slotListRef,
        "style": floatStyle.value
      }, [$default()]), isScroll ? copyNum.map(() => {
        if (html && typeof html === "function") {
          return createVNode("div", {
            "style": floatStyle.value
          }, [html()]);
        } else {
          return createVNode("div", {
            "style": floatStyle.value
          }, [$default()]);
        }
      }) : null]);
    };
    return () => createVNode("div", {
      "ref": scrollRef,
      "class": attrs.class
    }, [props2.wheel && props2.hover ? createVNode("div", {
      "ref": realBoxRef,
      "style": realBoxStyle.value,
      "onMouseenter": () => {
        if (hoverStop.value) {
          stopMove();
        }
      },
      "onMouseleave": () => {
        if (hoverStop.value) {
          startMove();
        }
      },
      "onWheel": (e) => {
        if (hoverStop.value) {
          onWheel(e);
        }
      }
    }, [getHtml()]) : createVNode("div", {
      "ref": realBoxRef,
      "style": realBoxStyle.value,
      "onMouseenter": () => {
        if (hoverStop.value) {
          stopMove();
        }
      },
      "onMouseleave": () => {
        if (hoverStop.value) {
          startMove();
        }
      }
    }, [getHtml()])]);
  }
});
var install = function(app, options = {}) {
  app.component(options.name || Vue3SeamlessScroll.name, Vue3SeamlessScroll);
};
function index(app) {
  app.use(install);
}

// dep:vue3-seamless-scroll
var vue3_seamless_scroll_default = index;
export {
  Vue3SeamlessScroll,
  vue3_seamless_scroll_default as default
};
//# sourceMappingURL=vue3-seamless-scroll.js.map
