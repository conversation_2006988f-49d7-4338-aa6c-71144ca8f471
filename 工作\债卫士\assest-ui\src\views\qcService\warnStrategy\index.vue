<template>
    <div class="app-container left-right-layout">
        <el-tabs tab-position="left" class="tab-pane" v-model="activeName">
            <el-tab-pane label="语音库配置" name="语音库配置" />
            <el-tab-pane label="邮箱设置" name="邮箱设置" />
        </el-tabs>
        <div style="width:100%">
            <voiceLibrary v-if="activeName == '语音库配置'" ref="voiceLibraryRef" />
            <mailBox v-if="activeName == '邮箱设置'" ref="mailBoxRef" />
        </div>
    </div>
</template>

<script setup>
import mailBox from './tabPage/mailBox';
import voiceLibrary from './tabPage/voiceLibrary';
const activeName = ref('语音库配置')
</script>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
    width: 200px;

    .el-tabs__nav.is-left {
        width: 180px;
    }
}

.left-right-layout {
    display: flex;
}
</style>