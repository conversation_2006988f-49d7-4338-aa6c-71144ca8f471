{"version": 3, "sources": ["../fingerprintjs2/fingerprint2.js", "dep:fingerprintjs2"], "sourcesContent": ["/*\n* Fingerprintjs2 2.1.4 - Modern & flexible browser fingerprint library v2\n* https://github.com/fingerprintjs/fingerprintjs\n* Copyright (c) 2020 Vale<PERSON> Vasilyev (<EMAIL>)\n* Licensed under the MIT (http://www.opensource.org/licenses/mit-license.php) license.\n*\n* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n* AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n* IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n* ARE DISCLAIMED. IN NO EVENT SHALL VALENTIN VASILYEV BE LIABLE FOR ANY\n* DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) H<PERSON><PERSON>VE<PERSON> CAUSED AND\n* ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n* (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n* THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n/*\n* This software contains code from open-source projects:\n* MurmurHash3 by Karan Lyons (https://github.com/karanlyons/murmurHash3.js)\n*/\n\n/* global define */\n(function (name, context, definition) {\n  'use strict'\n  if (typeof window !== 'undefined' && typeof define === 'function' && define.amd) { define(definition) } else if (typeof module !== 'undefined' && module.exports) { module.exports = definition() } else if (context.exports) { context.exports = definition() } else { context[name] = definition() }\n})('Fingerprint2', this, function () {\n  'use strict'\n\n  // detect if object is array\n  // only implement if no native implementation is available\n  if (typeof Array.isArray === 'undefined') {\n    Array.isArray = function (obj) {\n      return Object.prototype.toString.call(obj) === '[object Array]'\n    }\n  };\n\n  /// MurmurHash3 related functions\n\n  //\n  // Given two 64bit ints (as an array of two 32bit ints) returns the two\n  // added together as a 64bit int (as an array of two 32bit ints).\n  //\n  var x64Add = function (m, n) {\n    m = [m[0] >>> 16, m[0] & 0xffff, m[1] >>> 16, m[1] & 0xffff]\n    n = [n[0] >>> 16, n[0] & 0xffff, n[1] >>> 16, n[1] & 0xffff]\n    var o = [0, 0, 0, 0]\n    o[3] += m[3] + n[3]\n    o[2] += o[3] >>> 16\n    o[3] &= 0xffff\n    o[2] += m[2] + n[2]\n    o[1] += o[2] >>> 16\n    o[2] &= 0xffff\n    o[1] += m[1] + n[1]\n    o[0] += o[1] >>> 16\n    o[1] &= 0xffff\n    o[0] += m[0] + n[0]\n    o[0] &= 0xffff\n    return [(o[0] << 16) | o[1], (o[2] << 16) | o[3]]\n  }\n\n  //\n  // Given two 64bit ints (as an array of two 32bit ints) returns the two\n  // multiplied together as a 64bit int (as an array of two 32bit ints).\n  //\n  var x64Multiply = function (m, n) {\n    m = [m[0] >>> 16, m[0] & 0xffff, m[1] >>> 16, m[1] & 0xffff]\n    n = [n[0] >>> 16, n[0] & 0xffff, n[1] >>> 16, n[1] & 0xffff]\n    var o = [0, 0, 0, 0]\n    o[3] += m[3] * n[3]\n    o[2] += o[3] >>> 16\n    o[3] &= 0xffff\n    o[2] += m[2] * n[3]\n    o[1] += o[2] >>> 16\n    o[2] &= 0xffff\n    o[2] += m[3] * n[2]\n    o[1] += o[2] >>> 16\n    o[2] &= 0xffff\n    o[1] += m[1] * n[3]\n    o[0] += o[1] >>> 16\n    o[1] &= 0xffff\n    o[1] += m[2] * n[2]\n    o[0] += o[1] >>> 16\n    o[1] &= 0xffff\n    o[1] += m[3] * n[1]\n    o[0] += o[1] >>> 16\n    o[1] &= 0xffff\n    o[0] += (m[0] * n[3]) + (m[1] * n[2]) + (m[2] * n[1]) + (m[3] * n[0])\n    o[0] &= 0xffff\n    return [(o[0] << 16) | o[1], (o[2] << 16) | o[3]]\n  }\n  //\n  // Given a 64bit int (as an array of two 32bit ints) and an int\n  // representing a number of bit positions, returns the 64bit int (as an\n  // array of two 32bit ints) rotated left by that number of positions.\n  //\n  var x64Rotl = function (m, n) {\n    n %= 64\n    if (n === 32) {\n      return [m[1], m[0]]\n    } else if (n < 32) {\n      return [(m[0] << n) | (m[1] >>> (32 - n)), (m[1] << n) | (m[0] >>> (32 - n))]\n    } else {\n      n -= 32\n      return [(m[1] << n) | (m[0] >>> (32 - n)), (m[0] << n) | (m[1] >>> (32 - n))]\n    }\n  }\n  //\n  // Given a 64bit int (as an array of two 32bit ints) and an int\n  // representing a number of bit positions, returns the 64bit int (as an\n  // array of two 32bit ints) shifted left by that number of positions.\n  //\n  var x64LeftShift = function (m, n) {\n    n %= 64\n    if (n === 0) {\n      return m\n    } else if (n < 32) {\n      return [(m[0] << n) | (m[1] >>> (32 - n)), m[1] << n]\n    } else {\n      return [m[1] << (n - 32), 0]\n    }\n  }\n  //\n  // Given two 64bit ints (as an array of two 32bit ints) returns the two\n  // xored together as a 64bit int (as an array of two 32bit ints).\n  //\n  var x64Xor = function (m, n) {\n    return [m[0] ^ n[0], m[1] ^ n[1]]\n  }\n  //\n  // Given a block, returns murmurHash3's final x64 mix of that block.\n  // (`[0, h[0] >>> 1]` is a 33 bit unsigned right shift. This is the\n  // only place where we need to right shift 64bit ints.)\n  //\n  var x64Fmix = function (h) {\n    h = x64Xor(h, [0, h[0] >>> 1])\n    h = x64Multiply(h, [0xff51afd7, 0xed558ccd])\n    h = x64Xor(h, [0, h[0] >>> 1])\n    h = x64Multiply(h, [0xc4ceb9fe, 0x1a85ec53])\n    h = x64Xor(h, [0, h[0] >>> 1])\n    return h\n  }\n\n  //\n  // Given a string and an optional seed as an int, returns a 128 bit\n  // hash using the x64 flavor of MurmurHash3, as an unsigned hex.\n  //\n  var x64hash128 = function (key, seed) {\n    key = key || ''\n    seed = seed || 0\n    var remainder = key.length % 16\n    var bytes = key.length - remainder\n    var h1 = [0, seed]\n    var h2 = [0, seed]\n    var k1 = [0, 0]\n    var k2 = [0, 0]\n    var c1 = [0x87c37b91, 0x114253d5]\n    var c2 = [0x4cf5ad43, 0x2745937f]\n    for (var i = 0; i < bytes; i = i + 16) {\n      k1 = [((key.charCodeAt(i + 4) & 0xff)) | ((key.charCodeAt(i + 5) & 0xff) << 8) | ((key.charCodeAt(i + 6) & 0xff) << 16) | ((key.charCodeAt(i + 7) & 0xff) << 24), ((key.charCodeAt(i) & 0xff)) | ((key.charCodeAt(i + 1) & 0xff) << 8) | ((key.charCodeAt(i + 2) & 0xff) << 16) | ((key.charCodeAt(i + 3) & 0xff) << 24)]\n      k2 = [((key.charCodeAt(i + 12) & 0xff)) | ((key.charCodeAt(i + 13) & 0xff) << 8) | ((key.charCodeAt(i + 14) & 0xff) << 16) | ((key.charCodeAt(i + 15) & 0xff) << 24), ((key.charCodeAt(i + 8) & 0xff)) | ((key.charCodeAt(i + 9) & 0xff) << 8) | ((key.charCodeAt(i + 10) & 0xff) << 16) | ((key.charCodeAt(i + 11) & 0xff) << 24)]\n      k1 = x64Multiply(k1, c1)\n      k1 = x64Rotl(k1, 31)\n      k1 = x64Multiply(k1, c2)\n      h1 = x64Xor(h1, k1)\n      h1 = x64Rotl(h1, 27)\n      h1 = x64Add(h1, h2)\n      h1 = x64Add(x64Multiply(h1, [0, 5]), [0, 0x52dce729])\n      k2 = x64Multiply(k2, c2)\n      k2 = x64Rotl(k2, 33)\n      k2 = x64Multiply(k2, c1)\n      h2 = x64Xor(h2, k2)\n      h2 = x64Rotl(h2, 31)\n      h2 = x64Add(h2, h1)\n      h2 = x64Add(x64Multiply(h2, [0, 5]), [0, 0x38495ab5])\n    }\n    k1 = [0, 0]\n    k2 = [0, 0]\n    switch (remainder) {\n      case 15:\n        k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 14)], 48))\n      // fallthrough\n      case 14:\n        k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 13)], 40))\n      // fallthrough\n      case 13:\n        k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 12)], 32))\n      // fallthrough\n      case 12:\n        k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 11)], 24))\n      // fallthrough\n      case 11:\n        k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 10)], 16))\n      // fallthrough\n      case 10:\n        k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 9)], 8))\n      // fallthrough\n      case 9:\n        k2 = x64Xor(k2, [0, key.charCodeAt(i + 8)])\n        k2 = x64Multiply(k2, c2)\n        k2 = x64Rotl(k2, 33)\n        k2 = x64Multiply(k2, c1)\n        h2 = x64Xor(h2, k2)\n      // fallthrough\n      case 8:\n        k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 7)], 56))\n      // fallthrough\n      case 7:\n        k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 6)], 48))\n      // fallthrough\n      case 6:\n        k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 5)], 40))\n      // fallthrough\n      case 5:\n        k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 4)], 32))\n      // fallthrough\n      case 4:\n        k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 3)], 24))\n      // fallthrough\n      case 3:\n        k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 2)], 16))\n      // fallthrough\n      case 2:\n        k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 1)], 8))\n      // fallthrough\n      case 1:\n        k1 = x64Xor(k1, [0, key.charCodeAt(i)])\n        k1 = x64Multiply(k1, c1)\n        k1 = x64Rotl(k1, 31)\n        k1 = x64Multiply(k1, c2)\n        h1 = x64Xor(h1, k1)\n      // fallthrough\n    }\n    h1 = x64Xor(h1, [0, key.length])\n    h2 = x64Xor(h2, [0, key.length])\n    h1 = x64Add(h1, h2)\n    h2 = x64Add(h2, h1)\n    h1 = x64Fmix(h1)\n    h2 = x64Fmix(h2)\n    h1 = x64Add(h1, h2)\n    h2 = x64Add(h2, h1)\n    return ('00000000' + (h1[0] >>> 0).toString(16)).slice(-8) + ('00000000' + (h1[1] >>> 0).toString(16)).slice(-8) + ('00000000' + (h2[0] >>> 0).toString(16)).slice(-8) + ('00000000' + (h2[1] >>> 0).toString(16)).slice(-8)\n  }\n\n  var defaultOptions = {\n    preprocessor: null,\n    audio: {\n      timeout: 1000,\n      // On iOS 11, audio context can only be used in response to user interaction.\n      // We require users to explicitly enable audio fingerprinting on iOS 11.\n      // See https://stackoverflow.com/questions/46363048/onaudioprocess-not-called-on-ios11#46534088\n      excludeIOS11: true\n    },\n    fonts: {\n      swfContainerId: 'fingerprintjs2',\n      swfPath: 'flash/compiled/FontList.swf',\n      userDefinedFonts: [],\n      extendedJsFonts: false\n    },\n    screen: {\n      // To ensure consistent fingerprints when users rotate their mobile devices\n      detectScreenOrientation: true\n    },\n    plugins: {\n      sortPluginsFor: [/palemoon/i],\n      excludeIE: false\n    },\n    extraComponents: [],\n    excludes: {\n      // Unreliable on Windows, see https://github.com/fingerprintjs/fingerprintjs/issues/375\n      'enumerateDevices': true,\n      // devicePixelRatio depends on browser zoom, and it's impossible to detect browser zoom\n      'pixelRatio': true,\n      // DNT depends on incognito mode for some browsers (Chrome) and it's impossible to detect incognito mode\n      'doNotTrack': true,\n      // uses js fonts already\n      'fontsFlash': true,\n      // Extensions (including AdBlock) are disabled by default in Incognito mod of Chrome and Firefox\n      // See https://github.com/fingerprintjs/fingerprintjs/issues/405\n      'adBlock': true\n    },\n    NOT_AVAILABLE: 'not available',\n    ERROR: 'error',\n    EXCLUDED: 'excluded'\n  }\n\n  var each = function (obj, iterator) {\n    if (Array.prototype.forEach && obj.forEach === Array.prototype.forEach) {\n      obj.forEach(iterator)\n    } else if (obj.length === +obj.length) {\n      for (var i = 0, l = obj.length; i < l; i++) {\n        iterator(obj[i], i, obj)\n      }\n    } else {\n      for (var key in obj) {\n        if (obj.hasOwnProperty(key)) {\n          iterator(obj[key], key, obj)\n        }\n      }\n    }\n  }\n\n  var map = function (obj, iterator) {\n    var results = []\n    // Not using strict equality so that this acts as a\n    // shortcut to checking for `null` and `undefined`.\n    if (obj == null) {\n      return results\n    }\n    if (Array.prototype.map && obj.map === Array.prototype.map) { return obj.map(iterator) }\n    each(obj, function (value, index, list) {\n      results.push(iterator(value, index, list))\n    })\n    return results\n  }\n\n  var extendSoft = function (target, source) {\n    if (source == null) { return target }\n    var value\n    var key\n    for (key in source) {\n      value = source[key]\n      if (value != null && !(Object.prototype.hasOwnProperty.call(target, key))) {\n        target[key] = value\n      }\n    }\n    return target\n  }\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/enumerateDevices\n  var enumerateDevicesKey = function (done, options) {\n    if (!isEnumerateDevicesSupported()) {\n      return done(options.NOT_AVAILABLE)\n    }\n    navigator.mediaDevices.enumerateDevices().then(function (devices) {\n      done(devices.map(function (device) {\n        return 'id=' + device.deviceId + ';gid=' + device.groupId + ';' + device.kind + ';' + device.label\n      }))\n    }).catch(function (error) {\n      done(error)\n    })\n  }\n\n  var isEnumerateDevicesSupported = function () {\n    return (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices)\n  }\n  // Inspired by and based on https://github.com/cozylife/audio-fingerprint\n  var audioKey = function (done, options) {\n    var audioOptions = options.audio\n    if (audioOptions.excludeIOS11 && navigator.userAgent.match(/OS 11.+Version\\/11.+Safari/)) {\n      // See comment for excludeUserAgent and https://stackoverflow.com/questions/46363048/onaudioprocess-not-called-on-ios11#46534088\n      return done(options.EXCLUDED)\n    }\n\n    var AudioContext = window.OfflineAudioContext || window.webkitOfflineAudioContext\n\n    if (AudioContext == null) {\n      return done(options.NOT_AVAILABLE)\n    }\n\n    var context = new AudioContext(1, 44100, 44100)\n\n    var oscillator = context.createOscillator()\n    oscillator.type = 'triangle'\n    oscillator.frequency.setValueAtTime(10000, context.currentTime)\n\n    var compressor = context.createDynamicsCompressor()\n    each([\n      ['threshold', -50],\n      ['knee', 40],\n      ['ratio', 12],\n      ['reduction', -20],\n      ['attack', 0],\n      ['release', 0.25]\n    ], function (item) {\n      if (compressor[item[0]] !== undefined && typeof compressor[item[0]].setValueAtTime === 'function') {\n        compressor[item[0]].setValueAtTime(item[1], context.currentTime)\n      }\n    })\n\n    oscillator.connect(compressor)\n    compressor.connect(context.destination)\n    oscillator.start(0)\n    context.startRendering()\n\n    var audioTimeoutId = setTimeout(function () {\n      console.warn('Audio fingerprint timed out. Please report bug at https://github.com/fingerprintjs/fingerprintjs with your user agent: \"' + navigator.userAgent + '\".')\n      context.oncomplete = function () { }\n      context = null\n      return done('audioTimeout')\n    }, audioOptions.timeout)\n\n    context.oncomplete = function (event) {\n      var fingerprint\n      try {\n        clearTimeout(audioTimeoutId)\n        fingerprint = event.renderedBuffer.getChannelData(0)\n          .slice(4500, 5000)\n          .reduce(function (acc, val) { return acc + Math.abs(val) }, 0)\n          .toString()\n        oscillator.disconnect()\n        compressor.disconnect()\n      } catch (error) {\n        done(error)\n        return\n      }\n      done(fingerprint)\n    }\n  }\n  var UserAgent = function (done) {\n    done(navigator.userAgent)\n  }\n  var webdriver = function (done, options) {\n    done(navigator.webdriver == null ? options.NOT_AVAILABLE : navigator.webdriver)\n  }\n  var languageKey = function (done, options) {\n    done(navigator.language || navigator.userLanguage || navigator.browserLanguage || navigator.systemLanguage || options.NOT_AVAILABLE)\n  }\n  var colorDepthKey = function (done, options) {\n    done(window.screen.colorDepth || options.NOT_AVAILABLE)\n  }\n  var deviceMemoryKey = function (done, options) {\n    done(navigator.deviceMemory || options.NOT_AVAILABLE)\n  }\n  var pixelRatioKey = function (done, options) {\n    done(window.devicePixelRatio || options.NOT_AVAILABLE)\n  }\n  var screenResolutionKey = function (done, options) {\n    done(getScreenResolution(options))\n  }\n  var getScreenResolution = function (options) {\n    var resolution = [window.screen.width, window.screen.height]\n    if (options.screen.detectScreenOrientation) {\n      resolution.sort().reverse()\n    }\n    return resolution\n  }\n  var availableScreenResolutionKey = function (done, options) {\n    done(getAvailableScreenResolution(options))\n  }\n  var getAvailableScreenResolution = function (options) {\n    if (window.screen.availWidth && window.screen.availHeight) {\n      var available = [window.screen.availHeight, window.screen.availWidth]\n      if (options.screen.detectScreenOrientation) {\n        available.sort().reverse()\n      }\n      return available\n    }\n    // headless browsers\n    return options.NOT_AVAILABLE\n  }\n  var timezoneOffset = function (done) {\n    done(new Date().getTimezoneOffset())\n  }\n  var timezone = function (done, options) {\n    if (window.Intl && window.Intl.DateTimeFormat) {\n      done(new window.Intl.DateTimeFormat().resolvedOptions().timeZone || options.NOT_AVAILABLE)\n      return\n    }\n    done(options.NOT_AVAILABLE)\n  }\n  var sessionStorageKey = function (done, options) {\n    done(hasSessionStorage(options))\n  }\n  var localStorageKey = function (done, options) {\n    done(hasLocalStorage(options))\n  }\n  var indexedDbKey = function (done, options) {\n    done(hasIndexedDB(options))\n  }\n  var addBehaviorKey = function (done) {\n    done(!!window.HTMLElement.prototype.addBehavior)\n  }\n  var openDatabaseKey = function (done) {\n    done(!!window.openDatabase)\n  }\n  var cpuClassKey = function (done, options) {\n    done(getNavigatorCpuClass(options))\n  }\n  var platformKey = function (done, options) {\n    done(getNavigatorPlatform(options))\n  }\n  var doNotTrackKey = function (done, options) {\n    done(getDoNotTrack(options))\n  }\n  var canvasKey = function (done, options) {\n    if (isCanvasSupported()) {\n      done(getCanvasFp(options))\n      return\n    }\n    done(options.NOT_AVAILABLE)\n  }\n  var webglKey = function (done, options) {\n    if (isWebGlSupported()) {\n      done(getWebglFp())\n      return\n    }\n    done(options.NOT_AVAILABLE)\n  }\n  var webglVendorAndRendererKey = function (done) {\n    if (isWebGlSupported()) {\n      done(getWebglVendorAndRenderer())\n      return\n    }\n    done()\n  }\n  var adBlockKey = function (done) {\n    done(getAdBlock())\n  }\n  var hasLiedLanguagesKey = function (done) {\n    done(getHasLiedLanguages())\n  }\n  var hasLiedResolutionKey = function (done) {\n    done(getHasLiedResolution())\n  }\n  var hasLiedOsKey = function (done) {\n    done(getHasLiedOs())\n  }\n  var hasLiedBrowserKey = function (done) {\n    done(getHasLiedBrowser())\n  }\n  // flash fonts (will increase fingerprinting time 20X to ~ 130-150ms)\n  var flashFontsKey = function (done, options) {\n    // we do flash if swfobject is loaded\n    if (!hasSwfObjectLoaded()) {\n      return done('swf object not loaded')\n    }\n    if (!hasMinFlashInstalled()) {\n      return done('flash not installed')\n    }\n    if (!options.fonts.swfPath) {\n      return done('missing options.fonts.swfPath')\n    }\n    loadSwfAndDetectFonts(function (fonts) {\n      done(fonts)\n    }, options)\n  }\n  // kudos to http://www.lalit.org/lab/javascript-css-font-detect/\n  var jsFontsKey = function (done, options) {\n    // a font will be compared against all the three default fonts.\n    // and if it doesn't match all 3 then that font is not available.\n    var baseFonts = ['monospace', 'sans-serif', 'serif']\n\n    var fontList = [\n      'Andale Mono', 'Arial', 'Arial Black', 'Arial Hebrew', 'Arial MT', 'Arial Narrow', 'Arial Rounded MT Bold', 'Arial Unicode MS',\n      'Bitstream Vera Sans Mono', 'Book Antiqua', 'Bookman Old Style',\n      'Calibri', 'Cambria', 'Cambria Math', 'Century', 'Century Gothic', 'Century Schoolbook', 'Comic Sans', 'Comic Sans MS', 'Consolas', 'Courier', 'Courier New',\n      'Geneva', 'Georgia',\n      'Helvetica', 'Helvetica Neue',\n      'Impact',\n      'Lucida Bright', 'Lucida Calligraphy', 'Lucida Console', 'Lucida Fax', 'LUCIDA GRANDE', 'Lucida Handwriting', 'Lucida Sans', 'Lucida Sans Typewriter', 'Lucida Sans Unicode',\n      'Microsoft Sans Serif', 'Monaco', 'Monotype Corsiva', 'MS Gothic', 'MS Outlook', 'MS PGothic', 'MS Reference Sans Serif', 'MS Sans Serif', 'MS Serif', 'MYRIAD', 'MYRIAD PRO',\n      'Palatino', 'Palatino Linotype',\n      'Segoe Print', 'Segoe Script', 'Segoe UI', 'Segoe UI Light', 'Segoe UI Semibold', 'Segoe UI Symbol',\n      'Tahoma', 'Times', 'Times New Roman', 'Times New Roman PS', 'Trebuchet MS',\n      'Verdana', 'Wingdings', 'Wingdings 2', 'Wingdings 3'\n    ]\n\n    if (options.fonts.extendedJsFonts) {\n      var extendedFontList = [\n        'Abadi MT Condensed Light', 'Academy Engraved LET', 'ADOBE CASLON PRO', 'Adobe Garamond', 'ADOBE GARAMOND PRO', 'Agency FB', 'Aharoni', 'Albertus Extra Bold', 'Albertus Medium', 'Algerian', 'Amazone BT', 'American Typewriter',\n        'American Typewriter Condensed', 'AmerType Md BT', 'Andalus', 'Angsana New', 'AngsanaUPC', 'Antique Olive', 'Aparajita', 'Apple Chancery', 'Apple Color Emoji', 'Apple SD Gothic Neo', 'Arabic Typesetting', 'ARCHER',\n        'ARNO PRO', 'Arrus BT', 'Aurora Cn BT', 'AvantGarde Bk BT', 'AvantGarde Md BT', 'AVENIR', 'Ayuthaya', 'Bandy', 'Bangla Sangam MN', 'Bank Gothic', 'BankGothic Md BT', 'Baskerville',\n        'Baskerville Old Face', 'Batang', 'BatangChe', 'Bauer Bodoni', 'Bauhaus 93', 'Bazooka', 'Bell MT', 'Bembo', 'Benguiat Bk BT', 'Berlin Sans FB', 'Berlin Sans FB Demi', 'Bernard MT Condensed', 'BernhardFashion BT', 'BernhardMod BT', 'Big Caslon', 'BinnerD',\n        'Blackadder ITC', 'BlairMdITC TT', 'Bodoni 72', 'Bodoni 72 Oldstyle', 'Bodoni 72 Smallcaps', 'Bodoni MT', 'Bodoni MT Black', 'Bodoni MT Condensed', 'Bodoni MT Poster Compressed',\n        'Bookshelf Symbol 7', 'Boulder', 'Bradley Hand', 'Bradley Hand ITC', 'Bremen Bd BT', 'Britannic Bold', 'Broadway', 'Browallia New', 'BrowalliaUPC', 'Brush Script MT', 'Californian FB', 'Calisto MT', 'Calligrapher', 'Candara',\n        'CaslonOpnface BT', 'Castellar', 'Centaur', 'Cezanne', 'CG Omega', 'CG Times', 'Chalkboard', 'Chalkboard SE', 'Chalkduster', 'Charlesworth', 'Charter Bd BT', 'Charter BT', 'Chaucer',\n        'ChelthmITC Bk BT', 'Chiller', 'Clarendon', 'Clarendon Condensed', 'CloisterBlack BT', 'Cochin', 'Colonna MT', 'Constantia', 'Cooper Black', 'Copperplate', 'Copperplate Gothic', 'Copperplate Gothic Bold',\n        'Copperplate Gothic Light', 'CopperplGoth Bd BT', 'Corbel', 'Cordia New', 'CordiaUPC', 'Cornerstone', 'Coronet', 'Cuckoo', 'Curlz MT', 'DaunPenh', 'Dauphin', 'David', 'DB LCD Temp', 'DELICIOUS', 'Denmark',\n        'DFKai-SB', 'Didot', 'DilleniaUPC', 'DIN', 'DokChampa', 'Dotum', 'DotumChe', 'Ebrima', 'Edwardian Script ITC', 'Elephant', 'English 111 Vivace BT', 'Engravers MT', 'EngraversGothic BT', 'Eras Bold ITC', 'Eras Demi ITC', 'Eras Light ITC', 'Eras Medium ITC',\n        'EucrosiaUPC', 'Euphemia', 'Euphemia UCAS', 'EUROSTILE', 'Exotc350 Bd BT', 'FangSong', 'Felix Titling', 'Fixedsys', 'FONTIN', 'Footlight MT Light', 'Forte',\n        'FrankRuehl', 'Fransiscan', 'Freefrm721 Blk BT', 'FreesiaUPC', 'Freestyle Script', 'French Script MT', 'FrnkGothITC Bk BT', 'Fruitger', 'FRUTIGER',\n        'Futura', 'Futura Bk BT', 'Futura Lt BT', 'Futura Md BT', 'Futura ZBlk BT', 'FuturaBlack BT', 'Gabriola', 'Galliard BT', 'Gautami', 'Geeza Pro', 'Geometr231 BT', 'Geometr231 Hv BT', 'Geometr231 Lt BT', 'GeoSlab 703 Lt BT',\n        'GeoSlab 703 XBd BT', 'Gigi', 'Gill Sans', 'Gill Sans MT', 'Gill Sans MT Condensed', 'Gill Sans MT Ext Condensed Bold', 'Gill Sans Ultra Bold', 'Gill Sans Ultra Bold Condensed', 'Gisha', 'Gloucester MT Extra Condensed', 'GOTHAM', 'GOTHAM BOLD',\n        'Goudy Old Style', 'Goudy Stout', 'GoudyHandtooled BT', 'GoudyOLSt BT', 'Gujarati Sangam MN', 'Gulim', 'GulimChe', 'Gungsuh', 'GungsuhChe', 'Gurmukhi MN', 'Haettenschweiler', 'Harlow Solid Italic', 'Harrington', 'Heather', 'Heiti SC', 'Heiti TC', 'HELV',\n        'Herald', 'High Tower Text', 'Hiragino Kaku Gothic ProN', 'Hiragino Mincho ProN', 'Hoefler Text', 'Humanst 521 Cn BT', 'Humanst521 BT', 'Humanst521 Lt BT', 'Imprint MT Shadow', 'Incised901 Bd BT', 'Incised901 BT',\n        'Incised901 Lt BT', 'INCONSOLATA', 'Informal Roman', 'Informal011 BT', 'INTERSTATE', 'IrisUPC', 'Iskoola Pota', 'JasmineUPC', 'Jazz LET', 'Jenson', 'Jester', 'Jokerman', 'Juice ITC', 'Kabel Bk BT', 'Kabel Ult BT', 'Kailasa', 'KaiTi', 'Kalinga', 'Kannada Sangam MN',\n        'Kartika', 'Kaufmann Bd BT', 'Kaufmann BT', 'Khmer UI', 'KodchiangUPC', 'Kokila', 'Korinna BT', 'Kristen ITC', 'Krungthep', 'Kunstler Script', 'Lao UI', 'Latha', 'Leelawadee', 'Letter Gothic', 'Levenim MT', 'LilyUPC', 'Lithograph', 'Lithograph Light', 'Long Island',\n        'Lydian BT', 'Magneto', 'Maiandra GD', 'Malayalam Sangam MN', 'Malgun Gothic',\n        'Mangal', 'Marigold', 'Marion', 'Marker Felt', 'Market', 'Marlett', 'Matisse ITC', 'Matura MT Script Capitals', 'Meiryo', 'Meiryo UI', 'Microsoft Himalaya', 'Microsoft JhengHei', 'Microsoft New Tai Lue', 'Microsoft PhagsPa', 'Microsoft Tai Le',\n        'Microsoft Uighur', 'Microsoft YaHei', 'Microsoft Yi Baiti', 'MingLiU', 'MingLiU_HKSCS', 'MingLiU_HKSCS-ExtB', 'MingLiU-ExtB', 'Minion', 'Minion Pro', 'Miriam', 'Miriam Fixed', 'Mistral', 'Modern', 'Modern No. 20', 'Mona Lisa Solid ITC TT', 'Mongolian Baiti',\n        'MONO', 'MoolBoran', 'Mrs Eaves', 'MS LineDraw', 'MS Mincho', 'MS PMincho', 'MS Reference Specialty', 'MS UI Gothic', 'MT Extra', 'MUSEO', 'MV Boli',\n        'Nadeem', 'Narkisim', 'NEVIS', 'News Gothic', 'News GothicMT', 'NewsGoth BT', 'Niagara Engraved', 'Niagara Solid', 'Noteworthy', 'NSimSun', 'Nyala', 'OCR A Extended', 'Old Century', 'Old English Text MT', 'Onyx', 'Onyx BT', 'OPTIMA', 'Oriya Sangam MN',\n        'OSAKA', 'OzHandicraft BT', 'Palace Script MT', 'Papyrus', 'Parchment', 'Party LET', 'Pegasus', 'Perpetua', 'Perpetua Titling MT', 'PetitaBold', 'Pickwick', 'Plantagenet Cherokee', 'Playbill', 'PMingLiU', 'PMingLiU-ExtB',\n        'Poor Richard', 'Poster', 'PosterBodoni BT', 'PRINCETOWN LET', 'Pristina', 'PTBarnum BT', 'Pythagoras', 'Raavi', 'Rage Italic', 'Ravie', 'Ribbon131 Bd BT', 'Rockwell', 'Rockwell Condensed', 'Rockwell Extra Bold', 'Rod', 'Roman', 'Sakkal Majalla',\n        'Santa Fe LET', 'Savoye LET', 'Sceptre', 'Script', 'Script MT Bold', 'SCRIPTINA', 'Serifa', 'Serifa BT', 'Serifa Th BT', 'ShelleyVolante BT', 'Sherwood',\n        'Shonar Bangla', 'Showcard Gothic', 'Shruti', 'Signboard', 'SILKSCREEN', 'SimHei', 'Simplified Arabic', 'Simplified Arabic Fixed', 'SimSun', 'SimSun-ExtB', 'Sinhala Sangam MN', 'Sketch Rockwell', 'Skia', 'Small Fonts', 'Snap ITC', 'Snell Roundhand', 'Socket',\n        'Souvenir Lt BT', 'Staccato222 BT', 'Steamer', 'Stencil', 'Storybook', 'Styllo', 'Subway', 'Swis721 BlkEx BT', 'Swiss911 XCm BT', 'Sylfaen', 'Synchro LET', 'System', 'Tamil Sangam MN', 'Technical', 'Teletype', 'Telugu Sangam MN', 'Tempus Sans ITC',\n        'Terminal', 'Thonburi', 'Traditional Arabic', 'Trajan', 'TRAJAN PRO', 'Tristan', 'Tubular', 'Tunga', 'Tw Cen MT', 'Tw Cen MT Condensed', 'Tw Cen MT Condensed Extra Bold',\n        'TypoUpright BT', 'Unicorn', 'Univers', 'Univers CE 55 Medium', 'Univers Condensed', 'Utsaah', 'Vagabond', 'Vani', 'Vijaya', 'Viner Hand ITC', 'VisualUI', 'Vivaldi', 'Vladimir Script', 'Vrinda', 'Westminster', 'WHITNEY', 'Wide Latin',\n        'ZapfEllipt BT', 'ZapfHumnst BT', 'ZapfHumnst Dm BT', 'Zapfino', 'Zurich BlkEx BT', 'Zurich Ex BT', 'ZWAdobeF']\n      fontList = fontList.concat(extendedFontList)\n    }\n\n    fontList = fontList.concat(options.fonts.userDefinedFonts)\n\n    // remove duplicate fonts\n    fontList = fontList.filter(function (font, position) {\n      return fontList.indexOf(font) === position\n    })\n\n    // we use m or w because these two characters take up the maximum width.\n    // And we use a LLi so that the same matching fonts can get separated\n    var testString = 'mmmmmmmmmmlli'\n\n    // we test using 72px font size, we may use any size. I guess larger the better.\n    var testSize = '72px'\n\n    var h = document.getElementsByTagName('body')[0]\n\n    // div to load spans for the base fonts\n    var baseFontsDiv = document.createElement('div')\n\n    // div to load spans for the fonts to detect\n    var fontsDiv = document.createElement('div')\n\n    var defaultWidth = {}\n    var defaultHeight = {}\n\n    // creates a span where the fonts will be loaded\n    var createSpan = function () {\n      var s = document.createElement('span')\n      /*\n       * We need this css as in some weird browser this\n       * span elements shows up for a microSec which creates a\n       * bad user experience\n       */\n      s.style.position = 'absolute'\n      s.style.left = '-9999px'\n      s.style.fontSize = testSize\n\n      // css font reset to reset external styles\n      s.style.fontStyle = 'normal'\n      s.style.fontWeight = 'normal'\n      s.style.letterSpacing = 'normal'\n      s.style.lineBreak = 'auto'\n      s.style.lineHeight = 'normal'\n      s.style.textTransform = 'none'\n      s.style.textAlign = 'left'\n      s.style.textDecoration = 'none'\n      s.style.textShadow = 'none'\n      s.style.whiteSpace = 'normal'\n      s.style.wordBreak = 'normal'\n      s.style.wordSpacing = 'normal'\n\n      s.innerHTML = testString\n      return s\n    }\n\n    // creates a span and load the font to detect and a base font for fallback\n    var createSpanWithFonts = function (fontToDetect, baseFont) {\n      var s = createSpan()\n      s.style.fontFamily = \"'\" + fontToDetect + \"',\" + baseFont\n      return s\n    }\n\n    // creates spans for the base fonts and adds them to baseFontsDiv\n    var initializeBaseFontsSpans = function () {\n      var spans = []\n      for (var index = 0, length = baseFonts.length; index < length; index++) {\n        var s = createSpan()\n        s.style.fontFamily = baseFonts[index]\n        baseFontsDiv.appendChild(s)\n        spans.push(s)\n      }\n      return spans\n    }\n\n    // creates spans for the fonts to detect and adds them to fontsDiv\n    var initializeFontsSpans = function () {\n      var spans = {}\n      for (var i = 0, l = fontList.length; i < l; i++) {\n        var fontSpans = []\n        for (var j = 0, numDefaultFonts = baseFonts.length; j < numDefaultFonts; j++) {\n          var s = createSpanWithFonts(fontList[i], baseFonts[j])\n          fontsDiv.appendChild(s)\n          fontSpans.push(s)\n        }\n        spans[fontList[i]] = fontSpans // Stores {fontName : [spans for that font]}\n      }\n      return spans\n    }\n\n    // checks if a font is available\n    var isFontAvailable = function (fontSpans) {\n      var detected = false\n      for (var i = 0; i < baseFonts.length; i++) {\n        detected = (fontSpans[i].offsetWidth !== defaultWidth[baseFonts[i]] || fontSpans[i].offsetHeight !== defaultHeight[baseFonts[i]])\n        if (detected) {\n          return detected\n        }\n      }\n      return detected\n    }\n\n    // create spans for base fonts\n    var baseFontsSpans = initializeBaseFontsSpans()\n\n    // add the spans to the DOM\n    h.appendChild(baseFontsDiv)\n\n    // get the default width for the three base fonts\n    for (var index = 0, length = baseFonts.length; index < length; index++) {\n      defaultWidth[baseFonts[index]] = baseFontsSpans[index].offsetWidth // width for the default font\n      defaultHeight[baseFonts[index]] = baseFontsSpans[index].offsetHeight // height for the default font\n    }\n\n    // create spans for fonts to detect\n    var fontsSpans = initializeFontsSpans()\n\n    // add all the spans to the DOM\n    h.appendChild(fontsDiv)\n\n    // check available fonts\n    var available = []\n    for (var i = 0, l = fontList.length; i < l; i++) {\n      if (isFontAvailable(fontsSpans[fontList[i]])) {\n        available.push(fontList[i])\n      }\n    }\n\n    // remove spans from DOM\n    h.removeChild(fontsDiv)\n    h.removeChild(baseFontsDiv)\n    done(available)\n  }\n  var pluginsComponent = function (done, options) {\n    if (isIE()) {\n      if (!options.plugins.excludeIE) {\n        done(getIEPlugins(options))\n      } else {\n        done(options.EXCLUDED)\n      }\n    } else {\n      done(getRegularPlugins(options))\n    }\n  }\n  var getRegularPlugins = function (options) {\n    if (navigator.plugins == null) {\n      return options.NOT_AVAILABLE\n    }\n\n    var plugins = []\n    // plugins isn't defined in Node envs.\n    for (var i = 0, l = navigator.plugins.length; i < l; i++) {\n      if (navigator.plugins[i]) { plugins.push(navigator.plugins[i]) }\n    }\n\n    // sorting plugins only for those user agents, that we know randomize the plugins\n    // every time we try to enumerate them\n    if (pluginsShouldBeSorted(options)) {\n      plugins = plugins.sort(function (a, b) {\n        if (a.name > b.name) { return 1 }\n        if (a.name < b.name) { return -1 }\n        return 0\n      })\n    }\n    return map(plugins, function (p) {\n      var mimeTypes = map(p, function (mt) {\n        return [mt.type, mt.suffixes]\n      })\n      return [p.name, p.description, mimeTypes]\n    })\n  }\n  var getIEPlugins = function (options) {\n    var result = []\n    if ((Object.getOwnPropertyDescriptor && Object.getOwnPropertyDescriptor(window, 'ActiveXObject')) || ('ActiveXObject' in window)) {\n      var names = [\n        'AcroPDF.PDF', // Adobe PDF reader 7+\n        'Adodb.Stream',\n        'AgControl.AgControl', // Silverlight\n        'DevalVRXCtrl.DevalVRXCtrl.1',\n        'MacromediaFlashPaper.MacromediaFlashPaper',\n        'Msxml2.DOMDocument',\n        'Msxml2.XMLHTTP',\n        'PDF.PdfCtrl', // Adobe PDF reader 6 and earlier, brrr\n        'QuickTime.QuickTime', // QuickTime\n        'QuickTimeCheckObject.QuickTimeCheck.1',\n        'RealPlayer',\n        'RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)',\n        'RealVideo.RealVideo(tm) ActiveX Control (32-bit)',\n        'Scripting.Dictionary',\n        'SWCtl.SWCtl', // ShockWave player\n        'Shell.UIHelper',\n        'ShockwaveFlash.ShockwaveFlash', // flash plugin\n        'Skype.Detection',\n        'TDCCtl.TDCCtl',\n        'WMPlayer.OCX', // Windows media player\n        'rmocx.RealPlayer G2 Control',\n        'rmocx.RealPlayer G2 Control.1'\n      ]\n      // starting to detect plugins in IE\n      result = map(names, function (name) {\n        try {\n          // eslint-disable-next-line no-new\n          new window.ActiveXObject(name)\n          return name\n        } catch (e) {\n          return options.ERROR\n        }\n      })\n    } else {\n      result.push(options.NOT_AVAILABLE)\n    }\n    if (navigator.plugins) {\n      result = result.concat(getRegularPlugins(options))\n    }\n    return result\n  }\n  var pluginsShouldBeSorted = function (options) {\n    var should = false\n    for (var i = 0, l = options.plugins.sortPluginsFor.length; i < l; i++) {\n      var re = options.plugins.sortPluginsFor[i]\n      if (navigator.userAgent.match(re)) {\n        should = true\n        break\n      }\n    }\n    return should\n  }\n  var touchSupportKey = function (done) {\n    done(getTouchSupport())\n  }\n  var hardwareConcurrencyKey = function (done, options) {\n    done(getHardwareConcurrency(options))\n  }\n  var hasSessionStorage = function (options) {\n    try {\n      return !!window.sessionStorage\n    } catch (e) {\n      return options.ERROR // SecurityError when referencing it means it exists\n    }\n  }\n\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=781447\n  var hasLocalStorage = function (options) {\n    try {\n      return !!window.localStorage\n    } catch (e) {\n      return options.ERROR // SecurityError when referencing it means it exists\n    }\n  }\n  var hasIndexedDB = function (options) {\n    // IE and Edge don't allow accessing indexedDB in private mode, therefore IE and Edge will have different\n    // fingerprints in normal and private modes.\n    if (isIEOrOldEdge()) {\n      return options.EXCLUDED\n    }\n    try {\n      return !!window.indexedDB\n    } catch (e) {\n      return options.ERROR // SecurityError when referencing it means it exists\n    }\n  }\n  var getHardwareConcurrency = function (options) {\n    if (navigator.hardwareConcurrency) {\n      return navigator.hardwareConcurrency\n    }\n    return options.NOT_AVAILABLE\n  }\n  var getNavigatorCpuClass = function (options) {\n    return navigator.cpuClass || options.NOT_AVAILABLE\n  }\n  var getNavigatorPlatform = function (options) {\n    if (navigator.platform) {\n      return navigator.platform\n    } else {\n      return options.NOT_AVAILABLE\n    }\n  }\n  var getDoNotTrack = function (options) {\n    if (navigator.doNotTrack) {\n      return navigator.doNotTrack\n    } else if (navigator.msDoNotTrack) {\n      return navigator.msDoNotTrack\n    } else if (window.doNotTrack) {\n      return window.doNotTrack\n    } else {\n      return options.NOT_AVAILABLE\n    }\n  }\n  // This is a crude and primitive touch screen detection.\n  // It's not possible to currently reliably detect the  availability of a touch screen\n  // with a JS, without actually subscribing to a touch event.\n  // http://www.stucox.com/blog/you-cant-detect-a-touchscreen/\n  // https://github.com/Modernizr/Modernizr/issues/548\n  // method returns an array of 3 values:\n  // maxTouchPoints, the success or failure of creating a TouchEvent,\n  // and the availability of the 'ontouchstart' property\n\n  var getTouchSupport = function () {\n    var maxTouchPoints = 0\n    var touchEvent\n    if (typeof navigator.maxTouchPoints !== 'undefined') {\n      maxTouchPoints = navigator.maxTouchPoints\n    } else if (typeof navigator.msMaxTouchPoints !== 'undefined') {\n      maxTouchPoints = navigator.msMaxTouchPoints\n    }\n    try {\n      document.createEvent('TouchEvent')\n      touchEvent = true\n    } catch (_) {\n      touchEvent = false\n    }\n    var touchStart = 'ontouchstart' in window\n    return [maxTouchPoints, touchEvent, touchStart]\n  }\n  // https://www.browserleaks.com/canvas#how-does-it-work\n\n  var getCanvasFp = function (options) {\n    var result = []\n    // Very simple now, need to make it more complex (geo shapes etc)\n    var canvas = document.createElement('canvas')\n    canvas.width = 2000\n    canvas.height = 200\n    canvas.style.display = 'inline'\n    var ctx = canvas.getContext('2d')\n    // detect browser support of canvas winding\n    // http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // https://github.com/Modernizr/Modernizr/blob/master/feature-detects/canvas/winding.js\n    ctx.rect(0, 0, 10, 10)\n    ctx.rect(2, 2, 6, 6)\n    result.push('canvas winding:' + ((ctx.isPointInPath(5, 5, 'evenodd') === false) ? 'yes' : 'no'))\n\n    ctx.textBaseline = 'alphabetic'\n    ctx.fillStyle = '#f60'\n    ctx.fillRect(125, 1, 62, 20)\n    ctx.fillStyle = '#069'\n    // https://github.com/fingerprintjs/fingerprintjs/issues/66\n    if (options.dontUseFakeFontInCanvas) {\n      ctx.font = '11pt Arial'\n    } else {\n      ctx.font = '11pt no-real-font-123'\n    }\n    ctx.fillText('Cwm fjordbank glyphs vext quiz, \\ud83d\\ude03', 2, 15)\n    ctx.fillStyle = 'rgba(102, 204, 0, 0.2)'\n    ctx.font = '18pt Arial'\n    ctx.fillText('Cwm fjordbank glyphs vext quiz, \\ud83d\\ude03', 4, 45)\n\n    // canvas blending\n    // http://blogs.adobe.com/webplatform/2013/01/28/blending-features-in-canvas/\n    // http://jsfiddle.net/NDYV8/16/\n    ctx.globalCompositeOperation = 'multiply'\n    ctx.fillStyle = 'rgb(255,0,255)'\n    ctx.beginPath()\n    ctx.arc(50, 50, 50, 0, Math.PI * 2, true)\n    ctx.closePath()\n    ctx.fill()\n    ctx.fillStyle = 'rgb(0,255,255)'\n    ctx.beginPath()\n    ctx.arc(100, 50, 50, 0, Math.PI * 2, true)\n    ctx.closePath()\n    ctx.fill()\n    ctx.fillStyle = 'rgb(255,255,0)'\n    ctx.beginPath()\n    ctx.arc(75, 100, 50, 0, Math.PI * 2, true)\n    ctx.closePath()\n    ctx.fill()\n    ctx.fillStyle = 'rgb(255,0,255)'\n    // canvas winding\n    // http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // http://jsfiddle.net/NDYV8/19/\n    ctx.arc(75, 75, 75, 0, Math.PI * 2, true)\n    ctx.arc(75, 75, 25, 0, Math.PI * 2, true)\n    ctx.fill('evenodd')\n\n    if (canvas.toDataURL) { result.push('canvas fp:' + canvas.toDataURL()) }\n    return result\n  }\n  var getWebglFp = function () {\n    var gl\n    var fa2s = function (fa) {\n      gl.clearColor(0.0, 0.0, 0.0, 1.0)\n      gl.enable(gl.DEPTH_TEST)\n      gl.depthFunc(gl.LEQUAL)\n      gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT)\n      return '[' + fa[0] + ', ' + fa[1] + ']'\n    }\n    var maxAnisotropy = function (gl) {\n      var ext = gl.getExtension('EXT_texture_filter_anisotropic') || gl.getExtension('WEBKIT_EXT_texture_filter_anisotropic') || gl.getExtension('MOZ_EXT_texture_filter_anisotropic')\n      if (ext) {\n        var anisotropy = gl.getParameter(ext.MAX_TEXTURE_MAX_ANISOTROPY_EXT)\n        if (anisotropy === 0) {\n          anisotropy = 2\n        }\n        return anisotropy\n      } else {\n        return null\n      }\n    }\n\n    gl = getWebglCanvas()\n    if (!gl) { return null }\n    // WebGL fingerprinting is a combination of techniques, found in MaxMind antifraud script & Augur fingerprinting.\n    // First it draws a gradient object with shaders and convers the image to the Base64 string.\n    // Then it enumerates all WebGL extensions & capabilities and appends them to the Base64 string, resulting in a huge WebGL string, potentially very unique on each device\n    // Since iOS supports webgl starting from version 8.1 and 8.1 runs on several graphics chips, the results may be different across ios devices, but we need to verify it.\n    var result = []\n    var vShaderTemplate = 'attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}'\n    var fShaderTemplate = 'precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}'\n    var vertexPosBuffer = gl.createBuffer()\n    gl.bindBuffer(gl.ARRAY_BUFFER, vertexPosBuffer)\n    var vertices = new Float32Array([-0.2, -0.9, 0, 0.4, -0.26, 0, 0, 0.732134444, 0])\n    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW)\n    vertexPosBuffer.itemSize = 3\n    vertexPosBuffer.numItems = 3\n    var program = gl.createProgram()\n    var vshader = gl.createShader(gl.VERTEX_SHADER)\n    gl.shaderSource(vshader, vShaderTemplate)\n    gl.compileShader(vshader)\n    var fshader = gl.createShader(gl.FRAGMENT_SHADER)\n    gl.shaderSource(fshader, fShaderTemplate)\n    gl.compileShader(fshader)\n    gl.attachShader(program, vshader)\n    gl.attachShader(program, fshader)\n    gl.linkProgram(program)\n    gl.useProgram(program)\n    program.vertexPosAttrib = gl.getAttribLocation(program, 'attrVertex')\n    program.offsetUniform = gl.getUniformLocation(program, 'uniformOffset')\n    gl.enableVertexAttribArray(program.vertexPosArray)\n    gl.vertexAttribPointer(program.vertexPosAttrib, vertexPosBuffer.itemSize, gl.FLOAT, !1, 0, 0)\n    gl.uniform2f(program.offsetUniform, 1, 1)\n    gl.drawArrays(gl.TRIANGLE_STRIP, 0, vertexPosBuffer.numItems)\n    try {\n      result.push(gl.canvas.toDataURL())\n    } catch (e) {\n      /* .toDataURL may be absent or broken (blocked by extension) */\n    }\n    result.push('extensions:' + (gl.getSupportedExtensions() || []).join(';'))\n    result.push('webgl aliased line width range:' + fa2s(gl.getParameter(gl.ALIASED_LINE_WIDTH_RANGE)))\n    result.push('webgl aliased point size range:' + fa2s(gl.getParameter(gl.ALIASED_POINT_SIZE_RANGE)))\n    result.push('webgl alpha bits:' + gl.getParameter(gl.ALPHA_BITS))\n    result.push('webgl antialiasing:' + (gl.getContextAttributes().antialias ? 'yes' : 'no'))\n    result.push('webgl blue bits:' + gl.getParameter(gl.BLUE_BITS))\n    result.push('webgl depth bits:' + gl.getParameter(gl.DEPTH_BITS))\n    result.push('webgl green bits:' + gl.getParameter(gl.GREEN_BITS))\n    result.push('webgl max anisotropy:' + maxAnisotropy(gl))\n    result.push('webgl max combined texture image units:' + gl.getParameter(gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS))\n    result.push('webgl max cube map texture size:' + gl.getParameter(gl.MAX_CUBE_MAP_TEXTURE_SIZE))\n    result.push('webgl max fragment uniform vectors:' + gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS))\n    result.push('webgl max render buffer size:' + gl.getParameter(gl.MAX_RENDERBUFFER_SIZE))\n    result.push('webgl max texture image units:' + gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS))\n    result.push('webgl max texture size:' + gl.getParameter(gl.MAX_TEXTURE_SIZE))\n    result.push('webgl max varying vectors:' + gl.getParameter(gl.MAX_VARYING_VECTORS))\n    result.push('webgl max vertex attribs:' + gl.getParameter(gl.MAX_VERTEX_ATTRIBS))\n    result.push('webgl max vertex texture image units:' + gl.getParameter(gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS))\n    result.push('webgl max vertex uniform vectors:' + gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS))\n    result.push('webgl max viewport dims:' + fa2s(gl.getParameter(gl.MAX_VIEWPORT_DIMS)))\n    result.push('webgl red bits:' + gl.getParameter(gl.RED_BITS))\n    result.push('webgl renderer:' + gl.getParameter(gl.RENDERER))\n    result.push('webgl shading language version:' + gl.getParameter(gl.SHADING_LANGUAGE_VERSION))\n    result.push('webgl stencil bits:' + gl.getParameter(gl.STENCIL_BITS))\n    result.push('webgl vendor:' + gl.getParameter(gl.VENDOR))\n    result.push('webgl version:' + gl.getParameter(gl.VERSION))\n\n    try {\n      // Add the unmasked vendor and unmasked renderer if the debug_renderer_info extension is available\n      var extensionDebugRendererInfo = gl.getExtension('WEBGL_debug_renderer_info')\n      if (extensionDebugRendererInfo) {\n        result.push('webgl unmasked vendor:' + gl.getParameter(extensionDebugRendererInfo.UNMASKED_VENDOR_WEBGL))\n        result.push('webgl unmasked renderer:' + gl.getParameter(extensionDebugRendererInfo.UNMASKED_RENDERER_WEBGL))\n      }\n    } catch (e) { /* squelch */ }\n\n    if (!gl.getShaderPrecisionFormat) {\n      loseWebglContext(gl)\n      return result\n    }\n\n    each(['FLOAT', 'INT'], function (numType) {\n      each(['VERTEX', 'FRAGMENT'], function (shader) {\n        each(['HIGH', 'MEDIUM', 'LOW'], function (numSize) {\n          each(['precision', 'rangeMin', 'rangeMax'], function (key) {\n            var format = gl.getShaderPrecisionFormat(gl[shader + '_SHADER'], gl[numSize + '_' + numType])[key]\n            if (key !== 'precision') {\n              key = 'precision ' + key\n            }\n            var line = ['webgl ', shader.toLowerCase(), ' shader ', numSize.toLowerCase(), ' ', numType.toLowerCase(), ' ', key, ':', format].join('')\n            result.push(line)\n          })\n        })\n      })\n    })\n    loseWebglContext(gl)\n    return result\n  }\n  var getWebglVendorAndRenderer = function () {\n    /* This a subset of the WebGL fingerprint with a lot of entropy, while being reasonably browser-independent */\n    try {\n      var glContext = getWebglCanvas()\n      var extensionDebugRendererInfo = glContext.getExtension('WEBGL_debug_renderer_info')\n      var params = glContext.getParameter(extensionDebugRendererInfo.UNMASKED_VENDOR_WEBGL) + '~' + glContext.getParameter(extensionDebugRendererInfo.UNMASKED_RENDERER_WEBGL)\n      loseWebglContext(glContext)\n      return params\n    } catch (e) {\n      return null\n    }\n  }\n  var getAdBlock = function () {\n    var ads = document.createElement('div')\n    ads.innerHTML = '&nbsp;'\n    ads.className = 'adsbox'\n    var result = false\n    try {\n      // body may not exist, that's why we need try/catch\n      document.body.appendChild(ads)\n      result = document.getElementsByClassName('adsbox')[0].offsetHeight === 0\n      document.body.removeChild(ads)\n    } catch (e) {\n      result = false\n    }\n    return result\n  }\n  var getHasLiedLanguages = function () {\n    // We check if navigator.language is equal to the first language of navigator.languages\n    // navigator.languages is undefined on IE11 (and potentially older IEs)\n    if (typeof navigator.languages !== 'undefined') {\n      try {\n        var firstLanguages = navigator.languages[0].substr(0, 2)\n        if (firstLanguages !== navigator.language.substr(0, 2)) {\n          return true\n        }\n      } catch (err) {\n        return true\n      }\n    }\n    return false\n  }\n  var getHasLiedResolution = function () {\n    return window.screen.width < window.screen.availWidth || window.screen.height < window.screen.availHeight\n  }\n  var getHasLiedOs = function () {\n    var userAgent = navigator.userAgent.toLowerCase()\n    var oscpu = navigator.oscpu\n    var platform = navigator.platform.toLowerCase()\n    var os\n    // We extract the OS from the user agent (respect the order of the if else if statement)\n    if (userAgent.indexOf('windows phone') >= 0) {\n      os = 'Windows Phone'\n    } else if (userAgent.indexOf('windows') >= 0 || userAgent.indexOf('win16') >= 0 || userAgent.indexOf('win32') >= 0 || userAgent.indexOf('win64') >= 0 || userAgent.indexOf('win95') >= 0 || userAgent.indexOf('win98') >= 0 || userAgent.indexOf('winnt') >= 0 || userAgent.indexOf('wow64') >= 0) {\n      os = 'Windows'\n    } else if (userAgent.indexOf('android') >= 0) {\n      os = 'Android'\n    } else if (userAgent.indexOf('linux') >= 0 || userAgent.indexOf('cros') >= 0 || userAgent.indexOf('x11') >= 0) {\n      os = 'Linux'\n    } else if (userAgent.indexOf('iphone') >= 0 || userAgent.indexOf('ipad') >= 0 || userAgent.indexOf('ipod') >= 0 || userAgent.indexOf('crios') >= 0 || userAgent.indexOf('fxios') >= 0) {\n      os = 'iOS'\n    } else if (userAgent.indexOf('macintosh') >= 0 || userAgent.indexOf('mac_powerpc)') >= 0) {\n      os = 'Mac'\n    } else {\n      os = 'Other'\n    }\n    // We detect if the person uses a touch device\n    var mobileDevice = (('ontouchstart' in window) ||\n      (navigator.maxTouchPoints > 0) ||\n      (navigator.msMaxTouchPoints > 0))\n\n    if (mobileDevice && os !== 'Windows' && os !== 'Windows Phone' && os !== 'Android' && os !== 'iOS' && os !== 'Other' && userAgent.indexOf('cros') === -1) {\n      return true\n    }\n\n    // We compare oscpu with the OS extracted from the UA\n    if (typeof oscpu !== 'undefined') {\n      oscpu = oscpu.toLowerCase()\n      if (oscpu.indexOf('win') >= 0 && os !== 'Windows' && os !== 'Windows Phone') {\n        return true\n      } else if (oscpu.indexOf('linux') >= 0 && os !== 'Linux' && os !== 'Android') {\n        return true\n      } else if (oscpu.indexOf('mac') >= 0 && os !== 'Mac' && os !== 'iOS') {\n        return true\n      } else if ((oscpu.indexOf('win') === -1 && oscpu.indexOf('linux') === -1 && oscpu.indexOf('mac') === -1) !== (os === 'Other')) {\n        return true\n      }\n    }\n\n    // We compare platform with the OS extracted from the UA\n    if (platform.indexOf('win') >= 0 && os !== 'Windows' && os !== 'Windows Phone') {\n      return true\n    } else if ((platform.indexOf('linux') >= 0 || platform.indexOf('android') >= 0 || platform.indexOf('pike') >= 0) && os !== 'Linux' && os !== 'Android') {\n      return true\n    } else if ((platform.indexOf('mac') >= 0 || platform.indexOf('ipad') >= 0 || platform.indexOf('ipod') >= 0 || platform.indexOf('iphone') >= 0) && os !== 'Mac' && os !== 'iOS') {\n      return true\n    } else if (platform.indexOf('arm') >= 0 && os === 'Windows Phone') {\n      return false\n    } else if (platform.indexOf('pike') >= 0 && userAgent.indexOf('opera mini') >= 0) {\n      return false\n    } else {\n      var platformIsOther = platform.indexOf('win') < 0 &&\n        platform.indexOf('linux') < 0 &&\n        platform.indexOf('mac') < 0 &&\n        platform.indexOf('iphone') < 0 &&\n        platform.indexOf('ipad') < 0 &&\n        platform.indexOf('ipod') < 0\n      if (platformIsOther !== (os === 'Other')) {\n        return true\n      }\n    }\n\n    return typeof navigator.plugins === 'undefined' && os !== 'Windows' && os !== 'Windows Phone'\n  }\n  var getHasLiedBrowser = function () {\n    var userAgent = navigator.userAgent.toLowerCase()\n    var productSub = navigator.productSub\n\n    // we extract the browser from the user agent (respect the order of the tests)\n    var browser\n    if (userAgent.indexOf('edge/') >= 0 || userAgent.indexOf('iemobile/') >= 0) {\n      // Unreliable, different versions use EdgeHTML, Webkit, Blink, etc.\n      return false\n    } else if (userAgent.indexOf('opera mini') >= 0) {\n      // Unreliable, different modes use Presto, WebView, Webkit, etc.\n      return false\n    } else if (userAgent.indexOf('firefox/') >= 0) {\n      browser = 'Firefox'\n    } else if (userAgent.indexOf('opera/') >= 0 || userAgent.indexOf(' opr/') >= 0) {\n      browser = 'Opera'\n    } else if (userAgent.indexOf('chrome/') >= 0) {\n      browser = 'Chrome'\n    } else if (userAgent.indexOf('safari/') >= 0) {\n      if (userAgent.indexOf('android 1.') >= 0 || userAgent.indexOf('android 2.') >= 0 || userAgent.indexOf('android 3.') >= 0 || userAgent.indexOf('android 4.') >= 0) {\n        browser = 'AOSP'\n      } else {\n        browser = 'Safari'\n      }\n    } else if (userAgent.indexOf('trident/') >= 0) {\n      browser = 'Internet Explorer'\n    } else {\n      browser = 'Other'\n    }\n\n    if ((browser === 'Chrome' || browser === 'Safari' || browser === 'Opera') && productSub !== '20030107') {\n      return true\n    }\n\n    // eslint-disable-next-line no-eval\n    var tempRes = eval.toString().length\n    if (tempRes === 37 && browser !== 'Safari' && browser !== 'Firefox' && browser !== 'Other') {\n      return true\n    } else if (tempRes === 39 && browser !== 'Internet Explorer' && browser !== 'Other') {\n      return true\n    } else if (tempRes === 33 && browser !== 'Chrome' && browser !== 'AOSP' && browser !== 'Opera' && browser !== 'Other') {\n      return true\n    }\n\n    // We create an error to see how it is handled\n    var errFirefox\n    try {\n      // eslint-disable-next-line no-throw-literal\n      throw 'a'\n    } catch (err) {\n      try {\n        err.toSource()\n        errFirefox = true\n      } catch (errOfErr) {\n        errFirefox = false\n      }\n    }\n    return errFirefox && browser !== 'Firefox' && browser !== 'Other'\n  }\n  var isCanvasSupported = function () {\n    var elem = document.createElement('canvas')\n    return !!(elem.getContext && elem.getContext('2d'))\n  }\n  var isWebGlSupported = function () {\n    // code taken from Modernizr\n    if (!isCanvasSupported()) {\n      return false\n    }\n\n    var glContext = getWebglCanvas()\n    var isSupported = !!window.WebGLRenderingContext && !!glContext\n    loseWebglContext(glContext)\n    return isSupported\n  }\n  var isIE = function () {\n    if (navigator.appName === 'Microsoft Internet Explorer') {\n      return true\n    } else if (navigator.appName === 'Netscape' && /Trident/.test(navigator.userAgent)) { // IE 11\n      return true\n    }\n    return false\n  }\n  var isIEOrOldEdge = function () {\n    // The properties are checked to be in IE 10, IE 11 and Edge 18 and not to be in other browsers\n    return ('msWriteProfilerMark' in window) + ('msLaunchUri' in navigator) + ('msSaveBlob' in navigator) >= 2\n  }\n  var hasSwfObjectLoaded = function () {\n    return typeof window.swfobject !== 'undefined'\n  }\n  var hasMinFlashInstalled = function () {\n    return window.swfobject.hasFlashPlayerVersion('9.0.0')\n  }\n  var addFlashDivNode = function (options) {\n    var node = document.createElement('div')\n    node.setAttribute('id', options.fonts.swfContainerId)\n    document.body.appendChild(node)\n  }\n  var loadSwfAndDetectFonts = function (done, options) {\n    var hiddenCallback = '___fp_swf_loaded'\n    window[hiddenCallback] = function (fonts) {\n      done(fonts)\n    }\n    var id = options.fonts.swfContainerId\n    addFlashDivNode()\n    var flashvars = { onReady: hiddenCallback }\n    var flashparams = { allowScriptAccess: 'always', menu: 'false' }\n    window.swfobject.embedSWF(options.fonts.swfPath, id, '1', '1', '9.0.0', false, flashvars, flashparams, {})\n  }\n  var getWebglCanvas = function () {\n    var canvas = document.createElement('canvas')\n    var gl = null\n    try {\n      gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')\n    } catch (e) { /* squelch */ }\n    if (!gl) { gl = null }\n    return gl\n  }\n  var loseWebglContext = function (context) {\n    var loseContextExtension = context.getExtension('WEBGL_lose_context')\n    if (loseContextExtension != null) {\n      loseContextExtension.loseContext()\n    }\n  }\n\n  var components = [\n    { key: 'userAgent', getData: UserAgent },\n    { key: 'webdriver', getData: webdriver },\n    { key: 'language', getData: languageKey },\n    { key: 'colorDepth', getData: colorDepthKey },\n    { key: 'deviceMemory', getData: deviceMemoryKey },\n    { key: 'pixelRatio', getData: pixelRatioKey },\n    { key: 'hardwareConcurrency', getData: hardwareConcurrencyKey },\n    { key: 'screenResolution', getData: screenResolutionKey },\n    { key: 'availableScreenResolution', getData: availableScreenResolutionKey },\n    { key: 'timezoneOffset', getData: timezoneOffset },\n    { key: 'timezone', getData: timezone },\n    { key: 'sessionStorage', getData: sessionStorageKey },\n    { key: 'localStorage', getData: localStorageKey },\n    { key: 'indexedDb', getData: indexedDbKey },\n    { key: 'addBehavior', getData: addBehaviorKey },\n    { key: 'openDatabase', getData: openDatabaseKey },\n    { key: 'cpuClass', getData: cpuClassKey },\n    { key: 'platform', getData: platformKey },\n    { key: 'doNotTrack', getData: doNotTrackKey },\n    { key: 'plugins', getData: pluginsComponent },\n    { key: 'canvas', getData: canvasKey },\n    { key: 'webgl', getData: webglKey },\n    { key: 'webglVendorAndRenderer', getData: webglVendorAndRendererKey },\n    { key: 'adBlock', getData: adBlockKey },\n    { key: 'hasLiedLanguages', getData: hasLiedLanguagesKey },\n    { key: 'hasLiedResolution', getData: hasLiedResolutionKey },\n    { key: 'hasLiedOs', getData: hasLiedOsKey },\n    { key: 'hasLiedBrowser', getData: hasLiedBrowserKey },\n    { key: 'touchSupport', getData: touchSupportKey },\n    { key: 'fonts', getData: jsFontsKey, pauseBefore: true },\n    { key: 'fontsFlash', getData: flashFontsKey, pauseBefore: true },\n    { key: 'audio', getData: audioKey },\n    { key: 'enumerateDevices', getData: enumerateDevicesKey }\n  ]\n\n  var Fingerprint2 = function (options) {\n    throw new Error(\"'new Fingerprint()' is deprecated, see https://github.com/fingerprintjs/fingerprintjs#upgrade-guide-from-182-to-200\")\n  }\n\n  Fingerprint2.get = function (options, callback) {\n    if (!callback) {\n      callback = options\n      options = {}\n    } else if (!options) {\n      options = {}\n    }\n    extendSoft(options, defaultOptions)\n    options.components = options.extraComponents.concat(components)\n\n    var keys = {\n      data: [],\n      addPreprocessedComponent: function (key, value) {\n        if (typeof options.preprocessor === 'function') {\n          value = options.preprocessor(key, value)\n        }\n        keys.data.push({ key: key, value: value })\n      }\n    }\n\n    var i = -1\n    var chainComponents = function (alreadyWaited) {\n      i += 1\n      if (i >= options.components.length) { // on finish\n        callback(keys.data)\n        return\n      }\n      var component = options.components[i]\n\n      if (options.excludes[component.key]) {\n        chainComponents(false) // skip\n        return\n      }\n\n      if (!alreadyWaited && component.pauseBefore) {\n        i -= 1\n        setTimeout(function () {\n          chainComponents(true)\n        }, 1)\n        return\n      }\n\n      try {\n        component.getData(function (value) {\n          keys.addPreprocessedComponent(component.key, value)\n          chainComponents(false)\n        }, options)\n      } catch (error) {\n        // main body error\n        keys.addPreprocessedComponent(component.key, String(error))\n        chainComponents(false)\n      }\n    }\n\n    chainComponents(false)\n  }\n\n  Fingerprint2.getPromise = function (options) {\n    return new Promise(function (resolve, reject) {\n      Fingerprint2.get(options, resolve)\n    })\n  }\n\n  Fingerprint2.getV18 = function (options, callback) {\n    if (callback == null) {\n      callback = options\n      options = {}\n    }\n    return Fingerprint2.get(options, function (components) {\n      var newComponents = []\n      for (var i = 0; i < components.length; i++) {\n        var component = components[i]\n        if (component.value === (options.NOT_AVAILABLE || 'not available')) {\n          newComponents.push({ key: component.key, value: 'unknown' })\n        } else if (component.key === 'plugins') {\n          newComponents.push({\n            key: 'plugins',\n            value: map(component.value, function (p) {\n              var mimeTypes = map(p[2], function (mt) {\n                if (mt.join) { return mt.join('~') }\n                return mt\n              }).join(',')\n              return [p[0], p[1], mimeTypes].join('::')\n            })\n          })\n        } else if (['canvas', 'webgl'].indexOf(component.key) !== -1 && Array.isArray(component.value)) {\n          // sometimes WebGL returns error in headless browsers (during CI testing for example)\n          // so we need to join only if the values are array\n          newComponents.push({ key: component.key, value: component.value.join('~') })\n        } else if (['sessionStorage', 'localStorage', 'indexedDb', 'addBehavior', 'openDatabase'].indexOf(component.key) !== -1) {\n          if (component.value) {\n            newComponents.push({ key: component.key, value: 1 })\n          } else {\n            // skip\n            continue\n          }\n        } else {\n          if (component.value) {\n            newComponents.push(component.value.join ? { key: component.key, value: component.value.join(';') } : component)\n          } else {\n            newComponents.push({ key: component.key, value: component.value })\n          }\n        }\n      }\n      var murmur = x64hash128(map(newComponents, function (component) { return component.value }).join('~~~'), 31)\n      callback(murmur, newComponents)\n    })\n  }\n\n  Fingerprint2.x64hash128 = x64hash128\n  Fingerprint2.VERSION = '2.1.4'\n  return Fingerprint2\n})\n", "export default require(\"./node_modules/fingerprintjs2/fingerprint2.js\");"], "mappings": ";;;;;AAAA;AAAA;AAuBA,IAAC,UAAU,MAAM,SAAS,YAAY;AACpC;AACA,UAAI,OAAO,WAAW,eAAe,OAAO,WAAW,cAAc,OAAO,KAAK;AAAE,eAAO;AAAA,iBAAuB,OAAO,WAAW,eAAe,OAAO,SAAS;AAAE,eAAO,UAAU;AAAA,iBAAwB,QAAQ,SAAS;AAAE,gBAAQ,UAAU;AAAA,aAAoB;AAAE,gBAAQ,QAAQ;AAAA;AAAA,OACvR,gBAAgB,SAAM,WAAY;AACnC;AAIA,UAAI,OAAO,MAAM,YAAY,aAAa;AACxC,cAAM,UAAU,SAAU,KAAK;AAC7B,iBAAO,OAAO,UAAU,SAAS,KAAK,SAAS;AAAA;AAAA;AAElD;AAQD,UAAI,SAAS,SAAU,GAAG,GAAG;AAC3B,YAAI,CAAC,EAAE,OAAO,IAAI,EAAE,KAAK,OAAQ,EAAE,OAAO,IAAI,EAAE,KAAK;AACrD,YAAI,CAAC,EAAE,OAAO,IAAI,EAAE,KAAK,OAAQ,EAAE,OAAO,IAAI,EAAE,KAAK;AACrD,YAAI,IAAI,CAAC,GAAG,GAAG,GAAG;AAClB,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM;AACR,eAAO,CAAE,EAAE,MAAM,KAAM,EAAE,IAAK,EAAE,MAAM,KAAM,EAAE;AAAA;AAOhD,UAAI,cAAc,SAAU,GAAG,GAAG;AAChC,YAAI,CAAC,EAAE,OAAO,IAAI,EAAE,KAAK,OAAQ,EAAE,OAAO,IAAI,EAAE,KAAK;AACrD,YAAI,CAAC,EAAE,OAAO,IAAI,EAAE,KAAK,OAAQ,EAAE,OAAO,IAAI,EAAE,KAAK;AACrD,YAAI,IAAI,CAAC,GAAG,GAAG,GAAG;AAClB,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAM,EAAE,KAAK,EAAE;AACjB,UAAE,MAAM,EAAE,OAAO;AACjB,UAAE,MAAM;AACR,UAAE,MAAO,EAAE,KAAK,EAAE,KAAO,EAAE,KAAK,EAAE,KAAO,EAAE,KAAK,EAAE,KAAO,EAAE,KAAK,EAAE;AAClE,UAAE,MAAM;AACR,eAAO,CAAE,EAAE,MAAM,KAAM,EAAE,IAAK,EAAE,MAAM,KAAM,EAAE;AAAA;AAOhD,UAAI,UAAU,SAAU,GAAG,GAAG;AAC5B,aAAK;AACL,YAAI,MAAM,IAAI;AACZ,iBAAO,CAAC,EAAE,IAAI,EAAE;AAAA,mBACP,IAAI,IAAI;AACjB,iBAAO,CAAE,EAAE,MAAM,IAAM,EAAE,OAAQ,KAAK,GAAM,EAAE,MAAM,IAAM,EAAE,OAAQ,KAAK;AAAA,eACpE;AACL,eAAK;AACL,iBAAO,CAAE,EAAE,MAAM,IAAM,EAAE,OAAQ,KAAK,GAAM,EAAE,MAAM,IAAM,EAAE,OAAQ,KAAK;AAAA;AAAA;AAQ7E,UAAI,eAAe,SAAU,GAAG,GAAG;AACjC,aAAK;AACL,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,mBACE,IAAI,IAAI;AACjB,iBAAO,CAAE,EAAE,MAAM,IAAM,EAAE,OAAQ,KAAK,GAAK,EAAE,MAAM;AAAA,eAC9C;AACL,iBAAO,CAAC,EAAE,MAAO,IAAI,IAAK;AAAA;AAAA;AAO9B,UAAI,SAAS,SAAU,GAAG,GAAG;AAC3B,eAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;AAAA;AAOhC,UAAI,UAAU,SAAU,GAAG;AACzB,YAAI,OAAO,GAAG,CAAC,GAAG,EAAE,OAAO;AAC3B,YAAI,YAAY,GAAG,CAAC,YAAY;AAChC,YAAI,OAAO,GAAG,CAAC,GAAG,EAAE,OAAO;AAC3B,YAAI,YAAY,GAAG,CAAC,YAAY;AAChC,YAAI,OAAO,GAAG,CAAC,GAAG,EAAE,OAAO;AAC3B,eAAO;AAAA;AAOT,UAAI,aAAa,SAAU,KAAK,MAAM;AACpC,cAAM,OAAO;AACb,eAAO,QAAQ;AACf,YAAI,YAAY,IAAI,SAAS;AAC7B,YAAI,QAAQ,IAAI,SAAS;AACzB,YAAI,KAAK,CAAC,GAAG;AACb,YAAI,KAAK,CAAC,GAAG;AACb,YAAI,KAAK,CAAC,GAAG;AACb,YAAI,KAAK,CAAC,GAAG;AACb,YAAI,KAAK,CAAC,YAAY;AACtB,YAAI,KAAK,CAAC,YAAY;AACtB,iBAAS,IAAI,GAAG,IAAI,OAAO,IAAI,IAAI,IAAI;AACrC,eAAK,CAAG,IAAI,WAAW,IAAI,KAAK,MAAW,KAAI,WAAW,IAAI,KAAK,QAAS,IAAO,KAAI,WAAW,IAAI,KAAK,QAAS,KAAQ,KAAI,WAAW,IAAI,KAAK,QAAS,IAAO,IAAI,WAAW,KAAK,MAAW,KAAI,WAAW,IAAI,KAAK,QAAS,IAAO,KAAI,WAAW,IAAI,KAAK,QAAS,KAAQ,KAAI,WAAW,IAAI,KAAK,QAAS;AACrT,eAAK,CAAG,IAAI,WAAW,IAAI,MAAM,MAAW,KAAI,WAAW,IAAI,MAAM,QAAS,IAAO,KAAI,WAAW,IAAI,MAAM,QAAS,KAAQ,KAAI,WAAW,IAAI,MAAM,QAAS,IAAO,IAAI,WAAW,IAAI,KAAK,MAAW,KAAI,WAAW,IAAI,KAAK,QAAS,IAAO,KAAI,WAAW,IAAI,MAAM,QAAS,KAAQ,KAAI,WAAW,IAAI,MAAM,QAAS;AAC/T,eAAK,YAAY,IAAI;AACrB,eAAK,QAAQ,IAAI;AACjB,eAAK,YAAY,IAAI;AACrB,eAAK,OAAO,IAAI;AAChB,eAAK,QAAQ,IAAI;AACjB,eAAK,OAAO,IAAI;AAChB,eAAK,OAAO,YAAY,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG;AACzC,eAAK,YAAY,IAAI;AACrB,eAAK,QAAQ,IAAI;AACjB,eAAK,YAAY,IAAI;AACrB,eAAK,OAAO,IAAI;AAChB,eAAK,QAAQ,IAAI;AACjB,eAAK,OAAO,IAAI;AAChB,eAAK,OAAO,YAAY,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG;AAAA;AAE3C,aAAK,CAAC,GAAG;AACT,aAAK,CAAC,GAAG;AACT,gBAAQ;AAAA,eACD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,MAAM;AAAA,eAEvD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,MAAM;AAAA,eAEvD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,MAAM;AAAA,eAEvD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,MAAM;AAAA,eAEvD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,MAAM;AAAA,eAEvD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,KAAK;AAAA,eAEtD;AACH,iBAAK,OAAO,IAAI,CAAC,GAAG,IAAI,WAAW,IAAI;AACvC,iBAAK,YAAY,IAAI;AACrB,iBAAK,QAAQ,IAAI;AACjB,iBAAK,YAAY,IAAI;AACrB,iBAAK,OAAO,IAAI;AAAA,eAEb;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,KAAK;AAAA,eAEtD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,KAAK;AAAA,eAEtD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,KAAK;AAAA,eAEtD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,KAAK;AAAA,eAEtD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,KAAK;AAAA,eAEtD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,KAAK;AAAA,eAEtD;AACH,iBAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,KAAK;AAAA,eAEtD;AACH,iBAAK,OAAO,IAAI,CAAC,GAAG,IAAI,WAAW;AACnC,iBAAK,YAAY,IAAI;AACrB,iBAAK,QAAQ,IAAI;AACjB,iBAAK,YAAY,IAAI;AACrB,iBAAK,OAAO,IAAI;AAAA;AAGpB,aAAK,OAAO,IAAI,CAAC,GAAG,IAAI;AACxB,aAAK,OAAO,IAAI,CAAC,GAAG,IAAI;AACxB,aAAK,OAAO,IAAI;AAChB,aAAK,OAAO,IAAI;AAChB,aAAK,QAAQ;AACb,aAAK,QAAQ;AACb,aAAK,OAAO,IAAI;AAChB,aAAK,OAAO,IAAI;AAChB,eAAQ,cAAc,IAAG,OAAO,GAAG,SAAS,KAAK,MAAM,MAAO,cAAc,IAAG,OAAO,GAAG,SAAS,KAAK,MAAM,MAAO,cAAc,IAAG,OAAO,GAAG,SAAS,KAAK,MAAM,MAAO,cAAc,IAAG,OAAO,GAAG,SAAS,KAAK,MAAM;AAAA;AAG3N,UAAI,iBAAiB;AAAA,QACnB,cAAc;AAAA,QACd,OAAO;AAAA,UACL,SAAS;AAAA,UAIT,cAAc;AAAA;AAAA,QAEhB,OAAO;AAAA,UACL,gBAAgB;AAAA,UAChB,SAAS;AAAA,UACT,kBAAkB;AAAA,UAClB,iBAAiB;AAAA;AAAA,QAEnB,QAAQ;AAAA,UAEN,yBAAyB;AAAA;AAAA,QAE3B,SAAS;AAAA,UACP,gBAAgB,CAAC;AAAA,UACjB,WAAW;AAAA;AAAA,QAEb,iBAAiB;AAAA,QACjB,UAAU;AAAA,UAER,oBAAoB;AAAA,UAEpB,cAAc;AAAA,UAEd,cAAc;AAAA,UAEd,cAAc;AAAA,UAGd,WAAW;AAAA;AAAA,QAEb,eAAe;AAAA,QACf,OAAO;AAAA,QACP,UAAU;AAAA;AAGZ,UAAI,OAAO,SAAU,KAAK,UAAU;AAClC,YAAI,MAAM,UAAU,WAAW,IAAI,YAAY,MAAM,UAAU,SAAS;AACtE,cAAI,QAAQ;AAAA,mBACH,IAAI,WAAW,CAAC,IAAI,QAAQ;AACrC,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AAC1C,qBAAS,IAAI,IAAI,GAAG;AAAA;AAAA,eAEjB;AACL,mBAAS,OAAO,KAAK;AACnB,gBAAI,IAAI,eAAe,MAAM;AAC3B,uBAAS,IAAI,MAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAMhC,UAAI,MAAM,SAAU,KAAK,UAAU;AACjC,YAAI,UAAU;AAGd,YAAI,OAAO,MAAM;AACf,iBAAO;AAAA;AAET,YAAI,MAAM,UAAU,OAAO,IAAI,QAAQ,MAAM,UAAU,KAAK;AAAE,iBAAO,IAAI,IAAI;AAAA;AAC7E,aAAK,KAAK,SAAU,OAAO,OAAO,MAAM;AACtC,kBAAQ,KAAK,SAAS,OAAO,OAAO;AAAA;AAEtC,eAAO;AAAA;AAGT,UAAI,aAAa,SAAU,QAAQ,QAAQ;AACzC,YAAI,UAAU,MAAM;AAAE,iBAAO;AAAA;AAC7B,YAAI;AACJ,YAAI;AACJ,aAAK,OAAO,QAAQ;AAClB,kBAAQ,OAAO;AACf,cAAI,SAAS,QAAQ,CAAE,OAAO,UAAU,eAAe,KAAK,QAAQ,MAAO;AACzE,mBAAO,OAAO;AAAA;AAAA;AAGlB,eAAO;AAAA;AAIT,UAAI,sBAAsB,SAAU,MAAM,SAAS;AACjD,YAAI,CAAC,+BAA+B;AAClC,iBAAO,KAAK,QAAQ;AAAA;AAEtB,kBAAU,aAAa,mBAAmB,KAAK,SAAU,SAAS;AAChE,eAAK,QAAQ,IAAI,SAAU,QAAQ;AACjC,mBAAO,QAAQ,OAAO,WAAW,UAAU,OAAO,UAAU,MAAM,OAAO,OAAO,MAAM,OAAO;AAAA;AAAA,WAE9F,MAAM,SAAU,OAAO;AACxB,eAAK;AAAA;AAAA;AAIT,UAAI,8BAA8B,WAAY;AAC5C,eAAQ,UAAU,gBAAgB,UAAU,aAAa;AAAA;AAG3D,UAAI,WAAW,SAAU,MAAM,SAAS;AACtC,YAAI,eAAe,QAAQ;AAC3B,YAAI,aAAa,gBAAgB,UAAU,UAAU,MAAM,+BAA+B;AAExF,iBAAO,KAAK,QAAQ;AAAA;AAGtB,YAAI,eAAe,OAAO,uBAAuB,OAAO;AAExD,YAAI,gBAAgB,MAAM;AACxB,iBAAO,KAAK,QAAQ;AAAA;AAGtB,YAAI,UAAU,IAAI,aAAa,GAAG,OAAO;AAEzC,YAAI,aAAa,QAAQ;AACzB,mBAAW,OAAO;AAClB,mBAAW,UAAU,eAAe,KAAO,QAAQ;AAEnD,YAAI,aAAa,QAAQ;AACzB,aAAK;AAAA,UACH,CAAC,aAAa;AAAA,UACd,CAAC,QAAQ;AAAA,UACT,CAAC,SAAS;AAAA,UACV,CAAC,aAAa;AAAA,UACd,CAAC,UAAU;AAAA,UACX,CAAC,WAAW;AAAA,WACX,SAAU,MAAM;AACjB,cAAI,WAAW,KAAK,QAAQ,UAAa,OAAO,WAAW,KAAK,IAAI,mBAAmB,YAAY;AACjG,uBAAW,KAAK,IAAI,eAAe,KAAK,IAAI,QAAQ;AAAA;AAAA;AAIxD,mBAAW,QAAQ;AACnB,mBAAW,QAAQ,QAAQ;AAC3B,mBAAW,MAAM;AACjB,gBAAQ;AAER,YAAI,iBAAiB,WAAW,WAAY;AAC1C,kBAAQ,KAAK,6HAA6H,UAAU,YAAY;AAChK,kBAAQ,aAAa,WAAY;AAAA;AACjC,oBAAU;AACV,iBAAO,KAAK;AAAA,WACX,aAAa;AAEhB,gBAAQ,aAAa,SAAU,OAAO;AACpC,cAAI;AACJ,cAAI;AACF,yBAAa;AACb,0BAAc,MAAM,eAAe,eAAe,GAC/C,MAAM,MAAM,KACZ,OAAO,SAAU,KAAK,KAAK;AAAE,qBAAO,MAAM,KAAK,IAAI;AAAA,eAAQ,GAC3D;AACH,uBAAW;AACX,uBAAW;AAAA,mBACJ,OAAP;AACA,iBAAK;AACL;AAAA;AAEF,eAAK;AAAA;AAAA;AAGT,UAAI,YAAY,SAAU,MAAM;AAC9B,aAAK,UAAU;AAAA;AAEjB,UAAI,YAAY,SAAU,MAAM,SAAS;AACvC,aAAK,UAAU,aAAa,OAAO,QAAQ,gBAAgB,UAAU;AAAA;AAEvE,UAAI,cAAc,SAAU,MAAM,SAAS;AACzC,aAAK,UAAU,YAAY,UAAU,gBAAgB,UAAU,mBAAmB,UAAU,kBAAkB,QAAQ;AAAA;AAExH,UAAI,gBAAgB,SAAU,MAAM,SAAS;AAC3C,aAAK,OAAO,OAAO,cAAc,QAAQ;AAAA;AAE3C,UAAI,kBAAkB,SAAU,MAAM,SAAS;AAC7C,aAAK,UAAU,gBAAgB,QAAQ;AAAA;AAEzC,UAAI,gBAAgB,SAAU,MAAM,SAAS;AAC3C,aAAK,OAAO,oBAAoB,QAAQ;AAAA;AAE1C,UAAI,sBAAsB,SAAU,MAAM,SAAS;AACjD,aAAK,oBAAoB;AAAA;AAE3B,UAAI,sBAAsB,SAAU,SAAS;AAC3C,YAAI,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO;AACrD,YAAI,QAAQ,OAAO,yBAAyB;AAC1C,qBAAW,OAAO;AAAA;AAEpB,eAAO;AAAA;AAET,UAAI,+BAA+B,SAAU,MAAM,SAAS;AAC1D,aAAK,6BAA6B;AAAA;AAEpC,UAAI,+BAA+B,SAAU,SAAS;AACpD,YAAI,OAAO,OAAO,cAAc,OAAO,OAAO,aAAa;AACzD,cAAI,YAAY,CAAC,OAAO,OAAO,aAAa,OAAO,OAAO;AAC1D,cAAI,QAAQ,OAAO,yBAAyB;AAC1C,sBAAU,OAAO;AAAA;AAEnB,iBAAO;AAAA;AAGT,eAAO,QAAQ;AAAA;AAEjB,UAAI,iBAAiB,SAAU,MAAM;AACnC,aAAK,IAAI,OAAO;AAAA;AAElB,UAAI,WAAW,SAAU,MAAM,SAAS;AACtC,YAAI,OAAO,QAAQ,OAAO,KAAK,gBAAgB;AAC7C,eAAK,IAAI,OAAO,KAAK,iBAAiB,kBAAkB,YAAY,QAAQ;AAC5E;AAAA;AAEF,aAAK,QAAQ;AAAA;AAEf,UAAI,oBAAoB,SAAU,MAAM,SAAS;AAC/C,aAAK,kBAAkB;AAAA;AAEzB,UAAI,kBAAkB,SAAU,MAAM,SAAS;AAC7C,aAAK,gBAAgB;AAAA;AAEvB,UAAI,eAAe,SAAU,MAAM,SAAS;AAC1C,aAAK,aAAa;AAAA;AAEpB,UAAI,iBAAiB,SAAU,MAAM;AACnC,aAAK,CAAC,CAAC,OAAO,YAAY,UAAU;AAAA;AAEtC,UAAI,kBAAkB,SAAU,MAAM;AACpC,aAAK,CAAC,CAAC,OAAO;AAAA;AAEhB,UAAI,cAAc,SAAU,MAAM,SAAS;AACzC,aAAK,qBAAqB;AAAA;AAE5B,UAAI,cAAc,SAAU,MAAM,SAAS;AACzC,aAAK,qBAAqB;AAAA;AAE5B,UAAI,gBAAgB,SAAU,MAAM,SAAS;AAC3C,aAAK,cAAc;AAAA;AAErB,UAAI,YAAY,SAAU,MAAM,SAAS;AACvC,YAAI,qBAAqB;AACvB,eAAK,YAAY;AACjB;AAAA;AAEF,aAAK,QAAQ;AAAA;AAEf,UAAI,WAAW,SAAU,MAAM,SAAS;AACtC,YAAI,oBAAoB;AACtB,eAAK;AACL;AAAA;AAEF,aAAK,QAAQ;AAAA;AAEf,UAAI,4BAA4B,SAAU,MAAM;AAC9C,YAAI,oBAAoB;AACtB,eAAK;AACL;AAAA;AAEF;AAAA;AAEF,UAAI,aAAa,SAAU,MAAM;AAC/B,aAAK;AAAA;AAEP,UAAI,sBAAsB,SAAU,MAAM;AACxC,aAAK;AAAA;AAEP,UAAI,uBAAuB,SAAU,MAAM;AACzC,aAAK;AAAA;AAEP,UAAI,eAAe,SAAU,MAAM;AACjC,aAAK;AAAA;AAEP,UAAI,oBAAoB,SAAU,MAAM;AACtC,aAAK;AAAA;AAGP,UAAI,gBAAgB,SAAU,MAAM,SAAS;AAE3C,YAAI,CAAC,sBAAsB;AACzB,iBAAO,KAAK;AAAA;AAEd,YAAI,CAAC,wBAAwB;AAC3B,iBAAO,KAAK;AAAA;AAEd,YAAI,CAAC,QAAQ,MAAM,SAAS;AAC1B,iBAAO,KAAK;AAAA;AAEd,8BAAsB,SAAU,OAAO;AACrC,eAAK;AAAA,WACJ;AAAA;AAGL,UAAI,aAAa,SAAU,MAAM,SAAS;AAGxC,YAAI,YAAY,CAAC,aAAa,cAAc;AAE5C,YAAI,WAAW;AAAA,UACb;AAAA,UAAe;AAAA,UAAS;AAAA,UAAe;AAAA,UAAgB;AAAA,UAAY;AAAA,UAAgB;AAAA,UAAyB;AAAA,UAC5G;AAAA,UAA4B;AAAA,UAAgB;AAAA,UAC5C;AAAA,UAAW;AAAA,UAAW;AAAA,UAAgB;AAAA,UAAW;AAAA,UAAkB;AAAA,UAAsB;AAAA,UAAc;AAAA,UAAiB;AAAA,UAAY;AAAA,UAAW;AAAA,UAC/I;AAAA,UAAU;AAAA,UACV;AAAA,UAAa;AAAA,UACb;AAAA,UACA;AAAA,UAAiB;AAAA,UAAsB;AAAA,UAAkB;AAAA,UAAc;AAAA,UAAiB;AAAA,UAAsB;AAAA,UAAe;AAAA,UAA0B;AAAA,UACvJ;AAAA,UAAwB;AAAA,UAAU;AAAA,UAAoB;AAAA,UAAa;AAAA,UAAc;AAAA,UAAc;AAAA,UAA2B;AAAA,UAAiB;AAAA,UAAY;AAAA,UAAU;AAAA,UACjK;AAAA,UAAY;AAAA,UACZ;AAAA,UAAe;AAAA,UAAgB;AAAA,UAAY;AAAA,UAAkB;AAAA,UAAqB;AAAA,UAClF;AAAA,UAAU;AAAA,UAAS;AAAA,UAAmB;AAAA,UAAsB;AAAA,UAC5D;AAAA,UAAW;AAAA,UAAa;AAAA,UAAe;AAAA;AAGzC,YAAI,QAAQ,MAAM,iBAAiB;AACjC,cAAI,mBAAmB;AAAA,YACrB;AAAA,YAA4B;AAAA,YAAwB;AAAA,YAAoB;AAAA,YAAkB;AAAA,YAAsB;AAAA,YAAa;AAAA,YAAW;AAAA,YAAuB;AAAA,YAAmB;AAAA,YAAY;AAAA,YAAc;AAAA,YAC5M;AAAA,YAAiC;AAAA,YAAkB;AAAA,YAAW;AAAA,YAAe;AAAA,YAAc;AAAA,YAAiB;AAAA,YAAa;AAAA,YAAkB;AAAA,YAAqB;AAAA,YAAuB;AAAA,YAAsB;AAAA,YAC7M;AAAA,YAAY;AAAA,YAAY;AAAA,YAAgB;AAAA,YAAoB;AAAA,YAAoB;AAAA,YAAU;AAAA,YAAY;AAAA,YAAS;AAAA,YAAoB;AAAA,YAAe;AAAA,YAAoB;AAAA,YACtK;AAAA,YAAwB;AAAA,YAAU;AAAA,YAAa;AAAA,YAAgB;AAAA,YAAc;AAAA,YAAW;AAAA,YAAW;AAAA,YAAS;AAAA,YAAkB;AAAA,YAAkB;AAAA,YAAuB;AAAA,YAAwB;AAAA,YAAsB;AAAA,YAAkB;AAAA,YAAc;AAAA,YACrP;AAAA,YAAkB;AAAA,YAAiB;AAAA,YAAa;AAAA,YAAsB;AAAA,YAAuB;AAAA,YAAa;AAAA,YAAmB;AAAA,YAAuB;AAAA,YACpJ;AAAA,YAAsB;AAAA,YAAW;AAAA,YAAgB;AAAA,YAAoB;AAAA,YAAgB;AAAA,YAAkB;AAAA,YAAY;AAAA,YAAiB;AAAA,YAAgB;AAAA,YAAmB;AAAA,YAAkB;AAAA,YAAc;AAAA,YAAgB;AAAA,YACvN;AAAA,YAAoB;AAAA,YAAa;AAAA,YAAW;AAAA,YAAW;AAAA,YAAY;AAAA,YAAY;AAAA,YAAc;AAAA,YAAiB;AAAA,YAAe;AAAA,YAAgB;AAAA,YAAiB;AAAA,YAAc;AAAA,YAC5K;AAAA,YAAoB;AAAA,YAAW;AAAA,YAAa;AAAA,YAAuB;AAAA,YAAoB;AAAA,YAAU;AAAA,YAAc;AAAA,YAAc;AAAA,YAAgB;AAAA,YAAe;AAAA,YAAsB;AAAA,YAClL;AAAA,YAA4B;AAAA,YAAsB;AAAA,YAAU;AAAA,YAAc;AAAA,YAAa;AAAA,YAAe;AAAA,YAAW;AAAA,YAAU;AAAA,YAAY;AAAA,YAAY;AAAA,YAAW;AAAA,YAAS;AAAA,YAAe;AAAA,YAAa;AAAA,YACnM;AAAA,YAAY;AAAA,YAAS;AAAA,YAAe;AAAA,YAAO;AAAA,YAAa;AAAA,YAAS;AAAA,YAAY;AAAA,YAAU;AAAA,YAAwB;AAAA,YAAY;AAAA,YAAyB;AAAA,YAAgB;AAAA,YAAsB;AAAA,YAAiB;AAAA,YAAiB;AAAA,YAAkB;AAAA,YAC9O;AAAA,YAAe;AAAA,YAAY;AAAA,YAAiB;AAAA,YAAa;AAAA,YAAkB;AAAA,YAAY;AAAA,YAAiB;AAAA,YAAY;AAAA,YAAU;AAAA,YAAsB;AAAA,YACpJ;AAAA,YAAc;AAAA,YAAc;AAAA,YAAqB;AAAA,YAAc;AAAA,YAAoB;AAAA,YAAoB;AAAA,YAAqB;AAAA,YAAY;AAAA,YACxI;AAAA,YAAU;AAAA,YAAgB;AAAA,YAAgB;AAAA,YAAgB;AAAA,YAAkB;AAAA,YAAkB;AAAA,YAAY;AAAA,YAAe;AAAA,YAAW;AAAA,YAAa;AAAA,YAAiB;AAAA,YAAoB;AAAA,YAAoB;AAAA,YAC1M;AAAA,YAAsB;AAAA,YAAQ;AAAA,YAAa;AAAA,YAAgB;AAAA,YAA0B;AAAA,YAAmC;AAAA,YAAwB;AAAA,YAAkC;AAAA,YAAS;AAAA,YAAiC;AAAA,YAAU;AAAA,YACtO;AAAA,YAAmB;AAAA,YAAe;AAAA,YAAsB;AAAA,YAAgB;AAAA,YAAsB;AAAA,YAAS;AAAA,YAAY;AAAA,YAAW;AAAA,YAAc;AAAA,YAAe;AAAA,YAAoB;AAAA,YAAuB;AAAA,YAAc;AAAA,YAAW;AAAA,YAAY;AAAA,YAAY;AAAA,YACvP;AAAA,YAAU;AAAA,YAAmB;AAAA,YAA6B;AAAA,YAAwB;AAAA,YAAgB;AAAA,YAAqB;AAAA,YAAiB;AAAA,YAAoB;AAAA,YAAqB;AAAA,YAAoB;AAAA,YACrM;AAAA,YAAoB;AAAA,YAAe;AAAA,YAAkB;AAAA,YAAkB;AAAA,YAAc;AAAA,YAAW;AAAA,YAAgB;AAAA,YAAc;AAAA,YAAY;AAAA,YAAU;AAAA,YAAU;AAAA,YAAY;AAAA,YAAa;AAAA,YAAe;AAAA,YAAgB;AAAA,YAAW;AAAA,YAAS;AAAA,YAAW;AAAA,YACrP;AAAA,YAAW;AAAA,YAAkB;AAAA,YAAe;AAAA,YAAY;AAAA,YAAgB;AAAA,YAAU;AAAA,YAAc;AAAA,YAAe;AAAA,YAAa;AAAA,YAAmB;AAAA,YAAU;AAAA,YAAS;AAAA,YAAc;AAAA,YAAiB;AAAA,YAAc;AAAA,YAAW;AAAA,YAAc;AAAA,YAAoB;AAAA,YAC5P;AAAA,YAAa;AAAA,YAAW;AAAA,YAAe;AAAA,YAAuB;AAAA,YAC9D;AAAA,YAAU;AAAA,YAAY;AAAA,YAAU;AAAA,YAAe;AAAA,YAAU;AAAA,YAAW;AAAA,YAAe;AAAA,YAA6B;AAAA,YAAU;AAAA,YAAa;AAAA,YAAsB;AAAA,YAAsB;AAAA,YAAyB;AAAA,YAAqB;AAAA,YACjO;AAAA,YAAoB;AAAA,YAAmB;AAAA,YAAsB;AAAA,YAAW;AAAA,YAAiB;AAAA,YAAsB;AAAA,YAAgB;AAAA,YAAU;AAAA,YAAc;AAAA,YAAU;AAAA,YAAgB;AAAA,YAAW;AAAA,YAAU;AAAA,YAAiB;AAAA,YAA0B;AAAA,YACjP;AAAA,YAAQ;AAAA,YAAa;AAAA,YAAa;AAAA,YAAe;AAAA,YAAa;AAAA,YAAc;AAAA,YAA0B;AAAA,YAAgB;AAAA,YAAY;AAAA,YAAS;AAAA,YAC3I;AAAA,YAAU;AAAA,YAAY;AAAA,YAAS;AAAA,YAAe;AAAA,YAAiB;AAAA,YAAe;AAAA,YAAoB;AAAA,YAAiB;AAAA,YAAc;AAAA,YAAW;AAAA,YAAS;AAAA,YAAkB;AAAA,YAAe;AAAA,YAAuB;AAAA,YAAQ;AAAA,YAAW;AAAA,YAAU;AAAA,YAC1O;AAAA,YAAS;AAAA,YAAmB;AAAA,YAAoB;AAAA,YAAW;AAAA,YAAa;AAAA,YAAa;AAAA,YAAW;AAAA,YAAY;AAAA,YAAuB;AAAA,YAAc;AAAA,YAAY;AAAA,YAAwB;AAAA,YAAY;AAAA,YAAY;AAAA,YAC7M;AAAA,YAAgB;AAAA,YAAU;AAAA,YAAmB;AAAA,YAAkB;AAAA,YAAY;AAAA,YAAe;AAAA,YAAc;AAAA,YAAS;AAAA,YAAe;AAAA,YAAS;AAAA,YAAmB;AAAA,YAAY;AAAA,YAAsB;AAAA,YAAuB;AAAA,YAAO;AAAA,YAAS;AAAA,YACrO;AAAA,YAAgB;AAAA,YAAc;AAAA,YAAW;AAAA,YAAU;AAAA,YAAkB;AAAA,YAAa;AAAA,YAAU;AAAA,YAAa;AAAA,YAAgB;AAAA,YAAqB;AAAA,YAC9I;AAAA,YAAiB;AAAA,YAAmB;AAAA,YAAU;AAAA,YAAa;AAAA,YAAc;AAAA,YAAU;AAAA,YAAqB;AAAA,YAA2B;AAAA,YAAU;AAAA,YAAe;AAAA,YAAqB;AAAA,YAAmB;AAAA,YAAQ;AAAA,YAAe;AAAA,YAAY;AAAA,YAAmB;AAAA,YAC1P;AAAA,YAAkB;AAAA,YAAkB;AAAA,YAAW;AAAA,YAAW;AAAA,YAAa;AAAA,YAAU;AAAA,YAAU;AAAA,YAAoB;AAAA,YAAmB;AAAA,YAAW;AAAA,YAAe;AAAA,YAAU;AAAA,YAAmB;AAAA,YAAa;AAAA,YAAY;AAAA,YAAoB;AAAA,YACtO;AAAA,YAAY;AAAA,YAAY;AAAA,YAAsB;AAAA,YAAU;AAAA,YAAc;AAAA,YAAW;AAAA,YAAW;AAAA,YAAS;AAAA,YAAa;AAAA,YAAuB;AAAA,YACzI;AAAA,YAAkB;AAAA,YAAW;AAAA,YAAW;AAAA,YAAwB;AAAA,YAAqB;AAAA,YAAU;AAAA,YAAY;AAAA,YAAQ;AAAA,YAAU;AAAA,YAAkB;AAAA,YAAY;AAAA,YAAW;AAAA,YAAmB;AAAA,YAAU;AAAA,YAAe;AAAA,YAAW;AAAA,YAC7N;AAAA,YAAiB;AAAA,YAAiB;AAAA,YAAoB;AAAA,YAAW;AAAA,YAAmB;AAAA,YAAgB;AAAA;AACtG,qBAAW,SAAS,OAAO;AAAA;AAG7B,mBAAW,SAAS,OAAO,QAAQ,MAAM;AAGzC,mBAAW,SAAS,OAAO,SAAU,MAAM,UAAU;AACnD,iBAAO,SAAS,QAAQ,UAAU;AAAA;AAKpC,YAAI,aAAa;AAGjB,YAAI,WAAW;AAEf,YAAI,IAAI,SAAS,qBAAqB,QAAQ;AAG9C,YAAI,eAAe,SAAS,cAAc;AAG1C,YAAI,WAAW,SAAS,cAAc;AAEtC,YAAI,eAAe;AACnB,YAAI,gBAAgB;AAGpB,YAAI,aAAa,WAAY;AAC3B,cAAI,IAAI,SAAS,cAAc;AAM/B,YAAE,MAAM,WAAW;AACnB,YAAE,MAAM,OAAO;AACf,YAAE,MAAM,WAAW;AAGnB,YAAE,MAAM,YAAY;AACpB,YAAE,MAAM,aAAa;AACrB,YAAE,MAAM,gBAAgB;AACxB,YAAE,MAAM,YAAY;AACpB,YAAE,MAAM,aAAa;AACrB,YAAE,MAAM,gBAAgB;AACxB,YAAE,MAAM,YAAY;AACpB,YAAE,MAAM,iBAAiB;AACzB,YAAE,MAAM,aAAa;AACrB,YAAE,MAAM,aAAa;AACrB,YAAE,MAAM,YAAY;AACpB,YAAE,MAAM,cAAc;AAEtB,YAAE,YAAY;AACd,iBAAO;AAAA;AAIT,YAAI,sBAAsB,SAAU,cAAc,UAAU;AAC1D,cAAI,IAAI;AACR,YAAE,MAAM,aAAa,MAAM,eAAe,OAAO;AACjD,iBAAO;AAAA;AAIT,YAAI,2BAA2B,WAAY;AACzC,cAAI,QAAQ;AACZ,mBAAS,SAAQ,GAAG,UAAS,UAAU,QAAQ,SAAQ,SAAQ,UAAS;AACtE,gBAAI,IAAI;AACR,cAAE,MAAM,aAAa,UAAU;AAC/B,yBAAa,YAAY;AACzB,kBAAM,KAAK;AAAA;AAEb,iBAAO;AAAA;AAIT,YAAI,uBAAuB,WAAY;AACrC,cAAI,QAAQ;AACZ,mBAAS,KAAI,GAAG,KAAI,SAAS,QAAQ,KAAI,IAAG,MAAK;AAC/C,gBAAI,YAAY;AAChB,qBAAS,IAAI,GAAG,kBAAkB,UAAU,QAAQ,IAAI,iBAAiB,KAAK;AAC5E,kBAAI,IAAI,oBAAoB,SAAS,KAAI,UAAU;AACnD,uBAAS,YAAY;AACrB,wBAAU,KAAK;AAAA;AAEjB,kBAAM,SAAS,OAAM;AAAA;AAEvB,iBAAO;AAAA;AAIT,YAAI,kBAAkB,SAAU,WAAW;AACzC,cAAI,WAAW;AACf,mBAAS,KAAI,GAAG,KAAI,UAAU,QAAQ,MAAK;AACzC,uBAAY,UAAU,IAAG,gBAAgB,aAAa,UAAU,QAAO,UAAU,IAAG,iBAAiB,cAAc,UAAU;AAC7H,gBAAI,UAAU;AACZ,qBAAO;AAAA;AAAA;AAGX,iBAAO;AAAA;AAIT,YAAI,iBAAiB;AAGrB,UAAE,YAAY;AAGd,iBAAS,QAAQ,GAAG,SAAS,UAAU,QAAQ,QAAQ,QAAQ,SAAS;AACtE,uBAAa,UAAU,UAAU,eAAe,OAAO;AACvD,wBAAc,UAAU,UAAU,eAAe,OAAO;AAAA;AAI1D,YAAI,aAAa;AAGjB,UAAE,YAAY;AAGd,YAAI,YAAY;AAChB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,cAAI,gBAAgB,WAAW,SAAS,MAAM;AAC5C,sBAAU,KAAK,SAAS;AAAA;AAAA;AAK5B,UAAE,YAAY;AACd,UAAE,YAAY;AACd,aAAK;AAAA;AAEP,UAAI,mBAAmB,SAAU,MAAM,SAAS;AAC9C,YAAI,QAAQ;AACV,cAAI,CAAC,QAAQ,QAAQ,WAAW;AAC9B,iBAAK,aAAa;AAAA,iBACb;AACL,iBAAK,QAAQ;AAAA;AAAA,eAEV;AACL,eAAK,kBAAkB;AAAA;AAAA;AAG3B,UAAI,oBAAoB,SAAU,SAAS;AACzC,YAAI,UAAU,WAAW,MAAM;AAC7B,iBAAO,QAAQ;AAAA;AAGjB,YAAI,UAAU;AAEd,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,QAAQ,IAAI,GAAG,KAAK;AACxD,cAAI,UAAU,QAAQ,IAAI;AAAE,oBAAQ,KAAK,UAAU,QAAQ;AAAA;AAAA;AAK7D,YAAI,sBAAsB,UAAU;AAClC,oBAAU,QAAQ,KAAK,SAAU,GAAG,GAAG;AACrC,gBAAI,EAAE,OAAO,EAAE,MAAM;AAAE,qBAAO;AAAA;AAC9B,gBAAI,EAAE,OAAO,EAAE,MAAM;AAAE,qBAAO;AAAA;AAC9B,mBAAO;AAAA;AAAA;AAGX,eAAO,IAAI,SAAS,SAAU,GAAG;AAC/B,cAAI,YAAY,IAAI,GAAG,SAAU,IAAI;AACnC,mBAAO,CAAC,GAAG,MAAM,GAAG;AAAA;AAEtB,iBAAO,CAAC,EAAE,MAAM,EAAE,aAAa;AAAA;AAAA;AAGnC,UAAI,eAAe,SAAU,SAAS;AACpC,YAAI,SAAS;AACb,YAAK,OAAO,4BAA4B,OAAO,yBAAyB,QAAQ,oBAAsB,mBAAmB,QAAS;AAChI,cAAI,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAGF,mBAAS,IAAI,OAAO,SAAU,MAAM;AAClC,gBAAI;AAEF,kBAAI,OAAO,cAAc;AACzB,qBAAO;AAAA,qBACA,GAAP;AACA,qBAAO,QAAQ;AAAA;AAAA;AAAA,eAGd;AACL,iBAAO,KAAK,QAAQ;AAAA;AAEtB,YAAI,UAAU,SAAS;AACrB,mBAAS,OAAO,OAAO,kBAAkB;AAAA;AAE3C,eAAO;AAAA;AAET,UAAI,wBAAwB,SAAU,SAAS;AAC7C,YAAI,SAAS;AACb,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,eAAe,QAAQ,IAAI,GAAG,KAAK;AACrE,cAAI,KAAK,QAAQ,QAAQ,eAAe;AACxC,cAAI,UAAU,UAAU,MAAM,KAAK;AACjC,qBAAS;AACT;AAAA;AAAA;AAGJ,eAAO;AAAA;AAET,UAAI,kBAAkB,SAAU,MAAM;AACpC,aAAK;AAAA;AAEP,UAAI,yBAAyB,SAAU,MAAM,SAAS;AACpD,aAAK,uBAAuB;AAAA;AAE9B,UAAI,oBAAoB,SAAU,SAAS;AACzC,YAAI;AACF,iBAAO,CAAC,CAAC,OAAO;AAAA,iBACT,GAAP;AACA,iBAAO,QAAQ;AAAA;AAAA;AAKnB,UAAI,kBAAkB,SAAU,SAAS;AACvC,YAAI;AACF,iBAAO,CAAC,CAAC,OAAO;AAAA,iBACT,GAAP;AACA,iBAAO,QAAQ;AAAA;AAAA;AAGnB,UAAI,eAAe,SAAU,SAAS;AAGpC,YAAI,iBAAiB;AACnB,iBAAO,QAAQ;AAAA;AAEjB,YAAI;AACF,iBAAO,CAAC,CAAC,OAAO;AAAA,iBACT,GAAP;AACA,iBAAO,QAAQ;AAAA;AAAA;AAGnB,UAAI,yBAAyB,SAAU,SAAS;AAC9C,YAAI,UAAU,qBAAqB;AACjC,iBAAO,UAAU;AAAA;AAEnB,eAAO,QAAQ;AAAA;AAEjB,UAAI,uBAAuB,SAAU,SAAS;AAC5C,eAAO,UAAU,YAAY,QAAQ;AAAA;AAEvC,UAAI,uBAAuB,SAAU,SAAS;AAC5C,YAAI,UAAU,UAAU;AACtB,iBAAO,UAAU;AAAA,eACZ;AACL,iBAAO,QAAQ;AAAA;AAAA;AAGnB,UAAI,gBAAgB,SAAU,SAAS;AACrC,YAAI,UAAU,YAAY;AACxB,iBAAO,UAAU;AAAA,mBACR,UAAU,cAAc;AACjC,iBAAO,UAAU;AAAA,mBACR,OAAO,YAAY;AAC5B,iBAAO,OAAO;AAAA,eACT;AACL,iBAAO,QAAQ;AAAA;AAAA;AAYnB,UAAI,kBAAkB,WAAY;AAChC,YAAI,iBAAiB;AACrB,YAAI;AACJ,YAAI,OAAO,UAAU,mBAAmB,aAAa;AACnD,2BAAiB,UAAU;AAAA,mBAClB,OAAO,UAAU,qBAAqB,aAAa;AAC5D,2BAAiB,UAAU;AAAA;AAE7B,YAAI;AACF,mBAAS,YAAY;AACrB,uBAAa;AAAA,iBACN,GAAP;AACA,uBAAa;AAAA;AAEf,YAAI,aAAa,kBAAkB;AACnC,eAAO,CAAC,gBAAgB,YAAY;AAAA;AAItC,UAAI,cAAc,SAAU,SAAS;AACnC,YAAI,SAAS;AAEb,YAAI,SAAS,SAAS,cAAc;AACpC,eAAO,QAAQ;AACf,eAAO,SAAS;AAChB,eAAO,MAAM,UAAU;AACvB,YAAI,MAAM,OAAO,WAAW;AAI5B,YAAI,KAAK,GAAG,GAAG,IAAI;AACnB,YAAI,KAAK,GAAG,GAAG,GAAG;AAClB,eAAO,KAAK,oBAAsB,KAAI,cAAc,GAAG,GAAG,eAAe,QAAS,QAAQ;AAE1F,YAAI,eAAe;AACnB,YAAI,YAAY;AAChB,YAAI,SAAS,KAAK,GAAG,IAAI;AACzB,YAAI,YAAY;AAEhB,YAAI,QAAQ,yBAAyB;AACnC,cAAI,OAAO;AAAA,eACN;AACL,cAAI,OAAO;AAAA;AAEb,YAAI,SAAS,6CAAgD,GAAG;AAChE,YAAI,YAAY;AAChB,YAAI,OAAO;AACX,YAAI,SAAS,6CAAgD,GAAG;AAKhE,YAAI,2BAA2B;AAC/B,YAAI,YAAY;AAChB,YAAI;AACJ,YAAI,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,GAAG;AACpC,YAAI;AACJ,YAAI;AACJ,YAAI,YAAY;AAChB,YAAI;AACJ,YAAI,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,KAAK,GAAG;AACrC,YAAI;AACJ,YAAI;AACJ,YAAI,YAAY;AAChB,YAAI;AACJ,YAAI,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;AACrC,YAAI;AACJ,YAAI;AACJ,YAAI,YAAY;AAIhB,YAAI,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,GAAG;AACpC,YAAI,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,GAAG;AACpC,YAAI,KAAK;AAET,YAAI,OAAO,WAAW;AAAE,iBAAO,KAAK,eAAe,OAAO;AAAA;AAC1D,eAAO;AAAA;AAET,UAAI,aAAa,WAAY;AAC3B,YAAI;AACJ,YAAI,OAAO,SAAU,IAAI;AACvB,aAAG,WAAW,GAAK,GAAK,GAAK;AAC7B,aAAG,OAAO,GAAG;AACb,aAAG,UAAU,GAAG;AAChB,aAAG,MAAM,GAAG,mBAAmB,GAAG;AAClC,iBAAO,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK;AAAA;AAEtC,YAAI,gBAAgB,SAAU,KAAI;AAChC,cAAI,MAAM,IAAG,aAAa,qCAAqC,IAAG,aAAa,4CAA4C,IAAG,aAAa;AAC3I,cAAI,KAAK;AACP,gBAAI,aAAa,IAAG,aAAa,IAAI;AACrC,gBAAI,eAAe,GAAG;AACpB,2BAAa;AAAA;AAEf,mBAAO;AAAA,iBACF;AACL,mBAAO;AAAA;AAAA;AAIX,aAAK;AACL,YAAI,CAAC,IAAI;AAAE,iBAAO;AAAA;AAKlB,YAAI,SAAS;AACb,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB,GAAG;AACzB,WAAG,WAAW,GAAG,cAAc;AAC/B,YAAI,WAAW,IAAI,aAAa,CAAC,MAAM,MAAM,GAAG,KAAK,OAAO,GAAG,GAAG,aAAa;AAC/E,WAAG,WAAW,GAAG,cAAc,UAAU,GAAG;AAC5C,wBAAgB,WAAW;AAC3B,wBAAgB,WAAW;AAC3B,YAAI,UAAU,GAAG;AACjB,YAAI,UAAU,GAAG,aAAa,GAAG;AACjC,WAAG,aAAa,SAAS;AACzB,WAAG,cAAc;AACjB,YAAI,UAAU,GAAG,aAAa,GAAG;AACjC,WAAG,aAAa,SAAS;AACzB,WAAG,cAAc;AACjB,WAAG,aAAa,SAAS;AACzB,WAAG,aAAa,SAAS;AACzB,WAAG,YAAY;AACf,WAAG,WAAW;AACd,gBAAQ,kBAAkB,GAAG,kBAAkB,SAAS;AACxD,gBAAQ,gBAAgB,GAAG,mBAAmB,SAAS;AACvD,WAAG,wBAAwB,QAAQ;AACnC,WAAG,oBAAoB,QAAQ,iBAAiB,gBAAgB,UAAU,GAAG,OAAO,OAAI,GAAG;AAC3F,WAAG,UAAU,QAAQ,eAAe,GAAG;AACvC,WAAG,WAAW,GAAG,gBAAgB,GAAG,gBAAgB;AACpD,YAAI;AACF,iBAAO,KAAK,GAAG,OAAO;AAAA,iBACf,GAAP;AAAA;AAGF,eAAO,KAAK,gBAAiB,IAAG,4BAA4B,IAAI,KAAK;AACrE,eAAO,KAAK,oCAAoC,KAAK,GAAG,aAAa,GAAG;AACxE,eAAO,KAAK,oCAAoC,KAAK,GAAG,aAAa,GAAG;AACxE,eAAO,KAAK,sBAAsB,GAAG,aAAa,GAAG;AACrD,eAAO,KAAK,wBAAyB,IAAG,uBAAuB,YAAY,QAAQ;AACnF,eAAO,KAAK,qBAAqB,GAAG,aAAa,GAAG;AACpD,eAAO,KAAK,sBAAsB,GAAG,aAAa,GAAG;AACrD,eAAO,KAAK,sBAAsB,GAAG,aAAa,GAAG;AACrD,eAAO,KAAK,0BAA0B,cAAc;AACpD,eAAO,KAAK,4CAA4C,GAAG,aAAa,GAAG;AAC3E,eAAO,KAAK,qCAAqC,GAAG,aAAa,GAAG;AACpE,eAAO,KAAK,wCAAwC,GAAG,aAAa,GAAG;AACvE,eAAO,KAAK,kCAAkC,GAAG,aAAa,GAAG;AACjE,eAAO,KAAK,mCAAmC,GAAG,aAAa,GAAG;AAClE,eAAO,KAAK,4BAA4B,GAAG,aAAa,GAAG;AAC3D,eAAO,KAAK,+BAA+B,GAAG,aAAa,GAAG;AAC9D,eAAO,KAAK,8BAA8B,GAAG,aAAa,GAAG;AAC7D,eAAO,KAAK,0CAA0C,GAAG,aAAa,GAAG;AACzE,eAAO,KAAK,sCAAsC,GAAG,aAAa,GAAG;AACrE,eAAO,KAAK,6BAA6B,KAAK,GAAG,aAAa,GAAG;AACjE,eAAO,KAAK,oBAAoB,GAAG,aAAa,GAAG;AACnD,eAAO,KAAK,oBAAoB,GAAG,aAAa,GAAG;AACnD,eAAO,KAAK,oCAAoC,GAAG,aAAa,GAAG;AACnE,eAAO,KAAK,wBAAwB,GAAG,aAAa,GAAG;AACvD,eAAO,KAAK,kBAAkB,GAAG,aAAa,GAAG;AACjD,eAAO,KAAK,mBAAmB,GAAG,aAAa,GAAG;AAElD,YAAI;AAEF,cAAI,6BAA6B,GAAG,aAAa;AACjD,cAAI,4BAA4B;AAC9B,mBAAO,KAAK,2BAA2B,GAAG,aAAa,2BAA2B;AAClF,mBAAO,KAAK,6BAA6B,GAAG,aAAa,2BAA2B;AAAA;AAAA,iBAE/E,GAAP;AAAA;AAEF,YAAI,CAAC,GAAG,0BAA0B;AAChC,2BAAiB;AACjB,iBAAO;AAAA;AAGT,aAAK,CAAC,SAAS,QAAQ,SAAU,SAAS;AACxC,eAAK,CAAC,UAAU,aAAa,SAAU,QAAQ;AAC7C,iBAAK,CAAC,QAAQ,UAAU,QAAQ,SAAU,SAAS;AACjD,mBAAK,CAAC,aAAa,YAAY,aAAa,SAAU,KAAK;AACzD,oBAAI,SAAS,GAAG,yBAAyB,GAAG,SAAS,YAAY,GAAG,UAAU,MAAM,UAAU;AAC9F,oBAAI,QAAQ,aAAa;AACvB,wBAAM,eAAe;AAAA;AAEvB,oBAAI,OAAO,CAAC,UAAU,OAAO,eAAe,YAAY,QAAQ,eAAe,KAAK,QAAQ,eAAe,KAAK,KAAK,KAAK,QAAQ,KAAK;AACvI,uBAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAKpB,yBAAiB;AACjB,eAAO;AAAA;AAET,UAAI,4BAA4B,WAAY;AAE1C,YAAI;AACF,cAAI,YAAY;AAChB,cAAI,6BAA6B,UAAU,aAAa;AACxD,cAAI,SAAS,UAAU,aAAa,2BAA2B,yBAAyB,MAAM,UAAU,aAAa,2BAA2B;AAChJ,2BAAiB;AACjB,iBAAO;AAAA,iBACA,GAAP;AACA,iBAAO;AAAA;AAAA;AAGX,UAAI,aAAa,WAAY;AAC3B,YAAI,MAAM,SAAS,cAAc;AACjC,YAAI,YAAY;AAChB,YAAI,YAAY;AAChB,YAAI,SAAS;AACb,YAAI;AAEF,mBAAS,KAAK,YAAY;AAC1B,mBAAS,SAAS,uBAAuB,UAAU,GAAG,iBAAiB;AACvE,mBAAS,KAAK,YAAY;AAAA,iBACnB,GAAP;AACA,mBAAS;AAAA;AAEX,eAAO;AAAA;AAET,UAAI,sBAAsB,WAAY;AAGpC,YAAI,OAAO,UAAU,cAAc,aAAa;AAC9C,cAAI;AACF,gBAAI,iBAAiB,UAAU,UAAU,GAAG,OAAO,GAAG;AACtD,gBAAI,mBAAmB,UAAU,SAAS,OAAO,GAAG,IAAI;AACtD,qBAAO;AAAA;AAAA,mBAEF,KAAP;AACA,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,uBAAuB,WAAY;AACrC,eAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,cAAc,OAAO,OAAO,SAAS,OAAO,OAAO;AAAA;AAEhG,UAAI,eAAe,WAAY;AAC7B,YAAI,YAAY,UAAU,UAAU;AACpC,YAAI,QAAQ,UAAU;AACtB,YAAI,WAAW,UAAU,SAAS;AAClC,YAAI;AAEJ,YAAI,UAAU,QAAQ,oBAAoB,GAAG;AAC3C,eAAK;AAAA,mBACI,UAAU,QAAQ,cAAc,KAAK,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,YAAY,GAAG;AACjS,eAAK;AAAA,mBACI,UAAU,QAAQ,cAAc,GAAG;AAC5C,eAAK;AAAA,mBACI,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,UAAU,GAAG;AAC7G,eAAK;AAAA,mBACI,UAAU,QAAQ,aAAa,KAAK,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,YAAY,GAAG;AACrL,eAAK;AAAA,mBACI,UAAU,QAAQ,gBAAgB,KAAK,UAAU,QAAQ,mBAAmB,GAAG;AACxF,eAAK;AAAA,eACA;AACL,eAAK;AAAA;AAGP,YAAI,eAAiB,kBAAkB,UACpC,UAAU,iBAAiB,KAC3B,UAAU,mBAAmB;AAEhC,YAAI,gBAAgB,OAAO,aAAa,OAAO,mBAAmB,OAAO,aAAa,OAAO,SAAS,OAAO,WAAW,UAAU,QAAQ,YAAY,IAAI;AACxJ,iBAAO;AAAA;AAIT,YAAI,OAAO,UAAU,aAAa;AAChC,kBAAQ,MAAM;AACd,cAAI,MAAM,QAAQ,UAAU,KAAK,OAAO,aAAa,OAAO,iBAAiB;AAC3E,mBAAO;AAAA,qBACE,MAAM,QAAQ,YAAY,KAAK,OAAO,WAAW,OAAO,WAAW;AAC5E,mBAAO;AAAA,qBACE,MAAM,QAAQ,UAAU,KAAK,OAAO,SAAS,OAAO,OAAO;AACpE,mBAAO;AAAA,qBACG,OAAM,QAAQ,WAAW,MAAM,MAAM,QAAQ,aAAa,MAAM,MAAM,QAAQ,WAAW,QAAS,QAAO,UAAU;AAC7H,mBAAO;AAAA;AAAA;AAKX,YAAI,SAAS,QAAQ,UAAU,KAAK,OAAO,aAAa,OAAO,iBAAiB;AAC9E,iBAAO;AAAA,mBACG,UAAS,QAAQ,YAAY,KAAK,SAAS,QAAQ,cAAc,KAAK,SAAS,QAAQ,WAAW,MAAM,OAAO,WAAW,OAAO,WAAW;AACtJ,iBAAO;AAAA,mBACG,UAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,WAAW,KAAK,SAAS,QAAQ,WAAW,KAAK,SAAS,QAAQ,aAAa,MAAM,OAAO,SAAS,OAAO,OAAO;AAC9K,iBAAO;AAAA,mBACE,SAAS,QAAQ,UAAU,KAAK,OAAO,iBAAiB;AACjE,iBAAO;AAAA,mBACE,SAAS,QAAQ,WAAW,KAAK,UAAU,QAAQ,iBAAiB,GAAG;AAChF,iBAAO;AAAA,eACF;AACL,cAAI,kBAAkB,SAAS,QAAQ,SAAS,KAC9C,SAAS,QAAQ,WAAW,KAC5B,SAAS,QAAQ,SAAS,KAC1B,SAAS,QAAQ,YAAY,KAC7B,SAAS,QAAQ,UAAU,KAC3B,SAAS,QAAQ,UAAU;AAC7B,cAAI,oBAAqB,QAAO,UAAU;AACxC,mBAAO;AAAA;AAAA;AAIX,eAAO,OAAO,UAAU,YAAY,eAAe,OAAO,aAAa,OAAO;AAAA;AAEhF,UAAI,oBAAoB,WAAY;AAClC,YAAI,YAAY,UAAU,UAAU;AACpC,YAAI,aAAa,UAAU;AAG3B,YAAI;AACJ,YAAI,UAAU,QAAQ,YAAY,KAAK,UAAU,QAAQ,gBAAgB,GAAG;AAE1E,iBAAO;AAAA,mBACE,UAAU,QAAQ,iBAAiB,GAAG;AAE/C,iBAAO;AAAA,mBACE,UAAU,QAAQ,eAAe,GAAG;AAC7C,oBAAU;AAAA,mBACD,UAAU,QAAQ,aAAa,KAAK,UAAU,QAAQ,YAAY,GAAG;AAC9E,oBAAU;AAAA,mBACD,UAAU,QAAQ,cAAc,GAAG;AAC5C,oBAAU;AAAA,mBACD,UAAU,QAAQ,cAAc,GAAG;AAC5C,cAAI,UAAU,QAAQ,iBAAiB,KAAK,UAAU,QAAQ,iBAAiB,KAAK,UAAU,QAAQ,iBAAiB,KAAK,UAAU,QAAQ,iBAAiB,GAAG;AAChK,sBAAU;AAAA,iBACL;AACL,sBAAU;AAAA;AAAA,mBAEH,UAAU,QAAQ,eAAe,GAAG;AAC7C,oBAAU;AAAA,eACL;AACL,oBAAU;AAAA;AAGZ,YAAK,aAAY,YAAY,YAAY,YAAY,YAAY,YAAY,eAAe,YAAY;AACtG,iBAAO;AAAA;AAIT,YAAI,UAAU,KAAK,WAAW;AAC9B,YAAI,YAAY,MAAM,YAAY,YAAY,YAAY,aAAa,YAAY,SAAS;AAC1F,iBAAO;AAAA,mBACE,YAAY,MAAM,YAAY,uBAAuB,YAAY,SAAS;AACnF,iBAAO;AAAA,mBACE,YAAY,MAAM,YAAY,YAAY,YAAY,UAAU,YAAY,WAAW,YAAY,SAAS;AACrH,iBAAO;AAAA;AAIT,YAAI;AACJ,YAAI;AAEF,gBAAM;AAAA,iBACC,KAAP;AACA,cAAI;AACF,gBAAI;AACJ,yBAAa;AAAA,mBACN,UAAP;AACA,yBAAa;AAAA;AAAA;AAGjB,eAAO,cAAc,YAAY,aAAa,YAAY;AAAA;AAE5D,UAAI,oBAAoB,WAAY;AAClC,YAAI,OAAO,SAAS,cAAc;AAClC,eAAO,CAAC,CAAE,MAAK,cAAc,KAAK,WAAW;AAAA;AAE/C,UAAI,mBAAmB,WAAY;AAEjC,YAAI,CAAC,qBAAqB;AACxB,iBAAO;AAAA;AAGT,YAAI,YAAY;AAChB,YAAI,cAAc,CAAC,CAAC,OAAO,yBAAyB,CAAC,CAAC;AACtD,yBAAiB;AACjB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,UAAU,YAAY,+BAA+B;AACvD,iBAAO;AAAA,mBACE,UAAU,YAAY,cAAc,UAAU,KAAK,UAAU,YAAY;AAClF,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,gBAAgB,WAAY;AAE9B,eAAQ,0BAAyB,UAAW,kBAAiB,aAAc,iBAAgB,cAAc;AAAA;AAE3G,UAAI,qBAAqB,WAAY;AACnC,eAAO,OAAO,OAAO,cAAc;AAAA;AAErC,UAAI,uBAAuB,WAAY;AACrC,eAAO,OAAO,UAAU,sBAAsB;AAAA;AAEhD,UAAI,kBAAkB,SAAU,SAAS;AACvC,YAAI,OAAO,SAAS,cAAc;AAClC,aAAK,aAAa,MAAM,QAAQ,MAAM;AACtC,iBAAS,KAAK,YAAY;AAAA;AAE5B,UAAI,wBAAwB,SAAU,MAAM,SAAS;AACnD,YAAI,iBAAiB;AACrB,eAAO,kBAAkB,SAAU,OAAO;AACxC,eAAK;AAAA;AAEP,YAAI,KAAK,QAAQ,MAAM;AACvB;AACA,YAAI,YAAY,EAAE,SAAS;AAC3B,YAAI,cAAc,EAAE,mBAAmB,UAAU,MAAM;AACvD,eAAO,UAAU,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK,KAAK,SAAS,OAAO,WAAW,aAAa;AAAA;AAEzG,UAAI,iBAAiB,WAAY;AAC/B,YAAI,SAAS,SAAS,cAAc;AACpC,YAAI,KAAK;AACT,YAAI;AACF,eAAK,OAAO,WAAW,YAAY,OAAO,WAAW;AAAA,iBAC9C,GAAP;AAAA;AACF,YAAI,CAAC,IAAI;AAAE,eAAK;AAAA;AAChB,eAAO;AAAA;AAET,UAAI,mBAAmB,SAAU,SAAS;AACxC,YAAI,uBAAuB,QAAQ,aAAa;AAChD,YAAI,wBAAwB,MAAM;AAChC,+BAAqB;AAAA;AAAA;AAIzB,UAAI,aAAa;AAAA,QACf,EAAE,KAAK,aAAa,SAAS;AAAA,QAC7B,EAAE,KAAK,aAAa,SAAS;AAAA,QAC7B,EAAE,KAAK,YAAY,SAAS;AAAA,QAC5B,EAAE,KAAK,cAAc,SAAS;AAAA,QAC9B,EAAE,KAAK,gBAAgB,SAAS;AAAA,QAChC,EAAE,KAAK,cAAc,SAAS;AAAA,QAC9B,EAAE,KAAK,uBAAuB,SAAS;AAAA,QACvC,EAAE,KAAK,oBAAoB,SAAS;AAAA,QACpC,EAAE,KAAK,6BAA6B,SAAS;AAAA,QAC7C,EAAE,KAAK,kBAAkB,SAAS;AAAA,QAClC,EAAE,KAAK,YAAY,SAAS;AAAA,QAC5B,EAAE,KAAK,kBAAkB,SAAS;AAAA,QAClC,EAAE,KAAK,gBAAgB,SAAS;AAAA,QAChC,EAAE,KAAK,aAAa,SAAS;AAAA,QAC7B,EAAE,KAAK,eAAe,SAAS;AAAA,QAC/B,EAAE,KAAK,gBAAgB,SAAS;AAAA,QAChC,EAAE,KAAK,YAAY,SAAS;AAAA,QAC5B,EAAE,KAAK,YAAY,SAAS;AAAA,QAC5B,EAAE,KAAK,cAAc,SAAS;AAAA,QAC9B,EAAE,KAAK,WAAW,SAAS;AAAA,QAC3B,EAAE,KAAK,UAAU,SAAS;AAAA,QAC1B,EAAE,KAAK,SAAS,SAAS;AAAA,QACzB,EAAE,KAAK,0BAA0B,SAAS;AAAA,QAC1C,EAAE,KAAK,WAAW,SAAS;AAAA,QAC3B,EAAE,KAAK,oBAAoB,SAAS;AAAA,QACpC,EAAE,KAAK,qBAAqB,SAAS;AAAA,QACrC,EAAE,KAAK,aAAa,SAAS;AAAA,QAC7B,EAAE,KAAK,kBAAkB,SAAS;AAAA,QAClC,EAAE,KAAK,gBAAgB,SAAS;AAAA,QAChC,EAAE,KAAK,SAAS,SAAS,YAAY,aAAa;AAAA,QAClD,EAAE,KAAK,cAAc,SAAS,eAAe,aAAa;AAAA,QAC1D,EAAE,KAAK,SAAS,SAAS;AAAA,QACzB,EAAE,KAAK,oBAAoB,SAAS;AAAA;AAGtC,UAAI,eAAe,SAAU,SAAS;AACpC,cAAM,IAAI,MAAM;AAAA;AAGlB,mBAAa,MAAM,SAAU,SAAS,UAAU;AAC9C,YAAI,CAAC,UAAU;AACb,qBAAW;AACX,oBAAU;AAAA,mBACD,CAAC,SAAS;AACnB,oBAAU;AAAA;AAEZ,mBAAW,SAAS;AACpB,gBAAQ,aAAa,QAAQ,gBAAgB,OAAO;AAEpD,YAAI,OAAO;AAAA,UACT,MAAM;AAAA,UACN,0BAA0B,SAAU,KAAK,OAAO;AAC9C,gBAAI,OAAO,QAAQ,iBAAiB,YAAY;AAC9C,sBAAQ,QAAQ,aAAa,KAAK;AAAA;AAEpC,iBAAK,KAAK,KAAK,EAAE,KAAU;AAAA;AAAA;AAI/B,YAAI,IAAI;AACR,YAAI,kBAAkB,SAAU,eAAe;AAC7C,eAAK;AACL,cAAI,KAAK,QAAQ,WAAW,QAAQ;AAClC,qBAAS,KAAK;AACd;AAAA;AAEF,cAAI,YAAY,QAAQ,WAAW;AAEnC,cAAI,QAAQ,SAAS,UAAU,MAAM;AACnC,4BAAgB;AAChB;AAAA;AAGF,cAAI,CAAC,iBAAiB,UAAU,aAAa;AAC3C,iBAAK;AACL,uBAAW,WAAY;AACrB,8BAAgB;AAAA,eACf;AACH;AAAA;AAGF,cAAI;AACF,sBAAU,QAAQ,SAAU,OAAO;AACjC,mBAAK,yBAAyB,UAAU,KAAK;AAC7C,8BAAgB;AAAA,eACf;AAAA,mBACI,OAAP;AAEA,iBAAK,yBAAyB,UAAU,KAAK,OAAO;AACpD,4BAAgB;AAAA;AAAA;AAIpB,wBAAgB;AAAA;AAGlB,mBAAa,aAAa,SAAU,SAAS;AAC3C,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,uBAAa,IAAI,SAAS;AAAA;AAAA;AAI9B,mBAAa,SAAS,SAAU,SAAS,UAAU;AACjD,YAAI,YAAY,MAAM;AACpB,qBAAW;AACX,oBAAU;AAAA;AAEZ,eAAO,aAAa,IAAI,SAAS,SAAU,aAAY;AACrD,cAAI,gBAAgB;AACpB,mBAAS,IAAI,GAAG,IAAI,YAAW,QAAQ,KAAK;AAC1C,gBAAI,YAAY,YAAW;AAC3B,gBAAI,UAAU,UAAW,SAAQ,iBAAiB,kBAAkB;AAClE,4BAAc,KAAK,EAAE,KAAK,UAAU,KAAK,OAAO;AAAA,uBACvC,UAAU,QAAQ,WAAW;AACtC,4BAAc,KAAK;AAAA,gBACjB,KAAK;AAAA,gBACL,OAAO,IAAI,UAAU,OAAO,SAAU,GAAG;AACvC,sBAAI,YAAY,IAAI,EAAE,IAAI,SAAU,IAAI;AACtC,wBAAI,GAAG,MAAM;AAAE,6BAAO,GAAG,KAAK;AAAA;AAC9B,2BAAO;AAAA,qBACN,KAAK;AACR,yBAAO,CAAC,EAAE,IAAI,EAAE,IAAI,WAAW,KAAK;AAAA;AAAA;AAAA,uBAG/B,CAAC,UAAU,SAAS,QAAQ,UAAU,SAAS,MAAM,MAAM,QAAQ,UAAU,QAAQ;AAG9F,4BAAc,KAAK,EAAE,KAAK,UAAU,KAAK,OAAO,UAAU,MAAM,KAAK;AAAA,uBAC5D,CAAC,kBAAkB,gBAAgB,aAAa,eAAe,gBAAgB,QAAQ,UAAU,SAAS,IAAI;AACvH,kBAAI,UAAU,OAAO;AACnB,8BAAc,KAAK,EAAE,KAAK,UAAU,KAAK,OAAO;AAAA,qBAC3C;AAEL;AAAA;AAAA,mBAEG;AACL,kBAAI,UAAU,OAAO;AACnB,8BAAc,KAAK,UAAU,MAAM,OAAO,EAAE,KAAK,UAAU,KAAK,OAAO,UAAU,MAAM,KAAK,SAAS;AAAA,qBAChG;AACL,8BAAc,KAAK,EAAE,KAAK,UAAU,KAAK,OAAO,UAAU;AAAA;AAAA;AAAA;AAIhE,cAAI,SAAS,WAAW,IAAI,eAAe,SAAU,YAAW;AAAE,mBAAO,WAAU;AAAA,aAAS,KAAK,QAAQ;AACzG,mBAAS,QAAQ;AAAA;AAAA;AAIrB,mBAAa,aAAa;AAC1B,mBAAa,UAAU;AACvB,aAAO;AAAA;AAAA;AAAA;;;ACn8CT,IAAO,yBAAQ;", "names": []}