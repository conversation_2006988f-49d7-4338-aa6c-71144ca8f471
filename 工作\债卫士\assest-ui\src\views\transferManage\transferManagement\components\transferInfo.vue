<template>
    <div class="transferInfo">
        <div class="content-head df-jc-sb mb20">
            <span>资产包名称：招商银行4月第1季度信用不良3号包</span>
            <div class="btn-list">
                <el-button type="primary" v-if="props.activeStep == 2 && statusInfo.isRegistered"
                    @click="handleOpenDialog(cancelInfo[0])">取消登记</el-button>
                <el-button type="primary" v-if="props.activeStep == 2 && !statusInfo.isRegistered"
                    @click="handleOpenDialog(cancelInfo[1])">银登登记</el-button>
                <el-button type="primary"
                    v-if="props.activeStep == 2 && !statusInfo.isListing && statusInfo.isRegistered"
                    @click="handleOpenDialog(cancelInfo[2])">银登挂牌</el-button>
                <el-button type="primary"
                    v-if="props.activeStep == 3 && statusInfo.isListing && statusInfo.isRegistered"
                    @click="handleOpenDialog(cancelInfo[3])">取消挂牌</el-button>
                <el-button v-for="v in btnShowList" :key="v" type="primary" @click="handleOpenDialog(v)">
                    {{ v.label }}
                </el-button>
            </div>
        </div>
        <div class="head-info">
            <div class="head-item">
                <div class="head-item-label">资产包状态：</div>
                <div class="info-item-value">{{ state }}</div>
            </div>
            <div class="head-item" v-for="v in headInfoList" :key="v">
                <div class="head-item-label">{{ v.label }}：</div>
                <div class="info-item-value">{{ v.value ? v.value : (form[v.prop] || '--') }}</div>
            </div>
        </div>
        <el-descriptions border class="mb20" :column="3">
            <el-descriptions-item :label="v.label" v-for="v in infoList" :key="v">
                {{ v.value ? v.value : (form[v.prop] || '--') }}
            </el-descriptions-item>
        </el-descriptions>
        <el-table :data="dataList" v-loading="loading">
            <el-table-column label="操作人" align="center" min-width="120" prop="aa" />
            <el-table-column label="备注信息" align="center" min-width="200" prop="bb" />
            <el-table-column label="附件" align="center" min-width="120" prop="cc" />
            <el-table-column label="提交时间" align="center" width="160" prop="dd" />
            <el-table-column label="当时状态" align="center" min-width="120" prop="ee" />
        </el-table>
        <GetAMCFile ref="GetAMCFileRef" />
        <UploadInfo ref="UploadInfoRef" />
        <DeliveryList ref="DeliveryListRef" />
    </div>
</template>

<script setup>

import DeliveryList from '../dialog/deliveryList.vue'
import GetAMCFile from '../dialog/getAMCFile.vue'
import UploadInfo from '../dialog/uploadInfo.vue'
const props = defineProps({
    state: { type: String, default: '' },
    activeStep: { type: [String, Number], default: 1 },
    nextStep: { type: Function, default: () => { } },
    prevStep: { type: Function, default: () => { } },
})
const { proxy } = getCurrentInstance()
const statusInfo = ref({
    isListing: false,
    isRegistered: false,
})
const dataList = ref([
    { aa: "内部01", bb: "董主任你好，请注意文件是否复合银登中心的要求", cc: "银登中心要求文件.png", dd: "2025-06-09 09:47:40", ee: "已预登记", }
])
const cancelInfo = ref({
    0: { label: '取消登记', optFun, fileName: 'isRegistered', isCancel: true, typeList: [2], dialogInfo: { fileName: '上传附件', title: '取消登记', desc: '确认取消登记吗，将会同步向银登进行取消操作？' } },
    1: { label: '银登登记', optFun, fileName: 'isRegistered', typeList: [2], step: '0', dialogInfo: { fileName: '上传附件', title: '银登登记', desc: '确认后将生成登记数据，并在银登进行正式登记?' } },
    2: { label: '银登挂牌', optFun, fileName: 'isListing', typeList: [2], dialogInfo: { fileName: '上传附件', title: '银登挂牌', desc: '确认后将向银登进行挂牌操作？' } },
    3: { label: '取消挂牌', optFun, fileName: 'isListing', isCancel: true, typeList: [3], dialogInfo: { fileName: '上传附件', title: '取消挂牌', desc: '是否取消挂牌，使银登挂牌失效？' } },
})
const loading = ref(false)
const btnList = ref([
    { label: '评估尽调', typeList: [1], dialogInfo: { fileName: '上传附件', title: '评估尽调', desc: '确认后将向业务系统获取数据，并生成尽调报告' } },
    { label: '交割完成', typeList: [7], dialogInfo: { fileName: '上传附件', title: '获取银登确认书', desc: '确认完成交割吗，将会对该资产包进行数据闭环，请确认后再操作!' } },
    { label: '上传资金到账凭证', typeList: [5], step: '2', dialogInfo: { fileName: '上传到账凭证文件及附件', title: '上传资金到账凭证', desc: '请上传资金到账凭证！' } },
    { label: '下载待签署文件', typeList: [4], func: handleDownload },
    { label: '上传已签署协议', typeList: [4], dialogInfo: { fileName: '上传签署协议及附件', title: '上传签署协议', desc: '请上传已签署的协议!' } },
    { label: '获取银登确认书', typeList: [3], dialogInfo: { fileName: '上传附件', title: '获取银登确认书', desc: '确认则将向银登获取需签署的确认书!' } },
    { label: '审批AMC获取文件', isAll: true, func: handleAMCFile },
    { label: '交割清单', isAll: true, func: handleDeliveryList },
])
const form = ref({})
const headInfoList = ref([
    { label: '债权剩余未还总额', prop: '', value: '2,119,587,346.49' },
    { label: '帐龄结构', prop: '', value: '1-3年' },
    { label: '预估折扣', prop: '', value: '0.26折' },
])
const infoList = ref([
    { label: '资产包编号', prop: '', value: 'ZCB12341235321' },
    { label: '出让方名称', prop: '', value: '广东消费金融股份有限公司' },
    { label: '挂牌日期', prop: '', value: '--' },
    { label: '未偿本金总额', prop: '', value: '613,412,123.45' },
    { label: '未偿利息总额', prop: '', value: '2,119,587,346.49' },
    { label: '本息合计金额', prop: '', value: '2,119,587,346.49' },
    { label: '总笔数', prop: '', value: '228,683' },
    { label: '涉及借款人数', prop: '', value: '119612' },
    { label: '加权平均逾期天数', prop: '', value: '613-630 天' },
    { label: '起拍价', prop: '', value: '1,754 万 - 42,490 万元（分四期）' },
    { label: '本金折扣率', prop: '', value: '0.21% - 8.8%' },
    { label: '估值方法', prop: '', value: '历史回收率模型(同类资产包回收率 5%-10%)' },
])
function handleOpenDialog(row) {
    if (row.func) {
        row.func()
        return false
    }
    const updateStep = row.isCancel ? props.prevStep : props.nextStep
    const data = { ...row, updateStep }
    row.dialogInfo && proxy.$refs['UploadInfoRef'].openDialog(data)
}

function optFun(fileName) {
    statusInfo.value[fileName] = !statusInfo.value[fileName]
}

function handleAMCFile() {
    proxy.$refs['GetAMCFileRef'].openDialog()
}
function handleDeliveryList() {
    proxy.$refs['DeliveryListRef'].openDialog()
}
function handleDownload() {
    const fileName = `${+new Date()}.xlsx`
    exportFile('https://assest.amcmj.com/resource/preview/2025/06/18/d07d05ec0b6f40b99cff7fb833c46a0c_错误的案件.xlsx', fileName)
}
const btnShowList = computed(() => btnList.value.filter(v => v.isAll || v.typeList.includes(+props.activeStep)))

function exportFile(data, fileName) {
    // 地址不存在时，禁止操作
    if (!data) return;
    proxy.$modal.notify('正在下载中...')
    // 下载文件并保存到本地
    const callback = (data) => {
        // 创建a标签，使用 html5 download 属性下载，
        const link = document.createElement('a');
        // 创建url对象
        const objectUrl = window.URL.createObjectURL(new Blob([data]));
        link.style.display = 'none';
        link.href = objectUrl;
        // 自定义文件名称， fileName
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        // 适当释放url
        window.URL.revokeObjectURL(objectUrl);
    };
    // 把接口返回的url地址转换为 blob
    const xhr = new XMLHttpRequest();
    xhr.open('get', data, true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
        // 返回文件流，进行下载处理
        callback(xhr.response);
        proxy.$modal.msgSuccess('操作成功！')
    };
    xhr.send(); // 不要忘记发送
};
</script>

<style lang="scss" scoped>
.transferInfo {
    padding: 20px;
    background-color: #fff;
}

.content-head {
    padding-bottom: 20px;
    border-bottom: 1px solid #EFF0F1;
}

.head-info {
    display: grid;
    gap: 20px;
    margin-bottom: 20px;
    grid-template-columns: repeat(4, 1fr);

    .head-item {
        font-size: 14px;
        padding: 10px 20px;
        color: #999999;
        border-radius: 8px;
        border: 1px solid #EFF0F1;

        .info-item-value {
            font-size: 20px;
            color: #333333;
            margin-top: 5px;
        }
    }
}

.info-list {
    display: grid;
    margin-bottom: 20px;
    border: 1px solid #ccc;
    grid-template-columns: repeat(3, 1fr);

    .info-item {
        font-size: 14px;
        display: grid;
        border-right: 1px solid #ccc;
        grid-template-columns: repeat(2, 1fr);

        &>div {
            padding: 15px 0;
        }

        .info-item-label {
            text-align: center;
            border-right: 1px solid #ccc;
        }

        .info-item-value {
            padding: 15px;
        }

        &:nth-of-type(3n) {
            border-right: none;
        }

        &:nth-of-type(n + 3) {
            border-top: 1px solid #ccc;
        }
    }
}
</style>