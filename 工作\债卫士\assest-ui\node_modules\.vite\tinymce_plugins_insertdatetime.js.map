{"version": 3, "sources": ["../tinymce/plugins/insertdatetime/plugin.js", "../tinymce/plugins/insertdatetime/index.js", "dep:tinymce_plugins_insertdatetime"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var getDateFormat = function (editor) {\n      return editor.getParam('insertdatetime_dateformat', editor.translate('%Y-%m-%d'));\n    };\n    var getTimeFormat = function (editor) {\n      return editor.getParam('insertdatetime_timeformat', editor.translate('%H:%M:%S'));\n    };\n    var getFormats = function (editor) {\n      return editor.getParam('insertdatetime_formats', [\n        '%H:%M:%S',\n        '%Y-%m-%d',\n        '%I:%M:%S %p',\n        '%D'\n      ]);\n    };\n    var getDefaultDateTime = function (editor) {\n      var formats = getFormats(editor);\n      return formats.length > 0 ? formats[0] : getTimeFormat(editor);\n    };\n    var shouldInsertTimeElement = function (editor) {\n      return editor.getParam('insertdatetime_element', false);\n    };\n\n    var daysShort = 'Sun Mon Tue Wed Thu Fri Sat Sun'.split(' ');\n    var daysLong = 'Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday'.split(' ');\n    var monthsShort = 'Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec'.split(' ');\n    var monthsLong = 'January February March April May June July August September October November December'.split(' ');\n    var addZeros = function (value, len) {\n      value = '' + value;\n      if (value.length < len) {\n        for (var i = 0; i < len - value.length; i++) {\n          value = '0' + value;\n        }\n      }\n      return value;\n    };\n    var getDateTime = function (editor, fmt, date) {\n      if (date === void 0) {\n        date = new Date();\n      }\n      fmt = fmt.replace('%D', '%m/%d/%Y');\n      fmt = fmt.replace('%r', '%I:%M:%S %p');\n      fmt = fmt.replace('%Y', '' + date.getFullYear());\n      fmt = fmt.replace('%y', '' + date.getYear());\n      fmt = fmt.replace('%m', addZeros(date.getMonth() + 1, 2));\n      fmt = fmt.replace('%d', addZeros(date.getDate(), 2));\n      fmt = fmt.replace('%H', '' + addZeros(date.getHours(), 2));\n      fmt = fmt.replace('%M', '' + addZeros(date.getMinutes(), 2));\n      fmt = fmt.replace('%S', '' + addZeros(date.getSeconds(), 2));\n      fmt = fmt.replace('%I', '' + ((date.getHours() + 11) % 12 + 1));\n      fmt = fmt.replace('%p', '' + (date.getHours() < 12 ? 'AM' : 'PM'));\n      fmt = fmt.replace('%B', '' + editor.translate(monthsLong[date.getMonth()]));\n      fmt = fmt.replace('%b', '' + editor.translate(monthsShort[date.getMonth()]));\n      fmt = fmt.replace('%A', '' + editor.translate(daysLong[date.getDay()]));\n      fmt = fmt.replace('%a', '' + editor.translate(daysShort[date.getDay()]));\n      fmt = fmt.replace('%%', '%');\n      return fmt;\n    };\n    var updateElement = function (editor, timeElm, computerTime, userTime) {\n      var newTimeElm = editor.dom.create('time', { datetime: computerTime }, userTime);\n      timeElm.parentNode.insertBefore(newTimeElm, timeElm);\n      editor.dom.remove(timeElm);\n      editor.selection.select(newTimeElm, true);\n      editor.selection.collapse(false);\n    };\n    var insertDateTime = function (editor, format) {\n      if (shouldInsertTimeElement(editor)) {\n        var userTime = getDateTime(editor, format);\n        var computerTime = void 0;\n        if (/%[HMSIp]/.test(format)) {\n          computerTime = getDateTime(editor, '%Y-%m-%dT%H:%M');\n        } else {\n          computerTime = getDateTime(editor, '%Y-%m-%d');\n        }\n        var timeElm = editor.dom.getParent(editor.selection.getStart(), 'time');\n        if (timeElm) {\n          updateElement(editor, timeElm, computerTime, userTime);\n        } else {\n          editor.insertContent('<time datetime=\"' + computerTime + '\">' + userTime + '</time>');\n        }\n      } else {\n        editor.insertContent(getDateTime(editor, format));\n      }\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mceInsertDate', function (_ui, value) {\n        insertDateTime(editor, value !== null && value !== void 0 ? value : getDateFormat(editor));\n      });\n      editor.addCommand('mceInsertTime', function (_ui, value) {\n        insertDateTime(editor, value !== null && value !== void 0 ? value : getTimeFormat(editor));\n      });\n    };\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var register = function (editor) {\n      var formats = getFormats(editor);\n      var defaultFormat = Cell(getDefaultDateTime(editor));\n      var insertDateTime = function (format) {\n        return editor.execCommand('mceInsertDate', false, format);\n      };\n      editor.ui.registry.addSplitButton('insertdatetime', {\n        icon: 'insert-time',\n        tooltip: 'Insert date/time',\n        select: function (value) {\n          return value === defaultFormat.get();\n        },\n        fetch: function (done) {\n          done(global.map(formats, function (format) {\n            return {\n              type: 'choiceitem',\n              text: getDateTime(editor, format),\n              value: format\n            };\n          }));\n        },\n        onAction: function (_api) {\n          insertDateTime(defaultFormat.get());\n        },\n        onItemAction: function (_api, value) {\n          defaultFormat.set(value);\n          insertDateTime(value);\n        }\n      });\n      var makeMenuItemHandler = function (format) {\n        return function () {\n          defaultFormat.set(format);\n          insertDateTime(format);\n        };\n      };\n      editor.ui.registry.addNestedMenuItem('insertdatetime', {\n        icon: 'insert-time',\n        text: 'Date/time',\n        getSubmenuItems: function () {\n          return global.map(formats, function (format) {\n            return {\n              type: 'menuitem',\n              text: getDateTime(editor, format),\n              onAction: makeMenuItemHandler(format)\n            };\n          });\n        }\n      });\n    };\n\n    function Plugin () {\n      global$1.add('insertdatetime', function (editor) {\n        register$1(editor);\n        register(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"insertdatetime\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/insertdatetime')\n//   ES2015:\n//     import 'tinymce/plugins/insertdatetime'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/insertdatetime/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS,6BAA6B,OAAO,UAAU;AAAA;AAEvE,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS,6BAA6B,OAAO,UAAU;AAAA;AAEvE,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,OAAO,SAAS,0BAA0B;AAAA,UAC/C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,qBAAqB,SAAU,QAAQ;AACzC,YAAI,UAAU,WAAW;AACzB,eAAO,QAAQ,SAAS,IAAI,QAAQ,KAAK,cAAc;AAAA;AAEzD,UAAI,0BAA0B,SAAU,QAAQ;AAC9C,eAAO,OAAO,SAAS,0BAA0B;AAAA;AAGnD,UAAI,YAAY,kCAAkC,MAAM;AACxD,UAAI,WAAW,kEAAkE,MAAM;AACvF,UAAI,cAAc,kDAAkD,MAAM;AAC1E,UAAI,aAAa,wFAAwF,MAAM;AAC/G,UAAI,WAAW,SAAU,OAAO,KAAK;AACnC,gBAAQ,KAAK;AACb,YAAI,MAAM,SAAS,KAAK;AACtB,mBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,oBAAQ,MAAM;AAAA;AAAA;AAGlB,eAAO;AAAA;AAET,UAAI,cAAc,SAAU,QAAQ,KAAK,MAAM;AAC7C,YAAI,SAAS,QAAQ;AACnB,iBAAO,IAAI;AAAA;AAEb,cAAM,IAAI,QAAQ,MAAM;AACxB,cAAM,IAAI,QAAQ,MAAM;AACxB,cAAM,IAAI,QAAQ,MAAM,KAAK,KAAK;AAClC,cAAM,IAAI,QAAQ,MAAM,KAAK,KAAK;AAClC,cAAM,IAAI,QAAQ,MAAM,SAAS,KAAK,aAAa,GAAG;AACtD,cAAM,IAAI,QAAQ,MAAM,SAAS,KAAK,WAAW;AACjD,cAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,YAAY;AACvD,cAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,cAAc;AACzD,cAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,cAAc;AACzD,cAAM,IAAI,QAAQ,MAAM,KAAO,OAAK,aAAa,MAAM,KAAK;AAC5D,cAAM,IAAI,QAAQ,MAAM,KAAM,MAAK,aAAa,KAAK,OAAO;AAC5D,cAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,WAAW,KAAK;AAC9D,cAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,YAAY,KAAK;AAC/D,cAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,SAAS,KAAK;AAC5D,cAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,UAAU,KAAK;AAC7D,cAAM,IAAI,QAAQ,MAAM;AACxB,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,QAAQ,SAAS,cAAc,UAAU;AACrE,YAAI,aAAa,OAAO,IAAI,OAAO,QAAQ,EAAE,UAAU,gBAAgB;AACvE,gBAAQ,WAAW,aAAa,YAAY;AAC5C,eAAO,IAAI,OAAO;AAClB,eAAO,UAAU,OAAO,YAAY;AACpC,eAAO,UAAU,SAAS;AAAA;AAE5B,UAAI,iBAAiB,SAAU,QAAQ,QAAQ;AAC7C,YAAI,wBAAwB,SAAS;AACnC,cAAI,WAAW,YAAY,QAAQ;AACnC,cAAI,eAAe;AACnB,cAAI,WAAW,KAAK,SAAS;AAC3B,2BAAe,YAAY,QAAQ;AAAA,iBAC9B;AACL,2BAAe,YAAY,QAAQ;AAAA;AAErC,cAAI,UAAU,OAAO,IAAI,UAAU,OAAO,UAAU,YAAY;AAChE,cAAI,SAAS;AACX,0BAAc,QAAQ,SAAS,cAAc;AAAA,iBACxC;AACL,mBAAO,cAAc,qBAAqB,eAAe,OAAO,WAAW;AAAA;AAAA,eAExE;AACL,iBAAO,cAAc,YAAY,QAAQ;AAAA;AAAA;AAI7C,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,iBAAiB,SAAU,KAAK,OAAO;AACvD,yBAAe,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ,cAAc;AAAA;AAEpF,eAAO,WAAW,iBAAiB,SAAU,KAAK,OAAO;AACvD,yBAAe,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ,cAAc;AAAA;AAAA;AAItF,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,QAAQ;AACZ,YAAI,MAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,kBAAQ;AAAA;AAEV,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,UAAU,WAAW;AACzB,YAAI,gBAAgB,KAAK,mBAAmB;AAC5C,YAAI,kBAAiB,SAAU,QAAQ;AACrC,iBAAO,OAAO,YAAY,iBAAiB,OAAO;AAAA;AAEpD,eAAO,GAAG,SAAS,eAAe,kBAAkB;AAAA,UAClD,MAAM;AAAA,UACN,SAAS;AAAA,UACT,QAAQ,SAAU,OAAO;AACvB,mBAAO,UAAU,cAAc;AAAA;AAAA,UAEjC,OAAO,SAAU,MAAM;AACrB,iBAAK,OAAO,IAAI,SAAS,SAAU,QAAQ;AACzC,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM,YAAY,QAAQ;AAAA,gBAC1B,OAAO;AAAA;AAAA;AAAA;AAAA,UAIb,UAAU,SAAU,MAAM;AACxB,4BAAe,cAAc;AAAA;AAAA,UAE/B,cAAc,SAAU,MAAM,OAAO;AACnC,0BAAc,IAAI;AAClB,4BAAe;AAAA;AAAA;AAGnB,YAAI,sBAAsB,SAAU,QAAQ;AAC1C,iBAAO,WAAY;AACjB,0BAAc,IAAI;AAClB,4BAAe;AAAA;AAAA;AAGnB,eAAO,GAAG,SAAS,kBAAkB,kBAAkB;AAAA,UACrD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,iBAAiB,WAAY;AAC3B,mBAAO,OAAO,IAAI,SAAS,SAAU,QAAQ;AAC3C,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM,YAAY,QAAQ;AAAA,gBAC1B,UAAU,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAOxC,wBAAmB;AACjB,iBAAS,IAAI,kBAAkB,SAAU,QAAQ;AAC/C,qBAAW;AACX,mBAAS;AAAA;AAAA;AAIb;AAAA;AAAA;AAAA;;;ACnLJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,yCAAQ;", "names": []}