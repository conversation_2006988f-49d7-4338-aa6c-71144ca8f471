<template>
    <div class="mt20">
        <el-form inline label-width="auto" :model="queryParmas">
            <el-form-item label="项目ID" prop="projectId">
                <el-input v-model="queryParmas.projectId" style="width: 320px;" placeholder="请输入项目ID" />
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="queryParmas.projectName" style="width: 320px;" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item label="资产转让方" prop="assetTransferor">
                <el-input v-model="queryParmas.assetTransferor" style="width: 320px;" placeholder="请输入资产转让方" />
            </el-form-item>
            <el-form-item label="产品类型" prop="productType">
                <el-input v-model="queryParmas.productType" style="width: 320px;" placeholder="请输入产品类型" />
            </el-form-item>
            <el-form-item label="合同状态" prop="contractStatus">
                <el-input v-model="queryParmas.contractStatus" style="width: 320px;" placeholder="请输入合同状态" />
            </el-form-item>
            <el-form-item label="合同名称" prop="contractName">
                <el-input v-model="queryParmas.contractName" style="width: 320px;" placeholder="请输入合同名称" />
            </el-form-item>
            <el-form-item label="合同编号" prop="contractNumber">
                <el-input v-model="queryParmas.contractNumber" style="width: 320px;" placeholder="请输入合同编号" />
            </el-form-item>
            <el-form-item label="合同类型" prop="contractType">
                <el-input v-model="queryParmas.contractType" style="width: 320px;" placeholder="请输入合同类型" />
            </el-form-item>
            <el-form-item label="申请人" prop="applicant">
                <el-input v-model="queryParmas.applicant" style="width: 320px;" placeholder="请输入申请人" />
            </el-form-item>
            <el-form-item label="申请时间" prop="applicationTime">
                <el-input v-model="queryParmas.applicationTime" style="width: 320px;" placeholder="请输入申请时间" />
            </el-form-item>
            <el-form-item label="发起方式" prop="initiationMethod">
                <el-input v-model="queryParmas.initiationMethod" style="width: 320px;" placeholder="请输入发起方式" />
            </el-form-item>
            <el-form-item label="印章类型" prop="sealType">
                <el-input v-model="queryParmas.sealType" style="width: 320px;" placeholder="请输入印章类型" />
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button @click="antiShake(handleQuery)" plain type="primary">搜索</el-button>
            <el-button @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="opt-area">
            <el-button plain type="success">通过</el-button>
            <el-button plain type="warning">不通过</el-button>
        </div>
        <SelectedAll />
        <el-tabs v-model="activetab" @tab-change="antiShake(handleQuery)">
            <el-tab-pane v-for="item in tabList" :key="item.code" :label="item.info" :name="item.code" />
        </el-tabs>
        <el-table :data="dataList">
            <el-table-column type="selection" width="44px" :selectable="selectable" align="right" />
            <el-table-column label="项目ID" align="center" prop="projectId" width="120" />
            <el-table-column label="项目名称" align="center" prop="projectName" width="120" />
            <el-table-column label="资产转让方" align="center" prop="assetTransferor" width="120" />
            <el-table-column label="产品类型" align="center" prop="productType" width="120" />
            <el-table-column label="合同状态" align="center" prop="contractStatus" width="120" />
            <el-table-column label="合同名称" align="center" prop="contractName" width="120" />
            <el-table-column label="合同编号" align="center" prop="contractNumber" width="120" />
            <el-table-column label="合同类型" align="center" prop="contractType" width="120" />
            <el-table-column label="发起方式" align="center" prop="initiationMethod" width="120" />
            <el-table-column label="签署份数" align="center" prop="signatureCopies" width="120" />
            <el-table-column label="印章类型" align="center" prop="sealType" width="120" />
            <el-table-column label="申请人" align="center" prop="applicant" width="120" />
            <el-table-column label="申请时间" align="center" prop="applicationTime" width="120" />
            <el-table-column label="处理状态" align="center" prop="processingStatus" width="120" />
            <el-table-column label="处理人" align="center" prop="processor" width="120" />
            <el-table-column label="处理时间" align="center" prop="updateTime" width="120" />
            <el-table-column width="180" fixed="right" label="操作" align="center">
                <template #default="{ row }">
                    <el-button type="text" @click="handle(row)">通过</el-button>
                    <el-button type="text" @click="handle(row)">不通过</el-button>
                    <el-button type="text" @click="handleDetails(row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import SelectedAll from '@/components/SelectedAll/index.vue'

const route = useRoute()
const router = useRouter()
const activetab = ref('0')

const dataList = ref([
    {
        projectId: 'zsb20241122001',
        projectName: '2024不良资产处置项目',
        assetTransferor: '保密院',
        productType: '审批中',
        contractStatus: '',
        contractName: '',
        contractNumber: '',
        contractType: '',
        initiationMethod: '',
        signatureCopies: '3',
        sealType: '',
        applicant: '',
        applicationTime: '',
        processingStatus: '待处理',
        processor: '--',
        updateTime: '--'
    },
    {
        projectId: 'zsb20241122002',
        projectName: '2024不良资产处置项目22',
        assetTransferor: '金融发展研究',
        productType: '2023年度资产处置项目',
        contractStatus: '',
        contractName: '',
        contractNumber: '',
        contractType: '线下',
        initiationMethod: '私人签',
        signatureCopies: '3',
        sealType: '保密章',
        applicant: '',
        applicationTime: '2024-11-10 12:00:00',
        processingStatus: '已处理',
        processor: '保密章',
        updateTime: '2024-11-10 12:00:00'
    },
    {
        projectId: 'zsb20241122003',
        projectName: '2024不良资产处置项目11',
        assetTransferor: '金融发展研究',
        productType: '2023年度资产处置项目',
        contractStatus: '',
        contractName: '',
        contractNumber: '',
        contractType: '线上',
        initiationMethod: '公章',
        signatureCopies: '3',
        sealType: '保密章',
        applicant: '',
        applicationTime: '2024-11-10 12:00:00',
        processingStatus: '未处理',
        processor: '保密章',
        updateTime: '2024-11-10 12:00:00'
    }
])

const tabList = ref([
    { code: '0', info: '待处理' },
    { code: '1', info: '已同意' },
    { code: '2', info: '未同意' },
    { code: '3', info: '已撤销' },
    { code: 'all', info: '全部' },
])

const queryParmas = ref({ pageNum: 1, pageSize: 10 })

const handleQuery = () => {
    queryParmas.value.pageNum = 1
}

const handle = (row) => {
    console.log('处理行数据：', row)
}

function handleDetails(row) {
    const query = { path: route.path, pageType: 'contract', progressStatus: 2 }
    router.push({ path: `/dueDiligence/projectInfo`, query })
}
const selectable = (row) => {
    // 根据状态判断是否可选
    return true
}

const resetQuery = () => {
    queryParmas.value = { pageNum: 1, pageSize: 10 }
}
</script>

<style lang="scss" scoped>
.range-scope {
    display: flex;

    span {
        margin: 0 10px;
    }
}
</style>