<template>
  <div class="app-container">
    <zws-form ref="formRef" :form-items="formItems" @search="handleSearch" @reset="handleReset" />
    <zws-table :loading="loading" :columns="columns" :tableData="dataList">
      <template #opt="{ row }">
        <el-button type="primary" link size="small" @click="handleDownload(row)">撤销</el-button>
        <el-button type="primary" link size="small" @click="handleDetails(row)">详情</el-button>
      </template>
    </zws-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="myPolicy">
import ZwsForm from "@/components/ZwsForm.vue";
import SchemeApprovalTable from "./table";
const formRef = ref(null);

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
//列表选中
const ids = ref([]);

const loading = ref(true);
const total = ref(0);
const dataList = ref([]); //列表
const store = useStore();

const lawyerOptions = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 列显隐信息
const columns = ref([
  {
    key: 0,
    label: "项目ID",
    visible: true,
    width: "160",
    prop: "projectId",
  },
  {
    key: 1,
    label: "项目名称",
    visible: true,
    width: "180",
    prop: "projectName",
  },
  {
    key: 2,
    label: "产品类型",
    visible: true,
    width: "120",
    prop: "productType",
  },
  {
    key: 3,
    label: "项目状态",
    visible: true,
    width: "120",
    prop: "projectStatus",
  },
  {
    key: 4,
    label: "资产转让方",
    visible: true,
    width: "120",
    prop: "transferor",
  },
  {
    key: 5,
    label: "债权总金额(元)",
    visible: true,
    prop: "debtTotalAmount",
    width: "130",
    slot: true,
  },
  { key: 6, label: "债权本金", visible: true, prop: "debtPrincipal" },
  {
    key: 7,
    label: "户数",
    visible: true,
    prop: "debtCount",
  },
  {
    key: 8,
    label: "基准日",
    visible: true,
    prop: "benchmarkDate",
    width: "130",
  },
  {
    key: 9,
    label: "预计竞价日期",
    visible: true,
    prop: "expectedAuctionDate",
    width: "130",
  },
  {
    key: 10,
    label: "投标方式",
    visible: true,
    prop: "bidType",
    width: "130",
  },
  {
    key: 11,
    label: "报价上限",
    visible: true,
    prop: "bidUpperLimit",
    width: "130",
  },
  {
    key: 12,
    label: "资产评估表",
    visible: true,
    prop: "assetEvaluationTable",
    width: "130",
  },
  {
    key: 13,
    label: "立项报告",
    visible: true,
    prop: "projectProposal",
  },
  {
    key: 14,
    label: "其他附件",
    visible: true,
    prop: "otherAttachments",
    width: "130",
  },
  {
    key: 15,
    label: "立项人",
    visible: true,
    prop: "projectInitiator",
    width: "130",
  },
  {
    key: 16,
    label: "立项时间",
    visible: true,
    prop: "projectInitiationTime",
    width: "160",
  },
  {
    key: 17,
    label: "审核时间",
    visible: true,
    prop: "auditTime",
    width: "160",
  },
  {
    key: 18,
    label: "操作",
    visible: true,
    slot: true,
    width: "180",
    prop: "opt",
    fixed: "right",
  },
]);
const formItems = ref([
  {
    label: "项目ID",
    prop: "projectId",
    type: "select",
    placeholder: "请选择项目ID",
    options: [],
  },
  {
    label: "产品类型",
    prop: "productType",
    type: "select",
    placeholder: "请选择产品类型",
    options: [],
  },
  {
    label: "资产转让方",
    prop: "transferor",
    type: "input",
    placeholder: "请输入资产转让方",
  },
  {
    label: "债权总金额",
    prop: "debtTotalAmount",
    type: "input",
    placeholder: "请输入债权总金额(元)",
  },
  {
    label: "债权本金",
    prop: "debtPrincipal",
    type: "input",
    placeholder: "请输入债权本金(元)",
  },
  {
    label: "立项人",
    prop: "projectInitiator",
    type: "input",
    placeholder: "请输入立项人",
  },
  {
    label: "立项时间",
    prop: "projectInitiationTime",
    type: "date",
    placeholder: "请选择立项时间",
  },
  {
    label: "预计竞价日期",
    prop: "expectedAuctionDate",
    type: "date",
    placeholder: "请选择预计竞价日期",
  },
]);

//获取列表business
function getList() {
  let res = SchemeApprovalTable.getTableList();
  console.log(res);
  dataList.value = res.data;
  total.value = res.total;
  loading.value = false;
  // let query = JSON.parse(JSON.stringify(queryParams.value));
  // query.agencyId = store.state.user.userId;
  // getMyPolicy(query).then((res) => {
  //   dataList.value = res.rows;
  //   total.value = res.total;
  // });
  // loading.value = false;
}

const formatCommissionRate = (row) => {
  if (row.caseNumber === 1) {
    return row.smallCommissionRate ? `${row.smallCommissionRate}%` : "-";
  } else {
    return row.commissionRate ? `${row.commissionRate}` : "-";
  }
};
const handleSearch = (formData) => {
  queryParams.value = { ...queryParams.value, ...formData };
  handleQuery();
};

const handleReset = () => {
  formRef.value.formRef.resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  resetQuery();
};
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.category = undefined;
  queryParams.value.teamLevelType = undefined;
  lawyerOptions.value = [];
  handleQuery();
}

function handleDetails(row) {
  const query = { path: route.path, pageType: 'schemeApproval', progressStatus: 1 }
  router.push({ path: `/dueDiligence/projectInfo`, query })
}
// 下载保单
const handleDownload = (row) => {
  if (row.warrantyUrl) {
    window.open(row.warrantyUrl, "_blank");
  } else {
    proxy.$modal.msgError("暂无保单,请稍后再尝试");
  }
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;

  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;

    .check-icon {
      font-size: 34px;
    }
  }

  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}
</style>
