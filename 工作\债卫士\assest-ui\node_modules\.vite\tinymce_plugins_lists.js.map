{"version": 3, "sources": ["../tinymce/plugins/lists/plugin.js", "../tinymce/plugins/lists/index.js", "dep:tinymce_plugins_lists"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$7 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType$1 = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var isString = isType$1('string');\n    var isObject = isType$1('object');\n    var isArray = isType$1('array');\n    var isBoolean = isSimpleType('boolean');\n    var isFunction = isSimpleType('function');\n    var isNumber = isSimpleType('number');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var tripleEquals = function (a, b) {\n      return a === b;\n    };\n    var not = function (f) {\n      return function (t) {\n        return !f(t);\n      };\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var nativeSlice = Array.prototype.slice;\n    var nativePush = Array.prototype.push;\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each$1 = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var filter$1 = function (xs, pred) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    var groupBy = function (xs, f) {\n      if (xs.length === 0) {\n        return [];\n      } else {\n        var wasType = f(xs[0]);\n        var r = [];\n        var group = [];\n        for (var i = 0, len = xs.length; i < len; i++) {\n          var x = xs[i];\n          var type = f(x);\n          if (type !== wasType) {\n            r.push(group);\n            group = [];\n          }\n          wasType = type;\n          group.push(x);\n        }\n        if (group.length !== 0) {\n          r.push(group);\n        }\n        return r;\n      }\n    };\n    var foldl = function (xs, f, acc) {\n      each$1(xs, function (x, i) {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    var findUntil = function (xs, pred, until) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    var find$1 = function (xs, pred) {\n      return findUntil(xs, pred, never);\n    };\n    var flatten = function (xs) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    var bind = function (xs, f) {\n      return flatten(map(xs, f));\n    };\n    var reverse = function (xs) {\n      var r = nativeSlice.call(xs, 0);\n      r.reverse();\n      return r;\n    };\n    var get$1 = function (xs, i) {\n      return i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    };\n    var head = function (xs) {\n      return get$1(xs, 0);\n    };\n    var last = function (xs) {\n      return get$1(xs, xs.length - 1);\n    };\n    var findMap = function (arr, f) {\n      for (var i = 0; i < arr.length; i++) {\n        var r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n    function __spreadArray(to, from, pack) {\n      if (pack || arguments.length === 2)\n        for (var i = 0, l = from.length, ar; i < l; i++) {\n          if (ar || !(i in from)) {\n            if (!ar)\n              ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n          }\n        }\n      return to.concat(ar || Array.prototype.slice.call(from));\n    }\n\n    var cached = function (f) {\n      var called = false;\n      var r;\n      return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        if (!called) {\n          called = true;\n          r = f.apply(null, args);\n        }\n        return r;\n      };\n    };\n\n    var DeviceType = function (os, browser, userAgent, mediaMatch) {\n      var isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;\n      var isiPhone = os.isiOS() && !isiPad;\n      var isMobile = os.isiOS() || os.isAndroid();\n      var isTouch = isMobile || mediaMatch('(pointer:coarse)');\n      var isTablet = isiPad || !isiPhone && isMobile && mediaMatch('(min-device-width:768px)');\n      var isPhone = isiPhone || isMobile && !isTablet;\n      var iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;\n      var isDesktop = !isPhone && !isTablet && !iOSwebview;\n      return {\n        isiPad: constant(isiPad),\n        isiPhone: constant(isiPhone),\n        isTablet: constant(isTablet),\n        isPhone: constant(isPhone),\n        isTouch: constant(isTouch),\n        isAndroid: os.isAndroid,\n        isiOS: os.isiOS,\n        isWebView: constant(iOSwebview),\n        isDesktop: constant(isDesktop)\n      };\n    };\n\n    var firstMatch = function (regexes, s) {\n      for (var i = 0; i < regexes.length; i++) {\n        var x = regexes[i];\n        if (x.test(s)) {\n          return x;\n        }\n      }\n      return undefined;\n    };\n    var find = function (regexes, agent) {\n      var r = firstMatch(regexes, agent);\n      if (!r) {\n        return {\n          major: 0,\n          minor: 0\n        };\n      }\n      var group = function (i) {\n        return Number(agent.replace(r, '$' + i));\n      };\n      return nu$2(group(1), group(2));\n    };\n    var detect$3 = function (versionRegexes, agent) {\n      var cleanedAgent = String(agent).toLowerCase();\n      if (versionRegexes.length === 0) {\n        return unknown$2();\n      }\n      return find(versionRegexes, cleanedAgent);\n    };\n    var unknown$2 = function () {\n      return nu$2(0, 0);\n    };\n    var nu$2 = function (major, minor) {\n      return {\n        major: major,\n        minor: minor\n      };\n    };\n    var Version = {\n      nu: nu$2,\n      detect: detect$3,\n      unknown: unknown$2\n    };\n\n    var detectBrowser$1 = function (browsers, userAgentData) {\n      return findMap(userAgentData.brands, function (uaBrand) {\n        var lcBrand = uaBrand.brand.toLowerCase();\n        return find$1(browsers, function (browser) {\n          var _a;\n          return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase());\n        }).map(function (info) {\n          return {\n            current: info.name,\n            version: Version.nu(parseInt(uaBrand.version, 10), 0)\n          };\n        });\n      });\n    };\n\n    var detect$2 = function (candidates, userAgent) {\n      var agent = String(userAgent).toLowerCase();\n      return find$1(candidates, function (candidate) {\n        return candidate.search(agent);\n      });\n    };\n    var detectBrowser = function (browsers, userAgent) {\n      return detect$2(browsers, userAgent).map(function (browser) {\n        var version = Version.detect(browser.versionRegexes, userAgent);\n        return {\n          current: browser.name,\n          version: version\n        };\n      });\n    };\n    var detectOs = function (oses, userAgent) {\n      return detect$2(oses, userAgent).map(function (os) {\n        var version = Version.detect(os.versionRegexes, userAgent);\n        return {\n          current: os.name,\n          version: version\n        };\n      });\n    };\n\n    var contains$1 = function (str, substr) {\n      return str.indexOf(substr) !== -1;\n    };\n    var blank = function (r) {\n      return function (s) {\n        return s.replace(r, '');\n      };\n    };\n    var trim = blank(/^\\s+|\\s+$/g);\n    var isNotEmpty = function (s) {\n      return s.length > 0;\n    };\n    var isEmpty$1 = function (s) {\n      return !isNotEmpty(s);\n    };\n\n    var normalVersionRegex = /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/;\n    var checkContains = function (target) {\n      return function (uastring) {\n        return contains$1(uastring, target);\n      };\n    };\n    var browsers = [\n      {\n        name: 'Edge',\n        versionRegexes: [/.*?edge\\/ ?([0-9]+)\\.([0-9]+)$/],\n        search: function (uastring) {\n          return contains$1(uastring, 'edge/') && contains$1(uastring, 'chrome') && contains$1(uastring, 'safari') && contains$1(uastring, 'applewebkit');\n        }\n      },\n      {\n        name: 'Chrome',\n        brand: 'Chromium',\n        versionRegexes: [\n          /.*?chrome\\/([0-9]+)\\.([0-9]+).*/,\n          normalVersionRegex\n        ],\n        search: function (uastring) {\n          return contains$1(uastring, 'chrome') && !contains$1(uastring, 'chromeframe');\n        }\n      },\n      {\n        name: 'IE',\n        versionRegexes: [\n          /.*?msie\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*?rv:([0-9]+)\\.([0-9]+).*/\n        ],\n        search: function (uastring) {\n          return contains$1(uastring, 'msie') || contains$1(uastring, 'trident');\n        }\n      },\n      {\n        name: 'Opera',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?opera\\/([0-9]+)\\.([0-9]+).*/\n        ],\n        search: checkContains('opera')\n      },\n      {\n        name: 'Firefox',\n        versionRegexes: [/.*?firefox\\/\\ ?([0-9]+)\\.([0-9]+).*/],\n        search: checkContains('firefox')\n      },\n      {\n        name: 'Safari',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?cpu os ([0-9]+)_([0-9]+).*/\n        ],\n        search: function (uastring) {\n          return (contains$1(uastring, 'safari') || contains$1(uastring, 'mobile/')) && contains$1(uastring, 'applewebkit');\n        }\n      }\n    ];\n    var oses = [\n      {\n        name: 'Windows',\n        search: checkContains('win'),\n        versionRegexes: [/.*?windows\\ nt\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'iOS',\n        search: function (uastring) {\n          return contains$1(uastring, 'iphone') || contains$1(uastring, 'ipad');\n        },\n        versionRegexes: [\n          /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*cpu os ([0-9]+)_([0-9]+).*/,\n          /.*cpu iphone os ([0-9]+)_([0-9]+).*/\n        ]\n      },\n      {\n        name: 'Android',\n        search: checkContains('android'),\n        versionRegexes: [/.*?android\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'OSX',\n        search: checkContains('mac os x'),\n        versionRegexes: [/.*?mac\\ os\\ x\\ ?([0-9]+)_([0-9]+).*/]\n      },\n      {\n        name: 'Linux',\n        search: checkContains('linux'),\n        versionRegexes: []\n      },\n      {\n        name: 'Solaris',\n        search: checkContains('sunos'),\n        versionRegexes: []\n      },\n      {\n        name: 'FreeBSD',\n        search: checkContains('freebsd'),\n        versionRegexes: []\n      },\n      {\n        name: 'ChromeOS',\n        search: checkContains('cros'),\n        versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/]\n      }\n    ];\n    var PlatformInfo = {\n      browsers: constant(browsers),\n      oses: constant(oses)\n    };\n\n    var edge = 'Edge';\n    var chrome = 'Chrome';\n    var ie = 'IE';\n    var opera = 'Opera';\n    var firefox = 'Firefox';\n    var safari = 'Safari';\n    var unknown$1 = function () {\n      return nu$1({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    var nu$1 = function (info) {\n      var current = info.current;\n      var version = info.version;\n      var isBrowser = function (name) {\n        return function () {\n          return current === name;\n        };\n      };\n      return {\n        current: current,\n        version: version,\n        isEdge: isBrowser(edge),\n        isChrome: isBrowser(chrome),\n        isIE: isBrowser(ie),\n        isOpera: isBrowser(opera),\n        isFirefox: isBrowser(firefox),\n        isSafari: isBrowser(safari)\n      };\n    };\n    var Browser = {\n      unknown: unknown$1,\n      nu: nu$1,\n      edge: constant(edge),\n      chrome: constant(chrome),\n      ie: constant(ie),\n      opera: constant(opera),\n      firefox: constant(firefox),\n      safari: constant(safari)\n    };\n\n    var windows = 'Windows';\n    var ios = 'iOS';\n    var android = 'Android';\n    var linux = 'Linux';\n    var osx = 'OSX';\n    var solaris = 'Solaris';\n    var freebsd = 'FreeBSD';\n    var chromeos = 'ChromeOS';\n    var unknown = function () {\n      return nu({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    var nu = function (info) {\n      var current = info.current;\n      var version = info.version;\n      var isOS = function (name) {\n        return function () {\n          return current === name;\n        };\n      };\n      return {\n        current: current,\n        version: version,\n        isWindows: isOS(windows),\n        isiOS: isOS(ios),\n        isAndroid: isOS(android),\n        isOSX: isOS(osx),\n        isLinux: isOS(linux),\n        isSolaris: isOS(solaris),\n        isFreeBSD: isOS(freebsd),\n        isChromeOS: isOS(chromeos)\n      };\n    };\n    var OperatingSystem = {\n      unknown: unknown,\n      nu: nu,\n      windows: constant(windows),\n      ios: constant(ios),\n      android: constant(android),\n      linux: constant(linux),\n      osx: constant(osx),\n      solaris: constant(solaris),\n      freebsd: constant(freebsd),\n      chromeos: constant(chromeos)\n    };\n\n    var detect$1 = function (userAgent, userAgentDataOpt, mediaMatch) {\n      var browsers = PlatformInfo.browsers();\n      var oses = PlatformInfo.oses();\n      var browser = userAgentDataOpt.bind(function (userAgentData) {\n        return detectBrowser$1(browsers, userAgentData);\n      }).orThunk(function () {\n        return detectBrowser(browsers, userAgent);\n      }).fold(Browser.unknown, Browser.nu);\n      var os = detectOs(oses, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);\n      var deviceType = DeviceType(os, browser, userAgent, mediaMatch);\n      return {\n        browser: browser,\n        os: os,\n        deviceType: deviceType\n      };\n    };\n    var PlatformDetection = { detect: detect$1 };\n\n    var mediaMatch = function (query) {\n      return window.matchMedia(query).matches;\n    };\n    var platform = cached(function () {\n      return PlatformDetection.detect(navigator.userAgent, Optional.from(navigator.userAgentData), mediaMatch);\n    });\n    var detect = function () {\n      return platform();\n    };\n\n    var compareDocumentPosition = function (a, b, match) {\n      return (a.compareDocumentPosition(b) & match) !== 0;\n    };\n    var documentPositionContainedBy = function (a, b) {\n      return compareDocumentPosition(a, b, Node.DOCUMENT_POSITION_CONTAINED_BY);\n    };\n\n    var ELEMENT = 1;\n\n    var fromHtml = function (html, scope) {\n      var doc = scope || document;\n      var div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        console.error('HTML does not have a single root node', html);\n        throw new Error('HTML must have a single root node');\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    var fromTag = function (tag, scope) {\n      var doc = scope || document;\n      var node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    var fromText = function (text, scope) {\n      var doc = scope || document;\n      var node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    var fromDom = function (node) {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    var fromPoint = function (docElm, x, y) {\n      return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    };\n    var SugarElement = {\n      fromHtml: fromHtml,\n      fromTag: fromTag,\n      fromText: fromText,\n      fromDom: fromDom,\n      fromPoint: fromPoint\n    };\n\n    var is$2 = function (element, selector) {\n      var dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        var elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n\n    var eq = function (e1, e2) {\n      return e1.dom === e2.dom;\n    };\n    var regularContains = function (e1, e2) {\n      var d1 = e1.dom;\n      var d2 = e2.dom;\n      return d1 === d2 ? false : d1.contains(d2);\n    };\n    var ieContains = function (e1, e2) {\n      return documentPositionContainedBy(e1.dom, e2.dom);\n    };\n    var contains = function (e1, e2) {\n      return detect().browser.isIE() ? ieContains(e1, e2) : regularContains(e1, e2);\n    };\n    var is$1 = is$2;\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.dom.RangeUtils');\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    var keys = Object.keys;\n    var each = function (obj, f) {\n      var props = keys(obj);\n      for (var k = 0, len = props.length; k < len; k++) {\n        var i = props[k];\n        var x = obj[i];\n        f(x, i);\n      }\n    };\n    var objAcc = function (r) {\n      return function (x, i) {\n        r[i] = x;\n      };\n    };\n    var internalFilter = function (obj, pred, onTrue, onFalse) {\n      var r = {};\n      each(obj, function (x, i) {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n      return r;\n    };\n    var filter = function (obj, pred) {\n      var t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var name = function (element) {\n      var r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    var type = function (element) {\n      return element.dom.nodeType;\n    };\n    var isType = function (t) {\n      return function (element) {\n        return type(element) === t;\n      };\n    };\n    var isElement = isType(ELEMENT);\n    var isTag = function (tag) {\n      return function (e) {\n        return isElement(e) && name(e) === tag;\n      };\n    };\n\n    var rawSet = function (dom, key, value) {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    var setAll = function (element, attrs) {\n      var dom = element.dom;\n      each(attrs, function (v, k) {\n        rawSet(dom, k, v);\n      });\n    };\n    var clone$1 = function (element) {\n      return foldl(element.dom.attributes, function (acc, attr) {\n        acc[attr.name] = attr.value;\n        return acc;\n      }, {});\n    };\n\n    var parent = function (element) {\n      return Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    };\n    var children = function (element) {\n      return map(element.dom.childNodes, SugarElement.fromDom);\n    };\n    var child = function (element, index) {\n      var cs = element.dom.childNodes;\n      return Optional.from(cs[index]).map(SugarElement.fromDom);\n    };\n    var firstChild = function (element) {\n      return child(element, 0);\n    };\n    var lastChild = function (element) {\n      return child(element, element.dom.childNodes.length - 1);\n    };\n\n    var before$1 = function (marker, element) {\n      var parent$1 = parent(marker);\n      parent$1.each(function (v) {\n        v.dom.insertBefore(element.dom, marker.dom);\n      });\n    };\n    var append$1 = function (parent, element) {\n      parent.dom.appendChild(element.dom);\n    };\n\n    var before = function (marker, elements) {\n      each$1(elements, function (x) {\n        before$1(marker, x);\n      });\n    };\n    var append = function (parent, elements) {\n      each$1(elements, function (x) {\n        append$1(parent, x);\n      });\n    };\n\n    var remove = function (element) {\n      var dom = element.dom;\n      if (dom.parentNode !== null) {\n        dom.parentNode.removeChild(dom);\n      }\n    };\n\n    var clone = function (original, isDeep) {\n      return SugarElement.fromDom(original.dom.cloneNode(isDeep));\n    };\n    var deep = function (original) {\n      return clone(original, true);\n    };\n    var shallowAs = function (original, tag) {\n      var nu = SugarElement.fromTag(tag);\n      var attributes = clone$1(original);\n      setAll(nu, attributes);\n      return nu;\n    };\n    var mutate = function (original, tag) {\n      var nu = shallowAs(original, tag);\n      before$1(original, nu);\n      var children$1 = children(original);\n      append(nu, children$1);\n      remove(original);\n      return nu;\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var matchNodeName = function (name) {\n      return function (node) {\n        return node && node.nodeName.toLowerCase() === name;\n      };\n    };\n    var matchNodeNames = function (regex) {\n      return function (node) {\n        return node && regex.test(node.nodeName);\n      };\n    };\n    var isTextNode = function (node) {\n      return node && node.nodeType === 3;\n    };\n    var isListNode = matchNodeNames(/^(OL|UL|DL)$/);\n    var isOlUlNode = matchNodeNames(/^(OL|UL)$/);\n    var isOlNode = matchNodeName('ol');\n    var isListItemNode = matchNodeNames(/^(LI|DT|DD)$/);\n    var isDlItemNode = matchNodeNames(/^(DT|DD)$/);\n    var isTableCellNode = matchNodeNames(/^(TH|TD)$/);\n    var isBr = matchNodeName('br');\n    var isFirstChild = function (node) {\n      return node.parentNode.firstChild === node;\n    };\n    var isTextBlock = function (editor, node) {\n      return node && !!editor.schema.getTextBlockElements()[node.nodeName];\n    };\n    var isBlock = function (node, blockElements) {\n      return node && node.nodeName in blockElements;\n    };\n    var isBogusBr = function (dom, node) {\n      if (!isBr(node)) {\n        return false;\n      }\n      return dom.isBlock(node.nextSibling) && !isBr(node.previousSibling);\n    };\n    var isEmpty = function (dom, elm, keepBookmarks) {\n      var empty = dom.isEmpty(elm);\n      if (keepBookmarks && dom.select('span[data-mce-type=bookmark]', elm).length > 0) {\n        return false;\n      }\n      return empty;\n    };\n    var isChildOfBody = function (dom, elm) {\n      return dom.isChildOf(elm, dom.getRoot());\n    };\n\n    var shouldIndentOnTab = function (editor) {\n      return editor.getParam('lists_indent_on_tab', true);\n    };\n    var getForcedRootBlock = function (editor) {\n      var block = editor.getParam('forced_root_block', 'p');\n      if (block === false) {\n        return '';\n      } else if (block === true) {\n        return 'p';\n      } else {\n        return block;\n      }\n    };\n    var getForcedRootBlockAttrs = function (editor) {\n      return editor.getParam('forced_root_block_attrs', {});\n    };\n\n    var createTextBlock = function (editor, contentNode) {\n      var dom = editor.dom;\n      var blockElements = editor.schema.getBlockElements();\n      var fragment = dom.createFragment();\n      var blockName = getForcedRootBlock(editor);\n      var node, textBlock, hasContentNode;\n      if (blockName) {\n        textBlock = dom.create(blockName);\n        if (textBlock.tagName === blockName.toUpperCase()) {\n          dom.setAttribs(textBlock, getForcedRootBlockAttrs(editor));\n        }\n        if (!isBlock(contentNode.firstChild, blockElements)) {\n          fragment.appendChild(textBlock);\n        }\n      }\n      if (contentNode) {\n        while (node = contentNode.firstChild) {\n          var nodeName = node.nodeName;\n          if (!hasContentNode && (nodeName !== 'SPAN' || node.getAttribute('data-mce-type') !== 'bookmark')) {\n            hasContentNode = true;\n          }\n          if (isBlock(node, blockElements)) {\n            fragment.appendChild(node);\n            textBlock = null;\n          } else {\n            if (blockName) {\n              if (!textBlock) {\n                textBlock = dom.create(blockName);\n                fragment.appendChild(textBlock);\n              }\n              textBlock.appendChild(node);\n            } else {\n              fragment.appendChild(node);\n            }\n          }\n        }\n      }\n      if (!blockName) {\n        fragment.appendChild(dom.create('br'));\n      } else {\n        if (!hasContentNode) {\n          textBlock.appendChild(dom.create('br', { 'data-mce-bogus': '1' }));\n        }\n      }\n      return fragment;\n    };\n\n    var DOM$2 = global$3.DOM;\n    var splitList = function (editor, list, li) {\n      var removeAndKeepBookmarks = function (targetNode) {\n        global$2.each(bookmarks, function (node) {\n          targetNode.parentNode.insertBefore(node, li.parentNode);\n        });\n        DOM$2.remove(targetNode);\n      };\n      var bookmarks = DOM$2.select('span[data-mce-type=\"bookmark\"]', list);\n      var newBlock = createTextBlock(editor, li);\n      var tmpRng = DOM$2.createRng();\n      tmpRng.setStartAfter(li);\n      tmpRng.setEndAfter(list);\n      var fragment = tmpRng.extractContents();\n      for (var node = fragment.firstChild; node; node = node.firstChild) {\n        if (node.nodeName === 'LI' && editor.dom.isEmpty(node)) {\n          DOM$2.remove(node);\n          break;\n        }\n      }\n      if (!editor.dom.isEmpty(fragment)) {\n        DOM$2.insertAfter(fragment, list);\n      }\n      DOM$2.insertAfter(newBlock, list);\n      if (isEmpty(editor.dom, li.parentNode)) {\n        removeAndKeepBookmarks(li.parentNode);\n      }\n      DOM$2.remove(li);\n      if (isEmpty(editor.dom, list)) {\n        DOM$2.remove(list);\n      }\n    };\n\n    var isDescriptionDetail = isTag('dd');\n    var isDescriptionTerm = isTag('dt');\n    var outdentDlItem = function (editor, item) {\n      if (isDescriptionDetail(item)) {\n        mutate(item, 'dt');\n      } else if (isDescriptionTerm(item)) {\n        parent(item).each(function (dl) {\n          return splitList(editor, dl.dom, item.dom);\n        });\n      }\n    };\n    var indentDlItem = function (item) {\n      if (isDescriptionTerm(item)) {\n        mutate(item, 'dd');\n      }\n    };\n    var dlIndentation = function (editor, indentation, dlItems) {\n      if (indentation === 'Indent') {\n        each$1(dlItems, indentDlItem);\n      } else {\n        each$1(dlItems, function (item) {\n          return outdentDlItem(editor, item);\n        });\n      }\n    };\n\n    var getNormalizedPoint = function (container, offset) {\n      if (isTextNode(container)) {\n        return {\n          container: container,\n          offset: offset\n        };\n      }\n      var node = global$6.getNode(container, offset);\n      if (isTextNode(node)) {\n        return {\n          container: node,\n          offset: offset >= container.childNodes.length ? node.data.length : 0\n        };\n      } else if (node.previousSibling && isTextNode(node.previousSibling)) {\n        return {\n          container: node.previousSibling,\n          offset: node.previousSibling.data.length\n        };\n      } else if (node.nextSibling && isTextNode(node.nextSibling)) {\n        return {\n          container: node.nextSibling,\n          offset: 0\n        };\n      }\n      return {\n        container: container,\n        offset: offset\n      };\n    };\n    var normalizeRange = function (rng) {\n      var outRng = rng.cloneRange();\n      var rangeStart = getNormalizedPoint(rng.startContainer, rng.startOffset);\n      outRng.setStart(rangeStart.container, rangeStart.offset);\n      var rangeEnd = getNormalizedPoint(rng.endContainer, rng.endOffset);\n      outRng.setEnd(rangeEnd.container, rangeEnd.offset);\n      return outRng;\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.DomQuery');\n\n    var getParentList = function (editor, node) {\n      var selectionStart = node || editor.selection.getStart(true);\n      return editor.dom.getParent(selectionStart, 'OL,UL,DL', getClosestListRootElm(editor, selectionStart));\n    };\n    var isParentListSelected = function (parentList, selectedBlocks) {\n      return parentList && selectedBlocks.length === 1 && selectedBlocks[0] === parentList;\n    };\n    var findSubLists = function (parentList) {\n      return filter$1(parentList.querySelectorAll('ol,ul,dl'), isListNode);\n    };\n    var getSelectedSubLists = function (editor) {\n      var parentList = getParentList(editor);\n      var selectedBlocks = editor.selection.getSelectedBlocks();\n      if (isParentListSelected(parentList, selectedBlocks)) {\n        return findSubLists(parentList);\n      } else {\n        return filter$1(selectedBlocks, function (elm) {\n          return isListNode(elm) && parentList !== elm;\n        });\n      }\n    };\n    var findParentListItemsNodes = function (editor, elms) {\n      var listItemsElms = global$2.map(elms, function (elm) {\n        var parentLi = editor.dom.getParent(elm, 'li,dd,dt', getClosestListRootElm(editor, elm));\n        return parentLi ? parentLi : elm;\n      });\n      return global$1.unique(listItemsElms);\n    };\n    var getSelectedListItems = function (editor) {\n      var selectedBlocks = editor.selection.getSelectedBlocks();\n      return filter$1(findParentListItemsNodes(editor, selectedBlocks), isListItemNode);\n    };\n    var getSelectedDlItems = function (editor) {\n      return filter$1(getSelectedListItems(editor), isDlItemNode);\n    };\n    var getClosestListRootElm = function (editor, elm) {\n      var parentTableCell = editor.dom.getParents(elm, 'TD,TH');\n      return parentTableCell.length > 0 ? parentTableCell[0] : editor.getBody();\n    };\n    var findLastParentListNode = function (editor, elm) {\n      var parentLists = editor.dom.getParents(elm, 'ol,ul', getClosestListRootElm(editor, elm));\n      return last(parentLists);\n    };\n    var getSelectedLists = function (editor) {\n      var firstList = findLastParentListNode(editor, editor.selection.getStart());\n      var subsequentLists = filter$1(editor.selection.getSelectedBlocks(), isOlUlNode);\n      return firstList.toArray().concat(subsequentLists);\n    };\n    var getSelectedListRoots = function (editor) {\n      var selectedLists = getSelectedLists(editor);\n      return getUniqueListRoots(editor, selectedLists);\n    };\n    var getUniqueListRoots = function (editor, lists) {\n      var listRoots = map(lists, function (list) {\n        return findLastParentListNode(editor, list).getOr(list);\n      });\n      return global$1.unique(listRoots);\n    };\n\n    var is = function (lhs, rhs, comparator) {\n      if (comparator === void 0) {\n        comparator = tripleEquals;\n      }\n      return lhs.exists(function (left) {\n        return comparator(left, rhs);\n      });\n    };\n    var lift2 = function (oa, ob, f) {\n      return oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();\n    };\n\n    var fromElements = function (elements, scope) {\n      var doc = scope || document;\n      var fragment = doc.createDocumentFragment();\n      each$1(elements, function (element) {\n        fragment.appendChild(element.dom);\n      });\n      return SugarElement.fromDom(fragment);\n    };\n\n    var fireListEvent = function (editor, action, element) {\n      return editor.fire('ListMutation', {\n        action: action,\n        element: element\n      });\n    };\n\n    var isSupported = function (dom) {\n      return dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n    };\n\n    var internalSet = function (dom, property, value) {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    var set = function (element, property, value) {\n      var dom = element.dom;\n      internalSet(dom, property, value);\n    };\n\n    var joinSegment = function (parent, child) {\n      append$1(parent.item, child.list);\n    };\n    var joinSegments = function (segments) {\n      for (var i = 1; i < segments.length; i++) {\n        joinSegment(segments[i - 1], segments[i]);\n      }\n    };\n    var appendSegments = function (head$1, tail) {\n      lift2(last(head$1), head(tail), joinSegment);\n    };\n    var createSegment = function (scope, listType) {\n      var segment = {\n        list: SugarElement.fromTag(listType, scope),\n        item: SugarElement.fromTag('li', scope)\n      };\n      append$1(segment.list, segment.item);\n      return segment;\n    };\n    var createSegments = function (scope, entry, size) {\n      var segments = [];\n      for (var i = 0; i < size; i++) {\n        segments.push(createSegment(scope, entry.listType));\n      }\n      return segments;\n    };\n    var populateSegments = function (segments, entry) {\n      for (var i = 0; i < segments.length - 1; i++) {\n        set(segments[i].item, 'list-style-type', 'none');\n      }\n      last(segments).each(function (segment) {\n        setAll(segment.list, entry.listAttributes);\n        setAll(segment.item, entry.itemAttributes);\n        append(segment.item, entry.content);\n      });\n    };\n    var normalizeSegment = function (segment, entry) {\n      if (name(segment.list) !== entry.listType) {\n        segment.list = mutate(segment.list, entry.listType);\n      }\n      setAll(segment.list, entry.listAttributes);\n    };\n    var createItem = function (scope, attr, content) {\n      var item = SugarElement.fromTag('li', scope);\n      setAll(item, attr);\n      append(item, content);\n      return item;\n    };\n    var appendItem = function (segment, item) {\n      append$1(segment.list, item);\n      segment.item = item;\n    };\n    var writeShallow = function (scope, cast, entry) {\n      var newCast = cast.slice(0, entry.depth);\n      last(newCast).each(function (segment) {\n        var item = createItem(scope, entry.itemAttributes, entry.content);\n        appendItem(segment, item);\n        normalizeSegment(segment, entry);\n      });\n      return newCast;\n    };\n    var writeDeep = function (scope, cast, entry) {\n      var segments = createSegments(scope, entry, entry.depth - cast.length);\n      joinSegments(segments);\n      populateSegments(segments, entry);\n      appendSegments(cast, segments);\n      return cast.concat(segments);\n    };\n    var composeList = function (scope, entries) {\n      var cast = foldl(entries, function (cast, entry) {\n        return entry.depth > cast.length ? writeDeep(scope, cast, entry) : writeShallow(scope, cast, entry);\n      }, []);\n      return head(cast).map(function (segment) {\n        return segment.list;\n      });\n    };\n\n    var isList = function (el) {\n      return is$1(el, 'OL,UL');\n    };\n    var hasFirstChildList = function (el) {\n      return firstChild(el).exists(isList);\n    };\n    var hasLastChildList = function (el) {\n      return lastChild(el).exists(isList);\n    };\n\n    var isIndented = function (entry) {\n      return entry.depth > 0;\n    };\n    var isSelected = function (entry) {\n      return entry.isSelected;\n    };\n    var cloneItemContent = function (li) {\n      var children$1 = children(li);\n      var content = hasLastChildList(li) ? children$1.slice(0, -1) : children$1;\n      return map(content, deep);\n    };\n    var createEntry = function (li, depth, isSelected) {\n      return parent(li).filter(isElement).map(function (list) {\n        return {\n          depth: depth,\n          dirty: false,\n          isSelected: isSelected,\n          content: cloneItemContent(li),\n          itemAttributes: clone$1(li),\n          listAttributes: clone$1(list),\n          listType: name(list)\n        };\n      });\n    };\n\n    var indentEntry = function (indentation, entry) {\n      switch (indentation) {\n      case 'Indent':\n        entry.depth++;\n        break;\n      case 'Outdent':\n        entry.depth--;\n        break;\n      case 'Flatten':\n        entry.depth = 0;\n      }\n      entry.dirty = true;\n    };\n\n    var cloneListProperties = function (target, source) {\n      target.listType = source.listType;\n      target.listAttributes = __assign({}, source.listAttributes);\n    };\n    var cleanListProperties = function (entry) {\n      entry.listAttributes = filter(entry.listAttributes, function (_value, key) {\n        return key !== 'start';\n      });\n    };\n    var closestSiblingEntry = function (entries, start) {\n      var depth = entries[start].depth;\n      var matches = function (entry) {\n        return entry.depth === depth && !entry.dirty;\n      };\n      var until = function (entry) {\n        return entry.depth < depth;\n      };\n      return findUntil(reverse(entries.slice(0, start)), matches, until).orThunk(function () {\n        return findUntil(entries.slice(start + 1), matches, until);\n      });\n    };\n    var normalizeEntries = function (entries) {\n      each$1(entries, function (entry, i) {\n        closestSiblingEntry(entries, i).fold(function () {\n          if (entry.dirty) {\n            cleanListProperties(entry);\n          }\n        }, function (matchingEntry) {\n          return cloneListProperties(entry, matchingEntry);\n        });\n      });\n      return entries;\n    };\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var parseItem = function (depth, itemSelection, selectionState, item) {\n      return firstChild(item).filter(isList).fold(function () {\n        itemSelection.each(function (selection) {\n          if (eq(selection.start, item)) {\n            selectionState.set(true);\n          }\n        });\n        var currentItemEntry = createEntry(item, depth, selectionState.get());\n        itemSelection.each(function (selection) {\n          if (eq(selection.end, item)) {\n            selectionState.set(false);\n          }\n        });\n        var childListEntries = lastChild(item).filter(isList).map(function (list) {\n          return parseList(depth, itemSelection, selectionState, list);\n        }).getOr([]);\n        return currentItemEntry.toArray().concat(childListEntries);\n      }, function (list) {\n        return parseList(depth, itemSelection, selectionState, list);\n      });\n    };\n    var parseList = function (depth, itemSelection, selectionState, list) {\n      return bind(children(list), function (element) {\n        var parser = isList(element) ? parseList : parseItem;\n        var newDepth = depth + 1;\n        return parser(newDepth, itemSelection, selectionState, element);\n      });\n    };\n    var parseLists = function (lists, itemSelection) {\n      var selectionState = Cell(false);\n      var initialDepth = 0;\n      return map(lists, function (list) {\n        return {\n          sourceList: list,\n          entries: parseList(initialDepth, itemSelection, selectionState, list)\n        };\n      });\n    };\n\n    var outdentedComposer = function (editor, entries) {\n      var normalizedEntries = normalizeEntries(entries);\n      return map(normalizedEntries, function (entry) {\n        var content = fromElements(entry.content);\n        return SugarElement.fromDom(createTextBlock(editor, content.dom));\n      });\n    };\n    var indentedComposer = function (editor, entries) {\n      var normalizedEntries = normalizeEntries(entries);\n      return composeList(editor.contentDocument, normalizedEntries).toArray();\n    };\n    var composeEntries = function (editor, entries) {\n      return bind(groupBy(entries, isIndented), function (entries) {\n        var groupIsIndented = head(entries).exists(isIndented);\n        return groupIsIndented ? indentedComposer(editor, entries) : outdentedComposer(editor, entries);\n      });\n    };\n    var indentSelectedEntries = function (entries, indentation) {\n      each$1(filter$1(entries, isSelected), function (entry) {\n        return indentEntry(indentation, entry);\n      });\n    };\n    var getItemSelection = function (editor) {\n      var selectedListItems = map(getSelectedListItems(editor), SugarElement.fromDom);\n      return lift2(find$1(selectedListItems, not(hasFirstChildList)), find$1(reverse(selectedListItems), not(hasFirstChildList)), function (start, end) {\n        return {\n          start: start,\n          end: end\n        };\n      });\n    };\n    var listIndentation = function (editor, lists, indentation) {\n      var entrySets = parseLists(lists, getItemSelection(editor));\n      each$1(entrySets, function (entrySet) {\n        indentSelectedEntries(entrySet.entries, indentation);\n        var composedLists = composeEntries(editor, entrySet.entries);\n        each$1(composedLists, function (composedList) {\n          fireListEvent(editor, indentation === 'Indent' ? 'IndentList' : 'OutdentList', composedList.dom);\n        });\n        before(entrySet.sourceList, composedLists);\n        remove(entrySet.sourceList);\n      });\n    };\n\n    var selectionIndentation = function (editor, indentation) {\n      var lists = map(getSelectedListRoots(editor), SugarElement.fromDom);\n      var dlItems = map(getSelectedDlItems(editor), SugarElement.fromDom);\n      var isHandled = false;\n      if (lists.length || dlItems.length) {\n        var bookmark = editor.selection.getBookmark();\n        listIndentation(editor, lists, indentation);\n        dlIndentation(editor, indentation, dlItems);\n        editor.selection.moveToBookmark(bookmark);\n        editor.selection.setRng(normalizeRange(editor.selection.getRng()));\n        editor.nodeChanged();\n        isHandled = true;\n      }\n      return isHandled;\n    };\n    var indentListSelection = function (editor) {\n      return selectionIndentation(editor, 'Indent');\n    };\n    var outdentListSelection = function (editor) {\n      return selectionIndentation(editor, 'Outdent');\n    };\n    var flattenListSelection = function (editor) {\n      return selectionIndentation(editor, 'Flatten');\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.dom.BookmarkManager');\n\n    var DOM$1 = global$3.DOM;\n    var createBookmark = function (rng) {\n      var bookmark = {};\n      var setupEndPoint = function (start) {\n        var container = rng[start ? 'startContainer' : 'endContainer'];\n        var offset = rng[start ? 'startOffset' : 'endOffset'];\n        if (container.nodeType === 1) {\n          var offsetNode = DOM$1.create('span', { 'data-mce-type': 'bookmark' });\n          if (container.hasChildNodes()) {\n            offset = Math.min(offset, container.childNodes.length - 1);\n            if (start) {\n              container.insertBefore(offsetNode, container.childNodes[offset]);\n            } else {\n              DOM$1.insertAfter(offsetNode, container.childNodes[offset]);\n            }\n          } else {\n            container.appendChild(offsetNode);\n          }\n          container = offsetNode;\n          offset = 0;\n        }\n        bookmark[start ? 'startContainer' : 'endContainer'] = container;\n        bookmark[start ? 'startOffset' : 'endOffset'] = offset;\n      };\n      setupEndPoint(true);\n      if (!rng.collapsed) {\n        setupEndPoint();\n      }\n      return bookmark;\n    };\n    var resolveBookmark = function (bookmark) {\n      var restoreEndPoint = function (start) {\n        var node;\n        var nodeIndex = function (container) {\n          var node = container.parentNode.firstChild, idx = 0;\n          while (node) {\n            if (node === container) {\n              return idx;\n            }\n            if (node.nodeType !== 1 || node.getAttribute('data-mce-type') !== 'bookmark') {\n              idx++;\n            }\n            node = node.nextSibling;\n          }\n          return -1;\n        };\n        var container = node = bookmark[start ? 'startContainer' : 'endContainer'];\n        var offset = bookmark[start ? 'startOffset' : 'endOffset'];\n        if (!container) {\n          return;\n        }\n        if (container.nodeType === 1) {\n          offset = nodeIndex(container);\n          container = container.parentNode;\n          DOM$1.remove(node);\n          if (!container.hasChildNodes() && DOM$1.isBlock(container)) {\n            container.appendChild(DOM$1.create('br'));\n          }\n        }\n        bookmark[start ? 'startContainer' : 'endContainer'] = container;\n        bookmark[start ? 'startOffset' : 'endOffset'] = offset;\n      };\n      restoreEndPoint(true);\n      restoreEndPoint();\n      var rng = DOM$1.createRng();\n      rng.setStart(bookmark.startContainer, bookmark.startOffset);\n      if (bookmark.endContainer) {\n        rng.setEnd(bookmark.endContainer, bookmark.endOffset);\n      }\n      return normalizeRange(rng);\n    };\n\n    var listToggleActionFromListName = function (listName) {\n      switch (listName) {\n      case 'UL':\n        return 'ToggleUlList';\n      case 'OL':\n        return 'ToggleOlList';\n      case 'DL':\n        return 'ToggleDLList';\n      }\n    };\n\n    var isCustomList = function (list) {\n      return /\\btox\\-/.test(list.className);\n    };\n    var listState = function (editor, listName, activate) {\n      var nodeChangeHandler = function (e) {\n        var inList = findUntil(e.parents, isListNode, isTableCellNode).filter(function (list) {\n          return list.nodeName === listName && !isCustomList(list);\n        }).isSome();\n        activate(inList);\n      };\n      var parents = editor.dom.getParents(editor.selection.getNode());\n      nodeChangeHandler({ parents: parents });\n      editor.on('NodeChange', nodeChangeHandler);\n      return function () {\n        return editor.off('NodeChange', nodeChangeHandler);\n      };\n    };\n\n    var updateListStyle = function (dom, el, detail) {\n      var type = detail['list-style-type'] ? detail['list-style-type'] : null;\n      dom.setStyle(el, 'list-style-type', type);\n    };\n    var setAttribs = function (elm, attrs) {\n      global$2.each(attrs, function (value, key) {\n        elm.setAttribute(key, value);\n      });\n    };\n    var updateListAttrs = function (dom, el, detail) {\n      setAttribs(el, detail['list-attributes']);\n      global$2.each(dom.select('li', el), function (li) {\n        setAttribs(li, detail['list-item-attributes']);\n      });\n    };\n    var updateListWithDetails = function (dom, el, detail) {\n      updateListStyle(dom, el, detail);\n      updateListAttrs(dom, el, detail);\n    };\n    var removeStyles = function (dom, element, styles) {\n      global$2.each(styles, function (style) {\n        var _a;\n        return dom.setStyle(element, (_a = {}, _a[style] = '', _a));\n      });\n    };\n    var getEndPointNode = function (editor, rng, start, root) {\n      var container = rng[start ? 'startContainer' : 'endContainer'];\n      var offset = rng[start ? 'startOffset' : 'endOffset'];\n      if (container.nodeType === 1) {\n        container = container.childNodes[Math.min(offset, container.childNodes.length - 1)] || container;\n      }\n      if (!start && isBr(container.nextSibling)) {\n        container = container.nextSibling;\n      }\n      while (container.parentNode !== root) {\n        if (isTextBlock(editor, container)) {\n          return container;\n        }\n        if (/^(TD|TH)$/.test(container.parentNode.nodeName)) {\n          return container;\n        }\n        container = container.parentNode;\n      }\n      return container;\n    };\n    var getSelectedTextBlocks = function (editor, rng, root) {\n      var textBlocks = [];\n      var dom = editor.dom;\n      var startNode = getEndPointNode(editor, rng, true, root);\n      var endNode = getEndPointNode(editor, rng, false, root);\n      var block;\n      var siblings = [];\n      for (var node = startNode; node; node = node.nextSibling) {\n        siblings.push(node);\n        if (node === endNode) {\n          break;\n        }\n      }\n      global$2.each(siblings, function (node) {\n        if (isTextBlock(editor, node)) {\n          textBlocks.push(node);\n          block = null;\n          return;\n        }\n        if (dom.isBlock(node) || isBr(node)) {\n          if (isBr(node)) {\n            dom.remove(node);\n          }\n          block = null;\n          return;\n        }\n        var nextSibling = node.nextSibling;\n        if (global.isBookmarkNode(node)) {\n          if (isListNode(nextSibling) || isTextBlock(editor, nextSibling) || !nextSibling && node.parentNode === root) {\n            block = null;\n            return;\n          }\n        }\n        if (!block) {\n          block = dom.create('p');\n          node.parentNode.insertBefore(block, node);\n          textBlocks.push(block);\n        }\n        block.appendChild(node);\n      });\n      return textBlocks;\n    };\n    var hasCompatibleStyle = function (dom, sib, detail) {\n      var sibStyle = dom.getStyle(sib, 'list-style-type');\n      var detailStyle = detail ? detail['list-style-type'] : '';\n      detailStyle = detailStyle === null ? '' : detailStyle;\n      return sibStyle === detailStyle;\n    };\n    var applyList = function (editor, listName, detail) {\n      var rng = editor.selection.getRng();\n      var listItemName = 'LI';\n      var root = getClosestListRootElm(editor, editor.selection.getStart(true));\n      var dom = editor.dom;\n      if (dom.getContentEditable(editor.selection.getNode()) === 'false') {\n        return;\n      }\n      listName = listName.toUpperCase();\n      if (listName === 'DL') {\n        listItemName = 'DT';\n      }\n      var bookmark = createBookmark(rng);\n      var selectedTextBlocks = getSelectedTextBlocks(editor, rng, root);\n      global$2.each(selectedTextBlocks, function (block) {\n        var listBlock;\n        var sibling = block.previousSibling;\n        var parent = block.parentNode;\n        if (!isListItemNode(parent)) {\n          if (sibling && isListNode(sibling) && sibling.nodeName === listName && hasCompatibleStyle(dom, sibling, detail)) {\n            listBlock = sibling;\n            block = dom.rename(block, listItemName);\n            sibling.appendChild(block);\n          } else {\n            listBlock = dom.create(listName);\n            block.parentNode.insertBefore(listBlock, block);\n            listBlock.appendChild(block);\n            block = dom.rename(block, listItemName);\n          }\n          removeStyles(dom, block, [\n            'margin',\n            'margin-right',\n            'margin-bottom',\n            'margin-left',\n            'margin-top',\n            'padding',\n            'padding-right',\n            'padding-bottom',\n            'padding-left',\n            'padding-top'\n          ]);\n          updateListWithDetails(dom, listBlock, detail);\n          mergeWithAdjacentLists(editor.dom, listBlock);\n        }\n      });\n      editor.selection.setRng(resolveBookmark(bookmark));\n    };\n    var isValidLists = function (list1, list2) {\n      return list1 && list2 && isListNode(list1) && list1.nodeName === list2.nodeName;\n    };\n    var hasSameListStyle = function (dom, list1, list2) {\n      var targetStyle = dom.getStyle(list1, 'list-style-type', true);\n      var style = dom.getStyle(list2, 'list-style-type', true);\n      return targetStyle === style;\n    };\n    var hasSameClasses = function (elm1, elm2) {\n      return elm1.className === elm2.className;\n    };\n    var shouldMerge = function (dom, list1, list2) {\n      return isValidLists(list1, list2) && hasSameListStyle(dom, list1, list2) && hasSameClasses(list1, list2);\n    };\n    var mergeWithAdjacentLists = function (dom, listBlock) {\n      var sibling, node;\n      sibling = listBlock.nextSibling;\n      if (shouldMerge(dom, listBlock, sibling)) {\n        while (node = sibling.firstChild) {\n          listBlock.appendChild(node);\n        }\n        dom.remove(sibling);\n      }\n      sibling = listBlock.previousSibling;\n      if (shouldMerge(dom, listBlock, sibling)) {\n        while (node = sibling.lastChild) {\n          listBlock.insertBefore(node, listBlock.firstChild);\n        }\n        dom.remove(sibling);\n      }\n    };\n    var updateList$1 = function (editor, list, listName, detail) {\n      if (list.nodeName !== listName) {\n        var newList = editor.dom.rename(list, listName);\n        updateListWithDetails(editor.dom, newList, detail);\n        fireListEvent(editor, listToggleActionFromListName(listName), newList);\n      } else {\n        updateListWithDetails(editor.dom, list, detail);\n        fireListEvent(editor, listToggleActionFromListName(listName), list);\n      }\n    };\n    var toggleMultipleLists = function (editor, parentList, lists, listName, detail) {\n      var parentIsList = isListNode(parentList);\n      if (parentIsList && parentList.nodeName === listName && !hasListStyleDetail(detail)) {\n        flattenListSelection(editor);\n      } else {\n        applyList(editor, listName, detail);\n        var bookmark = createBookmark(editor.selection.getRng());\n        var allLists = parentIsList ? __spreadArray([parentList], lists, true) : lists;\n        global$2.each(allLists, function (elm) {\n          updateList$1(editor, elm, listName, detail);\n        });\n        editor.selection.setRng(resolveBookmark(bookmark));\n      }\n    };\n    var hasListStyleDetail = function (detail) {\n      return 'list-style-type' in detail;\n    };\n    var toggleSingleList = function (editor, parentList, listName, detail) {\n      if (parentList === editor.getBody()) {\n        return;\n      }\n      if (parentList) {\n        if (parentList.nodeName === listName && !hasListStyleDetail(detail) && !isCustomList(parentList)) {\n          flattenListSelection(editor);\n        } else {\n          var bookmark = createBookmark(editor.selection.getRng());\n          updateListWithDetails(editor.dom, parentList, detail);\n          var newList = editor.dom.rename(parentList, listName);\n          mergeWithAdjacentLists(editor.dom, newList);\n          editor.selection.setRng(resolveBookmark(bookmark));\n          applyList(editor, listName, detail);\n          fireListEvent(editor, listToggleActionFromListName(listName), newList);\n        }\n      } else {\n        applyList(editor, listName, detail);\n        fireListEvent(editor, listToggleActionFromListName(listName), parentList);\n      }\n    };\n    var toggleList = function (editor, listName, _detail) {\n      var parentList = getParentList(editor);\n      var selectedSubLists = getSelectedSubLists(editor);\n      var detail = isObject(_detail) ? _detail : {};\n      if (selectedSubLists.length > 0) {\n        toggleMultipleLists(editor, parentList, selectedSubLists, listName, detail);\n      } else {\n        toggleSingleList(editor, parentList, listName, detail);\n      }\n    };\n\n    var DOM = global$3.DOM;\n    var normalizeList = function (dom, list) {\n      var parentNode = list.parentNode;\n      if (parentNode.nodeName === 'LI' && parentNode.firstChild === list) {\n        var sibling = parentNode.previousSibling;\n        if (sibling && sibling.nodeName === 'LI') {\n          sibling.appendChild(list);\n          if (isEmpty(dom, parentNode)) {\n            DOM.remove(parentNode);\n          }\n        } else {\n          DOM.setStyle(parentNode, 'listStyleType', 'none');\n        }\n      }\n      if (isListNode(parentNode)) {\n        var sibling = parentNode.previousSibling;\n        if (sibling && sibling.nodeName === 'LI') {\n          sibling.appendChild(list);\n        }\n      }\n    };\n    var normalizeLists = function (dom, element) {\n      var lists = global$2.grep(dom.select('ol,ul', element));\n      global$2.each(lists, function (list) {\n        normalizeList(dom, list);\n      });\n    };\n\n    var findNextCaretContainer = function (editor, rng, isForward, root) {\n      var node = rng.startContainer;\n      var offset = rng.startOffset;\n      if (isTextNode(node) && (isForward ? offset < node.data.length : offset > 0)) {\n        return node;\n      }\n      var nonEmptyBlocks = editor.schema.getNonEmptyElements();\n      if (node.nodeType === 1) {\n        node = global$6.getNode(node, offset);\n      }\n      var walker = new global$5(node, root);\n      if (isForward) {\n        if (isBogusBr(editor.dom, node)) {\n          walker.next();\n        }\n      }\n      while (node = walker[isForward ? 'next' : 'prev2']()) {\n        if (node.nodeName === 'LI' && !node.hasChildNodes()) {\n          return node;\n        }\n        if (nonEmptyBlocks[node.nodeName]) {\n          return node;\n        }\n        if (isTextNode(node) && node.data.length > 0) {\n          return node;\n        }\n      }\n    };\n    var hasOnlyOneBlockChild = function (dom, elm) {\n      var childNodes = elm.childNodes;\n      return childNodes.length === 1 && !isListNode(childNodes[0]) && dom.isBlock(childNodes[0]);\n    };\n    var unwrapSingleBlockChild = function (dom, elm) {\n      if (hasOnlyOneBlockChild(dom, elm)) {\n        dom.remove(elm.firstChild, true);\n      }\n    };\n    var moveChildren = function (dom, fromElm, toElm) {\n      var node;\n      var targetElm = hasOnlyOneBlockChild(dom, toElm) ? toElm.firstChild : toElm;\n      unwrapSingleBlockChild(dom, fromElm);\n      if (!isEmpty(dom, fromElm, true)) {\n        while (node = fromElm.firstChild) {\n          targetElm.appendChild(node);\n        }\n      }\n    };\n    var mergeLiElements = function (dom, fromElm, toElm) {\n      var listNode;\n      var ul = fromElm.parentNode;\n      if (!isChildOfBody(dom, fromElm) || !isChildOfBody(dom, toElm)) {\n        return;\n      }\n      if (isListNode(toElm.lastChild)) {\n        listNode = toElm.lastChild;\n      }\n      if (ul === toElm.lastChild) {\n        if (isBr(ul.previousSibling)) {\n          dom.remove(ul.previousSibling);\n        }\n      }\n      var node = toElm.lastChild;\n      if (node && isBr(node) && fromElm.hasChildNodes()) {\n        dom.remove(node);\n      }\n      if (isEmpty(dom, toElm, true)) {\n        dom.$(toElm).empty();\n      }\n      moveChildren(dom, fromElm, toElm);\n      if (listNode) {\n        toElm.appendChild(listNode);\n      }\n      var contains$1 = contains(SugarElement.fromDom(toElm), SugarElement.fromDom(fromElm));\n      var nestedLists = contains$1 ? dom.getParents(fromElm, isListNode, toElm) : [];\n      dom.remove(fromElm);\n      each$1(nestedLists, function (list) {\n        if (isEmpty(dom, list) && list !== dom.getRoot()) {\n          dom.remove(list);\n        }\n      });\n    };\n    var mergeIntoEmptyLi = function (editor, fromLi, toLi) {\n      editor.dom.$(toLi).empty();\n      mergeLiElements(editor.dom, fromLi, toLi);\n      editor.selection.setCursorLocation(toLi, 0);\n    };\n    var mergeForward = function (editor, rng, fromLi, toLi) {\n      var dom = editor.dom;\n      if (dom.isEmpty(toLi)) {\n        mergeIntoEmptyLi(editor, fromLi, toLi);\n      } else {\n        var bookmark = createBookmark(rng);\n        mergeLiElements(dom, fromLi, toLi);\n        editor.selection.setRng(resolveBookmark(bookmark));\n      }\n    };\n    var mergeBackward = function (editor, rng, fromLi, toLi) {\n      var bookmark = createBookmark(rng);\n      mergeLiElements(editor.dom, fromLi, toLi);\n      var resolvedBookmark = resolveBookmark(bookmark);\n      editor.selection.setRng(resolvedBookmark);\n    };\n    var backspaceDeleteFromListToListCaret = function (editor, isForward) {\n      var dom = editor.dom, selection = editor.selection;\n      var selectionStartElm = selection.getStart();\n      var root = getClosestListRootElm(editor, selectionStartElm);\n      var li = dom.getParent(selection.getStart(), 'LI', root);\n      if (li) {\n        var ul = li.parentNode;\n        if (ul === editor.getBody() && isEmpty(dom, ul)) {\n          return true;\n        }\n        var rng_1 = normalizeRange(selection.getRng());\n        var otherLi_1 = dom.getParent(findNextCaretContainer(editor, rng_1, isForward, root), 'LI', root);\n        if (otherLi_1 && otherLi_1 !== li) {\n          editor.undoManager.transact(function () {\n            if (isForward) {\n              mergeForward(editor, rng_1, otherLi_1, li);\n            } else {\n              if (isFirstChild(li)) {\n                outdentListSelection(editor);\n              } else {\n                mergeBackward(editor, rng_1, li, otherLi_1);\n              }\n            }\n          });\n          return true;\n        } else if (!otherLi_1) {\n          if (!isForward && rng_1.startOffset === 0 && rng_1.endOffset === 0) {\n            editor.undoManager.transact(function () {\n              flattenListSelection(editor);\n            });\n            return true;\n          }\n        }\n      }\n      return false;\n    };\n    var removeBlock = function (dom, block, root) {\n      var parentBlock = dom.getParent(block.parentNode, dom.isBlock, root);\n      dom.remove(block);\n      if (parentBlock && dom.isEmpty(parentBlock)) {\n        dom.remove(parentBlock);\n      }\n    };\n    var backspaceDeleteIntoListCaret = function (editor, isForward) {\n      var dom = editor.dom;\n      var selectionStartElm = editor.selection.getStart();\n      var root = getClosestListRootElm(editor, selectionStartElm);\n      var block = dom.getParent(selectionStartElm, dom.isBlock, root);\n      if (block && dom.isEmpty(block)) {\n        var rng = normalizeRange(editor.selection.getRng());\n        var otherLi_2 = dom.getParent(findNextCaretContainer(editor, rng, isForward, root), 'LI', root);\n        if (otherLi_2) {\n          editor.undoManager.transact(function () {\n            removeBlock(dom, block, root);\n            mergeWithAdjacentLists(dom, otherLi_2.parentNode);\n            editor.selection.select(otherLi_2, true);\n            editor.selection.collapse(isForward);\n          });\n          return true;\n        }\n      }\n      return false;\n    };\n    var backspaceDeleteCaret = function (editor, isForward) {\n      return backspaceDeleteFromListToListCaret(editor, isForward) || backspaceDeleteIntoListCaret(editor, isForward);\n    };\n    var backspaceDeleteRange = function (editor) {\n      var selectionStartElm = editor.selection.getStart();\n      var root = getClosestListRootElm(editor, selectionStartElm);\n      var startListParent = editor.dom.getParent(selectionStartElm, 'LI,DT,DD', root);\n      if (startListParent || getSelectedListItems(editor).length > 0) {\n        editor.undoManager.transact(function () {\n          editor.execCommand('Delete');\n          normalizeLists(editor.dom, editor.getBody());\n        });\n        return true;\n      }\n      return false;\n    };\n    var backspaceDelete = function (editor, isForward) {\n      return editor.selection.isCollapsed() ? backspaceDeleteCaret(editor, isForward) : backspaceDeleteRange(editor);\n    };\n    var setup$1 = function (editor) {\n      editor.on('keydown', function (e) {\n        if (e.keyCode === global$4.BACKSPACE) {\n          if (backspaceDelete(editor, false)) {\n            e.preventDefault();\n          }\n        } else if (e.keyCode === global$4.DELETE) {\n          if (backspaceDelete(editor, true)) {\n            e.preventDefault();\n          }\n        }\n      });\n    };\n\n    var get = function (editor) {\n      return {\n        backspaceDelete: function (isForward) {\n          backspaceDelete(editor, isForward);\n        }\n      };\n    };\n\n    var updateList = function (editor, update) {\n      var parentList = getParentList(editor);\n      editor.undoManager.transact(function () {\n        if (isObject(update.styles)) {\n          editor.dom.setStyles(parentList, update.styles);\n        }\n        if (isObject(update.attrs)) {\n          each(update.attrs, function (v, k) {\n            return editor.dom.setAttrib(parentList, k, v);\n          });\n        }\n      });\n    };\n\n    var parseAlphabeticBase26 = function (str) {\n      var chars = reverse(trim(str).split(''));\n      var values = map(chars, function (char, i) {\n        var charValue = char.toUpperCase().charCodeAt(0) - 'A'.charCodeAt(0) + 1;\n        return Math.pow(26, i) * charValue;\n      });\n      return foldl(values, function (sum, v) {\n        return sum + v;\n      }, 0);\n    };\n    var composeAlphabeticBase26 = function (value) {\n      value--;\n      if (value < 0) {\n        return '';\n      } else {\n        var remainder = value % 26;\n        var quotient = Math.floor(value / 26);\n        var rest = composeAlphabeticBase26(quotient);\n        var char = String.fromCharCode('A'.charCodeAt(0) + remainder);\n        return rest + char;\n      }\n    };\n    var isUppercase = function (str) {\n      return /^[A-Z]+$/.test(str);\n    };\n    var isLowercase = function (str) {\n      return /^[a-z]+$/.test(str);\n    };\n    var isNumeric = function (str) {\n      return /^[0-9]+$/.test(str);\n    };\n    var deduceListType = function (start) {\n      if (isNumeric(start)) {\n        return 2;\n      } else if (isUppercase(start)) {\n        return 0;\n      } else if (isLowercase(start)) {\n        return 1;\n      } else if (isEmpty$1(start)) {\n        return 3;\n      } else {\n        return 4;\n      }\n    };\n    var parseStartValue = function (start) {\n      switch (deduceListType(start)) {\n      case 2:\n        return Optional.some({\n          listStyleType: Optional.none(),\n          start: start\n        });\n      case 0:\n        return Optional.some({\n          listStyleType: Optional.some('upper-alpha'),\n          start: parseAlphabeticBase26(start).toString()\n        });\n      case 1:\n        return Optional.some({\n          listStyleType: Optional.some('lower-alpha'),\n          start: parseAlphabeticBase26(start).toString()\n        });\n      case 3:\n        return Optional.some({\n          listStyleType: Optional.none(),\n          start: ''\n        });\n      case 4:\n        return Optional.none();\n      }\n    };\n    var parseDetail = function (detail) {\n      var start = parseInt(detail.start, 10);\n      if (is(detail.listStyleType, 'upper-alpha')) {\n        return composeAlphabeticBase26(start);\n      } else if (is(detail.listStyleType, 'lower-alpha')) {\n        return composeAlphabeticBase26(start).toLowerCase();\n      } else {\n        return detail.start;\n      }\n    };\n\n    var open = function (editor) {\n      var currentList = getParentList(editor);\n      if (!isOlNode(currentList)) {\n        return;\n      }\n      editor.windowManager.open({\n        title: 'List Properties',\n        body: {\n          type: 'panel',\n          items: [{\n              type: 'input',\n              name: 'start',\n              label: 'Start list at number',\n              inputMode: 'numeric'\n            }]\n        },\n        initialData: {\n          start: parseDetail({\n            start: editor.dom.getAttrib(currentList, 'start', '1'),\n            listStyleType: Optional.some(editor.dom.getStyle(currentList, 'list-style-type'))\n          })\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        onSubmit: function (api) {\n          var data = api.getData();\n          parseStartValue(data.start).each(function (detail) {\n            editor.execCommand('mceListUpdate', false, {\n              attrs: { start: detail.start === '1' ? '' : detail.start },\n              styles: { 'list-style-type': detail.listStyleType.getOr('') }\n            });\n          });\n          api.close();\n        }\n      });\n    };\n\n    var queryListCommandState = function (editor, listName) {\n      return function () {\n        var parentList = getParentList(editor);\n        return parentList && parentList.nodeName === listName;\n      };\n    };\n    var registerDialog = function (editor) {\n      editor.addCommand('mceListProps', function () {\n        open(editor);\n      });\n    };\n    var register$2 = function (editor) {\n      editor.on('BeforeExecCommand', function (e) {\n        var cmd = e.command.toLowerCase();\n        if (cmd === 'indent') {\n          indentListSelection(editor);\n        } else if (cmd === 'outdent') {\n          outdentListSelection(editor);\n        }\n      });\n      editor.addCommand('InsertUnorderedList', function (ui, detail) {\n        toggleList(editor, 'UL', detail);\n      });\n      editor.addCommand('InsertOrderedList', function (ui, detail) {\n        toggleList(editor, 'OL', detail);\n      });\n      editor.addCommand('InsertDefinitionList', function (ui, detail) {\n        toggleList(editor, 'DL', detail);\n      });\n      editor.addCommand('RemoveList', function () {\n        flattenListSelection(editor);\n      });\n      registerDialog(editor);\n      editor.addCommand('mceListUpdate', function (ui, detail) {\n        if (isObject(detail)) {\n          updateList(editor, detail);\n        }\n      });\n      editor.addQueryStateHandler('InsertUnorderedList', queryListCommandState(editor, 'UL'));\n      editor.addQueryStateHandler('InsertOrderedList', queryListCommandState(editor, 'OL'));\n      editor.addQueryStateHandler('InsertDefinitionList', queryListCommandState(editor, 'DL'));\n    };\n\n    var setupTabKey = function (editor) {\n      editor.on('keydown', function (e) {\n        if (e.keyCode !== global$4.TAB || global$4.metaKeyPressed(e)) {\n          return;\n        }\n        editor.undoManager.transact(function () {\n          if (e.shiftKey ? outdentListSelection(editor) : indentListSelection(editor)) {\n            e.preventDefault();\n          }\n        });\n      });\n    };\n    var setup = function (editor) {\n      if (shouldIndentOnTab(editor)) {\n        setupTabKey(editor);\n      }\n      setup$1(editor);\n    };\n\n    var register$1 = function (editor) {\n      var exec = function (command) {\n        return function () {\n          return editor.execCommand(command);\n        };\n      };\n      if (!editor.hasPlugin('advlist')) {\n        editor.ui.registry.addToggleButton('numlist', {\n          icon: 'ordered-list',\n          active: false,\n          tooltip: 'Numbered list',\n          onAction: exec('InsertOrderedList'),\n          onSetup: function (api) {\n            return listState(editor, 'OL', api.setActive);\n          }\n        });\n        editor.ui.registry.addToggleButton('bullist', {\n          icon: 'unordered-list',\n          active: false,\n          tooltip: 'Bullet list',\n          onAction: exec('InsertUnorderedList'),\n          onSetup: function (api) {\n            return listState(editor, 'UL', api.setActive);\n          }\n        });\n      }\n    };\n\n    var register = function (editor) {\n      var listProperties = {\n        text: 'List properties...',\n        icon: 'ordered-list',\n        onAction: function () {\n          return editor.execCommand('mceListProps');\n        },\n        onSetup: function (api) {\n          return listState(editor, 'OL', function (active) {\n            return api.setDisabled(!active);\n          });\n        }\n      };\n      editor.ui.registry.addMenuItem('listprops', listProperties);\n      editor.ui.registry.addContextMenu('lists', {\n        update: function (node) {\n          var parentList = getParentList(editor, node);\n          return isOlNode(parentList) ? ['listprops'] : [];\n        }\n      });\n    };\n\n    function Plugin () {\n      global$7.add('lists', function (editor) {\n        if (editor.hasPlugin('rtc', true) === false) {\n          setup(editor);\n          register$2(editor);\n        } else {\n          registerDialog(editor);\n        }\n        register$1(editor);\n        register(editor);\n        return get(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"lists\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/lists')\n//   ES2015:\n//     import 'tinymce/plugins/lists'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/lists/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,OAAM;AAC7B,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,WAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,OAAM;AACjC,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,UAAU;AAAA;AAAA;AAG5B,UAAI,WAAW,SAAS;AACxB,UAAI,WAAW,SAAS;AACxB,UAAI,UAAU,SAAS;AACvB,UAAI,YAAY,aAAa;AAC7B,UAAI,aAAa,aAAa;AAC9B,UAAI,WAAW,aAAa;AAE5B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,GAAG,GAAG;AACjC,eAAO,MAAM;AAAA;AAEf,UAAI,MAAM,SAAU,GAAG;AACrB,eAAO,SAAU,GAAG;AAClB,iBAAO,CAAC,EAAE;AAAA;AAAA;AAGd,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,QAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,cAAc,MAAM,UAAU;AAClC,UAAI,aAAa,MAAM,UAAU;AACjC,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,IAAI,GAAG;AAC5B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,WAAW,SAAU,IAAI,MAAM;AACjC,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,cAAE,KAAK;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,UAAU,SAAU,IAAI,GAAG;AAC7B,YAAI,GAAG,WAAW,GAAG;AACnB,iBAAO;AAAA,eACF;AACL,cAAI,UAAU,EAAE,GAAG;AACnB,cAAI,IAAI;AACR,cAAI,QAAQ;AACZ,mBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAI,IAAI,GAAG;AACX,gBAAI,QAAO,EAAE;AACb,gBAAI,UAAS,SAAS;AACpB,gBAAE,KAAK;AACP,sBAAQ;AAAA;AAEV,sBAAU;AACV,kBAAM,KAAK;AAAA;AAEb,cAAI,MAAM,WAAW,GAAG;AACtB,cAAE,KAAK;AAAA;AAET,iBAAO;AAAA;AAAA;AAGX,UAAI,QAAQ,SAAU,IAAI,GAAG,KAAK;AAChC,eAAO,IAAI,SAAU,GAAG,GAAG;AACzB,gBAAM,EAAE,KAAK,GAAG;AAAA;AAElB,eAAO;AAAA;AAET,UAAI,YAAY,SAAU,IAAI,MAAM,OAAO;AACzC,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,mBAAO,SAAS,KAAK;AAAA,qBACZ,MAAM,GAAG,IAAI;AACtB;AAAA;AAAA;AAGJ,eAAO,SAAS;AAAA;AAElB,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,eAAO,UAAU,IAAI,MAAM;AAAA;AAE7B,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,KAAK;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;AAAA;AAE3E,qBAAW,MAAM,GAAG,GAAG;AAAA;AAEzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,eAAO,QAAQ,IAAI,IAAI;AAAA;AAEzB,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,IAAI,YAAY,KAAK,IAAI;AAC7B,UAAE;AACF,eAAO;AAAA;AAET,UAAI,QAAQ,SAAU,IAAI,GAAG;AAC3B,eAAO,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,MAAM,SAAS;AAAA;AAEnE,UAAI,OAAO,SAAU,IAAI;AACvB,eAAO,MAAM,IAAI;AAAA;AAEnB,UAAI,OAAO,SAAU,IAAI;AACvB,eAAO,MAAM,IAAI,GAAG,SAAS;AAAA;AAE/B,UAAI,UAAU,SAAU,KAAK,GAAG;AAC9B,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,IAAI,EAAE,IAAI,IAAI;AAClB,cAAI,EAAE,UAAU;AACd,mBAAO;AAAA;AAAA;AAGX,eAAO,SAAS;AAAA;AAGlB,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAE9B,6BAAuB,IAAI,OAAM,MAAM;AACrC,YAAI,QAAQ,UAAU,WAAW;AAC/B,mBAAS,IAAI,GAAG,IAAI,MAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AAC/C,gBAAI,MAAM,CAAE,MAAK,QAAO;AACtB,kBAAI,CAAC;AACH,qBAAK,MAAM,UAAU,MAAM,KAAK,OAAM,GAAG;AAC3C,iBAAG,KAAK,MAAK;AAAA;AAAA;AAGnB,eAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK;AAAA;AAGpD,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,SAAS;AACb,YAAI;AACJ,eAAO,WAAY;AACjB,cAAI,OAAO;AACX,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,iBAAK,MAAM,UAAU;AAAA;AAEvB,cAAI,CAAC,QAAQ;AACX,qBAAS;AACT,gBAAI,EAAE,MAAM,MAAM;AAAA;AAEpB,iBAAO;AAAA;AAAA;AAIX,UAAI,aAAa,SAAU,IAAI,SAAS,WAAW,aAAY;AAC7D,YAAI,SAAS,GAAG,WAAW,QAAQ,KAAK,eAAe;AACvD,YAAI,WAAW,GAAG,WAAW,CAAC;AAC9B,YAAI,WAAW,GAAG,WAAW,GAAG;AAChC,YAAI,UAAU,YAAY,YAAW;AACrC,YAAI,WAAW,UAAU,CAAC,YAAY,YAAY,YAAW;AAC7D,YAAI,UAAU,YAAY,YAAY,CAAC;AACvC,YAAI,aAAa,QAAQ,cAAc,GAAG,WAAW,UAAU,KAAK,eAAe;AACnF,YAAI,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;AAC1C,eAAO;AAAA,UACL,QAAQ,SAAS;AAAA,UACjB,UAAU,SAAS;AAAA,UACnB,UAAU,SAAS;AAAA,UACnB,SAAS,SAAS;AAAA,UAClB,SAAS,SAAS;AAAA,UAClB,WAAW,GAAG;AAAA,UACd,OAAO,GAAG;AAAA,UACV,WAAW,SAAS;AAAA,UACpB,WAAW,SAAS;AAAA;AAAA;AAIxB,UAAI,aAAa,SAAU,SAAS,GAAG;AACrC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,IAAI,QAAQ;AAChB,cAAI,EAAE,KAAK,IAAI;AACb,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,SAAS,OAAO;AACnC,YAAI,IAAI,WAAW,SAAS;AAC5B,YAAI,CAAC,GAAG;AACN,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO;AAAA;AAAA;AAGX,YAAI,QAAQ,SAAU,GAAG;AACvB,iBAAO,OAAO,MAAM,QAAQ,GAAG,MAAM;AAAA;AAEvC,eAAO,KAAK,MAAM,IAAI,MAAM;AAAA;AAE9B,UAAI,WAAW,SAAU,gBAAgB,OAAO;AAC9C,YAAI,eAAe,OAAO,OAAO;AACjC,YAAI,eAAe,WAAW,GAAG;AAC/B,iBAAO;AAAA;AAET,eAAO,KAAK,gBAAgB;AAAA;AAE9B,UAAI,YAAY,WAAY;AAC1B,eAAO,KAAK,GAAG;AAAA;AAEjB,UAAI,OAAO,SAAU,OAAO,OAAO;AACjC,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,UAAU;AAAA,QACZ,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA;AAGX,UAAI,kBAAkB,SAAU,WAAU,eAAe;AACvD,eAAO,QAAQ,cAAc,QAAQ,SAAU,SAAS;AACtD,cAAI,UAAU,QAAQ,MAAM;AAC5B,iBAAO,OAAO,WAAU,SAAU,SAAS;AACzC,gBAAI;AACJ,mBAAO,YAAc,OAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,aAChF,IAAI,SAAU,MAAM;AACrB,mBAAO;AAAA,cACL,SAAS,KAAK;AAAA,cACd,SAAS,QAAQ,GAAG,SAAS,QAAQ,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAM3D,UAAI,WAAW,SAAU,YAAY,WAAW;AAC9C,YAAI,QAAQ,OAAO,WAAW;AAC9B,eAAO,OAAO,YAAY,SAAU,WAAW;AAC7C,iBAAO,UAAU,OAAO;AAAA;AAAA;AAG5B,UAAI,gBAAgB,SAAU,WAAU,WAAW;AACjD,eAAO,SAAS,WAAU,WAAW,IAAI,SAAU,SAAS;AAC1D,cAAI,UAAU,QAAQ,OAAO,QAAQ,gBAAgB;AACrD,iBAAO;AAAA,YACL,SAAS,QAAQ;AAAA,YACjB;AAAA;AAAA;AAAA;AAIN,UAAI,WAAW,SAAU,OAAM,WAAW;AACxC,eAAO,SAAS,OAAM,WAAW,IAAI,SAAU,IAAI;AACjD,cAAI,UAAU,QAAQ,OAAO,GAAG,gBAAgB;AAChD,iBAAO;AAAA,YACL,SAAS,GAAG;AAAA,YACZ;AAAA;AAAA;AAAA;AAKN,UAAI,aAAa,SAAU,KAAK,QAAQ;AACtC,eAAO,IAAI,QAAQ,YAAY;AAAA;AAEjC,UAAI,QAAQ,SAAU,GAAG;AACvB,eAAO,SAAU,GAAG;AAClB,iBAAO,EAAE,QAAQ,GAAG;AAAA;AAAA;AAGxB,UAAI,OAAO,MAAM;AACjB,UAAI,aAAa,SAAU,GAAG;AAC5B,eAAO,EAAE,SAAS;AAAA;AAEpB,UAAI,YAAY,SAAU,GAAG;AAC3B,eAAO,CAAC,WAAW;AAAA;AAGrB,UAAI,qBAAqB;AACzB,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,SAAU,UAAU;AACzB,iBAAO,WAAW,UAAU;AAAA;AAAA;AAGhC,UAAI,WAAW;AAAA,QACb;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB,CAAC;AAAA,UACjB,QAAQ,SAAU,UAAU;AAC1B,mBAAO,WAAW,UAAU,YAAY,WAAW,UAAU,aAAa,WAAW,UAAU,aAAa,WAAW,UAAU;AAAA;AAAA;AAAA,QAGrI;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ,SAAU,UAAU;AAC1B,mBAAO,WAAW,UAAU,aAAa,CAAC,WAAW,UAAU;AAAA;AAAA;AAAA,QAGnE;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ,SAAU,UAAU;AAC1B,mBAAO,WAAW,UAAU,WAAW,WAAW,UAAU;AAAA;AAAA;AAAA,QAGhE;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ,cAAc;AAAA;AAAA,QAExB;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB,CAAC;AAAA,UACjB,QAAQ,cAAc;AAAA;AAAA,QAExB;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ,SAAU,UAAU;AAC1B,mBAAQ,YAAW,UAAU,aAAa,WAAW,UAAU,eAAe,WAAW,UAAU;AAAA;AAAA;AAAA;AAIzG,UAAI,OAAO;AAAA,QACT;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB,CAAC;AAAA;AAAA,QAEnB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,SAAU,UAAU;AAC1B,mBAAO,WAAW,UAAU,aAAa,WAAW,UAAU;AAAA;AAAA,UAEhE,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA;AAAA;AAAA,QAGJ;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB,CAAC;AAAA;AAAA,QAEnB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB,CAAC;AAAA;AAAA,QAEnB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB;AAAA;AAAA,QAElB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB;AAAA;AAAA,QAElB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB;AAAA;AAAA,QAElB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB,CAAC;AAAA;AAAA;AAGrB,UAAI,eAAe;AAAA,QACjB,UAAU,SAAS;AAAA,QACnB,MAAM,SAAS;AAAA;AAGjB,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,KAAK;AACT,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,SAAS;AACb,UAAI,YAAY,WAAY;AAC1B,eAAO,KAAK;AAAA,UACV,SAAS;AAAA,UACT,SAAS,QAAQ;AAAA;AAAA;AAGrB,UAAI,OAAO,SAAU,MAAM;AACzB,YAAI,UAAU,KAAK;AACnB,YAAI,UAAU,KAAK;AACnB,YAAI,YAAY,SAAU,OAAM;AAC9B,iBAAO,WAAY;AACjB,mBAAO,YAAY;AAAA;AAAA;AAGvB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ,UAAU;AAAA,UAClB,UAAU,UAAU;AAAA,UACpB,MAAM,UAAU;AAAA,UAChB,SAAS,UAAU;AAAA,UACnB,WAAW,UAAU;AAAA,UACrB,UAAU,UAAU;AAAA;AAAA;AAGxB,UAAI,UAAU;AAAA,QACZ,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,MAAM,SAAS;AAAA,QACf,QAAQ,SAAS;AAAA,QACjB,IAAI,SAAS;AAAA,QACb,OAAO,SAAS;AAAA,QAChB,SAAS,SAAS;AAAA,QAClB,QAAQ,SAAS;AAAA;AAGnB,UAAI,UAAU;AACd,UAAI,MAAM;AACV,UAAI,UAAU;AACd,UAAI,QAAQ;AACZ,UAAI,MAAM;AACV,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,UAAU,WAAY;AACxB,eAAO,GAAG;AAAA,UACR,SAAS;AAAA,UACT,SAAS,QAAQ;AAAA;AAAA;AAGrB,UAAI,KAAK,SAAU,MAAM;AACvB,YAAI,UAAU,KAAK;AACnB,YAAI,UAAU,KAAK;AACnB,YAAI,OAAO,SAAU,OAAM;AACzB,iBAAO,WAAY;AACjB,mBAAO,YAAY;AAAA;AAAA;AAGvB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,WAAW,KAAK;AAAA,UAChB,OAAO,KAAK;AAAA,UACZ,WAAW,KAAK;AAAA,UAChB,OAAO,KAAK;AAAA,UACZ,SAAS,KAAK;AAAA,UACd,WAAW,KAAK;AAAA,UAChB,WAAW,KAAK;AAAA,UAChB,YAAY,KAAK;AAAA;AAAA;AAGrB,UAAI,kBAAkB;AAAA,QACpB;AAAA,QACA;AAAA,QACA,SAAS,SAAS;AAAA,QAClB,KAAK,SAAS;AAAA,QACd,SAAS,SAAS;AAAA,QAClB,OAAO,SAAS;AAAA,QAChB,KAAK,SAAS;AAAA,QACd,SAAS,SAAS;AAAA,QAClB,SAAS,SAAS;AAAA,QAClB,UAAU,SAAS;AAAA;AAGrB,UAAI,WAAW,SAAU,WAAW,kBAAkB,aAAY;AAChE,YAAI,YAAW,aAAa;AAC5B,YAAI,QAAO,aAAa;AACxB,YAAI,UAAU,iBAAiB,KAAK,SAAU,eAAe;AAC3D,iBAAO,gBAAgB,WAAU;AAAA,WAChC,QAAQ,WAAY;AACrB,iBAAO,cAAc,WAAU;AAAA,WAC9B,KAAK,QAAQ,SAAS,QAAQ;AACjC,YAAI,KAAK,SAAS,OAAM,WAAW,KAAK,gBAAgB,SAAS,gBAAgB;AACjF,YAAI,aAAa,WAAW,IAAI,SAAS,WAAW;AACpD,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,oBAAoB,EAAE,QAAQ;AAElC,UAAI,aAAa,SAAU,OAAO;AAChC,eAAO,OAAO,WAAW,OAAO;AAAA;AAElC,UAAI,WAAW,OAAO,WAAY;AAChC,eAAO,kBAAkB,OAAO,UAAU,WAAW,SAAS,KAAK,UAAU,gBAAgB;AAAA;AAE/F,UAAI,SAAS,WAAY;AACvB,eAAO;AAAA;AAGT,UAAI,0BAA0B,SAAU,GAAG,GAAG,OAAO;AACnD,eAAQ,GAAE,wBAAwB,KAAK,WAAW;AAAA;AAEpD,UAAI,8BAA8B,SAAU,GAAG,GAAG;AAChD,eAAO,wBAAwB,GAAG,GAAG,KAAK;AAAA;AAG5C,UAAI,UAAU;AAEd,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,IAAI,cAAc;AAC5B,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,mBAAmB,IAAI,WAAW,SAAS,GAAG;AACrD,kBAAQ,MAAM,yCAAyC;AACvD,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,QAAQ,IAAI,WAAW;AAAA;AAEhC,UAAI,UAAU,SAAU,KAAK,OAAO;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,cAAc;AAC7B,eAAO,QAAQ;AAAA;AAEjB,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,eAAe;AAC9B,eAAO,QAAQ;AAAA;AAEjB,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,EAAE,KAAK;AAAA;AAEhB,UAAI,YAAY,SAAU,QAAQ,GAAG,GAAG;AACtC,eAAO,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,IAAI,IAAI;AAAA;AAE9D,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,OAAO,SAAU,SAAS,UAAU;AACtC,YAAI,MAAM,QAAQ;AAClB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,eACF;AACL,cAAI,OAAO;AACX,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ;AAAA,qBACX,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB;AAAA,qBACrB,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB;AAAA,qBACzB,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB;AAAA,iBAC1B;AACL,kBAAM,IAAI,MAAM;AAAA;AAAA;AAAA;AAKtB,UAAI,KAAK,SAAU,IAAI,IAAI;AACzB,eAAO,GAAG,QAAQ,GAAG;AAAA;AAEvB,UAAI,kBAAkB,SAAU,IAAI,IAAI;AACtC,YAAI,KAAK,GAAG;AACZ,YAAI,KAAK,GAAG;AACZ,eAAO,OAAO,KAAK,QAAQ,GAAG,SAAS;AAAA;AAEzC,UAAI,aAAa,SAAU,IAAI,IAAI;AACjC,eAAO,4BAA4B,GAAG,KAAK,GAAG;AAAA;AAEhD,UAAI,WAAW,SAAU,IAAI,IAAI;AAC/B,eAAO,SAAS,QAAQ,SAAS,WAAW,IAAI,MAAM,gBAAgB,IAAI;AAAA;AAE5E,UAAI,OAAO;AAEX,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO,SAAU,KAAK,GAAG;AAC3B,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,IAAI;AACZ,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,SAAS,SAAU,GAAG;AACxB,eAAO,SAAU,GAAG,GAAG;AACrB,YAAE,KAAK;AAAA;AAAA;AAGX,UAAI,iBAAiB,SAAU,KAAK,MAAM,QAAQ,SAAS;AACzD,YAAI,IAAI;AACR,aAAK,KAAK,SAAU,GAAG,GAAG;AACxB,UAAC,MAAK,GAAG,KAAK,SAAS,SAAS,GAAG;AAAA;AAErC,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,KAAK,MAAM;AAChC,YAAI,IAAI;AACR,uBAAe,KAAK,MAAM,OAAO,IAAI;AACrC,eAAO;AAAA;AAGT,aAAO,WAAW,cAAc,SAAS,SAAS;AAElD,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,IAAI,QAAQ,IAAI;AACpB,eAAO,EAAE;AAAA;AAEX,UAAI,OAAO,SAAU,SAAS;AAC5B,eAAO,QAAQ,IAAI;AAAA;AAErB,UAAI,SAAS,SAAU,GAAG;AACxB,eAAO,SAAU,SAAS;AACxB,iBAAO,KAAK,aAAa;AAAA;AAAA;AAG7B,UAAI,YAAY,OAAO;AACvB,UAAI,QAAQ,SAAU,KAAK;AACzB,eAAO,SAAU,GAAG;AAClB,iBAAO,UAAU,MAAM,KAAK,OAAO;AAAA;AAAA;AAIvC,UAAI,SAAS,SAAU,KAAK,KAAK,OAAO;AACtC,YAAI,SAAS,UAAU,UAAU,UAAU,SAAS,QAAQ;AAC1D,cAAI,aAAa,KAAK,QAAQ;AAAA,eACzB;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAa,OAAO,eAAe;AAC7F,gBAAM,IAAI,MAAM;AAAA;AAAA;AAGpB,UAAI,SAAS,SAAU,SAAS,OAAO;AACrC,YAAI,MAAM,QAAQ;AAClB,aAAK,OAAO,SAAU,GAAG,GAAG;AAC1B,iBAAO,KAAK,GAAG;AAAA;AAAA;AAGnB,UAAI,UAAU,SAAU,SAAS;AAC/B,eAAO,MAAM,QAAQ,IAAI,YAAY,SAAU,KAAK,MAAM;AACxD,cAAI,KAAK,QAAQ,KAAK;AACtB,iBAAO;AAAA,WACN;AAAA;AAGL,UAAI,SAAS,SAAU,SAAS;AAC9B,eAAO,SAAS,KAAK,QAAQ,IAAI,YAAY,IAAI,aAAa;AAAA;AAEhE,UAAI,WAAW,SAAU,SAAS;AAChC,eAAO,IAAI,QAAQ,IAAI,YAAY,aAAa;AAAA;AAElD,UAAI,QAAQ,SAAU,SAAS,OAAO;AACpC,YAAI,KAAK,QAAQ,IAAI;AACrB,eAAO,SAAS,KAAK,GAAG,QAAQ,IAAI,aAAa;AAAA;AAEnD,UAAI,aAAa,SAAU,SAAS;AAClC,eAAO,MAAM,SAAS;AAAA;AAExB,UAAI,YAAY,SAAU,SAAS;AACjC,eAAO,MAAM,SAAS,QAAQ,IAAI,WAAW,SAAS;AAAA;AAGxD,UAAI,WAAW,SAAU,QAAQ,SAAS;AACxC,YAAI,WAAW,OAAO;AACtB,iBAAS,KAAK,SAAU,GAAG;AACzB,YAAE,IAAI,aAAa,QAAQ,KAAK,OAAO;AAAA;AAAA;AAG3C,UAAI,WAAW,SAAU,SAAQ,SAAS;AACxC,gBAAO,IAAI,YAAY,QAAQ;AAAA;AAGjC,UAAI,SAAS,SAAU,QAAQ,UAAU;AACvC,eAAO,UAAU,SAAU,GAAG;AAC5B,mBAAS,QAAQ;AAAA;AAAA;AAGrB,UAAI,SAAS,SAAU,SAAQ,UAAU;AACvC,eAAO,UAAU,SAAU,GAAG;AAC5B,mBAAS,SAAQ;AAAA;AAAA;AAIrB,UAAI,SAAS,SAAU,SAAS;AAC9B,YAAI,MAAM,QAAQ;AAClB,YAAI,IAAI,eAAe,MAAM;AAC3B,cAAI,WAAW,YAAY;AAAA;AAAA;AAI/B,UAAI,QAAQ,SAAU,UAAU,QAAQ;AACtC,eAAO,aAAa,QAAQ,SAAS,IAAI,UAAU;AAAA;AAErD,UAAI,OAAO,SAAU,UAAU;AAC7B,eAAO,MAAM,UAAU;AAAA;AAEzB,UAAI,YAAY,SAAU,UAAU,KAAK;AACvC,YAAI,MAAK,aAAa,QAAQ;AAC9B,YAAI,aAAa,QAAQ;AACzB,eAAO,KAAI;AACX,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,UAAU,KAAK;AACpC,YAAI,MAAK,UAAU,UAAU;AAC7B,iBAAS,UAAU;AACnB,YAAI,aAAa,SAAS;AAC1B,eAAO,KAAI;AACX,eAAO;AACP,eAAO;AAAA;AAGT,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,gBAAgB,SAAU,OAAM;AAClC,eAAO,SAAU,MAAM;AACrB,iBAAO,QAAQ,KAAK,SAAS,kBAAkB;AAAA;AAAA;AAGnD,UAAI,iBAAiB,SAAU,OAAO;AACpC,eAAO,SAAU,MAAM;AACrB,iBAAO,QAAQ,MAAM,KAAK,KAAK;AAAA;AAAA;AAGnC,UAAI,aAAa,SAAU,MAAM;AAC/B,eAAO,QAAQ,KAAK,aAAa;AAAA;AAEnC,UAAI,aAAa,eAAe;AAChC,UAAI,aAAa,eAAe;AAChC,UAAI,WAAW,cAAc;AAC7B,UAAI,iBAAiB,eAAe;AACpC,UAAI,eAAe,eAAe;AAClC,UAAI,kBAAkB,eAAe;AACrC,UAAI,OAAO,cAAc;AACzB,UAAI,eAAe,SAAU,MAAM;AACjC,eAAO,KAAK,WAAW,eAAe;AAAA;AAExC,UAAI,cAAc,SAAU,QAAQ,MAAM;AACxC,eAAO,QAAQ,CAAC,CAAC,OAAO,OAAO,uBAAuB,KAAK;AAAA;AAE7D,UAAI,UAAU,SAAU,MAAM,eAAe;AAC3C,eAAO,QAAQ,KAAK,YAAY;AAAA;AAElC,UAAI,YAAY,SAAU,KAAK,MAAM;AACnC,YAAI,CAAC,KAAK,OAAO;AACf,iBAAO;AAAA;AAET,eAAO,IAAI,QAAQ,KAAK,gBAAgB,CAAC,KAAK,KAAK;AAAA;AAErD,UAAI,UAAU,SAAU,KAAK,KAAK,eAAe;AAC/C,YAAI,QAAQ,IAAI,QAAQ;AACxB,YAAI,iBAAiB,IAAI,OAAO,gCAAgC,KAAK,SAAS,GAAG;AAC/E,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,KAAK,KAAK;AACtC,eAAO,IAAI,UAAU,KAAK,IAAI;AAAA;AAGhC,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,OAAO,SAAS,uBAAuB;AAAA;AAEhD,UAAI,qBAAqB,SAAU,QAAQ;AACzC,YAAI,QAAQ,OAAO,SAAS,qBAAqB;AACjD,YAAI,UAAU,OAAO;AACnB,iBAAO;AAAA,mBACE,UAAU,MAAM;AACzB,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,0BAA0B,SAAU,QAAQ;AAC9C,eAAO,OAAO,SAAS,2BAA2B;AAAA;AAGpD,UAAI,kBAAkB,SAAU,QAAQ,aAAa;AACnD,YAAI,MAAM,OAAO;AACjB,YAAI,gBAAgB,OAAO,OAAO;AAClC,YAAI,WAAW,IAAI;AACnB,YAAI,YAAY,mBAAmB;AACnC,YAAI,MAAM,WAAW;AACrB,YAAI,WAAW;AACb,sBAAY,IAAI,OAAO;AACvB,cAAI,UAAU,YAAY,UAAU,eAAe;AACjD,gBAAI,WAAW,WAAW,wBAAwB;AAAA;AAEpD,cAAI,CAAC,QAAQ,YAAY,YAAY,gBAAgB;AACnD,qBAAS,YAAY;AAAA;AAAA;AAGzB,YAAI,aAAa;AACf,iBAAO,OAAO,YAAY,YAAY;AACpC,gBAAI,WAAW,KAAK;AACpB,gBAAI,CAAC,kBAAmB,cAAa,UAAU,KAAK,aAAa,qBAAqB,aAAa;AACjG,+BAAiB;AAAA;AAEnB,gBAAI,QAAQ,MAAM,gBAAgB;AAChC,uBAAS,YAAY;AACrB,0BAAY;AAAA,mBACP;AACL,kBAAI,WAAW;AACb,oBAAI,CAAC,WAAW;AACd,8BAAY,IAAI,OAAO;AACvB,2BAAS,YAAY;AAAA;AAEvB,0BAAU,YAAY;AAAA,qBACjB;AACL,yBAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAK7B,YAAI,CAAC,WAAW;AACd,mBAAS,YAAY,IAAI,OAAO;AAAA,eAC3B;AACL,cAAI,CAAC,gBAAgB;AACnB,sBAAU,YAAY,IAAI,OAAO,MAAM,EAAE,kBAAkB;AAAA;AAAA;AAG/D,eAAO;AAAA;AAGT,UAAI,QAAQ,SAAS;AACrB,UAAI,YAAY,SAAU,QAAQ,MAAM,IAAI;AAC1C,YAAI,yBAAyB,SAAU,YAAY;AACjD,mBAAS,KAAK,WAAW,SAAU,OAAM;AACvC,uBAAW,WAAW,aAAa,OAAM,GAAG;AAAA;AAE9C,gBAAM,OAAO;AAAA;AAEf,YAAI,YAAY,MAAM,OAAO,kCAAkC;AAC/D,YAAI,WAAW,gBAAgB,QAAQ;AACvC,YAAI,SAAS,MAAM;AACnB,eAAO,cAAc;AACrB,eAAO,YAAY;AACnB,YAAI,WAAW,OAAO;AACtB,iBAAS,OAAO,SAAS,YAAY,MAAM,OAAO,KAAK,YAAY;AACjE,cAAI,KAAK,aAAa,QAAQ,OAAO,IAAI,QAAQ,OAAO;AACtD,kBAAM,OAAO;AACb;AAAA;AAAA;AAGJ,YAAI,CAAC,OAAO,IAAI,QAAQ,WAAW;AACjC,gBAAM,YAAY,UAAU;AAAA;AAE9B,cAAM,YAAY,UAAU;AAC5B,YAAI,QAAQ,OAAO,KAAK,GAAG,aAAa;AACtC,iCAAuB,GAAG;AAAA;AAE5B,cAAM,OAAO;AACb,YAAI,QAAQ,OAAO,KAAK,OAAO;AAC7B,gBAAM,OAAO;AAAA;AAAA;AAIjB,UAAI,sBAAsB,MAAM;AAChC,UAAI,oBAAoB,MAAM;AAC9B,UAAI,gBAAgB,SAAU,QAAQ,MAAM;AAC1C,YAAI,oBAAoB,OAAO;AAC7B,iBAAO,MAAM;AAAA,mBACJ,kBAAkB,OAAO;AAClC,iBAAO,MAAM,KAAK,SAAU,IAAI;AAC9B,mBAAO,UAAU,QAAQ,GAAG,KAAK,KAAK;AAAA;AAAA;AAAA;AAI5C,UAAI,eAAe,SAAU,MAAM;AACjC,YAAI,kBAAkB,OAAO;AAC3B,iBAAO,MAAM;AAAA;AAAA;AAGjB,UAAI,gBAAgB,SAAU,QAAQ,aAAa,SAAS;AAC1D,YAAI,gBAAgB,UAAU;AAC5B,iBAAO,SAAS;AAAA,eACX;AACL,iBAAO,SAAS,SAAU,MAAM;AAC9B,mBAAO,cAAc,QAAQ;AAAA;AAAA;AAAA;AAKnC,UAAI,qBAAqB,SAAU,WAAW,QAAQ;AACpD,YAAI,WAAW,YAAY;AACzB,iBAAO;AAAA,YACL;AAAA,YACA;AAAA;AAAA;AAGJ,YAAI,OAAO,SAAS,QAAQ,WAAW;AACvC,YAAI,WAAW,OAAO;AACpB,iBAAO;AAAA,YACL,WAAW;AAAA,YACX,QAAQ,UAAU,UAAU,WAAW,SAAS,KAAK,KAAK,SAAS;AAAA;AAAA,mBAE5D,KAAK,mBAAmB,WAAW,KAAK,kBAAkB;AACnE,iBAAO;AAAA,YACL,WAAW,KAAK;AAAA,YAChB,QAAQ,KAAK,gBAAgB,KAAK;AAAA;AAAA,mBAE3B,KAAK,eAAe,WAAW,KAAK,cAAc;AAC3D,iBAAO;AAAA,YACL,WAAW,KAAK;AAAA,YAChB,QAAQ;AAAA;AAAA;AAGZ,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,iBAAiB,SAAU,KAAK;AAClC,YAAI,SAAS,IAAI;AACjB,YAAI,aAAa,mBAAmB,IAAI,gBAAgB,IAAI;AAC5D,eAAO,SAAS,WAAW,WAAW,WAAW;AACjD,YAAI,WAAW,mBAAmB,IAAI,cAAc,IAAI;AACxD,eAAO,OAAO,SAAS,WAAW,SAAS;AAC3C,eAAO;AAAA;AAGT,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,gBAAgB,SAAU,QAAQ,MAAM;AAC1C,YAAI,iBAAiB,QAAQ,OAAO,UAAU,SAAS;AACvD,eAAO,OAAO,IAAI,UAAU,gBAAgB,YAAY,sBAAsB,QAAQ;AAAA;AAExF,UAAI,uBAAuB,SAAU,YAAY,gBAAgB;AAC/D,eAAO,cAAc,eAAe,WAAW,KAAK,eAAe,OAAO;AAAA;AAE5E,UAAI,eAAe,SAAU,YAAY;AACvC,eAAO,SAAS,WAAW,iBAAiB,aAAa;AAAA;AAE3D,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,YAAI,aAAa,cAAc;AAC/B,YAAI,iBAAiB,OAAO,UAAU;AACtC,YAAI,qBAAqB,YAAY,iBAAiB;AACpD,iBAAO,aAAa;AAAA,eACf;AACL,iBAAO,SAAS,gBAAgB,SAAU,KAAK;AAC7C,mBAAO,WAAW,QAAQ,eAAe;AAAA;AAAA;AAAA;AAI/C,UAAI,2BAA2B,SAAU,QAAQ,MAAM;AACrD,YAAI,gBAAgB,SAAS,IAAI,MAAM,SAAU,KAAK;AACpD,cAAI,WAAW,OAAO,IAAI,UAAU,KAAK,YAAY,sBAAsB,QAAQ;AACnF,iBAAO,WAAW,WAAW;AAAA;AAE/B,eAAO,SAAS,OAAO;AAAA;AAEzB,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,YAAI,iBAAiB,OAAO,UAAU;AACtC,eAAO,SAAS,yBAAyB,QAAQ,iBAAiB;AAAA;AAEpE,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,SAAS,qBAAqB,SAAS;AAAA;AAEhD,UAAI,wBAAwB,SAAU,QAAQ,KAAK;AACjD,YAAI,kBAAkB,OAAO,IAAI,WAAW,KAAK;AACjD,eAAO,gBAAgB,SAAS,IAAI,gBAAgB,KAAK,OAAO;AAAA;AAElE,UAAI,yBAAyB,SAAU,QAAQ,KAAK;AAClD,YAAI,cAAc,OAAO,IAAI,WAAW,KAAK,SAAS,sBAAsB,QAAQ;AACpF,eAAO,KAAK;AAAA;AAEd,UAAI,mBAAmB,SAAU,QAAQ;AACvC,YAAI,YAAY,uBAAuB,QAAQ,OAAO,UAAU;AAChE,YAAI,kBAAkB,SAAS,OAAO,UAAU,qBAAqB;AACrE,eAAO,UAAU,UAAU,OAAO;AAAA;AAEpC,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,YAAI,gBAAgB,iBAAiB;AACrC,eAAO,mBAAmB,QAAQ;AAAA;AAEpC,UAAI,qBAAqB,SAAU,QAAQ,OAAO;AAChD,YAAI,YAAY,IAAI,OAAO,SAAU,MAAM;AACzC,iBAAO,uBAAuB,QAAQ,MAAM,MAAM;AAAA;AAEpD,eAAO,SAAS,OAAO;AAAA;AAGzB,UAAI,KAAK,SAAU,KAAK,KAAK,YAAY;AACvC,YAAI,eAAe,QAAQ;AACzB,uBAAa;AAAA;AAEf,eAAO,IAAI,OAAO,SAAU,MAAM;AAChC,iBAAO,WAAW,MAAM;AAAA;AAAA;AAG5B,UAAI,QAAQ,SAAU,IAAI,IAAI,GAAG;AAC/B,eAAO,GAAG,YAAY,GAAG,WAAW,SAAS,KAAK,EAAE,GAAG,YAAY,GAAG,eAAe,SAAS;AAAA;AAGhG,UAAI,eAAe,SAAU,UAAU,OAAO;AAC5C,YAAI,MAAM,SAAS;AACnB,YAAI,WAAW,IAAI;AACnB,eAAO,UAAU,SAAU,SAAS;AAClC,mBAAS,YAAY,QAAQ;AAAA;AAE/B,eAAO,aAAa,QAAQ;AAAA;AAG9B,UAAI,gBAAgB,SAAU,QAAQ,QAAQ,SAAS;AACrD,eAAO,OAAO,KAAK,gBAAgB;AAAA,UACjC;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,cAAc,SAAU,KAAK;AAC/B,eAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM;AAAA;AAGzD,UAAI,cAAc,SAAU,KAAK,UAAU,OAAO;AAChD,YAAI,CAAC,SAAS,QAAQ;AACpB,kBAAQ,MAAM,sCAAsC,UAAU,aAAa,OAAO,eAAe;AACjG,gBAAM,IAAI,MAAM,iCAAiC;AAAA;AAEnD,YAAI,YAAY,MAAM;AACpB,cAAI,MAAM,YAAY,UAAU;AAAA;AAAA;AAGpC,UAAI,MAAM,SAAU,SAAS,UAAU,OAAO;AAC5C,YAAI,MAAM,QAAQ;AAClB,oBAAY,KAAK,UAAU;AAAA;AAG7B,UAAI,cAAc,SAAU,SAAQ,QAAO;AACzC,iBAAS,QAAO,MAAM,OAAM;AAAA;AAE9B,UAAI,eAAe,SAAU,UAAU;AACrC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,sBAAY,SAAS,IAAI,IAAI,SAAS;AAAA;AAAA;AAG1C,UAAI,iBAAiB,SAAU,QAAQ,MAAM;AAC3C,cAAM,KAAK,SAAS,KAAK,OAAO;AAAA;AAElC,UAAI,gBAAgB,SAAU,OAAO,UAAU;AAC7C,YAAI,UAAU;AAAA,UACZ,MAAM,aAAa,QAAQ,UAAU;AAAA,UACrC,MAAM,aAAa,QAAQ,MAAM;AAAA;AAEnC,iBAAS,QAAQ,MAAM,QAAQ;AAC/B,eAAO;AAAA;AAET,UAAI,iBAAiB,SAAU,OAAO,OAAO,MAAM;AACjD,YAAI,WAAW;AACf,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,mBAAS,KAAK,cAAc,OAAO,MAAM;AAAA;AAE3C,eAAO;AAAA;AAET,UAAI,mBAAmB,SAAU,UAAU,OAAO;AAChD,iBAAS,IAAI,GAAG,IAAI,SAAS,SAAS,GAAG,KAAK;AAC5C,cAAI,SAAS,GAAG,MAAM,mBAAmB;AAAA;AAE3C,aAAK,UAAU,KAAK,SAAU,SAAS;AACrC,iBAAO,QAAQ,MAAM,MAAM;AAC3B,iBAAO,QAAQ,MAAM,MAAM;AAC3B,iBAAO,QAAQ,MAAM,MAAM;AAAA;AAAA;AAG/B,UAAI,mBAAmB,SAAU,SAAS,OAAO;AAC/C,YAAI,KAAK,QAAQ,UAAU,MAAM,UAAU;AACzC,kBAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM;AAAA;AAE5C,eAAO,QAAQ,MAAM,MAAM;AAAA;AAE7B,UAAI,aAAa,SAAU,OAAO,MAAM,SAAS;AAC/C,YAAI,OAAO,aAAa,QAAQ,MAAM;AACtC,eAAO,MAAM;AACb,eAAO,MAAM;AACb,eAAO;AAAA;AAET,UAAI,aAAa,SAAU,SAAS,MAAM;AACxC,iBAAS,QAAQ,MAAM;AACvB,gBAAQ,OAAO;AAAA;AAEjB,UAAI,eAAe,SAAU,OAAO,MAAM,OAAO;AAC/C,YAAI,UAAU,KAAK,MAAM,GAAG,MAAM;AAClC,aAAK,SAAS,KAAK,SAAU,SAAS;AACpC,cAAI,OAAO,WAAW,OAAO,MAAM,gBAAgB,MAAM;AACzD,qBAAW,SAAS;AACpB,2BAAiB,SAAS;AAAA;AAE5B,eAAO;AAAA;AAET,UAAI,YAAY,SAAU,OAAO,MAAM,OAAO;AAC5C,YAAI,WAAW,eAAe,OAAO,OAAO,MAAM,QAAQ,KAAK;AAC/D,qBAAa;AACb,yBAAiB,UAAU;AAC3B,uBAAe,MAAM;AACrB,eAAO,KAAK,OAAO;AAAA;AAErB,UAAI,cAAc,SAAU,OAAO,SAAS;AAC1C,YAAI,OAAO,MAAM,SAAS,SAAU,OAAM,OAAO;AAC/C,iBAAO,MAAM,QAAQ,MAAK,SAAS,UAAU,OAAO,OAAM,SAAS,aAAa,OAAO,OAAM;AAAA,WAC5F;AACH,eAAO,KAAK,MAAM,IAAI,SAAU,SAAS;AACvC,iBAAO,QAAQ;AAAA;AAAA;AAInB,UAAI,SAAS,SAAU,IAAI;AACzB,eAAO,KAAK,IAAI;AAAA;AAElB,UAAI,oBAAoB,SAAU,IAAI;AACpC,eAAO,WAAW,IAAI,OAAO;AAAA;AAE/B,UAAI,mBAAmB,SAAU,IAAI;AACnC,eAAO,UAAU,IAAI,OAAO;AAAA;AAG9B,UAAI,aAAa,SAAU,OAAO;AAChC,eAAO,MAAM,QAAQ;AAAA;AAEvB,UAAI,aAAa,SAAU,OAAO;AAChC,eAAO,MAAM;AAAA;AAEf,UAAI,mBAAmB,SAAU,IAAI;AACnC,YAAI,aAAa,SAAS;AAC1B,YAAI,UAAU,iBAAiB,MAAM,WAAW,MAAM,GAAG,MAAM;AAC/D,eAAO,IAAI,SAAS;AAAA;AAEtB,UAAI,cAAc,SAAU,IAAI,OAAO,aAAY;AACjD,eAAO,OAAO,IAAI,OAAO,WAAW,IAAI,SAAU,MAAM;AACtD,iBAAO;AAAA,YACL;AAAA,YACA,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,SAAS,iBAAiB;AAAA,YAC1B,gBAAgB,QAAQ;AAAA,YACxB,gBAAgB,QAAQ;AAAA,YACxB,UAAU,KAAK;AAAA;AAAA;AAAA;AAKrB,UAAI,cAAc,SAAU,aAAa,OAAO;AAC9C,gBAAQ;AAAA,eACH;AACH,kBAAM;AACN;AAAA,eACG;AACH,kBAAM;AACN;AAAA,eACG;AACH,kBAAM,QAAQ;AAAA;AAEhB,cAAM,QAAQ;AAAA;AAGhB,UAAI,sBAAsB,SAAU,QAAQ,QAAQ;AAClD,eAAO,WAAW,OAAO;AACzB,eAAO,iBAAiB,SAAS,IAAI,OAAO;AAAA;AAE9C,UAAI,sBAAsB,SAAU,OAAO;AACzC,cAAM,iBAAiB,OAAO,MAAM,gBAAgB,SAAU,QAAQ,KAAK;AACzE,iBAAO,QAAQ;AAAA;AAAA;AAGnB,UAAI,sBAAsB,SAAU,SAAS,OAAO;AAClD,YAAI,QAAQ,QAAQ,OAAO;AAC3B,YAAI,UAAU,SAAU,OAAO;AAC7B,iBAAO,MAAM,UAAU,SAAS,CAAC,MAAM;AAAA;AAEzC,YAAI,QAAQ,SAAU,OAAO;AAC3B,iBAAO,MAAM,QAAQ;AAAA;AAEvB,eAAO,UAAU,QAAQ,QAAQ,MAAM,GAAG,SAAS,SAAS,OAAO,QAAQ,WAAY;AACrF,iBAAO,UAAU,QAAQ,MAAM,QAAQ,IAAI,SAAS;AAAA;AAAA;AAGxD,UAAI,mBAAmB,SAAU,SAAS;AACxC,eAAO,SAAS,SAAU,OAAO,GAAG;AAClC,8BAAoB,SAAS,GAAG,KAAK,WAAY;AAC/C,gBAAI,MAAM,OAAO;AACf,kCAAoB;AAAA;AAAA,aAErB,SAAU,eAAe;AAC1B,mBAAO,oBAAoB,OAAO;AAAA;AAAA;AAGtC,eAAO;AAAA;AAGT,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,QAAQ;AACZ,YAAI,OAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,OAAM,SAAU,GAAG;AACrB,kBAAQ;AAAA;AAEV,eAAO;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA;AAAA;AAIT,UAAI,YAAY,SAAU,OAAO,eAAe,gBAAgB,MAAM;AACpE,eAAO,WAAW,MAAM,OAAO,QAAQ,KAAK,WAAY;AACtD,wBAAc,KAAK,SAAU,WAAW;AACtC,gBAAI,GAAG,UAAU,OAAO,OAAO;AAC7B,6BAAe,IAAI;AAAA;AAAA;AAGvB,cAAI,mBAAmB,YAAY,MAAM,OAAO,eAAe;AAC/D,wBAAc,KAAK,SAAU,WAAW;AACtC,gBAAI,GAAG,UAAU,KAAK,OAAO;AAC3B,6BAAe,IAAI;AAAA;AAAA;AAGvB,cAAI,mBAAmB,UAAU,MAAM,OAAO,QAAQ,IAAI,SAAU,MAAM;AACxE,mBAAO,UAAU,OAAO,eAAe,gBAAgB;AAAA,aACtD,MAAM;AACT,iBAAO,iBAAiB,UAAU,OAAO;AAAA,WACxC,SAAU,MAAM;AACjB,iBAAO,UAAU,OAAO,eAAe,gBAAgB;AAAA;AAAA;AAG3D,UAAI,YAAY,SAAU,OAAO,eAAe,gBAAgB,MAAM;AACpE,eAAO,KAAK,SAAS,OAAO,SAAU,SAAS;AAC7C,cAAI,SAAS,OAAO,WAAW,YAAY;AAC3C,cAAI,WAAW,QAAQ;AACvB,iBAAO,OAAO,UAAU,eAAe,gBAAgB;AAAA;AAAA;AAG3D,UAAI,aAAa,SAAU,OAAO,eAAe;AAC/C,YAAI,iBAAiB,KAAK;AAC1B,YAAI,eAAe;AACnB,eAAO,IAAI,OAAO,SAAU,MAAM;AAChC,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,SAAS,UAAU,cAAc,eAAe,gBAAgB;AAAA;AAAA;AAAA;AAKtE,UAAI,oBAAoB,SAAU,QAAQ,SAAS;AACjD,YAAI,oBAAoB,iBAAiB;AACzC,eAAO,IAAI,mBAAmB,SAAU,OAAO;AAC7C,cAAI,UAAU,aAAa,MAAM;AACjC,iBAAO,aAAa,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA;AAAA;AAGhE,UAAI,mBAAmB,SAAU,QAAQ,SAAS;AAChD,YAAI,oBAAoB,iBAAiB;AACzC,eAAO,YAAY,OAAO,iBAAiB,mBAAmB;AAAA;AAEhE,UAAI,iBAAiB,SAAU,QAAQ,SAAS;AAC9C,eAAO,KAAK,QAAQ,SAAS,aAAa,SAAU,UAAS;AAC3D,cAAI,kBAAkB,KAAK,UAAS,OAAO;AAC3C,iBAAO,kBAAkB,iBAAiB,QAAQ,YAAW,kBAAkB,QAAQ;AAAA;AAAA;AAG3F,UAAI,wBAAwB,SAAU,SAAS,aAAa;AAC1D,eAAO,SAAS,SAAS,aAAa,SAAU,OAAO;AACrD,iBAAO,YAAY,aAAa;AAAA;AAAA;AAGpC,UAAI,mBAAmB,SAAU,QAAQ;AACvC,YAAI,oBAAoB,IAAI,qBAAqB,SAAS,aAAa;AACvE,eAAO,MAAM,OAAO,mBAAmB,IAAI,qBAAqB,OAAO,QAAQ,oBAAoB,IAAI,qBAAqB,SAAU,OAAO,KAAK;AAChJ,iBAAO;AAAA,YACL;AAAA,YACA;AAAA;AAAA;AAAA;AAIN,UAAI,kBAAkB,SAAU,QAAQ,OAAO,aAAa;AAC1D,YAAI,YAAY,WAAW,OAAO,iBAAiB;AACnD,eAAO,WAAW,SAAU,UAAU;AACpC,gCAAsB,SAAS,SAAS;AACxC,cAAI,gBAAgB,eAAe,QAAQ,SAAS;AACpD,iBAAO,eAAe,SAAU,cAAc;AAC5C,0BAAc,QAAQ,gBAAgB,WAAW,eAAe,eAAe,aAAa;AAAA;AAE9F,iBAAO,SAAS,YAAY;AAC5B,iBAAO,SAAS;AAAA;AAAA;AAIpB,UAAI,uBAAuB,SAAU,QAAQ,aAAa;AACxD,YAAI,QAAQ,IAAI,qBAAqB,SAAS,aAAa;AAC3D,YAAI,UAAU,IAAI,mBAAmB,SAAS,aAAa;AAC3D,YAAI,YAAY;AAChB,YAAI,MAAM,UAAU,QAAQ,QAAQ;AAClC,cAAI,WAAW,OAAO,UAAU;AAChC,0BAAgB,QAAQ,OAAO;AAC/B,wBAAc,QAAQ,aAAa;AACnC,iBAAO,UAAU,eAAe;AAChC,iBAAO,UAAU,OAAO,eAAe,OAAO,UAAU;AACxD,iBAAO;AACP,sBAAY;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,qBAAqB,QAAQ;AAAA;AAEtC,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,qBAAqB,QAAQ;AAAA;AAEtC,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,qBAAqB,QAAQ;AAAA;AAGtC,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,QAAQ,SAAS;AACrB,UAAI,iBAAiB,SAAU,KAAK;AAClC,YAAI,WAAW;AACf,YAAI,gBAAgB,SAAU,OAAO;AACnC,cAAI,YAAY,IAAI,QAAQ,mBAAmB;AAC/C,cAAI,SAAS,IAAI,QAAQ,gBAAgB;AACzC,cAAI,UAAU,aAAa,GAAG;AAC5B,gBAAI,aAAa,MAAM,OAAO,QAAQ,EAAE,iBAAiB;AACzD,gBAAI,UAAU,iBAAiB;AAC7B,uBAAS,KAAK,IAAI,QAAQ,UAAU,WAAW,SAAS;AACxD,kBAAI,OAAO;AACT,0BAAU,aAAa,YAAY,UAAU,WAAW;AAAA,qBACnD;AACL,sBAAM,YAAY,YAAY,UAAU,WAAW;AAAA;AAAA,mBAEhD;AACL,wBAAU,YAAY;AAAA;AAExB,wBAAY;AACZ,qBAAS;AAAA;AAEX,mBAAS,QAAQ,mBAAmB,kBAAkB;AACtD,mBAAS,QAAQ,gBAAgB,eAAe;AAAA;AAElD,sBAAc;AACd,YAAI,CAAC,IAAI,WAAW;AAClB;AAAA;AAEF,eAAO;AAAA;AAET,UAAI,kBAAkB,SAAU,UAAU;AACxC,YAAI,kBAAkB,SAAU,OAAO;AACrC,cAAI;AACJ,cAAI,YAAY,SAAU,YAAW;AACnC,gBAAI,QAAO,WAAU,WAAW,YAAY,MAAM;AAClD,mBAAO,OAAM;AACX,kBAAI,UAAS,YAAW;AACtB,uBAAO;AAAA;AAET,kBAAI,MAAK,aAAa,KAAK,MAAK,aAAa,qBAAqB,YAAY;AAC5E;AAAA;AAEF,sBAAO,MAAK;AAAA;AAEd,mBAAO;AAAA;AAET,cAAI,YAAY,OAAO,SAAS,QAAQ,mBAAmB;AAC3D,cAAI,SAAS,SAAS,QAAQ,gBAAgB;AAC9C,cAAI,CAAC,WAAW;AACd;AAAA;AAEF,cAAI,UAAU,aAAa,GAAG;AAC5B,qBAAS,UAAU;AACnB,wBAAY,UAAU;AACtB,kBAAM,OAAO;AACb,gBAAI,CAAC,UAAU,mBAAmB,MAAM,QAAQ,YAAY;AAC1D,wBAAU,YAAY,MAAM,OAAO;AAAA;AAAA;AAGvC,mBAAS,QAAQ,mBAAmB,kBAAkB;AACtD,mBAAS,QAAQ,gBAAgB,eAAe;AAAA;AAElD,wBAAgB;AAChB;AACA,YAAI,MAAM,MAAM;AAChB,YAAI,SAAS,SAAS,gBAAgB,SAAS;AAC/C,YAAI,SAAS,cAAc;AACzB,cAAI,OAAO,SAAS,cAAc,SAAS;AAAA;AAE7C,eAAO,eAAe;AAAA;AAGxB,UAAI,+BAA+B,SAAU,UAAU;AACrD,gBAAQ;AAAA,eACH;AACH,mBAAO;AAAA,eACJ;AACH,mBAAO;AAAA,eACJ;AACH,mBAAO;AAAA;AAAA;AAIX,UAAI,eAAe,SAAU,MAAM;AACjC,eAAO,UAAU,KAAK,KAAK;AAAA;AAE7B,UAAI,YAAY,SAAU,QAAQ,UAAU,UAAU;AACpD,YAAI,oBAAoB,SAAU,GAAG;AACnC,cAAI,SAAS,UAAU,EAAE,SAAS,YAAY,iBAAiB,OAAO,SAAU,MAAM;AACpF,mBAAO,KAAK,aAAa,YAAY,CAAC,aAAa;AAAA,aAClD;AACH,mBAAS;AAAA;AAEX,YAAI,UAAU,OAAO,IAAI,WAAW,OAAO,UAAU;AACrD,0BAAkB,EAAE;AACpB,eAAO,GAAG,cAAc;AACxB,eAAO,WAAY;AACjB,iBAAO,OAAO,IAAI,cAAc;AAAA;AAAA;AAIpC,UAAI,kBAAkB,SAAU,KAAK,IAAI,QAAQ;AAC/C,YAAI,QAAO,OAAO,qBAAqB,OAAO,qBAAqB;AACnE,YAAI,SAAS,IAAI,mBAAmB;AAAA;AAEtC,UAAI,aAAa,SAAU,KAAK,OAAO;AACrC,iBAAS,KAAK,OAAO,SAAU,OAAO,KAAK;AACzC,cAAI,aAAa,KAAK;AAAA;AAAA;AAG1B,UAAI,kBAAkB,SAAU,KAAK,IAAI,QAAQ;AAC/C,mBAAW,IAAI,OAAO;AACtB,iBAAS,KAAK,IAAI,OAAO,MAAM,KAAK,SAAU,IAAI;AAChD,qBAAW,IAAI,OAAO;AAAA;AAAA;AAG1B,UAAI,wBAAwB,SAAU,KAAK,IAAI,QAAQ;AACrD,wBAAgB,KAAK,IAAI;AACzB,wBAAgB,KAAK,IAAI;AAAA;AAE3B,UAAI,eAAe,SAAU,KAAK,SAAS,QAAQ;AACjD,iBAAS,KAAK,QAAQ,SAAU,OAAO;AACrC,cAAI;AACJ,iBAAO,IAAI,SAAS,SAAU,MAAK,IAAI,GAAG,SAAS,IAAI;AAAA;AAAA;AAG3D,UAAI,kBAAkB,SAAU,QAAQ,KAAK,OAAO,MAAM;AACxD,YAAI,YAAY,IAAI,QAAQ,mBAAmB;AAC/C,YAAI,SAAS,IAAI,QAAQ,gBAAgB;AACzC,YAAI,UAAU,aAAa,GAAG;AAC5B,sBAAY,UAAU,WAAW,KAAK,IAAI,QAAQ,UAAU,WAAW,SAAS,OAAO;AAAA;AAEzF,YAAI,CAAC,SAAS,KAAK,UAAU,cAAc;AACzC,sBAAY,UAAU;AAAA;AAExB,eAAO,UAAU,eAAe,MAAM;AACpC,cAAI,YAAY,QAAQ,YAAY;AAClC,mBAAO;AAAA;AAET,cAAI,YAAY,KAAK,UAAU,WAAW,WAAW;AACnD,mBAAO;AAAA;AAET,sBAAY,UAAU;AAAA;AAExB,eAAO;AAAA;AAET,UAAI,wBAAwB,SAAU,QAAQ,KAAK,MAAM;AACvD,YAAI,aAAa;AACjB,YAAI,MAAM,OAAO;AACjB,YAAI,YAAY,gBAAgB,QAAQ,KAAK,MAAM;AACnD,YAAI,UAAU,gBAAgB,QAAQ,KAAK,OAAO;AAClD,YAAI;AACJ,YAAI,WAAW;AACf,iBAAS,OAAO,WAAW,MAAM,OAAO,KAAK,aAAa;AACxD,mBAAS,KAAK;AACd,cAAI,SAAS,SAAS;AACpB;AAAA;AAAA;AAGJ,iBAAS,KAAK,UAAU,SAAU,OAAM;AACtC,cAAI,YAAY,QAAQ,QAAO;AAC7B,uBAAW,KAAK;AAChB,oBAAQ;AACR;AAAA;AAEF,cAAI,IAAI,QAAQ,UAAS,KAAK,QAAO;AACnC,gBAAI,KAAK,QAAO;AACd,kBAAI,OAAO;AAAA;AAEb,oBAAQ;AACR;AAAA;AAEF,cAAI,cAAc,MAAK;AACvB,cAAI,OAAO,eAAe,QAAO;AAC/B,gBAAI,WAAW,gBAAgB,YAAY,QAAQ,gBAAgB,CAAC,eAAe,MAAK,eAAe,MAAM;AAC3G,sBAAQ;AACR;AAAA;AAAA;AAGJ,cAAI,CAAC,OAAO;AACV,oBAAQ,IAAI,OAAO;AACnB,kBAAK,WAAW,aAAa,OAAO;AACpC,uBAAW,KAAK;AAAA;AAElB,gBAAM,YAAY;AAAA;AAEpB,eAAO;AAAA;AAET,UAAI,qBAAqB,SAAU,KAAK,KAAK,QAAQ;AACnD,YAAI,WAAW,IAAI,SAAS,KAAK;AACjC,YAAI,cAAc,SAAS,OAAO,qBAAqB;AACvD,sBAAc,gBAAgB,OAAO,KAAK;AAC1C,eAAO,aAAa;AAAA;AAEtB,UAAI,YAAY,SAAU,QAAQ,UAAU,QAAQ;AAClD,YAAI,MAAM,OAAO,UAAU;AAC3B,YAAI,eAAe;AACnB,YAAI,OAAO,sBAAsB,QAAQ,OAAO,UAAU,SAAS;AACnE,YAAI,MAAM,OAAO;AACjB,YAAI,IAAI,mBAAmB,OAAO,UAAU,eAAe,SAAS;AAClE;AAAA;AAEF,mBAAW,SAAS;AACpB,YAAI,aAAa,MAAM;AACrB,yBAAe;AAAA;AAEjB,YAAI,WAAW,eAAe;AAC9B,YAAI,qBAAqB,sBAAsB,QAAQ,KAAK;AAC5D,iBAAS,KAAK,oBAAoB,SAAU,OAAO;AACjD,cAAI;AACJ,cAAI,UAAU,MAAM;AACpB,cAAI,UAAS,MAAM;AACnB,cAAI,CAAC,eAAe,UAAS;AAC3B,gBAAI,WAAW,WAAW,YAAY,QAAQ,aAAa,YAAY,mBAAmB,KAAK,SAAS,SAAS;AAC/G,0BAAY;AACZ,sBAAQ,IAAI,OAAO,OAAO;AAC1B,sBAAQ,YAAY;AAAA,mBACf;AACL,0BAAY,IAAI,OAAO;AACvB,oBAAM,WAAW,aAAa,WAAW;AACzC,wBAAU,YAAY;AACtB,sBAAQ,IAAI,OAAO,OAAO;AAAA;AAE5B,yBAAa,KAAK,OAAO;AAAA,cACvB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA;AAEF,kCAAsB,KAAK,WAAW;AACtC,mCAAuB,OAAO,KAAK;AAAA;AAAA;AAGvC,eAAO,UAAU,OAAO,gBAAgB;AAAA;AAE1C,UAAI,eAAe,SAAU,OAAO,OAAO;AACzC,eAAO,SAAS,SAAS,WAAW,UAAU,MAAM,aAAa,MAAM;AAAA;AAEzE,UAAI,mBAAmB,SAAU,KAAK,OAAO,OAAO;AAClD,YAAI,cAAc,IAAI,SAAS,OAAO,mBAAmB;AACzD,YAAI,QAAQ,IAAI,SAAS,OAAO,mBAAmB;AACnD,eAAO,gBAAgB;AAAA;AAEzB,UAAI,iBAAiB,SAAU,MAAM,MAAM;AACzC,eAAO,KAAK,cAAc,KAAK;AAAA;AAEjC,UAAI,cAAc,SAAU,KAAK,OAAO,OAAO;AAC7C,eAAO,aAAa,OAAO,UAAU,iBAAiB,KAAK,OAAO,UAAU,eAAe,OAAO;AAAA;AAEpG,UAAI,yBAAyB,SAAU,KAAK,WAAW;AACrD,YAAI,SAAS;AACb,kBAAU,UAAU;AACpB,YAAI,YAAY,KAAK,WAAW,UAAU;AACxC,iBAAO,OAAO,QAAQ,YAAY;AAChC,sBAAU,YAAY;AAAA;AAExB,cAAI,OAAO;AAAA;AAEb,kBAAU,UAAU;AACpB,YAAI,YAAY,KAAK,WAAW,UAAU;AACxC,iBAAO,OAAO,QAAQ,WAAW;AAC/B,sBAAU,aAAa,MAAM,UAAU;AAAA;AAEzC,cAAI,OAAO;AAAA;AAAA;AAGf,UAAI,eAAe,SAAU,QAAQ,MAAM,UAAU,QAAQ;AAC3D,YAAI,KAAK,aAAa,UAAU;AAC9B,cAAI,UAAU,OAAO,IAAI,OAAO,MAAM;AACtC,gCAAsB,OAAO,KAAK,SAAS;AAC3C,wBAAc,QAAQ,6BAA6B,WAAW;AAAA,eACzD;AACL,gCAAsB,OAAO,KAAK,MAAM;AACxC,wBAAc,QAAQ,6BAA6B,WAAW;AAAA;AAAA;AAGlE,UAAI,sBAAsB,SAAU,QAAQ,YAAY,OAAO,UAAU,QAAQ;AAC/E,YAAI,eAAe,WAAW;AAC9B,YAAI,gBAAgB,WAAW,aAAa,YAAY,CAAC,mBAAmB,SAAS;AACnF,+BAAqB;AAAA,eAChB;AACL,oBAAU,QAAQ,UAAU;AAC5B,cAAI,WAAW,eAAe,OAAO,UAAU;AAC/C,cAAI,WAAW,eAAe,cAAc,CAAC,aAAa,OAAO,QAAQ;AACzE,mBAAS,KAAK,UAAU,SAAU,KAAK;AACrC,yBAAa,QAAQ,KAAK,UAAU;AAAA;AAEtC,iBAAO,UAAU,OAAO,gBAAgB;AAAA;AAAA;AAG5C,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,qBAAqB;AAAA;AAE9B,UAAI,mBAAmB,SAAU,QAAQ,YAAY,UAAU,QAAQ;AACrE,YAAI,eAAe,OAAO,WAAW;AACnC;AAAA;AAEF,YAAI,YAAY;AACd,cAAI,WAAW,aAAa,YAAY,CAAC,mBAAmB,WAAW,CAAC,aAAa,aAAa;AAChG,iCAAqB;AAAA,iBAChB;AACL,gBAAI,WAAW,eAAe,OAAO,UAAU;AAC/C,kCAAsB,OAAO,KAAK,YAAY;AAC9C,gBAAI,UAAU,OAAO,IAAI,OAAO,YAAY;AAC5C,mCAAuB,OAAO,KAAK;AACnC,mBAAO,UAAU,OAAO,gBAAgB;AACxC,sBAAU,QAAQ,UAAU;AAC5B,0BAAc,QAAQ,6BAA6B,WAAW;AAAA;AAAA,eAE3D;AACL,oBAAU,QAAQ,UAAU;AAC5B,wBAAc,QAAQ,6BAA6B,WAAW;AAAA;AAAA;AAGlE,UAAI,aAAa,SAAU,QAAQ,UAAU,SAAS;AACpD,YAAI,aAAa,cAAc;AAC/B,YAAI,mBAAmB,oBAAoB;AAC3C,YAAI,SAAS,SAAS,WAAW,UAAU;AAC3C,YAAI,iBAAiB,SAAS,GAAG;AAC/B,8BAAoB,QAAQ,YAAY,kBAAkB,UAAU;AAAA,eAC/D;AACL,2BAAiB,QAAQ,YAAY,UAAU;AAAA;AAAA;AAInD,UAAI,MAAM,SAAS;AACnB,UAAI,gBAAgB,SAAU,KAAK,MAAM;AACvC,YAAI,aAAa,KAAK;AACtB,YAAI,WAAW,aAAa,QAAQ,WAAW,eAAe,MAAM;AAClE,cAAI,UAAU,WAAW;AACzB,cAAI,WAAW,QAAQ,aAAa,MAAM;AACxC,oBAAQ,YAAY;AACpB,gBAAI,QAAQ,KAAK,aAAa;AAC5B,kBAAI,OAAO;AAAA;AAAA,iBAER;AACL,gBAAI,SAAS,YAAY,iBAAiB;AAAA;AAAA;AAG9C,YAAI,WAAW,aAAa;AAC1B,cAAI,UAAU,WAAW;AACzB,cAAI,WAAW,QAAQ,aAAa,MAAM;AACxC,oBAAQ,YAAY;AAAA;AAAA;AAAA;AAI1B,UAAI,iBAAiB,SAAU,KAAK,SAAS;AAC3C,YAAI,QAAQ,SAAS,KAAK,IAAI,OAAO,SAAS;AAC9C,iBAAS,KAAK,OAAO,SAAU,MAAM;AACnC,wBAAc,KAAK;AAAA;AAAA;AAIvB,UAAI,yBAAyB,SAAU,QAAQ,KAAK,WAAW,MAAM;AACnE,YAAI,OAAO,IAAI;AACf,YAAI,SAAS,IAAI;AACjB,YAAI,WAAW,SAAU,aAAY,SAAS,KAAK,KAAK,SAAS,SAAS,IAAI;AAC5E,iBAAO;AAAA;AAET,YAAI,iBAAiB,OAAO,OAAO;AACnC,YAAI,KAAK,aAAa,GAAG;AACvB,iBAAO,SAAS,QAAQ,MAAM;AAAA;AAEhC,YAAI,SAAS,IAAI,SAAS,MAAM;AAChC,YAAI,WAAW;AACb,cAAI,UAAU,OAAO,KAAK,OAAO;AAC/B,mBAAO;AAAA;AAAA;AAGX,eAAO,OAAO,OAAO,YAAY,SAAS,YAAY;AACpD,cAAI,KAAK,aAAa,QAAQ,CAAC,KAAK,iBAAiB;AACnD,mBAAO;AAAA;AAET,cAAI,eAAe,KAAK,WAAW;AACjC,mBAAO;AAAA;AAET,cAAI,WAAW,SAAS,KAAK,KAAK,SAAS,GAAG;AAC5C,mBAAO;AAAA;AAAA;AAAA;AAIb,UAAI,uBAAuB,SAAU,KAAK,KAAK;AAC7C,YAAI,aAAa,IAAI;AACrB,eAAO,WAAW,WAAW,KAAK,CAAC,WAAW,WAAW,OAAO,IAAI,QAAQ,WAAW;AAAA;AAEzF,UAAI,yBAAyB,SAAU,KAAK,KAAK;AAC/C,YAAI,qBAAqB,KAAK,MAAM;AAClC,cAAI,OAAO,IAAI,YAAY;AAAA;AAAA;AAG/B,UAAI,eAAe,SAAU,KAAK,SAAS,OAAO;AAChD,YAAI;AACJ,YAAI,YAAY,qBAAqB,KAAK,SAAS,MAAM,aAAa;AACtE,+BAAuB,KAAK;AAC5B,YAAI,CAAC,QAAQ,KAAK,SAAS,OAAO;AAChC,iBAAO,OAAO,QAAQ,YAAY;AAChC,sBAAU,YAAY;AAAA;AAAA;AAAA;AAI5B,UAAI,kBAAkB,SAAU,KAAK,SAAS,OAAO;AACnD,YAAI;AACJ,YAAI,KAAK,QAAQ;AACjB,YAAI,CAAC,cAAc,KAAK,YAAY,CAAC,cAAc,KAAK,QAAQ;AAC9D;AAAA;AAEF,YAAI,WAAW,MAAM,YAAY;AAC/B,qBAAW,MAAM;AAAA;AAEnB,YAAI,OAAO,MAAM,WAAW;AAC1B,cAAI,KAAK,GAAG,kBAAkB;AAC5B,gBAAI,OAAO,GAAG;AAAA;AAAA;AAGlB,YAAI,OAAO,MAAM;AACjB,YAAI,QAAQ,KAAK,SAAS,QAAQ,iBAAiB;AACjD,cAAI,OAAO;AAAA;AAEb,YAAI,QAAQ,KAAK,OAAO,OAAO;AAC7B,cAAI,EAAE,OAAO;AAAA;AAEf,qBAAa,KAAK,SAAS;AAC3B,YAAI,UAAU;AACZ,gBAAM,YAAY;AAAA;AAEpB,YAAI,cAAa,SAAS,aAAa,QAAQ,QAAQ,aAAa,QAAQ;AAC5E,YAAI,cAAc,cAAa,IAAI,WAAW,SAAS,YAAY,SAAS;AAC5E,YAAI,OAAO;AACX,eAAO,aAAa,SAAU,MAAM;AAClC,cAAI,QAAQ,KAAK,SAAS,SAAS,IAAI,WAAW;AAChD,gBAAI,OAAO;AAAA;AAAA;AAAA;AAIjB,UAAI,mBAAmB,SAAU,QAAQ,QAAQ,MAAM;AACrD,eAAO,IAAI,EAAE,MAAM;AACnB,wBAAgB,OAAO,KAAK,QAAQ;AACpC,eAAO,UAAU,kBAAkB,MAAM;AAAA;AAE3C,UAAI,eAAe,SAAU,QAAQ,KAAK,QAAQ,MAAM;AACtD,YAAI,MAAM,OAAO;AACjB,YAAI,IAAI,QAAQ,OAAO;AACrB,2BAAiB,QAAQ,QAAQ;AAAA,eAC5B;AACL,cAAI,WAAW,eAAe;AAC9B,0BAAgB,KAAK,QAAQ;AAC7B,iBAAO,UAAU,OAAO,gBAAgB;AAAA;AAAA;AAG5C,UAAI,gBAAgB,SAAU,QAAQ,KAAK,QAAQ,MAAM;AACvD,YAAI,WAAW,eAAe;AAC9B,wBAAgB,OAAO,KAAK,QAAQ;AACpC,YAAI,mBAAmB,gBAAgB;AACvC,eAAO,UAAU,OAAO;AAAA;AAE1B,UAAI,qCAAqC,SAAU,QAAQ,WAAW;AACpE,YAAI,MAAM,OAAO,KAAK,YAAY,OAAO;AACzC,YAAI,oBAAoB,UAAU;AAClC,YAAI,OAAO,sBAAsB,QAAQ;AACzC,YAAI,KAAK,IAAI,UAAU,UAAU,YAAY,MAAM;AACnD,YAAI,IAAI;AACN,cAAI,KAAK,GAAG;AACZ,cAAI,OAAO,OAAO,aAAa,QAAQ,KAAK,KAAK;AAC/C,mBAAO;AAAA;AAET,cAAI,QAAQ,eAAe,UAAU;AACrC,cAAI,YAAY,IAAI,UAAU,uBAAuB,QAAQ,OAAO,WAAW,OAAO,MAAM;AAC5F,cAAI,aAAa,cAAc,IAAI;AACjC,mBAAO,YAAY,SAAS,WAAY;AACtC,kBAAI,WAAW;AACb,6BAAa,QAAQ,OAAO,WAAW;AAAA,qBAClC;AACL,oBAAI,aAAa,KAAK;AACpB,uCAAqB;AAAA,uBAChB;AACL,gCAAc,QAAQ,OAAO,IAAI;AAAA;AAAA;AAAA;AAIvC,mBAAO;AAAA,qBACE,CAAC,WAAW;AACrB,gBAAI,CAAC,aAAa,MAAM,gBAAgB,KAAK,MAAM,cAAc,GAAG;AAClE,qBAAO,YAAY,SAAS,WAAY;AACtC,qCAAqB;AAAA;AAEvB,qBAAO;AAAA;AAAA;AAAA;AAIb,eAAO;AAAA;AAET,UAAI,cAAc,SAAU,KAAK,OAAO,MAAM;AAC5C,YAAI,cAAc,IAAI,UAAU,MAAM,YAAY,IAAI,SAAS;AAC/D,YAAI,OAAO;AACX,YAAI,eAAe,IAAI,QAAQ,cAAc;AAC3C,cAAI,OAAO;AAAA;AAAA;AAGf,UAAI,+BAA+B,SAAU,QAAQ,WAAW;AAC9D,YAAI,MAAM,OAAO;AACjB,YAAI,oBAAoB,OAAO,UAAU;AACzC,YAAI,OAAO,sBAAsB,QAAQ;AACzC,YAAI,QAAQ,IAAI,UAAU,mBAAmB,IAAI,SAAS;AAC1D,YAAI,SAAS,IAAI,QAAQ,QAAQ;AAC/B,cAAI,MAAM,eAAe,OAAO,UAAU;AAC1C,cAAI,YAAY,IAAI,UAAU,uBAAuB,QAAQ,KAAK,WAAW,OAAO,MAAM;AAC1F,cAAI,WAAW;AACb,mBAAO,YAAY,SAAS,WAAY;AACtC,0BAAY,KAAK,OAAO;AACxB,qCAAuB,KAAK,UAAU;AACtC,qBAAO,UAAU,OAAO,WAAW;AACnC,qBAAO,UAAU,SAAS;AAAA;AAE5B,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,uBAAuB,SAAU,QAAQ,WAAW;AACtD,eAAO,mCAAmC,QAAQ,cAAc,6BAA6B,QAAQ;AAAA;AAEvG,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,YAAI,oBAAoB,OAAO,UAAU;AACzC,YAAI,OAAO,sBAAsB,QAAQ;AACzC,YAAI,kBAAkB,OAAO,IAAI,UAAU,mBAAmB,YAAY;AAC1E,YAAI,mBAAmB,qBAAqB,QAAQ,SAAS,GAAG;AAC9D,iBAAO,YAAY,SAAS,WAAY;AACtC,mBAAO,YAAY;AACnB,2BAAe,OAAO,KAAK,OAAO;AAAA;AAEpC,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,kBAAkB,SAAU,QAAQ,WAAW;AACjD,eAAO,OAAO,UAAU,gBAAgB,qBAAqB,QAAQ,aAAa,qBAAqB;AAAA;AAEzG,UAAI,UAAU,SAAU,QAAQ;AAC9B,eAAO,GAAG,WAAW,SAAU,GAAG;AAChC,cAAI,EAAE,YAAY,SAAS,WAAW;AACpC,gBAAI,gBAAgB,QAAQ,QAAQ;AAClC,gBAAE;AAAA;AAAA,qBAEK,EAAE,YAAY,SAAS,QAAQ;AACxC,gBAAI,gBAAgB,QAAQ,OAAO;AACjC,gBAAE;AAAA;AAAA;AAAA;AAAA;AAMV,UAAI,MAAM,SAAU,QAAQ;AAC1B,eAAO;AAAA,UACL,iBAAiB,SAAU,WAAW;AACpC,4BAAgB,QAAQ;AAAA;AAAA;AAAA;AAK9B,UAAI,aAAa,SAAU,QAAQ,QAAQ;AACzC,YAAI,aAAa,cAAc;AAC/B,eAAO,YAAY,SAAS,WAAY;AACtC,cAAI,SAAS,OAAO,SAAS;AAC3B,mBAAO,IAAI,UAAU,YAAY,OAAO;AAAA;AAE1C,cAAI,SAAS,OAAO,QAAQ;AAC1B,iBAAK,OAAO,OAAO,SAAU,GAAG,GAAG;AACjC,qBAAO,OAAO,IAAI,UAAU,YAAY,GAAG;AAAA;AAAA;AAAA;AAAA;AAMnD,UAAI,wBAAwB,SAAU,KAAK;AACzC,YAAI,QAAQ,QAAQ,KAAK,KAAK,MAAM;AACpC,YAAI,SAAS,IAAI,OAAO,SAAU,MAAM,GAAG;AACzC,cAAI,YAAY,KAAK,cAAc,WAAW,KAAK,IAAI,WAAW,KAAK;AACvE,iBAAO,KAAK,IAAI,IAAI,KAAK;AAAA;AAE3B,eAAO,MAAM,QAAQ,SAAU,KAAK,GAAG;AACrC,iBAAO,MAAM;AAAA,WACZ;AAAA;AAEL,UAAI,0BAA0B,SAAU,OAAO;AAC7C;AACA,YAAI,QAAQ,GAAG;AACb,iBAAO;AAAA,eACF;AACL,cAAI,YAAY,QAAQ;AACxB,cAAI,WAAW,KAAK,MAAM,QAAQ;AAClC,cAAI,OAAO,wBAAwB;AACnC,cAAI,OAAO,OAAO,aAAa,IAAI,WAAW,KAAK;AACnD,iBAAO,OAAO;AAAA;AAAA;AAGlB,UAAI,cAAc,SAAU,KAAK;AAC/B,eAAO,WAAW,KAAK;AAAA;AAEzB,UAAI,cAAc,SAAU,KAAK;AAC/B,eAAO,WAAW,KAAK;AAAA;AAEzB,UAAI,YAAY,SAAU,KAAK;AAC7B,eAAO,WAAW,KAAK;AAAA;AAEzB,UAAI,iBAAiB,SAAU,OAAO;AACpC,YAAI,UAAU,QAAQ;AACpB,iBAAO;AAAA,mBACE,YAAY,QAAQ;AAC7B,iBAAO;AAAA,mBACE,YAAY,QAAQ;AAC7B,iBAAO;AAAA,mBACE,UAAU,QAAQ;AAC3B,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,kBAAkB,SAAU,OAAO;AACrC,gBAAQ,eAAe;AAAA,eAClB;AACH,mBAAO,SAAS,KAAK;AAAA,cACnB,eAAe,SAAS;AAAA,cACxB;AAAA;AAAA,eAEC;AACH,mBAAO,SAAS,KAAK;AAAA,cACnB,eAAe,SAAS,KAAK;AAAA,cAC7B,OAAO,sBAAsB,OAAO;AAAA;AAAA,eAEnC;AACH,mBAAO,SAAS,KAAK;AAAA,cACnB,eAAe,SAAS,KAAK;AAAA,cAC7B,OAAO,sBAAsB,OAAO;AAAA;AAAA,eAEnC;AACH,mBAAO,SAAS,KAAK;AAAA,cACnB,eAAe,SAAS;AAAA,cACxB,OAAO;AAAA;AAAA,eAEN;AACH,mBAAO,SAAS;AAAA;AAAA;AAGpB,UAAI,cAAc,SAAU,QAAQ;AAClC,YAAI,QAAQ,SAAS,OAAO,OAAO;AACnC,YAAI,GAAG,OAAO,eAAe,gBAAgB;AAC3C,iBAAO,wBAAwB;AAAA,mBACtB,GAAG,OAAO,eAAe,gBAAgB;AAClD,iBAAO,wBAAwB,OAAO;AAAA,eACjC;AACL,iBAAO,OAAO;AAAA;AAAA;AAIlB,UAAI,OAAO,SAAU,QAAQ;AAC3B,YAAI,cAAc,cAAc;AAChC,YAAI,CAAC,SAAS,cAAc;AAC1B;AAAA;AAEF,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,WAAW;AAAA;AAAA;AAAA,UAGjB,aAAa;AAAA,YACX,OAAO,YAAY;AAAA,cACjB,OAAO,OAAO,IAAI,UAAU,aAAa,SAAS;AAAA,cAClD,eAAe,SAAS,KAAK,OAAO,IAAI,SAAS,aAAa;AAAA;AAAA;AAAA,UAGlE,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA;AAAA,YAER;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,UAGb,UAAU,SAAU,KAAK;AACvB,gBAAI,OAAO,IAAI;AACf,4BAAgB,KAAK,OAAO,KAAK,SAAU,QAAQ;AACjD,qBAAO,YAAY,iBAAiB,OAAO;AAAA,gBACzC,OAAO,EAAE,OAAO,OAAO,UAAU,MAAM,KAAK,OAAO;AAAA,gBACnD,QAAQ,EAAE,mBAAmB,OAAO,cAAc,MAAM;AAAA;AAAA;AAG5D,gBAAI;AAAA;AAAA;AAAA;AAKV,UAAI,wBAAwB,SAAU,QAAQ,UAAU;AACtD,eAAO,WAAY;AACjB,cAAI,aAAa,cAAc;AAC/B,iBAAO,cAAc,WAAW,aAAa;AAAA;AAAA;AAGjD,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,WAAW,gBAAgB,WAAY;AAC5C,eAAK;AAAA;AAAA;AAGT,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,GAAG,qBAAqB,SAAU,GAAG;AAC1C,cAAI,MAAM,EAAE,QAAQ;AACpB,cAAI,QAAQ,UAAU;AACpB,gCAAoB;AAAA,qBACX,QAAQ,WAAW;AAC5B,iCAAqB;AAAA;AAAA;AAGzB,eAAO,WAAW,uBAAuB,SAAU,IAAI,QAAQ;AAC7D,qBAAW,QAAQ,MAAM;AAAA;AAE3B,eAAO,WAAW,qBAAqB,SAAU,IAAI,QAAQ;AAC3D,qBAAW,QAAQ,MAAM;AAAA;AAE3B,eAAO,WAAW,wBAAwB,SAAU,IAAI,QAAQ;AAC9D,qBAAW,QAAQ,MAAM;AAAA;AAE3B,eAAO,WAAW,cAAc,WAAY;AAC1C,+BAAqB;AAAA;AAEvB,uBAAe;AACf,eAAO,WAAW,iBAAiB,SAAU,IAAI,QAAQ;AACvD,cAAI,SAAS,SAAS;AACpB,uBAAW,QAAQ;AAAA;AAAA;AAGvB,eAAO,qBAAqB,uBAAuB,sBAAsB,QAAQ;AACjF,eAAO,qBAAqB,qBAAqB,sBAAsB,QAAQ;AAC/E,eAAO,qBAAqB,wBAAwB,sBAAsB,QAAQ;AAAA;AAGpF,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,GAAG,WAAW,SAAU,GAAG;AAChC,cAAI,EAAE,YAAY,SAAS,OAAO,SAAS,eAAe,IAAI;AAC5D;AAAA;AAEF,iBAAO,YAAY,SAAS,WAAY;AACtC,gBAAI,EAAE,WAAW,qBAAqB,UAAU,oBAAoB,SAAS;AAC3E,gBAAE;AAAA;AAAA;AAAA;AAAA;AAKV,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI,kBAAkB,SAAS;AAC7B,sBAAY;AAAA;AAEd,gBAAQ;AAAA;AAGV,UAAI,aAAa,SAAU,QAAQ;AACjC,YAAI,OAAO,SAAU,SAAS;AAC5B,iBAAO,WAAY;AACjB,mBAAO,OAAO,YAAY;AAAA;AAAA;AAG9B,YAAI,CAAC,OAAO,UAAU,YAAY;AAChC,iBAAO,GAAG,SAAS,gBAAgB,WAAW;AAAA,YAC5C,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,UAAU,KAAK;AAAA,YACf,SAAS,SAAU,KAAK;AACtB,qBAAO,UAAU,QAAQ,MAAM,IAAI;AAAA;AAAA;AAGvC,iBAAO,GAAG,SAAS,gBAAgB,WAAW;AAAA,YAC5C,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,UAAU,KAAK;AAAA,YACf,SAAS,SAAU,KAAK;AACtB,qBAAO,UAAU,QAAQ,MAAM,IAAI;AAAA;AAAA;AAAA;AAAA;AAM3C,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,iBAAiB;AAAA,UACnB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA,UAE5B,SAAS,SAAU,KAAK;AACtB,mBAAO,UAAU,QAAQ,MAAM,SAAU,QAAQ;AAC/C,qBAAO,IAAI,YAAY,CAAC;AAAA;AAAA;AAAA;AAI9B,eAAO,GAAG,SAAS,YAAY,aAAa;AAC5C,eAAO,GAAG,SAAS,eAAe,SAAS;AAAA,UACzC,QAAQ,SAAU,MAAM;AACtB,gBAAI,aAAa,cAAc,QAAQ;AACvC,mBAAO,SAAS,cAAc,CAAC,eAAe;AAAA;AAAA;AAAA;AAKpD,wBAAmB;AACjB,iBAAS,IAAI,SAAS,SAAU,QAAQ;AACtC,cAAI,OAAO,UAAU,OAAO,UAAU,OAAO;AAC3C,kBAAM;AACN,uBAAW;AAAA,iBACN;AACL,2BAAe;AAAA;AAEjB,qBAAW;AACX,mBAAS;AACT,iBAAO,IAAI;AAAA;AAAA;AAIf;AAAA;AAAA;AAAA;;;ACtwEJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,gCAAQ;", "names": []}