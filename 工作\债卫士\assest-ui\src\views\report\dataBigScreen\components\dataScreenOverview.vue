<template>
    <div class="data-screen-overview">
        <div class="data-screen-overview-title">{{ title }}</div>
        <slot>
            <div class="data-screen-overview-info-list">
                <div class="data-screen-overview-info-item" v-for="v in infoList" :key="v">
                    <div class="item-label">{{ v.label }}</div>
                    <div class="item-value">{{ infoForm[v.prop] || '--' }}&nbsp;万</div>
                </div>
            </div>
        </slot>
    </div>
</template>

<script setup>
const props = defineProps({
    title: { type: String, default: '电催团队机构概况' },
    infoList: {
        type: Array, default: [
            { label: '委案金额', prop: '' },
            { label: '户数', prop: '' },
            { label: '委案本金', prop: '' },
            { label: '回款金额', prop: '' },
        ]
    },
    infoForm: { type: Object, default: {} }
})
</script>

<style lang="scss" scoped>
.data-screen-overview {
    color: #DAE3F6;
    position: relative;
    border-width: 50px 0 30px 0px;
    border-style: solid;
    border-image-source: url(@/assets/images/dataBigScreen/screen-overview-bg.png);
    border-image-slice: 90 0 70 0 fill;
    border-image-repeat: stretch;

    &::after {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        right: 0;
        bottom: -18px;
        z-index: 2;
        border: 5px solid transparent;
        border-right-color: #D6F1FF;
        border-bottom-color: #D6F1FF;
    }

    .data-screen-overview-title {
        position: absolute;
        top: -42px;
        left: 35px;
        font-size: 20px;
        font-weight: bold;
    }

    .data-screen-overview-info-list {
        display: flex;
        margin-top: 20px;

        div {
            flex: 1;
            text-align: center;
        }

        .item-label {
            font-size: 14px;
        }

        .item-value {
            color: #38E1FF;
            font-size: 18px;
            font-weight: bold;
            padding: 8px 0 0;
        }
    }

}
</style>