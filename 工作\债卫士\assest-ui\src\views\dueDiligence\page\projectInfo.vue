<template>
    <!-- 主容器，带有加载状态 -->
    <div class="app-container" v-loading="loading">
        <!-- 项目信息卡片，当页面类型不是 startNapeList 时显示 -->
        <el-card class="mb20" v-if="!['startNapeList'].includes(pageType)">
            <!-- 项目标题和状态标签 -->
            <div class="title">项目名称：收购东莞银行南城分行2024-11号资产包项目 <el-tag type="primary">收购成功</el-tag></div>
            <!-- 项目基本信息列表 -->
            <div class="sub-title-list">
                <!-- 项目ID -->
                <div class="sub-title-item">项目ID：dg202411110001</div>
                <!-- 申请时间 -->
                <div class="sub-title-item">申请时间： 2024-11-12 12:00:00</div>
            </div>
        </el-card>

        <!-- 项目进展卡片 -->
        <el-card class="mb20">
            <div class="title">项目进展</div>
            <!-- 进展步骤列表 -->
            <div class="progress-list">
                <div :class="`progress-item ${i == progressStatus && 'active'}`" v-for="(v, i) in progressList"
                    :key="v">
                    {{ v.label }}
                </div>
            </div>
        </el-card>

        <!-- 主要内容区域 -->
        <div class="pay-info">
            <!-- 左侧主要内容卡片 -->
            <el-card>
                <!-- 标签页切换 -->
                <el-tabs v-model="activeTab" @tab-change="handleChangeTab">
                    <el-tab-pane v-for="v in showFormInfo.tabList" :key="v" :label="v.label" :name="v.value" />
                </el-tabs>

                <!-- 标题和操作按钮区域 -->
                <div class="title-area">
                    <div class="title">{{ showFormInfo.title }}</div>
                    <el-button type="primary" v-if="showFormInfo.btnName" @click="handAdd()">
                        {{ showFormInfo.btnName }}
                    </el-button>
                </div>

                <!-- 表单区域 -->
                <el-form inline label-width="auto" ref="formRef">

                    <el-row :getter="24">
                        <!-- 遍历表单字段配置 -->
                        <el-col v-for="v in showFormListFun(showFormInfo.formList || [])" :key="v" :span="v.span">
                            <!-- 表单项 -->
                            <el-form-item :label="v.label" :prop="v.prop">
                                <!-- 字段容器 -->
                                <div style="width:1000px;">
                                    <!-- 详情模式：只显示文本 -->
                                    <div v-if="isDetails == 1"> {{ form[v.prop] }}</div>
                                    <!-- 编辑模式：显示表单控件 -->
                                    <span v-if="isDetails == 0">
                                        <!-- 文本输入框 -->
                                        <el-input v-model="form[v.prop]" v-if="v.type == 'input'" clearable filterable
                                            style="width: 22vw;" :placeholder="`请输入${v.label}`" />
                                        <!-- 多行文本输入框 -->
                                        <el-input v-model="form[v.prop]" v-if="v.type == 'textarea'" type="textarea"
                                            maxlength="500" style="width: 60vw" show-word-limit rows="4" clearable
                                            filterable :placeholder="`请输入${v.label}`" />
                                        <!-- 下拉选择框 -->
                                        <el-select v-model="form[v.prop]" v-if="v.type == 'select'" clearable filterable
                                            :placeholder="`请选择${v.label}`" style="width: 22vw;">
                                            <!-- 下拉选项 -->
                                            <el-option v-for="v in optionInfo[v.prop] | []" :key="v" :label="v.label"
                                                :value="v.value" />
                                        </el-select>
                                        <!-- 单选按钮组 -->
                                        <el-radio-group v-model="form[v.prop]" v-if="v.type == 'radio'">
                                            <!-- 遍历是否枚举选项 -->
                                            <el-radio v-for="v in isNoEnum" :label="v" :name="v">{{ v }}</el-radio>
                                        </el-radio-group>
                                    </span>
                                </div>
                            </el-form-item>
                        </el-col>

                     
                        <!-- 文件展示-->
                        <el-col :span="24" v-if="isDetails == 1" v-for="fileItem in fileInfo[pageType] || []" :key="fileItem.prop">
                            <el-form-item :label="v.label" :prop="v.prop">
                                <div class="file-list">
                                    <div class="file-item" v-for="v in form[v.prop] || []" :key="v">
                                        <div class="file-item-name">{{ v.fileName }}</div>
                                        <el-button type="text">下载</el-button>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                           <!-- 文件上传区域 -->
                         <el-col :span="24" v-if="isDetails == 0">
                            <el-form-item label="立项报告" prop="startNapeList_feasibilityReport">
                                <div class="file-upload-container">
                                    <div class="upload-component">
                                        <FileUpload
                                            ref="projectApprovalFileUpload"
                                            v-model:fileList="projectApprovalFileList"
                                            uploadFileUrl="/file/upload"
                                            :fileType="fileType"
                                            :limit="10"
                                            :fileSize="10"
                                        />
                                    </div>
                                    <!-- 文件上传提示信息 -->
                                    <div class="upload-tips">
                                        <div class="report-tig">(1)上传凭证格式：支持上传JPG，png,pdf,xlsx,docx,doc格式。</div>
                                        <div class="report-tig">(2)文件数量：单次最多支持上传10个文件。</div>
                                        <div class="report-tig">(3)文件大小：单个文件限制10M以内。</div>
                                    </div>
                                </div>
                            </el-form-item>
                            <el-form-item label="资产评估表" prop="startNapeList_fileList">
                                <div class="file-upload-container">
                                    <div class="upload-component">
                                        <FileUpload
                                            ref="asetEvaluationFormFileUpload"
                                            v-model:fileList="asetEvaluationFormFileList"
                                            uploadFileUrl="/file/upload"
                                            :fileType="fileType"
                                            :limit="10"
                                            :fileSize="10"
                                        />
                                    </div>
                                    <!-- 文件上传提示信息 -->
                                    <div class="upload-tips">
                                        <div class="report-tig">(1)上传凭证格式：支持上传JPG，png,pdf,xlsx,docx,doc格式。</div>
                                        <div class="report-tig">(2)文件数量：单次最多支持上传10个文件。</div>
                                        <div class="report-tig">(3)文件大小：单个文件限制10M以内。</div>
                                    </div>
                                </div>
                            </el-form-item>
                            <el-form-item label="其他附件" prop="startNapeList_ohterFileList">
                                <div class="file-upload-container">
                                    <div class="upload-component">
                                        <FileUpload
                                            ref="otherFileUpload"
                                            v-model:fileList="otherFileList"
                                            uploadFileUrl="/file/upload"
                                            :fileType="fileType"
                                            :limit="10"
                                            :fileSize="10"
                                        />
                                    </div>
                                    <!-- 文件上传提示信息 -->
                                    <div class="upload-tips">
                                        <div class="report-tig">(1)上传凭证格式：支持上传JPG，png,pdf,xlsx,docx,doc格式。</div>
                                        <div class="report-tig">(2)文件数量：单次最多支持上传10个文件。</div>
                                        <div class="report-tig">(3)文件大小：单个文件限制10M以内。</div>
                                    </div>
                                </div>
                            </el-form-item>
                         </el-col>

                    </el-row>
                </el-form>

                <!-- 数据表格区域 -->
                <div>
                    <!-- 表格标题 -->
                    <div class="title">{{ showFormInfo.tableTitle }}</div>
                    <!-- 数据表格，根据配置显示 -->
                    <el-table :data="dataList" v-loading="loading" v-if="showFormInfo.tableList">
                        <!-- 动态生成表格列 -->
                        <el-table-column v-for="v in showFormInfo.tableList" :label="v.label" :prop="v.prop"
                            align="center" :min-width="v.width" />
                    </el-table>
                </div>

                <!-- 操作按钮区域 -->
                <div class="text-center mt20">
                    <!-- 编辑模式下的保存和提交按钮 -->
                    <Fragment v-if="isDetails == 0">
                        <!-- 保存按钮 -->
                        <el-button @click="toBack()">保存</el-button>
                        <!-- 提交按钮 -->
                        <el-button @click="toBack()" type="primary">提交</el-button>
                    </Fragment>
                    <!-- 返回按钮 -->
                    <el-button class="ml15" @click="toBack()">返回</el-button>
                </div>
            </el-card>

            <!-- 右侧审批进度卡片 -->
            <el-card class="right-card" v-if="isShowPress">
                <!-- 审批进度标题 -->
                <div class="sub-title-list">
                    <div class="sub-title-item"> 审批进度</div>
                </div>
                <!-- 审批信息 -->
                <div class="content-list">
                    <!-- 流程名称 -->
                    <div class="content-item">流程名称：{{ form[formInfo.titleProp] }}</div>
                    <!-- 审批单号（硬编码） -->
                    <div class="content-item">审批单号：32222224353</div>
                </div>
                <!-- 审批时间线 -->
                <el-timeline style="max-width: 600px">
                    <!-- 遍历时间线项目 -->
                    <el-timeline-item v-for="(v) in timelineList" :key="v" type="primary" hollow>
                        <div>
                            <!-- 审批状态 -->
                            <div class="timeline-title"> {{ v.status }}</div>
                            <!-- 审批人和时间 -->
                            <div class="timeline-content mb5"> {{ v.name }}&nbsp;{{ v.timestamp }}</div>
                            <!-- 审批意见 -->
                            <div class="timeline-content"> {{ v.content }}</div>
                        </div>
                    </el-timeline-item>
                </el-timeline>
            </el-card>
        </div>
    </div>
</template>

<script setup>
import virtualData from './virtualData'
import { isNoEnum } from '@/utils/enum';
import FileUpload from '@/components/FileUpload';
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const dataList = ref(virtualData?.dataList || [])
const optionInfo = ref({})
const activeTab = ref('0')
const progressStatus = ref('0')
const isDetails = ref(1)

const pageType = ref('conference')
const data = reactive({
    form: virtualData?.formInfo || {},
    rule: {},
})
const progressList = ref([
    { label: '立项', value: '' },
    { label: '项目方案审批', value: '' },
    { label: '收购中', value: '' },
    { label: '项目建账', value: '' },
])

const formInfo = ref({
    projectAccounting:
    {
        title: '新增支付建账申请',
        detailsTitle: '支付建账详情',
        btnName: '新增建账',
        tabList: [
            { label: '支付建账', value: '0', prop: '0' },
        ],
        titleProp: 'projectAccounting_processTitle',
        formList: [
            // { label: '流程标题', prop: 'projectAccounting_processTitle', span: 12, type: 'input' },
            { label: '付款单申请流水号', prop: 'projectAccounting_serialNo', span: 12, type: 'input' },
            { label: '付款单状态', prop: 'projectAccounting_payStatus', span: 12, type: 'select' },
            { label: '付款类型', prop: 'projectAccounting_payType', span: 12, type: 'select' },
            { label: '收款账号', prop: 'projectAccounting_accountNum', span: 12, type: 'input' },
            { label: '账户名', prop: 'projectAccounting_accountName', span: 12, type: 'input' },
            { label: '收款人', prop: 'projectAccounting_accountName', span: 12, type: 'input' },
            { label: '开户行', prop: 'projectAccounting_openingBank', span: 12, type: 'input' },
            { label: '合同金额', prop: 'projectAccounting_contractMoney', span: 12, type: 'input' },
            { label: '申请金额', prop: 'projectAccounting_applayMoney', span: 12, type: 'input' },
            { label: '备注', prop: 'projectAccounting_remarks', span: 24, type: 'textarea' },
        ],
        tableTitle: '支付建账列表',
        titleProp: 'projectAccounting_processTitle',
        tableList: [
            { label: "流程标题", prop: 'projectAccounting_processTitle', width: 120 },
            { label: "建账状态", prop: 'projectAccounting_accountStatus', width: 120 },
            { label: "付款单申请流水号", prop: 'projectAccounting_paymentRequestSerialNo', width: 120 },
            { label: "付款单状态", prop: 'projectAccounting_paymentOrderStatus', width: 120 },
            { label: "付款类型", prop: 'projectAccounting_paymentType', width: 120 },
            { label: "收款账号", prop: 'projectAccounting_receivingAccount', width: 120 },
            { label: "账户名", prop: 'projectAccounting_accountName', width: 120 },
            { label: "收款人", prop: 'projectAccounting_recipient', width: 120 },
            { label: "开户行", prop: 'projectAccounting_openingBank', width: 120 },
            { label: "合同金额", prop: 'projectAccounting_contractAmount', width: 120 },
            { label: "申请金额", prop: 'projectAccounting_applicationAmount', width: 120 },
            { label: "备注", prop: 'projectAccounting_remarks', width: 120 },
            { label: "申请人", prop: 'projectAccounting_applicant', width: 120 },
            { label: "申请时间", prop: 'projectAccounting_applicationTime', width: 120 },
            { label: "附件", prop: 'projectAccounting_attachment', width: 120 },
        ]
    },
    contract: {
        title: '新增合同',
        detailsTitle: '合同详情',
        btnName: '新增合同',
        tabList: [
            { label: '会议决策', value: '0', prop: 'conference' },
            { label: '竞价收购', value: '1', prop: 'bidding' },
            { label: '合同发起', value: '2', prop: 'contract' },
        ],
        titleProp: 'contract_processTitle',
        formList: [
            { label: '流程标题', prop: 'contract_processTitle', span: 12, type: 'input' },
            { label: '合同名称', prop: 'contract_contractName', span: 12, type: 'input' },
            { label: '合同编号', prop: 'contract_contractNo', span: 12, type: 'input' },
            { label: '合同类型', prop: 'contract_contractType', span: 12, type: 'input' },
            { label: '合同状态', prop: 'contract_contractStatus', span: 12, type: 'select' },
            { label: '发起方式', prop: 'contract_initiateWay', span: 12, type: 'select' },
            { label: '签署份数', prop: 'contract_signNum', span: 12, type: 'input' },
            { label: '印章类型', prop: 'contract_seal', span: 12, type: 'select' },
            // { label: '备注', prop: 'contract_remarks', span: 24, type: 'textarea' },
        ],
        tableTitle: '合同列表',
        tableList: [
            { label: "合同名称", prop: 'contract_contractName', width: 120 },
            { label: "合同编号", prop: 'contract_contractNumber', width: 120 },
            { label: "合同类型", prop: 'contract_contractType', width: 120 },
            { label: "合同状态", prop: 'contract_contractStatus', width: 120 },
            { label: "发起方式", prop: 'contract_initiationMethod', width: 120 },
            { label: "签署份数", prop: 'contract_signatureCopies', width: 120 },
            { label: "印章类型", prop: 'contract_sealType', width: 120 },
            { label: "备注", prop: 'contract_remarks', width: 120 },
            { label: "创建人", prop: 'contract_creator', width: 120 },
            { label: "创建时间", prop: 'contract_creationTime', width: 120 },
            { label: "附件", prop: 'contract_attachment', width: 120 },
        ]
    },
    conference: {
        title: '新增会议决策',
        detailsTitle: '会议决策',
        btnName: '添加会议决策',
        tabList: [
            { label: '会议决策', value: '0', prop: 'conference' },
            { label: '竞价收购', value: '1', prop: 'bidding' },
            { label: '合同发起', value: '2', prop: 'contract' },
        ],
        titleProp: 'conference_processTitle',
        formList: [
            { label: '是否通过', prop: 'conference_whetherPass', span: 12, type: 'radio' },
            // { label: '流程标题', prop: 'conference_processTitle', span: 12, type: 'input' },
            { label: '决策会议', prop: 'conference_decisionMeeting', span: 12, type: 'input' },
            { label: '决策时间', prop: 'conference_decisionTime', span: 12, type: 'select' },
            { label: '决策文号', prop: 'conference_decisionNo', span: 12, type: 'input' },
            { label: '决策结果', prop: 'conference_decisionResult', span: 12, type: 'select' },
            { label: '备注', prop: 'conference_remarksk', span: 24, type: 'textarea' },
        ],
        tableTitle: '会议列表',
        tableList: [
            { label: "会议标题", prop: 'conference_meetingTitle', width: 120 },
            { label: "决策时间", prop: 'conference_decisionTime', width: 120 },
            { label: "决策文号", prop: 'conference_decisionNumber', width: 120 },
            { label: "决策结果", prop: 'conference_creator', width: 120 },
            { label: "备注", prop: 'conference_remarks', width: 120 },
            { label: "创建人", prop: 'conference_isApproved', width: 120 },
            { label: "创建时间", prop: 'conference_creationTime', width: 120 },
            { label: "附件", prop: 'conference_attachment', width: 120 },
        ]
    },
    bidding: {
        title: '新增竞价',
        detailsTitle: '竞价详情',
        btnName: '新增竞价申请',
        tabList: [
            { label: '会议决策', value: '0', prop: 'conference' },
            { label: '竞价收购', value: '1', prop: 'bidding' },
            { label: '合同发起', value: '2', prop: 'contract' },
        ],
        titleProp: 'bidding_processTitle',
        formList: [
            { label: '流程标题', prop: 'bidding_processTitle', span: 12, type: 'input' },
            { label: '申请人', prop: 'bidding_applayBy', span: 12, type: 'input', isDetails: 1 },
            { label: '申请部门', prop: 'bidding_applayDept', span: 12, type: 'input', isDetails: 1 },
            { label: '项目名称', prop: 'bidding_projectName', span: 12, type: 'input' },
            { label: '产品类型', prop: 'bidding_produceType', span: 12, type: 'input' },
            { label: '投标方式', prop: 'bidding_biddingMethod', span: 12, type: 'select' },
            { label: '报价金额', prop: 'bidding_quotationAmount', span: 12, type: 'input' },
            { label: '是否用印', prop: 'bidding_whetherSeal', span: 12, type: 'radio' },
            { label: '印章类型', prop: 'bidding_SealType', span: 12, type: 'select' },
            { label: '备注', prop: 'bidding_remarks', span: 24, type: 'textarea' },
        ],
        tableTitle: '竞价列表',
        tableList: [
            { label: "流程标题", prop: 'bidding_processTitle', width: 120 },
            { label: "项目名称", prop: 'bidding_projectName', width: 120 },
            { label: "竞价状态", prop: 'bidding_biddingStatus', width: 120 },
            { label: "产品类型", prop: 'bidding_productType', width: 120 },
            { label: "投标方式", prop: 'bidding_biddingMethod', width: 120 },
            { label: '报价金额', prop: 'bidding_bidAmount', width: 120 },
            { label: '是否用印', prop: 'bidding_isSealed', width: 120 },
            { label: '印章类型', prop: 'bidding_sealType', width: 120 },
            { label: '备注', prop: 'bidding_remarks', width: 120 },
            { label: '申请人', prop: 'bidding_applicant', width: 120 },
            { label: '申请部门', prop: 'bidding_department', width: 120 },
            { label: '申请时间', prop: 'bidding_applicationTime', width: 120 },
            { label: '附件', prop: 'bidding_attachment', width: 120 },
        ]
    },
    startNapeList: {
        title: '基本信息',
        detailsTitle: '新增支付建账申请',
        tabList: [
            { label: '项目立项', value: '0', prop: '0' },
        ],
        titleProp: 'startNapeList_processTitle',
        formList: [
            { label: '流程标题', prop: 'startNapeList_processTitle', span: 24, type: 'input' },
            { label: '项目名称', prop: 'startNapeList_projectName', span: 12, type: 'input' },
            { label: '项目ID', prop: 'startNapeList_projectId', span: 12, type: 'input' },
            { label: '资产转让方', prop: 'startNapeList_transferor', span: 12, type: 'select' },
            { label: '产品类型', prop: 'startNapeList_produceType', span: 12, type: 'select' },
            { label: '债权总金额', prop: 'startNapeList_entrustMoney', span: 12, type: 'input' },
            { label: '债权本金（元）', prop: 'startNapeList_entrustPrincipal', span: 12, type: 'input' },
            { label: '债权本息（元）', prop: 'startNapeList_entrustInterest', span: 12, type: 'input' },
            { label: '户数', prop: 'startNapeList_households', span: 12, type: 'input' },
            { label: '是否需要保证金', prop: 'startNapeList_whetherPayDeposit', span: 12, type: 'radio' },
            { label: '保证金额（元）', prop: 'startNapeList_payDeposit', span: 12, type: 'input' },
            { label: '基准日', prop: 'startNapeList_baseDate', span: 12, type: 'select' },
            { label: '预计竞价日期', prop: 'startNapeList_biddingDate', span: 12, type: 'select' },
            { label: '投标方式', prop: 'startNapeList_biddingMethod', span: 12, type: 'select' },
            { label: '报价上限（元）', prop: 'startNapeList_ouotationCeiling', span: 12, type: 'input' },
            { label: '项目主办', prop: 'startNapeList_projectSponsor', span: 12, type: 'select' },
            { label: '项目承办', prop: 'startNapeList_projectUndertaking', span: 12, type: 'select' },
            { label: '申请人', prop: 'startNapeList_applayBy', span: 12, type: 'select', isDetails: 1 },
            { label: '申请时间', prop: 'startNapeList_applayTime', span: 12, type: 'select', isDetails: 1 },
        ]
    },
    schemeApproval: {
        title: '基本信息',
        detailsTitle: '基本信息',
        tabList: [
            { label: '项目方案审批', value: '0', prop: '0' },
        ],
        titleProp: 'schemeApproval_processTitle',
        formList: [
            { label: '项目名称', prop: 'schemeApproval_projectName', span: 12, type: 'input' },
            { label: '项目ID', prop: 'schemeApproval_projectId', span: 12, type: 'input' },
            { label: '资产转让方', prop: 'schemeApproval_transferor', span: 12, type: 'select' },
            { label: '产品类型', prop: 'schemeApproval_entrustMoney', span: 12, type: 'select' },
            { label: '债权总金额', prop: 'schemeApproval_entrustMoney', span: 12, type: 'input' },
            { label: '债权本金（元）', prop: 'schemeApproval_entrustPrincipal', span: 12, type: 'input' },
            { label: '债权本息（元）', prop: 'schemeApproval_entrustInterest', span: 12, type: 'input' },
            { label: '户数', prop: 'schemeApproval_households', span: 12, type: 'input' },
            { label: '是否交际保证金', prop: 'schemeApproval_whetherPayDeposit', span: 12, type: 'input' },
            { label: '保证金金额', prop: 'schemeApproval_payDeposit', span: 12, type: 'input' },
            { label: '基准日', prop: 'schemeApproval_baseDate', span: 12, type: 'input' },
            { label: '预计竞价日期', prop: 'schemeApproval_biddingDate', span: 12, type: 'input' },
            { label: '投标方式', prop: 'schemeApproval_biddingMethod', span: 12, type: 'input' },
            { label: '报价上限（元）', prop: 'schemeApproval_ouotationCeiling', span: 12, type: 'input' },
            { label: '项目主办', prop: 'schemeApproval_projectSponsor', span: 12, type: 'input' },
            { label: '项目承办', prop: 'schemeApproval_projectUndertaking', span: 12, type: 'input' },
            { label: '申请人', prop: 'schemeApproval_applayBy', span: 12, type: 'input', isDetails: 1 },
            { label: '申请时间', prop: 'schemeApproval_applayTime', span: 12, type: 'input', isDetails: 1 },
        ]
    }
})

const { form, rule } = toRefs(data)
const timelineList = [
    {
        status: '通过',
        timestamp: '2018-04-12 20:46',
        content: '同意申请',
        name: 'monca',
    },
    {
        status: '通过',
        timestamp: '2018-04-03 20:46',
        content: '通过',
        name: 'monca',
    },
    {
        status: '通过',
        timestamp: '2018-04-03 20:46',
        name: '胡图图',
    },
]

const fileInfo = ref({
    schemeApproval: [
        { label: '立项报告', prop: 'schemeApproval_feasibilityReport' },
        { label: '资产评估表', prop: 'schemeApproval_fileList' },
        { label: '其他附件', prop: 'schemeApproval_ohterFileList' },
    ],
    contract: [
        { label: '合同附件', prop: 'contract_fileList' },
        { label: '其他附件', prop: 'contract_ohterFileList' },
    ],
    bidding: [
        { label: '附件', prop: 'bidding_fileList' },
    ],
    conference: [
        { label: '附件', prop: 'conference_fileList' },
    ],
    projectAccounting: [
        { label: '其他附件', prop: 'projectAccounting_fileList' },
    ],
    startNapeList: [
        { label: '立项报告', prop: 'startNapeList_feasibilityReport' },
        { label: '资产评估表', prop: 'startNapeList_fileList' },
        { label: '其他附件', prop: 'startNapeList_ohterFileList' },
    ],
})

// 切换tab
function handleChangeTab(val) {
    const tabInfo = showFormInfo.value.tabList.find(v => v.value == val) || {}
    pageType.value = tabInfo.prop
    isDetails.value = 1

    // 切换标签页时初始化文件字段
    nextTick(() => {
        initFileFields()
    })
}

// 新增
function handAdd() {
    isDetails.value = 0
}

function toBack() {
    const obj = { path: route.query.path };
    proxy.$tab.closeOpenPage(obj);
}

onMounted(() => {
    pageType.value = route.query.pageType
    isDetails.value = route.query.isDetails || 1
    progressStatus.value = route.query.progressStatus

    // 初始化文件字段
    nextTick(() => {
        initFileFields()
    })
})
function showFormListFun(formList) {
    const showList = formList.filter(v => (v.isDetails == isDetails.value && isDetails.value == 1) || !v.isDetails)
    return showList
}
const showFormInfo = computed(() => {
    console.log('formInfo.value[pageType.value]', formInfo.value[pageType.value]);
    console.log('formInfo.value', formInfo.value);
    console.log('pageType.value', pageType.value);

    return formInfo.value[pageType.value] || []
})
const isShowPress = computed(() => !['conference'].includes(pageType.value))

// 文件上传配置
const projectApprovalFileList = ref([])
const asetEvaluationFormFileList = ref([])
const otherFileList = ref([])
const fileType = ref(['jpg', 'png', 'xlsx', 'docx', 'doc', 'pdf'])



// 监听独立文件列表变化，同步到表单数据
watch(projectApprovalFileList, (newVal) => {
    form.value['startNapeList_feasibilityReport'] = newVal
}, { deep: true })

watch(asetEvaluationFormFileList, (newVal) => {
    form.value['startNapeList_fileList'] = newVal
}, { deep: true })

watch(otherFileList, (newVal) => {
    form.value['startNapeList_ohterFileList'] = newVal
}, { deep: true })

// 获取特定文件上传组件的引用
function getFileUploadRef(refName) {
    return proxy.$refs[refName]
}

console.log(isDetails.value, 'isDetails.value');
</script>

<style lang="scss" scoped>
.title-area {
    display: flex;
    justify-content: space-between;
}

.content-list {
    font-size: 14px;
    font-weight: bold;

    .content-item {
        margin-bottom: 10px;

        &:last-of-type {
            margin-bottom: 20px;
        }
    }
}

.file-list {
    .file-item {
        display: flex;
        margin-bottom: 10px;
        align-items: center;

        .file-item-name {
            color: #409eff;
            padding: 5px 10px;
            border: 1px solid #666;
        }

        .el-button {
            margin-left: 15px;
        }
    }
}

.title {
    font-size: 20px;
    color: #000;
    font-weight: bold;
    margin-bottom: 10px;
}

.progress-list {
    display: flex;
    width: 90%;
    margin: 20px auto 0;
    border-radius: 20px;
    overflow: hidden;
    background-color: #f2f2f2;

    &>div {
        flex: 1;
        height: 38px;
        color: #409eff;
        line-height: 38px;
        text-align: center;
    }

    .active {
        border-radius: 20px;
        color: #ffff !important;
        background-color: #409eff !important;
    }
}

.sub-title-list {
    display: flex;
}

.sub-title-item {
    color: #000;
    font-size: 14px;
    font-weight: bold;
    margin-right: 20px;
    margin-bottom: 10px;
}

.pay-info {
    display: flex;
    gap: 20px;

    .el-card {
        flex: 1;
    }

    .right-card {
        flex: none;
        width: 280px;
    }
}

.timeline-title {
    font-weight: bold;
}

.timeline-content {
    font-size: 12px;
    color: #999;
}

.file-upload-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;

    .upload-component {
        width: 100%;
        margin-bottom: 12px;
    }

    .upload-tips {
        width: 100%;

        .report-tig {
            display: block;
            font-size: 12px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 4px;
            text-align: left;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}
</style>