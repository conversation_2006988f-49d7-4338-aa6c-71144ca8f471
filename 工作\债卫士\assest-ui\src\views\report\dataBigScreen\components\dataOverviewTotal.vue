<template>
    <div class="data-screen-overview-info-list">
        <div class="data-screen-overview-info-item" v-for="v in infoList" :key="v"
            :style="`background-image: url(${v.src});`">
            <div class="item-value">{{ infoForm[v.prop] || '--' }}&nbsp;万</div>
            <div class="item-label">{{ v.label }}</div>
        </div>
    </div>
</template>

<script setup>
import overviewTotalSrc1 from "@/assets/images/dataBigScreen/overview-total-1.png";
import overviewTotalSrc2 from "@/assets/images/dataBigScreen/overview-total-2.png";
import overviewTotalSrc3 from "@/assets/images/dataBigScreen/overview-total-3.png";
const props = defineProps({
    infoList: {
        type: Array, default: [
            { label: '资产规模', prop: '', src: overviewTotalSrc1 },
            { label: '户数', prop: '', src: overviewTotalSrc2 },
            { label: '本金金额', prop: '', src: overviewTotalSrc3 },
        ]
    },
    infoForm: { type: Object, default: {} }
})
</script>

<style lang="scss" scoped>
.data-screen-overview-info-list {
    display: flex;
    color: #fff;
    margin-top: 24px;
    gap: 100px;
    padding: 0 54px;
    div {
        flex: 1;
        color: #fff;
        text-align: center;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
    }

    .item-label {
        font-size: 16px;
    }

    .item-value {
        font-size: 32px;
    }
}
</style>