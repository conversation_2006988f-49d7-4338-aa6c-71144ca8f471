{"version": 3, "sources": ["../tinymce/plugins/image/plugin.js", "../tinymce/plugins/image/index.js", "dep:tinymce_plugins_image"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var eq = function (t) {\n      return function (a) {\n        return t === a;\n      };\n    };\n    var isString = isType('string');\n    var isObject = isType('object');\n    var isArray = isType('array');\n    var isNull = eq(null);\n    var isBoolean = isSimpleType('boolean');\n    var isNullable = function (a) {\n      return a === null || a === undefined;\n    };\n    var isNonNullable = function (a) {\n      return !isNullable(a);\n    };\n    var isFunction = isSimpleType('function');\n    var isNumber = isSimpleType('number');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var keys = Object.keys;\n    var hasOwnProperty = Object.hasOwnProperty;\n    var each = function (obj, f) {\n      var props = keys(obj);\n      for (var k = 0, len = props.length; k < len; k++) {\n        var i = props[k];\n        var x = obj[i];\n        f(x, i);\n      }\n    };\n    var objAcc = function (r) {\n      return function (x, i) {\n        r[i] = x;\n      };\n    };\n    var internalFilter = function (obj, pred, onTrue, onFalse) {\n      var r = {};\n      each(obj, function (x, i) {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n      return r;\n    };\n    var filter = function (obj, pred) {\n      var t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n    var hasNonNullableKey = function (obj, key) {\n      return has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n    };\n\n    var nativePush = Array.prototype.push;\n    var flatten = function (xs) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    var get = function (xs, i) {\n      return i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    };\n    var head = function (xs) {\n      return get(xs, 0);\n    };\n    var findMap = function (arr, f) {\n      for (var i = 0; i < arr.length; i++) {\n        var r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var rawSet = function (dom, key, value) {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    var set = function (element, key, value) {\n      rawSet(element.dom, key, value);\n    };\n    var remove = function (element, key) {\n      element.dom.removeAttribute(key);\n    };\n\n    var fromHtml = function (html, scope) {\n      var doc = scope || document;\n      var div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        console.error('HTML does not have a single root node', html);\n        throw new Error('HTML must have a single root node');\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    var fromTag = function (tag, scope) {\n      var doc = scope || document;\n      var node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    var fromText = function (text, scope) {\n      var doc = scope || document;\n      var node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    var fromDom = function (node) {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    var fromPoint = function (docElm, x, y) {\n      return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    };\n    var SugarElement = {\n      fromHtml: fromHtml,\n      fromTag: fromTag,\n      fromText: fromText,\n      fromDom: fromDom,\n      fromPoint: fromPoint\n    };\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.util.Promise');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.util.URI');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.XHR');\n\n    var hasDimensions = function (editor) {\n      return editor.getParam('image_dimensions', true, 'boolean');\n    };\n    var hasAdvTab = function (editor) {\n      return editor.getParam('image_advtab', false, 'boolean');\n    };\n    var hasUploadTab = function (editor) {\n      return editor.getParam('image_uploadtab', true, 'boolean');\n    };\n    var getPrependUrl = function (editor) {\n      return editor.getParam('image_prepend_url', '', 'string');\n    };\n    var getClassList = function (editor) {\n      return editor.getParam('image_class_list');\n    };\n    var hasDescription = function (editor) {\n      return editor.getParam('image_description', true, 'boolean');\n    };\n    var hasImageTitle = function (editor) {\n      return editor.getParam('image_title', false, 'boolean');\n    };\n    var hasImageCaption = function (editor) {\n      return editor.getParam('image_caption', false, 'boolean');\n    };\n    var getImageList = function (editor) {\n      return editor.getParam('image_list', false);\n    };\n    var hasUploadUrl = function (editor) {\n      return isNonNullable(editor.getParam('images_upload_url'));\n    };\n    var hasUploadHandler = function (editor) {\n      return isNonNullable(editor.getParam('images_upload_handler'));\n    };\n    var showAccessibilityOptions = function (editor) {\n      return editor.getParam('a11y_advanced_options', false, 'boolean');\n    };\n    var isAutomaticUploadsEnabled = function (editor) {\n      return editor.getParam('automatic_uploads', true, 'boolean');\n    };\n\n    var parseIntAndGetMax = function (val1, val2) {\n      return Math.max(parseInt(val1, 10), parseInt(val2, 10));\n    };\n    var getImageSize = function (url) {\n      return new global$4(function (callback) {\n        var img = document.createElement('img');\n        var done = function (dimensions) {\n          img.onload = img.onerror = null;\n          if (img.parentNode) {\n            img.parentNode.removeChild(img);\n          }\n          callback(dimensions);\n        };\n        img.onload = function () {\n          var width = parseIntAndGetMax(img.width, img.clientWidth);\n          var height = parseIntAndGetMax(img.height, img.clientHeight);\n          var dimensions = {\n            width: width,\n            height: height\n          };\n          done(global$4.resolve(dimensions));\n        };\n        img.onerror = function () {\n          done(global$4.reject('Failed to get image dimensions for: ' + url));\n        };\n        var style = img.style;\n        style.visibility = 'hidden';\n        style.position = 'fixed';\n        style.bottom = style.left = '0px';\n        style.width = style.height = 'auto';\n        document.body.appendChild(img);\n        img.src = url;\n      });\n    };\n    var removePixelSuffix = function (value) {\n      if (value) {\n        value = value.replace(/px$/, '');\n      }\n      return value;\n    };\n    var addPixelSuffix = function (value) {\n      if (value.length > 0 && /^[0-9]+$/.test(value)) {\n        value += 'px';\n      }\n      return value;\n    };\n    var mergeMargins = function (css) {\n      if (css.margin) {\n        var splitMargin = String(css.margin).split(' ');\n        switch (splitMargin.length) {\n        case 1:\n          css['margin-top'] = css['margin-top'] || splitMargin[0];\n          css['margin-right'] = css['margin-right'] || splitMargin[0];\n          css['margin-bottom'] = css['margin-bottom'] || splitMargin[0];\n          css['margin-left'] = css['margin-left'] || splitMargin[0];\n          break;\n        case 2:\n          css['margin-top'] = css['margin-top'] || splitMargin[0];\n          css['margin-right'] = css['margin-right'] || splitMargin[1];\n          css['margin-bottom'] = css['margin-bottom'] || splitMargin[0];\n          css['margin-left'] = css['margin-left'] || splitMargin[1];\n          break;\n        case 3:\n          css['margin-top'] = css['margin-top'] || splitMargin[0];\n          css['margin-right'] = css['margin-right'] || splitMargin[1];\n          css['margin-bottom'] = css['margin-bottom'] || splitMargin[2];\n          css['margin-left'] = css['margin-left'] || splitMargin[1];\n          break;\n        case 4:\n          css['margin-top'] = css['margin-top'] || splitMargin[0];\n          css['margin-right'] = css['margin-right'] || splitMargin[1];\n          css['margin-bottom'] = css['margin-bottom'] || splitMargin[2];\n          css['margin-left'] = css['margin-left'] || splitMargin[3];\n        }\n        delete css.margin;\n      }\n      return css;\n    };\n    var createImageList = function (editor, callback) {\n      var imageList = getImageList(editor);\n      if (isString(imageList)) {\n        global$2.send({\n          url: imageList,\n          success: function (text) {\n            callback(JSON.parse(text));\n          }\n        });\n      } else if (isFunction(imageList)) {\n        imageList(callback);\n      } else {\n        callback(imageList);\n      }\n    };\n    var waitLoadImage = function (editor, data, imgElm) {\n      var selectImage = function () {\n        imgElm.onload = imgElm.onerror = null;\n        if (editor.selection) {\n          editor.selection.select(imgElm);\n          editor.nodeChanged();\n        }\n      };\n      imgElm.onload = function () {\n        if (!data.width && !data.height && hasDimensions(editor)) {\n          editor.dom.setAttribs(imgElm, {\n            width: String(imgElm.clientWidth),\n            height: String(imgElm.clientHeight)\n          });\n        }\n        selectImage();\n      };\n      imgElm.onerror = selectImage;\n    };\n    var blobToDataUri = function (blob) {\n      return new global$4(function (resolve, reject) {\n        var reader = new FileReader();\n        reader.onload = function () {\n          resolve(reader.result);\n        };\n        reader.onerror = function () {\n          reject(reader.error.message);\n        };\n        reader.readAsDataURL(blob);\n      });\n    };\n    var isPlaceholderImage = function (imgElm) {\n      return imgElm.nodeName === 'IMG' && (imgElm.hasAttribute('data-mce-object') || imgElm.hasAttribute('data-mce-placeholder'));\n    };\n    var isSafeImageUrl = function (editor, src) {\n      return global$3.isDomSafe(src, 'img', editor.settings);\n    };\n\n    var DOM = global$5.DOM;\n    var getHspace = function (image) {\n      if (image.style.marginLeft && image.style.marginRight && image.style.marginLeft === image.style.marginRight) {\n        return removePixelSuffix(image.style.marginLeft);\n      } else {\n        return '';\n      }\n    };\n    var getVspace = function (image) {\n      if (image.style.marginTop && image.style.marginBottom && image.style.marginTop === image.style.marginBottom) {\n        return removePixelSuffix(image.style.marginTop);\n      } else {\n        return '';\n      }\n    };\n    var getBorder = function (image) {\n      if (image.style.borderWidth) {\n        return removePixelSuffix(image.style.borderWidth);\n      } else {\n        return '';\n      }\n    };\n    var getAttrib = function (image, name) {\n      if (image.hasAttribute(name)) {\n        return image.getAttribute(name);\n      } else {\n        return '';\n      }\n    };\n    var getStyle = function (image, name) {\n      return image.style[name] ? image.style[name] : '';\n    };\n    var hasCaption = function (image) {\n      return image.parentNode !== null && image.parentNode.nodeName === 'FIGURE';\n    };\n    var updateAttrib = function (image, name, value) {\n      if (value === '') {\n        image.removeAttribute(name);\n      } else {\n        image.setAttribute(name, value);\n      }\n    };\n    var wrapInFigure = function (image) {\n      var figureElm = DOM.create('figure', { class: 'image' });\n      DOM.insertAfter(figureElm, image);\n      figureElm.appendChild(image);\n      figureElm.appendChild(DOM.create('figcaption', { contentEditable: 'true' }, 'Caption'));\n      figureElm.contentEditable = 'false';\n    };\n    var removeFigure = function (image) {\n      var figureElm = image.parentNode;\n      DOM.insertAfter(image, figureElm);\n      DOM.remove(figureElm);\n    };\n    var toggleCaption = function (image) {\n      if (hasCaption(image)) {\n        removeFigure(image);\n      } else {\n        wrapInFigure(image);\n      }\n    };\n    var normalizeStyle = function (image, normalizeCss) {\n      var attrValue = image.getAttribute('style');\n      var value = normalizeCss(attrValue !== null ? attrValue : '');\n      if (value.length > 0) {\n        image.setAttribute('style', value);\n        image.setAttribute('data-mce-style', value);\n      } else {\n        image.removeAttribute('style');\n      }\n    };\n    var setSize = function (name, normalizeCss) {\n      return function (image, name, value) {\n        if (image.style[name]) {\n          image.style[name] = addPixelSuffix(value);\n          normalizeStyle(image, normalizeCss);\n        } else {\n          updateAttrib(image, name, value);\n        }\n      };\n    };\n    var getSize = function (image, name) {\n      if (image.style[name]) {\n        return removePixelSuffix(image.style[name]);\n      } else {\n        return getAttrib(image, name);\n      }\n    };\n    var setHspace = function (image, value) {\n      var pxValue = addPixelSuffix(value);\n      image.style.marginLeft = pxValue;\n      image.style.marginRight = pxValue;\n    };\n    var setVspace = function (image, value) {\n      var pxValue = addPixelSuffix(value);\n      image.style.marginTop = pxValue;\n      image.style.marginBottom = pxValue;\n    };\n    var setBorder = function (image, value) {\n      var pxValue = addPixelSuffix(value);\n      image.style.borderWidth = pxValue;\n    };\n    var setBorderStyle = function (image, value) {\n      image.style.borderStyle = value;\n    };\n    var getBorderStyle = function (image) {\n      return getStyle(image, 'borderStyle');\n    };\n    var isFigure = function (elm) {\n      return elm.nodeName === 'FIGURE';\n    };\n    var isImage = function (elm) {\n      return elm.nodeName === 'IMG';\n    };\n    var getIsDecorative = function (image) {\n      return DOM.getAttrib(image, 'alt').length === 0 && DOM.getAttrib(image, 'role') === 'presentation';\n    };\n    var getAlt = function (image) {\n      if (getIsDecorative(image)) {\n        return '';\n      } else {\n        return getAttrib(image, 'alt');\n      }\n    };\n    var defaultData = function () {\n      return {\n        src: '',\n        alt: '',\n        title: '',\n        width: '',\n        height: '',\n        class: '',\n        style: '',\n        caption: false,\n        hspace: '',\n        vspace: '',\n        border: '',\n        borderStyle: '',\n        isDecorative: false\n      };\n    };\n    var getStyleValue = function (normalizeCss, data) {\n      var image = document.createElement('img');\n      updateAttrib(image, 'style', data.style);\n      if (getHspace(image) || data.hspace !== '') {\n        setHspace(image, data.hspace);\n      }\n      if (getVspace(image) || data.vspace !== '') {\n        setVspace(image, data.vspace);\n      }\n      if (getBorder(image) || data.border !== '') {\n        setBorder(image, data.border);\n      }\n      if (getBorderStyle(image) || data.borderStyle !== '') {\n        setBorderStyle(image, data.borderStyle);\n      }\n      return normalizeCss(image.getAttribute('style'));\n    };\n    var create = function (normalizeCss, data) {\n      var image = document.createElement('img');\n      write(normalizeCss, __assign(__assign({}, data), { caption: false }), image);\n      setAlt(image, data.alt, data.isDecorative);\n      if (data.caption) {\n        var figure = DOM.create('figure', { class: 'image' });\n        figure.appendChild(image);\n        figure.appendChild(DOM.create('figcaption', { contentEditable: 'true' }, 'Caption'));\n        figure.contentEditable = 'false';\n        return figure;\n      } else {\n        return image;\n      }\n    };\n    var read = function (normalizeCss, image) {\n      return {\n        src: getAttrib(image, 'src'),\n        alt: getAlt(image),\n        title: getAttrib(image, 'title'),\n        width: getSize(image, 'width'),\n        height: getSize(image, 'height'),\n        class: getAttrib(image, 'class'),\n        style: normalizeCss(getAttrib(image, 'style')),\n        caption: hasCaption(image),\n        hspace: getHspace(image),\n        vspace: getVspace(image),\n        border: getBorder(image),\n        borderStyle: getStyle(image, 'borderStyle'),\n        isDecorative: getIsDecorative(image)\n      };\n    };\n    var updateProp = function (image, oldData, newData, name, set) {\n      if (newData[name] !== oldData[name]) {\n        set(image, name, newData[name]);\n      }\n    };\n    var setAlt = function (image, alt, isDecorative) {\n      if (isDecorative) {\n        DOM.setAttrib(image, 'role', 'presentation');\n        var sugarImage = SugarElement.fromDom(image);\n        set(sugarImage, 'alt', '');\n      } else {\n        if (isNull(alt)) {\n          var sugarImage = SugarElement.fromDom(image);\n          remove(sugarImage, 'alt');\n        } else {\n          var sugarImage = SugarElement.fromDom(image);\n          set(sugarImage, 'alt', alt);\n        }\n        if (DOM.getAttrib(image, 'role') === 'presentation') {\n          DOM.setAttrib(image, 'role', '');\n        }\n      }\n    };\n    var updateAlt = function (image, oldData, newData) {\n      if (newData.alt !== oldData.alt || newData.isDecorative !== oldData.isDecorative) {\n        setAlt(image, newData.alt, newData.isDecorative);\n      }\n    };\n    var normalized = function (set, normalizeCss) {\n      return function (image, name, value) {\n        set(image, value);\n        normalizeStyle(image, normalizeCss);\n      };\n    };\n    var write = function (normalizeCss, newData, image) {\n      var oldData = read(normalizeCss, image);\n      updateProp(image, oldData, newData, 'caption', function (image, _name, _value) {\n        return toggleCaption(image);\n      });\n      updateProp(image, oldData, newData, 'src', updateAttrib);\n      updateProp(image, oldData, newData, 'title', updateAttrib);\n      updateProp(image, oldData, newData, 'width', setSize('width', normalizeCss));\n      updateProp(image, oldData, newData, 'height', setSize('height', normalizeCss));\n      updateProp(image, oldData, newData, 'class', updateAttrib);\n      updateProp(image, oldData, newData, 'style', normalized(function (image, value) {\n        return updateAttrib(image, 'style', value);\n      }, normalizeCss));\n      updateProp(image, oldData, newData, 'hspace', normalized(setHspace, normalizeCss));\n      updateProp(image, oldData, newData, 'vspace', normalized(setVspace, normalizeCss));\n      updateProp(image, oldData, newData, 'border', normalized(setBorder, normalizeCss));\n      updateProp(image, oldData, newData, 'borderStyle', normalized(setBorderStyle, normalizeCss));\n      updateAlt(image, oldData, newData);\n    };\n\n    var normalizeCss$1 = function (editor, cssText) {\n      var css = editor.dom.styles.parse(cssText);\n      var mergedCss = mergeMargins(css);\n      var compressed = editor.dom.styles.parse(editor.dom.styles.serialize(mergedCss));\n      return editor.dom.styles.serialize(compressed);\n    };\n    var getSelectedImage = function (editor) {\n      var imgElm = editor.selection.getNode();\n      var figureElm = editor.dom.getParent(imgElm, 'figure.image');\n      if (figureElm) {\n        return editor.dom.select('img', figureElm)[0];\n      }\n      if (imgElm && (imgElm.nodeName !== 'IMG' || isPlaceholderImage(imgElm))) {\n        return null;\n      }\n      return imgElm;\n    };\n    var splitTextBlock = function (editor, figure) {\n      var dom = editor.dom;\n      var textBlockElements = filter(editor.schema.getTextBlockElements(), function (_, parentElm) {\n        return !editor.schema.isValidChild(parentElm, 'figure');\n      });\n      var textBlock = dom.getParent(figure.parentNode, function (node) {\n        return hasNonNullableKey(textBlockElements, node.nodeName);\n      }, editor.getBody());\n      if (textBlock) {\n        return dom.split(textBlock, figure);\n      } else {\n        return figure;\n      }\n    };\n    var readImageDataFromSelection = function (editor) {\n      var image = getSelectedImage(editor);\n      return image ? read(function (css) {\n        return normalizeCss$1(editor, css);\n      }, image) : defaultData();\n    };\n    var insertImageAtCaret = function (editor, data) {\n      var elm = create(function (css) {\n        return normalizeCss$1(editor, css);\n      }, data);\n      editor.dom.setAttrib(elm, 'data-mce-id', '__mcenew');\n      editor.focus();\n      editor.selection.setContent(elm.outerHTML);\n      var insertedElm = editor.dom.select('*[data-mce-id=\"__mcenew\"]')[0];\n      editor.dom.setAttrib(insertedElm, 'data-mce-id', null);\n      if (isFigure(insertedElm)) {\n        var figure = splitTextBlock(editor, insertedElm);\n        editor.selection.select(figure);\n      } else {\n        editor.selection.select(insertedElm);\n      }\n    };\n    var syncSrcAttr = function (editor, image) {\n      editor.dom.setAttrib(image, 'src', image.getAttribute('src'));\n    };\n    var deleteImage = function (editor, image) {\n      if (image) {\n        var elm = editor.dom.is(image.parentNode, 'figure.image') ? image.parentNode : image;\n        editor.dom.remove(elm);\n        editor.focus();\n        editor.nodeChanged();\n        if (editor.dom.isEmpty(editor.getBody())) {\n          editor.setContent('');\n          editor.selection.setCursorLocation();\n        }\n      }\n    };\n    var writeImageDataToSelection = function (editor, data) {\n      var image = getSelectedImage(editor);\n      write(function (css) {\n        return normalizeCss$1(editor, css);\n      }, data, image);\n      syncSrcAttr(editor, image);\n      if (isFigure(image.parentNode)) {\n        var figure = image.parentNode;\n        splitTextBlock(editor, figure);\n        editor.selection.select(image.parentNode);\n      } else {\n        editor.selection.select(image);\n        waitLoadImage(editor, data, image);\n      }\n    };\n    var sanitizeImageData = function (editor, data) {\n      var src = data.src;\n      return __assign(__assign({}, data), { src: isSafeImageUrl(editor, src) ? src : '' });\n    };\n    var insertOrUpdateImage = function (editor, partialData) {\n      var image = getSelectedImage(editor);\n      if (image) {\n        var selectedImageData = read(function (css) {\n          return normalizeCss$1(editor, css);\n        }, image);\n        var data = __assign(__assign({}, selectedImageData), partialData);\n        var sanitizedData = sanitizeImageData(editor, data);\n        if (data.src) {\n          writeImageDataToSelection(editor, sanitizedData);\n        } else {\n          deleteImage(editor, image);\n        }\n      } else if (partialData.src) {\n        insertImageAtCaret(editor, __assign(__assign({}, defaultData()), partialData));\n      }\n    };\n\n    var deep = function (old, nu) {\n      var bothObjects = isObject(old) && isObject(nu);\n      return bothObjects ? deepMerge(old, nu) : nu;\n    };\n    var baseMerge = function (merger) {\n      return function () {\n        var objects = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          objects[_i] = arguments[_i];\n        }\n        if (objects.length === 0) {\n          throw new Error('Can\\'t merge zero objects');\n        }\n        var ret = {};\n        for (var j = 0; j < objects.length; j++) {\n          var curObject = objects[j];\n          for (var key in curObject) {\n            if (has(curObject, key)) {\n              ret[key] = merger(ret[key], curObject[key]);\n            }\n          }\n        }\n        return ret;\n      };\n    };\n    var deepMerge = baseMerge(deep);\n\n    var isNotEmpty = function (s) {\n      return s.length > 0;\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.ImageUploader');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var getValue = function (item) {\n      return isString(item.value) ? item.value : '';\n    };\n    var getText = function (item) {\n      if (isString(item.text)) {\n        return item.text;\n      } else if (isString(item.title)) {\n        return item.title;\n      } else {\n        return '';\n      }\n    };\n    var sanitizeList = function (list, extractValue) {\n      var out = [];\n      global.each(list, function (item) {\n        var text = getText(item);\n        if (item.menu !== undefined) {\n          var items = sanitizeList(item.menu, extractValue);\n          out.push({\n            text: text,\n            items: items\n          });\n        } else {\n          var value = extractValue(item);\n          out.push({\n            text: text,\n            value: value\n          });\n        }\n      });\n      return out;\n    };\n    var sanitizer = function (extractor) {\n      if (extractor === void 0) {\n        extractor = getValue;\n      }\n      return function (list) {\n        if (list) {\n          return Optional.from(list).map(function (list) {\n            return sanitizeList(list, extractor);\n          });\n        } else {\n          return Optional.none();\n        }\n      };\n    };\n    var sanitize = function (list) {\n      return sanitizer(getValue)(list);\n    };\n    var isGroup = function (item) {\n      return has(item, 'items');\n    };\n    var findEntryDelegate = function (list, value) {\n      return findMap(list, function (item) {\n        if (isGroup(item)) {\n          return findEntryDelegate(item.items, value);\n        } else if (item.value === value) {\n          return Optional.some(item);\n        } else {\n          return Optional.none();\n        }\n      });\n    };\n    var findEntry = function (optList, value) {\n      return optList.bind(function (list) {\n        return findEntryDelegate(list, value);\n      });\n    };\n    var ListUtils = {\n      sanitizer: sanitizer,\n      sanitize: sanitize,\n      findEntry: findEntry\n    };\n\n    var makeTab$2 = function (_info) {\n      return {\n        title: 'Advanced',\n        name: 'advanced',\n        items: [\n          {\n            type: 'input',\n            label: 'Style',\n            name: 'style'\n          },\n          {\n            type: 'grid',\n            columns: 2,\n            items: [\n              {\n                type: 'input',\n                label: 'Vertical space',\n                name: 'vspace',\n                inputMode: 'numeric'\n              },\n              {\n                type: 'input',\n                label: 'Horizontal space',\n                name: 'hspace',\n                inputMode: 'numeric'\n              },\n              {\n                type: 'input',\n                label: 'Border width',\n                name: 'border',\n                inputMode: 'numeric'\n              },\n              {\n                type: 'listbox',\n                name: 'borderstyle',\n                label: 'Border style',\n                items: [\n                  {\n                    text: 'Select...',\n                    value: ''\n                  },\n                  {\n                    text: 'Solid',\n                    value: 'solid'\n                  },\n                  {\n                    text: 'Dotted',\n                    value: 'dotted'\n                  },\n                  {\n                    text: 'Dashed',\n                    value: 'dashed'\n                  },\n                  {\n                    text: 'Double',\n                    value: 'double'\n                  },\n                  {\n                    text: 'Groove',\n                    value: 'groove'\n                  },\n                  {\n                    text: 'Ridge',\n                    value: 'ridge'\n                  },\n                  {\n                    text: 'Inset',\n                    value: 'inset'\n                  },\n                  {\n                    text: 'Outset',\n                    value: 'outset'\n                  },\n                  {\n                    text: 'None',\n                    value: 'none'\n                  },\n                  {\n                    text: 'Hidden',\n                    value: 'hidden'\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      };\n    };\n    var AdvTab = { makeTab: makeTab$2 };\n\n    var collect = function (editor) {\n      var urlListSanitizer = ListUtils.sanitizer(function (item) {\n        return editor.convertURL(item.value || item.url, 'src');\n      });\n      var futureImageList = new global$4(function (completer) {\n        createImageList(editor, function (imageList) {\n          completer(urlListSanitizer(imageList).map(function (items) {\n            return flatten([\n              [{\n                  text: 'None',\n                  value: ''\n                }],\n              items\n            ]);\n          }));\n        });\n      });\n      var classList = ListUtils.sanitize(getClassList(editor));\n      var hasAdvTab$1 = hasAdvTab(editor);\n      var hasUploadTab$1 = hasUploadTab(editor);\n      var hasUploadUrl$1 = hasUploadUrl(editor);\n      var hasUploadHandler$1 = hasUploadHandler(editor);\n      var image = readImageDataFromSelection(editor);\n      var hasDescription$1 = hasDescription(editor);\n      var hasImageTitle$1 = hasImageTitle(editor);\n      var hasDimensions$1 = hasDimensions(editor);\n      var hasImageCaption$1 = hasImageCaption(editor);\n      var hasAccessibilityOptions = showAccessibilityOptions(editor);\n      var automaticUploads = isAutomaticUploadsEnabled(editor);\n      var prependURL = Optional.some(getPrependUrl(editor)).filter(function (preUrl) {\n        return isString(preUrl) && preUrl.length > 0;\n      });\n      return futureImageList.then(function (imageList) {\n        return {\n          image: image,\n          imageList: imageList,\n          classList: classList,\n          hasAdvTab: hasAdvTab$1,\n          hasUploadTab: hasUploadTab$1,\n          hasUploadUrl: hasUploadUrl$1,\n          hasUploadHandler: hasUploadHandler$1,\n          hasDescription: hasDescription$1,\n          hasImageTitle: hasImageTitle$1,\n          hasDimensions: hasDimensions$1,\n          hasImageCaption: hasImageCaption$1,\n          prependURL: prependURL,\n          hasAccessibilityOptions: hasAccessibilityOptions,\n          automaticUploads: automaticUploads\n        };\n      });\n    };\n\n    var makeItems = function (info) {\n      var imageUrl = {\n        name: 'src',\n        type: 'urlinput',\n        filetype: 'image',\n        label: 'Source'\n      };\n      var imageList = info.imageList.map(function (items) {\n        return {\n          name: 'images',\n          type: 'listbox',\n          label: 'Image list',\n          items: items\n        };\n      });\n      var imageDescription = {\n        name: 'alt',\n        type: 'input',\n        label: 'Alternative description',\n        disabled: info.hasAccessibilityOptions && info.image.isDecorative\n      };\n      var imageTitle = {\n        name: 'title',\n        type: 'input',\n        label: 'Image title'\n      };\n      var imageDimensions = {\n        name: 'dimensions',\n        type: 'sizeinput'\n      };\n      var isDecorative = {\n        type: 'label',\n        label: 'Accessibility',\n        items: [{\n            name: 'isDecorative',\n            type: 'checkbox',\n            label: 'Image is decorative'\n          }]\n      };\n      var classList = info.classList.map(function (items) {\n        return {\n          name: 'classes',\n          type: 'listbox',\n          label: 'Class',\n          items: items\n        };\n      });\n      var caption = {\n        type: 'label',\n        label: 'Caption',\n        items: [{\n            type: 'checkbox',\n            name: 'caption',\n            label: 'Show caption'\n          }]\n      };\n      var getDialogContainerType = function (useColumns) {\n        return useColumns ? {\n          type: 'grid',\n          columns: 2\n        } : { type: 'panel' };\n      };\n      return flatten([\n        [imageUrl],\n        imageList.toArray(),\n        info.hasAccessibilityOptions && info.hasDescription ? [isDecorative] : [],\n        info.hasDescription ? [imageDescription] : [],\n        info.hasImageTitle ? [imageTitle] : [],\n        info.hasDimensions ? [imageDimensions] : [],\n        [__assign(__assign({}, getDialogContainerType(info.classList.isSome() && info.hasImageCaption)), {\n            items: flatten([\n              classList.toArray(),\n              info.hasImageCaption ? [caption] : []\n            ])\n          })]\n      ]);\n    };\n    var makeTab$1 = function (info) {\n      return {\n        title: 'General',\n        name: 'general',\n        items: makeItems(info)\n      };\n    };\n    var MainTab = {\n      makeTab: makeTab$1,\n      makeItems: makeItems\n    };\n\n    var makeTab = function (_info) {\n      var items = [{\n          type: 'dropzone',\n          name: 'fileinput'\n        }];\n      return {\n        title: 'Upload',\n        name: 'upload',\n        items: items\n      };\n    };\n    var UploadTab = { makeTab: makeTab };\n\n    var createState = function (info) {\n      return {\n        prevImage: ListUtils.findEntry(info.imageList, info.image.src),\n        prevAlt: info.image.alt,\n        open: true\n      };\n    };\n    var fromImageData = function (image) {\n      return {\n        src: {\n          value: image.src,\n          meta: {}\n        },\n        images: image.src,\n        alt: image.alt,\n        title: image.title,\n        dimensions: {\n          width: image.width,\n          height: image.height\n        },\n        classes: image.class,\n        caption: image.caption,\n        style: image.style,\n        vspace: image.vspace,\n        border: image.border,\n        hspace: image.hspace,\n        borderstyle: image.borderStyle,\n        fileinput: [],\n        isDecorative: image.isDecorative\n      };\n    };\n    var toImageData = function (data, removeEmptyAlt) {\n      return {\n        src: data.src.value,\n        alt: data.alt.length === 0 && removeEmptyAlt ? null : data.alt,\n        title: data.title,\n        width: data.dimensions.width,\n        height: data.dimensions.height,\n        class: data.classes,\n        style: data.style,\n        caption: data.caption,\n        hspace: data.hspace,\n        vspace: data.vspace,\n        border: data.border,\n        borderStyle: data.borderstyle,\n        isDecorative: data.isDecorative\n      };\n    };\n    var addPrependUrl2 = function (info, srcURL) {\n      if (!/^(?:[a-zA-Z]+:)?\\/\\//.test(srcURL)) {\n        return info.prependURL.bind(function (prependUrl) {\n          if (srcURL.substring(0, prependUrl.length) !== prependUrl) {\n            return Optional.some(prependUrl + srcURL);\n          }\n          return Optional.none();\n        });\n      }\n      return Optional.none();\n    };\n    var addPrependUrl = function (info, api) {\n      var data = api.getData();\n      addPrependUrl2(info, data.src.value).each(function (srcURL) {\n        api.setData({\n          src: {\n            value: srcURL,\n            meta: data.src.meta\n          }\n        });\n      });\n    };\n    var formFillFromMeta2 = function (info, data, meta) {\n      if (info.hasDescription && isString(meta.alt)) {\n        data.alt = meta.alt;\n      }\n      if (info.hasAccessibilityOptions) {\n        data.isDecorative = meta.isDecorative || data.isDecorative || false;\n      }\n      if (info.hasImageTitle && isString(meta.title)) {\n        data.title = meta.title;\n      }\n      if (info.hasDimensions) {\n        if (isString(meta.width)) {\n          data.dimensions.width = meta.width;\n        }\n        if (isString(meta.height)) {\n          data.dimensions.height = meta.height;\n        }\n      }\n      if (isString(meta.class)) {\n        ListUtils.findEntry(info.classList, meta.class).each(function (entry) {\n          data.classes = entry.value;\n        });\n      }\n      if (info.hasImageCaption) {\n        if (isBoolean(meta.caption)) {\n          data.caption = meta.caption;\n        }\n      }\n      if (info.hasAdvTab) {\n        if (isString(meta.style)) {\n          data.style = meta.style;\n        }\n        if (isString(meta.vspace)) {\n          data.vspace = meta.vspace;\n        }\n        if (isString(meta.border)) {\n          data.border = meta.border;\n        }\n        if (isString(meta.hspace)) {\n          data.hspace = meta.hspace;\n        }\n        if (isString(meta.borderstyle)) {\n          data.borderstyle = meta.borderstyle;\n        }\n      }\n    };\n    var formFillFromMeta = function (info, api) {\n      var data = api.getData();\n      var meta = data.src.meta;\n      if (meta !== undefined) {\n        var newData = deepMerge({}, data);\n        formFillFromMeta2(info, newData, meta);\n        api.setData(newData);\n      }\n    };\n    var calculateImageSize = function (helpers, info, state, api) {\n      var data = api.getData();\n      var url = data.src.value;\n      var meta = data.src.meta || {};\n      if (!meta.width && !meta.height && info.hasDimensions) {\n        if (isNotEmpty(url)) {\n          helpers.imageSize(url).then(function (size) {\n            if (state.open) {\n              api.setData({ dimensions: size });\n            }\n          }).catch(function (e) {\n            return console.error(e);\n          });\n        } else {\n          api.setData({\n            dimensions: {\n              width: '',\n              height: ''\n            }\n          });\n        }\n      }\n    };\n    var updateImagesDropdown = function (info, state, api) {\n      var data = api.getData();\n      var image = ListUtils.findEntry(info.imageList, data.src.value);\n      state.prevImage = image;\n      api.setData({\n        images: image.map(function (entry) {\n          return entry.value;\n        }).getOr('')\n      });\n    };\n    var changeSrc = function (helpers, info, state, api) {\n      addPrependUrl(info, api);\n      formFillFromMeta(info, api);\n      calculateImageSize(helpers, info, state, api);\n      updateImagesDropdown(info, state, api);\n    };\n    var changeImages = function (helpers, info, state, api) {\n      var data = api.getData();\n      var image = ListUtils.findEntry(info.imageList, data.images);\n      image.each(function (img) {\n        var updateAlt = data.alt === '' || state.prevImage.map(function (image) {\n          return image.text === data.alt;\n        }).getOr(false);\n        if (updateAlt) {\n          if (img.value === '') {\n            api.setData({\n              src: img,\n              alt: state.prevAlt\n            });\n          } else {\n            api.setData({\n              src: img,\n              alt: img.text\n            });\n          }\n        } else {\n          api.setData({ src: img });\n        }\n      });\n      state.prevImage = image;\n      changeSrc(helpers, info, state, api);\n    };\n    var calcVSpace = function (css) {\n      var matchingTopBottom = css['margin-top'] && css['margin-bottom'] && css['margin-top'] === css['margin-bottom'];\n      return matchingTopBottom ? removePixelSuffix(String(css['margin-top'])) : '';\n    };\n    var calcHSpace = function (css) {\n      var matchingLeftRight = css['margin-right'] && css['margin-left'] && css['margin-right'] === css['margin-left'];\n      return matchingLeftRight ? removePixelSuffix(String(css['margin-right'])) : '';\n    };\n    var calcBorderWidth = function (css) {\n      return css['border-width'] ? removePixelSuffix(String(css['border-width'])) : '';\n    };\n    var calcBorderStyle = function (css) {\n      return css['border-style'] ? String(css['border-style']) : '';\n    };\n    var calcStyle = function (parseStyle, serializeStyle, css) {\n      return serializeStyle(parseStyle(serializeStyle(css)));\n    };\n    var changeStyle2 = function (parseStyle, serializeStyle, data) {\n      var css = mergeMargins(parseStyle(data.style));\n      var dataCopy = deepMerge({}, data);\n      dataCopy.vspace = calcVSpace(css);\n      dataCopy.hspace = calcHSpace(css);\n      dataCopy.border = calcBorderWidth(css);\n      dataCopy.borderstyle = calcBorderStyle(css);\n      dataCopy.style = calcStyle(parseStyle, serializeStyle, css);\n      return dataCopy;\n    };\n    var changeStyle = function (helpers, api) {\n      var data = api.getData();\n      var newData = changeStyle2(helpers.parseStyle, helpers.serializeStyle, data);\n      api.setData(newData);\n    };\n    var changeAStyle = function (helpers, info, api) {\n      var data = deepMerge(fromImageData(info.image), api.getData());\n      var style = getStyleValue(helpers.normalizeCss, toImageData(data, false));\n      api.setData({ style: style });\n    };\n    var changeFileInput = function (helpers, info, state, api) {\n      var data = api.getData();\n      api.block('Uploading image');\n      head(data.fileinput).fold(function () {\n        api.unblock();\n      }, function (file) {\n        var blobUri = URL.createObjectURL(file);\n        var finalize = function () {\n          api.unblock();\n          URL.revokeObjectURL(blobUri);\n        };\n        var updateSrcAndSwitchTab = function (url) {\n          api.setData({\n            src: {\n              value: url,\n              meta: {}\n            }\n          });\n          api.showTab('general');\n          changeSrc(helpers, info, state, api);\n        };\n        blobToDataUri(file).then(function (dataUrl) {\n          var blobInfo = helpers.createBlobCache(file, blobUri, dataUrl);\n          if (info.automaticUploads) {\n            helpers.uploadImage(blobInfo).then(function (result) {\n              updateSrcAndSwitchTab(result.url);\n              finalize();\n            }).catch(function (err) {\n              finalize();\n              helpers.alertErr(err);\n            });\n          } else {\n            helpers.addToBlobCache(blobInfo);\n            updateSrcAndSwitchTab(blobInfo.blobUri());\n            api.unblock();\n          }\n        });\n      });\n    };\n    var changeHandler = function (helpers, info, state) {\n      return function (api, evt) {\n        if (evt.name === 'src') {\n          changeSrc(helpers, info, state, api);\n        } else if (evt.name === 'images') {\n          changeImages(helpers, info, state, api);\n        } else if (evt.name === 'alt') {\n          state.prevAlt = api.getData().alt;\n        } else if (evt.name === 'style') {\n          changeStyle(helpers, api);\n        } else if (evt.name === 'vspace' || evt.name === 'hspace' || evt.name === 'border' || evt.name === 'borderstyle') {\n          changeAStyle(helpers, info, api);\n        } else if (evt.name === 'fileinput') {\n          changeFileInput(helpers, info, state, api);\n        } else if (evt.name === 'isDecorative') {\n          if (api.getData().isDecorative) {\n            api.disable('alt');\n          } else {\n            api.enable('alt');\n          }\n        }\n      };\n    };\n    var closeHandler = function (state) {\n      return function () {\n        state.open = false;\n      };\n    };\n    var makeDialogBody = function (info) {\n      if (info.hasAdvTab || info.hasUploadUrl || info.hasUploadHandler) {\n        var tabPanel = {\n          type: 'tabpanel',\n          tabs: flatten([\n            [MainTab.makeTab(info)],\n            info.hasAdvTab ? [AdvTab.makeTab(info)] : [],\n            info.hasUploadTab && (info.hasUploadUrl || info.hasUploadHandler) ? [UploadTab.makeTab(info)] : []\n          ])\n        };\n        return tabPanel;\n      } else {\n        var panel = {\n          type: 'panel',\n          items: MainTab.makeItems(info)\n        };\n        return panel;\n      }\n    };\n    var makeDialog = function (helpers) {\n      return function (info) {\n        var state = createState(info);\n        return {\n          title: 'Insert/Edit Image',\n          size: 'normal',\n          body: makeDialogBody(info),\n          buttons: [\n            {\n              type: 'cancel',\n              name: 'cancel',\n              text: 'Cancel'\n            },\n            {\n              type: 'submit',\n              name: 'save',\n              text: 'Save',\n              primary: true\n            }\n          ],\n          initialData: fromImageData(info.image),\n          onSubmit: helpers.onSubmit(info),\n          onChange: changeHandler(helpers, info, state),\n          onClose: closeHandler(state)\n        };\n      };\n    };\n    var submitHandler = function (editor) {\n      return function (info) {\n        return function (api) {\n          var data = deepMerge(fromImageData(info.image), api.getData());\n          editor.execCommand('mceUpdateImage', false, toImageData(data, info.hasAccessibilityOptions));\n          editor.editorUpload.uploadImagesAuto();\n          api.close();\n        };\n      };\n    };\n    var imageSize = function (editor) {\n      return function (url) {\n        if (!isSafeImageUrl(editor, url)) {\n          return global$4.resolve({\n            width: '',\n            height: ''\n          });\n        } else {\n          return getImageSize(editor.documentBaseURI.toAbsolute(url)).then(function (dimensions) {\n            return {\n              width: String(dimensions.width),\n              height: String(dimensions.height)\n            };\n          });\n        }\n      };\n    };\n    var createBlobCache = function (editor) {\n      return function (file, blobUri, dataUrl) {\n        return editor.editorUpload.blobCache.create({\n          blob: file,\n          blobUri: blobUri,\n          name: file.name ? file.name.replace(/\\.[^\\.]+$/, '') : null,\n          filename: file.name,\n          base64: dataUrl.split(',')[1]\n        });\n      };\n    };\n    var addToBlobCache = function (editor) {\n      return function (blobInfo) {\n        editor.editorUpload.blobCache.add(blobInfo);\n      };\n    };\n    var alertErr = function (editor) {\n      return function (message) {\n        editor.windowManager.alert(message);\n      };\n    };\n    var normalizeCss = function (editor) {\n      return function (cssText) {\n        return normalizeCss$1(editor, cssText);\n      };\n    };\n    var parseStyle = function (editor) {\n      return function (cssText) {\n        return editor.dom.parseStyle(cssText);\n      };\n    };\n    var serializeStyle = function (editor) {\n      return function (stylesArg, name) {\n        return editor.dom.serializeStyle(stylesArg, name);\n      };\n    };\n    var uploadImage = function (editor) {\n      return function (blobInfo) {\n        return global$1(editor).upload([blobInfo], false).then(function (results) {\n          if (results.length === 0) {\n            return global$4.reject('Failed to upload image');\n          } else if (results[0].status === false) {\n            return global$4.reject(results[0].error.message);\n          } else {\n            return results[0];\n          }\n        });\n      };\n    };\n    var Dialog = function (editor) {\n      var helpers = {\n        onSubmit: submitHandler(editor),\n        imageSize: imageSize(editor),\n        addToBlobCache: addToBlobCache(editor),\n        createBlobCache: createBlobCache(editor),\n        alertErr: alertErr(editor),\n        normalizeCss: normalizeCss(editor),\n        parseStyle: parseStyle(editor),\n        serializeStyle: serializeStyle(editor),\n        uploadImage: uploadImage(editor)\n      };\n      var open = function () {\n        collect(editor).then(makeDialog(helpers)).then(editor.windowManager.open);\n      };\n      return { open: open };\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mceImage', Dialog(editor).open);\n      editor.addCommand('mceUpdateImage', function (_ui, data) {\n        editor.undoManager.transact(function () {\n          return insertOrUpdateImage(editor, data);\n        });\n      });\n    };\n\n    var hasImageClass = function (node) {\n      var className = node.attr('class');\n      return className && /\\bimage\\b/.test(className);\n    };\n    var toggleContentEditableState = function (state) {\n      return function (nodes) {\n        var i = nodes.length;\n        var toggleContentEditable = function (node) {\n          node.attr('contenteditable', state ? 'true' : null);\n        };\n        while (i--) {\n          var node = nodes[i];\n          if (hasImageClass(node)) {\n            node.attr('contenteditable', state ? 'false' : null);\n            global.each(node.getAll('figcaption'), toggleContentEditable);\n          }\n        }\n      };\n    };\n    var setup = function (editor) {\n      editor.on('PreInit', function () {\n        editor.parser.addNodeFilter('figure', toggleContentEditableState(true));\n        editor.serializer.addNodeFilter('figure', toggleContentEditableState(false));\n      });\n    };\n\n    var register = function (editor) {\n      editor.ui.registry.addToggleButton('image', {\n        icon: 'image',\n        tooltip: 'Insert/edit image',\n        onAction: Dialog(editor).open,\n        onSetup: function (buttonApi) {\n          buttonApi.setActive(isNonNullable(getSelectedImage(editor)));\n          return editor.selection.selectorChangedWithUnbind('img:not([data-mce-object],[data-mce-placeholder]),figure.image', buttonApi.setActive).unbind;\n        }\n      });\n      editor.ui.registry.addMenuItem('image', {\n        icon: 'image',\n        text: 'Image...',\n        onAction: Dialog(editor).open\n      });\n      editor.ui.registry.addContextMenu('image', {\n        update: function (element) {\n          return isFigure(element) || isImage(element) && !isPlaceholderImage(element) ? ['image'] : [];\n        }\n      });\n    };\n\n    function Plugin () {\n      global$6.add('image', function (editor) {\n        setup(editor);\n        register(editor);\n        register$1(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"image\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/image')\n//   ES2015:\n//     import 'tinymce/plugins/image'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/image/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,WAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,MAAM;AACjC,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,UAAU;AAAA;AAAA;AAG5B,UAAI,KAAK,SAAU,GAAG;AACpB,eAAO,SAAU,GAAG;AAClB,iBAAO,MAAM;AAAA;AAAA;AAGjB,UAAI,WAAW,OAAO;AACtB,UAAI,WAAW,OAAO;AACtB,UAAI,UAAU,OAAO;AACrB,UAAI,SAAS,GAAG;AAChB,UAAI,YAAY,aAAa;AAC7B,UAAI,aAAa,SAAU,GAAG;AAC5B,eAAO,MAAM,QAAQ,MAAM;AAAA;AAE7B,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,CAAC,WAAW;AAAA;AAErB,UAAI,aAAa,aAAa;AAC9B,UAAI,WAAW,aAAa;AAE5B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,OAAO,OAAO;AAClB,UAAI,iBAAiB,OAAO;AAC5B,UAAI,OAAO,SAAU,KAAK,GAAG;AAC3B,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,IAAI;AACZ,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,SAAS,SAAU,GAAG;AACxB,eAAO,SAAU,GAAG,GAAG;AACrB,YAAE,KAAK;AAAA;AAAA;AAGX,UAAI,iBAAiB,SAAU,KAAK,MAAM,QAAQ,SAAS;AACzD,YAAI,IAAI;AACR,aAAK,KAAK,SAAU,GAAG,GAAG;AACxB,UAAC,MAAK,GAAG,KAAK,SAAS,SAAS,GAAG;AAAA;AAErC,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,KAAK,MAAM;AAChC,YAAI,IAAI;AACR,uBAAe,KAAK,MAAM,OAAO,IAAI;AACrC,eAAO;AAAA;AAET,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAElC,UAAI,oBAAoB,SAAU,KAAK,KAAK;AAC1C,eAAO,IAAI,KAAK,QAAQ,IAAI,SAAS,UAAa,IAAI,SAAS;AAAA;AAGjE,UAAI,aAAa,MAAM,UAAU;AACjC,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,KAAK;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;AAAA;AAE3E,qBAAW,MAAM,GAAG,GAAG;AAAA;AAEzB,eAAO;AAAA;AAET,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,eAAO,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,MAAM,SAAS;AAAA;AAEnE,UAAI,OAAO,SAAU,IAAI;AACvB,eAAO,IAAI,IAAI;AAAA;AAEjB,UAAI,UAAU,SAAU,KAAK,GAAG;AAC9B,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,IAAI,EAAE,IAAI,IAAI;AAClB,cAAI,EAAE,UAAU;AACd,mBAAO;AAAA;AAAA;AAGX,eAAO,SAAS;AAAA;AAGlB,aAAO,WAAW,cAAc,SAAS,SAAS;AAElD,UAAI,SAAS,SAAU,KAAK,KAAK,OAAO;AACtC,YAAI,SAAS,UAAU,UAAU,UAAU,SAAS,QAAQ;AAC1D,cAAI,aAAa,KAAK,QAAQ;AAAA,eACzB;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAa,OAAO,eAAe;AAC7F,gBAAM,IAAI,MAAM;AAAA;AAAA;AAGpB,UAAI,MAAM,SAAU,SAAS,KAAK,OAAO;AACvC,eAAO,QAAQ,KAAK,KAAK;AAAA;AAE3B,UAAI,SAAS,SAAU,SAAS,KAAK;AACnC,gBAAQ,IAAI,gBAAgB;AAAA;AAG9B,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,IAAI,cAAc;AAC5B,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,mBAAmB,IAAI,WAAW,SAAS,GAAG;AACrD,kBAAQ,MAAM,yCAAyC;AACvD,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,QAAQ,IAAI,WAAW;AAAA;AAEhC,UAAI,UAAU,SAAU,KAAK,OAAO;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,cAAc;AAC7B,eAAO,QAAQ;AAAA;AAEjB,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,eAAe;AAC9B,eAAO,QAAQ;AAAA;AAEjB,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,EAAE,KAAK;AAAA;AAEhB,UAAI,YAAY,SAAU,QAAQ,GAAG,GAAG;AACtC,eAAO,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,IAAI,IAAI;AAAA;AAE9D,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS,oBAAoB,MAAM;AAAA;AAEnD,UAAI,YAAY,SAAU,QAAQ;AAChC,eAAO,OAAO,SAAS,gBAAgB,OAAO;AAAA;AAEhD,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,SAAS,mBAAmB,MAAM;AAAA;AAElD,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS,qBAAqB,IAAI;AAAA;AAElD,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,OAAO,SAAS,qBAAqB,MAAM;AAAA;AAEpD,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS,eAAe,OAAO;AAAA;AAE/C,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,OAAO,SAAS,iBAAiB,OAAO;AAAA;AAEjD,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,SAAS,cAAc;AAAA;AAEvC,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,cAAc,OAAO,SAAS;AAAA;AAEvC,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,cAAc,OAAO,SAAS;AAAA;AAEvC,UAAI,2BAA2B,SAAU,QAAQ;AAC/C,eAAO,OAAO,SAAS,yBAAyB,OAAO;AAAA;AAEzD,UAAI,4BAA4B,SAAU,QAAQ;AAChD,eAAO,OAAO,SAAS,qBAAqB,MAAM;AAAA;AAGpD,UAAI,oBAAoB,SAAU,MAAM,MAAM;AAC5C,eAAO,KAAK,IAAI,SAAS,MAAM,KAAK,SAAS,MAAM;AAAA;AAErD,UAAI,eAAe,SAAU,KAAK;AAChC,eAAO,IAAI,SAAS,SAAU,UAAU;AACtC,cAAI,MAAM,SAAS,cAAc;AACjC,cAAI,OAAO,SAAU,YAAY;AAC/B,gBAAI,SAAS,IAAI,UAAU;AAC3B,gBAAI,IAAI,YAAY;AAClB,kBAAI,WAAW,YAAY;AAAA;AAE7B,qBAAS;AAAA;AAEX,cAAI,SAAS,WAAY;AACvB,gBAAI,QAAQ,kBAAkB,IAAI,OAAO,IAAI;AAC7C,gBAAI,SAAS,kBAAkB,IAAI,QAAQ,IAAI;AAC/C,gBAAI,aAAa;AAAA,cACf;AAAA,cACA;AAAA;AAEF,iBAAK,SAAS,QAAQ;AAAA;AAExB,cAAI,UAAU,WAAY;AACxB,iBAAK,SAAS,OAAO,yCAAyC;AAAA;AAEhE,cAAI,QAAQ,IAAI;AAChB,gBAAM,aAAa;AACnB,gBAAM,WAAW;AACjB,gBAAM,SAAS,MAAM,OAAO;AAC5B,gBAAM,QAAQ,MAAM,SAAS;AAC7B,mBAAS,KAAK,YAAY;AAC1B,cAAI,MAAM;AAAA;AAAA;AAGd,UAAI,oBAAoB,SAAU,OAAO;AACvC,YAAI,OAAO;AACT,kBAAQ,MAAM,QAAQ,OAAO;AAAA;AAE/B,eAAO;AAAA;AAET,UAAI,iBAAiB,SAAU,OAAO;AACpC,YAAI,MAAM,SAAS,KAAK,WAAW,KAAK,QAAQ;AAC9C,mBAAS;AAAA;AAEX,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,KAAK;AAChC,YAAI,IAAI,QAAQ;AACd,cAAI,cAAc,OAAO,IAAI,QAAQ,MAAM;AAC3C,kBAAQ,YAAY;AAAA,iBACf;AACH,kBAAI,gBAAgB,IAAI,iBAAiB,YAAY;AACrD,kBAAI,kBAAkB,IAAI,mBAAmB,YAAY;AACzD,kBAAI,mBAAmB,IAAI,oBAAoB,YAAY;AAC3D,kBAAI,iBAAiB,IAAI,kBAAkB,YAAY;AACvD;AAAA,iBACG;AACH,kBAAI,gBAAgB,IAAI,iBAAiB,YAAY;AACrD,kBAAI,kBAAkB,IAAI,mBAAmB,YAAY;AACzD,kBAAI,mBAAmB,IAAI,oBAAoB,YAAY;AAC3D,kBAAI,iBAAiB,IAAI,kBAAkB,YAAY;AACvD;AAAA,iBACG;AACH,kBAAI,gBAAgB,IAAI,iBAAiB,YAAY;AACrD,kBAAI,kBAAkB,IAAI,mBAAmB,YAAY;AACzD,kBAAI,mBAAmB,IAAI,oBAAoB,YAAY;AAC3D,kBAAI,iBAAiB,IAAI,kBAAkB,YAAY;AACvD;AAAA,iBACG;AACH,kBAAI,gBAAgB,IAAI,iBAAiB,YAAY;AACrD,kBAAI,kBAAkB,IAAI,mBAAmB,YAAY;AACzD,kBAAI,mBAAmB,IAAI,oBAAoB,YAAY;AAC3D,kBAAI,iBAAiB,IAAI,kBAAkB,YAAY;AAAA;AAEzD,iBAAO,IAAI;AAAA;AAEb,eAAO;AAAA;AAET,UAAI,kBAAkB,SAAU,QAAQ,UAAU;AAChD,YAAI,YAAY,aAAa;AAC7B,YAAI,SAAS,YAAY;AACvB,mBAAS,KAAK;AAAA,YACZ,KAAK;AAAA,YACL,SAAS,SAAU,MAAM;AACvB,uBAAS,KAAK,MAAM;AAAA;AAAA;AAAA,mBAGf,WAAW,YAAY;AAChC,oBAAU;AAAA,eACL;AACL,mBAAS;AAAA;AAAA;AAGb,UAAI,gBAAgB,SAAU,QAAQ,MAAM,QAAQ;AAClD,YAAI,cAAc,WAAY;AAC5B,iBAAO,SAAS,OAAO,UAAU;AACjC,cAAI,OAAO,WAAW;AACpB,mBAAO,UAAU,OAAO;AACxB,mBAAO;AAAA;AAAA;AAGX,eAAO,SAAS,WAAY;AAC1B,cAAI,CAAC,KAAK,SAAS,CAAC,KAAK,UAAU,cAAc,SAAS;AACxD,mBAAO,IAAI,WAAW,QAAQ;AAAA,cAC5B,OAAO,OAAO,OAAO;AAAA,cACrB,QAAQ,OAAO,OAAO;AAAA;AAAA;AAG1B;AAAA;AAEF,eAAO,UAAU;AAAA;AAEnB,UAAI,gBAAgB,SAAU,MAAM;AAClC,eAAO,IAAI,SAAS,SAAU,SAAS,QAAQ;AAC7C,cAAI,SAAS,IAAI;AACjB,iBAAO,SAAS,WAAY;AAC1B,oBAAQ,OAAO;AAAA;AAEjB,iBAAO,UAAU,WAAY;AAC3B,mBAAO,OAAO,MAAM;AAAA;AAEtB,iBAAO,cAAc;AAAA;AAAA;AAGzB,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,aAAa,SAAU,QAAO,aAAa,sBAAsB,OAAO,aAAa;AAAA;AAErG,UAAI,iBAAiB,SAAU,QAAQ,KAAK;AAC1C,eAAO,SAAS,UAAU,KAAK,OAAO,OAAO;AAAA;AAG/C,UAAI,MAAM,SAAS;AACnB,UAAI,YAAY,SAAU,OAAO;AAC/B,YAAI,MAAM,MAAM,cAAc,MAAM,MAAM,eAAe,MAAM,MAAM,eAAe,MAAM,MAAM,aAAa;AAC3G,iBAAO,kBAAkB,MAAM,MAAM;AAAA,eAChC;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,YAAY,SAAU,OAAO;AAC/B,YAAI,MAAM,MAAM,aAAa,MAAM,MAAM,gBAAgB,MAAM,MAAM,cAAc,MAAM,MAAM,cAAc;AAC3G,iBAAO,kBAAkB,MAAM,MAAM;AAAA,eAChC;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,YAAY,SAAU,OAAO;AAC/B,YAAI,MAAM,MAAM,aAAa;AAC3B,iBAAO,kBAAkB,MAAM,MAAM;AAAA,eAChC;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,YAAY,SAAU,OAAO,MAAM;AACrC,YAAI,MAAM,aAAa,OAAO;AAC5B,iBAAO,MAAM,aAAa;AAAA,eACrB;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,OAAO,MAAM;AACpC,eAAO,MAAM,MAAM,QAAQ,MAAM,MAAM,QAAQ;AAAA;AAEjD,UAAI,aAAa,SAAU,OAAO;AAChC,eAAO,MAAM,eAAe,QAAQ,MAAM,WAAW,aAAa;AAAA;AAEpE,UAAI,eAAe,SAAU,OAAO,MAAM,OAAO;AAC/C,YAAI,UAAU,IAAI;AAChB,gBAAM,gBAAgB;AAAA,eACjB;AACL,gBAAM,aAAa,MAAM;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,OAAO;AAClC,YAAI,YAAY,IAAI,OAAO,UAAU,EAAE,OAAO;AAC9C,YAAI,YAAY,WAAW;AAC3B,kBAAU,YAAY;AACtB,kBAAU,YAAY,IAAI,OAAO,cAAc,EAAE,iBAAiB,UAAU;AAC5E,kBAAU,kBAAkB;AAAA;AAE9B,UAAI,eAAe,SAAU,OAAO;AAClC,YAAI,YAAY,MAAM;AACtB,YAAI,YAAY,OAAO;AACvB,YAAI,OAAO;AAAA;AAEb,UAAI,gBAAgB,SAAU,OAAO;AACnC,YAAI,WAAW,QAAQ;AACrB,uBAAa;AAAA,eACR;AACL,uBAAa;AAAA;AAAA;AAGjB,UAAI,iBAAiB,SAAU,OAAO,eAAc;AAClD,YAAI,YAAY,MAAM,aAAa;AACnC,YAAI,QAAQ,cAAa,cAAc,OAAO,YAAY;AAC1D,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,aAAa,SAAS;AAC5B,gBAAM,aAAa,kBAAkB;AAAA,eAChC;AACL,gBAAM,gBAAgB;AAAA;AAAA;AAG1B,UAAI,UAAU,SAAU,MAAM,eAAc;AAC1C,eAAO,SAAU,OAAO,OAAM,OAAO;AACnC,cAAI,MAAM,MAAM,QAAO;AACrB,kBAAM,MAAM,SAAQ,eAAe;AACnC,2BAAe,OAAO;AAAA,iBACjB;AACL,yBAAa,OAAO,OAAM;AAAA;AAAA;AAAA;AAIhC,UAAI,UAAU,SAAU,OAAO,MAAM;AACnC,YAAI,MAAM,MAAM,OAAO;AACrB,iBAAO,kBAAkB,MAAM,MAAM;AAAA,eAChC;AACL,iBAAO,UAAU,OAAO;AAAA;AAAA;AAG5B,UAAI,YAAY,SAAU,OAAO,OAAO;AACtC,YAAI,UAAU,eAAe;AAC7B,cAAM,MAAM,aAAa;AACzB,cAAM,MAAM,cAAc;AAAA;AAE5B,UAAI,YAAY,SAAU,OAAO,OAAO;AACtC,YAAI,UAAU,eAAe;AAC7B,cAAM,MAAM,YAAY;AACxB,cAAM,MAAM,eAAe;AAAA;AAE7B,UAAI,YAAY,SAAU,OAAO,OAAO;AACtC,YAAI,UAAU,eAAe;AAC7B,cAAM,MAAM,cAAc;AAAA;AAE5B,UAAI,iBAAiB,SAAU,OAAO,OAAO;AAC3C,cAAM,MAAM,cAAc;AAAA;AAE5B,UAAI,iBAAiB,SAAU,OAAO;AACpC,eAAO,SAAS,OAAO;AAAA;AAEzB,UAAI,WAAW,SAAU,KAAK;AAC5B,eAAO,IAAI,aAAa;AAAA;AAE1B,UAAI,UAAU,SAAU,KAAK;AAC3B,eAAO,IAAI,aAAa;AAAA;AAE1B,UAAI,kBAAkB,SAAU,OAAO;AACrC,eAAO,IAAI,UAAU,OAAO,OAAO,WAAW,KAAK,IAAI,UAAU,OAAO,YAAY;AAAA;AAEtF,UAAI,SAAS,SAAU,OAAO;AAC5B,YAAI,gBAAgB,QAAQ;AAC1B,iBAAO;AAAA,eACF;AACL,iBAAO,UAAU,OAAO;AAAA;AAAA;AAG5B,UAAI,cAAc,WAAY;AAC5B,eAAO;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,cAAc;AAAA;AAAA;AAGlB,UAAI,gBAAgB,SAAU,eAAc,MAAM;AAChD,YAAI,QAAQ,SAAS,cAAc;AACnC,qBAAa,OAAO,SAAS,KAAK;AAClC,YAAI,UAAU,UAAU,KAAK,WAAW,IAAI;AAC1C,oBAAU,OAAO,KAAK;AAAA;AAExB,YAAI,UAAU,UAAU,KAAK,WAAW,IAAI;AAC1C,oBAAU,OAAO,KAAK;AAAA;AAExB,YAAI,UAAU,UAAU,KAAK,WAAW,IAAI;AAC1C,oBAAU,OAAO,KAAK;AAAA;AAExB,YAAI,eAAe,UAAU,KAAK,gBAAgB,IAAI;AACpD,yBAAe,OAAO,KAAK;AAAA;AAE7B,eAAO,cAAa,MAAM,aAAa;AAAA;AAEzC,UAAI,SAAS,SAAU,eAAc,MAAM;AACzC,YAAI,QAAQ,SAAS,cAAc;AACnC,cAAM,eAAc,SAAS,SAAS,IAAI,OAAO,EAAE,SAAS,UAAU;AACtE,eAAO,OAAO,KAAK,KAAK,KAAK;AAC7B,YAAI,KAAK,SAAS;AAChB,cAAI,SAAS,IAAI,OAAO,UAAU,EAAE,OAAO;AAC3C,iBAAO,YAAY;AACnB,iBAAO,YAAY,IAAI,OAAO,cAAc,EAAE,iBAAiB,UAAU;AACzE,iBAAO,kBAAkB;AACzB,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,OAAO,SAAU,eAAc,OAAO;AACxC,eAAO;AAAA,UACL,KAAK,UAAU,OAAO;AAAA,UACtB,KAAK,OAAO;AAAA,UACZ,OAAO,UAAU,OAAO;AAAA,UACxB,OAAO,QAAQ,OAAO;AAAA,UACtB,QAAQ,QAAQ,OAAO;AAAA,UACvB,OAAO,UAAU,OAAO;AAAA,UACxB,OAAO,cAAa,UAAU,OAAO;AAAA,UACrC,SAAS,WAAW;AAAA,UACpB,QAAQ,UAAU;AAAA,UAClB,QAAQ,UAAU;AAAA,UAClB,QAAQ,UAAU;AAAA,UAClB,aAAa,SAAS,OAAO;AAAA,UAC7B,cAAc,gBAAgB;AAAA;AAAA;AAGlC,UAAI,aAAa,SAAU,OAAO,SAAS,SAAS,MAAM,MAAK;AAC7D,YAAI,QAAQ,UAAU,QAAQ,OAAO;AACnC,eAAI,OAAO,MAAM,QAAQ;AAAA;AAAA;AAG7B,UAAI,SAAS,SAAU,OAAO,KAAK,cAAc;AAC/C,YAAI,cAAc;AAChB,cAAI,UAAU,OAAO,QAAQ;AAC7B,cAAI,aAAa,aAAa,QAAQ;AACtC,cAAI,YAAY,OAAO;AAAA,eAClB;AACL,cAAI,OAAO,MAAM;AACf,gBAAI,aAAa,aAAa,QAAQ;AACtC,mBAAO,YAAY;AAAA,iBACd;AACL,gBAAI,aAAa,aAAa,QAAQ;AACtC,gBAAI,YAAY,OAAO;AAAA;AAEzB,cAAI,IAAI,UAAU,OAAO,YAAY,gBAAgB;AACnD,gBAAI,UAAU,OAAO,QAAQ;AAAA;AAAA;AAAA;AAInC,UAAI,YAAY,SAAU,OAAO,SAAS,SAAS;AACjD,YAAI,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,iBAAiB,QAAQ,cAAc;AAChF,iBAAO,OAAO,QAAQ,KAAK,QAAQ;AAAA;AAAA;AAGvC,UAAI,aAAa,SAAU,MAAK,eAAc;AAC5C,eAAO,SAAU,OAAO,MAAM,OAAO;AACnC,eAAI,OAAO;AACX,yBAAe,OAAO;AAAA;AAAA;AAG1B,UAAI,QAAQ,SAAU,eAAc,SAAS,OAAO;AAClD,YAAI,UAAU,KAAK,eAAc;AACjC,mBAAW,OAAO,SAAS,SAAS,WAAW,SAAU,QAAO,OAAO,QAAQ;AAC7E,iBAAO,cAAc;AAAA;AAEvB,mBAAW,OAAO,SAAS,SAAS,OAAO;AAC3C,mBAAW,OAAO,SAAS,SAAS,SAAS;AAC7C,mBAAW,OAAO,SAAS,SAAS,SAAS,QAAQ,SAAS;AAC9D,mBAAW,OAAO,SAAS,SAAS,UAAU,QAAQ,UAAU;AAChE,mBAAW,OAAO,SAAS,SAAS,SAAS;AAC7C,mBAAW,OAAO,SAAS,SAAS,SAAS,WAAW,SAAU,QAAO,OAAO;AAC9E,iBAAO,aAAa,QAAO,SAAS;AAAA,WACnC;AACH,mBAAW,OAAO,SAAS,SAAS,UAAU,WAAW,WAAW;AACpE,mBAAW,OAAO,SAAS,SAAS,UAAU,WAAW,WAAW;AACpE,mBAAW,OAAO,SAAS,SAAS,UAAU,WAAW,WAAW;AACpE,mBAAW,OAAO,SAAS,SAAS,eAAe,WAAW,gBAAgB;AAC9E,kBAAU,OAAO,SAAS;AAAA;AAG5B,UAAI,iBAAiB,SAAU,QAAQ,SAAS;AAC9C,YAAI,MAAM,OAAO,IAAI,OAAO,MAAM;AAClC,YAAI,YAAY,aAAa;AAC7B,YAAI,aAAa,OAAO,IAAI,OAAO,MAAM,OAAO,IAAI,OAAO,UAAU;AACrE,eAAO,OAAO,IAAI,OAAO,UAAU;AAAA;AAErC,UAAI,mBAAmB,SAAU,QAAQ;AACvC,YAAI,SAAS,OAAO,UAAU;AAC9B,YAAI,YAAY,OAAO,IAAI,UAAU,QAAQ;AAC7C,YAAI,WAAW;AACb,iBAAO,OAAO,IAAI,OAAO,OAAO,WAAW;AAAA;AAE7C,YAAI,UAAW,QAAO,aAAa,SAAS,mBAAmB,UAAU;AACvE,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,iBAAiB,SAAU,QAAQ,QAAQ;AAC7C,YAAI,MAAM,OAAO;AACjB,YAAI,oBAAoB,OAAO,OAAO,OAAO,wBAAwB,SAAU,GAAG,WAAW;AAC3F,iBAAO,CAAC,OAAO,OAAO,aAAa,WAAW;AAAA;AAEhD,YAAI,YAAY,IAAI,UAAU,OAAO,YAAY,SAAU,MAAM;AAC/D,iBAAO,kBAAkB,mBAAmB,KAAK;AAAA,WAChD,OAAO;AACV,YAAI,WAAW;AACb,iBAAO,IAAI,MAAM,WAAW;AAAA,eACvB;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,6BAA6B,SAAU,QAAQ;AACjD,YAAI,QAAQ,iBAAiB;AAC7B,eAAO,QAAQ,KAAK,SAAU,KAAK;AACjC,iBAAO,eAAe,QAAQ;AAAA,WAC7B,SAAS;AAAA;AAEd,UAAI,qBAAqB,SAAU,QAAQ,MAAM;AAC/C,YAAI,MAAM,OAAO,SAAU,KAAK;AAC9B,iBAAO,eAAe,QAAQ;AAAA,WAC7B;AACH,eAAO,IAAI,UAAU,KAAK,eAAe;AACzC,eAAO;AACP,eAAO,UAAU,WAAW,IAAI;AAChC,YAAI,cAAc,OAAO,IAAI,OAAO,6BAA6B;AACjE,eAAO,IAAI,UAAU,aAAa,eAAe;AACjD,YAAI,SAAS,cAAc;AACzB,cAAI,SAAS,eAAe,QAAQ;AACpC,iBAAO,UAAU,OAAO;AAAA,eACnB;AACL,iBAAO,UAAU,OAAO;AAAA;AAAA;AAG5B,UAAI,cAAc,SAAU,QAAQ,OAAO;AACzC,eAAO,IAAI,UAAU,OAAO,OAAO,MAAM,aAAa;AAAA;AAExD,UAAI,cAAc,SAAU,QAAQ,OAAO;AACzC,YAAI,OAAO;AACT,cAAI,MAAM,OAAO,IAAI,GAAG,MAAM,YAAY,kBAAkB,MAAM,aAAa;AAC/E,iBAAO,IAAI,OAAO;AAClB,iBAAO;AACP,iBAAO;AACP,cAAI,OAAO,IAAI,QAAQ,OAAO,YAAY;AACxC,mBAAO,WAAW;AAClB,mBAAO,UAAU;AAAA;AAAA;AAAA;AAIvB,UAAI,4BAA4B,SAAU,QAAQ,MAAM;AACtD,YAAI,QAAQ,iBAAiB;AAC7B,cAAM,SAAU,KAAK;AACnB,iBAAO,eAAe,QAAQ;AAAA,WAC7B,MAAM;AACT,oBAAY,QAAQ;AACpB,YAAI,SAAS,MAAM,aAAa;AAC9B,cAAI,SAAS,MAAM;AACnB,yBAAe,QAAQ;AACvB,iBAAO,UAAU,OAAO,MAAM;AAAA,eACzB;AACL,iBAAO,UAAU,OAAO;AACxB,wBAAc,QAAQ,MAAM;AAAA;AAAA;AAGhC,UAAI,oBAAoB,SAAU,QAAQ,MAAM;AAC9C,YAAI,MAAM,KAAK;AACf,eAAO,SAAS,SAAS,IAAI,OAAO,EAAE,KAAK,eAAe,QAAQ,OAAO,MAAM;AAAA;AAEjF,UAAI,sBAAsB,SAAU,QAAQ,aAAa;AACvD,YAAI,QAAQ,iBAAiB;AAC7B,YAAI,OAAO;AACT,cAAI,oBAAoB,KAAK,SAAU,KAAK;AAC1C,mBAAO,eAAe,QAAQ;AAAA,aAC7B;AACH,cAAI,OAAO,SAAS,SAAS,IAAI,oBAAoB;AACrD,cAAI,gBAAgB,kBAAkB,QAAQ;AAC9C,cAAI,KAAK,KAAK;AACZ,sCAA0B,QAAQ;AAAA,iBAC7B;AACL,wBAAY,QAAQ;AAAA;AAAA,mBAEb,YAAY,KAAK;AAC1B,6BAAmB,QAAQ,SAAS,SAAS,IAAI,gBAAgB;AAAA;AAAA;AAIrE,UAAI,OAAO,SAAU,KAAK,IAAI;AAC5B,YAAI,cAAc,SAAS,QAAQ,SAAS;AAC5C,eAAO,cAAc,UAAU,KAAK,MAAM;AAAA;AAE5C,UAAI,YAAY,SAAU,QAAQ;AAChC,eAAO,WAAY;AACjB,cAAI,UAAU;AACd,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,oBAAQ,MAAM,UAAU;AAAA;AAE1B,cAAI,QAAQ,WAAW,GAAG;AACxB,kBAAM,IAAI,MAAM;AAAA;AAElB,cAAI,MAAM;AACV,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAI,YAAY,QAAQ;AACxB,qBAAS,OAAO,WAAW;AACzB,kBAAI,IAAI,WAAW,MAAM;AACvB,oBAAI,OAAO,OAAO,IAAI,MAAM,UAAU;AAAA;AAAA;AAAA;AAI5C,iBAAO;AAAA;AAAA;AAGX,UAAI,YAAY,UAAU;AAE1B,UAAI,aAAa,SAAU,GAAG;AAC5B,eAAO,EAAE,SAAS;AAAA;AAGpB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,WAAW,SAAU,MAAM;AAC7B,eAAO,SAAS,KAAK,SAAS,KAAK,QAAQ;AAAA;AAE7C,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,KAAK,OAAO;AACvB,iBAAO,KAAK;AAAA,mBACH,SAAS,KAAK,QAAQ;AAC/B,iBAAO,KAAK;AAAA,eACP;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,eAAe,SAAU,MAAM,cAAc;AAC/C,YAAI,MAAM;AACV,eAAO,KAAK,MAAM,SAAU,MAAM;AAChC,cAAI,OAAO,QAAQ;AACnB,cAAI,KAAK,SAAS,QAAW;AAC3B,gBAAI,QAAQ,aAAa,KAAK,MAAM;AACpC,gBAAI,KAAK;AAAA,cACP;AAAA,cACA;AAAA;AAAA,iBAEG;AACL,gBAAI,QAAQ,aAAa;AACzB,gBAAI,KAAK;AAAA,cACP;AAAA,cACA;AAAA;AAAA;AAAA;AAIN,eAAO;AAAA;AAET,UAAI,YAAY,SAAU,WAAW;AACnC,YAAI,cAAc,QAAQ;AACxB,sBAAY;AAAA;AAEd,eAAO,SAAU,MAAM;AACrB,cAAI,MAAM;AACR,mBAAO,SAAS,KAAK,MAAM,IAAI,SAAU,OAAM;AAC7C,qBAAO,aAAa,OAAM;AAAA;AAAA,iBAEvB;AACL,mBAAO,SAAS;AAAA;AAAA;AAAA;AAItB,UAAI,WAAW,SAAU,MAAM;AAC7B,eAAO,UAAU,UAAU;AAAA;AAE7B,UAAI,UAAU,SAAU,MAAM;AAC5B,eAAO,IAAI,MAAM;AAAA;AAEnB,UAAI,oBAAoB,SAAU,MAAM,OAAO;AAC7C,eAAO,QAAQ,MAAM,SAAU,MAAM;AACnC,cAAI,QAAQ,OAAO;AACjB,mBAAO,kBAAkB,KAAK,OAAO;AAAA,qBAC5B,KAAK,UAAU,OAAO;AAC/B,mBAAO,SAAS,KAAK;AAAA,iBAChB;AACL,mBAAO,SAAS;AAAA;AAAA;AAAA;AAItB,UAAI,YAAY,SAAU,SAAS,OAAO;AACxC,eAAO,QAAQ,KAAK,SAAU,MAAM;AAClC,iBAAO,kBAAkB,MAAM;AAAA;AAAA;AAGnC,UAAI,YAAY;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,YAAY,SAAU,OAAO;AAC/B,eAAO;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA;AAAA,YAER;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,OAAO;AAAA,gBACL;AAAA,kBACE,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,WAAW;AAAA;AAAA,gBAEb;AAAA,kBACE,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,WAAW;AAAA;AAAA,gBAEb;AAAA,kBACE,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,WAAW;AAAA;AAAA,gBAEb;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,OAAO;AAAA,oBACL;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA,oBAET;AAAA,sBACE,MAAM;AAAA,sBACN,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASvB,UAAI,SAAS,EAAE,SAAS;AAExB,UAAI,UAAU,SAAU,QAAQ;AAC9B,YAAI,mBAAmB,UAAU,UAAU,SAAU,MAAM;AACzD,iBAAO,OAAO,WAAW,KAAK,SAAS,KAAK,KAAK;AAAA;AAEnD,YAAI,kBAAkB,IAAI,SAAS,SAAU,WAAW;AACtD,0BAAgB,QAAQ,SAAU,WAAW;AAC3C,sBAAU,iBAAiB,WAAW,IAAI,SAAU,OAAO;AACzD,qBAAO,QAAQ;AAAA,gBACb,CAAC;AAAA,kBACG,MAAM;AAAA,kBACN,OAAO;AAAA;AAAA,gBAEX;AAAA;AAAA;AAAA;AAAA;AAKR,YAAI,YAAY,UAAU,SAAS,aAAa;AAChD,YAAI,cAAc,UAAU;AAC5B,YAAI,iBAAiB,aAAa;AAClC,YAAI,iBAAiB,aAAa;AAClC,YAAI,qBAAqB,iBAAiB;AAC1C,YAAI,QAAQ,2BAA2B;AACvC,YAAI,mBAAmB,eAAe;AACtC,YAAI,kBAAkB,cAAc;AACpC,YAAI,kBAAkB,cAAc;AACpC,YAAI,oBAAoB,gBAAgB;AACxC,YAAI,0BAA0B,yBAAyB;AACvD,YAAI,mBAAmB,0BAA0B;AACjD,YAAI,aAAa,SAAS,KAAK,cAAc,SAAS,OAAO,SAAU,QAAQ;AAC7E,iBAAO,SAAS,WAAW,OAAO,SAAS;AAAA;AAE7C,eAAO,gBAAgB,KAAK,SAAU,WAAW;AAC/C,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA,WAAW;AAAA,YACX,cAAc;AAAA,YACd,cAAc;AAAA,YACd,kBAAkB;AAAA,YAClB,gBAAgB;AAAA,YAChB,eAAe;AAAA,YACf,eAAe;AAAA,YACf,iBAAiB;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA;AAAA;AAAA;AAKN,UAAI,YAAY,SAAU,MAAM;AAC9B,YAAI,WAAW;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA;AAET,YAAI,YAAY,KAAK,UAAU,IAAI,SAAU,OAAO;AAClD,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP;AAAA;AAAA;AAGJ,YAAI,mBAAmB;AAAA,UACrB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,UAAU,KAAK,2BAA2B,KAAK,MAAM;AAAA;AAEvD,YAAI,aAAa;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA;AAET,YAAI,kBAAkB;AAAA,UACpB,MAAM;AAAA,UACN,MAAM;AAAA;AAER,YAAI,eAAe;AAAA,UACjB,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO,CAAC;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA;AAAA;AAGb,YAAI,YAAY,KAAK,UAAU,IAAI,SAAU,OAAO;AAClD,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP;AAAA;AAAA;AAGJ,YAAI,UAAU;AAAA,UACZ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO,CAAC;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA;AAAA;AAGb,YAAI,yBAAyB,SAAU,YAAY;AACjD,iBAAO,aAAa;AAAA,YAClB,MAAM;AAAA,YACN,SAAS;AAAA,cACP,EAAE,MAAM;AAAA;AAEd,eAAO,QAAQ;AAAA,UACb,CAAC;AAAA,UACD,UAAU;AAAA,UACV,KAAK,2BAA2B,KAAK,iBAAiB,CAAC,gBAAgB;AAAA,UACvE,KAAK,iBAAiB,CAAC,oBAAoB;AAAA,UAC3C,KAAK,gBAAgB,CAAC,cAAc;AAAA,UACpC,KAAK,gBAAgB,CAAC,mBAAmB;AAAA,UACzC,CAAC,SAAS,SAAS,IAAI,uBAAuB,KAAK,UAAU,YAAY,KAAK,mBAAmB;AAAA,YAC7F,OAAO,QAAQ;AAAA,cACb,UAAU;AAAA,cACV,KAAK,kBAAkB,CAAC,WAAW;AAAA;AAAA;AAAA;AAAA;AAK7C,UAAI,YAAY,SAAU,MAAM;AAC9B,eAAO;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,UAAU;AAAA;AAAA;AAGrB,UAAI,UAAU;AAAA,QACZ,SAAS;AAAA,QACT;AAAA;AAGF,UAAI,UAAU,SAAU,OAAO;AAC7B,YAAI,QAAQ,CAAC;AAAA,UACT,MAAM;AAAA,UACN,MAAM;AAAA;AAEV,eAAO;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,UACN;AAAA;AAAA;AAGJ,UAAI,YAAY,EAAE;AAElB,UAAI,cAAc,SAAU,MAAM;AAChC,eAAO;AAAA,UACL,WAAW,UAAU,UAAU,KAAK,WAAW,KAAK,MAAM;AAAA,UAC1D,SAAS,KAAK,MAAM;AAAA,UACpB,MAAM;AAAA;AAAA;AAGV,UAAI,gBAAgB,SAAU,OAAO;AACnC,eAAO;AAAA,UACL,KAAK;AAAA,YACH,OAAO,MAAM;AAAA,YACb,MAAM;AAAA;AAAA,UAER,QAAQ,MAAM;AAAA,UACd,KAAK,MAAM;AAAA,UACX,OAAO,MAAM;AAAA,UACb,YAAY;AAAA,YACV,OAAO,MAAM;AAAA,YACb,QAAQ,MAAM;AAAA;AAAA,UAEhB,SAAS,MAAM;AAAA,UACf,SAAS,MAAM;AAAA,UACf,OAAO,MAAM;AAAA,UACb,QAAQ,MAAM;AAAA,UACd,QAAQ,MAAM;AAAA,UACd,QAAQ,MAAM;AAAA,UACd,aAAa,MAAM;AAAA,UACnB,WAAW;AAAA,UACX,cAAc,MAAM;AAAA;AAAA;AAGxB,UAAI,cAAc,SAAU,MAAM,gBAAgB;AAChD,eAAO;AAAA,UACL,KAAK,KAAK,IAAI;AAAA,UACd,KAAK,KAAK,IAAI,WAAW,KAAK,iBAAiB,OAAO,KAAK;AAAA,UAC3D,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK,WAAW;AAAA,UACvB,QAAQ,KAAK,WAAW;AAAA,UACxB,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,UACZ,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,aAAa,KAAK;AAAA,UAClB,cAAc,KAAK;AAAA;AAAA;AAGvB,UAAI,iBAAiB,SAAU,MAAM,QAAQ;AAC3C,YAAI,CAAC,uBAAuB,KAAK,SAAS;AACxC,iBAAO,KAAK,WAAW,KAAK,SAAU,YAAY;AAChD,gBAAI,OAAO,UAAU,GAAG,WAAW,YAAY,YAAY;AACzD,qBAAO,SAAS,KAAK,aAAa;AAAA;AAEpC,mBAAO,SAAS;AAAA;AAAA;AAGpB,eAAO,SAAS;AAAA;AAElB,UAAI,gBAAgB,SAAU,MAAM,KAAK;AACvC,YAAI,OAAO,IAAI;AACf,uBAAe,MAAM,KAAK,IAAI,OAAO,KAAK,SAAU,QAAQ;AAC1D,cAAI,QAAQ;AAAA,YACV,KAAK;AAAA,cACH,OAAO;AAAA,cACP,MAAM,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAKvB,UAAI,oBAAoB,SAAU,MAAM,MAAM,MAAM;AAClD,YAAI,KAAK,kBAAkB,SAAS,KAAK,MAAM;AAC7C,eAAK,MAAM,KAAK;AAAA;AAElB,YAAI,KAAK,yBAAyB;AAChC,eAAK,eAAe,KAAK,gBAAgB,KAAK,gBAAgB;AAAA;AAEhE,YAAI,KAAK,iBAAiB,SAAS,KAAK,QAAQ;AAC9C,eAAK,QAAQ,KAAK;AAAA;AAEpB,YAAI,KAAK,eAAe;AACtB,cAAI,SAAS,KAAK,QAAQ;AACxB,iBAAK,WAAW,QAAQ,KAAK;AAAA;AAE/B,cAAI,SAAS,KAAK,SAAS;AACzB,iBAAK,WAAW,SAAS,KAAK;AAAA;AAAA;AAGlC,YAAI,SAAS,KAAK,QAAQ;AACxB,oBAAU,UAAU,KAAK,WAAW,KAAK,OAAO,KAAK,SAAU,OAAO;AACpE,iBAAK,UAAU,MAAM;AAAA;AAAA;AAGzB,YAAI,KAAK,iBAAiB;AACxB,cAAI,UAAU,KAAK,UAAU;AAC3B,iBAAK,UAAU,KAAK;AAAA;AAAA;AAGxB,YAAI,KAAK,WAAW;AAClB,cAAI,SAAS,KAAK,QAAQ;AACxB,iBAAK,QAAQ,KAAK;AAAA;AAEpB,cAAI,SAAS,KAAK,SAAS;AACzB,iBAAK,SAAS,KAAK;AAAA;AAErB,cAAI,SAAS,KAAK,SAAS;AACzB,iBAAK,SAAS,KAAK;AAAA;AAErB,cAAI,SAAS,KAAK,SAAS;AACzB,iBAAK,SAAS,KAAK;AAAA;AAErB,cAAI,SAAS,KAAK,cAAc;AAC9B,iBAAK,cAAc,KAAK;AAAA;AAAA;AAAA;AAI9B,UAAI,mBAAmB,SAAU,MAAM,KAAK;AAC1C,YAAI,OAAO,IAAI;AACf,YAAI,OAAO,KAAK,IAAI;AACpB,YAAI,SAAS,QAAW;AACtB,cAAI,UAAU,UAAU,IAAI;AAC5B,4BAAkB,MAAM,SAAS;AACjC,cAAI,QAAQ;AAAA;AAAA;AAGhB,UAAI,qBAAqB,SAAU,SAAS,MAAM,OAAO,KAAK;AAC5D,YAAI,OAAO,IAAI;AACf,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,OAAO,KAAK,IAAI,QAAQ;AAC5B,YAAI,CAAC,KAAK,SAAS,CAAC,KAAK,UAAU,KAAK,eAAe;AACrD,cAAI,WAAW,MAAM;AACnB,oBAAQ,UAAU,KAAK,KAAK,SAAU,MAAM;AAC1C,kBAAI,MAAM,MAAM;AACd,oBAAI,QAAQ,EAAE,YAAY;AAAA;AAAA,eAE3B,MAAM,SAAU,GAAG;AACpB,qBAAO,QAAQ,MAAM;AAAA;AAAA,iBAElB;AACL,gBAAI,QAAQ;AAAA,cACV,YAAY;AAAA,gBACV,OAAO;AAAA,gBACP,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAMlB,UAAI,uBAAuB,SAAU,MAAM,OAAO,KAAK;AACrD,YAAI,OAAO,IAAI;AACf,YAAI,QAAQ,UAAU,UAAU,KAAK,WAAW,KAAK,IAAI;AACzD,cAAM,YAAY;AAClB,YAAI,QAAQ;AAAA,UACV,QAAQ,MAAM,IAAI,SAAU,OAAO;AACjC,mBAAO,MAAM;AAAA,aACZ,MAAM;AAAA;AAAA;AAGb,UAAI,YAAY,SAAU,SAAS,MAAM,OAAO,KAAK;AACnD,sBAAc,MAAM;AACpB,yBAAiB,MAAM;AACvB,2BAAmB,SAAS,MAAM,OAAO;AACzC,6BAAqB,MAAM,OAAO;AAAA;AAEpC,UAAI,eAAe,SAAU,SAAS,MAAM,OAAO,KAAK;AACtD,YAAI,OAAO,IAAI;AACf,YAAI,QAAQ,UAAU,UAAU,KAAK,WAAW,KAAK;AACrD,cAAM,KAAK,SAAU,KAAK;AACxB,cAAI,aAAY,KAAK,QAAQ,MAAM,MAAM,UAAU,IAAI,SAAU,QAAO;AACtE,mBAAO,OAAM,SAAS,KAAK;AAAA,aAC1B,MAAM;AACT,cAAI,YAAW;AACb,gBAAI,IAAI,UAAU,IAAI;AACpB,kBAAI,QAAQ;AAAA,gBACV,KAAK;AAAA,gBACL,KAAK,MAAM;AAAA;AAAA,mBAER;AACL,kBAAI,QAAQ;AAAA,gBACV,KAAK;AAAA,gBACL,KAAK,IAAI;AAAA;AAAA;AAAA,iBAGR;AACL,gBAAI,QAAQ,EAAE,KAAK;AAAA;AAAA;AAGvB,cAAM,YAAY;AAClB,kBAAU,SAAS,MAAM,OAAO;AAAA;AAElC,UAAI,aAAa,SAAU,KAAK;AAC9B,YAAI,oBAAoB,IAAI,iBAAiB,IAAI,oBAAoB,IAAI,kBAAkB,IAAI;AAC/F,eAAO,oBAAoB,kBAAkB,OAAO,IAAI,kBAAkB;AAAA;AAE5E,UAAI,aAAa,SAAU,KAAK;AAC9B,YAAI,oBAAoB,IAAI,mBAAmB,IAAI,kBAAkB,IAAI,oBAAoB,IAAI;AACjG,eAAO,oBAAoB,kBAAkB,OAAO,IAAI,oBAAoB;AAAA;AAE9E,UAAI,kBAAkB,SAAU,KAAK;AACnC,eAAO,IAAI,kBAAkB,kBAAkB,OAAO,IAAI,oBAAoB;AAAA;AAEhF,UAAI,kBAAkB,SAAU,KAAK;AACnC,eAAO,IAAI,kBAAkB,OAAO,IAAI,mBAAmB;AAAA;AAE7D,UAAI,YAAY,SAAU,aAAY,iBAAgB,KAAK;AACzD,eAAO,gBAAe,YAAW,gBAAe;AAAA;AAElD,UAAI,eAAe,SAAU,aAAY,iBAAgB,MAAM;AAC7D,YAAI,MAAM,aAAa,YAAW,KAAK;AACvC,YAAI,WAAW,UAAU,IAAI;AAC7B,iBAAS,SAAS,WAAW;AAC7B,iBAAS,SAAS,WAAW;AAC7B,iBAAS,SAAS,gBAAgB;AAClC,iBAAS,cAAc,gBAAgB;AACvC,iBAAS,QAAQ,UAAU,aAAY,iBAAgB;AACvD,eAAO;AAAA;AAET,UAAI,cAAc,SAAU,SAAS,KAAK;AACxC,YAAI,OAAO,IAAI;AACf,YAAI,UAAU,aAAa,QAAQ,YAAY,QAAQ,gBAAgB;AACvE,YAAI,QAAQ;AAAA;AAEd,UAAI,eAAe,SAAU,SAAS,MAAM,KAAK;AAC/C,YAAI,OAAO,UAAU,cAAc,KAAK,QAAQ,IAAI;AACpD,YAAI,QAAQ,cAAc,QAAQ,cAAc,YAAY,MAAM;AAClE,YAAI,QAAQ,EAAE;AAAA;AAEhB,UAAI,kBAAkB,SAAU,SAAS,MAAM,OAAO,KAAK;AACzD,YAAI,OAAO,IAAI;AACf,YAAI,MAAM;AACV,aAAK,KAAK,WAAW,KAAK,WAAY;AACpC,cAAI;AAAA,WACH,SAAU,MAAM;AACjB,cAAI,UAAU,IAAI,gBAAgB;AAClC,cAAI,WAAW,WAAY;AACzB,gBAAI;AACJ,gBAAI,gBAAgB;AAAA;AAEtB,cAAI,wBAAwB,SAAU,KAAK;AACzC,gBAAI,QAAQ;AAAA,cACV,KAAK;AAAA,gBACH,OAAO;AAAA,gBACP,MAAM;AAAA;AAAA;AAGV,gBAAI,QAAQ;AACZ,sBAAU,SAAS,MAAM,OAAO;AAAA;AAElC,wBAAc,MAAM,KAAK,SAAU,SAAS;AAC1C,gBAAI,WAAW,QAAQ,gBAAgB,MAAM,SAAS;AACtD,gBAAI,KAAK,kBAAkB;AACzB,sBAAQ,YAAY,UAAU,KAAK,SAAU,QAAQ;AACnD,sCAAsB,OAAO;AAC7B;AAAA,iBACC,MAAM,SAAU,KAAK;AACtB;AACA,wBAAQ,SAAS;AAAA;AAAA,mBAEd;AACL,sBAAQ,eAAe;AACvB,oCAAsB,SAAS;AAC/B,kBAAI;AAAA;AAAA;AAAA;AAAA;AAKZ,UAAI,gBAAgB,SAAU,SAAS,MAAM,OAAO;AAClD,eAAO,SAAU,KAAK,KAAK;AACzB,cAAI,IAAI,SAAS,OAAO;AACtB,sBAAU,SAAS,MAAM,OAAO;AAAA,qBACvB,IAAI,SAAS,UAAU;AAChC,yBAAa,SAAS,MAAM,OAAO;AAAA,qBAC1B,IAAI,SAAS,OAAO;AAC7B,kBAAM,UAAU,IAAI,UAAU;AAAA,qBACrB,IAAI,SAAS,SAAS;AAC/B,wBAAY,SAAS;AAAA,qBACZ,IAAI,SAAS,YAAY,IAAI,SAAS,YAAY,IAAI,SAAS,YAAY,IAAI,SAAS,eAAe;AAChH,yBAAa,SAAS,MAAM;AAAA,qBACnB,IAAI,SAAS,aAAa;AACnC,4BAAgB,SAAS,MAAM,OAAO;AAAA,qBAC7B,IAAI,SAAS,gBAAgB;AACtC,gBAAI,IAAI,UAAU,cAAc;AAC9B,kBAAI,QAAQ;AAAA,mBACP;AACL,kBAAI,OAAO;AAAA;AAAA;AAAA;AAAA;AAKnB,UAAI,eAAe,SAAU,OAAO;AAClC,eAAO,WAAY;AACjB,gBAAM,OAAO;AAAA;AAAA;AAGjB,UAAI,iBAAiB,SAAU,MAAM;AACnC,YAAI,KAAK,aAAa,KAAK,gBAAgB,KAAK,kBAAkB;AAChE,cAAI,WAAW;AAAA,YACb,MAAM;AAAA,YACN,MAAM,QAAQ;AAAA,cACZ,CAAC,QAAQ,QAAQ;AAAA,cACjB,KAAK,YAAY,CAAC,OAAO,QAAQ,SAAS;AAAA,cAC1C,KAAK,gBAAiB,MAAK,gBAAgB,KAAK,oBAAoB,CAAC,UAAU,QAAQ,SAAS;AAAA;AAAA;AAGpG,iBAAO;AAAA,eACF;AACL,cAAI,QAAQ;AAAA,YACV,MAAM;AAAA,YACN,OAAO,QAAQ,UAAU;AAAA;AAE3B,iBAAO;AAAA;AAAA;AAGX,UAAI,aAAa,SAAU,SAAS;AAClC,eAAO,SAAU,MAAM;AACrB,cAAI,QAAQ,YAAY;AACxB,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM,eAAe;AAAA,YACrB,SAAS;AAAA,cACP;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,MAAM;AAAA;AAAA,cAER;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA;AAAA;AAAA,YAGb,aAAa,cAAc,KAAK;AAAA,YAChC,UAAU,QAAQ,SAAS;AAAA,YAC3B,UAAU,cAAc,SAAS,MAAM;AAAA,YACvC,SAAS,aAAa;AAAA;AAAA;AAAA;AAI5B,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,SAAU,MAAM;AACrB,iBAAO,SAAU,KAAK;AACpB,gBAAI,OAAO,UAAU,cAAc,KAAK,QAAQ,IAAI;AACpD,mBAAO,YAAY,kBAAkB,OAAO,YAAY,MAAM,KAAK;AACnE,mBAAO,aAAa;AACpB,gBAAI;AAAA;AAAA;AAAA;AAIV,UAAI,YAAY,SAAU,QAAQ;AAChC,eAAO,SAAU,KAAK;AACpB,cAAI,CAAC,eAAe,QAAQ,MAAM;AAChC,mBAAO,SAAS,QAAQ;AAAA,cACtB,OAAO;AAAA,cACP,QAAQ;AAAA;AAAA,iBAEL;AACL,mBAAO,aAAa,OAAO,gBAAgB,WAAW,MAAM,KAAK,SAAU,YAAY;AACrF,qBAAO;AAAA,gBACL,OAAO,OAAO,WAAW;AAAA,gBACzB,QAAQ,OAAO,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAMpC,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,SAAU,MAAM,SAAS,SAAS;AACvC,iBAAO,OAAO,aAAa,UAAU,OAAO;AAAA,YAC1C,MAAM;AAAA,YACN;AAAA,YACA,MAAM,KAAK,OAAO,KAAK,KAAK,QAAQ,aAAa,MAAM;AAAA,YACvD,UAAU,KAAK;AAAA,YACf,QAAQ,QAAQ,MAAM,KAAK;AAAA;AAAA;AAAA;AAIjC,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,SAAU,UAAU;AACzB,iBAAO,aAAa,UAAU,IAAI;AAAA;AAAA;AAGtC,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,SAAU,SAAS;AACxB,iBAAO,cAAc,MAAM;AAAA;AAAA;AAG/B,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,SAAU,SAAS;AACxB,iBAAO,eAAe,QAAQ;AAAA;AAAA;AAGlC,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,SAAU,SAAS;AACxB,iBAAO,OAAO,IAAI,WAAW;AAAA;AAAA;AAGjC,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,SAAU,WAAW,MAAM;AAChC,iBAAO,OAAO,IAAI,eAAe,WAAW;AAAA;AAAA;AAGhD,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,SAAU,UAAU;AACzB,iBAAO,SAAS,QAAQ,OAAO,CAAC,WAAW,OAAO,KAAK,SAAU,SAAS;AACxE,gBAAI,QAAQ,WAAW,GAAG;AACxB,qBAAO,SAAS,OAAO;AAAA,uBACd,QAAQ,GAAG,WAAW,OAAO;AACtC,qBAAO,SAAS,OAAO,QAAQ,GAAG,MAAM;AAAA,mBACnC;AACL,qBAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAKvB,UAAI,SAAS,SAAU,QAAQ;AAC7B,YAAI,UAAU;AAAA,UACZ,UAAU,cAAc;AAAA,UACxB,WAAW,UAAU;AAAA,UACrB,gBAAgB,eAAe;AAAA,UAC/B,iBAAiB,gBAAgB;AAAA,UACjC,UAAU,SAAS;AAAA,UACnB,cAAc,aAAa;AAAA,UAC3B,YAAY,WAAW;AAAA,UACvB,gBAAgB,eAAe;AAAA,UAC/B,aAAa,YAAY;AAAA;AAE3B,YAAI,OAAO,WAAY;AACrB,kBAAQ,QAAQ,KAAK,WAAW,UAAU,KAAK,OAAO,cAAc;AAAA;AAEtE,eAAO,EAAE;AAAA;AAGX,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,YAAY,OAAO,QAAQ;AAC7C,eAAO,WAAW,kBAAkB,SAAU,KAAK,MAAM;AACvD,iBAAO,YAAY,SAAS,WAAY;AACtC,mBAAO,oBAAoB,QAAQ;AAAA;AAAA;AAAA;AAKzC,UAAI,gBAAgB,SAAU,MAAM;AAClC,YAAI,YAAY,KAAK,KAAK;AAC1B,eAAO,aAAa,YAAY,KAAK;AAAA;AAEvC,UAAI,6BAA6B,SAAU,OAAO;AAChD,eAAO,SAAU,OAAO;AACtB,cAAI,IAAI,MAAM;AACd,cAAI,wBAAwB,SAAU,OAAM;AAC1C,kBAAK,KAAK,mBAAmB,QAAQ,SAAS;AAAA;AAEhD,iBAAO,KAAK;AACV,gBAAI,OAAO,MAAM;AACjB,gBAAI,cAAc,OAAO;AACvB,mBAAK,KAAK,mBAAmB,QAAQ,UAAU;AAC/C,qBAAO,KAAK,KAAK,OAAO,eAAe;AAAA;AAAA;AAAA;AAAA;AAK/C,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,GAAG,WAAW,WAAY;AAC/B,iBAAO,OAAO,cAAc,UAAU,2BAA2B;AACjE,iBAAO,WAAW,cAAc,UAAU,2BAA2B;AAAA;AAAA;AAIzE,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,GAAG,SAAS,gBAAgB,SAAS;AAAA,UAC1C,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,OAAO,QAAQ;AAAA,UACzB,SAAS,SAAU,WAAW;AAC5B,sBAAU,UAAU,cAAc,iBAAiB;AACnD,mBAAO,OAAO,UAAU,0BAA0B,kEAAkE,UAAU,WAAW;AAAA;AAAA;AAG7I,eAAO,GAAG,SAAS,YAAY,SAAS;AAAA,UACtC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,OAAO,QAAQ;AAAA;AAE3B,eAAO,GAAG,SAAS,eAAe,SAAS;AAAA,UACzC,QAAQ,SAAU,SAAS;AACzB,mBAAO,SAAS,YAAY,QAAQ,YAAY,CAAC,mBAAmB,WAAW,CAAC,WAAW;AAAA;AAAA;AAAA;AAKjG,wBAAmB;AACjB,iBAAS,IAAI,SAAS,SAAU,QAAQ;AACtC,gBAAM;AACN,mBAAS;AACT,qBAAW;AAAA;AAAA;AAIf;AAAA;AAAA;AAAA;;;AC/nDJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,gCAAQ;", "names": []}