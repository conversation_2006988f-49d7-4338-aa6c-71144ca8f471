{"version": 3, "sources": ["../tinymce/plugins/code/plugin.js", "../tinymce/plugins/code/index.js", "dep:tinymce_plugins_code"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var setContent = function (editor, html) {\n      editor.focus();\n      editor.undoManager.transact(function () {\n        editor.setContent(html);\n      });\n      editor.selection.setCursorLocation();\n      editor.nodeChanged();\n    };\n    var getContent = function (editor) {\n      return editor.getContent({ source_view: true });\n    };\n\n    var open = function (editor) {\n      var editorContent = getContent(editor);\n      editor.windowManager.open({\n        title: 'Source Code',\n        size: 'large',\n        body: {\n          type: 'panel',\n          items: [{\n              type: 'textarea',\n              name: 'code'\n            }]\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: { code: editorContent },\n        onSubmit: function (api) {\n          setContent(editor, api.getData().code);\n          api.close();\n        }\n      });\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mceCodeEditor', function () {\n        open(editor);\n      });\n    };\n\n    var register = function (editor) {\n      var onAction = function () {\n        return editor.execCommand('mceCodeEditor');\n      };\n      editor.ui.registry.addButton('code', {\n        icon: 'sourcecode',\n        tooltip: 'Source code',\n        onAction: onAction\n      });\n      editor.ui.registry.addMenuItem('code', {\n        icon: 'sourcecode',\n        text: 'Source code',\n        onAction: onAction\n      });\n    };\n\n    function Plugin () {\n      global.add('code', function (editor) {\n        register$1(editor);\n        register(editor);\n        return {};\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"code\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/code')\n//   ES2015:\n//     import 'tinymce/plugins/code'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/code/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,aAAa,SAAU,QAAQ,MAAM;AACvC,eAAO;AACP,eAAO,YAAY,SAAS,WAAY;AACtC,iBAAO,WAAW;AAAA;AAEpB,eAAO,UAAU;AACjB,eAAO;AAAA;AAET,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,OAAO,WAAW,EAAE,aAAa;AAAA;AAG1C,UAAI,OAAO,SAAU,QAAQ;AAC3B,YAAI,gBAAgB,WAAW;AAC/B,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA;AAAA;AAAA,UAGZ,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA;AAAA,YAER;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,UAGb,aAAa,EAAE,MAAM;AAAA,UACrB,UAAU,SAAU,KAAK;AACvB,uBAAW,QAAQ,IAAI,UAAU;AACjC,gBAAI;AAAA;AAAA;AAAA;AAKV,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,iBAAiB,WAAY;AAC7C,eAAK;AAAA;AAAA;AAIT,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,UAAU,QAAQ;AAAA,UACnC,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA;AAEF,eAAO,GAAG,SAAS,YAAY,QAAQ;AAAA,UACrC,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA;AAAA;AAIJ,wBAAmB;AACjB,eAAO,IAAI,QAAQ,SAAU,QAAQ;AACnC,qBAAW;AACX,mBAAS;AACT,iBAAO;AAAA;AAAA;AAIX;AAAA;AAAA;AAAA;;;ACxFJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,+BAAQ;", "names": []}