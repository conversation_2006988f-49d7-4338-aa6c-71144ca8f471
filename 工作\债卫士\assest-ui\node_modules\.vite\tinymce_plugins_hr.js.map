{"version": 3, "sources": ["../tinymce/plugins/hr/plugin.js", "../tinymce/plugins/hr/index.js", "dep:tinymce_plugins_hr"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var register$1 = function (editor) {\n      editor.addCommand('InsertHorizontalRule', function () {\n        editor.execCommand('mceInsertContent', false, '<hr />');\n      });\n    };\n\n    var register = function (editor) {\n      var onAction = function () {\n        return editor.execCommand('InsertHorizontalRule');\n      };\n      editor.ui.registry.addButton('hr', {\n        icon: 'horizontal-rule',\n        tooltip: 'Horizontal line',\n        onAction: onAction\n      });\n      editor.ui.registry.addMenuItem('hr', {\n        icon: 'horizontal-rule',\n        text: 'Horizontal line',\n        onAction: onAction\n      });\n    };\n\n    function Plugin () {\n      global.add('hr', function (editor) {\n        register$1(editor);\n        register(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"hr\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/hr')\n//   ES2015:\n//     import 'tinymce/plugins/hr'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/hr/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,wBAAwB,WAAY;AACpD,iBAAO,YAAY,oBAAoB,OAAO;AAAA;AAAA;AAIlD,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,UAAU,MAAM;AAAA,UACjC,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA;AAEF,eAAO,GAAG,SAAS,YAAY,MAAM;AAAA,UACnC,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA;AAAA;AAIJ,wBAAmB;AACjB,eAAO,IAAI,MAAM,SAAU,QAAQ;AACjC,qBAAW;AACX,mBAAS;AAAA;AAAA;AAIb;AAAA;AAAA;AAAA;;;AC1CJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,6BAAQ;", "names": []}