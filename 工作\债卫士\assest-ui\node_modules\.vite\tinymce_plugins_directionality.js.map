{"version": 3, "sources": ["../tinymce/plugins/directionality/plugin.js", "../tinymce/plugins/directionality/index.js", "dep:tinymce_plugins_directionality"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType$1 = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var isString = isType$1('string');\n    var isBoolean = isSimpleType('boolean');\n    var isNullable = function (a) {\n      return a === null || a === undefined;\n    };\n    var isNonNullable = function (a) {\n      return !isNullable(a);\n    };\n    var isFunction = isSimpleType('function');\n    var isNumber = isSimpleType('number');\n\n    var noop = function () {\n    };\n    var compose1 = function (fbc, fab) {\n      return function (a) {\n        return fbc(fab(a));\n      };\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var filter = function (xs, pred) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n\n    var DOCUMENT = 9;\n    var DOCUMENT_FRAGMENT = 11;\n    var ELEMENT = 1;\n    var TEXT = 3;\n\n    var fromHtml = function (html, scope) {\n      var doc = scope || document;\n      var div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        console.error('HTML does not have a single root node', html);\n        throw new Error('HTML must have a single root node');\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    var fromTag = function (tag, scope) {\n      var doc = scope || document;\n      var node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    var fromText = function (text, scope) {\n      var doc = scope || document;\n      var node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    var fromDom = function (node) {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    var fromPoint = function (docElm, x, y) {\n      return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    };\n    var SugarElement = {\n      fromHtml: fromHtml,\n      fromTag: fromTag,\n      fromText: fromText,\n      fromDom: fromDom,\n      fromPoint: fromPoint\n    };\n\n    var is = function (element, selector) {\n      var dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        var elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var name = function (element) {\n      var r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    var type = function (element) {\n      return element.dom.nodeType;\n    };\n    var isType = function (t) {\n      return function (element) {\n        return type(element) === t;\n      };\n    };\n    var isElement = isType(ELEMENT);\n    var isText = isType(TEXT);\n    var isDocument = isType(DOCUMENT);\n    var isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    var isTag = function (tag) {\n      return function (e) {\n        return isElement(e) && name(e) === tag;\n      };\n    };\n\n    var owner = function (element) {\n      return SugarElement.fromDom(element.dom.ownerDocument);\n    };\n    var documentOrOwner = function (dos) {\n      return isDocument(dos) ? dos : owner(dos);\n    };\n    var parent = function (element) {\n      return Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    };\n    var children$2 = function (element) {\n      return map(element.dom.childNodes, SugarElement.fromDom);\n    };\n\n    var rawSet = function (dom, key, value) {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    var set = function (element, key, value) {\n      rawSet(element.dom, key, value);\n    };\n    var remove = function (element, key) {\n      element.dom.removeAttribute(key);\n    };\n\n    var isShadowRoot = function (dos) {\n      return isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    };\n    var supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    var getRootNode = supported ? function (e) {\n      return SugarElement.fromDom(e.dom.getRootNode());\n    } : documentOrOwner;\n    var getShadowRoot = function (e) {\n      var r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    var getShadowHost = function (e) {\n      return SugarElement.fromDom(e.dom.host);\n    };\n\n    var inBody = function (element) {\n      var dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      var doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(function () {\n        return doc.body.contains(dom);\n      }, compose1(inBody, getShadowHost));\n    };\n\n    var ancestor$1 = function (scope, predicate, isRoot) {\n      var element = scope.dom;\n      var stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        var el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n\n    var ancestor = function (scope, selector, isRoot) {\n      return ancestor$1(scope, function (e) {\n        return is(e, selector);\n      }, isRoot);\n    };\n\n    var isSupported = function (dom) {\n      return dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n    };\n\n    var get = function (element, property) {\n      var dom = element.dom;\n      var styles = window.getComputedStyle(dom);\n      var r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    var getUnsafeProperty = function (dom, property) {\n      return isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n    };\n\n    var getDirection = function (element) {\n      return get(element, 'direction') === 'rtl' ? 'rtl' : 'ltr';\n    };\n\n    var children$1 = function (scope, predicate) {\n      return filter(children$2(scope), predicate);\n    };\n\n    var children = function (scope, selector) {\n      return children$1(scope, function (e) {\n        return is(e, selector);\n      });\n    };\n\n    var getParentElement = function (element) {\n      return parent(element).filter(isElement);\n    };\n    var getNormalizedBlock = function (element, isListItem) {\n      var normalizedElement = isListItem ? ancestor(element, 'ol,ul') : Optional.some(element);\n      return normalizedElement.getOr(element);\n    };\n    var isListItem = isTag('li');\n    var setDir = function (editor, dir) {\n      var selectedBlocks = editor.selection.getSelectedBlocks();\n      if (selectedBlocks.length > 0) {\n        each(selectedBlocks, function (block) {\n          var blockElement = SugarElement.fromDom(block);\n          var isBlockElementListItem = isListItem(blockElement);\n          var normalizedBlock = getNormalizedBlock(blockElement, isBlockElementListItem);\n          var normalizedBlockParent = getParentElement(normalizedBlock);\n          normalizedBlockParent.each(function (parent) {\n            var parentDirection = getDirection(parent);\n            if (parentDirection !== dir) {\n              set(normalizedBlock, 'dir', dir);\n            } else if (getDirection(normalizedBlock) !== dir) {\n              remove(normalizedBlock, 'dir');\n            }\n            if (isBlockElementListItem) {\n              var listItems = children(normalizedBlock, 'li[dir]');\n              each(listItems, function (listItem) {\n                return remove(listItem, 'dir');\n              });\n            }\n          });\n        });\n        editor.nodeChanged();\n      }\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mceDirectionLTR', function () {\n        setDir(editor, 'ltr');\n      });\n      editor.addCommand('mceDirectionRTL', function () {\n        setDir(editor, 'rtl');\n      });\n    };\n\n    var getNodeChangeHandler = function (editor, dir) {\n      return function (api) {\n        var nodeChangeHandler = function (e) {\n          var element = SugarElement.fromDom(e.element);\n          api.setActive(getDirection(element) === dir);\n        };\n        editor.on('NodeChange', nodeChangeHandler);\n        return function () {\n          return editor.off('NodeChange', nodeChangeHandler);\n        };\n      };\n    };\n    var register = function (editor) {\n      editor.ui.registry.addToggleButton('ltr', {\n        tooltip: 'Left to right',\n        icon: 'ltr',\n        onAction: function () {\n          return editor.execCommand('mceDirectionLTR');\n        },\n        onSetup: getNodeChangeHandler(editor, 'ltr')\n      });\n      editor.ui.registry.addToggleButton('rtl', {\n        tooltip: 'Right to left',\n        icon: 'rtl',\n        onAction: function () {\n          return editor.execCommand('mceDirectionRTL');\n        },\n        onSetup: getNodeChangeHandler(editor, 'rtl')\n      });\n    };\n\n    function Plugin () {\n      global.add('directionality', function (editor) {\n        register$1(editor);\n        register(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"directionality\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/directionality')\n//   ES2015:\n//     import 'tinymce/plugins/directionality'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/directionality/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,OAAM;AAC7B,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,WAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,OAAM;AACjC,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,UAAU;AAAA;AAAA;AAG5B,UAAI,WAAW,SAAS;AACxB,UAAI,YAAY,aAAa;AAC7B,UAAI,aAAa,SAAU,GAAG;AAC5B,eAAO,MAAM,QAAQ,MAAM;AAAA;AAE7B,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,CAAC,WAAW;AAAA;AAErB,UAAI,aAAa,aAAa;AAC9B,UAAI,WAAW,aAAa;AAE5B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,KAAK,KAAK;AACjC,eAAO,SAAU,GAAG;AAClB,iBAAO,IAAI,IAAI;AAAA;AAAA;AAGnB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,cAAE,KAAK;AAAA;AAAA;AAGX,eAAO;AAAA;AAGT,UAAI,WAAW;AACf,UAAI,oBAAoB;AACxB,UAAI,UAAU;AACd,UAAI,OAAO;AAEX,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,IAAI,cAAc;AAC5B,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,mBAAmB,IAAI,WAAW,SAAS,GAAG;AACrD,kBAAQ,MAAM,yCAAyC;AACvD,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,QAAQ,IAAI,WAAW;AAAA;AAEhC,UAAI,UAAU,SAAU,KAAK,OAAO;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,cAAc;AAC7B,eAAO,QAAQ;AAAA;AAEjB,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,eAAe;AAC9B,eAAO,QAAQ;AAAA;AAEjB,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,EAAE,KAAK;AAAA;AAEhB,UAAI,YAAY,SAAU,QAAQ,GAAG,GAAG;AACtC,eAAO,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,IAAI,IAAI;AAAA;AAE9D,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,KAAK,SAAU,SAAS,UAAU;AACpC,YAAI,MAAM,QAAQ;AAClB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,eACF;AACL,cAAI,OAAO;AACX,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ;AAAA,qBACX,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB;AAAA,qBACrB,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB;AAAA,qBACzB,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB;AAAA,iBAC1B;AACL,kBAAM,IAAI,MAAM;AAAA;AAAA;AAAA;AAKtB,aAAO,WAAW,cAAc,SAAS,SAAS;AAElD,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,IAAI,QAAQ,IAAI;AACpB,eAAO,EAAE;AAAA;AAEX,UAAI,OAAO,SAAU,SAAS;AAC5B,eAAO,QAAQ,IAAI;AAAA;AAErB,UAAI,SAAS,SAAU,GAAG;AACxB,eAAO,SAAU,SAAS;AACxB,iBAAO,KAAK,aAAa;AAAA;AAAA;AAG7B,UAAI,YAAY,OAAO;AACvB,UAAI,SAAS,OAAO;AACpB,UAAI,aAAa,OAAO;AACxB,UAAI,qBAAqB,OAAO;AAChC,UAAI,QAAQ,SAAU,KAAK;AACzB,eAAO,SAAU,GAAG;AAClB,iBAAO,UAAU,MAAM,KAAK,OAAO;AAAA;AAAA;AAIvC,UAAI,QAAQ,SAAU,SAAS;AAC7B,eAAO,aAAa,QAAQ,QAAQ,IAAI;AAAA;AAE1C,UAAI,kBAAkB,SAAU,KAAK;AACnC,eAAO,WAAW,OAAO,MAAM,MAAM;AAAA;AAEvC,UAAI,SAAS,SAAU,SAAS;AAC9B,eAAO,SAAS,KAAK,QAAQ,IAAI,YAAY,IAAI,aAAa;AAAA;AAEhE,UAAI,aAAa,SAAU,SAAS;AAClC,eAAO,IAAI,QAAQ,IAAI,YAAY,aAAa;AAAA;AAGlD,UAAI,SAAS,SAAU,KAAK,KAAK,OAAO;AACtC,YAAI,SAAS,UAAU,UAAU,UAAU,SAAS,QAAQ;AAC1D,cAAI,aAAa,KAAK,QAAQ;AAAA,eACzB;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAa,OAAO,eAAe;AAC7F,gBAAM,IAAI,MAAM;AAAA;AAAA;AAGpB,UAAI,MAAM,SAAU,SAAS,KAAK,OAAO;AACvC,eAAO,QAAQ,KAAK,KAAK;AAAA;AAE3B,UAAI,SAAS,SAAU,SAAS,KAAK;AACnC,gBAAQ,IAAI,gBAAgB;AAAA;AAG9B,UAAI,eAAe,SAAU,KAAK;AAChC,eAAO,mBAAmB,QAAQ,cAAc,IAAI,IAAI;AAAA;AAE1D,UAAI,YAAY,WAAW,QAAQ,UAAU,iBAAiB,WAAW,KAAK,UAAU;AACxF,UAAI,cAAc,YAAY,SAAU,GAAG;AACzC,eAAO,aAAa,QAAQ,EAAE,IAAI;AAAA,UAChC;AACJ,UAAI,gBAAgB,SAAU,GAAG;AAC/B,YAAI,IAAI,YAAY;AACpB,eAAO,aAAa,KAAK,SAAS,KAAK,KAAK,SAAS;AAAA;AAEvD,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,aAAa,QAAQ,EAAE,IAAI;AAAA;AAGpC,UAAI,SAAS,SAAU,SAAS;AAC9B,YAAI,MAAM,OAAO,WAAW,QAAQ,IAAI,aAAa,QAAQ;AAC7D,YAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,iBAAO;AAAA;AAET,YAAI,MAAM,IAAI;AACd,eAAO,cAAc,aAAa,QAAQ,MAAM,KAAK,WAAY;AAC/D,iBAAO,IAAI,KAAK,SAAS;AAAA,WACxB,SAAS,QAAQ;AAAA;AAGtB,UAAI,aAAa,SAAU,OAAO,WAAW,QAAQ;AACnD,YAAI,UAAU,MAAM;AACpB,YAAI,OAAO,WAAW,UAAU,SAAS;AACzC,eAAO,QAAQ,YAAY;AACzB,oBAAU,QAAQ;AAClB,cAAI,KAAK,aAAa,QAAQ;AAC9B,cAAI,UAAU,KAAK;AACjB,mBAAO,SAAS,KAAK;AAAA,qBACZ,KAAK,KAAK;AACnB;AAAA;AAAA;AAGJ,eAAO,SAAS;AAAA;AAGlB,UAAI,WAAW,SAAU,OAAO,UAAU,QAAQ;AAChD,eAAO,WAAW,OAAO,SAAU,GAAG;AACpC,iBAAO,GAAG,GAAG;AAAA,WACZ;AAAA;AAGL,UAAI,cAAc,SAAU,KAAK;AAC/B,eAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM;AAAA;AAGzD,UAAI,MAAM,SAAU,SAAS,UAAU;AACrC,YAAI,MAAM,QAAQ;AAClB,YAAI,SAAS,OAAO,iBAAiB;AACrC,YAAI,IAAI,OAAO,iBAAiB;AAChC,eAAO,MAAM,MAAM,CAAC,OAAO,WAAW,kBAAkB,KAAK,YAAY;AAAA;AAE3E,UAAI,oBAAoB,SAAU,KAAK,UAAU;AAC/C,eAAO,YAAY,OAAO,IAAI,MAAM,iBAAiB,YAAY;AAAA;AAGnE,UAAI,eAAe,SAAU,SAAS;AACpC,eAAO,IAAI,SAAS,iBAAiB,QAAQ,QAAQ;AAAA;AAGvD,UAAI,aAAa,SAAU,OAAO,WAAW;AAC3C,eAAO,OAAO,WAAW,QAAQ;AAAA;AAGnC,UAAI,WAAW,SAAU,OAAO,UAAU;AACxC,eAAO,WAAW,OAAO,SAAU,GAAG;AACpC,iBAAO,GAAG,GAAG;AAAA;AAAA;AAIjB,UAAI,mBAAmB,SAAU,SAAS;AACxC,eAAO,OAAO,SAAS,OAAO;AAAA;AAEhC,UAAI,qBAAqB,SAAU,SAAS,aAAY;AACtD,YAAI,oBAAoB,cAAa,SAAS,SAAS,WAAW,SAAS,KAAK;AAChF,eAAO,kBAAkB,MAAM;AAAA;AAEjC,UAAI,aAAa,MAAM;AACvB,UAAI,SAAS,SAAU,QAAQ,KAAK;AAClC,YAAI,iBAAiB,OAAO,UAAU;AACtC,YAAI,eAAe,SAAS,GAAG;AAC7B,eAAK,gBAAgB,SAAU,OAAO;AACpC,gBAAI,eAAe,aAAa,QAAQ;AACxC,gBAAI,yBAAyB,WAAW;AACxC,gBAAI,kBAAkB,mBAAmB,cAAc;AACvD,gBAAI,wBAAwB,iBAAiB;AAC7C,kCAAsB,KAAK,SAAU,SAAQ;AAC3C,kBAAI,kBAAkB,aAAa;AACnC,kBAAI,oBAAoB,KAAK;AAC3B,oBAAI,iBAAiB,OAAO;AAAA,yBACnB,aAAa,qBAAqB,KAAK;AAChD,uBAAO,iBAAiB;AAAA;AAE1B,kBAAI,wBAAwB;AAC1B,oBAAI,YAAY,SAAS,iBAAiB;AAC1C,qBAAK,WAAW,SAAU,UAAU;AAClC,yBAAO,OAAO,UAAU;AAAA;AAAA;AAAA;AAAA;AAKhC,iBAAO;AAAA;AAAA;AAIX,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,mBAAmB,WAAY;AAC/C,iBAAO,QAAQ;AAAA;AAEjB,eAAO,WAAW,mBAAmB,WAAY;AAC/C,iBAAO,QAAQ;AAAA;AAAA;AAInB,UAAI,uBAAuB,SAAU,QAAQ,KAAK;AAChD,eAAO,SAAU,KAAK;AACpB,cAAI,oBAAoB,SAAU,GAAG;AACnC,gBAAI,UAAU,aAAa,QAAQ,EAAE;AACrC,gBAAI,UAAU,aAAa,aAAa;AAAA;AAE1C,iBAAO,GAAG,cAAc;AACxB,iBAAO,WAAY;AACjB,mBAAO,OAAO,IAAI,cAAc;AAAA;AAAA;AAAA;AAItC,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,GAAG,SAAS,gBAAgB,OAAO;AAAA,UACxC,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA,UAE5B,SAAS,qBAAqB,QAAQ;AAAA;AAExC,eAAO,GAAG,SAAS,gBAAgB,OAAO;AAAA,UACxC,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA,UAE5B,SAAS,qBAAqB,QAAQ;AAAA;AAAA;AAI1C,wBAAmB;AACjB,eAAO,IAAI,kBAAkB,SAAU,QAAQ;AAC7C,qBAAW;AACX,mBAAS;AAAA;AAAA;AAIb;AAAA;AAAA;AAAA;;;AClcJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,yCAAQ;", "names": []}