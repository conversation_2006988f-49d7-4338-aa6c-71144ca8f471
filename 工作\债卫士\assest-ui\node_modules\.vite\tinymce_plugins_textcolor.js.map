{"version": 3, "sources": ["../tinymce/plugins/textcolor/plugin.js", "../tinymce/plugins/textcolor/index.js", "dep:tinymce_plugins_textcolor"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    function Plugin () {\n      global.add('textcolor', function () {\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"textcolor\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/textcolor')\n//   ES2015:\n//     import 'tinymce/plugins/textcolor'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/textcolor/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,wBAAmB;AACjB,eAAO,IAAI,aAAa,WAAY;AAAA;AAAA;AAItC;AAAA;AAAA;AAAA;;;AClBJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,oCAAQ;", "names": []}