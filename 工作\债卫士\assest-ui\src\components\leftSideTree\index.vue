<template>
    <div class="tree-container">
        <el-input v-model="filterText" class="mb10" style="width: 90%;" placeholder="请输入产品名称" />
        <div class="home-style" @click="clickAll">
            <el-icon>
                <HomeFilled />
            </el-icon>
            <span class="ml5">全部</span>
        </div>
        <el-tree :data="data" ref="treeRef" :props="treeProps" default-expand-all :filter-node-method="filterNode"
            :node-key="nodeKey" :highlight-current="highlightCurrent" @node-click="nodeClick">
            <template #default="{ node }">
                <Tooltip :content="node.label" />
            </template>
        </el-tree>
    </div>
</template>

<script setup>
const { proxy } = getCurrentInstance()
const props = defineProps({
    data: { type: Array, default: [] },
    nodeKey: { type: String, default: 'id' },
    highlightCurrent: { type: <PERSON>olean, default: true },
    nodeClick: { type: Function, default: () => { } },
    treeProps: { type: Object, default: { label: 'label', children: 'children' } }
})
const filterText = ref(null)
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
};
watch(() => filterText.value, (val) => {
    proxy.$refs['treeRef']?.filter(val)
})
</script>

<style lang="scss" scoped></style>