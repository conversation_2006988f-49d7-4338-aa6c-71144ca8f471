<template>
    <div class="mt20">
        <el-form inline label-width="auto">
            <el-form-item label="项目ID" prop="项目ID">
                <el-input style="width: 320px;" placeholder="请输入项目ID" v-model="queryParmas.projectId" />
            </el-form-item>
            <el-form-item label="项目名称" prop="项目名称">
                <el-input style="width: 320px;" placeholder="请输入项目名称" v-model="queryParmas.projectName" />
            </el-form-item>
            <el-form-item label="产品类型" prop="产品类型">
                <el-input style="width: 320px;" placeholder="请输入产品类型" v-model="queryParmas.productType" />
            </el-form-item>
            <el-form-item label="竞价状态" prop="竞价状态">
                <el-input style="width: 320px;" placeholder="请输入竞价状态" v-model="queryParmas.biddingStatus" />
            </el-form-item>
            <el-form-item label="资产转让方" prop="资产转让方">
                <el-input style="width: 320px;" placeholder="请输入资产转让方" v-model="queryParmas.transferor" />
            </el-form-item>
            <el-form-item label="申请人" prop="申请人">
                <el-input style="width: 320px;" placeholder="请输入申请人" v-model="queryParmas.applicant" />
            </el-form-item>
            <el-form-item label="申请时间" prop="申请时间">
                <el-input style="width: 320px;" placeholder="请输入申请时间" v-model="queryParmas.applyTime" />
            </el-form-item>
            <el-form-item label="投标方式" prop="投标方式">
                <el-input style="width: 320px;" placeholder="请输入投标方式" v-model="queryParmas.biddingMethod" />
            </el-form-item>
            <el-form-item label="印章类型" prop="印章类型">
                <el-input style="width: 320px;" placeholder="请输入印章类型" v-model="queryParmas.stampType" />
            </el-form-item>
            <el-form-item label="是否用印" prop="是否用印">
                <el-input style="width: 320px;" placeholder="请输入是否用印" v-model="queryParmas.needStamp" />
            </el-form-item>
            <el-form-item label="报价金额" prop="债权总金额">
                <div class="range-scope" style="width: 320px;">
                    <el-input v-model="queryParmas.amountMin" />
                    <span>-</span>
                    <el-input v-model="queryParmas.amountMax" />
                </div>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button @click="antiShake(handleQuery)" plain type="primary">搜索</el-button>
            <el-button @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="opt-area">
            <el-button plain type="success">通过</el-button>
            <el-button plain type="warning">不通过</el-button>
        </div>
        <SelectedAll />
        <el-tabs v-model="activetab" @tab-change="antiShake(handleQuery)">
            <el-tab-pane v-for="item in tabList" :key="item.code" :label="item.info" :name="item.code" />
        </el-tabs>
        <el-table :data="dataList">
            <el-table-column type="selection" width="44px" :selectable="selectable" align="right" />
            <el-table-column label="项目ID" prop="projectId" width="120" align="center" />
            <el-table-column label="项目名称" prop="projectName" min-width="120" align="center" />
            <el-table-column label="资产转让方" prop="transferor" min-width="120" align="center" />
            <el-table-column label="产品类型" prop="productType" min-width="120" align="center" />
            <el-table-column label="竞价状态" prop="biddingStatus" min-width="120" align="center" />
            <el-table-column label="流程标题" prop="processTitle" min-width="120" align="center" />
            <el-table-column label="投标方式" prop="biddingMethod" width="120" align="center" />
            <el-table-column label="报价金额（元）" prop="biddingAmount" width="120" align="center" />
            <el-table-column label="是否用印" prop="needStamp" width="120" align="center" />
            <el-table-column label="印章类型" prop="stampType" width="120" align="center" />
            <el-table-column label="申请人" prop="applicant" width="120" align="center" />
            <el-table-column label="申请时间" prop="applyTime" width="120" align="center" />
            <el-table-column label="处理状态" prop="processStatus" width="120" align="center" />
            <el-table-column label="处理人" prop="processor" width="120" align="center" />
            <el-table-column label="处理时间" prop="processTime" width="120" align="center" />
            <el-table-column width="180" fixed="right" label="操作" align="center">
                <template #default="{ row }">
                    <el-button type="text" @click="handle(row)">通过</el-button>
                    <el-button type="text" @click="handle(row)">不通过</el-button>
                    <el-button type="text" @click="handleDetails(row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
const route = useRoute()
const router = useRouter()

const activetab = ref('0')
const dataList = ref([
    {
        projectId: 'zdb202411220001',
        projectName: '2024不良资产收购项目',
        transferor: '传能花',
        productType: '债权类',
        biddingStatus: '2024年债权资产转让项目',
        processTitle: '',
        biddingMethod: '线上公开',
        biddingAmount: '1,000,000.00',
        needStamp: '是',
        stampType: '公章',
        applicant: '胡宝宝',
        applyTime: '2024-11-10 12:00:00',
        processStatus: '待处理',
        processor: '--',
        processTime: '--'
    },
    {
        projectId: 'zdb202411220002',
        projectName: '2024不良资产收购项目2',
        transferor: '黄小龙',
        productType: '债权类',
        biddingStatus: '',
        processTitle: '',
        biddingMethod: '线上公开',
        biddingAmount: '1,000,000.00',
        needStamp: '是',
        stampType: '公章',
        applicant: '胡宝宝',
        applyTime: '2024-11-10 12:00:00',
        processStatus: '已处理',
        processor: '胡宝宝',
        processTime: '2024-11-10 12:00:00'
    },
    {
        projectId: 'zdb202411220003',
        projectName: '2024不良资产收购项目11',
        transferor: '黄小英',
        productType: '债权类',
        biddingStatus: '',
        processTitle: '',
        biddingMethod: '线上公开',
        biddingAmount: '1,000,000.00',
        needStamp: '是',
        stampType: '公章',
        applicant: '胡宝宝',
        applyTime: '2024-11-10 12:00:00',
        processStatus: '已处理',
        processor: '胡宝宝',
        processTime: '2024-11-10 12:00:00'
    }
])

const tabList = ref([
    { code: '0', info: '待处理' },
    { code: '1', info: '已同意' },
    { code: '2', info: '未同意' },
    { code: '3', info: '已撤销' },
    { code: 'all', info: '全部' },
])

const queryParmas = ref({ pageNum: 1, pageSize: 10 })

const handleQuery = () => {
    queryParmas.value.pageNum = 1
}

const handle = (row) => {
    console.log('处理行数据：', row)
}

function handleDetails(row) {
    const query = { path: route.path, pageType: 'bidding', progressStatus: 2 }
    router.push({ path: `/dueDiligence/projectInfo`, query })
}
const selectable = (row) => {
    // 根据状态判断是否可选
    return true
}

const resetQuery = () => {
    queryParmas.value = { pageNum: 1, pageSize: 10 }
}
</script>

<style lang="scss" scoped>
.range-scope {
    display: flex;

    span {
        margin: 0 10px;
    }
}
</style>