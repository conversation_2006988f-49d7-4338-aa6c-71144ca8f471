import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/textcolor/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/textcolor/plugin.js"() {
    (function() {
      "use strict";
      var global = tinymce.util.Tools.resolve("tinymce.PluginManager");
      function Plugin() {
        global.add("textcolor", function() {
        });
      }
      Plugin();
    })();
  }
});

// node_modules/tinymce/plugins/textcolor/index.js
var require_textcolor = __commonJS({
  "node_modules/tinymce/plugins/textcolor/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_textcolor
var tinymce_plugins_textcolor_default = require_textcolor();
export {
  tinymce_plugins_textcolor_default as default
};
//# sourceMappingURL=tinymce_plugins_textcolor.js.map
