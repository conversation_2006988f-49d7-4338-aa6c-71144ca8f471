{"version": 3, "sources": ["../tinymce/plugins/advlist/plugin.js", "../tinymce/plugins/advlist/index.js", "dep:tinymce_plugins_advlist"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var applyListFormat = function (editor, listName, styleValue) {\n      var cmd = listName === 'UL' ? 'InsertUnorderedList' : 'InsertOrderedList';\n      editor.execCommand(cmd, false, styleValue === false ? null : { 'list-style-type': styleValue });\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('ApplyUnorderedListStyle', function (ui, value) {\n        applyListFormat(editor, 'UL', value['list-style-type']);\n      });\n      editor.addCommand('ApplyOrderedListStyle', function (ui, value) {\n        applyListFormat(editor, 'OL', value['list-style-type']);\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var getNumberStyles = function (editor) {\n      var styles = editor.getParam('advlist_number_styles', 'default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman');\n      return styles ? styles.split(/[ ,]/) : [];\n    };\n    var getBulletStyles = function (editor) {\n      var styles = editor.getParam('advlist_bullet_styles', 'default,circle,square');\n      return styles ? styles.split(/[ ,]/) : [];\n    };\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var isChildOfBody = function (editor, elm) {\n      return editor.$.contains(editor.getBody(), elm);\n    };\n    var isTableCellNode = function (node) {\n      return node && /^(TH|TD)$/.test(node.nodeName);\n    };\n    var isListNode = function (editor) {\n      return function (node) {\n        return node && /^(OL|UL|DL)$/.test(node.nodeName) && isChildOfBody(editor, node);\n      };\n    };\n    var getSelectedStyleType = function (editor) {\n      var listElm = editor.dom.getParent(editor.selection.getNode(), 'ol,ul');\n      var style = editor.dom.getStyle(listElm, 'listStyleType');\n      return Optional.from(style);\n    };\n\n    var findIndex = function (list, predicate) {\n      for (var index = 0; index < list.length; index++) {\n        var element = list[index];\n        if (predicate(element)) {\n          return index;\n        }\n      }\n      return -1;\n    };\n    var styleValueToText = function (styleValue) {\n      return styleValue.replace(/\\-/g, ' ').replace(/\\b\\w/g, function (chr) {\n        return chr.toUpperCase();\n      });\n    };\n    var isWithinList = function (editor, e, nodeName) {\n      var tableCellIndex = findIndex(e.parents, isTableCellNode);\n      var parents = tableCellIndex !== -1 ? e.parents.slice(0, tableCellIndex) : e.parents;\n      var lists = global.grep(parents, isListNode(editor));\n      return lists.length > 0 && lists[0].nodeName === nodeName;\n    };\n    var makeSetupHandler = function (editor, nodeName) {\n      return function (api) {\n        var nodeChangeHandler = function (e) {\n          api.setActive(isWithinList(editor, e, nodeName));\n        };\n        editor.on('NodeChange', nodeChangeHandler);\n        return function () {\n          return editor.off('NodeChange', nodeChangeHandler);\n        };\n      };\n    };\n    var addSplitButton = function (editor, id, tooltip, cmd, nodeName, styles) {\n      editor.ui.registry.addSplitButton(id, {\n        tooltip: tooltip,\n        icon: nodeName === 'OL' ? 'ordered-list' : 'unordered-list',\n        presets: 'listpreview',\n        columns: 3,\n        fetch: function (callback) {\n          var items = global.map(styles, function (styleValue) {\n            var iconStyle = nodeName === 'OL' ? 'num' : 'bull';\n            var iconName = styleValue === 'disc' || styleValue === 'decimal' ? 'default' : styleValue;\n            var itemValue = styleValue === 'default' ? '' : styleValue;\n            var displayText = styleValueToText(styleValue);\n            return {\n              type: 'choiceitem',\n              value: itemValue,\n              icon: 'list-' + iconStyle + '-' + iconName,\n              text: displayText\n            };\n          });\n          callback(items);\n        },\n        onAction: function () {\n          return editor.execCommand(cmd);\n        },\n        onItemAction: function (_splitButtonApi, value) {\n          applyListFormat(editor, nodeName, value);\n        },\n        select: function (value) {\n          var listStyleType = getSelectedStyleType(editor);\n          return listStyleType.map(function (listStyle) {\n            return value === listStyle;\n          }).getOr(false);\n        },\n        onSetup: makeSetupHandler(editor, nodeName)\n      });\n    };\n    var addButton = function (editor, id, tooltip, cmd, nodeName, _styles) {\n      editor.ui.registry.addToggleButton(id, {\n        active: false,\n        tooltip: tooltip,\n        icon: nodeName === 'OL' ? 'ordered-list' : 'unordered-list',\n        onSetup: makeSetupHandler(editor, nodeName),\n        onAction: function () {\n          return editor.execCommand(cmd);\n        }\n      });\n    };\n    var addControl = function (editor, id, tooltip, cmd, nodeName, styles) {\n      if (styles.length > 1) {\n        addSplitButton(editor, id, tooltip, cmd, nodeName, styles);\n      } else {\n        addButton(editor, id, tooltip, cmd, nodeName);\n      }\n    };\n    var register = function (editor) {\n      addControl(editor, 'numlist', 'Numbered list', 'InsertOrderedList', 'OL', getNumberStyles(editor));\n      addControl(editor, 'bullist', 'Bullet list', 'InsertUnorderedList', 'UL', getBulletStyles(editor));\n    };\n\n    function Plugin () {\n      global$1.add('advlist', function (editor) {\n        if (editor.hasPlugin('lists')) {\n          register(editor);\n          register$1(editor);\n        } else {\n          console.error('Please use the Lists plugin together with the Advanced List plugin.');\n        }\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"advlist\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/advlist')\n//   ES2015:\n//     import 'tinymce/plugins/advlist'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/advlist/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,kBAAkB,SAAU,QAAQ,UAAU,YAAY;AAC5D,YAAI,MAAM,aAAa,OAAO,wBAAwB;AACtD,eAAO,YAAY,KAAK,OAAO,eAAe,QAAQ,OAAO,EAAE,mBAAmB;AAAA;AAGpF,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,2BAA2B,SAAU,IAAI,OAAO;AAChE,0BAAgB,QAAQ,MAAM,MAAM;AAAA;AAEtC,eAAO,WAAW,yBAAyB,SAAU,IAAI,OAAO;AAC9D,0BAAgB,QAAQ,MAAM,MAAM;AAAA;AAAA;AAIxC,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,kBAAkB,SAAU,QAAQ;AACtC,YAAI,SAAS,OAAO,SAAS,yBAAyB;AACtD,eAAO,SAAS,OAAO,MAAM,UAAU;AAAA;AAEzC,UAAI,kBAAkB,SAAU,QAAQ;AACtC,YAAI,SAAS,OAAO,SAAS,yBAAyB;AACtD,eAAO,SAAS,OAAO,MAAM,UAAU;AAAA;AAGzC,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,gBAAgB,SAAU,QAAQ,KAAK;AACzC,eAAO,OAAO,EAAE,SAAS,OAAO,WAAW;AAAA;AAE7C,UAAI,kBAAkB,SAAU,MAAM;AACpC,eAAO,QAAQ,YAAY,KAAK,KAAK;AAAA;AAEvC,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,SAAU,MAAM;AACrB,iBAAO,QAAQ,eAAe,KAAK,KAAK,aAAa,cAAc,QAAQ;AAAA;AAAA;AAG/E,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,YAAI,UAAU,OAAO,IAAI,UAAU,OAAO,UAAU,WAAW;AAC/D,YAAI,QAAQ,OAAO,IAAI,SAAS,SAAS;AACzC,eAAO,SAAS,KAAK;AAAA;AAGvB,UAAI,YAAY,SAAU,MAAM,WAAW;AACzC,iBAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS;AAChD,cAAI,UAAU,KAAK;AACnB,cAAI,UAAU,UAAU;AACtB,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,mBAAmB,SAAU,YAAY;AAC3C,eAAO,WAAW,QAAQ,OAAO,KAAK,QAAQ,SAAS,SAAU,KAAK;AACpE,iBAAO,IAAI;AAAA;AAAA;AAGf,UAAI,eAAe,SAAU,QAAQ,GAAG,UAAU;AAChD,YAAI,iBAAiB,UAAU,EAAE,SAAS;AAC1C,YAAI,UAAU,mBAAmB,KAAK,EAAE,QAAQ,MAAM,GAAG,kBAAkB,EAAE;AAC7E,YAAI,QAAQ,OAAO,KAAK,SAAS,WAAW;AAC5C,eAAO,MAAM,SAAS,KAAK,MAAM,GAAG,aAAa;AAAA;AAEnD,UAAI,mBAAmB,SAAU,QAAQ,UAAU;AACjD,eAAO,SAAU,KAAK;AACpB,cAAI,oBAAoB,SAAU,GAAG;AACnC,gBAAI,UAAU,aAAa,QAAQ,GAAG;AAAA;AAExC,iBAAO,GAAG,cAAc;AACxB,iBAAO,WAAY;AACjB,mBAAO,OAAO,IAAI,cAAc;AAAA;AAAA;AAAA;AAItC,UAAI,iBAAiB,SAAU,QAAQ,IAAI,SAAS,KAAK,UAAU,QAAQ;AACzE,eAAO,GAAG,SAAS,eAAe,IAAI;AAAA,UACpC;AAAA,UACA,MAAM,aAAa,OAAO,iBAAiB;AAAA,UAC3C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO,SAAU,UAAU;AACzB,gBAAI,QAAQ,OAAO,IAAI,QAAQ,SAAU,YAAY;AACnD,kBAAI,YAAY,aAAa,OAAO,QAAQ;AAC5C,kBAAI,WAAW,eAAe,UAAU,eAAe,YAAY,YAAY;AAC/E,kBAAI,YAAY,eAAe,YAAY,KAAK;AAChD,kBAAI,cAAc,iBAAiB;AACnC,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,MAAM,UAAU,YAAY,MAAM;AAAA,gBAClC,MAAM;AAAA;AAAA;AAGV,qBAAS;AAAA;AAAA,UAEX,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA,UAE5B,cAAc,SAAU,iBAAiB,OAAO;AAC9C,4BAAgB,QAAQ,UAAU;AAAA;AAAA,UAEpC,QAAQ,SAAU,OAAO;AACvB,gBAAI,gBAAgB,qBAAqB;AACzC,mBAAO,cAAc,IAAI,SAAU,WAAW;AAC5C,qBAAO,UAAU;AAAA,eAChB,MAAM;AAAA;AAAA,UAEX,SAAS,iBAAiB,QAAQ;AAAA;AAAA;AAGtC,UAAI,YAAY,SAAU,QAAQ,IAAI,SAAS,KAAK,UAAU,SAAS;AACrE,eAAO,GAAG,SAAS,gBAAgB,IAAI;AAAA,UACrC,QAAQ;AAAA,UACR;AAAA,UACA,MAAM,aAAa,OAAO,iBAAiB;AAAA,UAC3C,SAAS,iBAAiB,QAAQ;AAAA,UAClC,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA;AAAA;AAIhC,UAAI,aAAa,SAAU,QAAQ,IAAI,SAAS,KAAK,UAAU,QAAQ;AACrE,YAAI,OAAO,SAAS,GAAG;AACrB,yBAAe,QAAQ,IAAI,SAAS,KAAK,UAAU;AAAA,eAC9C;AACL,oBAAU,QAAQ,IAAI,SAAS,KAAK;AAAA;AAAA;AAGxC,UAAI,WAAW,SAAU,QAAQ;AAC/B,mBAAW,QAAQ,WAAW,iBAAiB,qBAAqB,MAAM,gBAAgB;AAC1F,mBAAW,QAAQ,WAAW,eAAe,uBAAuB,MAAM,gBAAgB;AAAA;AAG5F,wBAAmB;AACjB,iBAAS,IAAI,WAAW,SAAU,QAAQ;AACxC,cAAI,OAAO,UAAU,UAAU;AAC7B,qBAAS;AACT,uBAAW;AAAA,iBACN;AACL,oBAAQ,MAAM;AAAA;AAAA;AAAA;AAKpB;AAAA;AAAA;AAAA;;;AClQJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,kCAAQ;", "names": []}