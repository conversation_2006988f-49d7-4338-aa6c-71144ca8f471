{"version": 3, "sources": ["../tinymce/plugins/media/plugin.js", "../tinymce/plugins/media/index.js", "dep:tinymce_plugins_media"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$9 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isString = isType('string');\n    var isObject = isType('object');\n    var isArray = isType('array');\n    var isNullable = function (a) {\n      return a === null || a === undefined;\n    };\n    var isNonNullable = function (a) {\n      return !isNullable(a);\n    };\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var nativePush = Array.prototype.push;\n    var each$1 = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var flatten = function (xs) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var keys = Object.keys;\n    var hasOwnProperty = Object.hasOwnProperty;\n    var each = function (obj, f) {\n      var props = keys(obj);\n      for (var k = 0, len = props.length; k < len; k++) {\n        var i = props[k];\n        var x = obj[i];\n        f(x, i);\n      }\n    };\n    var get$1 = function (obj, key) {\n      return has(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n\n    var getScripts = function (editor) {\n      return editor.getParam('media_scripts');\n    };\n    var getAudioTemplateCallback = function (editor) {\n      return editor.getParam('audio_template_callback');\n    };\n    var getVideoTemplateCallback = function (editor) {\n      return editor.getParam('video_template_callback');\n    };\n    var hasLiveEmbeds = function (editor) {\n      return editor.getParam('media_live_embeds', true);\n    };\n    var shouldFilterHtml = function (editor) {\n      return editor.getParam('media_filter_html', true);\n    };\n    var getUrlResolver = function (editor) {\n      return editor.getParam('media_url_resolver');\n    };\n    var hasAltSource = function (editor) {\n      return editor.getParam('media_alt_source', true);\n    };\n    var hasPoster = function (editor) {\n      return editor.getParam('media_poster', true);\n    };\n    var hasDimensions = function (editor) {\n      return editor.getParam('media_dimensions', true);\n    };\n\n    var global$8 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var global$7 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.html.SaxParser');\n\n    var getVideoScriptMatch = function (prefixes, src) {\n      if (prefixes) {\n        for (var i = 0; i < prefixes.length; i++) {\n          if (src.indexOf(prefixes[i].filter) !== -1) {\n            return prefixes[i];\n          }\n        }\n      }\n    };\n\n    var DOM$1 = global$7.DOM;\n    var trimPx = function (value) {\n      return value.replace(/px$/, '');\n    };\n    var getEphoxEmbedData = function (attrs) {\n      var style = attrs.map.style;\n      var styles = style ? DOM$1.parseStyle(style) : {};\n      return {\n        type: 'ephox-embed-iri',\n        source: attrs.map['data-ephox-embed-iri'],\n        altsource: '',\n        poster: '',\n        width: get$1(styles, 'max-width').map(trimPx).getOr(''),\n        height: get$1(styles, 'max-height').map(trimPx).getOr('')\n      };\n    };\n    var htmlToData = function (prefixes, html) {\n      var isEphoxEmbed = Cell(false);\n      var data = {};\n      global$6({\n        validate: false,\n        allow_conditional_comments: true,\n        start: function (name, attrs) {\n          if (isEphoxEmbed.get()) ; else if (has(attrs.map, 'data-ephox-embed-iri')) {\n            isEphoxEmbed.set(true);\n            data = getEphoxEmbedData(attrs);\n          } else {\n            if (!data.source && name === 'param') {\n              data.source = attrs.map.movie;\n            }\n            if (name === 'iframe' || name === 'object' || name === 'embed' || name === 'video' || name === 'audio') {\n              if (!data.type) {\n                data.type = name;\n              }\n              data = global$8.extend(attrs.map, data);\n            }\n            if (name === 'script') {\n              var videoScript = getVideoScriptMatch(prefixes, attrs.map.src);\n              if (!videoScript) {\n                return;\n              }\n              data = {\n                type: 'script',\n                source: attrs.map.src,\n                width: String(videoScript.width),\n                height: String(videoScript.height)\n              };\n            }\n            if (name === 'source') {\n              if (!data.source) {\n                data.source = attrs.map.src;\n              } else if (!data.altsource) {\n                data.altsource = attrs.map.src;\n              }\n            }\n            if (name === 'img' && !data.poster) {\n              data.poster = attrs.map.src;\n            }\n          }\n        }\n      }).parse(html);\n      data.source = data.source || data.src || data.data;\n      data.altsource = data.altsource || '';\n      data.poster = data.poster || '';\n      return data;\n    };\n\n    var guess = function (url) {\n      var mimes = {\n        mp3: 'audio/mpeg',\n        m4a: 'audio/x-m4a',\n        wav: 'audio/wav',\n        mp4: 'video/mp4',\n        webm: 'video/webm',\n        ogg: 'video/ogg',\n        swf: 'application/x-shockwave-flash'\n      };\n      var fileEnd = url.toLowerCase().split('.').pop();\n      var mime = mimes[fileEnd];\n      return mime ? mime : '';\n    };\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.html.Schema');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.html.Writer');\n\n    var DOM = global$7.DOM;\n    var addPx = function (value) {\n      return /^[0-9.]+$/.test(value) ? value + 'px' : value;\n    };\n    var setAttributes = function (attrs, updatedAttrs) {\n      each(updatedAttrs, function (val, name) {\n        var value = '' + val;\n        if (attrs.map[name]) {\n          var i = attrs.length;\n          while (i--) {\n            var attr = attrs[i];\n            if (attr.name === name) {\n              if (value) {\n                attrs.map[name] = value;\n                attr.value = value;\n              } else {\n                delete attrs.map[name];\n                attrs.splice(i, 1);\n              }\n            }\n          }\n        } else if (value) {\n          attrs.push({\n            name: name,\n            value: value\n          });\n          attrs.map[name] = value;\n        }\n      });\n    };\n    var updateEphoxEmbed = function (data, attrs) {\n      var style = attrs.map.style;\n      var styleMap = style ? DOM.parseStyle(style) : {};\n      styleMap['max-width'] = addPx(data.width);\n      styleMap['max-height'] = addPx(data.height);\n      setAttributes(attrs, { style: DOM.serializeStyle(styleMap) });\n    };\n    var sources = [\n      'source',\n      'altsource'\n    ];\n    var updateHtml = function (html, data, updateAll) {\n      var writer = global$4();\n      var isEphoxEmbed = Cell(false);\n      var sourceCount = 0;\n      var hasImage;\n      global$6({\n        validate: false,\n        allow_conditional_comments: true,\n        comment: function (text) {\n          writer.comment(text);\n        },\n        cdata: function (text) {\n          writer.cdata(text);\n        },\n        text: function (text, raw) {\n          writer.text(text, raw);\n        },\n        start: function (name, attrs, empty) {\n          if (isEphoxEmbed.get()) ; else if (has(attrs.map, 'data-ephox-embed-iri')) {\n            isEphoxEmbed.set(true);\n            updateEphoxEmbed(data, attrs);\n          } else {\n            switch (name) {\n            case 'video':\n            case 'object':\n            case 'embed':\n            case 'img':\n            case 'iframe':\n              if (data.height !== undefined && data.width !== undefined) {\n                setAttributes(attrs, {\n                  width: data.width,\n                  height: data.height\n                });\n              }\n              break;\n            }\n            if (updateAll) {\n              switch (name) {\n              case 'video':\n                setAttributes(attrs, {\n                  poster: data.poster,\n                  src: ''\n                });\n                if (data.altsource) {\n                  setAttributes(attrs, { src: '' });\n                }\n                break;\n              case 'iframe':\n                setAttributes(attrs, { src: data.source });\n                break;\n              case 'source':\n                if (sourceCount < 2) {\n                  setAttributes(attrs, {\n                    src: data[sources[sourceCount]],\n                    type: data[sources[sourceCount] + 'mime']\n                  });\n                  if (!data[sources[sourceCount]]) {\n                    return;\n                  }\n                }\n                sourceCount++;\n                break;\n              case 'img':\n                if (!data.poster) {\n                  return;\n                }\n                hasImage = true;\n                break;\n              }\n            }\n          }\n          writer.start(name, attrs, empty);\n        },\n        end: function (name) {\n          if (!isEphoxEmbed.get()) {\n            if (name === 'video' && updateAll) {\n              for (var index = 0; index < 2; index++) {\n                if (data[sources[index]]) {\n                  var attrs = [];\n                  attrs.map = {};\n                  if (sourceCount <= index) {\n                    setAttributes(attrs, {\n                      src: data[sources[index]],\n                      type: data[sources[index] + 'mime']\n                    });\n                    writer.start('source', attrs, true);\n                  }\n                }\n              }\n            }\n            if (data.poster && name === 'object' && updateAll && !hasImage) {\n              var imgAttrs = [];\n              imgAttrs.map = {};\n              setAttributes(imgAttrs, {\n                src: data.poster,\n                width: data.width,\n                height: data.height\n              });\n              writer.start('img', imgAttrs, true);\n            }\n          }\n          writer.end(name);\n        }\n      }, global$5({})).parse(html);\n      return writer.getContent();\n    };\n\n    var urlPatterns = [\n      {\n        regex: /youtu\\.be\\/([\\w\\-_\\?&=.]+)/i,\n        type: 'iframe',\n        w: 560,\n        h: 314,\n        url: 'www.youtube.com/embed/$1',\n        allowFullscreen: true\n      },\n      {\n        regex: /youtube\\.com(.+)v=([^&]+)(&([a-z0-9&=\\-_]+))?/i,\n        type: 'iframe',\n        w: 560,\n        h: 314,\n        url: 'www.youtube.com/embed/$2?$4',\n        allowFullscreen: true\n      },\n      {\n        regex: /youtube.com\\/embed\\/([a-z0-9\\?&=\\-_]+)/i,\n        type: 'iframe',\n        w: 560,\n        h: 314,\n        url: 'www.youtube.com/embed/$1',\n        allowFullscreen: true\n      },\n      {\n        regex: /vimeo\\.com\\/([0-9]+)/,\n        type: 'iframe',\n        w: 425,\n        h: 350,\n        url: 'player.vimeo.com/video/$1?title=0&byline=0&portrait=0&color=8dc7dc',\n        allowFullscreen: true\n      },\n      {\n        regex: /vimeo\\.com\\/(.*)\\/([0-9]+)/,\n        type: 'iframe',\n        w: 425,\n        h: 350,\n        url: 'player.vimeo.com/video/$2?title=0&amp;byline=0',\n        allowFullscreen: true\n      },\n      {\n        regex: /maps\\.google\\.([a-z]{2,3})\\/maps\\/(.+)msid=(.+)/,\n        type: 'iframe',\n        w: 425,\n        h: 350,\n        url: 'maps.google.com/maps/ms?msid=$2&output=embed\"',\n        allowFullscreen: false\n      },\n      {\n        regex: /dailymotion\\.com\\/video\\/([^_]+)/,\n        type: 'iframe',\n        w: 480,\n        h: 270,\n        url: 'www.dailymotion.com/embed/video/$1',\n        allowFullscreen: true\n      },\n      {\n        regex: /dai\\.ly\\/([^_]+)/,\n        type: 'iframe',\n        w: 480,\n        h: 270,\n        url: 'www.dailymotion.com/embed/video/$1',\n        allowFullscreen: true\n      }\n    ];\n    var getProtocol = function (url) {\n      var protocolMatches = url.match(/^(https?:\\/\\/|www\\.)(.+)$/i);\n      if (protocolMatches && protocolMatches.length > 1) {\n        return protocolMatches[1] === 'www.' ? 'https://' : protocolMatches[1];\n      } else {\n        return 'https://';\n      }\n    };\n    var getUrl = function (pattern, url) {\n      var protocol = getProtocol(url);\n      var match = pattern.regex.exec(url);\n      var newUrl = protocol + pattern.url;\n      var _loop_1 = function (i) {\n        newUrl = newUrl.replace('$' + i, function () {\n          return match[i] ? match[i] : '';\n        });\n      };\n      for (var i = 0; i < match.length; i++) {\n        _loop_1(i);\n      }\n      return newUrl.replace(/\\?$/, '');\n    };\n    var matchPattern = function (url) {\n      var patterns = urlPatterns.filter(function (pattern) {\n        return pattern.regex.test(url);\n      });\n      if (patterns.length > 0) {\n        return global$8.extend({}, patterns[0], { url: getUrl(patterns[0], url) });\n      } else {\n        return null;\n      }\n    };\n\n    var getIframeHtml = function (data) {\n      var allowFullscreen = data.allowfullscreen ? ' allowFullscreen=\"1\"' : '';\n      return '<iframe src=\"' + data.source + '\" width=\"' + data.width + '\" height=\"' + data.height + '\"' + allowFullscreen + '></iframe>';\n    };\n    var getFlashHtml = function (data) {\n      var html = '<object data=\"' + data.source + '\" width=\"' + data.width + '\" height=\"' + data.height + '\" type=\"application/x-shockwave-flash\">';\n      if (data.poster) {\n        html += '<img src=\"' + data.poster + '\" width=\"' + data.width + '\" height=\"' + data.height + '\" />';\n      }\n      html += '</object>';\n      return html;\n    };\n    var getAudioHtml = function (data, audioTemplateCallback) {\n      if (audioTemplateCallback) {\n        return audioTemplateCallback(data);\n      } else {\n        return '<audio controls=\"controls\" src=\"' + data.source + '\">' + (data.altsource ? '\\n<source src=\"' + data.altsource + '\"' + (data.altsourcemime ? ' type=\"' + data.altsourcemime + '\"' : '') + ' />\\n' : '') + '</audio>';\n      }\n    };\n    var getVideoHtml = function (data, videoTemplateCallback) {\n      if (videoTemplateCallback) {\n        return videoTemplateCallback(data);\n      } else {\n        return '<video width=\"' + data.width + '\" height=\"' + data.height + '\"' + (data.poster ? ' poster=\"' + data.poster + '\"' : '') + ' controls=\"controls\">\\n' + '<source src=\"' + data.source + '\"' + (data.sourcemime ? ' type=\"' + data.sourcemime + '\"' : '') + ' />\\n' + (data.altsource ? '<source src=\"' + data.altsource + '\"' + (data.altsourcemime ? ' type=\"' + data.altsourcemime + '\"' : '') + ' />\\n' : '') + '</video>';\n      }\n    };\n    var getScriptHtml = function (data) {\n      return '<script src=\"' + data.source + '\"></script>';\n    };\n    var dataToHtml = function (editor, dataIn) {\n      var data = global$8.extend({}, dataIn);\n      if (!data.source) {\n        global$8.extend(data, htmlToData(getScripts(editor), data.embed));\n        if (!data.source) {\n          return '';\n        }\n      }\n      if (!data.altsource) {\n        data.altsource = '';\n      }\n      if (!data.poster) {\n        data.poster = '';\n      }\n      data.source = editor.convertURL(data.source, 'source');\n      data.altsource = editor.convertURL(data.altsource, 'source');\n      data.sourcemime = guess(data.source);\n      data.altsourcemime = guess(data.altsource);\n      data.poster = editor.convertURL(data.poster, 'poster');\n      var pattern = matchPattern(data.source);\n      if (pattern) {\n        data.source = pattern.url;\n        data.type = pattern.type;\n        data.allowfullscreen = pattern.allowFullscreen;\n        data.width = data.width || String(pattern.w);\n        data.height = data.height || String(pattern.h);\n      }\n      if (data.embed) {\n        return updateHtml(data.embed, data, true);\n      } else {\n        var videoScript = getVideoScriptMatch(getScripts(editor), data.source);\n        if (videoScript) {\n          data.type = 'script';\n          data.width = String(videoScript.width);\n          data.height = String(videoScript.height);\n        }\n        var audioTemplateCallback = getAudioTemplateCallback(editor);\n        var videoTemplateCallback = getVideoTemplateCallback(editor);\n        data.width = data.width || '300';\n        data.height = data.height || '150';\n        global$8.each(data, function (value, key) {\n          data[key] = editor.dom.encode('' + value);\n        });\n        if (data.type === 'iframe') {\n          return getIframeHtml(data);\n        } else if (data.sourcemime === 'application/x-shockwave-flash') {\n          return getFlashHtml(data);\n        } else if (data.sourcemime.indexOf('audio') !== -1) {\n          return getAudioHtml(data, audioTemplateCallback);\n        } else if (data.type === 'script') {\n          return getScriptHtml(data);\n        } else {\n          return getVideoHtml(data, videoTemplateCallback);\n        }\n      }\n    };\n\n    var isMediaElement = function (element) {\n      return element.hasAttribute('data-mce-object') || element.hasAttribute('data-ephox-embed-iri');\n    };\n    var setup$2 = function (editor) {\n      editor.on('click keyup touchend', function () {\n        var selectedNode = editor.selection.getNode();\n        if (selectedNode && editor.dom.hasClass(selectedNode, 'mce-preview-object')) {\n          if (editor.dom.getAttrib(selectedNode, 'data-mce-selected')) {\n            selectedNode.setAttribute('data-mce-selected', '2');\n          }\n        }\n      });\n      editor.on('ObjectSelected', function (e) {\n        var objectType = e.target.getAttribute('data-mce-object');\n        if (objectType === 'script') {\n          e.preventDefault();\n        }\n      });\n      editor.on('ObjectResized', function (e) {\n        var target = e.target;\n        if (target.getAttribute('data-mce-object')) {\n          var html = target.getAttribute('data-mce-html');\n          if (html) {\n            html = unescape(html);\n            target.setAttribute('data-mce-html', escape(updateHtml(html, {\n              width: String(e.width),\n              height: String(e.height)\n            })));\n          }\n        }\n      });\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.util.Promise');\n\n    var cache = {};\n    var embedPromise = function (data, dataToHtml, handler) {\n      return new global$3(function (res, rej) {\n        var wrappedResolve = function (response) {\n          if (response.html) {\n            cache[data.source] = response;\n          }\n          return res({\n            url: data.source,\n            html: response.html ? response.html : dataToHtml(data)\n          });\n        };\n        if (cache[data.source]) {\n          wrappedResolve(cache[data.source]);\n        } else {\n          handler({ url: data.source }, wrappedResolve, rej);\n        }\n      });\n    };\n    var defaultPromise = function (data, dataToHtml) {\n      return global$3.resolve({\n        html: dataToHtml(data),\n        url: data.source\n      });\n    };\n    var loadedData = function (editor) {\n      return function (data) {\n        return dataToHtml(editor, data);\n      };\n    };\n    var getEmbedHtml = function (editor, data) {\n      var embedHandler = getUrlResolver(editor);\n      return embedHandler ? embedPromise(data, loadedData(editor), embedHandler) : defaultPromise(data, loadedData(editor));\n    };\n    var isCached = function (url) {\n      return has(cache, url);\n    };\n\n    var extractMeta = function (sourceInput, data) {\n      return get$1(data, sourceInput).bind(function (mainData) {\n        return get$1(mainData, 'meta');\n      });\n    };\n    var getValue = function (data, metaData, sourceInput) {\n      return function (prop) {\n        var _a;\n        var getFromData = function () {\n          return get$1(data, prop);\n        };\n        var getFromMetaData = function () {\n          return get$1(metaData, prop);\n        };\n        var getNonEmptyValue = function (c) {\n          return get$1(c, 'value').bind(function (v) {\n            return v.length > 0 ? Optional.some(v) : Optional.none();\n          });\n        };\n        var getFromValueFirst = function () {\n          return getFromData().bind(function (child) {\n            return isObject(child) ? getNonEmptyValue(child).orThunk(getFromMetaData) : getFromMetaData().orThunk(function () {\n              return Optional.from(child);\n            });\n          });\n        };\n        var getFromMetaFirst = function () {\n          return getFromMetaData().orThunk(function () {\n            return getFromData().bind(function (child) {\n              return isObject(child) ? getNonEmptyValue(child) : Optional.from(child);\n            });\n          });\n        };\n        return _a = {}, _a[prop] = (prop === sourceInput ? getFromValueFirst() : getFromMetaFirst()).getOr(''), _a;\n      };\n    };\n    var getDimensions = function (data, metaData) {\n      var dimensions = {};\n      get$1(data, 'dimensions').each(function (dims) {\n        each$1([\n          'width',\n          'height'\n        ], function (prop) {\n          get$1(metaData, prop).orThunk(function () {\n            return get$1(dims, prop);\n          }).each(function (value) {\n            return dimensions[prop] = value;\n          });\n        });\n      });\n      return dimensions;\n    };\n    var unwrap = function (data, sourceInput) {\n      var metaData = sourceInput ? extractMeta(sourceInput, data).getOr({}) : {};\n      var get = getValue(data, metaData, sourceInput);\n      return __assign(__assign(__assign(__assign(__assign({}, get('source')), get('altsource')), get('poster')), get('embed')), getDimensions(data, metaData));\n    };\n    var wrap = function (data) {\n      var wrapped = __assign(__assign({}, data), {\n        source: { value: get$1(data, 'source').getOr('') },\n        altsource: { value: get$1(data, 'altsource').getOr('') },\n        poster: { value: get$1(data, 'poster').getOr('') }\n      });\n      each$1([\n        'width',\n        'height'\n      ], function (prop) {\n        get$1(data, prop).each(function (value) {\n          var dimensions = wrapped.dimensions || {};\n          dimensions[prop] = value;\n          wrapped.dimensions = dimensions;\n        });\n      });\n      return wrapped;\n    };\n    var handleError = function (editor) {\n      return function (error) {\n        var errorMessage = error && error.msg ? 'Media embed handler error: ' + error.msg : 'Media embed handler threw unknown error.';\n        editor.notificationManager.open({\n          type: 'error',\n          text: errorMessage\n        });\n      };\n    };\n    var snippetToData = function (editor, embedSnippet) {\n      return htmlToData(getScripts(editor), embedSnippet);\n    };\n    var getEditorData = function (editor) {\n      var element = editor.selection.getNode();\n      var snippet = isMediaElement(element) ? editor.serializer.serialize(element, { selection: true }) : '';\n      return __assign({ embed: snippet }, htmlToData(getScripts(editor), snippet));\n    };\n    var addEmbedHtml = function (api, editor) {\n      return function (response) {\n        if (isString(response.url) && response.url.trim().length > 0) {\n          var html = response.html;\n          var snippetData = snippetToData(editor, html);\n          var nuData = __assign(__assign({}, snippetData), {\n            source: response.url,\n            embed: html\n          });\n          api.setData(wrap(nuData));\n        }\n      };\n    };\n    var selectPlaceholder = function (editor, beforeObjects) {\n      var afterObjects = editor.dom.select('*[data-mce-object]');\n      for (var i = 0; i < beforeObjects.length; i++) {\n        for (var y = afterObjects.length - 1; y >= 0; y--) {\n          if (beforeObjects[i] === afterObjects[y]) {\n            afterObjects.splice(y, 1);\n          }\n        }\n      }\n      editor.selection.select(afterObjects[0]);\n    };\n    var handleInsert = function (editor, html) {\n      var beforeObjects = editor.dom.select('*[data-mce-object]');\n      editor.insertContent(html);\n      selectPlaceholder(editor, beforeObjects);\n      editor.nodeChanged();\n    };\n    var submitForm = function (prevData, newData, editor) {\n      newData.embed = updateHtml(newData.embed, newData);\n      if (newData.embed && (prevData.source === newData.source || isCached(newData.source))) {\n        handleInsert(editor, newData.embed);\n      } else {\n        getEmbedHtml(editor, newData).then(function (response) {\n          handleInsert(editor, response.html);\n        }).catch(handleError(editor));\n      }\n    };\n    var showDialog = function (editor) {\n      var editorData = getEditorData(editor);\n      var currentData = Cell(editorData);\n      var initialData = wrap(editorData);\n      var handleSource = function (prevData, api) {\n        var serviceData = unwrap(api.getData(), 'source');\n        if (prevData.source !== serviceData.source) {\n          addEmbedHtml(win, editor)({\n            url: serviceData.source,\n            html: ''\n          });\n          getEmbedHtml(editor, serviceData).then(addEmbedHtml(win, editor)).catch(handleError(editor));\n        }\n      };\n      var handleEmbed = function (api) {\n        var data = unwrap(api.getData());\n        var dataFromEmbed = snippetToData(editor, data.embed);\n        api.setData(wrap(dataFromEmbed));\n      };\n      var handleUpdate = function (api, sourceInput) {\n        var data = unwrap(api.getData(), sourceInput);\n        var embed = dataToHtml(editor, data);\n        api.setData(wrap(__assign(__assign({}, data), { embed: embed })));\n      };\n      var mediaInput = [{\n          name: 'source',\n          type: 'urlinput',\n          filetype: 'media',\n          label: 'Source'\n        }];\n      var sizeInput = !hasDimensions(editor) ? [] : [{\n          type: 'sizeinput',\n          name: 'dimensions',\n          label: 'Constrain proportions',\n          constrain: true\n        }];\n      var generalTab = {\n        title: 'General',\n        name: 'general',\n        items: flatten([\n          mediaInput,\n          sizeInput\n        ])\n      };\n      var embedTextarea = {\n        type: 'textarea',\n        name: 'embed',\n        label: 'Paste your embed code below:'\n      };\n      var embedTab = {\n        title: 'Embed',\n        items: [embedTextarea]\n      };\n      var advancedFormItems = [];\n      if (hasAltSource(editor)) {\n        advancedFormItems.push({\n          name: 'altsource',\n          type: 'urlinput',\n          filetype: 'media',\n          label: 'Alternative source URL'\n        });\n      }\n      if (hasPoster(editor)) {\n        advancedFormItems.push({\n          name: 'poster',\n          type: 'urlinput',\n          filetype: 'image',\n          label: 'Media poster (Image URL)'\n        });\n      }\n      var advancedTab = {\n        title: 'Advanced',\n        name: 'advanced',\n        items: advancedFormItems\n      };\n      var tabs = [\n        generalTab,\n        embedTab\n      ];\n      if (advancedFormItems.length > 0) {\n        tabs.push(advancedTab);\n      }\n      var body = {\n        type: 'tabpanel',\n        tabs: tabs\n      };\n      var win = editor.windowManager.open({\n        title: 'Insert/Edit Media',\n        size: 'normal',\n        body: body,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        onSubmit: function (api) {\n          var serviceData = unwrap(api.getData());\n          submitForm(currentData.get(), serviceData, editor);\n          api.close();\n        },\n        onChange: function (api, detail) {\n          switch (detail.name) {\n          case 'source':\n            handleSource(currentData.get(), api);\n            break;\n          case 'embed':\n            handleEmbed(api);\n            break;\n          case 'dimensions':\n          case 'altsource':\n          case 'poster':\n            handleUpdate(api, detail.name);\n            break;\n          }\n          currentData.set(unwrap(api.getData()));\n        },\n        initialData: initialData\n      });\n    };\n\n    var get = function (editor) {\n      var showDialog$1 = function () {\n        showDialog(editor);\n      };\n      return { showDialog: showDialog$1 };\n    };\n\n    var register$1 = function (editor) {\n      var showDialog$1 = function () {\n        showDialog(editor);\n      };\n      editor.addCommand('mceMedia', showDialog$1);\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.html.Node');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.html.DomParser');\n\n    var sanitize = function (editor, html) {\n      if (shouldFilterHtml(editor) === false) {\n        return html;\n      }\n      var writer = global$4();\n      var blocked;\n      global$6({\n        validate: false,\n        allow_conditional_comments: false,\n        comment: function (text) {\n          if (!blocked) {\n            writer.comment(text);\n          }\n        },\n        cdata: function (text) {\n          if (!blocked) {\n            writer.cdata(text);\n          }\n        },\n        text: function (text, raw) {\n          if (!blocked) {\n            writer.text(text, raw);\n          }\n        },\n        start: function (name, attrs, empty) {\n          blocked = true;\n          if (name === 'script' || name === 'noscript' || name === 'svg') {\n            return;\n          }\n          for (var i = attrs.length - 1; i >= 0; i--) {\n            var attrName = attrs[i].name;\n            if (attrName.indexOf('on') === 0) {\n              delete attrs.map[attrName];\n              attrs.splice(i, 1);\n            }\n            if (attrName === 'style') {\n              attrs[i].value = editor.dom.serializeStyle(editor.dom.parseStyle(attrs[i].value), name);\n            }\n          }\n          writer.start(name, attrs, empty);\n          blocked = false;\n        },\n        end: function (name) {\n          if (blocked) {\n            return;\n          }\n          writer.end(name);\n        }\n      }, global$5({})).parse(html);\n      return writer.getContent();\n    };\n\n    var isLiveEmbedNode = function (node) {\n      var name = node.name;\n      return name === 'iframe' || name === 'video' || name === 'audio';\n    };\n    var getDimension = function (node, styles, dimension, defaultValue) {\n      if (defaultValue === void 0) {\n        defaultValue = null;\n      }\n      var value = node.attr(dimension);\n      if (isNonNullable(value)) {\n        return value;\n      } else if (!has(styles, dimension)) {\n        return defaultValue;\n      } else {\n        return null;\n      }\n    };\n    var setDimensions = function (node, previewNode, styles) {\n      var useDefaults = previewNode.name === 'img' || node.name === 'video';\n      var defaultWidth = useDefaults ? '300' : null;\n      var fallbackHeight = node.name === 'audio' ? '30' : '150';\n      var defaultHeight = useDefaults ? fallbackHeight : null;\n      previewNode.attr({\n        width: getDimension(node, styles, 'width', defaultWidth),\n        height: getDimension(node, styles, 'height', defaultHeight)\n      });\n    };\n    var appendNodeContent = function (editor, nodeName, previewNode, html) {\n      var newNode = global({\n        forced_root_block: false,\n        validate: false\n      }, editor.schema).parse(html, { context: nodeName });\n      while (newNode.firstChild) {\n        previewNode.append(newNode.firstChild);\n      }\n    };\n    var createPlaceholderNode = function (editor, node) {\n      var name = node.name;\n      var placeHolder = new global$2('img', 1);\n      placeHolder.shortEnded = true;\n      retainAttributesAndInnerHtml(editor, node, placeHolder);\n      setDimensions(node, placeHolder, {});\n      placeHolder.attr({\n        'style': node.attr('style'),\n        'src': global$1.transparentSrc,\n        'data-mce-object': name,\n        'class': 'mce-object mce-object-' + name\n      });\n      return placeHolder;\n    };\n    var createPreviewNode = function (editor, node) {\n      var name = node.name;\n      var previewWrapper = new global$2('span', 1);\n      previewWrapper.attr({\n        'contentEditable': 'false',\n        'style': node.attr('style'),\n        'data-mce-object': name,\n        'class': 'mce-preview-object mce-object-' + name\n      });\n      retainAttributesAndInnerHtml(editor, node, previewWrapper);\n      var styles = editor.dom.parseStyle(node.attr('style'));\n      var previewNode = new global$2(name, 1);\n      setDimensions(node, previewNode, styles);\n      previewNode.attr({\n        src: node.attr('src'),\n        style: node.attr('style'),\n        class: node.attr('class')\n      });\n      if (name === 'iframe') {\n        previewNode.attr({\n          allowfullscreen: node.attr('allowfullscreen'),\n          frameborder: '0'\n        });\n      } else {\n        var attrs = [\n          'controls',\n          'crossorigin',\n          'currentTime',\n          'loop',\n          'muted',\n          'poster',\n          'preload'\n        ];\n        each$1(attrs, function (attrName) {\n          previewNode.attr(attrName, node.attr(attrName));\n        });\n        var sanitizedHtml = previewWrapper.attr('data-mce-html');\n        if (isNonNullable(sanitizedHtml)) {\n          appendNodeContent(editor, name, previewNode, unescape(sanitizedHtml));\n        }\n      }\n      var shimNode = new global$2('span', 1);\n      shimNode.attr('class', 'mce-shim');\n      previewWrapper.append(previewNode);\n      previewWrapper.append(shimNode);\n      return previewWrapper;\n    };\n    var retainAttributesAndInnerHtml = function (editor, sourceNode, targetNode) {\n      var attribs = sourceNode.attributes;\n      var ai = attribs.length;\n      while (ai--) {\n        var attrName = attribs[ai].name;\n        var attrValue = attribs[ai].value;\n        if (attrName !== 'width' && attrName !== 'height' && attrName !== 'style') {\n          if (attrName === 'data' || attrName === 'src') {\n            attrValue = editor.convertURL(attrValue, attrName);\n          }\n          targetNode.attr('data-mce-p-' + attrName, attrValue);\n        }\n      }\n      var innerHtml = sourceNode.firstChild && sourceNode.firstChild.value;\n      if (innerHtml) {\n        targetNode.attr('data-mce-html', escape(sanitize(editor, innerHtml)));\n        targetNode.firstChild = null;\n      }\n    };\n    var isPageEmbedWrapper = function (node) {\n      var nodeClass = node.attr('class');\n      return nodeClass && /\\btiny-pageembed\\b/.test(nodeClass);\n    };\n    var isWithinEmbedWrapper = function (node) {\n      while (node = node.parent) {\n        if (node.attr('data-ephox-embed-iri') || isPageEmbedWrapper(node)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    var placeHolderConverter = function (editor) {\n      return function (nodes) {\n        var i = nodes.length;\n        var node;\n        var videoScript;\n        while (i--) {\n          node = nodes[i];\n          if (!node.parent) {\n            continue;\n          }\n          if (node.parent.attr('data-mce-object')) {\n            continue;\n          }\n          if (node.name === 'script') {\n            videoScript = getVideoScriptMatch(getScripts(editor), node.attr('src'));\n            if (!videoScript) {\n              continue;\n            }\n          }\n          if (videoScript) {\n            if (videoScript.width) {\n              node.attr('width', videoScript.width.toString());\n            }\n            if (videoScript.height) {\n              node.attr('height', videoScript.height.toString());\n            }\n          }\n          if (isLiveEmbedNode(node) && hasLiveEmbeds(editor) && global$1.ceFalse) {\n            if (!isWithinEmbedWrapper(node)) {\n              node.replace(createPreviewNode(editor, node));\n            }\n          } else {\n            if (!isWithinEmbedWrapper(node)) {\n              node.replace(createPlaceholderNode(editor, node));\n            }\n          }\n        }\n      };\n    };\n\n    var setup$1 = function (editor) {\n      editor.on('preInit', function () {\n        var specialElements = editor.schema.getSpecialElements();\n        global$8.each('video audio iframe object'.split(' '), function (name) {\n          specialElements[name] = new RegExp('</' + name + '[^>]*>', 'gi');\n        });\n        var boolAttrs = editor.schema.getBoolAttrs();\n        global$8.each('webkitallowfullscreen mozallowfullscreen allowfullscreen'.split(' '), function (name) {\n          boolAttrs[name] = {};\n        });\n        editor.parser.addNodeFilter('iframe,video,audio,object,embed,script', placeHolderConverter(editor));\n        editor.serializer.addAttributeFilter('data-mce-object', function (nodes, name) {\n          var i = nodes.length;\n          var node;\n          var realElm;\n          var ai;\n          var attribs;\n          var innerHtml;\n          var innerNode;\n          var realElmName;\n          var className;\n          while (i--) {\n            node = nodes[i];\n            if (!node.parent) {\n              continue;\n            }\n            realElmName = node.attr(name);\n            realElm = new global$2(realElmName, 1);\n            if (realElmName !== 'audio' && realElmName !== 'script') {\n              className = node.attr('class');\n              if (className && className.indexOf('mce-preview-object') !== -1) {\n                realElm.attr({\n                  width: node.firstChild.attr('width'),\n                  height: node.firstChild.attr('height')\n                });\n              } else {\n                realElm.attr({\n                  width: node.attr('width'),\n                  height: node.attr('height')\n                });\n              }\n            }\n            realElm.attr({ style: node.attr('style') });\n            attribs = node.attributes;\n            ai = attribs.length;\n            while (ai--) {\n              var attrName = attribs[ai].name;\n              if (attrName.indexOf('data-mce-p-') === 0) {\n                realElm.attr(attrName.substr(11), attribs[ai].value);\n              }\n            }\n            if (realElmName === 'script') {\n              realElm.attr('type', 'text/javascript');\n            }\n            innerHtml = node.attr('data-mce-html');\n            if (innerHtml) {\n              innerNode = new global$2('#text', 3);\n              innerNode.raw = true;\n              innerNode.value = sanitize(editor, unescape(innerHtml));\n              realElm.append(innerNode);\n            }\n            node.replace(realElm);\n          }\n        });\n      });\n      editor.on('SetContent', function () {\n        editor.$('span.mce-preview-object').each(function (index, elm) {\n          var $elm = editor.$(elm);\n          if ($elm.find('span.mce-shim').length === 0) {\n            $elm.append('<span class=\"mce-shim\"></span>');\n          }\n        });\n      });\n    };\n\n    var setup = function (editor) {\n      editor.on('ResolveName', function (e) {\n        var name;\n        if (e.target.nodeType === 1 && (name = e.target.getAttribute('data-mce-object'))) {\n          e.name = name;\n        }\n      });\n    };\n\n    var register = function (editor) {\n      var onAction = function () {\n        return editor.execCommand('mceMedia');\n      };\n      editor.ui.registry.addToggleButton('media', {\n        tooltip: 'Insert/edit media',\n        icon: 'embed',\n        onAction: onAction,\n        onSetup: function (buttonApi) {\n          var selection = editor.selection;\n          buttonApi.setActive(isMediaElement(selection.getNode()));\n          return selection.selectorChangedWithUnbind('img[data-mce-object],span[data-mce-object],div[data-ephox-embed-iri]', buttonApi.setActive).unbind;\n        }\n      });\n      editor.ui.registry.addMenuItem('media', {\n        icon: 'embed',\n        text: 'Media...',\n        onAction: onAction\n      });\n    };\n\n    function Plugin () {\n      global$9.add('media', function (editor) {\n        register$1(editor);\n        register(editor);\n        setup(editor);\n        setup$1(editor);\n        setup$2(editor);\n        return get(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"media\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/media')\n//   ES2015:\n//     import 'tinymce/plugins/media'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/media/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,WAAW;AAAA;AAAA;AAG7B,UAAI,WAAW,OAAO;AACtB,UAAI,WAAW,OAAO;AACtB,UAAI,UAAU,OAAO;AACrB,UAAI,aAAa,SAAU,GAAG;AAC5B,eAAO,MAAM,QAAQ,MAAM;AAAA;AAE7B,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,CAAC,WAAW;AAAA;AAGrB,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,aAAa,MAAM,UAAU;AACjC,UAAI,SAAS,SAAU,IAAI,GAAG;AAC5B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,KAAK;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;AAAA;AAE3E,qBAAW,MAAM,GAAG,GAAG;AAAA;AAEzB,eAAO;AAAA;AAGT,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,QAAQ;AACZ,YAAI,OAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,kBAAQ;AAAA;AAEV,eAAO;AAAA,UACL,KAAK;AAAA,UACL;AAAA;AAAA;AAIJ,UAAI,OAAO,OAAO;AAClB,UAAI,iBAAiB,OAAO;AAC5B,UAAI,OAAO,SAAU,KAAK,GAAG;AAC3B,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,IAAI;AACZ,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,QAAQ,SAAU,KAAK,KAAK;AAC9B,eAAO,IAAI,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,SAAS;AAAA;AAE5D,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAGlC,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,2BAA2B,SAAU,QAAQ;AAC/C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,2BAA2B,SAAU,QAAQ;AAC/C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS,qBAAqB;AAAA;AAE9C,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,SAAS,qBAAqB;AAAA;AAE9C,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,SAAS,oBAAoB;AAAA;AAE7C,UAAI,YAAY,SAAU,QAAQ;AAChC,eAAO,OAAO,SAAS,gBAAgB;AAAA;AAEzC,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS,oBAAoB;AAAA;AAG7C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,sBAAsB,SAAU,UAAU,KAAK;AACjD,YAAI,UAAU;AACZ,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAI,IAAI,QAAQ,SAAS,GAAG,YAAY,IAAI;AAC1C,qBAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAMxB,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAU,OAAO;AAC5B,eAAO,MAAM,QAAQ,OAAO;AAAA;AAE9B,UAAI,oBAAoB,SAAU,OAAO;AACvC,YAAI,QAAQ,MAAM,IAAI;AACtB,YAAI,SAAS,QAAQ,MAAM,WAAW,SAAS;AAC/C,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,MAAM,IAAI;AAAA,UAClB,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,OAAO,MAAM,QAAQ,aAAa,IAAI,QAAQ,MAAM;AAAA,UACpD,QAAQ,MAAM,QAAQ,cAAc,IAAI,QAAQ,MAAM;AAAA;AAAA;AAG1D,UAAI,aAAa,SAAU,UAAU,MAAM;AACzC,YAAI,eAAe,KAAK;AACxB,YAAI,OAAO;AACX,iBAAS;AAAA,UACP,UAAU;AAAA,UACV,4BAA4B;AAAA,UAC5B,OAAO,SAAU,MAAM,OAAO;AAC5B,gBAAI,aAAa;AAAO;AAAA,qBAAW,IAAI,MAAM,KAAK,yBAAyB;AACzE,2BAAa,IAAI;AACjB,qBAAO,kBAAkB;AAAA,mBACpB;AACL,kBAAI,CAAC,KAAK,UAAU,SAAS,SAAS;AACpC,qBAAK,SAAS,MAAM,IAAI;AAAA;AAE1B,kBAAI,SAAS,YAAY,SAAS,YAAY,SAAS,WAAW,SAAS,WAAW,SAAS,SAAS;AACtG,oBAAI,CAAC,KAAK,MAAM;AACd,uBAAK,OAAO;AAAA;AAEd,uBAAO,SAAS,OAAO,MAAM,KAAK;AAAA;AAEpC,kBAAI,SAAS,UAAU;AACrB,oBAAI,cAAc,oBAAoB,UAAU,MAAM,IAAI;AAC1D,oBAAI,CAAC,aAAa;AAChB;AAAA;AAEF,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,QAAQ,MAAM,IAAI;AAAA,kBAClB,OAAO,OAAO,YAAY;AAAA,kBAC1B,QAAQ,OAAO,YAAY;AAAA;AAAA;AAG/B,kBAAI,SAAS,UAAU;AACrB,oBAAI,CAAC,KAAK,QAAQ;AAChB,uBAAK,SAAS,MAAM,IAAI;AAAA,2BACf,CAAC,KAAK,WAAW;AAC1B,uBAAK,YAAY,MAAM,IAAI;AAAA;AAAA;AAG/B,kBAAI,SAAS,SAAS,CAAC,KAAK,QAAQ;AAClC,qBAAK,SAAS,MAAM,IAAI;AAAA;AAAA;AAAA;AAAA,WAI7B,MAAM;AACT,aAAK,SAAS,KAAK,UAAU,KAAK,OAAO,KAAK;AAC9C,aAAK,YAAY,KAAK,aAAa;AACnC,aAAK,SAAS,KAAK,UAAU;AAC7B,eAAO;AAAA;AAGT,UAAI,QAAQ,SAAU,KAAK;AACzB,YAAI,QAAQ;AAAA,UACV,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM;AAAA,UACN,KAAK;AAAA,UACL,KAAK;AAAA;AAEP,YAAI,UAAU,IAAI,cAAc,MAAM,KAAK;AAC3C,YAAI,OAAO,MAAM;AACjB,eAAO,OAAO,OAAO;AAAA;AAGvB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,MAAM,SAAS;AACnB,UAAI,QAAQ,SAAU,OAAO;AAC3B,eAAO,YAAY,KAAK,SAAS,QAAQ,OAAO;AAAA;AAElD,UAAI,gBAAgB,SAAU,OAAO,cAAc;AACjD,aAAK,cAAc,SAAU,KAAK,MAAM;AACtC,cAAI,QAAQ,KAAK;AACjB,cAAI,MAAM,IAAI,OAAO;AACnB,gBAAI,IAAI,MAAM;AACd,mBAAO,KAAK;AACV,kBAAI,OAAO,MAAM;AACjB,kBAAI,KAAK,SAAS,MAAM;AACtB,oBAAI,OAAO;AACT,wBAAM,IAAI,QAAQ;AAClB,uBAAK,QAAQ;AAAA,uBACR;AACL,yBAAO,MAAM,IAAI;AACjB,wBAAM,OAAO,GAAG;AAAA;AAAA;AAAA;AAAA,qBAIb,OAAO;AAChB,kBAAM,KAAK;AAAA,cACT;AAAA,cACA;AAAA;AAEF,kBAAM,IAAI,QAAQ;AAAA;AAAA;AAAA;AAIxB,UAAI,mBAAmB,SAAU,MAAM,OAAO;AAC5C,YAAI,QAAQ,MAAM,IAAI;AACtB,YAAI,WAAW,QAAQ,IAAI,WAAW,SAAS;AAC/C,iBAAS,eAAe,MAAM,KAAK;AACnC,iBAAS,gBAAgB,MAAM,KAAK;AACpC,sBAAc,OAAO,EAAE,OAAO,IAAI,eAAe;AAAA;AAEnD,UAAI,UAAU;AAAA,QACZ;AAAA,QACA;AAAA;AAEF,UAAI,aAAa,SAAU,MAAM,MAAM,WAAW;AAChD,YAAI,SAAS;AACb,YAAI,eAAe,KAAK;AACxB,YAAI,cAAc;AAClB,YAAI;AACJ,iBAAS;AAAA,UACP,UAAU;AAAA,UACV,4BAA4B;AAAA,UAC5B,SAAS,SAAU,MAAM;AACvB,mBAAO,QAAQ;AAAA;AAAA,UAEjB,OAAO,SAAU,MAAM;AACrB,mBAAO,MAAM;AAAA;AAAA,UAEf,MAAM,SAAU,MAAM,KAAK;AACzB,mBAAO,KAAK,MAAM;AAAA;AAAA,UAEpB,OAAO,SAAU,MAAM,OAAO,OAAO;AACnC,gBAAI,aAAa;AAAO;AAAA,qBAAW,IAAI,MAAM,KAAK,yBAAyB;AACzE,2BAAa,IAAI;AACjB,+BAAiB,MAAM;AAAA,mBAClB;AACL,sBAAQ;AAAA,qBACH;AAAA,qBACA;AAAA,qBACA;AAAA,qBACA;AAAA,qBACA;AACH,sBAAI,KAAK,WAAW,UAAa,KAAK,UAAU,QAAW;AACzD,kCAAc,OAAO;AAAA,sBACnB,OAAO,KAAK;AAAA,sBACZ,QAAQ,KAAK;AAAA;AAAA;AAGjB;AAAA;AAEF,kBAAI,WAAW;AACb,wBAAQ;AAAA,uBACH;AACH,kCAAc,OAAO;AAAA,sBACnB,QAAQ,KAAK;AAAA,sBACb,KAAK;AAAA;AAEP,wBAAI,KAAK,WAAW;AAClB,oCAAc,OAAO,EAAE,KAAK;AAAA;AAE9B;AAAA,uBACG;AACH,kCAAc,OAAO,EAAE,KAAK,KAAK;AACjC;AAAA,uBACG;AACH,wBAAI,cAAc,GAAG;AACnB,oCAAc,OAAO;AAAA,wBACnB,KAAK,KAAK,QAAQ;AAAA,wBAClB,MAAM,KAAK,QAAQ,eAAe;AAAA;AAEpC,0BAAI,CAAC,KAAK,QAAQ,eAAe;AAC/B;AAAA;AAAA;AAGJ;AACA;AAAA,uBACG;AACH,wBAAI,CAAC,KAAK,QAAQ;AAChB;AAAA;AAEF,+BAAW;AACX;AAAA;AAAA;AAAA;AAIN,mBAAO,MAAM,MAAM,OAAO;AAAA;AAAA,UAE5B,KAAK,SAAU,MAAM;AACnB,gBAAI,CAAC,aAAa,OAAO;AACvB,kBAAI,SAAS,WAAW,WAAW;AACjC,yBAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACtC,sBAAI,KAAK,QAAQ,SAAS;AACxB,wBAAI,QAAQ;AACZ,0BAAM,MAAM;AACZ,wBAAI,eAAe,OAAO;AACxB,oCAAc,OAAO;AAAA,wBACnB,KAAK,KAAK,QAAQ;AAAA,wBAClB,MAAM,KAAK,QAAQ,SAAS;AAAA;AAE9B,6BAAO,MAAM,UAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAKtC,kBAAI,KAAK,UAAU,SAAS,YAAY,aAAa,CAAC,UAAU;AAC9D,oBAAI,WAAW;AACf,yBAAS,MAAM;AACf,8BAAc,UAAU;AAAA,kBACtB,KAAK,KAAK;AAAA,kBACV,OAAO,KAAK;AAAA,kBACZ,QAAQ,KAAK;AAAA;AAEf,uBAAO,MAAM,OAAO,UAAU;AAAA;AAAA;AAGlC,mBAAO,IAAI;AAAA;AAAA,WAEZ,SAAS,KAAK,MAAM;AACvB,eAAO,OAAO;AAAA;AAGhB,UAAI,cAAc;AAAA,QAChB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,iBAAiB;AAAA;AAAA,QAEnB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,iBAAiB;AAAA;AAAA,QAEnB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,iBAAiB;AAAA;AAAA,QAEnB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,iBAAiB;AAAA;AAAA,QAEnB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,iBAAiB;AAAA;AAAA,QAEnB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,iBAAiB;AAAA;AAAA,QAEnB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,iBAAiB;AAAA;AAAA,QAEnB;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,iBAAiB;AAAA;AAAA;AAGrB,UAAI,cAAc,SAAU,KAAK;AAC/B,YAAI,kBAAkB,IAAI,MAAM;AAChC,YAAI,mBAAmB,gBAAgB,SAAS,GAAG;AACjD,iBAAO,gBAAgB,OAAO,SAAS,aAAa,gBAAgB;AAAA,eAC/D;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,SAAS,KAAK;AACnC,YAAI,WAAW,YAAY;AAC3B,YAAI,QAAQ,QAAQ,MAAM,KAAK;AAC/B,YAAI,SAAS,WAAW,QAAQ;AAChC,YAAI,UAAU,SAAU,IAAG;AACzB,mBAAS,OAAO,QAAQ,MAAM,IAAG,WAAY;AAC3C,mBAAO,MAAM,MAAK,MAAM,MAAK;AAAA;AAAA;AAGjC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAQ;AAAA;AAEV,eAAO,OAAO,QAAQ,OAAO;AAAA;AAE/B,UAAI,eAAe,SAAU,KAAK;AAChC,YAAI,WAAW,YAAY,OAAO,SAAU,SAAS;AACnD,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,YAAI,SAAS,SAAS,GAAG;AACvB,iBAAO,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,KAAK,OAAO,SAAS,IAAI;AAAA,eAC9D;AACL,iBAAO;AAAA;AAAA;AAIX,UAAI,gBAAgB,SAAU,MAAM;AAClC,YAAI,kBAAkB,KAAK,kBAAkB,yBAAyB;AACtE,eAAO,kBAAkB,KAAK,SAAS,cAAc,KAAK,QAAQ,eAAe,KAAK,SAAS,MAAM,kBAAkB;AAAA;AAEzH,UAAI,eAAe,SAAU,MAAM;AACjC,YAAI,OAAO,mBAAmB,KAAK,SAAS,cAAc,KAAK,QAAQ,eAAe,KAAK,SAAS;AACpG,YAAI,KAAK,QAAQ;AACf,kBAAQ,eAAe,KAAK,SAAS,cAAc,KAAK,QAAQ,eAAe,KAAK,SAAS;AAAA;AAE/F,gBAAQ;AACR,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,MAAM,uBAAuB;AACxD,YAAI,uBAAuB;AACzB,iBAAO,sBAAsB;AAAA,eACxB;AACL,iBAAO,qCAAqC,KAAK,SAAS,OAAQ,MAAK,YAAY,oBAAoB,KAAK,YAAY,MAAO,MAAK,gBAAgB,YAAY,KAAK,gBAAgB,MAAM,MAAM,UAAU,MAAM;AAAA;AAAA;AAGrN,UAAI,eAAe,SAAU,MAAM,uBAAuB;AACxD,YAAI,uBAAuB;AACzB,iBAAO,sBAAsB;AAAA,eACxB;AACL,iBAAO,mBAAmB,KAAK,QAAQ,eAAe,KAAK,SAAS,MAAO,MAAK,SAAS,cAAc,KAAK,SAAS,MAAM,MAAM,yCAA8C,KAAK,SAAS,MAAO,MAAK,aAAa,YAAY,KAAK,aAAa,MAAM,MAAM,UAAW,MAAK,YAAY,kBAAkB,KAAK,YAAY,MAAO,MAAK,gBAAgB,YAAY,KAAK,gBAAgB,MAAM,MAAM,UAAU,MAAM;AAAA;AAAA;AAG5Z,UAAI,gBAAgB,SAAU,MAAM;AAClC,eAAO,kBAAkB,KAAK,SAAS;AAAA;AAEzC,UAAI,aAAa,SAAU,QAAQ,QAAQ;AACzC,YAAI,OAAO,SAAS,OAAO,IAAI;AAC/B,YAAI,CAAC,KAAK,QAAQ;AAChB,mBAAS,OAAO,MAAM,WAAW,WAAW,SAAS,KAAK;AAC1D,cAAI,CAAC,KAAK,QAAQ;AAChB,mBAAO;AAAA;AAAA;AAGX,YAAI,CAAC,KAAK,WAAW;AACnB,eAAK,YAAY;AAAA;AAEnB,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,SAAS;AAAA;AAEhB,aAAK,SAAS,OAAO,WAAW,KAAK,QAAQ;AAC7C,aAAK,YAAY,OAAO,WAAW,KAAK,WAAW;AACnD,aAAK,aAAa,MAAM,KAAK;AAC7B,aAAK,gBAAgB,MAAM,KAAK;AAChC,aAAK,SAAS,OAAO,WAAW,KAAK,QAAQ;AAC7C,YAAI,UAAU,aAAa,KAAK;AAChC,YAAI,SAAS;AACX,eAAK,SAAS,QAAQ;AACtB,eAAK,OAAO,QAAQ;AACpB,eAAK,kBAAkB,QAAQ;AAC/B,eAAK,QAAQ,KAAK,SAAS,OAAO,QAAQ;AAC1C,eAAK,SAAS,KAAK,UAAU,OAAO,QAAQ;AAAA;AAE9C,YAAI,KAAK,OAAO;AACd,iBAAO,WAAW,KAAK,OAAO,MAAM;AAAA,eAC/B;AACL,cAAI,cAAc,oBAAoB,WAAW,SAAS,KAAK;AAC/D,cAAI,aAAa;AACf,iBAAK,OAAO;AACZ,iBAAK,QAAQ,OAAO,YAAY;AAChC,iBAAK,SAAS,OAAO,YAAY;AAAA;AAEnC,cAAI,wBAAwB,yBAAyB;AACrD,cAAI,wBAAwB,yBAAyB;AACrD,eAAK,QAAQ,KAAK,SAAS;AAC3B,eAAK,SAAS,KAAK,UAAU;AAC7B,mBAAS,KAAK,MAAM,SAAU,OAAO,KAAK;AACxC,iBAAK,OAAO,OAAO,IAAI,OAAO,KAAK;AAAA;AAErC,cAAI,KAAK,SAAS,UAAU;AAC1B,mBAAO,cAAc;AAAA,qBACZ,KAAK,eAAe,iCAAiC;AAC9D,mBAAO,aAAa;AAAA,qBACX,KAAK,WAAW,QAAQ,aAAa,IAAI;AAClD,mBAAO,aAAa,MAAM;AAAA,qBACjB,KAAK,SAAS,UAAU;AACjC,mBAAO,cAAc;AAAA,iBAChB;AACL,mBAAO,aAAa,MAAM;AAAA;AAAA;AAAA;AAKhC,UAAI,iBAAiB,SAAU,SAAS;AACtC,eAAO,QAAQ,aAAa,sBAAsB,QAAQ,aAAa;AAAA;AAEzE,UAAI,UAAU,SAAU,QAAQ;AAC9B,eAAO,GAAG,wBAAwB,WAAY;AAC5C,cAAI,eAAe,OAAO,UAAU;AACpC,cAAI,gBAAgB,OAAO,IAAI,SAAS,cAAc,uBAAuB;AAC3E,gBAAI,OAAO,IAAI,UAAU,cAAc,sBAAsB;AAC3D,2BAAa,aAAa,qBAAqB;AAAA;AAAA;AAAA;AAIrD,eAAO,GAAG,kBAAkB,SAAU,GAAG;AACvC,cAAI,aAAa,EAAE,OAAO,aAAa;AACvC,cAAI,eAAe,UAAU;AAC3B,cAAE;AAAA;AAAA;AAGN,eAAO,GAAG,iBAAiB,SAAU,GAAG;AACtC,cAAI,SAAS,EAAE;AACf,cAAI,OAAO,aAAa,oBAAoB;AAC1C,gBAAI,OAAO,OAAO,aAAa;AAC/B,gBAAI,MAAM;AACR,qBAAO,SAAS;AAChB,qBAAO,aAAa,iBAAiB,OAAO,WAAW,MAAM;AAAA,gBAC3D,OAAO,OAAO,EAAE;AAAA,gBAChB,QAAQ,OAAO,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAO3B,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,QAAQ;AACZ,UAAI,eAAe,SAAU,MAAM,aAAY,SAAS;AACtD,eAAO,IAAI,SAAS,SAAU,KAAK,KAAK;AACtC,cAAI,iBAAiB,SAAU,UAAU;AACvC,gBAAI,SAAS,MAAM;AACjB,oBAAM,KAAK,UAAU;AAAA;AAEvB,mBAAO,IAAI;AAAA,cACT,KAAK,KAAK;AAAA,cACV,MAAM,SAAS,OAAO,SAAS,OAAO,YAAW;AAAA;AAAA;AAGrD,cAAI,MAAM,KAAK,SAAS;AACtB,2BAAe,MAAM,KAAK;AAAA,iBACrB;AACL,oBAAQ,EAAE,KAAK,KAAK,UAAU,gBAAgB;AAAA;AAAA;AAAA;AAIpD,UAAI,iBAAiB,SAAU,MAAM,aAAY;AAC/C,eAAO,SAAS,QAAQ;AAAA,UACtB,MAAM,YAAW;AAAA,UACjB,KAAK,KAAK;AAAA;AAAA;AAGd,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,SAAU,MAAM;AACrB,iBAAO,WAAW,QAAQ;AAAA;AAAA;AAG9B,UAAI,eAAe,SAAU,QAAQ,MAAM;AACzC,YAAI,eAAe,eAAe;AAClC,eAAO,eAAe,aAAa,MAAM,WAAW,SAAS,gBAAgB,eAAe,MAAM,WAAW;AAAA;AAE/G,UAAI,WAAW,SAAU,KAAK;AAC5B,eAAO,IAAI,OAAO;AAAA;AAGpB,UAAI,cAAc,SAAU,aAAa,MAAM;AAC7C,eAAO,MAAM,MAAM,aAAa,KAAK,SAAU,UAAU;AACvD,iBAAO,MAAM,UAAU;AAAA;AAAA;AAG3B,UAAI,WAAW,SAAU,MAAM,UAAU,aAAa;AACpD,eAAO,SAAU,MAAM;AACrB,cAAI;AACJ,cAAI,cAAc,WAAY;AAC5B,mBAAO,MAAM,MAAM;AAAA;AAErB,cAAI,kBAAkB,WAAY;AAChC,mBAAO,MAAM,UAAU;AAAA;AAEzB,cAAI,mBAAmB,SAAU,GAAG;AAClC,mBAAO,MAAM,GAAG,SAAS,KAAK,SAAU,GAAG;AACzC,qBAAO,EAAE,SAAS,IAAI,SAAS,KAAK,KAAK,SAAS;AAAA;AAAA;AAGtD,cAAI,oBAAoB,WAAY;AAClC,mBAAO,cAAc,KAAK,SAAU,OAAO;AACzC,qBAAO,SAAS,SAAS,iBAAiB,OAAO,QAAQ,mBAAmB,kBAAkB,QAAQ,WAAY;AAChH,uBAAO,SAAS,KAAK;AAAA;AAAA;AAAA;AAI3B,cAAI,mBAAmB,WAAY;AACjC,mBAAO,kBAAkB,QAAQ,WAAY;AAC3C,qBAAO,cAAc,KAAK,SAAU,OAAO;AACzC,uBAAO,SAAS,SAAS,iBAAiB,SAAS,SAAS,KAAK;AAAA;AAAA;AAAA;AAIvE,iBAAO,KAAK,IAAI,GAAG,QAAS,UAAS,cAAc,sBAAsB,oBAAoB,MAAM,KAAK;AAAA;AAAA;AAG5G,UAAI,gBAAgB,SAAU,MAAM,UAAU;AAC5C,YAAI,aAAa;AACjB,cAAM,MAAM,cAAc,KAAK,SAAU,MAAM;AAC7C,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,aACC,SAAU,MAAM;AACjB,kBAAM,UAAU,MAAM,QAAQ,WAAY;AACxC,qBAAO,MAAM,MAAM;AAAA,eAClB,KAAK,SAAU,OAAO;AACvB,qBAAO,WAAW,QAAQ;AAAA;AAAA;AAAA;AAIhC,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,MAAM,aAAa;AACxC,YAAI,WAAW,cAAc,YAAY,aAAa,MAAM,MAAM,MAAM;AACxE,YAAI,OAAM,SAAS,MAAM,UAAU;AACnC,eAAO,SAAS,SAAS,SAAS,SAAS,SAAS,IAAI,KAAI,YAAY,KAAI,eAAe,KAAI,YAAY,KAAI,WAAW,cAAc,MAAM;AAAA;AAEhJ,UAAI,OAAO,SAAU,MAAM;AACzB,YAAI,UAAU,SAAS,SAAS,IAAI,OAAO;AAAA,UACzC,QAAQ,EAAE,OAAO,MAAM,MAAM,UAAU,MAAM;AAAA,UAC7C,WAAW,EAAE,OAAO,MAAM,MAAM,aAAa,MAAM;AAAA,UACnD,QAAQ,EAAE,OAAO,MAAM,MAAM,UAAU,MAAM;AAAA;AAE/C,eAAO;AAAA,UACL;AAAA,UACA;AAAA,WACC,SAAU,MAAM;AACjB,gBAAM,MAAM,MAAM,KAAK,SAAU,OAAO;AACtC,gBAAI,aAAa,QAAQ,cAAc;AACvC,uBAAW,QAAQ;AACnB,oBAAQ,aAAa;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,SAAU,OAAO;AACtB,cAAI,eAAe,SAAS,MAAM,MAAM,gCAAgC,MAAM,MAAM;AACpF,iBAAO,oBAAoB,KAAK;AAAA,YAC9B,MAAM;AAAA,YACN,MAAM;AAAA;AAAA;AAAA;AAIZ,UAAI,gBAAgB,SAAU,QAAQ,cAAc;AAClD,eAAO,WAAW,WAAW,SAAS;AAAA;AAExC,UAAI,gBAAgB,SAAU,QAAQ;AACpC,YAAI,UAAU,OAAO,UAAU;AAC/B,YAAI,UAAU,eAAe,WAAW,OAAO,WAAW,UAAU,SAAS,EAAE,WAAW,UAAU;AACpG,eAAO,SAAS,EAAE,OAAO,WAAW,WAAW,WAAW,SAAS;AAAA;AAErE,UAAI,eAAe,SAAU,KAAK,QAAQ;AACxC,eAAO,SAAU,UAAU;AACzB,cAAI,SAAS,SAAS,QAAQ,SAAS,IAAI,OAAO,SAAS,GAAG;AAC5D,gBAAI,OAAO,SAAS;AACpB,gBAAI,cAAc,cAAc,QAAQ;AACxC,gBAAI,SAAS,SAAS,SAAS,IAAI,cAAc;AAAA,cAC/C,QAAQ,SAAS;AAAA,cACjB,OAAO;AAAA;AAET,gBAAI,QAAQ,KAAK;AAAA;AAAA;AAAA;AAIvB,UAAI,oBAAoB,SAAU,QAAQ,eAAe;AACvD,YAAI,eAAe,OAAO,IAAI,OAAO;AACrC,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,mBAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,gBAAI,cAAc,OAAO,aAAa,IAAI;AACxC,2BAAa,OAAO,GAAG;AAAA;AAAA;AAAA;AAI7B,eAAO,UAAU,OAAO,aAAa;AAAA;AAEvC,UAAI,eAAe,SAAU,QAAQ,MAAM;AACzC,YAAI,gBAAgB,OAAO,IAAI,OAAO;AACtC,eAAO,cAAc;AACrB,0BAAkB,QAAQ;AAC1B,eAAO;AAAA;AAET,UAAI,aAAa,SAAU,UAAU,SAAS,QAAQ;AACpD,gBAAQ,QAAQ,WAAW,QAAQ,OAAO;AAC1C,YAAI,QAAQ,SAAU,UAAS,WAAW,QAAQ,UAAU,SAAS,QAAQ,UAAU;AACrF,uBAAa,QAAQ,QAAQ;AAAA,eACxB;AACL,uBAAa,QAAQ,SAAS,KAAK,SAAU,UAAU;AACrD,yBAAa,QAAQ,SAAS;AAAA,aAC7B,MAAM,YAAY;AAAA;AAAA;AAGzB,UAAI,aAAa,SAAU,QAAQ;AACjC,YAAI,aAAa,cAAc;AAC/B,YAAI,cAAc,KAAK;AACvB,YAAI,cAAc,KAAK;AACvB,YAAI,eAAe,SAAU,UAAU,KAAK;AAC1C,cAAI,cAAc,OAAO,IAAI,WAAW;AACxC,cAAI,SAAS,WAAW,YAAY,QAAQ;AAC1C,yBAAa,KAAK,QAAQ;AAAA,cACxB,KAAK,YAAY;AAAA,cACjB,MAAM;AAAA;AAER,yBAAa,QAAQ,aAAa,KAAK,aAAa,KAAK,SAAS,MAAM,YAAY;AAAA;AAAA;AAGxF,YAAI,cAAc,SAAU,KAAK;AAC/B,cAAI,OAAO,OAAO,IAAI;AACtB,cAAI,gBAAgB,cAAc,QAAQ,KAAK;AAC/C,cAAI,QAAQ,KAAK;AAAA;AAEnB,YAAI,eAAe,SAAU,KAAK,aAAa;AAC7C,cAAI,OAAO,OAAO,IAAI,WAAW;AACjC,cAAI,QAAQ,WAAW,QAAQ;AAC/B,cAAI,QAAQ,KAAK,SAAS,SAAS,IAAI,OAAO,EAAE;AAAA;AAElD,YAAI,aAAa,CAAC;AAAA,UACd,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA;AAEX,YAAI,YAAY,CAAC,cAAc,UAAU,KAAK,CAAC;AAAA,UAC3C,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA;AAEf,YAAI,aAAa;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,QAAQ;AAAA,YACb;AAAA,YACA;AAAA;AAAA;AAGJ,YAAI,gBAAgB;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA;AAET,YAAI,WAAW;AAAA,UACb,OAAO;AAAA,UACP,OAAO,CAAC;AAAA;AAEV,YAAI,oBAAoB;AACxB,YAAI,aAAa,SAAS;AACxB,4BAAkB,KAAK;AAAA,YACrB,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,YACV,OAAO;AAAA;AAAA;AAGX,YAAI,UAAU,SAAS;AACrB,4BAAkB,KAAK;AAAA,YACrB,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,YACV,OAAO;AAAA;AAAA;AAGX,YAAI,cAAc;AAAA,UAChB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA;AAET,YAAI,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAEF,YAAI,kBAAkB,SAAS,GAAG;AAChC,eAAK,KAAK;AAAA;AAEZ,YAAI,OAAO;AAAA,UACT,MAAM;AAAA,UACN;AAAA;AAEF,YAAI,MAAM,OAAO,cAAc,KAAK;AAAA,UAClC,OAAO;AAAA,UACP,MAAM;AAAA,UACN;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA;AAAA,YAER;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,UAGb,UAAU,SAAU,KAAK;AACvB,gBAAI,cAAc,OAAO,IAAI;AAC7B,uBAAW,YAAY,OAAO,aAAa;AAC3C,gBAAI;AAAA;AAAA,UAEN,UAAU,SAAU,KAAK,QAAQ;AAC/B,oBAAQ,OAAO;AAAA,mBACV;AACH,6BAAa,YAAY,OAAO;AAChC;AAAA,mBACG;AACH,4BAAY;AACZ;AAAA,mBACG;AAAA,mBACA;AAAA,mBACA;AACH,6BAAa,KAAK,OAAO;AACzB;AAAA;AAEF,wBAAY,IAAI,OAAO,IAAI;AAAA;AAAA,UAE7B;AAAA;AAAA;AAIJ,UAAI,MAAM,SAAU,QAAQ;AAC1B,YAAI,eAAe,WAAY;AAC7B,qBAAW;AAAA;AAEb,eAAO,EAAE,YAAY;AAAA;AAGvB,UAAI,aAAa,SAAU,QAAQ;AACjC,YAAI,eAAe,WAAY;AAC7B,qBAAW;AAAA;AAEb,eAAO,WAAW,YAAY;AAAA;AAGhC,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,WAAW,SAAU,QAAQ,MAAM;AACrC,YAAI,iBAAiB,YAAY,OAAO;AACtC,iBAAO;AAAA;AAET,YAAI,SAAS;AACb,YAAI;AACJ,iBAAS;AAAA,UACP,UAAU;AAAA,UACV,4BAA4B;AAAA,UAC5B,SAAS,SAAU,MAAM;AACvB,gBAAI,CAAC,SAAS;AACZ,qBAAO,QAAQ;AAAA;AAAA;AAAA,UAGnB,OAAO,SAAU,MAAM;AACrB,gBAAI,CAAC,SAAS;AACZ,qBAAO,MAAM;AAAA;AAAA;AAAA,UAGjB,MAAM,SAAU,MAAM,KAAK;AACzB,gBAAI,CAAC,SAAS;AACZ,qBAAO,KAAK,MAAM;AAAA;AAAA;AAAA,UAGtB,OAAO,SAAU,MAAM,OAAO,OAAO;AACnC,sBAAU;AACV,gBAAI,SAAS,YAAY,SAAS,cAAc,SAAS,OAAO;AAC9D;AAAA;AAEF,qBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,kBAAI,WAAW,MAAM,GAAG;AACxB,kBAAI,SAAS,QAAQ,UAAU,GAAG;AAChC,uBAAO,MAAM,IAAI;AACjB,sBAAM,OAAO,GAAG;AAAA;AAElB,kBAAI,aAAa,SAAS;AACxB,sBAAM,GAAG,QAAQ,OAAO,IAAI,eAAe,OAAO,IAAI,WAAW,MAAM,GAAG,QAAQ;AAAA;AAAA;AAGtF,mBAAO,MAAM,MAAM,OAAO;AAC1B,sBAAU;AAAA;AAAA,UAEZ,KAAK,SAAU,MAAM;AACnB,gBAAI,SAAS;AACX;AAAA;AAEF,mBAAO,IAAI;AAAA;AAAA,WAEZ,SAAS,KAAK,MAAM;AACvB,eAAO,OAAO;AAAA;AAGhB,UAAI,kBAAkB,SAAU,MAAM;AACpC,YAAI,OAAO,KAAK;AAChB,eAAO,SAAS,YAAY,SAAS,WAAW,SAAS;AAAA;AAE3D,UAAI,eAAe,SAAU,MAAM,QAAQ,WAAW,cAAc;AAClE,YAAI,iBAAiB,QAAQ;AAC3B,yBAAe;AAAA;AAEjB,YAAI,QAAQ,KAAK,KAAK;AACtB,YAAI,cAAc,QAAQ;AACxB,iBAAO;AAAA,mBACE,CAAC,IAAI,QAAQ,YAAY;AAClC,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,gBAAgB,SAAU,MAAM,aAAa,QAAQ;AACvD,YAAI,cAAc,YAAY,SAAS,SAAS,KAAK,SAAS;AAC9D,YAAI,eAAe,cAAc,QAAQ;AACzC,YAAI,iBAAiB,KAAK,SAAS,UAAU,OAAO;AACpD,YAAI,gBAAgB,cAAc,iBAAiB;AACnD,oBAAY,KAAK;AAAA,UACf,OAAO,aAAa,MAAM,QAAQ,SAAS;AAAA,UAC3C,QAAQ,aAAa,MAAM,QAAQ,UAAU;AAAA;AAAA;AAGjD,UAAI,oBAAoB,SAAU,QAAQ,UAAU,aAAa,MAAM;AACrE,YAAI,UAAU,OAAO;AAAA,UACnB,mBAAmB;AAAA,UACnB,UAAU;AAAA,WACT,OAAO,QAAQ,MAAM,MAAM,EAAE,SAAS;AACzC,eAAO,QAAQ,YAAY;AACzB,sBAAY,OAAO,QAAQ;AAAA;AAAA;AAG/B,UAAI,wBAAwB,SAAU,QAAQ,MAAM;AAClD,YAAI,OAAO,KAAK;AAChB,YAAI,cAAc,IAAI,SAAS,OAAO;AACtC,oBAAY,aAAa;AACzB,qCAA6B,QAAQ,MAAM;AAC3C,sBAAc,MAAM,aAAa;AACjC,oBAAY,KAAK;AAAA,UACf,SAAS,KAAK,KAAK;AAAA,UACnB,OAAO,SAAS;AAAA,UAChB,mBAAmB;AAAA,UACnB,SAAS,2BAA2B;AAAA;AAEtC,eAAO;AAAA;AAET,UAAI,oBAAoB,SAAU,QAAQ,MAAM;AAC9C,YAAI,OAAO,KAAK;AAChB,YAAI,iBAAiB,IAAI,SAAS,QAAQ;AAC1C,uBAAe,KAAK;AAAA,UAClB,mBAAmB;AAAA,UACnB,SAAS,KAAK,KAAK;AAAA,UACnB,mBAAmB;AAAA,UACnB,SAAS,mCAAmC;AAAA;AAE9C,qCAA6B,QAAQ,MAAM;AAC3C,YAAI,SAAS,OAAO,IAAI,WAAW,KAAK,KAAK;AAC7C,YAAI,cAAc,IAAI,SAAS,MAAM;AACrC,sBAAc,MAAM,aAAa;AACjC,oBAAY,KAAK;AAAA,UACf,KAAK,KAAK,KAAK;AAAA,UACf,OAAO,KAAK,KAAK;AAAA,UACjB,OAAO,KAAK,KAAK;AAAA;AAEnB,YAAI,SAAS,UAAU;AACrB,sBAAY,KAAK;AAAA,YACf,iBAAiB,KAAK,KAAK;AAAA,YAC3B,aAAa;AAAA;AAAA,eAEV;AACL,cAAI,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAEF,iBAAO,OAAO,SAAU,UAAU;AAChC,wBAAY,KAAK,UAAU,KAAK,KAAK;AAAA;AAEvC,cAAI,gBAAgB,eAAe,KAAK;AACxC,cAAI,cAAc,gBAAgB;AAChC,8BAAkB,QAAQ,MAAM,aAAa,SAAS;AAAA;AAAA;AAG1D,YAAI,WAAW,IAAI,SAAS,QAAQ;AACpC,iBAAS,KAAK,SAAS;AACvB,uBAAe,OAAO;AACtB,uBAAe,OAAO;AACtB,eAAO;AAAA;AAET,UAAI,+BAA+B,SAAU,QAAQ,YAAY,YAAY;AAC3E,YAAI,UAAU,WAAW;AACzB,YAAI,KAAK,QAAQ;AACjB,eAAO,MAAM;AACX,cAAI,WAAW,QAAQ,IAAI;AAC3B,cAAI,YAAY,QAAQ,IAAI;AAC5B,cAAI,aAAa,WAAW,aAAa,YAAY,aAAa,SAAS;AACzE,gBAAI,aAAa,UAAU,aAAa,OAAO;AAC7C,0BAAY,OAAO,WAAW,WAAW;AAAA;AAE3C,uBAAW,KAAK,gBAAgB,UAAU;AAAA;AAAA;AAG9C,YAAI,YAAY,WAAW,cAAc,WAAW,WAAW;AAC/D,YAAI,WAAW;AACb,qBAAW,KAAK,iBAAiB,OAAO,SAAS,QAAQ;AACzD,qBAAW,aAAa;AAAA;AAAA;AAG5B,UAAI,qBAAqB,SAAU,MAAM;AACvC,YAAI,YAAY,KAAK,KAAK;AAC1B,eAAO,aAAa,qBAAqB,KAAK;AAAA;AAEhD,UAAI,uBAAuB,SAAU,MAAM;AACzC,eAAO,OAAO,KAAK,QAAQ;AACzB,cAAI,KAAK,KAAK,2BAA2B,mBAAmB,OAAO;AACjE,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,SAAU,OAAO;AACtB,cAAI,IAAI,MAAM;AACd,cAAI;AACJ,cAAI;AACJ,iBAAO,KAAK;AACV,mBAAO,MAAM;AACb,gBAAI,CAAC,KAAK,QAAQ;AAChB;AAAA;AAEF,gBAAI,KAAK,OAAO,KAAK,oBAAoB;AACvC;AAAA;AAEF,gBAAI,KAAK,SAAS,UAAU;AAC1B,4BAAc,oBAAoB,WAAW,SAAS,KAAK,KAAK;AAChE,kBAAI,CAAC,aAAa;AAChB;AAAA;AAAA;AAGJ,gBAAI,aAAa;AACf,kBAAI,YAAY,OAAO;AACrB,qBAAK,KAAK,SAAS,YAAY,MAAM;AAAA;AAEvC,kBAAI,YAAY,QAAQ;AACtB,qBAAK,KAAK,UAAU,YAAY,OAAO;AAAA;AAAA;AAG3C,gBAAI,gBAAgB,SAAS,cAAc,WAAW,SAAS,SAAS;AACtE,kBAAI,CAAC,qBAAqB,OAAO;AAC/B,qBAAK,QAAQ,kBAAkB,QAAQ;AAAA;AAAA,mBAEpC;AACL,kBAAI,CAAC,qBAAqB,OAAO;AAC/B,qBAAK,QAAQ,sBAAsB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAOrD,UAAI,UAAU,SAAU,QAAQ;AAC9B,eAAO,GAAG,WAAW,WAAY;AAC/B,cAAI,kBAAkB,OAAO,OAAO;AACpC,mBAAS,KAAK,4BAA4B,MAAM,MAAM,SAAU,MAAM;AACpE,4BAAgB,QAAQ,IAAI,OAAO,OAAO,OAAO,UAAU;AAAA;AAE7D,cAAI,YAAY,OAAO,OAAO;AAC9B,mBAAS,KAAK,2DAA2D,MAAM,MAAM,SAAU,MAAM;AACnG,sBAAU,QAAQ;AAAA;AAEpB,iBAAO,OAAO,cAAc,0CAA0C,qBAAqB;AAC3F,iBAAO,WAAW,mBAAmB,mBAAmB,SAAU,OAAO,MAAM;AAC7E,gBAAI,IAAI,MAAM;AACd,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AACJ,mBAAO,KAAK;AACV,qBAAO,MAAM;AACb,kBAAI,CAAC,KAAK,QAAQ;AAChB;AAAA;AAEF,4BAAc,KAAK,KAAK;AACxB,wBAAU,IAAI,SAAS,aAAa;AACpC,kBAAI,gBAAgB,WAAW,gBAAgB,UAAU;AACvD,4BAAY,KAAK,KAAK;AACtB,oBAAI,aAAa,UAAU,QAAQ,0BAA0B,IAAI;AAC/D,0BAAQ,KAAK;AAAA,oBACX,OAAO,KAAK,WAAW,KAAK;AAAA,oBAC5B,QAAQ,KAAK,WAAW,KAAK;AAAA;AAAA,uBAE1B;AACL,0BAAQ,KAAK;AAAA,oBACX,OAAO,KAAK,KAAK;AAAA,oBACjB,QAAQ,KAAK,KAAK;AAAA;AAAA;AAAA;AAIxB,sBAAQ,KAAK,EAAE,OAAO,KAAK,KAAK;AAChC,wBAAU,KAAK;AACf,mBAAK,QAAQ;AACb,qBAAO,MAAM;AACX,oBAAI,WAAW,QAAQ,IAAI;AAC3B,oBAAI,SAAS,QAAQ,mBAAmB,GAAG;AACzC,0BAAQ,KAAK,SAAS,OAAO,KAAK,QAAQ,IAAI;AAAA;AAAA;AAGlD,kBAAI,gBAAgB,UAAU;AAC5B,wBAAQ,KAAK,QAAQ;AAAA;AAEvB,0BAAY,KAAK,KAAK;AACtB,kBAAI,WAAW;AACb,4BAAY,IAAI,SAAS,SAAS;AAClC,0BAAU,MAAM;AAChB,0BAAU,QAAQ,SAAS,QAAQ,SAAS;AAC5C,wBAAQ,OAAO;AAAA;AAEjB,mBAAK,QAAQ;AAAA;AAAA;AAAA;AAInB,eAAO,GAAG,cAAc,WAAY;AAClC,iBAAO,EAAE,2BAA2B,KAAK,SAAU,OAAO,KAAK;AAC7D,gBAAI,OAAO,OAAO,EAAE;AACpB,gBAAI,KAAK,KAAK,iBAAiB,WAAW,GAAG;AAC3C,mBAAK,OAAO;AAAA;AAAA;AAAA;AAAA;AAMpB,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,GAAG,eAAe,SAAU,GAAG;AACpC,cAAI;AACJ,cAAI,EAAE,OAAO,aAAa,KAAM,QAAO,EAAE,OAAO,aAAa,qBAAqB;AAChF,cAAE,OAAO;AAAA;AAAA;AAAA;AAKf,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,gBAAgB,SAAS;AAAA,UAC1C,SAAS;AAAA,UACT,MAAM;AAAA,UACN;AAAA,UACA,SAAS,SAAU,WAAW;AAC5B,gBAAI,YAAY,OAAO;AACvB,sBAAU,UAAU,eAAe,UAAU;AAC7C,mBAAO,UAAU,0BAA0B,wEAAwE,UAAU,WAAW;AAAA;AAAA;AAG5I,eAAO,GAAG,SAAS,YAAY,SAAS;AAAA,UACtC,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA;AAAA;AAIJ,wBAAmB;AACjB,iBAAS,IAAI,SAAS,SAAU,QAAQ;AACtC,qBAAW;AACX,mBAAS;AACT,gBAAM;AACN,kBAAQ;AACR,kBAAQ;AACR,iBAAO,IAAI;AAAA;AAAA;AAIf;AAAA;AAAA;AAAA;;;AC50CJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,gCAAQ;", "names": []}