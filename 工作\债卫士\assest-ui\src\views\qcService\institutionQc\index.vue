<template>
    <div class="app-container">
        <el-form inline label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef">
            <el-form-item prop="cname" label="机构名称">
                <el-select v-model="queryParams.cname" placeholder="请选择机构名称" clearable multiple collapse-tags
                    collapse-tags-tooltip filterable :reserve-keyword="false" style="width: 240px">
                    <el-option-group v-for="group in teamOption" :key="group.label" :label="group.label">
                        <el-option v-for="item in group.children" :key="item.id" :label="item.label"
                            :value="item.label" />
                    </el-option-group>
                </el-select>
            </el-form-item>
            <el-form-item prop="id" label="机构ID">
                <el-input v-model="queryParams.id" style="width:240px" placeholder="请输入机构ID" />
            </el-form-item>
            <el-form-item prop="qualityStatus" label="状态">
                <el-select v-model="queryParams.qualityStatus" style="width:240px;" placeholder="请选择状态">
                    <el-option v-for="(v, i) in switchStatusEnum" :key="v" :value="i" :label="v" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button :loading="loading" @click="antiShake(resetQuery)">重置</el-button>
            <el-button :loading="loading" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
        </div>
        <div class="operation-revealing-area mb20">
            <el-button :loading="loading" :disabled="!selectedArr.length" type="danger"
                @click="handleSet()">质检设置</el-button>
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <div class="table-box">
            <el-table :data="dataList" v-loading="loading" @selection-change="handleSelectionChange"
                @sort-change="handleSortChange">
                <el-table-column type="selection" width="44px" :selectable="selectable" align="right" />
                <el-table-column v-if="columns[0].visible" align="center" sortable="id" prop="id" label="机构ID"
                    width="120" />
                <el-table-column v-if="columns[1].visible" align="center" prop="cname" label="机构名称" />
                <el-table-column v-if="columns[2].visible" align="center" prop="qualityRemark" label="备注" />
                <el-table-column v-if="columns[3].visible" align="center" prop="qualityBy" label="质检员" />
                <el-table-column v-if="columns[4].visible" align="center" prop="qualityStatus"
                    :formatter="row => switchStatusEnum[row.qualityStatus]" label="状态" width="120" />
                <el-table-column v-if="columns[5].visible" align="center" prop="updateBy" label="更新人" width="120" />
                <el-table-column v-if="columns[6].visible" align="center" prop="updateTime" label="更新时间" width="160" />
                <el-table-column fixed="right" width="140px" label="操作">
                    <template #default="{ row }">
                        <div>
                            <el-button type="text" @click="edit(row)">编辑</el-button>
                            <el-button type="text" @click="handleSet(row)">质检设置</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
        <setInfo :getList="getList" ref="setInfoRef" />
        <updateInfo :getList="getList" ref="updateInfoRef" />
    </div>
</template>

<script setup>
import setInfo from './dialog/setInfo';
import updateInfo from './dialog/updateInfo';
import { getTeamTree } from "@/api/team/team";
import { switchStatusEnum } from '@/utils/enum';
import { teamQcListApi } from '@/api/qcService/institutionQc'
const { proxy } = getCurrentInstance()
const data = reactive({
    queryParams: { pageNum: 1, pageSize: 10 }
})
const teamOption = ref([]);
const total = ref(10)
const dataList = ref([])
const selectedArr = ref([])
const loading = ref(false)
const showSearch = ref(false)
const { queryParams } = toRefs(data)
const columns = ref([
    { "key": 0, "label": "机构ID", "visible": true },
    { "key": 1, "label": "机构名称", "visible": true },
    { "key": 2, "label": "备注", "visible": true },
    { "key": 3, "label": "质检员", "visible": true },
    { "key": 4, "label": "状态", "visible": true },
    { "key": 5, "label": "更新人", "visible": true },
    { "key": 6, "label": "更新时间", "visible": true },
])
getList()
function getList() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    loading.value = true
    for (const key in reqForm) {
        if (Array.isArray(reqForm[key])) {
            if (reqForm[key].length) {
                reqForm[key] = String(reqForm[key])
            } else {
                delete reqForm[key]
            }
        }
    }
    teamQcListApi(reqForm).then(res => {
        total.value = res.total
        dataList.value = res.rows
    }).finally(() => loading.value = false)
}
function edit(row) {
    proxy.$refs['updateInfoRef'].openDialog(row)
}
function handleSet(row) {
    const data = {
        ...row,
        teamId: row ? row.id : String(selectedArr.value.map(v => v.id))
    }
    proxy.$refs['setInfoRef'].openDialog(data)
}
function handleSortChange({ prop, order }) {
    const orderObj = { id: 1 }
    delete queryParams.value.orderBy
    delete queryParams.value.sortOrder
    if (order) {
        queryParams.value.orderBy = orderObj[prop]
        queryParams.value.sortOrder = proxy.orderEnum[order]
    }
    handleQuery()
}
function resetQuery() {
    queryParams.value = { pageNum: 1, pageSize: 10 }
    getList()
}
function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
}

function handleSelectionChange(selection) {
    selectedArr.value = selection
}
// 获取机构
getTeamTreeFun()
function getTeamTreeFun() {
    getTeamTree().then((res) => {
        teamOption.value = res.data;
    })
}

</script>

<style lang="scss" scoped></style>