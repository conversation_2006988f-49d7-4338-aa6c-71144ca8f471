<template>
    <div class="data-screen-header">
        <div class="data-screen-date">
            <div class="date-time">{{ hMSTime }}</div>
            <div class="date-ymd">
                <div>{{ yMDTime }}</div>
                <div>{{ weekday }}</div>
            </div>
        </div>
        <div class="data-screen-title">M+个贷不良资产分析系统</div>
        <div class="data-screen-user-info">
            <el-dropdown>
                <span class="user-info el-dropdown-link">
                    <el-button icon="UserFilled" type="text">
                        你好！admin
                    </el-button>
                    <el-button icon="CaretBottom" type="text" />
                </span>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            <img class="exit-btn" @click="toBack()" src="@/assets/images/dataBigScreen/exit.png" alt="">
        </div>
    </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
const store = useStore();
const router = useRouter()
const yMDTime = ref(null)
const hMSTime = ref(null)
const timeId = ref(null)
const weekday = ref(null)
const loginForm = ref({
    browserFingerprint: "",
    internalIp: "",
});

onMounted(() => {
    if (timeId.value) {
        clearInterval(timeId.value)
    }
    getFingerprint().then((res) => {
        loginForm.value.browserFingerprint = res.murmur
    });
    getIpAddress().then((res) => {
        loginForm.value.internalIp = findPrivateIP(res)
    });
    timeId.value = setInterval(() => {
        const { yMD, hMS, week } = getTime()
        yMDTime.value = yMD
        hMSTime.value = hMS
        weekday.value = week
    }, 1000)
})
onUnmounted(() => {
    clearInterval(timeId.value)
})
function logout() {
    ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(() => {
        store.dispatch("LogOut", loginForm.value).then(() => {
            location.href = "/index";
        });
    })
}
function getTime() {
    const date = new Date()
    const years = String(date.getFullYear()).padStart(2, 0)
    const months = String(date.getMonth() + 1).padStart(2, 0)
    const days = String(date.getDate()).padStart(2, 0)
    const hours = String(date.getHours()).padStart(2, 0)
    const minutes = String(date.getMinutes()).padStart(2, 0)
    const seconds = String(date.getSeconds()).padStart(2, 0)
    let weekday = date.getDay()
    const yMD = `${years}-${months}-${days}`
    const hMS = `${hours}:${minutes}:${seconds}`
    const weekdayInfo = { 0: '星期日', 1: '星期一', 2: '星期二', 3: '星期三', 4: '星期四', 5: '星期五', 6: '星期六' }
    weekday = weekdayInfo[weekday]
    return { yMD, hMS, week: weekday }
}
function toBack() {
    router.go(-1)
}
</script>

<style lang="scss" scoped>
.data-screen-header {
    position: relative;
    color: #38E1FF;
    background-image: url(@/assets/images/dataBigScreen/screen-header-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    .data-screen-date {
        position: absolute;
        display: flex;
        top: 50%;
        transform: translateY(-50%);

        .date-time {
            font-size: 38px;
            margin: 0 8px 0 32px;
        }

        .date-ymd {
            font-size: 14px;
        }
    }

    .data-screen-title {
        padding: 10px 0 25px;
        text-align: center;
        font-size: 36px;
        font-weight: bold;
    }

    .data-screen-user-info {
        position: absolute;
        right: 20px;
        top: 50%;
        z-index: 9;
        transform: translateY(-50%);

        .user-info {
            color: #fff;
        }

        .exit-btn {
            width: 24px;
            cursor: pointer;
            margin-left: 20px;
        }
    }
}
</style>