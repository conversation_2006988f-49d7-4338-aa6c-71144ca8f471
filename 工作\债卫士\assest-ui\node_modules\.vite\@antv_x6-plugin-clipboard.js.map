{"version": 3, "sources": ["../@antv/x6-plugin-clipboard/src/clipboard.ts", "../@antv/x6-plugin-clipboard/src/api.ts", "../@antv/x6-plugin-clipboard/src/index.ts"], "sourcesContent": [null, null, null], "mappings": ";;;;;;;;;;;AAEM,0BAAoB;EAA1B,cAAA;AAES,SAAA,QAAgB;;EAEvB,KACE,OACA,OACA,UAAqC,IAAE;AAEvC,SAAK,UAAO,OAAA,OAAA,IAAQ;AACpB,UAAM,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM;AACnD,UAAM,SAAS,MAAM,cAAc,OAAO;AAG1C,SAAK,QAAQ,cAAS,OACpB,OAAO,KAAK,QAAQ,IAAI,CAAC,QAAQ,OAAO,OACxC,CAAC,SAAU,KAAK,WAAW,IAAI;AAGjC,SAAK,UAAU;;EAGjB,IACE,OACA,OACA,UAAqC,IAAE;AAEvC,SAAK,KAAK,OAAO,OAAO;AACxB,UAAM,QAAQ,MAAM,QAAQ,SAAS,MAAM,QAAQ;AACnD,UAAM,YAAY,OAAO,MAAK;AAC5B,YAAM,QAAQ,CAAC,SAAS,KAAK;;;EAIjC,MAAM,OAAsB,UAAsC,IAAE;AAClE,UAAM,eAAY,OAAA,OAAA,OAAA,OAAA,IAAQ,KAAK,UAAY;AAC3C,UAAM,EAAE,QAAQ,WAAW,cAAc;AAEzC,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,QAAQ;AACV,WAAK,OAAO,WAAW,WAAW,SAAS,OAAO;AAClD,WAAK,OAAO,WAAW,WAAW,SAAS,OAAO;;AAGpD,SAAK,YAAY;AACjB,UAAM,QAAQ,KAAK;AAEnB,UAAM,QAAQ,CAAC,SAAQ;AACrB,WAAK,QAAQ;AACb,WAAK,WAAW;AAChB,UAAI,MAAM,IAAI;AACZ,aAAK,UAAU,IAAI;;AAGrB,UAAI,aAAa,KAAK,UAAU;AAC9B,aAAK,KAAK;;AAGZ,UAAI,aAAa,KAAK,UAAU;AAC9B,aAAK,KAAK;;;AAId,UAAM,QAAQ,MAAM,QAAQ,SAAS,MAAM,QAAQ;AACnD,UAAM,YAAY,SAAS,MAAK;AAC9B,YAAM,SAAS,KAAK;;AAGtB,SAAK,KAAK,OAAO,OAAO;AAExB,WAAO;;EAGT,UAAU,SAAmC;AAC3C,QAAI,QAAQ,oBAAoB,OAAO;AACrC,cAAQ,KAAK,KAAK;;;EAItB,YAAY,SAAmC;AAC7C,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,QAAQ,QAAQ;AACtB,UAAI,OAAO;AACT,aAAK,QAAQ;;;;EAKnB,QAAQ,UAAiC,IAAE;AACzC,QAAI,QAAQ,iBAAiB;AAG3B,WAAK,YAAY;;AAEnB,WAAO,KAAK,MAAM,UAAU;;EAG9B,QAAK;AACH,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,YAAQ;;;AAkCZ,IAAU;AAAV,AAAA,UAAU,UAAO;AACf,QAAM,oBAAoB,GAAG,OAAO;AAEpC,gBAAqB,OAAa;AAChC,QAAI,OAAO,cAAc;AACvB,YAAM,OAAO,MAAM,IAAI,CAAC,SAAS,KAAK;AACtC,mBAAa,QAAQ,mBAAmB,KAAK,UAAU;;;AAH3C,WAAA,OAAI;AAOpB,mBAAqB;AACnB,QAAI,OAAO,cAAc;AACvB,YAAM,MAAM,aAAa,QAAQ;AACjC,YAAM,QAAQ,MAAM,KAAK,MAAM,OAAO;AACtC,UAAI,OAAO;AACT,eAAO,MAAM,SAAS;;;;AALZ,WAAA,QAAK;AAUrB,mBAAqB;AACnB,QAAI,OAAO,cAAc;AACvB,mBAAa,WAAW;;;AAFZ,WAAA,QAAK;GApBb,WAAA,WAAO;;;ACjHjB,MAAM,UAAU,qBAAqB,WAAA;AACnC,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,WAAO,UAAU;;AAEnB,SAAO;;AAGT,MAAM,UAAU,kBAAkB,WAAA;AAChC,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,cAAU;;AAEZ,SAAO;;AAGT,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,cAAU;;AAEZ,SAAO;;AAGT,MAAM,UAAU,kBAAkB,SAAU,SAAiB;AAC3D,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,cAAU,cAAc;;AAG1B,SAAO;;AAGT,MAAM,UAAU,mBAAmB,SAAU,SAA2B;AACtE,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,WAAO,UAAU,QAAQ;;AAE3B,SAAO;;AAGT,MAAM,UAAU,sBAAsB,WAAA;AACpC,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,WAAO,UAAU;;AAEnB,SAAO;;AAGT,MAAM,UAAU,iBAAiB,WAAA;AAC/B,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,cAAU;;AAEZ,SAAO;;AAGT,MAAM,UAAU,OAAO,SACrB,OACA,SAA+B;AAE/B,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,cAAU,KAAK,OAAO;;AAExB,SAAO;;AAGT,MAAM,UAAU,MAAM,SACpB,OACA,SAA+B;AAE/B,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,cAAU,IAAI,OAAO;;AAEvB,SAAO;;AAGT,MAAM,UAAU,QAAQ,SACtB,SACA,OAAa;AAEb,QAAM,YAAY,KAAK,UAAU;AACjC,MAAI,WAAW;AACb,WAAO,UAAU,MAAM,SAAS;;AAElC,SAAO;;;;;;;;;;;;;;;;;;;;;;;;;;AC3GH,8BACI,SAA6B;MAQjC,WAAQ;AACV,WAAO,KAAK,QAAQ,YAAY;;MAG9B,QAAK;AACP,WAAO,KAAK,cAAc;;EAG5B,YAAY,UAA6B,IAAE;AACzC;AAdK,SAAA,OAAO;AAeZ,SAAK,UAAO,OAAA,OAAA,EAAK,SAAS,QAAS;;EAGrC,KAAK,OAAY;AACf,SAAK,QAAQ;AACb,SAAK,gBAAgB,IAAI;AACzB,SAAK,cAAc,YAAY,KAAK;;EAKtC,YAAS;AACP,WAAO,CAAC,KAAK;;EAGf,SAAM;AACJ,QAAI,KAAK,UAAU;AACjB,WAAK,QAAQ,UAAU;;;EAI3B,UAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,QAAQ,UAAU;;;EAI3B,cAAc,SAAiB;AAC7B,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,KAAK,aAAa;AAChC,YAAI,SAAS;AACX,eAAK;eACA;AACL,eAAK;;;eAGA,KAAK,aAAa;AAC3B,WAAK;WACA;AACL,WAAK;;AAGP,WAAO;;EAGT,QAAQ,UAA6B,IAAE;AACrC,WAAO,KAAK,cAAc,QAAQ;;EAGpC,sBAAmB;AACjB,WAAO,KAAK;;EAGd,MAAM,OAAe;AACnB,QAAI,CAAC,KAAK,YAAY,OAAO;AAC3B,WAAK,cAAc;AACnB,WAAK,OAAO,qBAAqB,EAAE,OAAO;;AAE5C,WAAO;;EAGT,KAAK,OAAe,UAAiC,IAAE;AACrD,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,cAAc,KAAK,OAAO,KAAK,OAAK,OAAA,OAAA,OAAA,OAAA,IACpC,KAAK,gBACL;AAEL,WAAK,OAAO,qBAAqB,EAAE;;AAErC,WAAO;;EAGT,IAAI,OAAe,UAAiC,IAAE;AACpD,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,cAAc,IAAI,OAAO,KAAK,OAAK,OAAA,OAAA,OAAA,OAAA,IACnC,KAAK,gBACL;AAEL,WAAK,OAAO,qBAAqB,EAAE;;AAErC,WAAO;;EAGT,MAAM,UAAkC,IAAI,QAAe,KAAK,OAAK;AACnE,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,KAAK,cAAc,MAAM,OAAK,OAAA,OAAA,OAAA,OAAA,IAChC,KAAK,gBACL;;AAGP,WAAO;;MAKK,gBAAa;AACzB,UAAM,KAAyB,KAAK,SAA9B,EAAE,YAAO,IAAK,SAAM,OAAA,IAApB,CAAA;AACN,WAAO;;EAGC,OACR,MACA,MAA4B;AAE5B,SAAK,QAAQ,MAAM;AACnB,SAAK,MAAM,QAAQ,MAAM;;EAI3B,UAAO;AACL,SAAK,MAAM;AACX,SAAK;;;AAFP,WAAA;EADC,SAAS;;", "names": []}