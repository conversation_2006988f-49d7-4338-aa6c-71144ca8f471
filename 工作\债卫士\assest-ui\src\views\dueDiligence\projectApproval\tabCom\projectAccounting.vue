<template>
    <div class="mt20">
        <el-form inline label-width="auto">
            <el-form-item label="项目ID" prop="projectId">
                <el-input v-model="queryParams.projectId" style="width: 320px;" placeholder="请输入项目ID" />
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="queryParams.projectName" style="width: 320px;" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item label="资产转让方" prop="transferor">
                <el-input v-model="queryParams.transferor" style="width: 320px;" placeholder="请输入资产转让方" />
            </el-form-item>
            <el-form-item label="产品类型" prop="productType">
                <el-input v-model="queryParams.productType" style="width: 320px;" placeholder="请输入产品类型" />
            </el-form-item>
            <el-form-item label="付款单状态" prop="paymentStatus">
                <el-input v-model="queryParams.paymentStatus" style="width: 320px;" placeholder="请输入付款单状态" />
            </el-form-item>
            <el-form-item label="付款单申请流水号" prop="paymentSerialNo">
                <el-input v-model="queryParams.paymentSerialNo" style="width: 320px;" placeholder="请输入付款单申请流水号" />
            </el-form-item>
            <el-form-item label="收款账号" prop="receivingAccount">
                <el-input v-model="queryParams.receivingAccount" style="width: 320px;" placeholder="请输入收款账号" />
            </el-form-item>
            <el-form-item label="付款类型" prop="paymentType">
                <el-input v-model="queryParams.paymentType" style="width: 320px;" placeholder="请输入付款类型" />
            </el-form-item>
            <el-form-item label="申请人" prop="applicant">
                <el-input v-model="queryParams.applicant" style="width: 320px;" placeholder="请输入申请人" />
            </el-form-item>
            <el-form-item label="申请时间" prop="applyTime">
                <el-date-picker v-model="queryParams.applyTime" type="datetime" style="width: 320px;"
                    placeholder="请选择申请时间" />
            </el-form-item>
            <el-form-item label="收款人" prop="payee">
                <el-input v-model="queryParams.payee" style="width: 320px;" placeholder="请输入收款人" />
            </el-form-item>
            <el-form-item label="账户名" prop="accountName">
                <el-input v-model="queryParams.accountName" style="width: 320px;" placeholder="请输入账户名" />
            </el-form-item>
            <el-form-item label="开户行" prop="bank">
                <el-input v-model="queryParams.bank" style="width: 320px;" placeholder="请输入开户行" />
            </el-form-item>
            <el-form-item label="处理人" prop="processor">
                <el-input v-model="queryParams.processor" style="width: 320px;" placeholder="请输入处理人" />
            </el-form-item>
            <el-form-item label="处理时间" prop="processTime">
                <el-date-picker v-model="queryParams.processTime" type="datetime" style="width: 320px;"
                    placeholder="请选择处理时间" />
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="opt-area">
            <el-button plain type="success" @click="handle()">通过</el-button>
            <el-button plain type="warning" @click="handle()">不通过</el-button>
        </div>
        <SelectedAll />
        <el-tabs v-model="activetab" @tab-change="antiShake(handleQuery)">
            <el-tab-pane v-for="item in tabList" :key="item.code" :label="item.info" :name="item.code" />
        </el-tabs>
        <el-table :data="dataList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="44px" :selectable="selectable" align="right" />
            <el-table-column label="项目ID" prop="projectId" align="center" width="120" />
            <el-table-column label="项目名称" prop="projectName" align="center" width="120" />
            <el-table-column label="资产转让方" prop="transferor" align="center" width="120" />
            <el-table-column label="产品类型" prop="productType" align="center" width="120" />
            <el-table-column label="建账状态" prop="accountStatus" align="center" width="120" />
            <el-table-column label="付款单申请流水号" prop="paymentSerialNo" align="center" width="150" />
            <el-table-column label="付款单状态" prop="paymentStatus" align="center" width="120" />
            <el-table-column label="付款类型" prop="paymentType" align="center" width="120" />
            <el-table-column label="收款账号" prop="receivingAccount" align="center" width="120" />
            <el-table-column label="账户名" prop="accountName" align="center" width="120" />
            <el-table-column label="收款人" prop="payee" align="center" width="120" />
            <el-table-column label="开户行" prop="bank" align="center" width="120" />
            <el-table-column label="合同金额" prop="contractAmount" align="center" width="120" />
            <el-table-column label="申请金额" prop="applyAmount" align="center" width="120" />
            <el-table-column label="备注" prop="remark" align="center" width="120" />
            <el-table-column label="申请人" prop="applicant" align="center" width="120" />
            <el-table-column label="申请时间" prop="applyTime" align="center" width="160" />
            <el-table-column label="处理状态" prop="processStatus" align="center" width="120" />
            <el-table-column label="处理人" prop="processor" align="center" width="120" />
            <el-table-column label="处理时间" prop="processTime" align="center" width="160" />
            <el-table-column width="180" fixed="right" label="操作" align="center">
                <template #default="{ row }">
                    <el-button type="text" @click="handle(row)">通过</el-button>
                    <el-button type="text" @click="handle(row)">不通过</el-button>
                    <el-button type="text" @click="handleDetails(row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
const route = useRoute()
const router = useRouter()
const tabList = ref([
    { code: '0', info: '待处理' },
    { code: '1', info: '已同意' },
    { code: '2', info: '未同意' },
    { code: '3', info: '已撤销' },
    { code: 'all', info: '全部' },
])

const queryParams = ref({ pageNum: 1, pageSize: 10 })
// 模拟数据
const dataList = ref([
    {
        projectId: 'A123456789',
        projectName: '2024年度资产项目',
        transferor: '某资产公司',
        productType: '企业贷',
        accountStatus: '已建账',
        paymentSerialNo: 'P202401100001',
        paymentStatus: '待审批',
        paymentType: '转账',
        receivingAccount: '6222********1234',
        accountName: '张三',
        payee: '张三',
        bank: '某银行',
        contractAmount: '1,000.00',
        applyAmount: '1,000.00',
        remark: '测试备注',
        applicant: '李四',
        applyTime: '2024-01-10 12:00:00',
        processStatus: '待处理',
        processor: '-',
        processTime: '-'
    }
])

const handleQuery = () => {
    queryParmas.value.pageNum = 1
}

const handle = (row) => {
    console.log('处理行数据：', row)
}

function handleDetails(row) {
    const query = { path: route.path, pageType: 'projectAccounting', progressStatus: 2 }
    router.push({ path: `/dueDiligence/projectInfo`, query })
}
const selectable = (row) => {
    // 根据状态判断是否可选
    return true
}

const resetQuery = () => {
    queryParmas.value = { pageNum: 1, pageSize: 10 }
}
</script>

<style lang="scss" scoped>
.range-scope {
    display: flex;

    span {
        margin: 0 10px;
    }
}
</style>