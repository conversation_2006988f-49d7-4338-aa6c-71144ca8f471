<template>
    <div class="ml20">
        <div class="operation-revealing-area mb20">
            <div class="operation-revealing-area-btn">
                <el-button :loading="loading" type="primary" @click="update()">添加账号</el-button>
                <span class="ml10">注：语音库配置的是风控管理-智能质检通话录音的语音库！</span>
            </div>
            <right-toolbar :columns="columns" @queryTable="getList" :types="[2, 3]" />
        </div>
        <div class="table-box">
            <el-table :data="dataList" :loading="loading">
                <el-table-column v-if="columns[0].visible" align="center" prop="accountName" label="账号名称" width="120" />
                <el-table-column v-if="columns[1].visible" align="center" prop="appKey" label="appkey" />
                <el-table-column v-if="columns[2].visible" align="center" prop="accessKeyId" label="accesskeyid" />
                <el-table-column v-if="columns[3].visible" align="center" prop="accessKeySecret"
                    label="accesskeysecret" />
                <el-table-column v-if="columns[4].visible" align="center" prop="duration" label="通话最短时长" />
                <el-table-column v-if="columns[5].visible" align="center" prop="status"
                    :formatter="row => switchStatusEnum[row.status]" label="状态" width="120" />
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <div>
                            <el-button :loading="loading" type="text" @click="update(row)">编辑</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
        <voiceLibraryUpdateInfo :getList="getList" ref="voiceLibraryUpdateInfoRef" />
    </div>
</template>

<script setup>
import { switchStatusEnum } from '@/utils/enum';
import voiceLibraryUpdateInfo from '../dialog/voiceLibraryUpdateInfo';
import { warnStrategyEmailListApi } from '@/api/qcService/warnStrategy';
const { proxy } = getCurrentInstance()
const queryParams = ref({
    pageNum: 1, pageSize: 10
})
const total = ref(1)
const dataList = ref([
    { "createBy": "admin", "createTime": "2023-12-12 09:44:52", "updateTime": "2024-05-21 09:47:52", "id": 1, "accountName": "阿里云-test", "appKey": "testBAiOfwS2232051rU", "accessKeyId": "testLTAI0uwRGQ3232327f", "accessKeySecret": "testilaIb7QLmNC32434343c6KRhmpEWcZk4Enj", "duration": 15, "status": 0, "limit": 0 }
])
const loading = ref(false)
const columns = ref([
    { "key": 0, "label": "账号名称", "visible": true },
    { "key": 1, "label": "appkey", "visible": true },
    { "key": 2, "label": "accesskeyid", "visible": true },
    { "key": 3, "label": "accesskeysecret", "visible": true },
    { "key": 4, "label": "通话最短时长", "visible": true },
    { "key": 5, "label": "状态", "visible": true }
])
function getList() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    loading.value = true
    for (const key in reqForm) {
        if (Array.isArray(reqForm[key])) {
            reqForm[key] = String(reqForm[key])
        }
    }
    warnStrategyEmailListApi(reqForm).then(res => {
        total.value = res.total
        dataList.value = res.rows
    }).finally(() => loading.value = false)
}
function update(row) {
    const title = row ? '编辑' : '添加'
    proxy.$refs['voiceLibraryUpdateInfoRef'].openDialog({ row, title })
}
</script>

<style lang="scss" scoped>
.operation-revealing-area-btn {
    display: flex;
    align-items: center;
}
</style>