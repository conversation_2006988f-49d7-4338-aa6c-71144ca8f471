<template>
    <el-dialog :title="title" v-model="open" append-to-body width="550" :before-close="cancel">
        <el-form inline ref="formRef" label-width="auto" :key="form" :model="form" :rules="rules">
            <el-form-item prop="emailName" label="邮箱名称">
                <el-input v-model="form.emailName" style="width:320px;" :maxlength="30" placeholder="请输入邮箱名称" />
            </el-form-item>
            <el-form-item prop="emailAccount" label="邮箱账号">
                <el-input v-model="form.emailAccount" style="width:320px;" :maxlength="30" placeholder="请输入邮箱账号" />
            </el-form-item>
            <el-form-item prop="password" label="服务密码">
                <el-input v-model="form.password" style="width:320px;" :maxlength="20" placeholder="请输入服务密码" />
            </el-form-item>
            <el-form-item prop="address" label="域名">
                <el-input v-model="form.address" style="width:320px;" :maxlength="50" placeholder="请输入域名" />
            </el-form-item>
            <el-form-item prop="port" label="端口">
                <el-input v-model="form.port" style="width:320px;" :maxlength="20" placeholder="请输入端口" />
            </el-form-item>
            <el-form-item prop="status" label="状态">
                <el-radio-group v-model="form.status">
                    <el-radio v-for="(v, i) in switchStatusEnum" :key="i" :label="+i">{{ v }}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" @click="submit" type="primary"> 确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { switchStatusEnum } from '@/utils/enum';
import { addEmailWarnStrategyApi, editEmailWarnStrategyApi } from '@/api/qcService/warnStrategy';
const props = defineProps({
    getList: { type: Function, default: () => { } }
})
const { proxy } = getCurrentInstance()
const open = ref(false)
const loading = ref(false)
const title = ref('添加')
const data = reactive({
    form: { stutas: 0 },
    rules: {
        emailName: [{ required: true, message: '请输入邮箱名称', trigger: 'blur' }],
        emailAccount: [
            { required: true, message: '请输入邮箱账号', trigger: 'blur' },
            { pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入正确邮箱格式', trigger: 'blur' }
        ],
        password: [{ required: true, message: '请输入服务密码', trigger: 'blur' }],
        address: [{ required: true, message: '请输入域名', trigger: 'blur' }],
        port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    },
})
const { form, rules } = toRefs(data)
function submit() {
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            const reqForm = JSON.parse(JSON.stringify(form.value))
            const reqApi = reqForm.id ? editEmailWarnStrategyApi : addEmailWarnStrategyApi
            reqApi(reqForm).then(() => {
                cancel()
                props.getList && props.getList()
                proxy.$modal.msgSuccess('操作成功！')
            })
        }
    })
}

function openDialog(data) {
    open.value = true
    console.log(data);

    title.value = data.title
    form.value = data.row ? data.row : {}
}

function cancel() {
    open.value = false
}
defineExpose({ openDialog })
</script>
<style lang="scss" scoped></style>