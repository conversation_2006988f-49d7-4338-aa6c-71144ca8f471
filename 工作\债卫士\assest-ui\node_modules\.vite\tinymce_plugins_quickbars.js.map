{"version": 3, "sources": ["../tinymce/plugins/quickbars/plugin.js", "../tinymce/plugins/quickbars/index.js", "dep:tinymce_plugins_quickbars"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var unique = 0;\n    var generate = function (prefix) {\n      var date = new Date();\n      var time = date.getTime();\n      var random = Math.floor(Math.random() * **********);\n      unique++;\n      return prefix + '_' + random + unique + String(time);\n    };\n\n    var createTableHtml = function (cols, rows) {\n      var html = '<table data-mce-id=\"mce\" style=\"width: 100%\">';\n      html += '<tbody>';\n      for (var y = 0; y < rows; y++) {\n        html += '<tr>';\n        for (var x = 0; x < cols; x++) {\n          html += '<td><br></td>';\n        }\n        html += '</tr>';\n      }\n      html += '</tbody>';\n      html += '</table>';\n      return html;\n    };\n    var getInsertedElement = function (editor) {\n      var elms = editor.dom.select('*[data-mce-id]');\n      return elms[0];\n    };\n    var insertTableHtml = function (editor, cols, rows) {\n      editor.undoManager.transact(function () {\n        editor.insertContent(createTableHtml(cols, rows));\n        var tableElm = getInsertedElement(editor);\n        tableElm.removeAttribute('data-mce-id');\n        var cellElm = editor.dom.select('td,th', tableElm);\n        editor.selection.setCursorLocation(cellElm[0], 0);\n      });\n    };\n    var insertTable = function (editor, cols, rows) {\n      editor.plugins.table ? editor.plugins.table.insertTable(cols, rows) : insertTableHtml(editor, cols, rows);\n    };\n    var insertBlob = function (editor, base64, blob) {\n      var blobCache = editor.editorUpload.blobCache;\n      var blobInfo = blobCache.create(generate('mceu'), blob, base64);\n      blobCache.add(blobInfo);\n      editor.insertContent(editor.dom.createHTML('img', { src: blobInfo.blobUri() }));\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Promise');\n\n    var blobToBase64 = function (blob) {\n      return new global$2(function (resolve) {\n        var reader = new FileReader();\n        reader.onloadend = function () {\n          resolve(reader.result.split(',')[1]);\n        };\n        reader.readAsDataURL(blob);\n      });\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var pickFile = function (editor) {\n      return new global$2(function (resolve) {\n        var fileInput = document.createElement('input');\n        fileInput.type = 'file';\n        fileInput.accept = 'image/*';\n        fileInput.style.position = 'fixed';\n        fileInput.style.left = '0';\n        fileInput.style.top = '0';\n        fileInput.style.opacity = '0.001';\n        document.body.appendChild(fileInput);\n        var changeHandler = function (e) {\n          resolve(Array.prototype.slice.call(e.target.files));\n        };\n        fileInput.addEventListener('change', changeHandler);\n        var cancelHandler = function (e) {\n          var cleanup = function () {\n            resolve([]);\n            fileInput.parentNode.removeChild(fileInput);\n          };\n          if (global$1.os.isAndroid() && e.type !== 'remove') {\n            global.setEditorTimeout(editor, cleanup, 0);\n          } else {\n            cleanup();\n          }\n          editor.off('focusin remove', cancelHandler);\n        };\n        editor.on('focusin remove', cancelHandler);\n        fileInput.click();\n      });\n    };\n\n    var setupButtons = function (editor) {\n      editor.ui.registry.addButton('quickimage', {\n        icon: 'image',\n        tooltip: 'Insert image',\n        onAction: function () {\n          pickFile(editor).then(function (files) {\n            if (files.length > 0) {\n              var blob_1 = files[0];\n              blobToBase64(blob_1).then(function (base64) {\n                insertBlob(editor, base64, blob_1);\n              });\n            }\n          });\n        }\n      });\n      editor.ui.registry.addButton('quicktable', {\n        icon: 'table',\n        tooltip: 'Insert table',\n        onAction: function () {\n          insertTable(editor, 2, 2);\n        }\n      });\n    };\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var eq = function (t) {\n      return function (a) {\n        return t === a;\n      };\n    };\n    var isString = isType('string');\n    var isObject = isType('object');\n    var isArray = isType('array');\n    var isBoolean = isSimpleType('boolean');\n    var isUndefined = eq(undefined);\n    var isFunction = isSimpleType('function');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    function ClosestOrAncestor (is, ancestor, scope, a, isRoot) {\n      if (is(scope, a)) {\n        return Optional.some(scope);\n      } else if (isFunction(isRoot) && isRoot(scope)) {\n        return Optional.none();\n      } else {\n        return ancestor(scope, a, isRoot);\n      }\n    }\n\n    var ELEMENT = 1;\n\n    var fromHtml = function (html, scope) {\n      var doc = scope || document;\n      var div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        console.error('HTML does not have a single root node', html);\n        throw new Error('HTML must have a single root node');\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    var fromTag = function (tag, scope) {\n      var doc = scope || document;\n      var node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    var fromText = function (text, scope) {\n      var doc = scope || document;\n      var node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    var fromDom = function (node) {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    var fromPoint = function (docElm, x, y) {\n      return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    };\n    var SugarElement = {\n      fromHtml: fromHtml,\n      fromTag: fromTag,\n      fromText: fromText,\n      fromDom: fromDom,\n      fromPoint: fromPoint\n    };\n\n    var is = function (element, selector) {\n      var dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        var elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var name = function (element) {\n      var r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n\n    var ancestor$1 = function (scope, predicate, isRoot) {\n      var element = scope.dom;\n      var stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        var el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    var closest$1 = function (scope, predicate, isRoot) {\n      var is = function (s, test) {\n        return test(s);\n      };\n      return ClosestOrAncestor(is, ancestor$1, scope, predicate, isRoot);\n    };\n\n    var ancestor = function (scope, selector, isRoot) {\n      return ancestor$1(scope, function (e) {\n        return is(e, selector);\n      }, isRoot);\n    };\n    var closest = function (scope, selector, isRoot) {\n      var is$1 = function (element, selector) {\n        return is(element, selector);\n      };\n      return ClosestOrAncestor(is$1, ancestor, scope, selector, isRoot);\n    };\n\n    var validDefaultOrDie = function (value, predicate) {\n      if (predicate(value)) {\n        return true;\n      }\n      throw new Error('Default value doesn\\'t match requested type.');\n    };\n    var items = function (value, defaultValue) {\n      if (isArray(value) || isObject(value)) {\n        throw new Error('expected a string but found: ' + value);\n      }\n      if (isUndefined(value)) {\n        return defaultValue;\n      }\n      if (isBoolean(value)) {\n        return value === false ? '' : defaultValue;\n      }\n      return value;\n    };\n    var getToolbarItemsOr_ = function (predicate) {\n      return function (editor, name, defaultValue) {\n        validDefaultOrDie(defaultValue, predicate);\n        var value = editor.getParam(name, defaultValue);\n        return items(value, defaultValue);\n      };\n    };\n    var getToolbarItemsOr = getToolbarItemsOr_(isString);\n\n    var getTextSelectionToolbarItems = function (editor) {\n      return getToolbarItemsOr(editor, 'quickbars_selection_toolbar', 'bold italic | quicklink h2 h3 blockquote');\n    };\n    var getInsertToolbarItems = function (editor) {\n      return getToolbarItemsOr(editor, 'quickbars_insert_toolbar', 'quickimage quicktable');\n    };\n    var getImageToolbarItems = function (editor) {\n      return getToolbarItemsOr(editor, 'quickbars_image_toolbar', 'alignleft aligncenter alignright');\n    };\n\n    var addToEditor$1 = function (editor) {\n      var insertToolbarItems = getInsertToolbarItems(editor);\n      if (insertToolbarItems.trim().length > 0) {\n        editor.ui.registry.addContextToolbar('quickblock', {\n          predicate: function (node) {\n            var sugarNode = SugarElement.fromDom(node);\n            var textBlockElementsMap = editor.schema.getTextBlockElements();\n            var isRoot = function (elem) {\n              return elem.dom === editor.getBody();\n            };\n            return closest(sugarNode, 'table', isRoot).fold(function () {\n              return closest$1(sugarNode, function (elem) {\n                return name(elem) in textBlockElementsMap && editor.dom.isEmpty(elem.dom);\n              }, isRoot).isSome();\n            }, never);\n          },\n          items: insertToolbarItems,\n          position: 'line',\n          scope: 'editor'\n        });\n      }\n    };\n\n    var addToEditor = function (editor) {\n      var isEditable = function (node) {\n        return editor.dom.getContentEditableParent(node) !== 'false';\n      };\n      var isImage = function (node) {\n        return node.nodeName === 'IMG' || node.nodeName === 'FIGURE' && /image/i.test(node.className);\n      };\n      var imageToolbarItems = getImageToolbarItems(editor);\n      if (imageToolbarItems.trim().length > 0) {\n        editor.ui.registry.addContextToolbar('imageselection', {\n          predicate: isImage,\n          items: imageToolbarItems,\n          position: 'node'\n        });\n      }\n      var textToolbarItems = getTextSelectionToolbarItems(editor);\n      if (textToolbarItems.trim().length > 0) {\n        editor.ui.registry.addContextToolbar('textselection', {\n          predicate: function (node) {\n            return !isImage(node) && !editor.selection.isCollapsed() && isEditable(node);\n          },\n          items: textToolbarItems,\n          position: 'selection',\n          scope: 'editor'\n        });\n      }\n    };\n\n    function Plugin () {\n      global$3.add('quickbars', function (editor) {\n        setupButtons(editor);\n        addToEditor$1(editor);\n        addToEditor(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"quickbars\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/quickbars')\n//   ES2015:\n//     import 'tinymce/plugins/quickbars'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/quickbars/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS;AACb,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,OAAO,IAAI;AACf,YAAI,OAAO,KAAK;AAChB,YAAI,SAAS,KAAK,MAAM,KAAK,WAAW;AACxC;AACA,eAAO,SAAS,MAAM,SAAS,SAAS,OAAO;AAAA;AAGjD,UAAI,kBAAkB,SAAU,MAAM,MAAM;AAC1C,YAAI,OAAO;AACX,gBAAQ;AACR,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,kBAAQ;AACR,mBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,oBAAQ;AAAA;AAEV,kBAAQ;AAAA;AAEV,gBAAQ;AACR,gBAAQ;AACR,eAAO;AAAA;AAET,UAAI,qBAAqB,SAAU,QAAQ;AACzC,YAAI,OAAO,OAAO,IAAI,OAAO;AAC7B,eAAO,KAAK;AAAA;AAEd,UAAI,kBAAkB,SAAU,QAAQ,MAAM,MAAM;AAClD,eAAO,YAAY,SAAS,WAAY;AACtC,iBAAO,cAAc,gBAAgB,MAAM;AAC3C,cAAI,WAAW,mBAAmB;AAClC,mBAAS,gBAAgB;AACzB,cAAI,UAAU,OAAO,IAAI,OAAO,SAAS;AACzC,iBAAO,UAAU,kBAAkB,QAAQ,IAAI;AAAA;AAAA;AAGnD,UAAI,cAAc,SAAU,QAAQ,MAAM,MAAM;AAC9C,eAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM,YAAY,MAAM,QAAQ,gBAAgB,QAAQ,MAAM;AAAA;AAEtG,UAAI,aAAa,SAAU,QAAQ,QAAQ,MAAM;AAC/C,YAAI,YAAY,OAAO,aAAa;AACpC,YAAI,WAAW,UAAU,OAAO,SAAS,SAAS,MAAM;AACxD,kBAAU,IAAI;AACd,eAAO,cAAc,OAAO,IAAI,WAAW,OAAO,EAAE,KAAK,SAAS;AAAA;AAGpE,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,eAAe,SAAU,MAAM;AACjC,eAAO,IAAI,SAAS,SAAU,SAAS;AACrC,cAAI,SAAS,IAAI;AACjB,iBAAO,YAAY,WAAY;AAC7B,oBAAQ,OAAO,OAAO,MAAM,KAAK;AAAA;AAEnC,iBAAO,cAAc;AAAA;AAAA;AAIzB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,IAAI,SAAS,SAAU,SAAS;AACrC,cAAI,YAAY,SAAS,cAAc;AACvC,oBAAU,OAAO;AACjB,oBAAU,SAAS;AACnB,oBAAU,MAAM,WAAW;AAC3B,oBAAU,MAAM,OAAO;AACvB,oBAAU,MAAM,MAAM;AACtB,oBAAU,MAAM,UAAU;AAC1B,mBAAS,KAAK,YAAY;AAC1B,cAAI,gBAAgB,SAAU,GAAG;AAC/B,oBAAQ,MAAM,UAAU,MAAM,KAAK,EAAE,OAAO;AAAA;AAE9C,oBAAU,iBAAiB,UAAU;AACrC,cAAI,gBAAgB,SAAU,GAAG;AAC/B,gBAAI,UAAU,WAAY;AACxB,sBAAQ;AACR,wBAAU,WAAW,YAAY;AAAA;AAEnC,gBAAI,SAAS,GAAG,eAAe,EAAE,SAAS,UAAU;AAClD,qBAAO,iBAAiB,QAAQ,SAAS;AAAA,mBACpC;AACL;AAAA;AAEF,mBAAO,IAAI,kBAAkB;AAAA;AAE/B,iBAAO,GAAG,kBAAkB;AAC5B,oBAAU;AAAA;AAAA;AAId,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,GAAG,SAAS,UAAU,cAAc;AAAA,UACzC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,WAAY;AACpB,qBAAS,QAAQ,KAAK,SAAU,OAAO;AACrC,kBAAI,MAAM,SAAS,GAAG;AACpB,oBAAI,SAAS,MAAM;AACnB,6BAAa,QAAQ,KAAK,SAAU,QAAQ;AAC1C,6BAAW,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAMrC,eAAO,GAAG,SAAS,UAAU,cAAc;AAAA,UACzC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,WAAY;AACpB,wBAAY,QAAQ,GAAG;AAAA;AAAA;AAAA;AAK7B,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,WAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,MAAM;AACjC,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,UAAU;AAAA;AAAA;AAG5B,UAAI,KAAK,SAAU,GAAG;AACpB,eAAO,SAAU,GAAG;AAClB,iBAAO,MAAM;AAAA;AAAA;AAGjB,UAAI,WAAW,OAAO;AACtB,UAAI,WAAW,OAAO;AACtB,UAAI,UAAU,OAAO;AACrB,UAAI,YAAY,aAAa;AAC7B,UAAI,cAAc,GAAG;AACrB,UAAI,aAAa,aAAa;AAE9B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,iCAA4B,KAAI,WAAU,OAAO,GAAG,QAAQ;AAC1D,YAAI,IAAG,OAAO,IAAI;AAChB,iBAAO,SAAS,KAAK;AAAA,mBACZ,WAAW,WAAW,OAAO,QAAQ;AAC9C,iBAAO,SAAS;AAAA,eACX;AACL,iBAAO,UAAS,OAAO,GAAG;AAAA;AAAA;AAI9B,UAAI,UAAU;AAEd,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,IAAI,cAAc;AAC5B,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,mBAAmB,IAAI,WAAW,SAAS,GAAG;AACrD,kBAAQ,MAAM,yCAAyC;AACvD,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,QAAQ,IAAI,WAAW;AAAA;AAEhC,UAAI,UAAU,SAAU,KAAK,OAAO;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,cAAc;AAC7B,eAAO,QAAQ;AAAA;AAEjB,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,eAAe;AAC9B,eAAO,QAAQ;AAAA;AAEjB,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,EAAE,KAAK;AAAA;AAEhB,UAAI,YAAY,SAAU,QAAQ,GAAG,GAAG;AACtC,eAAO,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,IAAI,IAAI;AAAA;AAE9D,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,KAAK,SAAU,SAAS,UAAU;AACpC,YAAI,MAAM,QAAQ;AAClB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,eACF;AACL,cAAI,OAAO;AACX,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ;AAAA,qBACX,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB;AAAA,qBACrB,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB;AAAA,qBACzB,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB;AAAA,iBAC1B;AACL,kBAAM,IAAI,MAAM;AAAA;AAAA;AAAA;AAKtB,aAAO,WAAW,cAAc,SAAS,SAAS;AAElD,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,IAAI,QAAQ,IAAI;AACpB,eAAO,EAAE;AAAA;AAGX,UAAI,aAAa,SAAU,OAAO,WAAW,QAAQ;AACnD,YAAI,UAAU,MAAM;AACpB,YAAI,OAAO,WAAW,UAAU,SAAS;AACzC,eAAO,QAAQ,YAAY;AACzB,oBAAU,QAAQ;AAClB,cAAI,KAAK,aAAa,QAAQ;AAC9B,cAAI,UAAU,KAAK;AACjB,mBAAO,SAAS,KAAK;AAAA,qBACZ,KAAK,KAAK;AACnB;AAAA;AAAA;AAGJ,eAAO,SAAS;AAAA;AAElB,UAAI,YAAY,SAAU,OAAO,WAAW,QAAQ;AAClD,YAAI,MAAK,SAAU,GAAG,MAAM;AAC1B,iBAAO,KAAK;AAAA;AAEd,eAAO,kBAAkB,KAAI,YAAY,OAAO,WAAW;AAAA;AAG7D,UAAI,WAAW,SAAU,OAAO,UAAU,QAAQ;AAChD,eAAO,WAAW,OAAO,SAAU,GAAG;AACpC,iBAAO,GAAG,GAAG;AAAA,WACZ;AAAA;AAEL,UAAI,UAAU,SAAU,OAAO,UAAU,QAAQ;AAC/C,YAAI,OAAO,SAAU,SAAS,WAAU;AACtC,iBAAO,GAAG,SAAS;AAAA;AAErB,eAAO,kBAAkB,MAAM,UAAU,OAAO,UAAU;AAAA;AAG5D,UAAI,oBAAoB,SAAU,OAAO,WAAW;AAClD,YAAI,UAAU,QAAQ;AACpB,iBAAO;AAAA;AAET,cAAM,IAAI,MAAM;AAAA;AAElB,UAAI,QAAQ,SAAU,OAAO,cAAc;AACzC,YAAI,QAAQ,UAAU,SAAS,QAAQ;AACrC,gBAAM,IAAI,MAAM,kCAAkC;AAAA;AAEpD,YAAI,YAAY,QAAQ;AACtB,iBAAO;AAAA;AAET,YAAI,UAAU,QAAQ;AACpB,iBAAO,UAAU,QAAQ,KAAK;AAAA;AAEhC,eAAO;AAAA;AAET,UAAI,qBAAqB,SAAU,WAAW;AAC5C,eAAO,SAAU,QAAQ,OAAM,cAAc;AAC3C,4BAAkB,cAAc;AAChC,cAAI,QAAQ,OAAO,SAAS,OAAM;AAClC,iBAAO,MAAM,OAAO;AAAA;AAAA;AAGxB,UAAI,oBAAoB,mBAAmB;AAE3C,UAAI,+BAA+B,SAAU,QAAQ;AACnD,eAAO,kBAAkB,QAAQ,+BAA+B;AAAA;AAElE,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,eAAO,kBAAkB,QAAQ,4BAA4B;AAAA;AAE/D,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,kBAAkB,QAAQ,2BAA2B;AAAA;AAG9D,UAAI,gBAAgB,SAAU,QAAQ;AACpC,YAAI,qBAAqB,sBAAsB;AAC/C,YAAI,mBAAmB,OAAO,SAAS,GAAG;AACxC,iBAAO,GAAG,SAAS,kBAAkB,cAAc;AAAA,YACjD,WAAW,SAAU,MAAM;AACzB,kBAAI,YAAY,aAAa,QAAQ;AACrC,kBAAI,uBAAuB,OAAO,OAAO;AACzC,kBAAI,SAAS,SAAU,MAAM;AAC3B,uBAAO,KAAK,QAAQ,OAAO;AAAA;AAE7B,qBAAO,QAAQ,WAAW,SAAS,QAAQ,KAAK,WAAY;AAC1D,uBAAO,UAAU,WAAW,SAAU,MAAM;AAC1C,yBAAO,KAAK,SAAS,wBAAwB,OAAO,IAAI,QAAQ,KAAK;AAAA,mBACpE,QAAQ;AAAA,iBACV;AAAA;AAAA,YAEL,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA;AAAA;AAAA;AAKb,UAAI,cAAc,SAAU,QAAQ;AAClC,YAAI,aAAa,SAAU,MAAM;AAC/B,iBAAO,OAAO,IAAI,yBAAyB,UAAU;AAAA;AAEvD,YAAI,UAAU,SAAU,MAAM;AAC5B,iBAAO,KAAK,aAAa,SAAS,KAAK,aAAa,YAAY,SAAS,KAAK,KAAK;AAAA;AAErF,YAAI,oBAAoB,qBAAqB;AAC7C,YAAI,kBAAkB,OAAO,SAAS,GAAG;AACvC,iBAAO,GAAG,SAAS,kBAAkB,kBAAkB;AAAA,YACrD,WAAW;AAAA,YACX,OAAO;AAAA,YACP,UAAU;AAAA;AAAA;AAGd,YAAI,mBAAmB,6BAA6B;AACpD,YAAI,iBAAiB,OAAO,SAAS,GAAG;AACtC,iBAAO,GAAG,SAAS,kBAAkB,iBAAiB;AAAA,YACpD,WAAW,SAAU,MAAM;AACzB,qBAAO,CAAC,QAAQ,SAAS,CAAC,OAAO,UAAU,iBAAiB,WAAW;AAAA;AAAA,YAEzE,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA;AAAA;AAAA;AAKb,wBAAmB;AACjB,iBAAS,IAAI,aAAa,SAAU,QAAQ;AAC1C,uBAAa;AACb,wBAAc;AACd,sBAAY;AAAA;AAAA;AAIhB;AAAA;AAAA;AAAA;;;ACvdJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,oCAAQ;", "names": []}