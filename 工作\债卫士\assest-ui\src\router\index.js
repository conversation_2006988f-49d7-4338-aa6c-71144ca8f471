import { createWebHistory, createRouter } from 'vue-router'
import Layout from '@/layout'
/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/dataBigScreen',
    component: () => import('@/views/report/dataBigScreen/index'),
    hidden: true
  },
  {
    path: '/case/checkFilelist/:applyId',
    component: () => import('@/views/checkFilelist/index'),
    hidden: true,
    meta: { title: '文档管理' }
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/FssoLogin',
    component: () => import('@/views/FssoLogin'),
    hidden: true,
    meta: { title: '中央认证' }
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      },
      // {
      //   path: '/assetsMonitor',
      //   component: () => import('@/views/projectInitPackage/assetsManage/index.vue'),
      //   name: 'AssetsMonitor',
      //   meta: { title: '资产分析报告', icon: 'dashboard', affix: true }
      // },
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/transferManage',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'transferDetails',
        component: () => import('@/views/transferManage/transferManagement/page/transferDetails'),
        name: 'TransferDetails',
        meta: { title: '查看详情', icon: 'user' }
      },
      {
        path: 'createAssets',
        component: () => import('@/views/projectInitPackage/assetsManage/page/createAssets'),
        name: 'CreateAssets',
        meta: { title: '组建资产包', icon: 'user' }
      }
    ]
  },
  {
    path: '/message/homeMessage-content',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'msgContent/:mesgId(\\d+)',
        component: () => import('@/views/message/homeMessage/msgContent.vue'),
        name: 'MsgContent',
        meta: { title: '消息内容', activeMenu: '/message/homeMessage' }
      }
    ]
  },
  {
    path: '/message/sysMessage-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addmesg/:mesgId*',
        component: () => import('@/views/message/sysMessage/page/addmesg.vue'),
        name: 'AddMesg',
        meta: { title: '消息内容', activeMenu: '/message/sysMessage' }
      },
      {
        path: 'details/:mesgId(\\d+)',
        component: () => import('@/views/message/sysMessage/details/index.vue'),
        name: 'SysMesgDetail',
        meta: { title: '消息内容', activeMenu: '/message/sysMessage' }
      }
    ]
  },
  {
    path: '/assets/assetside-add',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addproduct/:ownerId*',
        component: () => import('@/views/assets/assetside/addproduct'),
        name: 'AddProduct',
        meta: { title: '创建产品' }
      }
    ]
  },
  {
    path: '/assets/caseManage-handle',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'batchHandle',
        component: () => import('@/views/assets/caseManage/batchhandle'),
        name: 'BatchHandle',
        meta: { title: '案件批量操作' }
      }
    ]
  },
  {
    path: '/assets/caseManage-outcase',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'fast/:time(\\d+)',
        component: () => import('@/views/assets/caseManage/handOutCase/fast'),
        name: 'Fast',
        meta: { title: '快速分案' }
      }, {
        path: 'rules/:time(\\d+)',
        component: () => import('@/views/assets/caseManage/handOutCase/rules'),
        name: 'Rules',
        meta: { title: '规则分案' }
      }, {
        path: 'jointDebt',
        component: () => import('@/views/assets/caseManage/jointDebt/index'),
        name: 'JointDebt',
        meta: { title: '共债分案列表' }
      }, {
        path: 'jointDebt/division/:time(\\d+)',
        component: () => import('@/views/assets/caseManage/jointDebt/division'),
        name: 'JDdivision',
        meta: { title: '共债分案' }
      }
    ]
  },
  {
    path: '/assets/assetside-edit',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'editProduct/:proId(\\d+)',
        component: () => import('@/views/assets/assetside/editproduct'),
        name: 'EditProduct',
        meta: { title: '编辑产品' }
      }
    ]
  },
  {
    path: '/assets/caseManage-details',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'caseDetails/:caseId(\\d+)',
        component: () => import('@/views/caseDetails/index'),
        name: 'CaseDetails',
        meta: { title: '案件详情' },
      },
      {
        path: 'batchNumDetails',
        component: () => import('@/views/assets/caseApprove/page/batchNumDetails'),
        name: 'batchNumDetails',
        meta: { title: '委案批次号详情' }
      }
    ]
  },
  {
    path: '/assets/zccasemanage-details',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'caseDetails/:caseId(\\d+)',
        component: () => import('@/views/caseDetails/index'),
        name: 'zcCaseDetails',
        meta: { title: '案件详情', activeMenu: '/assets/zccasemanage' }
      }
    ]
  },
  {
    path: '/assets/asset-import',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'importCase/:productid(\\d+)',
        component: () => import('@/views/assets/asset/importCase'),
        name: 'ImportCase',
        meta: { title: '导入案件' }
      }
    ]
  },
  {
    path: '/assets/asset-btn',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'contract/:assetId(\\d+)',
        component: () => import('@/views/assets/asset/contract'),
        name: 'Contract',
        meta: { title: '合同管理' }
      }
    ]
  },
  {
    path: '/assets/oftentpl-handle',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'handeloftenTpl',
        component: () => import('@/views/assets/oftentpl/handeloftenTpl'),
        name: 'HandeloftenTpl',
        meta: { title: '模板添加/编辑', activeMenu: '/assets/oftentpl' }
      }
    ]
  },
  {
    path: '/service/workorder-detail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'orderDetail/:orderId(\\d+)',
        component: () => import('@/views/service/workorder/orderDetail'),
        name: 'OrderDetail',
        meta: { title: '工单详情', activeMenu: '/service/workorder' }
      }, {
        path: 'caseDetails/:caseId(\\d+)',
        component: () => import('@/views/caseDetails/index'),
        name: 'OrderCaseDetails',
        meta: { title: '案件详情', activeMenu: '/service/workorder' }
      }
    ]
  },
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }, {
        path: 'importUser',
        component: () => import('@/views/system/user/importUser'),
        name: 'ImportUser',
        meta: { title: '导入员工', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/mediation-data',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/system/stage/page/setFormPage.vue'),
        name: 'SetFormPage',
        meta: { title: '设置表单', activeMenu: '/system' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/team',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'teamMenu/:teamId(\\d+)',
        component: () => import('@/views/team/teamMenu/index'),
        name: 'TeamMenu',
        meta: { title: '机构菜单管理', activeMenu: '/team/teamlist' }
      }
    ]
  },
  {
    path: '/team/teamlist-add',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addteam',
        component: () => import('@/views/team/teamlist/addteam/index'),
        name: 'AddTeam',
        meta: { title: '创建机构', activeMenu: '/team/teamlist' }
      }
    ]
  },
  {
    path: '/team/teamlist-edit',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'teamManage/:teamId(\\d+)',
        component: () => import('@/views/team/teamlist/teamManage/index'),
        name: 'teamManage',
        meta: { title: '机构管理', activeMenu: '/team/teamlist' }
      }
    ]
  },
  {
    path: '/team/teamlist-safe',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'safeSet/:teamId(\\d+)',
        component: () => import('@/views/team/teamlist/safeSet/index'),
        name: 'SafeSet',
        meta: { title: '安全设置', activeMenu: '/team/teamlist' }
      }
    ]
  },
  {
    path: '/team/teamlist-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'operate/:teamId(\\d+)',
        component: () => import('@/views/team/teamlist/operate/index'),
        name: 'TeamOperate',
        meta: { title: '运营分析', activeMenu: '/team/teamlist' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
  {
    path: '/assessment/set-parameter',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'operate/:id(\\d+)',
        component: () => import('@/views/assessment/assessment/page/setParameter'),
        name: 'setParameter',
        meta: { title: '设置参数', activeMenu: '/assessment/assessment' }
      }
    ]
  },
  {
    path: '/assessment/view-evaluation',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'operate/:id(\\d+)',
        component: () => import('@/views/assessment/assessment/page/viewTplResults'),
        name: 'viewEvaluation',
        meta: { title: '查看模型结果', activeMenu: '/assessment/assessment' }
      }
    ]
  },
  {
    path: '/assessment/view-Report',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'operate/:id(\\d+)',
        component: () => import('@/views/assessment/assessment/page/createReport'),
        name: 'createReport',
        meta: { title: '预览模型结果', activeMenu: '/assessment/assessment' }
      }
    ]
  },
  {
    path: '/note/send-note',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'operate',
        component: () => import('@/views/assets/zcCaseManage/page/sendNote'),
        name: 'SendNote',
        meta: { title: '发送短信', activeMenu: '/assets/zccasemanage' }
      }
    ]
  },
  {
    path: '/lawsuit/refresh-launch',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'operate/:caseId(\\d+)',
        component: () => import('@/views/lawsuit/list/page/refreshLaunch'),
        name: 'refreshLaunch',
        meta: { title: '更新诉讼进度', activeMenu: '/lawsuit/lawsuitList' }
      }
    ]
  },
  {
    path: '/lawsuit/check-launch',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'operate/:caseId(\\d+)',
        component: () => import('@/views/lawsuit/list/page/checkLaunch'),
        name: 'checkLaunch',
        meta: { title: '查看诉讼进度', activeMenu: '/lawsuit/lawsuitList' }
      }
    ]
  },
  {
    path: '/finance/settlement-setting',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'record',
        component: () => import('@/views/finance/settlementSetting/page/record'),
        name: 'record',
        meta: { title: '费率操作记录', activeMenu: '/record/recordList' }
      }, {
        path: 'settlementDayRecord',
        component: () => import('@/views/finance/settlementSetting/page/settlementDayRecord'),
        name: 'settlementDayRecord',
        meta: { title: '结佣日操作记录', activeMenu: '/record/settlementDayRecord' }
      }, {
        path: 'previewPDF',
        component: () => import('@/views/finance/settlementCenter/page/previewPDF'),
        name: 'previewPDF',
        meta: { title: '预览PDF文件', activeMenu: '/finance/settlementCenter' }
      }
    ]
  },
  {
    path: '/finance/settlement-center',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'details/:id(\\d+)',
        component: () => import('@/views/finance/settlementCenter/page/settlementDetails'),
        name: 'settlementDetails',
        meta: { title: '结算详情', activeMenu: '/finance/settlementCenter' }
      }
    ]
  },
  {
    path: '/risk/calllog-details',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'caseDetails/:caseId(\\d+)',
        component: () => import('@/views/caseDetails/index'),
        name: 'riskCalllog',
        meta: { title: '案件详情', activeMenu: '/risk/calllog' }
      },
      {
        path: 'textPlay',
        component: () => import('@/views/risk/callLog/page/textPlay'),
        name: 'TextPlay',
        meta: { title: '文本播放', activeMenu: '/risk/textPlay' }
      },
    ]
  },
  {
    path: '/risk/list-qc',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'details/:id(\\d+)',
        component: () => import('@/views/risk/listQc/page/details'),
        name: 'listQcDetails',
        meta: { title: '查看详情', activeMenu: '/risk/listQc' }
      },
    ]
  },
  {
    path: '/template',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addTemplate',
        component: () => import('@/views/system/template/page/addTemplate'),
        name: 'addTemplate',
        meta: { title: '创建模板', activeMenu: '/system/templateIndex' }
      },
      {
        path: 'editTemplate/:id(\\d+)',
        component: () => import('@/views/system/template/page/addTemplate'),
        name: 'editTemplate',
        meta: { title: '编辑模板', activeMenu: '/system/templateIndex' }
      },
      {
        path: 'addPDFTemplate',
        component: () => import('@/views/system/template/page/addPDFTemplate'),
        name: 'addPDFTemplate',
        meta: { title: '创建模板', activeMenu: '/system/templateIndex' }
      },
      {
        path: 'editPDFTemplate/:id(\\d+)',
        component: () => import('@/views/system/template/page/addPDFTemplate'),
        name: 'editPDFTemplate',
        meta: { title: '编辑模板', activeMenu: '/system/templateIndex' }
      },
    ]
  },
  {
    path: '/workExamine',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'decisionLog',
        component: () => import('@/views/workExamine/decisionService/page/decisionLog'),
        name: 'decisionLog',
        meta: { title: '分案决策记录', activeMenu: '/decisionService/decisionLog' }
      },
      {
        path: 'policyName/:id(\\d+)',
        component: () => import('@/views/workExamine/decisionService/page/strategyIndex'),
        name: 'policyName',
        meta: { title: '设置分案决策', activeMenu: '/workExamine/decisionService' }
      },
    ]
  },
  {
    path: '/signature',
    component: Layout,
    hidden: true,
    children: [
      // /:id(\d+)
      {
        path: 'addLawyer',
        component: () => import('@/views/signature/signatureDocuments/page/addLawyer'),
        name: 'AddLawyer',
        meta: { title: '新建发函', activeMenu: '/signature/signatureDocuments' }
      },
      {
        path: 'SigDocsList/:id(\\d+)',
        component: () => import('@/views/signature/signatureDocuments/page/SigDocsList'),
        name: 'SigDocsList',
        meta: { title: '签章文件详情', activeMenu: '/signature/SigDocsList/' }
      },
    ]
  },
  {
    path: '/agingManage',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'agingDetails',
        component: () => import('@/views/agingManage/agingManage/page/agingDetails'),
        name: 'agingDetails',
        meta: { title: '查看详情', activeMenu: '/agingManage/agingDetails/' }
      },
      {
        path: 'AlVoiceTaskDetails',
        component: () => import('@/views/agingManage/AlVoiceTask/page/AlVoiceTaskDetails'),
        name: 'AlVoiceTaskDetails',
        meta: { title: '查看结果', activeMenu: '/agingManage/AlVoiceTaskDetails/' }
      },
    ]
  },
  {
    path: '/dueDiligence',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'projectInfo',
        component: () => import('@/views/dueDiligence/page/projectInfo.vue'),
        name: 'ProjectInfo',
        meta: { title: '查看详情', activeMenu: '/dueDiligence/projectInfo' }
      },
    ]
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
});

export default router;