<template>
    <div class="mt20">
        <el-form inline label-width="auto" :model="queryParmas">
            <el-form-item label="项目ID" prop="projectId">
                <el-input v-model="queryParmas.projectId" style="width: 320px;" placeholder="请输入项目ID" />
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="queryParmas.projectName" style="width: 320px;" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item label="产品类型" prop="productType">
                <el-input v-model="queryParmas.productType" style="width: 320px;" placeholder="请输入产品类型" />
            </el-form-item>
            <el-form-item label="立项状态" prop="projectStatus">
                <el-input v-model="queryParmas.projectStatus" style="width: 320px;" placeholder="请输入立项状态" />
            </el-form-item>
            <el-form-item label="申请人" prop="applicant">
                <el-input v-model="queryParmas.applicant" style="width: 320px;" placeholder="请输入申请人" />
            </el-form-item>
            <el-form-item label="申请时间" prop="applyTime">
                <el-date-picker v-model="queryParmas.applyTime" type="daterange" range-separator="-"
                    start-placeholder="开始日期" end-placeholder="结束日期" style="width: 320px;" />
            </el-form-item>
            <el-form-item label="债权总金额" prop="totalAmount">
                <div class="range-scope" style="width: 320px;">
                    <el-input v-model="queryParmas.totalAmountMin" placeholder="最小金额" />
                    <span>-</span>
                    <el-input v-model="queryParmas.totalAmountMax" placeholder="最大金额" />
                </div>
            </el-form-item>
            <el-form-item label="债权本金" prop="principalAmount">
                <div class="range-scope" style="width: 320px;">
                    <el-input v-model="queryParmas.principalAmountMin" placeholder="最小金额" />
                    <span>-</span>
                    <el-input v-model="queryParmas.principalAmountMax" placeholder="最大金额" />
                </div>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <SelectedAll />
        <el-tabs v-model="activetab" @tab-change="antiShake(handleQuery)">
            <el-tab-pane v-for="item in tabList" :key="item.code" :label="item.info" :name="item.code" />
        </el-tabs>
        <el-table :data="dataList">
            <el-table-column type="selection" width="44px" :selectable="selectable" align="right" />
            <el-table-column label="项目ID" width="120" prop="projectId" align="center" />
            <el-table-column label="项目名称" width="120" prop="projectName" align="center" />
            <el-table-column label="产品类型" width="120" prop="productType" align="center" />
            <el-table-column label="立项状态" width="120" prop="projectStatus" align="center" />
            <el-table-column label="资产转让方" width="120" prop="assetTransferor" align="center" />
            <el-table-column label="债权总金额（元）" width="160" prop="totalAmount" align="center" />
            <el-table-column label="债权本金（元）" width="120" prop="principalAmount" align="center" />
            <el-table-column label="户数" width="120" prop="accountCount" align="center" />
            <el-table-column label="基准日" width="120" prop="baselineDate" align="center" />
            <el-table-column label="预计竞价日期" width="120" prop="expectedBidDate" align="center" />
            <el-table-column label="投标方式" width="120" prop="biddingMethod" align="center" />
            <el-table-column label="报价上限" width="120" prop="priceLimit" align="center" />
            <el-table-column label="资产评估表" width="120" prop="assetEvaluation" align="center" />
            <el-table-column label="立项报告" width="120" prop="projectReport" align="center" />
            <el-table-column label="其他附件" width="120" prop="otherAttachments" align="center" />
            <el-table-column label="申请人" width="120" prop="applicant" align="center" />
            <el-table-column label="申请时间" width="120" prop="applyTime" align="center" />
            <el-table-column label="处理状态" width="120" prop="processStatus" align="center" />
            <el-table-column label="处理人" width="120" prop="processor" align="center" />
            <el-table-column label="处理时间" width="120" prop="processTime" align="center" />
            <el-table-column width="180" fixed="right" label="操作" align="center">
                <template #default="{ row }">
                    <el-button type="text" @click="handle(row)">通过</el-button>
                    <el-button type="text" @click="handle(row)">不通过</el-button>
                    <el-button type="text" @click="handleDetails(row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
const route = useRoute()
const router = useRouter()

const activetab = ref('0')
const queryParmas = ref({ pageNum: 1, pageSize: 10 })

const dataList = ref([
    {
        projectId: '***************',
        projectName: '2024年公开竞价项目0101',
        productType: '信贷',
        projectStatus: '立项阶段',
        assetTransferor: '某某银行',
        totalAmount: 10000.00,
        principalAmount: 8000.00,
        accountCount: '////',
        baselineDate: '2024-01-09',
        expectedBidDate: '2024-02-09',
        biddingMethod: '公开竞价',
        priceLimit: '查看',
        assetEvaluation: '查看',
        projectReport: '查看',
        otherAttachments: '查看',
        applicant: '张某某',
        applyTime: '2024-01-10 12:00:00',
        processStatus: '待处理',
        processor: '-',
        processTime: '-'
    },
    {
        projectId: '***************',
        projectName: '2024年公开竞价项目0102',
        productType: '信贷',
        projectStatus: '立项阶段',
        assetTransferor: '某某银行',
        totalAmount: 10000.00,
        principalAmount: null,
        accountCount: '?',
        baselineDate: null,
        expectedBidDate: null,
        biddingMethod: '查看',
        priceLimit: '查看',
        assetEvaluation: '-',
        projectReport: '查看',
        otherAttachments: '-',
        applicant: null,
        applyTime: '2024-01-10 12:00:00',
        processStatus: '待处理',
        processor: '-',
        processTime: '-'
    }
])

const tabList = ref([
    { code: '0', info: '待处理' },
    { code: '1', info: '已同意' },
    { code: '2', info: '未同意' },
    { code: '3', info: '已撤销' },
    { code: 'all', info: '全部' },
])

const handleQuery = () => {
    queryParmas.value.pageNum = 1
}

const handle = (row) => {
    console.log('处理行数据：', row)
}

function handleDetails(row) {
    const query = { path: route.path, pageType: 'startNapeList' , progressStatus: 0}
    router.push({ path: `/dueDiligence/projectInfo`, query })
}
const selectable = (row) => {
    // 根据状态判断是否可选
    return true
}

const resetQuery = () => {
    queryParmas.value = { pageNum: 1, pageSize: 10 }
}
</script>

<style lang="scss" scoped>
.range-scope {
    display: flex;

    span {
        margin: 0 10px;
    }
}
</style>