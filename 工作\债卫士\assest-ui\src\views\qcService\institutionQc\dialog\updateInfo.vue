<template>
    <el-dialog title="编辑" v-model="open" append-to-body width="650px" :before-close="cancel">
        <el-form ref="formRef" label-width="100px" :model="form" :rules="rules">
            <el-form-item prop="qualityStatus" label="状态">
                <el-radio-group v-model="form.qualityStatus">
                    <el-radio v-for="(v, i) in switchStatusEnum" :key="i" :label="+i">{{ v }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item prop="qualityRemark" label="备注">
                <el-input v-model="form.qualityRemark" type="textarea" show-word-limit maxlength="300" style="width:80%"
                    placeholder="请输入备注" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" type="primary" @click="submit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { switchStatusEnum } from '@/utils/enum';
import { editTeamQcApi } from "@/api/qcService/institutionQc";
const props = defineProps({
    getList: { type: Function, default: () => { } }
})
const { proxy } = getCurrentInstance()
const data = reactive({
    form: { qualityStatus: 0 },
    rules: {
        qualityRemark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
        qualityStatus: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    }
})
const loading = ref(false)
const open = ref(false)
const { form, rules } = toRefs(data)
function submit() {
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            const reqForm = JSON.parse(JSON.stringify(form.value))
            editTeamQcApi(reqForm).then(() => {
                cancel()
                props.getList && props.getList()
                proxy.$modal.msgSuccess('操作成功！')
            })
        }
    })
}

function openDialog(data) {
    open.value = true
    form.value = data
}
function cancel() {
    open.value = false
}

defineExpose({ openDialog })
</script>