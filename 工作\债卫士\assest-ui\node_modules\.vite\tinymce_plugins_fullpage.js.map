{"version": 3, "sources": ["../tinymce/plugins/fullpage/plugin.js", "../tinymce/plugins/fullpage/index.js", "dep:tinymce_plugins_fullpage"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.html.DomParser');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.html.Node');\n\n    var global = tinymce.util.Tools.resolve('tinymce.html.Serializer');\n\n    var shouldHideInSourceView = function (editor) {\n      return editor.getParam('fullpage_hide_in_source_view');\n    };\n    var getDefaultXmlPi = function (editor) {\n      return editor.getParam('fullpage_default_xml_pi');\n    };\n    var getDefaultEncoding = function (editor) {\n      return editor.getParam('fullpage_default_encoding');\n    };\n    var getDefaultFontFamily = function (editor) {\n      return editor.getParam('fullpage_default_font_family');\n    };\n    var getDefaultFontSize = function (editor) {\n      return editor.getParam('fullpage_default_font_size');\n    };\n    var getDefaultTextColor = function (editor) {\n      return editor.getParam('fullpage_default_text_color');\n    };\n    var getDefaultTitle = function (editor) {\n      return editor.getParam('fullpage_default_title');\n    };\n    var getDefaultDocType = function (editor) {\n      return editor.getParam('fullpage_default_doctype', '<!DOCTYPE html>');\n    };\n    var getProtect = function (editor) {\n      return editor.getParam('protect');\n    };\n\n    var parseHeader = function (editor, head) {\n      return global$2({\n        validate: false,\n        root_name: '#document'\n      }, editor.schema).parse(head, { format: 'xhtml' });\n    };\n    var htmlToData = function (editor, head) {\n      var headerFragment = parseHeader(editor, head);\n      var data = {};\n      var elm, matches;\n      var getAttr = function (elm, name) {\n        var value = elm.attr(name);\n        return value || '';\n      };\n      data.fontface = getDefaultFontFamily(editor);\n      data.fontsize = getDefaultFontSize(editor);\n      elm = headerFragment.firstChild;\n      if (elm.type === 7) {\n        data.xml_pi = true;\n        matches = /encoding=\"([^\"]+)\"/.exec(elm.value);\n        if (matches) {\n          data.docencoding = matches[1];\n        }\n      }\n      elm = headerFragment.getAll('#doctype')[0];\n      if (elm) {\n        data.doctype = '<!DOCTYPE' + elm.value + '>';\n      }\n      elm = headerFragment.getAll('title')[0];\n      if (elm && elm.firstChild) {\n        data.title = elm.firstChild.value;\n      }\n      global$3.each(headerFragment.getAll('meta'), function (meta) {\n        var name = meta.attr('name');\n        var httpEquiv = meta.attr('http-equiv');\n        var matches;\n        if (name) {\n          data[name.toLowerCase()] = meta.attr('content');\n        } else if (httpEquiv === 'Content-Type') {\n          matches = /charset\\s*=\\s*(.*)\\s*/gi.exec(meta.attr('content'));\n          if (matches) {\n            data.docencoding = matches[1];\n          }\n        }\n      });\n      elm = headerFragment.getAll('html')[0];\n      if (elm) {\n        data.langcode = getAttr(elm, 'lang') || getAttr(elm, 'xml:lang');\n      }\n      data.stylesheets = [];\n      global$3.each(headerFragment.getAll('link'), function (link) {\n        if (link.attr('rel') === 'stylesheet') {\n          data.stylesheets.push(link.attr('href'));\n        }\n      });\n      elm = headerFragment.getAll('body')[0];\n      if (elm) {\n        data.langdir = getAttr(elm, 'dir');\n        data.style = getAttr(elm, 'style');\n        data.visited_color = getAttr(elm, 'vlink');\n        data.link_color = getAttr(elm, 'link');\n        data.active_color = getAttr(elm, 'alink');\n      }\n      return data;\n    };\n    var dataToHtml = function (editor, data, head) {\n      var headElement, elm;\n      var dom = editor.dom;\n      var setAttr = function (elm, name, value) {\n        elm.attr(name, value ? value : undefined);\n      };\n      var addHeadNode = function (node) {\n        if (headElement.firstChild) {\n          headElement.insert(node, headElement.firstChild);\n        } else {\n          headElement.append(node);\n        }\n      };\n      var headerFragment = parseHeader(editor, head);\n      headElement = headerFragment.getAll('head')[0];\n      if (!headElement) {\n        elm = headerFragment.getAll('html')[0];\n        headElement = new global$1('head', 1);\n        if (elm.firstChild) {\n          elm.insert(headElement, elm.firstChild, true);\n        } else {\n          elm.append(headElement);\n        }\n      }\n      elm = headerFragment.firstChild;\n      if (data.xml_pi) {\n        var value = 'version=\"1.0\"';\n        if (data.docencoding) {\n          value += ' encoding=\"' + data.docencoding + '\"';\n        }\n        if (elm.type !== 7) {\n          elm = new global$1('xml', 7);\n          headerFragment.insert(elm, headerFragment.firstChild, true);\n        }\n        elm.value = value;\n      } else if (elm && elm.type === 7) {\n        elm.remove();\n      }\n      elm = headerFragment.getAll('#doctype')[0];\n      if (data.doctype) {\n        if (!elm) {\n          elm = new global$1('#doctype', 10);\n          if (data.xml_pi) {\n            headerFragment.insert(elm, headerFragment.firstChild);\n          } else {\n            addHeadNode(elm);\n          }\n        }\n        elm.value = data.doctype.substring(9, data.doctype.length - 1);\n      } else if (elm) {\n        elm.remove();\n      }\n      elm = null;\n      global$3.each(headerFragment.getAll('meta'), function (meta) {\n        if (meta.attr('http-equiv') === 'Content-Type') {\n          elm = meta;\n        }\n      });\n      if (data.docencoding) {\n        if (!elm) {\n          elm = new global$1('meta', 1);\n          elm.attr('http-equiv', 'Content-Type');\n          elm.shortEnded = true;\n          addHeadNode(elm);\n        }\n        elm.attr('content', 'text/html; charset=' + data.docencoding);\n      } else if (elm) {\n        elm.remove();\n      }\n      elm = headerFragment.getAll('title')[0];\n      if (data.title) {\n        if (!elm) {\n          elm = new global$1('title', 1);\n          addHeadNode(elm);\n        } else {\n          elm.empty();\n        }\n        elm.append(new global$1('#text', 3)).value = data.title;\n      } else if (elm) {\n        elm.remove();\n      }\n      global$3.each('keywords,description,author,copyright,robots'.split(','), function (name) {\n        var nodes = headerFragment.getAll('meta');\n        var i, meta;\n        var value = data[name];\n        for (i = 0; i < nodes.length; i++) {\n          meta = nodes[i];\n          if (meta.attr('name') === name) {\n            if (value) {\n              meta.attr('content', value);\n            } else {\n              meta.remove();\n            }\n            return;\n          }\n        }\n        if (value) {\n          elm = new global$1('meta', 1);\n          elm.attr('name', name);\n          elm.attr('content', value);\n          elm.shortEnded = true;\n          addHeadNode(elm);\n        }\n      });\n      var currentStyleSheetsMap = {};\n      global$3.each(headerFragment.getAll('link'), function (stylesheet) {\n        if (stylesheet.attr('rel') === 'stylesheet') {\n          currentStyleSheetsMap[stylesheet.attr('href')] = stylesheet;\n        }\n      });\n      global$3.each(data.stylesheets, function (stylesheet) {\n        if (!currentStyleSheetsMap[stylesheet]) {\n          elm = new global$1('link', 1);\n          elm.attr({\n            rel: 'stylesheet',\n            text: 'text/css',\n            href: stylesheet\n          });\n          elm.shortEnded = true;\n          addHeadNode(elm);\n        }\n        delete currentStyleSheetsMap[stylesheet];\n      });\n      global$3.each(currentStyleSheetsMap, function (stylesheet) {\n        stylesheet.remove();\n      });\n      elm = headerFragment.getAll('body')[0];\n      if (elm) {\n        setAttr(elm, 'dir', data.langdir);\n        setAttr(elm, 'style', data.style);\n        setAttr(elm, 'vlink', data.visited_color);\n        setAttr(elm, 'link', data.link_color);\n        setAttr(elm, 'alink', data.active_color);\n        dom.setAttribs(editor.getBody(), {\n          style: data.style,\n          dir: data.dir,\n          vLink: data.visited_color,\n          link: data.link_color,\n          aLink: data.active_color\n        });\n      }\n      elm = headerFragment.getAll('html')[0];\n      if (elm) {\n        setAttr(elm, 'lang', data.langcode);\n        setAttr(elm, 'xml:lang', data.langcode);\n      }\n      if (!headElement.firstChild) {\n        headElement.remove();\n      }\n      var html = global({\n        validate: false,\n        indent: true,\n        indent_before: 'head,html,body,meta,title,script,link,style',\n        indent_after: 'head,html,body,meta,title,script,link,style'\n      }).serialize(headerFragment);\n      return html.substring(0, html.indexOf('</body>'));\n    };\n\n    var open = function (editor, headState) {\n      var data = htmlToData(editor, headState.get());\n      var defaultData = {\n        title: '',\n        keywords: '',\n        description: '',\n        robots: '',\n        author: '',\n        docencoding: ''\n      };\n      var initialData = __assign(__assign({}, defaultData), data);\n      editor.windowManager.open({\n        title: 'Metadata and Document Properties',\n        size: 'normal',\n        body: {\n          type: 'panel',\n          items: [\n            {\n              name: 'title',\n              type: 'input',\n              label: 'Title'\n            },\n            {\n              name: 'keywords',\n              type: 'input',\n              label: 'Keywords'\n            },\n            {\n              name: 'description',\n              type: 'input',\n              label: 'Description'\n            },\n            {\n              name: 'robots',\n              type: 'input',\n              label: 'Robots'\n            },\n            {\n              name: 'author',\n              type: 'input',\n              label: 'Author'\n            },\n            {\n              name: 'docencoding',\n              type: 'input',\n              label: 'Encoding'\n            }\n          ]\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: initialData,\n        onSubmit: function (api) {\n          var nuData = api.getData();\n          var headHtml = dataToHtml(editor, global$3.extend(data, nuData), headState.get());\n          headState.set(headHtml);\n          api.close();\n        }\n      });\n    };\n\n    var register$1 = function (editor, headState) {\n      editor.addCommand('mceFullPageProperties', function () {\n        open(editor, headState);\n      });\n    };\n\n    var protectHtml = function (protect, html) {\n      global$3.each(protect, function (pattern) {\n        html = html.replace(pattern, function (str) {\n          return '<!--mce:protected ' + escape(str) + '-->';\n        });\n      });\n      return html;\n    };\n    var unprotectHtml = function (html) {\n      return html.replace(/<!--mce:protected ([\\s\\S]*?)-->/g, function (a, m) {\n        return unescape(m);\n      });\n    };\n\n    var each = global$3.each;\n    var low = function (s) {\n      return s.replace(/<\\/?[A-Z]+/g, function (a) {\n        return a.toLowerCase();\n      });\n    };\n    var handleSetContent = function (editor, headState, footState, evt) {\n      var startPos, endPos, content, styles = '';\n      var dom = editor.dom;\n      if (evt.selection) {\n        return;\n      }\n      content = protectHtml(getProtect(editor), evt.content);\n      if (evt.format === 'raw' && headState.get()) {\n        return;\n      }\n      if (evt.source_view && shouldHideInSourceView(editor)) {\n        return;\n      }\n      if (content.length === 0 && !evt.source_view) {\n        content = global$3.trim(headState.get()) + '\\n' + global$3.trim(content) + '\\n' + global$3.trim(footState.get());\n      }\n      content = content.replace(/<(\\/?)BODY/gi, '<$1body');\n      startPos = content.indexOf('<body');\n      if (startPos !== -1) {\n        startPos = content.indexOf('>', startPos);\n        headState.set(low(content.substring(0, startPos + 1)));\n        endPos = content.indexOf('</body', startPos);\n        if (endPos === -1) {\n          endPos = content.length;\n        }\n        evt.content = global$3.trim(content.substring(startPos + 1, endPos));\n        footState.set(low(content.substring(endPos)));\n      } else {\n        headState.set(getDefaultHeader(editor));\n        footState.set('\\n</body>\\n</html>');\n      }\n      var headerFragment = parseHeader(editor, headState.get());\n      each(headerFragment.getAll('style'), function (node) {\n        if (node.firstChild) {\n          styles += node.firstChild.value;\n        }\n      });\n      var bodyElm = headerFragment.getAll('body')[0];\n      if (bodyElm) {\n        dom.setAttribs(editor.getBody(), {\n          style: bodyElm.attr('style') || '',\n          dir: bodyElm.attr('dir') || '',\n          vLink: bodyElm.attr('vlink') || '',\n          link: bodyElm.attr('link') || '',\n          aLink: bodyElm.attr('alink') || ''\n        });\n      }\n      dom.remove('fullpage_styles');\n      var headElm = editor.getDoc().getElementsByTagName('head')[0];\n      if (styles) {\n        var styleElm = dom.add(headElm, 'style', { id: 'fullpage_styles' });\n        styleElm.appendChild(document.createTextNode(styles));\n      }\n      var currentStyleSheetsMap = {};\n      global$3.each(headElm.getElementsByTagName('link'), function (stylesheet) {\n        if (stylesheet.rel === 'stylesheet' && stylesheet.getAttribute('data-mce-fullpage')) {\n          currentStyleSheetsMap[stylesheet.href] = stylesheet;\n        }\n      });\n      global$3.each(headerFragment.getAll('link'), function (stylesheet) {\n        var href = stylesheet.attr('href');\n        if (!href) {\n          return true;\n        }\n        if (!currentStyleSheetsMap[href] && stylesheet.attr('rel') === 'stylesheet') {\n          dom.add(headElm, 'link', {\n            'rel': 'stylesheet',\n            'text': 'text/css',\n            href: href,\n            'data-mce-fullpage': '1'\n          });\n        }\n        delete currentStyleSheetsMap[href];\n      });\n      global$3.each(currentStyleSheetsMap, function (stylesheet) {\n        stylesheet.parentNode.removeChild(stylesheet);\n      });\n    };\n    var getDefaultHeader = function (editor) {\n      var header = '', value, styles = '';\n      if (getDefaultXmlPi(editor)) {\n        var piEncoding = getDefaultEncoding(editor);\n        header += '<?xml version=\"1.0\" encoding=\"' + (piEncoding ? piEncoding : 'ISO-8859-1') + '\" ?>\\n';\n      }\n      header += getDefaultDocType(editor);\n      header += '\\n<html>\\n<head>\\n';\n      if (value = getDefaultTitle(editor)) {\n        header += '<title>' + value + '</title>\\n';\n      }\n      if (value = getDefaultEncoding(editor)) {\n        header += '<meta http-equiv=\"Content-Type\" content=\"text/html; charset=' + value + '\" />\\n';\n      }\n      if (value = getDefaultFontFamily(editor)) {\n        styles += 'font-family: ' + value + ';';\n      }\n      if (value = getDefaultFontSize(editor)) {\n        styles += 'font-size: ' + value + ';';\n      }\n      if (value = getDefaultTextColor(editor)) {\n        styles += 'color: ' + value + ';';\n      }\n      header += '</head>\\n<body' + (styles ? ' style=\"' + styles + '\"' : '') + '>\\n';\n      return header;\n    };\n    var handleGetContent = function (editor, head, foot, evt) {\n      if (evt.format === 'html' && !evt.selection && (!evt.source_view || !shouldHideInSourceView(editor))) {\n        evt.content = unprotectHtml(global$3.trim(head) + '\\n' + global$3.trim(evt.content) + '\\n' + global$3.trim(foot));\n      }\n    };\n    var setup = function (editor, headState, footState) {\n      editor.on('BeforeSetContent', function (evt) {\n        handleSetContent(editor, headState, footState, evt);\n      });\n      editor.on('GetContent', function (evt) {\n        handleGetContent(editor, headState.get(), footState.get(), evt);\n      });\n    };\n\n    var register = function (editor) {\n      editor.ui.registry.addButton('fullpage', {\n        tooltip: 'Metadata and document properties',\n        icon: 'document-properties',\n        onAction: function () {\n          editor.execCommand('mceFullPageProperties');\n        }\n      });\n      editor.ui.registry.addMenuItem('fullpage', {\n        text: 'Metadata and document properties',\n        icon: 'document-properties',\n        onAction: function () {\n          editor.execCommand('mceFullPageProperties');\n        }\n      });\n    };\n\n    function Plugin () {\n      global$4.add('fullpage', function (editor) {\n        var headState = Cell(''), footState = Cell('');\n        register$1(editor, headState);\n        register(editor);\n        setup(editor, headState, footState);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"fullpage\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/fullpage')\n//   ES2015:\n//     import 'tinymce/plugins/fullpage'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/fullpage/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,QAAQ;AACZ,YAAI,MAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,kBAAQ;AAAA;AAEV,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,OAAO,SAAS,4BAA4B;AAAA;AAErD,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,OAAO,SAAS;AAAA;AAGzB,UAAI,cAAc,SAAU,QAAQ,MAAM;AACxC,eAAO,SAAS;AAAA,UACd,UAAU;AAAA,UACV,WAAW;AAAA,WACV,OAAO,QAAQ,MAAM,MAAM,EAAE,QAAQ;AAAA;AAE1C,UAAI,aAAa,SAAU,QAAQ,MAAM;AACvC,YAAI,iBAAiB,YAAY,QAAQ;AACzC,YAAI,OAAO;AACX,YAAI,KAAK;AACT,YAAI,UAAU,SAAU,MAAK,MAAM;AACjC,cAAI,QAAQ,KAAI,KAAK;AACrB,iBAAO,SAAS;AAAA;AAElB,aAAK,WAAW,qBAAqB;AACrC,aAAK,WAAW,mBAAmB;AACnC,cAAM,eAAe;AACrB,YAAI,IAAI,SAAS,GAAG;AAClB,eAAK,SAAS;AACd,oBAAU,qBAAqB,KAAK,IAAI;AACxC,cAAI,SAAS;AACX,iBAAK,cAAc,QAAQ;AAAA;AAAA;AAG/B,cAAM,eAAe,OAAO,YAAY;AACxC,YAAI,KAAK;AACP,eAAK,UAAU,cAAc,IAAI,QAAQ;AAAA;AAE3C,cAAM,eAAe,OAAO,SAAS;AACrC,YAAI,OAAO,IAAI,YAAY;AACzB,eAAK,QAAQ,IAAI,WAAW;AAAA;AAE9B,iBAAS,KAAK,eAAe,OAAO,SAAS,SAAU,MAAM;AAC3D,cAAI,OAAO,KAAK,KAAK;AACrB,cAAI,YAAY,KAAK,KAAK;AAC1B,cAAI;AACJ,cAAI,MAAM;AACR,iBAAK,KAAK,iBAAiB,KAAK,KAAK;AAAA,qBAC5B,cAAc,gBAAgB;AACvC,uBAAU,0BAA0B,KAAK,KAAK,KAAK;AACnD,gBAAI,UAAS;AACX,mBAAK,cAAc,SAAQ;AAAA;AAAA;AAAA;AAIjC,cAAM,eAAe,OAAO,QAAQ;AACpC,YAAI,KAAK;AACP,eAAK,WAAW,QAAQ,KAAK,WAAW,QAAQ,KAAK;AAAA;AAEvD,aAAK,cAAc;AACnB,iBAAS,KAAK,eAAe,OAAO,SAAS,SAAU,MAAM;AAC3D,cAAI,KAAK,KAAK,WAAW,cAAc;AACrC,iBAAK,YAAY,KAAK,KAAK,KAAK;AAAA;AAAA;AAGpC,cAAM,eAAe,OAAO,QAAQ;AACpC,YAAI,KAAK;AACP,eAAK,UAAU,QAAQ,KAAK;AAC5B,eAAK,QAAQ,QAAQ,KAAK;AAC1B,eAAK,gBAAgB,QAAQ,KAAK;AAClC,eAAK,aAAa,QAAQ,KAAK;AAC/B,eAAK,eAAe,QAAQ,KAAK;AAAA;AAEnC,eAAO;AAAA;AAET,UAAI,aAAa,SAAU,QAAQ,MAAM,MAAM;AAC7C,YAAI,aAAa;AACjB,YAAI,MAAM,OAAO;AACjB,YAAI,UAAU,SAAU,MAAK,MAAM,QAAO;AACxC,eAAI,KAAK,MAAM,SAAQ,SAAQ;AAAA;AAEjC,YAAI,cAAc,SAAU,MAAM;AAChC,cAAI,YAAY,YAAY;AAC1B,wBAAY,OAAO,MAAM,YAAY;AAAA,iBAChC;AACL,wBAAY,OAAO;AAAA;AAAA;AAGvB,YAAI,iBAAiB,YAAY,QAAQ;AACzC,sBAAc,eAAe,OAAO,QAAQ;AAC5C,YAAI,CAAC,aAAa;AAChB,gBAAM,eAAe,OAAO,QAAQ;AACpC,wBAAc,IAAI,SAAS,QAAQ;AACnC,cAAI,IAAI,YAAY;AAClB,gBAAI,OAAO,aAAa,IAAI,YAAY;AAAA,iBACnC;AACL,gBAAI,OAAO;AAAA;AAAA;AAGf,cAAM,eAAe;AACrB,YAAI,KAAK,QAAQ;AACf,cAAI,QAAQ;AACZ,cAAI,KAAK,aAAa;AACpB,qBAAS,gBAAgB,KAAK,cAAc;AAAA;AAE9C,cAAI,IAAI,SAAS,GAAG;AAClB,kBAAM,IAAI,SAAS,OAAO;AAC1B,2BAAe,OAAO,KAAK,eAAe,YAAY;AAAA;AAExD,cAAI,QAAQ;AAAA,mBACH,OAAO,IAAI,SAAS,GAAG;AAChC,cAAI;AAAA;AAEN,cAAM,eAAe,OAAO,YAAY;AACxC,YAAI,KAAK,SAAS;AAChB,cAAI,CAAC,KAAK;AACR,kBAAM,IAAI,SAAS,YAAY;AAC/B,gBAAI,KAAK,QAAQ;AACf,6BAAe,OAAO,KAAK,eAAe;AAAA,mBACrC;AACL,0BAAY;AAAA;AAAA;AAGhB,cAAI,QAAQ,KAAK,QAAQ,UAAU,GAAG,KAAK,QAAQ,SAAS;AAAA,mBACnD,KAAK;AACd,cAAI;AAAA;AAEN,cAAM;AACN,iBAAS,KAAK,eAAe,OAAO,SAAS,SAAU,MAAM;AAC3D,cAAI,KAAK,KAAK,kBAAkB,gBAAgB;AAC9C,kBAAM;AAAA;AAAA;AAGV,YAAI,KAAK,aAAa;AACpB,cAAI,CAAC,KAAK;AACR,kBAAM,IAAI,SAAS,QAAQ;AAC3B,gBAAI,KAAK,cAAc;AACvB,gBAAI,aAAa;AACjB,wBAAY;AAAA;AAEd,cAAI,KAAK,WAAW,wBAAwB,KAAK;AAAA,mBACxC,KAAK;AACd,cAAI;AAAA;AAEN,cAAM,eAAe,OAAO,SAAS;AACrC,YAAI,KAAK,OAAO;AACd,cAAI,CAAC,KAAK;AACR,kBAAM,IAAI,SAAS,SAAS;AAC5B,wBAAY;AAAA,iBACP;AACL,gBAAI;AAAA;AAEN,cAAI,OAAO,IAAI,SAAS,SAAS,IAAI,QAAQ,KAAK;AAAA,mBACzC,KAAK;AACd,cAAI;AAAA;AAEN,iBAAS,KAAK,+CAA+C,MAAM,MAAM,SAAU,MAAM;AACvF,cAAI,QAAQ,eAAe,OAAO;AAClC,cAAI,GAAG;AACP,cAAI,SAAQ,KAAK;AACjB,eAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,mBAAO,MAAM;AACb,gBAAI,KAAK,KAAK,YAAY,MAAM;AAC9B,kBAAI,QAAO;AACT,qBAAK,KAAK,WAAW;AAAA,qBAChB;AACL,qBAAK;AAAA;AAEP;AAAA;AAAA;AAGJ,cAAI,QAAO;AACT,kBAAM,IAAI,SAAS,QAAQ;AAC3B,gBAAI,KAAK,QAAQ;AACjB,gBAAI,KAAK,WAAW;AACpB,gBAAI,aAAa;AACjB,wBAAY;AAAA;AAAA;AAGhB,YAAI,wBAAwB;AAC5B,iBAAS,KAAK,eAAe,OAAO,SAAS,SAAU,YAAY;AACjE,cAAI,WAAW,KAAK,WAAW,cAAc;AAC3C,kCAAsB,WAAW,KAAK,WAAW;AAAA;AAAA;AAGrD,iBAAS,KAAK,KAAK,aAAa,SAAU,YAAY;AACpD,cAAI,CAAC,sBAAsB,aAAa;AACtC,kBAAM,IAAI,SAAS,QAAQ;AAC3B,gBAAI,KAAK;AAAA,cACP,KAAK;AAAA,cACL,MAAM;AAAA,cACN,MAAM;AAAA;AAER,gBAAI,aAAa;AACjB,wBAAY;AAAA;AAEd,iBAAO,sBAAsB;AAAA;AAE/B,iBAAS,KAAK,uBAAuB,SAAU,YAAY;AACzD,qBAAW;AAAA;AAEb,cAAM,eAAe,OAAO,QAAQ;AACpC,YAAI,KAAK;AACP,kBAAQ,KAAK,OAAO,KAAK;AACzB,kBAAQ,KAAK,SAAS,KAAK;AAC3B,kBAAQ,KAAK,SAAS,KAAK;AAC3B,kBAAQ,KAAK,QAAQ,KAAK;AAC1B,kBAAQ,KAAK,SAAS,KAAK;AAC3B,cAAI,WAAW,OAAO,WAAW;AAAA,YAC/B,OAAO,KAAK;AAAA,YACZ,KAAK,KAAK;AAAA,YACV,OAAO,KAAK;AAAA,YACZ,MAAM,KAAK;AAAA,YACX,OAAO,KAAK;AAAA;AAAA;AAGhB,cAAM,eAAe,OAAO,QAAQ;AACpC,YAAI,KAAK;AACP,kBAAQ,KAAK,QAAQ,KAAK;AAC1B,kBAAQ,KAAK,YAAY,KAAK;AAAA;AAEhC,YAAI,CAAC,YAAY,YAAY;AAC3B,sBAAY;AAAA;AAEd,YAAI,OAAO,OAAO;AAAA,UAChB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,eAAe;AAAA,UACf,cAAc;AAAA,WACb,UAAU;AACb,eAAO,KAAK,UAAU,GAAG,KAAK,QAAQ;AAAA;AAGxC,UAAI,OAAO,SAAU,QAAQ,WAAW;AACtC,YAAI,OAAO,WAAW,QAAQ,UAAU;AACxC,YAAI,cAAc;AAAA,UAChB,OAAO;AAAA,UACP,UAAU;AAAA,UACV,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,aAAa;AAAA;AAEf,YAAI,cAAc,SAAS,SAAS,IAAI,cAAc;AACtD,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,cACL;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA;AAAA,cAET;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA;AAAA,cAET;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA;AAAA,cAET;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA;AAAA,cAET;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA;AAAA,cAET;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA;AAAA;AAAA;AAAA,UAIb,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA;AAAA,YAER;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,UAGb;AAAA,UACA,UAAU,SAAU,KAAK;AACvB,gBAAI,SAAS,IAAI;AACjB,gBAAI,WAAW,WAAW,QAAQ,SAAS,OAAO,MAAM,SAAS,UAAU;AAC3E,sBAAU,IAAI;AACd,gBAAI;AAAA;AAAA;AAAA;AAKV,UAAI,aAAa,SAAU,QAAQ,WAAW;AAC5C,eAAO,WAAW,yBAAyB,WAAY;AACrD,eAAK,QAAQ;AAAA;AAAA;AAIjB,UAAI,cAAc,SAAU,SAAS,MAAM;AACzC,iBAAS,KAAK,SAAS,SAAU,SAAS;AACxC,iBAAO,KAAK,QAAQ,SAAS,SAAU,KAAK;AAC1C,mBAAO,uBAAuB,OAAO,OAAO;AAAA;AAAA;AAGhD,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,MAAM;AAClC,eAAO,KAAK,QAAQ,oCAAoC,SAAU,GAAG,GAAG;AACtE,iBAAO,SAAS;AAAA;AAAA;AAIpB,UAAI,OAAO,SAAS;AACpB,UAAI,MAAM,SAAU,GAAG;AACrB,eAAO,EAAE,QAAQ,eAAe,SAAU,GAAG;AAC3C,iBAAO,EAAE;AAAA;AAAA;AAGb,UAAI,mBAAmB,SAAU,QAAQ,WAAW,WAAW,KAAK;AAClE,YAAI,UAAU,QAAQ,SAAS,SAAS;AACxC,YAAI,MAAM,OAAO;AACjB,YAAI,IAAI,WAAW;AACjB;AAAA;AAEF,kBAAU,YAAY,WAAW,SAAS,IAAI;AAC9C,YAAI,IAAI,WAAW,SAAS,UAAU,OAAO;AAC3C;AAAA;AAEF,YAAI,IAAI,eAAe,uBAAuB,SAAS;AACrD;AAAA;AAEF,YAAI,QAAQ,WAAW,KAAK,CAAC,IAAI,aAAa;AAC5C,oBAAU,SAAS,KAAK,UAAU,SAAS,OAAO,SAAS,KAAK,WAAW,OAAO,SAAS,KAAK,UAAU;AAAA;AAE5G,kBAAU,QAAQ,QAAQ,gBAAgB;AAC1C,mBAAW,QAAQ,QAAQ;AAC3B,YAAI,aAAa,IAAI;AACnB,qBAAW,QAAQ,QAAQ,KAAK;AAChC,oBAAU,IAAI,IAAI,QAAQ,UAAU,GAAG,WAAW;AAClD,mBAAS,QAAQ,QAAQ,UAAU;AACnC,cAAI,WAAW,IAAI;AACjB,qBAAS,QAAQ;AAAA;AAEnB,cAAI,UAAU,SAAS,KAAK,QAAQ,UAAU,WAAW,GAAG;AAC5D,oBAAU,IAAI,IAAI,QAAQ,UAAU;AAAA,eAC/B;AACL,oBAAU,IAAI,iBAAiB;AAC/B,oBAAU,IAAI;AAAA;AAEhB,YAAI,iBAAiB,YAAY,QAAQ,UAAU;AACnD,aAAK,eAAe,OAAO,UAAU,SAAU,MAAM;AACnD,cAAI,KAAK,YAAY;AACnB,sBAAU,KAAK,WAAW;AAAA;AAAA;AAG9B,YAAI,UAAU,eAAe,OAAO,QAAQ;AAC5C,YAAI,SAAS;AACX,cAAI,WAAW,OAAO,WAAW;AAAA,YAC/B,OAAO,QAAQ,KAAK,YAAY;AAAA,YAChC,KAAK,QAAQ,KAAK,UAAU;AAAA,YAC5B,OAAO,QAAQ,KAAK,YAAY;AAAA,YAChC,MAAM,QAAQ,KAAK,WAAW;AAAA,YAC9B,OAAO,QAAQ,KAAK,YAAY;AAAA;AAAA;AAGpC,YAAI,OAAO;AACX,YAAI,UAAU,OAAO,SAAS,qBAAqB,QAAQ;AAC3D,YAAI,QAAQ;AACV,cAAI,WAAW,IAAI,IAAI,SAAS,SAAS,EAAE,IAAI;AAC/C,mBAAS,YAAY,SAAS,eAAe;AAAA;AAE/C,YAAI,wBAAwB;AAC5B,iBAAS,KAAK,QAAQ,qBAAqB,SAAS,SAAU,YAAY;AACxE,cAAI,WAAW,QAAQ,gBAAgB,WAAW,aAAa,sBAAsB;AACnF,kCAAsB,WAAW,QAAQ;AAAA;AAAA;AAG7C,iBAAS,KAAK,eAAe,OAAO,SAAS,SAAU,YAAY;AACjE,cAAI,OAAO,WAAW,KAAK;AAC3B,cAAI,CAAC,MAAM;AACT,mBAAO;AAAA;AAET,cAAI,CAAC,sBAAsB,SAAS,WAAW,KAAK,WAAW,cAAc;AAC3E,gBAAI,IAAI,SAAS,QAAQ;AAAA,cACvB,OAAO;AAAA,cACP,QAAQ;AAAA,cACR;AAAA,cACA,qBAAqB;AAAA;AAAA;AAGzB,iBAAO,sBAAsB;AAAA;AAE/B,iBAAS,KAAK,uBAAuB,SAAU,YAAY;AACzD,qBAAW,WAAW,YAAY;AAAA;AAAA;AAGtC,UAAI,mBAAmB,SAAU,QAAQ;AACvC,YAAI,SAAS,IAAI,OAAO,SAAS;AACjC,YAAI,gBAAgB,SAAS;AAC3B,cAAI,aAAa,mBAAmB;AACpC,oBAAU,mCAAoC,cAAa,aAAa,gBAAgB;AAAA;AAE1F,kBAAU,kBAAkB;AAC5B,kBAAU;AACV,YAAI,QAAQ,gBAAgB,SAAS;AACnC,oBAAU,YAAY,QAAQ;AAAA;AAEhC,YAAI,QAAQ,mBAAmB,SAAS;AACtC,oBAAU,iEAAiE,QAAQ;AAAA;AAErF,YAAI,QAAQ,qBAAqB,SAAS;AACxC,oBAAU,kBAAkB,QAAQ;AAAA;AAEtC,YAAI,QAAQ,mBAAmB,SAAS;AACtC,oBAAU,gBAAgB,QAAQ;AAAA;AAEpC,YAAI,QAAQ,oBAAoB,SAAS;AACvC,oBAAU,YAAY,QAAQ;AAAA;AAEhC,kBAAU,mBAAoB,UAAS,aAAa,SAAS,MAAM,MAAM;AACzE,eAAO;AAAA;AAET,UAAI,mBAAmB,SAAU,QAAQ,MAAM,MAAM,KAAK;AACxD,YAAI,IAAI,WAAW,UAAU,CAAC,IAAI,aAAc,EAAC,IAAI,eAAe,CAAC,uBAAuB,UAAU;AACpG,cAAI,UAAU,cAAc,SAAS,KAAK,QAAQ,OAAO,SAAS,KAAK,IAAI,WAAW,OAAO,SAAS,KAAK;AAAA;AAAA;AAG/G,UAAI,QAAQ,SAAU,QAAQ,WAAW,WAAW;AAClD,eAAO,GAAG,oBAAoB,SAAU,KAAK;AAC3C,2BAAiB,QAAQ,WAAW,WAAW;AAAA;AAEjD,eAAO,GAAG,cAAc,SAAU,KAAK;AACrC,2BAAiB,QAAQ,UAAU,OAAO,UAAU,OAAO;AAAA;AAAA;AAI/D,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,GAAG,SAAS,UAAU,YAAY;AAAA,UACvC,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,mBAAO,YAAY;AAAA;AAAA;AAGvB,eAAO,GAAG,SAAS,YAAY,YAAY;AAAA,UACzC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,mBAAO,YAAY;AAAA;AAAA;AAAA;AAKzB,wBAAmB;AACjB,iBAAS,IAAI,YAAY,SAAU,QAAQ;AACzC,cAAI,YAAY,KAAK,KAAK,YAAY,KAAK;AAC3C,qBAAW,QAAQ;AACnB,mBAAS;AACT,gBAAM,QAAQ,WAAW;AAAA;AAAA;AAI7B;AAAA;AAAA;AAAA;;;AC7hBJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,mCAAQ;", "names": []}