{"version": 3, "sources": ["../tinymce/plugins/paste/plugin.js", "../tinymce/plugins/paste/index.js", "dep:tinymce_plugins_paste"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global$b = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var hasProPlugin = function (editor) {\n      if (editor.hasPlugin('powerpaste', true)) {\n        if (typeof window.console !== 'undefined' && window.console.log) {\n          window.console.log('PowerPaste is incompatible with Paste plugin! Remove \\'paste\\' from the \\'plugins\\' option.');\n        }\n        return true;\n      } else {\n        return false;\n      }\n    };\n\n    var get = function (clipboard) {\n      return { clipboard: clipboard };\n    };\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var isArray = isType('array');\n    var isNullable = function (a) {\n      return a === null || a === undefined;\n    };\n    var isNonNullable = function (a) {\n      return !isNullable(a);\n    };\n    var isFunction = isSimpleType('function');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from$1 = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from$1\n    };\n\n    var nativeSlice = Array.prototype.slice;\n    var nativePush = Array.prototype.push;\n    var exists = function (xs, pred) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var filter$1 = function (xs, pred) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    var foldl = function (xs, f, acc) {\n      each(xs, function (x, i) {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    var flatten = function (xs) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    var bind = function (xs, f) {\n      return flatten(map(xs, f));\n    };\n    var from = isFunction(Array.from) ? Array.from : function (x) {\n      return nativeSlice.call(x);\n    };\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var singleton = function (doRevoke) {\n      var subject = Cell(Optional.none());\n      var revoke = function () {\n        return subject.get().each(doRevoke);\n      };\n      var clear = function () {\n        revoke();\n        subject.set(Optional.none());\n      };\n      var isSet = function () {\n        return subject.get().isSome();\n      };\n      var get = function () {\n        return subject.get();\n      };\n      var set = function (s) {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear: clear,\n        isSet: isSet,\n        get: get,\n        set: set\n      };\n    };\n    var value = function () {\n      var subject = singleton(noop);\n      var on = function (f) {\n        return subject.get().each(f);\n      };\n      return __assign(__assign({}, subject), { on: on });\n    };\n\n    var checkRange = function (str, substr, start) {\n      return substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    };\n    var startsWith = function (str, prefix) {\n      return checkRange(str, prefix, 0);\n    };\n    var endsWith = function (str, suffix) {\n      return checkRange(str, suffix, str.length - suffix.length);\n    };\n    var repeat = function (s, count) {\n      return count <= 0 ? '' : new Array(count + 1).join(s);\n    };\n\n    var global$a = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global$9 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var global$8 = tinymce.util.Tools.resolve('tinymce.util.Promise');\n\n    var global$7 = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    var firePastePreProcess = function (editor, html, internal, isWordHtml) {\n      return editor.fire('PastePreProcess', {\n        content: html,\n        internal: internal,\n        wordContent: isWordHtml\n      });\n    };\n    var firePastePostProcess = function (editor, node, internal, isWordHtml) {\n      return editor.fire('PastePostProcess', {\n        node: node,\n        internal: internal,\n        wordContent: isWordHtml\n      });\n    };\n    var firePastePlainTextToggle = function (editor, state) {\n      return editor.fire('PastePlainTextToggle', { state: state });\n    };\n    var firePaste = function (editor, ieFake) {\n      return editor.fire('paste', { ieFake: ieFake });\n    };\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var shouldBlockDrop = function (editor) {\n      return editor.getParam('paste_block_drop', false);\n    };\n    var shouldPasteDataImages = function (editor) {\n      return editor.getParam('paste_data_images', false);\n    };\n    var shouldFilterDrop = function (editor) {\n      return editor.getParam('paste_filter_drop', true);\n    };\n    var getPreProcess = function (editor) {\n      return editor.getParam('paste_preprocess');\n    };\n    var getPostProcess = function (editor) {\n      return editor.getParam('paste_postprocess');\n    };\n    var getWebkitStyles = function (editor) {\n      return editor.getParam('paste_webkit_styles');\n    };\n    var shouldRemoveWebKitStyles = function (editor) {\n      return editor.getParam('paste_remove_styles_if_webkit', true);\n    };\n    var shouldMergeFormats = function (editor) {\n      return editor.getParam('paste_merge_formats', true);\n    };\n    var isSmartPasteEnabled = function (editor) {\n      return editor.getParam('smart_paste', true);\n    };\n    var isPasteAsTextEnabled = function (editor) {\n      return editor.getParam('paste_as_text', false);\n    };\n    var getRetainStyleProps = function (editor) {\n      return editor.getParam('paste_retain_style_properties');\n    };\n    var getWordValidElements = function (editor) {\n      var defaultValidElements = '-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,' + '-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,' + 'td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody';\n      return editor.getParam('paste_word_valid_elements', defaultValidElements);\n    };\n    var shouldConvertWordFakeLists = function (editor) {\n      return editor.getParam('paste_convert_word_fake_lists', true);\n    };\n    var shouldUseDefaultFilters = function (editor) {\n      return editor.getParam('paste_enable_default_filters', true);\n    };\n    var getValidate = function (editor) {\n      return editor.getParam('validate');\n    };\n    var getAllowHtmlDataUrls = function (editor) {\n      return editor.getParam('allow_html_data_urls', false, 'boolean');\n    };\n    var getPasteDataImages = function (editor) {\n      return editor.getParam('paste_data_images', false, 'boolean');\n    };\n    var getImagesDataImgFilter = function (editor) {\n      return editor.getParam('images_dataimg_filter');\n    };\n    var getImagesReuseFilename = function (editor) {\n      return editor.getParam('images_reuse_filename');\n    };\n    var getForcedRootBlock = function (editor) {\n      return editor.getParam('forced_root_block');\n    };\n    var getForcedRootBlockAttrs = function (editor) {\n      return editor.getParam('forced_root_block_attrs');\n    };\n    var getTabSpaces = function (editor) {\n      return editor.getParam('paste_tab_spaces', 4, 'number');\n    };\n    var getAllowedImageFileTypes = function (editor) {\n      var defaultImageFileTypes = 'jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp';\n      return global$6.explode(editor.getParam('images_file_types', defaultImageFileTypes, 'string'));\n    };\n\n    var internalMimeType = 'x-tinymce/html';\n    var internalMark = '<!-- ' + internalMimeType + ' -->';\n    var mark = function (html) {\n      return internalMark + html;\n    };\n    var unmark = function (html) {\n      return html.replace(internalMark, '');\n    };\n    var isMarked = function (html) {\n      return html.indexOf(internalMark) !== -1;\n    };\n    var internalHtmlMime = constant(internalMimeType);\n\n    var hasOwnProperty = Object.hasOwnProperty;\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.html.Entities');\n\n    var isPlainText = function (text) {\n      return !/<(?:\\/?(?!(?:div|p|br|span)>)\\w+|(?:(?!(?:span style=\"white-space:\\s?pre;?\">)|br\\s?\\/>))\\w+\\s[^>]+)>/i.test(text);\n    };\n    var toBRs = function (text) {\n      return text.replace(/\\r?\\n/g, '<br>');\n    };\n    var openContainer = function (rootTag, rootAttrs) {\n      var attrs = [];\n      var tag = '<' + rootTag;\n      if (typeof rootAttrs === 'object') {\n        for (var key in rootAttrs) {\n          if (has(rootAttrs, key)) {\n            attrs.push(key + '=\"' + global$5.encodeAllRaw(rootAttrs[key]) + '\"');\n          }\n        }\n        if (attrs.length) {\n          tag += ' ' + attrs.join(' ');\n        }\n      }\n      return tag + '>';\n    };\n    var toBlockElements = function (text, rootTag, rootAttrs) {\n      var blocks = text.split(/\\n\\n/);\n      var tagOpen = openContainer(rootTag, rootAttrs);\n      var tagClose = '</' + rootTag + '>';\n      var paragraphs = global$6.map(blocks, function (p) {\n        return p.split(/\\n/).join('<br />');\n      });\n      var stitch = function (p) {\n        return tagOpen + p + tagClose;\n      };\n      return paragraphs.length === 1 ? paragraphs[0] : global$6.map(paragraphs, stitch).join('');\n    };\n    var convert = function (text, rootTag, rootAttrs) {\n      return rootTag ? toBlockElements(text, rootTag === true ? 'p' : rootTag, rootAttrs) : toBRs(text);\n    };\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.html.DomParser');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.html.Serializer');\n\n    var nbsp = '\\xA0';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.html.Node');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.html.Schema');\n\n    var isRegExp = function (val) {\n      return val.constructor === RegExp;\n    };\n    var filter = function (content, items) {\n      global$6.each(items, function (v) {\n        if (isRegExp(v)) {\n          content = content.replace(v, '');\n        } else {\n          content = content.replace(v[0], v[1]);\n        }\n      });\n      return content;\n    };\n    var innerText = function (html) {\n      var schema = global$1();\n      var domParser = global$4({}, schema);\n      var text = '';\n      var shortEndedElements = schema.getShortEndedElements();\n      var ignoreElements = global$6.makeMap('script noscript style textarea video audio iframe object', ' ');\n      var blockElements = schema.getBlockElements();\n      var walk = function (node) {\n        var name = node.name, currentNode = node;\n        if (name === 'br') {\n          text += '\\n';\n          return;\n        }\n        if (name === 'wbr') {\n          return;\n        }\n        if (shortEndedElements[name]) {\n          text += ' ';\n        }\n        if (ignoreElements[name]) {\n          text += ' ';\n          return;\n        }\n        if (node.type === 3) {\n          text += node.value;\n        }\n        if (!node.shortEnded) {\n          if (node = node.firstChild) {\n            do {\n              walk(node);\n            } while (node = node.next);\n          }\n        }\n        if (blockElements[name] && currentNode.next) {\n          text += '\\n';\n          if (name === 'p') {\n            text += '\\n';\n          }\n        }\n      };\n      html = filter(html, [/<!\\[[^\\]]+\\]>/g]);\n      walk(domParser.parse(html));\n      return text;\n    };\n    var trimHtml = function (html) {\n      var trimSpaces = function (all, s1, s2) {\n        if (!s1 && !s2) {\n          return ' ';\n        }\n        return nbsp;\n      };\n      html = filter(html, [\n        /^[\\s\\S]*<body[^>]*>\\s*|\\s*<\\/body[^>]*>[\\s\\S]*$/ig,\n        /<!--StartFragment-->|<!--EndFragment-->/g,\n        [\n          /( ?)<span class=\"Apple-converted-space\">\\u00a0<\\/span>( ?)/g,\n          trimSpaces\n        ],\n        /<br class=\"Apple-interchange-newline\">/g,\n        /<br>$/i\n      ]);\n      return html;\n    };\n    var createIdGenerator = function (prefix) {\n      var count = 0;\n      return function () {\n        return prefix + count++;\n      };\n    };\n    var getImageMimeType = function (ext) {\n      var lowerExt = ext.toLowerCase();\n      var mimeOverrides = {\n        jpg: 'jpeg',\n        jpe: 'jpeg',\n        jfi: 'jpeg',\n        jif: 'jpeg',\n        jfif: 'jpeg',\n        pjpeg: 'jpeg',\n        pjp: 'jpeg',\n        svg: 'svg+xml'\n      };\n      return global$6.hasOwn(mimeOverrides, lowerExt) ? 'image/' + mimeOverrides[lowerExt] : 'image/' + lowerExt;\n    };\n\n    var isWordContent = function (content) {\n      return /<font face=\"Times New Roman\"|class=\"?Mso|style=\"[^\"]*\\bmso-|style='[^']*\\bmso-|w:WordDocument/i.test(content) || /class=\"OutlineElement/.test(content) || /id=\"?docs\\-internal\\-guid\\-/.test(content);\n    };\n    var isNumericList = function (text) {\n      var found = false;\n      var patterns = [\n        /^[IVXLMCD]+\\.[ \\u00a0]/,\n        /^[ivxlmcd]+\\.[ \\u00a0]/,\n        /^[a-z]{1,2}[\\.\\)][ \\u00a0]/,\n        /^[A-Z]{1,2}[\\.\\)][ \\u00a0]/,\n        /^[0-9]+\\.[ \\u00a0]/,\n        /^[\\u3007\\u4e00\\u4e8c\\u4e09\\u56db\\u4e94\\u516d\\u4e03\\u516b\\u4e5d]+\\.[ \\u00a0]/,\n        /^[\\u58f1\\u5f10\\u53c2\\u56db\\u4f0d\\u516d\\u4e03\\u516b\\u4e5d\\u62fe]+\\.[ \\u00a0]/\n      ];\n      text = text.replace(/^[\\u00a0 ]+/, '');\n      global$6.each(patterns, function (pattern) {\n        if (pattern.test(text)) {\n          found = true;\n          return false;\n        }\n      });\n      return found;\n    };\n    var isBulletList = function (text) {\n      return /^[\\s\\u00a0]*[\\u2022\\u00b7\\u00a7\\u25CF]\\s*/.test(text);\n    };\n    var convertFakeListsToProperLists = function (node) {\n      var currentListNode, prevListNode, lastLevel = 1;\n      var getText = function (node) {\n        var txt = '';\n        if (node.type === 3) {\n          return node.value;\n        }\n        if (node = node.firstChild) {\n          do {\n            txt += getText(node);\n          } while (node = node.next);\n        }\n        return txt;\n      };\n      var trimListStart = function (node, regExp) {\n        if (node.type === 3) {\n          if (regExp.test(node.value)) {\n            node.value = node.value.replace(regExp, '');\n            return false;\n          }\n        }\n        if (node = node.firstChild) {\n          do {\n            if (!trimListStart(node, regExp)) {\n              return false;\n            }\n          } while (node = node.next);\n        }\n        return true;\n      };\n      var removeIgnoredNodes = function (node) {\n        if (node._listIgnore) {\n          node.remove();\n          return;\n        }\n        if (node = node.firstChild) {\n          do {\n            removeIgnoredNodes(node);\n          } while (node = node.next);\n        }\n      };\n      var convertParagraphToLi = function (paragraphNode, listName, start) {\n        var level = paragraphNode._listLevel || lastLevel;\n        if (level !== lastLevel) {\n          if (level < lastLevel) {\n            if (currentListNode) {\n              currentListNode = currentListNode.parent.parent;\n            }\n          } else {\n            prevListNode = currentListNode;\n            currentListNode = null;\n          }\n        }\n        if (!currentListNode || currentListNode.name !== listName) {\n          prevListNode = prevListNode || currentListNode;\n          currentListNode = new global$2(listName, 1);\n          if (start > 1) {\n            currentListNode.attr('start', '' + start);\n          }\n          paragraphNode.wrap(currentListNode);\n        } else {\n          currentListNode.append(paragraphNode);\n        }\n        paragraphNode.name = 'li';\n        if (level > lastLevel && prevListNode) {\n          prevListNode.lastChild.append(currentListNode);\n        }\n        lastLevel = level;\n        removeIgnoredNodes(paragraphNode);\n        trimListStart(paragraphNode, /^\\u00a0+/);\n        trimListStart(paragraphNode, /^\\s*([\\u2022\\u00b7\\u00a7\\u25CF]|\\w+\\.)/);\n        trimListStart(paragraphNode, /^\\u00a0+/);\n      };\n      var elements = [];\n      var child = node.firstChild;\n      while (typeof child !== 'undefined' && child !== null) {\n        elements.push(child);\n        child = child.walk();\n        if (child !== null) {\n          while (typeof child !== 'undefined' && child.parent !== node) {\n            child = child.walk();\n          }\n        }\n      }\n      for (var i = 0; i < elements.length; i++) {\n        node = elements[i];\n        if (node.name === 'p' && node.firstChild) {\n          var nodeText = getText(node);\n          if (isBulletList(nodeText)) {\n            convertParagraphToLi(node, 'ul');\n            continue;\n          }\n          if (isNumericList(nodeText)) {\n            var matches = /([0-9]+)\\./.exec(nodeText);\n            var start = 1;\n            if (matches) {\n              start = parseInt(matches[1], 10);\n            }\n            convertParagraphToLi(node, 'ol', start);\n            continue;\n          }\n          if (node._listLevel) {\n            convertParagraphToLi(node, 'ul', 1);\n            continue;\n          }\n          currentListNode = null;\n        } else {\n          prevListNode = currentListNode;\n          currentListNode = null;\n        }\n      }\n    };\n    var filterStyles = function (editor, validStyles, node, styleValue) {\n      var outputStyles = {};\n      var styles = editor.dom.parseStyle(styleValue);\n      global$6.each(styles, function (value, name) {\n        switch (name) {\n        case 'mso-list':\n          var matches = /\\w+ \\w+([0-9]+)/i.exec(styleValue);\n          if (matches) {\n            node._listLevel = parseInt(matches[1], 10);\n          }\n          if (/Ignore/i.test(value) && node.firstChild) {\n            node._listIgnore = true;\n            node.firstChild._listIgnore = true;\n          }\n          break;\n        case 'horiz-align':\n          name = 'text-align';\n          break;\n        case 'vert-align':\n          name = 'vertical-align';\n          break;\n        case 'font-color':\n        case 'mso-foreground':\n          name = 'color';\n          break;\n        case 'mso-background':\n        case 'mso-highlight':\n          name = 'background';\n          break;\n        case 'font-weight':\n        case 'font-style':\n          if (value !== 'normal') {\n            outputStyles[name] = value;\n          }\n          return;\n        case 'mso-element':\n          if (/^(comment|comment-list)$/i.test(value)) {\n            node.remove();\n            return;\n          }\n          break;\n        }\n        if (name.indexOf('mso-comment') === 0) {\n          node.remove();\n          return;\n        }\n        if (name.indexOf('mso-') === 0) {\n          return;\n        }\n        if (getRetainStyleProps(editor) === 'all' || validStyles && validStyles[name]) {\n          outputStyles[name] = value;\n        }\n      });\n      if (/(bold)/i.test(outputStyles['font-weight'])) {\n        delete outputStyles['font-weight'];\n        node.wrap(new global$2('b', 1));\n      }\n      if (/(italic)/i.test(outputStyles['font-style'])) {\n        delete outputStyles['font-style'];\n        node.wrap(new global$2('i', 1));\n      }\n      var outputStyle = editor.dom.serializeStyle(outputStyles, node.name);\n      if (outputStyle) {\n        return outputStyle;\n      }\n      return null;\n    };\n    var filterWordContent = function (editor, content) {\n      var validStyles;\n      var retainStyleProperties = getRetainStyleProps(editor);\n      if (retainStyleProperties) {\n        validStyles = global$6.makeMap(retainStyleProperties.split(/[, ]/));\n      }\n      content = filter(content, [\n        /<br class=\"?Apple-interchange-newline\"?>/gi,\n        /<b[^>]+id=\"?docs-internal-[^>]*>/gi,\n        /<!--[\\s\\S]+?-->/gi,\n        /<(!|script[^>]*>.*?<\\/script(?=[>\\s])|\\/?(\\?xml(:\\w+)?|img|meta|link|style|\\w:\\w+)(?=[\\s\\/>]))[^>]*>/gi,\n        [\n          /<(\\/?)s>/gi,\n          '<$1strike>'\n        ],\n        [\n          /&nbsp;/gi,\n          nbsp\n        ],\n        [\n          /<span\\s+style\\s*=\\s*\"\\s*mso-spacerun\\s*:\\s*yes\\s*;?\\s*\"\\s*>([\\s\\u00a0]*)<\\/span>/gi,\n          function (str, spaces) {\n            return spaces.length > 0 ? spaces.replace(/./, ' ').slice(Math.floor(spaces.length / 2)).split('').join(nbsp) : '';\n          }\n        ]\n      ]);\n      var validElements = getWordValidElements(editor);\n      var schema = global$1({\n        valid_elements: validElements,\n        valid_children: '-li[p]'\n      });\n      global$6.each(schema.elements, function (rule) {\n        if (!rule.attributes.class) {\n          rule.attributes.class = {};\n          rule.attributesOrder.push('class');\n        }\n        if (!rule.attributes.style) {\n          rule.attributes.style = {};\n          rule.attributesOrder.push('style');\n        }\n      });\n      var domParser = global$4({}, schema);\n      domParser.addAttributeFilter('style', function (nodes) {\n        var i = nodes.length, node;\n        while (i--) {\n          node = nodes[i];\n          node.attr('style', filterStyles(editor, validStyles, node, node.attr('style')));\n          if (node.name === 'span' && node.parent && !node.attributes.length) {\n            node.unwrap();\n          }\n        }\n      });\n      domParser.addAttributeFilter('class', function (nodes) {\n        var i = nodes.length, node, className;\n        while (i--) {\n          node = nodes[i];\n          className = node.attr('class');\n          if (/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(className)) {\n            node.remove();\n          }\n          node.attr('class', null);\n        }\n      });\n      domParser.addNodeFilter('del', function (nodes) {\n        var i = nodes.length;\n        while (i--) {\n          nodes[i].remove();\n        }\n      });\n      domParser.addNodeFilter('a', function (nodes) {\n        var i = nodes.length, node, href, name;\n        while (i--) {\n          node = nodes[i];\n          href = node.attr('href');\n          name = node.attr('name');\n          if (href && href.indexOf('#_msocom_') !== -1) {\n            node.remove();\n            continue;\n          }\n          if (href && href.indexOf('file://') === 0) {\n            href = href.split('#')[1];\n            if (href) {\n              href = '#' + href;\n            }\n          }\n          if (!href && !name) {\n            node.unwrap();\n          } else {\n            if (name && !/^_?(?:toc|edn|ftn)/i.test(name)) {\n              node.unwrap();\n              continue;\n            }\n            node.attr({\n              href: href,\n              name: name\n            });\n          }\n        }\n      });\n      var rootNode = domParser.parse(content);\n      if (shouldConvertWordFakeLists(editor)) {\n        convertFakeListsToProperLists(rootNode);\n      }\n      content = global$3({ validate: getValidate(editor) }, schema).serialize(rootNode);\n      return content;\n    };\n    var preProcess$1 = function (editor, content) {\n      return shouldUseDefaultFilters(editor) ? filterWordContent(editor, content) : content;\n    };\n\n    var preProcess = function (editor, html) {\n      var parser = global$4({}, editor.schema);\n      parser.addNodeFilter('meta', function (nodes) {\n        global$6.each(nodes, function (node) {\n          node.remove();\n        });\n      });\n      var fragment = parser.parse(html, {\n        forced_root_block: false,\n        isRootContent: true\n      });\n      return global$3({ validate: getValidate(editor) }, editor.schema).serialize(fragment);\n    };\n    var processResult = function (content, cancelled) {\n      return {\n        content: content,\n        cancelled: cancelled\n      };\n    };\n    var postProcessFilter = function (editor, html, internal, isWordHtml) {\n      var tempBody = editor.dom.create('div', { style: 'display:none' }, html);\n      var postProcessArgs = firePastePostProcess(editor, tempBody, internal, isWordHtml);\n      return processResult(postProcessArgs.node.innerHTML, postProcessArgs.isDefaultPrevented());\n    };\n    var filterContent = function (editor, content, internal, isWordHtml) {\n      var preProcessArgs = firePastePreProcess(editor, content, internal, isWordHtml);\n      var filteredContent = preProcess(editor, preProcessArgs.content);\n      if (editor.hasEventListeners('PastePostProcess') && !preProcessArgs.isDefaultPrevented()) {\n        return postProcessFilter(editor, filteredContent, internal, isWordHtml);\n      } else {\n        return processResult(filteredContent, preProcessArgs.isDefaultPrevented());\n      }\n    };\n    var process = function (editor, html, internal) {\n      var isWordHtml = isWordContent(html);\n      var content = isWordHtml ? preProcess$1(editor, html) : html;\n      return filterContent(editor, content, internal, isWordHtml);\n    };\n\n    var pasteHtml$1 = function (editor, html) {\n      editor.insertContent(html, {\n        merge: shouldMergeFormats(editor),\n        paste: true\n      });\n      return true;\n    };\n    var isAbsoluteUrl = function (url) {\n      return /^https?:\\/\\/[\\w\\-\\/+=.,!;:&%@^~(){}?#]+$/i.test(url);\n    };\n    var isImageUrl = function (editor, url) {\n      return isAbsoluteUrl(url) && exists(getAllowedImageFileTypes(editor), function (type) {\n        return endsWith(url.toLowerCase(), '.' + type.toLowerCase());\n      });\n    };\n    var createImage = function (editor, url, pasteHtmlFn) {\n      editor.undoManager.extra(function () {\n        pasteHtmlFn(editor, url);\n      }, function () {\n        editor.insertContent('<img src=\"' + url + '\">');\n      });\n      return true;\n    };\n    var createLink = function (editor, url, pasteHtmlFn) {\n      editor.undoManager.extra(function () {\n        pasteHtmlFn(editor, url);\n      }, function () {\n        editor.execCommand('mceInsertLink', false, url);\n      });\n      return true;\n    };\n    var linkSelection = function (editor, html, pasteHtmlFn) {\n      return editor.selection.isCollapsed() === false && isAbsoluteUrl(html) ? createLink(editor, html, pasteHtmlFn) : false;\n    };\n    var insertImage = function (editor, html, pasteHtmlFn) {\n      return isImageUrl(editor, html) ? createImage(editor, html, pasteHtmlFn) : false;\n    };\n    var smartInsertContent = function (editor, html) {\n      global$6.each([\n        linkSelection,\n        insertImage,\n        pasteHtml$1\n      ], function (action) {\n        return action(editor, html, pasteHtml$1) !== true;\n      });\n    };\n    var insertContent = function (editor, html, pasteAsText) {\n      if (pasteAsText || isSmartPasteEnabled(editor) === false) {\n        pasteHtml$1(editor, html);\n      } else {\n        smartInsertContent(editor, html);\n      }\n    };\n\n    var isCollapsibleWhitespace = function (c) {\n      return ' \\f\\t\\x0B'.indexOf(c) !== -1;\n    };\n    var isNewLineChar = function (c) {\n      return c === '\\n' || c === '\\r';\n    };\n    var isNewline = function (text, idx) {\n      return idx < text.length && idx >= 0 ? isNewLineChar(text[idx]) : false;\n    };\n    var normalizeWhitespace = function (editor, text) {\n      var tabSpace = repeat(' ', getTabSpaces(editor));\n      var normalizedText = text.replace(/\\t/g, tabSpace);\n      var result = foldl(normalizedText, function (acc, c) {\n        if (isCollapsibleWhitespace(c) || c === nbsp) {\n          if (acc.pcIsSpace || acc.str === '' || acc.str.length === normalizedText.length - 1 || isNewline(normalizedText, acc.str.length + 1)) {\n            return {\n              pcIsSpace: false,\n              str: acc.str + nbsp\n            };\n          } else {\n            return {\n              pcIsSpace: true,\n              str: acc.str + ' '\n            };\n          }\n        } else {\n          return {\n            pcIsSpace: isNewLineChar(c),\n            str: acc.str + c\n          };\n        }\n      }, {\n        pcIsSpace: false,\n        str: ''\n      });\n      return result.str;\n    };\n\n    var doPaste = function (editor, content, internal, pasteAsText) {\n      var args = process(editor, content, internal);\n      if (args.cancelled === false) {\n        insertContent(editor, args.content, pasteAsText);\n      }\n    };\n    var pasteHtml = function (editor, html, internalFlag) {\n      var internal = internalFlag ? internalFlag : isMarked(html);\n      doPaste(editor, unmark(html), internal, false);\n    };\n    var pasteText = function (editor, text) {\n      var encodedText = editor.dom.encode(text).replace(/\\r\\n/g, '\\n');\n      var normalizedText = normalizeWhitespace(editor, encodedText);\n      var html = convert(normalizedText, getForcedRootBlock(editor), getForcedRootBlockAttrs(editor));\n      doPaste(editor, html, false, true);\n    };\n    var getDataTransferItems = function (dataTransfer) {\n      var items = {};\n      var mceInternalUrlPrefix = 'data:text/mce-internal,';\n      if (dataTransfer) {\n        if (dataTransfer.getData) {\n          var legacyText = dataTransfer.getData('Text');\n          if (legacyText && legacyText.length > 0) {\n            if (legacyText.indexOf(mceInternalUrlPrefix) === -1) {\n              items['text/plain'] = legacyText;\n            }\n          }\n        }\n        if (dataTransfer.types) {\n          for (var i = 0; i < dataTransfer.types.length; i++) {\n            var contentType = dataTransfer.types[i];\n            try {\n              items[contentType] = dataTransfer.getData(contentType);\n            } catch (ex) {\n              items[contentType] = '';\n            }\n          }\n        }\n      }\n      return items;\n    };\n    var getClipboardContent = function (editor, clipboardEvent) {\n      return getDataTransferItems(clipboardEvent.clipboardData || editor.getDoc().dataTransfer);\n    };\n    var hasContentType = function (clipboardContent, mimeType) {\n      return mimeType in clipboardContent && clipboardContent[mimeType].length > 0;\n    };\n    var hasHtmlOrText = function (content) {\n      return hasContentType(content, 'text/html') || hasContentType(content, 'text/plain');\n    };\n    var parseDataUri = function (uri) {\n      var matches = /data:([^;]+);base64,([a-z0-9\\+\\/=]+)/i.exec(uri);\n      if (matches) {\n        return {\n          type: matches[1],\n          data: decodeURIComponent(matches[2])\n        };\n      } else {\n        return {\n          type: null,\n          data: null\n        };\n      }\n    };\n    var isValidDataUriImage = function (editor, imgElm) {\n      var filter = getImagesDataImgFilter(editor);\n      return filter ? filter(imgElm) : true;\n    };\n    var extractFilename = function (editor, str) {\n      var m = str.match(/([\\s\\S]+?)(?:\\.[a-z0-9.]+)$/i);\n      return isNonNullable(m) ? editor.dom.encode(m[1]) : null;\n    };\n    var uniqueId = createIdGenerator('mceclip');\n    var pasteImage = function (editor, imageItem) {\n      var _a = parseDataUri(imageItem.uri), base64 = _a.data, type = _a.type;\n      var id = uniqueId();\n      var file = imageItem.blob;\n      var img = new Image();\n      img.src = imageItem.uri;\n      if (isValidDataUriImage(editor, img)) {\n        var blobCache = editor.editorUpload.blobCache;\n        var blobInfo = void 0;\n        var existingBlobInfo = blobCache.getByData(base64, type);\n        if (!existingBlobInfo) {\n          var useFileName = getImagesReuseFilename(editor) && isNonNullable(file.name);\n          var name_1 = useFileName ? extractFilename(editor, file.name) : id;\n          var filename = useFileName ? file.name : undefined;\n          blobInfo = blobCache.create(id, file, base64, name_1, filename);\n          blobCache.add(blobInfo);\n        } else {\n          blobInfo = existingBlobInfo;\n        }\n        pasteHtml(editor, '<img src=\"' + blobInfo.blobUri() + '\">', false);\n      } else {\n        pasteHtml(editor, '<img src=\"' + imageItem.uri + '\">', false);\n      }\n    };\n    var isClipboardEvent = function (event) {\n      return event.type === 'paste';\n    };\n    var isDataTransferItem = function (item) {\n      return isNonNullable(item.getAsFile);\n    };\n    var readFilesAsDataUris = function (items) {\n      return global$8.all(map(items, function (item) {\n        return new global$8(function (resolve) {\n          var blob = isDataTransferItem(item) ? item.getAsFile() : item;\n          var reader = new window.FileReader();\n          reader.onload = function () {\n            resolve({\n              blob: blob,\n              uri: reader.result\n            });\n          };\n          reader.readAsDataURL(blob);\n        });\n      }));\n    };\n    var isImage = function (editor) {\n      var allowedExtensions = getAllowedImageFileTypes(editor);\n      return function (file) {\n        return startsWith(file.type, 'image/') && exists(allowedExtensions, function (extension) {\n          return getImageMimeType(extension) === file.type;\n        });\n      };\n    };\n    var getImagesFromDataTransfer = function (editor, dataTransfer) {\n      var items = dataTransfer.items ? bind(from(dataTransfer.items), function (item) {\n        return item.kind === 'file' ? [item.getAsFile()] : [];\n      }) : [];\n      var files = dataTransfer.files ? from(dataTransfer.files) : [];\n      return filter$1(items.length > 0 ? items : files, isImage(editor));\n    };\n    var pasteImageData = function (editor, e, rng) {\n      var dataTransfer = isClipboardEvent(e) ? e.clipboardData : e.dataTransfer;\n      if (getPasteDataImages(editor) && dataTransfer) {\n        var images = getImagesFromDataTransfer(editor, dataTransfer);\n        if (images.length > 0) {\n          e.preventDefault();\n          readFilesAsDataUris(images).then(function (fileResults) {\n            if (rng) {\n              editor.selection.setRng(rng);\n            }\n            each(fileResults, function (result) {\n              pasteImage(editor, result);\n            });\n          });\n          return true;\n        }\n      }\n      return false;\n    };\n    var isBrokenAndroidClipboardEvent = function (e) {\n      var clipboardData = e.clipboardData;\n      return navigator.userAgent.indexOf('Android') !== -1 && clipboardData && clipboardData.items && clipboardData.items.length === 0;\n    };\n    var isKeyboardPasteEvent = function (e) {\n      return global$7.metaKeyPressed(e) && e.keyCode === 86 || e.shiftKey && e.keyCode === 45;\n    };\n    var registerEventHandlers = function (editor, pasteBin, pasteFormat) {\n      var keyboardPasteEvent = value();\n      var keyboardPastePressed = value();\n      var keyboardPastePlainTextState;\n      editor.on('keyup', keyboardPastePressed.clear);\n      editor.on('keydown', function (e) {\n        var removePasteBinOnKeyUp = function (e) {\n          if (isKeyboardPasteEvent(e) && !e.isDefaultPrevented()) {\n            pasteBin.remove();\n          }\n        };\n        if (isKeyboardPasteEvent(e) && !e.isDefaultPrevented()) {\n          keyboardPastePlainTextState = e.shiftKey && e.keyCode === 86;\n          if (keyboardPastePlainTextState && global$a.webkit && navigator.userAgent.indexOf('Version/') !== -1) {\n            return;\n          }\n          e.stopImmediatePropagation();\n          keyboardPasteEvent.set(e);\n          keyboardPastePressed.set(true);\n          if (global$a.ie && keyboardPastePlainTextState) {\n            e.preventDefault();\n            firePaste(editor, true);\n            return;\n          }\n          pasteBin.remove();\n          pasteBin.create();\n          editor.once('keyup', removePasteBinOnKeyUp);\n          editor.once('paste', function () {\n            editor.off('keyup', removePasteBinOnKeyUp);\n          });\n        }\n      });\n      var insertClipboardContent = function (editor, clipboardContent, isKeyBoardPaste, plainTextMode, internal) {\n        var content;\n        if (hasContentType(clipboardContent, 'text/html')) {\n          content = clipboardContent['text/html'];\n        } else {\n          content = pasteBin.getHtml();\n          internal = internal ? internal : isMarked(content);\n          if (pasteBin.isDefaultContent(content)) {\n            plainTextMode = true;\n          }\n        }\n        content = trimHtml(content);\n        pasteBin.remove();\n        var isPlainTextHtml = internal === false && isPlainText(content);\n        var isAbsoluteUrl$1 = isAbsoluteUrl(content);\n        if (!content.length || isPlainTextHtml && !isAbsoluteUrl$1) {\n          plainTextMode = true;\n        }\n        if (plainTextMode || isAbsoluteUrl$1) {\n          if (hasContentType(clipboardContent, 'text/plain') && isPlainTextHtml) {\n            content = clipboardContent['text/plain'];\n          } else {\n            content = innerText(content);\n          }\n        }\n        if (pasteBin.isDefaultContent(content)) {\n          if (!isKeyBoardPaste) {\n            editor.windowManager.alert('Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents.');\n          }\n          return;\n        }\n        if (plainTextMode) {\n          pasteText(editor, content);\n        } else {\n          pasteHtml(editor, content, internal);\n        }\n      };\n      var getLastRng = function () {\n        return pasteBin.getLastRng() || editor.selection.getRng();\n      };\n      editor.on('paste', function (e) {\n        var isKeyboardPaste = keyboardPasteEvent.isSet() || keyboardPastePressed.isSet();\n        if (isKeyboardPaste) {\n          keyboardPasteEvent.clear();\n        }\n        var clipboardContent = getClipboardContent(editor, e);\n        var plainTextMode = pasteFormat.get() === 'text' || keyboardPastePlainTextState;\n        var internal = hasContentType(clipboardContent, internalHtmlMime());\n        keyboardPastePlainTextState = false;\n        if (e.isDefaultPrevented() || isBrokenAndroidClipboardEvent(e)) {\n          pasteBin.remove();\n          return;\n        }\n        if (!hasHtmlOrText(clipboardContent) && pasteImageData(editor, e, getLastRng())) {\n          pasteBin.remove();\n          return;\n        }\n        if (!isKeyboardPaste) {\n          e.preventDefault();\n        }\n        if (global$a.ie && (!isKeyboardPaste || e.ieFake) && !hasContentType(clipboardContent, 'text/html')) {\n          pasteBin.create();\n          editor.dom.bind(pasteBin.getEl(), 'paste', function (e) {\n            e.stopPropagation();\n          });\n          editor.getDoc().execCommand('Paste', false, null);\n          clipboardContent['text/html'] = pasteBin.getHtml();\n        }\n        if (hasContentType(clipboardContent, 'text/html')) {\n          e.preventDefault();\n          if (!internal) {\n            internal = isMarked(clipboardContent['text/html']);\n          }\n          insertClipboardContent(editor, clipboardContent, isKeyboardPaste, plainTextMode, internal);\n        } else {\n          global$9.setEditorTimeout(editor, function () {\n            insertClipboardContent(editor, clipboardContent, isKeyboardPaste, plainTextMode, internal);\n          }, 0);\n        }\n      });\n    };\n    var registerEventsAndFilters = function (editor, pasteBin, pasteFormat) {\n      registerEventHandlers(editor, pasteBin, pasteFormat);\n      var src;\n      editor.parser.addNodeFilter('img', function (nodes, name, args) {\n        var isPasteInsert = function (args) {\n          return args.data && args.data.paste === true;\n        };\n        var remove = function (node) {\n          if (!node.attr('data-mce-object') && src !== global$a.transparentSrc) {\n            node.remove();\n          }\n        };\n        var isWebKitFakeUrl = function (src) {\n          return src.indexOf('webkit-fake-url') === 0;\n        };\n        var isDataUri = function (src) {\n          return src.indexOf('data:') === 0;\n        };\n        if (!getPasteDataImages(editor) && isPasteInsert(args)) {\n          var i = nodes.length;\n          while (i--) {\n            src = nodes[i].attr('src');\n            if (!src) {\n              continue;\n            }\n            if (isWebKitFakeUrl(src)) {\n              remove(nodes[i]);\n            } else if (!getAllowHtmlDataUrls(editor) && isDataUri(src)) {\n              remove(nodes[i]);\n            }\n          }\n        }\n      });\n    };\n\n    var getPasteBinParent = function (editor) {\n      return global$a.ie && editor.inline ? document.body : editor.getBody();\n    };\n    var isExternalPasteBin = function (editor) {\n      return getPasteBinParent(editor) !== editor.getBody();\n    };\n    var delegatePasteEvents = function (editor, pasteBinElm, pasteBinDefaultContent) {\n      if (isExternalPasteBin(editor)) {\n        editor.dom.bind(pasteBinElm, 'paste keyup', function (_e) {\n          if (!isDefault(editor, pasteBinDefaultContent)) {\n            editor.fire('paste');\n          }\n        });\n      }\n    };\n    var create = function (editor, lastRngCell, pasteBinDefaultContent) {\n      var dom = editor.dom, body = editor.getBody();\n      lastRngCell.set(editor.selection.getRng());\n      var pasteBinElm = editor.dom.add(getPasteBinParent(editor), 'div', {\n        'id': 'mcepastebin',\n        'class': 'mce-pastebin',\n        'contentEditable': true,\n        'data-mce-bogus': 'all',\n        'style': 'position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0'\n      }, pasteBinDefaultContent);\n      if (global$a.ie || global$a.gecko) {\n        dom.setStyle(pasteBinElm, 'left', dom.getStyle(body, 'direction', true) === 'rtl' ? 65535 : -65535);\n      }\n      dom.bind(pasteBinElm, 'beforedeactivate focusin focusout', function (e) {\n        e.stopPropagation();\n      });\n      delegatePasteEvents(editor, pasteBinElm, pasteBinDefaultContent);\n      pasteBinElm.focus();\n      editor.selection.select(pasteBinElm, true);\n    };\n    var remove = function (editor, lastRngCell) {\n      if (getEl(editor)) {\n        var pasteBinClone = void 0;\n        var lastRng = lastRngCell.get();\n        while (pasteBinClone = editor.dom.get('mcepastebin')) {\n          editor.dom.remove(pasteBinClone);\n          editor.dom.unbind(pasteBinClone);\n        }\n        if (lastRng) {\n          editor.selection.setRng(lastRng);\n        }\n      }\n      lastRngCell.set(null);\n    };\n    var getEl = function (editor) {\n      return editor.dom.get('mcepastebin');\n    };\n    var getHtml = function (editor) {\n      var copyAndRemove = function (toElm, fromElm) {\n        toElm.appendChild(fromElm);\n        editor.dom.remove(fromElm, true);\n      };\n      var pasteBinClones = global$6.grep(getPasteBinParent(editor).childNodes, function (elm) {\n        return elm.id === 'mcepastebin';\n      });\n      var pasteBinElm = pasteBinClones.shift();\n      global$6.each(pasteBinClones, function (pasteBinClone) {\n        copyAndRemove(pasteBinElm, pasteBinClone);\n      });\n      var dirtyWrappers = editor.dom.select('div[id=mcepastebin]', pasteBinElm);\n      for (var i = dirtyWrappers.length - 1; i >= 0; i--) {\n        var cleanWrapper = editor.dom.create('div');\n        pasteBinElm.insertBefore(cleanWrapper, dirtyWrappers[i]);\n        copyAndRemove(cleanWrapper, dirtyWrappers[i]);\n      }\n      return pasteBinElm ? pasteBinElm.innerHTML : '';\n    };\n    var isDefaultContent = function (pasteBinDefaultContent, content) {\n      return content === pasteBinDefaultContent;\n    };\n    var isPasteBin = function (elm) {\n      return elm && elm.id === 'mcepastebin';\n    };\n    var isDefault = function (editor, pasteBinDefaultContent) {\n      var pasteBinElm = getEl(editor);\n      return isPasteBin(pasteBinElm) && isDefaultContent(pasteBinDefaultContent, pasteBinElm.innerHTML);\n    };\n    var PasteBin = function (editor) {\n      var lastRng = Cell(null);\n      var pasteBinDefaultContent = '%MCEPASTEBIN%';\n      return {\n        create: function () {\n          return create(editor, lastRng, pasteBinDefaultContent);\n        },\n        remove: function () {\n          return remove(editor, lastRng);\n        },\n        getEl: function () {\n          return getEl(editor);\n        },\n        getHtml: function () {\n          return getHtml(editor);\n        },\n        getLastRng: lastRng.get,\n        isDefault: function () {\n          return isDefault(editor, pasteBinDefaultContent);\n        },\n        isDefaultContent: function (content) {\n          return isDefaultContent(pasteBinDefaultContent, content);\n        }\n      };\n    };\n\n    var Clipboard = function (editor, pasteFormat) {\n      var pasteBin = PasteBin(editor);\n      editor.on('PreInit', function () {\n        return registerEventsAndFilters(editor, pasteBin, pasteFormat);\n      });\n      return {\n        pasteFormat: pasteFormat,\n        pasteHtml: function (html, internalFlag) {\n          return pasteHtml(editor, html, internalFlag);\n        },\n        pasteText: function (text) {\n          return pasteText(editor, text);\n        },\n        pasteImageData: function (e, rng) {\n          return pasteImageData(editor, e, rng);\n        },\n        getDataTransferItems: getDataTransferItems,\n        hasHtmlOrText: hasHtmlOrText,\n        hasContentType: hasContentType\n      };\n    };\n\n    var togglePlainTextPaste = function (editor, clipboard) {\n      if (clipboard.pasteFormat.get() === 'text') {\n        clipboard.pasteFormat.set('html');\n        firePastePlainTextToggle(editor, false);\n      } else {\n        clipboard.pasteFormat.set('text');\n        firePastePlainTextToggle(editor, true);\n      }\n      editor.focus();\n    };\n\n    var register$2 = function (editor, clipboard) {\n      editor.addCommand('mceTogglePlainTextPaste', function () {\n        togglePlainTextPaste(editor, clipboard);\n      });\n      editor.addCommand('mceInsertClipboardContent', function (ui, value) {\n        if (value.content) {\n          clipboard.pasteHtml(value.content, value.internal);\n        }\n        if (value.text) {\n          clipboard.pasteText(value.text);\n        }\n      });\n    };\n\n    var hasWorkingClipboardApi = function (clipboardData) {\n      return global$a.iOS === false && typeof (clipboardData === null || clipboardData === void 0 ? void 0 : clipboardData.setData) === 'function';\n    };\n    var setHtml5Clipboard = function (clipboardData, html, text) {\n      if (hasWorkingClipboardApi(clipboardData)) {\n        try {\n          clipboardData.clearData();\n          clipboardData.setData('text/html', html);\n          clipboardData.setData('text/plain', text);\n          clipboardData.setData(internalHtmlMime(), html);\n          return true;\n        } catch (e) {\n          return false;\n        }\n      } else {\n        return false;\n      }\n    };\n    var setClipboardData = function (evt, data, fallback, done) {\n      if (setHtml5Clipboard(evt.clipboardData, data.html, data.text)) {\n        evt.preventDefault();\n        done();\n      } else {\n        fallback(data.html, done);\n      }\n    };\n    var fallback = function (editor) {\n      return function (html, done) {\n        var markedHtml = mark(html);\n        var outer = editor.dom.create('div', {\n          'contenteditable': 'false',\n          'data-mce-bogus': 'all'\n        });\n        var inner = editor.dom.create('div', { contenteditable: 'true' }, markedHtml);\n        editor.dom.setStyles(outer, {\n          position: 'fixed',\n          top: '0',\n          left: '-3000px',\n          width: '1000px',\n          overflow: 'hidden'\n        });\n        outer.appendChild(inner);\n        editor.dom.add(editor.getBody(), outer);\n        var range = editor.selection.getRng();\n        inner.focus();\n        var offscreenRange = editor.dom.createRng();\n        offscreenRange.selectNodeContents(inner);\n        editor.selection.setRng(offscreenRange);\n        global$9.setTimeout(function () {\n          editor.selection.setRng(range);\n          outer.parentNode.removeChild(outer);\n          done();\n        }, 0);\n      };\n    };\n    var getData = function (editor) {\n      return {\n        html: editor.selection.getContent({ contextual: true }),\n        text: editor.selection.getContent({ format: 'text' })\n      };\n    };\n    var isTableSelection = function (editor) {\n      return !!editor.dom.getParent(editor.selection.getStart(), 'td[data-mce-selected],th[data-mce-selected]', editor.getBody());\n    };\n    var hasSelectedContent = function (editor) {\n      return !editor.selection.isCollapsed() || isTableSelection(editor);\n    };\n    var cut = function (editor) {\n      return function (evt) {\n        if (hasSelectedContent(editor)) {\n          setClipboardData(evt, getData(editor), fallback(editor), function () {\n            if (global$a.browser.isChrome() || global$a.browser.isFirefox()) {\n              var rng_1 = editor.selection.getRng();\n              global$9.setEditorTimeout(editor, function () {\n                editor.selection.setRng(rng_1);\n                editor.execCommand('Delete');\n              }, 0);\n            } else {\n              editor.execCommand('Delete');\n            }\n          });\n        }\n      };\n    };\n    var copy = function (editor) {\n      return function (evt) {\n        if (hasSelectedContent(editor)) {\n          setClipboardData(evt, getData(editor), fallback(editor), noop);\n        }\n      };\n    };\n    var register$1 = function (editor) {\n      editor.on('cut', cut(editor));\n      editor.on('copy', copy(editor));\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.dom.RangeUtils');\n\n    var getCaretRangeFromEvent = function (editor, e) {\n      return global.getCaretRangeFromPoint(e.clientX, e.clientY, editor.getDoc());\n    };\n    var isPlainTextFileUrl = function (content) {\n      var plainTextContent = content['text/plain'];\n      return plainTextContent ? plainTextContent.indexOf('file://') === 0 : false;\n    };\n    var setFocusedRange = function (editor, rng) {\n      editor.focus();\n      editor.selection.setRng(rng);\n    };\n    var setup$2 = function (editor, clipboard, draggingInternallyState) {\n      if (shouldBlockDrop(editor)) {\n        editor.on('dragend dragover draggesture dragdrop drop drag', function (e) {\n          e.preventDefault();\n          e.stopPropagation();\n        });\n      }\n      if (!shouldPasteDataImages(editor)) {\n        editor.on('drop', function (e) {\n          var dataTransfer = e.dataTransfer;\n          if (dataTransfer && dataTransfer.files && dataTransfer.files.length > 0) {\n            e.preventDefault();\n          }\n        });\n      }\n      editor.on('drop', function (e) {\n        var rng = getCaretRangeFromEvent(editor, e);\n        if (e.isDefaultPrevented() || draggingInternallyState.get()) {\n          return;\n        }\n        var dropContent = clipboard.getDataTransferItems(e.dataTransfer);\n        var internal = clipboard.hasContentType(dropContent, internalHtmlMime());\n        if ((!clipboard.hasHtmlOrText(dropContent) || isPlainTextFileUrl(dropContent)) && clipboard.pasteImageData(e, rng)) {\n          return;\n        }\n        if (rng && shouldFilterDrop(editor)) {\n          var content_1 = dropContent['mce-internal'] || dropContent['text/html'] || dropContent['text/plain'];\n          if (content_1) {\n            e.preventDefault();\n            global$9.setEditorTimeout(editor, function () {\n              editor.undoManager.transact(function () {\n                if (dropContent['mce-internal']) {\n                  editor.execCommand('Delete');\n                }\n                setFocusedRange(editor, rng);\n                content_1 = trimHtml(content_1);\n                if (!dropContent['text/html']) {\n                  clipboard.pasteText(content_1);\n                } else {\n                  clipboard.pasteHtml(content_1, internal);\n                }\n              });\n            });\n          }\n        }\n      });\n      editor.on('dragstart', function (_e) {\n        draggingInternallyState.set(true);\n      });\n      editor.on('dragover dragend', function (e) {\n        if (shouldPasteDataImages(editor) && draggingInternallyState.get() === false) {\n          e.preventDefault();\n          setFocusedRange(editor, getCaretRangeFromEvent(editor, e));\n        }\n        if (e.type === 'dragend') {\n          draggingInternallyState.set(false);\n        }\n      });\n    };\n\n    var setup$1 = function (editor) {\n      var plugin = editor.plugins.paste;\n      var preProcess = getPreProcess(editor);\n      if (preProcess) {\n        editor.on('PastePreProcess', function (e) {\n          preProcess.call(plugin, plugin, e);\n        });\n      }\n      var postProcess = getPostProcess(editor);\n      if (postProcess) {\n        editor.on('PastePostProcess', function (e) {\n          postProcess.call(plugin, plugin, e);\n        });\n      }\n    };\n\n    var addPreProcessFilter = function (editor, filterFunc) {\n      editor.on('PastePreProcess', function (e) {\n        e.content = filterFunc(editor, e.content, e.internal, e.wordContent);\n      });\n    };\n    var addPostProcessFilter = function (editor, filterFunc) {\n      editor.on('PastePostProcess', function (e) {\n        filterFunc(editor, e.node);\n      });\n    };\n    var removeExplorerBrElementsAfterBlocks = function (editor, html) {\n      if (!isWordContent(html)) {\n        return html;\n      }\n      var blockElements = [];\n      global$6.each(editor.schema.getBlockElements(), function (block, blockName) {\n        blockElements.push(blockName);\n      });\n      var explorerBlocksRegExp = new RegExp('(?:<br>&nbsp;[\\\\s\\\\r\\\\n]+|<br>)*(<\\\\/?(' + blockElements.join('|') + ')[^>]*>)(?:<br>&nbsp;[\\\\s\\\\r\\\\n]+|<br>)*', 'g');\n      html = filter(html, [[\n          explorerBlocksRegExp,\n          '$1'\n        ]]);\n      html = filter(html, [\n        [\n          /<br><br>/g,\n          '<BR><BR>'\n        ],\n        [\n          /<br>/g,\n          ' '\n        ],\n        [\n          /<BR><BR>/g,\n          '<br>'\n        ]\n      ]);\n      return html;\n    };\n    var removeWebKitStyles = function (editor, content, internal, isWordHtml) {\n      if (isWordHtml || internal) {\n        return content;\n      }\n      var webKitStylesSetting = getWebkitStyles(editor);\n      var webKitStyles;\n      if (shouldRemoveWebKitStyles(editor) === false || webKitStylesSetting === 'all') {\n        return content;\n      }\n      if (webKitStylesSetting) {\n        webKitStyles = webKitStylesSetting.split(/[, ]/);\n      }\n      if (webKitStyles) {\n        var dom_1 = editor.dom, node_1 = editor.selection.getNode();\n        content = content.replace(/(<[^>]+) style=\"([^\"]*)\"([^>]*>)/gi, function (all, before, value, after) {\n          var inputStyles = dom_1.parseStyle(dom_1.decode(value));\n          var outputStyles = {};\n          if (webKitStyles === 'none') {\n            return before + after;\n          }\n          for (var i = 0; i < webKitStyles.length; i++) {\n            var inputValue = inputStyles[webKitStyles[i]], currentValue = dom_1.getStyle(node_1, webKitStyles[i], true);\n            if (/color/.test(webKitStyles[i])) {\n              inputValue = dom_1.toHex(inputValue);\n              currentValue = dom_1.toHex(currentValue);\n            }\n            if (currentValue !== inputValue) {\n              outputStyles[webKitStyles[i]] = inputValue;\n            }\n          }\n          var outputStyle = dom_1.serializeStyle(outputStyles, 'span');\n          if (outputStyle) {\n            return before + ' style=\"' + outputStyle + '\"' + after;\n          }\n          return before + after;\n        });\n      } else {\n        content = content.replace(/(<[^>]+) style=\"([^\"]*)\"([^>]*>)/gi, '$1$3');\n      }\n      content = content.replace(/(<[^>]+) data-mce-style=\"([^\"]+)\"([^>]*>)/gi, function (all, before, value, after) {\n        return before + ' style=\"' + value + '\"' + after;\n      });\n      return content;\n    };\n    var removeUnderlineAndFontInAnchor = function (editor, root) {\n      editor.$('a', root).find('font,u').each(function (i, node) {\n        editor.dom.remove(node, true);\n      });\n    };\n    var setup = function (editor) {\n      if (global$a.webkit) {\n        addPreProcessFilter(editor, removeWebKitStyles);\n      }\n      if (global$a.ie) {\n        addPreProcessFilter(editor, removeExplorerBrElementsAfterBlocks);\n        addPostProcessFilter(editor, removeUnderlineAndFontInAnchor);\n      }\n    };\n\n    var makeSetupHandler = function (editor, clipboard) {\n      return function (api) {\n        api.setActive(clipboard.pasteFormat.get() === 'text');\n        var pastePlainTextToggleHandler = function (e) {\n          return api.setActive(e.state);\n        };\n        editor.on('PastePlainTextToggle', pastePlainTextToggleHandler);\n        return function () {\n          return editor.off('PastePlainTextToggle', pastePlainTextToggleHandler);\n        };\n      };\n    };\n    var register = function (editor, clipboard) {\n      var onAction = function () {\n        return editor.execCommand('mceTogglePlainTextPaste');\n      };\n      editor.ui.registry.addToggleButton('pastetext', {\n        active: false,\n        icon: 'paste-text',\n        tooltip: 'Paste as text',\n        onAction: onAction,\n        onSetup: makeSetupHandler(editor, clipboard)\n      });\n      editor.ui.registry.addToggleMenuItem('pastetext', {\n        text: 'Paste as text',\n        icon: 'paste-text',\n        onAction: onAction,\n        onSetup: makeSetupHandler(editor, clipboard)\n      });\n    };\n\n    function Plugin () {\n      global$b.add('paste', function (editor) {\n        if (hasProPlugin(editor) === false) {\n          var draggingInternallyState = Cell(false);\n          var pasteFormat = Cell(isPasteAsTextEnabled(editor) ? 'text' : 'html');\n          var clipboard = Clipboard(editor, pasteFormat);\n          setup(editor);\n          register(editor, clipboard);\n          register$2(editor, clipboard);\n          setup$1(editor);\n          register$1(editor);\n          setup$2(editor, clipboard, draggingInternallyState);\n          return get(clipboard);\n        }\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"paste\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/paste')\n//   ES2015:\n//     import 'tinymce/plugins/paste'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/paste/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,SAAQ;AACZ,YAAI,OAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,mBAAQ;AAAA;AAEV,eAAO;AAAA,UACL,KAAK;AAAA,UACL;AAAA;AAAA;AAIJ,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,eAAe,SAAU,QAAQ;AACnC,YAAI,OAAO,UAAU,cAAc,OAAO;AACxC,cAAI,OAAO,OAAO,YAAY,eAAe,OAAO,QAAQ,KAAK;AAC/D,mBAAO,QAAQ,IAAI;AAAA;AAErB,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAIX,UAAI,MAAM,SAAU,WAAW;AAC7B,eAAO,EAAE;AAAA;AAGX,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,YAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,MAAM;AACjC,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,WAAU;AAAA;AAAA;AAG5B,UAAI,UAAU,OAAO;AACrB,UAAI,aAAa,SAAU,GAAG;AAC5B,eAAO,MAAM,QAAQ,MAAM;AAAA;AAE7B,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,CAAC,WAAW;AAAA;AAErB,UAAI,aAAa,aAAa;AAE9B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,QAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,QAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,QAAO;AAC5B,eAAO,WAAU,QAAQ,WAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA,MAAM;AAAA;AAGR,UAAI,cAAc,MAAM,UAAU;AAClC,UAAI,aAAa,MAAM,UAAU;AACjC,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,WAAW,SAAU,IAAI,MAAM;AACjC,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,cAAE,KAAK;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,QAAQ,SAAU,IAAI,GAAG,KAAK;AAChC,aAAK,IAAI,SAAU,GAAG,GAAG;AACvB,gBAAM,EAAE,KAAK,GAAG;AAAA;AAElB,eAAO;AAAA;AAET,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,KAAK;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;AAAA;AAE3E,qBAAW,MAAM,GAAG,GAAG;AAAA;AAEzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,eAAO,QAAQ,IAAI,IAAI;AAAA;AAEzB,UAAI,OAAO,WAAW,MAAM,QAAQ,MAAM,OAAO,SAAU,GAAG;AAC5D,eAAO,YAAY,KAAK;AAAA;AAG1B,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,YAAY,SAAU,UAAU;AAClC,YAAI,UAAU,KAAK,SAAS;AAC5B,YAAI,SAAS,WAAY;AACvB,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,YAAI,QAAQ,WAAY;AACtB;AACA,kBAAQ,IAAI,SAAS;AAAA;AAEvB,YAAI,QAAQ,WAAY;AACtB,iBAAO,QAAQ,MAAM;AAAA;AAEvB,YAAI,OAAM,WAAY;AACpB,iBAAO,QAAQ;AAAA;AAEjB,YAAI,MAAM,SAAU,GAAG;AACrB;AACA,kBAAQ,IAAI,SAAS,KAAK;AAAA;AAE5B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA;AAAA;AAGJ,UAAI,QAAQ,WAAY;AACtB,YAAI,UAAU,UAAU;AACxB,YAAI,KAAK,SAAU,GAAG;AACpB,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,eAAO,SAAS,SAAS,IAAI,UAAU,EAAE;AAAA;AAG3C,UAAI,aAAa,SAAU,KAAK,QAAQ,OAAO;AAC7C,eAAO,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,YAAY;AAAA;AAEtG,UAAI,aAAa,SAAU,KAAK,QAAQ;AACtC,eAAO,WAAW,KAAK,QAAQ;AAAA;AAEjC,UAAI,WAAW,SAAU,KAAK,QAAQ;AACpC,eAAO,WAAW,KAAK,QAAQ,IAAI,SAAS,OAAO;AAAA;AAErD,UAAI,SAAS,SAAU,GAAG,OAAO;AAC/B,eAAO,SAAS,IAAI,KAAK,IAAI,MAAM,QAAQ,GAAG,KAAK;AAAA;AAGrD,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,sBAAsB,SAAU,QAAQ,MAAM,UAAU,YAAY;AACtE,eAAO,OAAO,KAAK,mBAAmB;AAAA,UACpC,SAAS;AAAA,UACT;AAAA,UACA,aAAa;AAAA;AAAA;AAGjB,UAAI,uBAAuB,SAAU,QAAQ,MAAM,UAAU,YAAY;AACvE,eAAO,OAAO,KAAK,oBAAoB;AAAA,UACrC;AAAA,UACA;AAAA,UACA,aAAa;AAAA;AAAA;AAGjB,UAAI,2BAA2B,SAAU,QAAQ,OAAO;AACtD,eAAO,OAAO,KAAK,wBAAwB,EAAE;AAAA;AAE/C,UAAI,YAAY,SAAU,QAAQ,QAAQ;AACxC,eAAO,OAAO,KAAK,SAAS,EAAE;AAAA;AAGhC,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,OAAO,SAAS,oBAAoB;AAAA;AAE7C,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,eAAO,OAAO,SAAS,qBAAqB;AAAA;AAE9C,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,SAAS,qBAAqB;AAAA;AAE9C,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,2BAA2B,SAAU,QAAQ;AAC/C,eAAO,OAAO,SAAS,iCAAiC;AAAA;AAE1D,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS,uBAAuB;AAAA;AAEhD,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,OAAO,SAAS,eAAe;AAAA;AAExC,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,OAAO,SAAS,iBAAiB;AAAA;AAE1C,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,YAAI,uBAAuB;AAC3B,eAAO,OAAO,SAAS,6BAA6B;AAAA;AAEtD,UAAI,6BAA6B,SAAU,QAAQ;AACjD,eAAO,OAAO,SAAS,iCAAiC;AAAA;AAE1D,UAAI,0BAA0B,SAAU,QAAQ;AAC9C,eAAO,OAAO,SAAS,gCAAgC;AAAA;AAEzD,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,OAAO,SAAS,wBAAwB,OAAO;AAAA;AAExD,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS,qBAAqB,OAAO;AAAA;AAErD,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,0BAA0B,SAAU,QAAQ;AAC9C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,SAAS,oBAAoB,GAAG;AAAA;AAEhD,UAAI,2BAA2B,SAAU,QAAQ;AAC/C,YAAI,wBAAwB;AAC5B,eAAO,SAAS,QAAQ,OAAO,SAAS,qBAAqB,uBAAuB;AAAA;AAGtF,UAAI,mBAAmB;AACvB,UAAI,eAAe,UAAU,mBAAmB;AAChD,UAAI,OAAO,SAAU,MAAM;AACzB,eAAO,eAAe;AAAA;AAExB,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,KAAK,QAAQ,cAAc;AAAA;AAEpC,UAAI,WAAW,SAAU,MAAM;AAC7B,eAAO,KAAK,QAAQ,kBAAkB;AAAA;AAExC,UAAI,mBAAmB,SAAS;AAEhC,UAAI,iBAAiB,OAAO;AAC5B,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAGlC,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,cAAc,SAAU,MAAM;AAChC,eAAO,CAAC,wGAAwG,KAAK;AAAA;AAEvH,UAAI,QAAQ,SAAU,MAAM;AAC1B,eAAO,KAAK,QAAQ,UAAU;AAAA;AAEhC,UAAI,gBAAgB,SAAU,SAAS,WAAW;AAChD,YAAI,QAAQ;AACZ,YAAI,MAAM,MAAM;AAChB,YAAI,OAAO,cAAc,UAAU;AACjC,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,MAAM;AACvB,oBAAM,KAAK,MAAM,OAAO,SAAS,aAAa,UAAU,QAAQ;AAAA;AAAA;AAGpE,cAAI,MAAM,QAAQ;AAChB,mBAAO,MAAM,MAAM,KAAK;AAAA;AAAA;AAG5B,eAAO,MAAM;AAAA;AAEf,UAAI,kBAAkB,SAAU,MAAM,SAAS,WAAW;AACxD,YAAI,SAAS,KAAK,MAAM;AACxB,YAAI,UAAU,cAAc,SAAS;AACrC,YAAI,WAAW,OAAO,UAAU;AAChC,YAAI,aAAa,SAAS,IAAI,QAAQ,SAAU,GAAG;AACjD,iBAAO,EAAE,MAAM,MAAM,KAAK;AAAA;AAE5B,YAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,UAAU,IAAI;AAAA;AAEvB,eAAO,WAAW,WAAW,IAAI,WAAW,KAAK,SAAS,IAAI,YAAY,QAAQ,KAAK;AAAA;AAEzF,UAAI,UAAU,SAAU,MAAM,SAAS,WAAW;AAChD,eAAO,UAAU,gBAAgB,MAAM,YAAY,OAAO,MAAM,SAAS,aAAa,MAAM;AAAA;AAG9F,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,OAAO;AAEX,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,SAAU,KAAK;AAC5B,eAAO,IAAI,gBAAgB;AAAA;AAE7B,UAAI,SAAS,SAAU,SAAS,OAAO;AACrC,iBAAS,KAAK,OAAO,SAAU,GAAG;AAChC,cAAI,SAAS,IAAI;AACf,sBAAU,QAAQ,QAAQ,GAAG;AAAA,iBACxB;AACL,sBAAU,QAAQ,QAAQ,EAAE,IAAI,EAAE;AAAA;AAAA;AAGtC,eAAO;AAAA;AAET,UAAI,YAAY,SAAU,MAAM;AAC9B,YAAI,SAAS;AACb,YAAI,YAAY,SAAS,IAAI;AAC7B,YAAI,OAAO;AACX,YAAI,qBAAqB,OAAO;AAChC,YAAI,iBAAiB,SAAS,QAAQ,4DAA4D;AAClG,YAAI,gBAAgB,OAAO;AAC3B,YAAI,OAAO,SAAU,MAAM;AACzB,cAAI,OAAO,KAAK,MAAM,cAAc;AACpC,cAAI,SAAS,MAAM;AACjB,oBAAQ;AACR;AAAA;AAEF,cAAI,SAAS,OAAO;AAClB;AAAA;AAEF,cAAI,mBAAmB,OAAO;AAC5B,oBAAQ;AAAA;AAEV,cAAI,eAAe,OAAO;AACxB,oBAAQ;AACR;AAAA;AAEF,cAAI,KAAK,SAAS,GAAG;AACnB,oBAAQ,KAAK;AAAA;AAEf,cAAI,CAAC,KAAK,YAAY;AACpB,gBAAI,OAAO,KAAK,YAAY;AAC1B,iBAAG;AACD,qBAAK;AAAA,uBACE,OAAO,KAAK;AAAA;AAAA;AAGzB,cAAI,cAAc,SAAS,YAAY,MAAM;AAC3C,oBAAQ;AACR,gBAAI,SAAS,KAAK;AAChB,sBAAQ;AAAA;AAAA;AAAA;AAId,eAAO,OAAO,MAAM,CAAC;AACrB,aAAK,UAAU,MAAM;AACrB,eAAO;AAAA;AAET,UAAI,WAAW,SAAU,MAAM;AAC7B,YAAI,aAAa,SAAU,KAAK,IAAI,IAAI;AACtC,cAAI,CAAC,MAAM,CAAC,IAAI;AACd,mBAAO;AAAA;AAET,iBAAO;AAAA;AAET,eAAO,OAAO,MAAM;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA;AAAA,UAEF;AAAA,UACA;AAAA;AAEF,eAAO;AAAA;AAET,UAAI,oBAAoB,SAAU,QAAQ;AACxC,YAAI,QAAQ;AACZ,eAAO,WAAY;AACjB,iBAAO,SAAS;AAAA;AAAA;AAGpB,UAAI,mBAAmB,SAAU,KAAK;AACpC,YAAI,WAAW,IAAI;AACnB,YAAI,gBAAgB;AAAA,UAClB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,KAAK;AAAA;AAEP,eAAO,SAAS,OAAO,eAAe,YAAY,WAAW,cAAc,YAAY,WAAW;AAAA;AAGpG,UAAI,gBAAgB,SAAU,SAAS;AACrC,eAAO,iGAAiG,KAAK,YAAY,wBAAwB,KAAK,YAAY,8BAA8B,KAAK;AAAA;AAEvM,UAAI,gBAAgB,SAAU,MAAM;AAClC,YAAI,QAAQ;AACZ,YAAI,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAEF,eAAO,KAAK,QAAQ,eAAe;AACnC,iBAAS,KAAK,UAAU,SAAU,SAAS;AACzC,cAAI,QAAQ,KAAK,OAAO;AACtB,oBAAQ;AACR,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,MAAM;AACjC,eAAO,4CAA4C,KAAK;AAAA;AAE1D,UAAI,gCAAgC,SAAU,MAAM;AAClD,YAAI,iBAAiB,cAAc,YAAY;AAC/C,YAAI,UAAU,SAAU,OAAM;AAC5B,cAAI,MAAM;AACV,cAAI,MAAK,SAAS,GAAG;AACnB,mBAAO,MAAK;AAAA;AAEd,cAAI,QAAO,MAAK,YAAY;AAC1B,eAAG;AACD,qBAAO,QAAQ;AAAA,qBACR,QAAO,MAAK;AAAA;AAEvB,iBAAO;AAAA;AAET,YAAI,gBAAgB,SAAU,OAAM,QAAQ;AAC1C,cAAI,MAAK,SAAS,GAAG;AACnB,gBAAI,OAAO,KAAK,MAAK,QAAQ;AAC3B,oBAAK,QAAQ,MAAK,MAAM,QAAQ,QAAQ;AACxC,qBAAO;AAAA;AAAA;AAGX,cAAI,QAAO,MAAK,YAAY;AAC1B,eAAG;AACD,kBAAI,CAAC,cAAc,OAAM,SAAS;AAChC,uBAAO;AAAA;AAAA,qBAEF,QAAO,MAAK;AAAA;AAEvB,iBAAO;AAAA;AAET,YAAI,qBAAqB,SAAU,OAAM;AACvC,cAAI,MAAK,aAAa;AACpB,kBAAK;AACL;AAAA;AAEF,cAAI,QAAO,MAAK,YAAY;AAC1B,eAAG;AACD,iCAAmB;AAAA,qBACZ,QAAO,MAAK;AAAA;AAAA;AAGzB,YAAI,uBAAuB,SAAU,eAAe,UAAU,QAAO;AACnE,cAAI,QAAQ,cAAc,cAAc;AACxC,cAAI,UAAU,WAAW;AACvB,gBAAI,QAAQ,WAAW;AACrB,kBAAI,iBAAiB;AACnB,kCAAkB,gBAAgB,OAAO;AAAA;AAAA,mBAEtC;AACL,6BAAe;AACf,gCAAkB;AAAA;AAAA;AAGtB,cAAI,CAAC,mBAAmB,gBAAgB,SAAS,UAAU;AACzD,2BAAe,gBAAgB;AAC/B,8BAAkB,IAAI,SAAS,UAAU;AACzC,gBAAI,SAAQ,GAAG;AACb,8BAAgB,KAAK,SAAS,KAAK;AAAA;AAErC,0BAAc,KAAK;AAAA,iBACd;AACL,4BAAgB,OAAO;AAAA;AAEzB,wBAAc,OAAO;AACrB,cAAI,QAAQ,aAAa,cAAc;AACrC,yBAAa,UAAU,OAAO;AAAA;AAEhC,sBAAY;AACZ,6BAAmB;AACnB,wBAAc,eAAe;AAC7B,wBAAc,eAAe;AAC7B,wBAAc,eAAe;AAAA;AAE/B,YAAI,WAAW;AACf,YAAI,QAAQ,KAAK;AACjB,eAAO,OAAO,UAAU,eAAe,UAAU,MAAM;AACrD,mBAAS,KAAK;AACd,kBAAQ,MAAM;AACd,cAAI,UAAU,MAAM;AAClB,mBAAO,OAAO,UAAU,eAAe,MAAM,WAAW,MAAM;AAC5D,sBAAQ,MAAM;AAAA;AAAA;AAAA;AAIpB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAO,SAAS;AAChB,cAAI,KAAK,SAAS,OAAO,KAAK,YAAY;AACxC,gBAAI,WAAW,QAAQ;AACvB,gBAAI,aAAa,WAAW;AAC1B,mCAAqB,MAAM;AAC3B;AAAA;AAEF,gBAAI,cAAc,WAAW;AAC3B,kBAAI,UAAU,aAAa,KAAK;AAChC,kBAAI,QAAQ;AACZ,kBAAI,SAAS;AACX,wBAAQ,SAAS,QAAQ,IAAI;AAAA;AAE/B,mCAAqB,MAAM,MAAM;AACjC;AAAA;AAEF,gBAAI,KAAK,YAAY;AACnB,mCAAqB,MAAM,MAAM;AACjC;AAAA;AAEF,8BAAkB;AAAA,iBACb;AACL,2BAAe;AACf,8BAAkB;AAAA;AAAA;AAAA;AAIxB,UAAI,eAAe,SAAU,QAAQ,aAAa,MAAM,YAAY;AAClE,YAAI,eAAe;AACnB,YAAI,SAAS,OAAO,IAAI,WAAW;AACnC,iBAAS,KAAK,QAAQ,SAAU,QAAO,MAAM;AAC3C,kBAAQ;AAAA,iBACH;AACH,kBAAI,UAAU,mBAAmB,KAAK;AACtC,kBAAI,SAAS;AACX,qBAAK,aAAa,SAAS,QAAQ,IAAI;AAAA;AAEzC,kBAAI,UAAU,KAAK,WAAU,KAAK,YAAY;AAC5C,qBAAK,cAAc;AACnB,qBAAK,WAAW,cAAc;AAAA;AAEhC;AAAA,iBACG;AACH,qBAAO;AACP;AAAA,iBACG;AACH,qBAAO;AACP;AAAA,iBACG;AAAA,iBACA;AACH,qBAAO;AACP;AAAA,iBACG;AAAA,iBACA;AACH,qBAAO;AACP;AAAA,iBACG;AAAA,iBACA;AACH,kBAAI,WAAU,UAAU;AACtB,6BAAa,QAAQ;AAAA;AAEvB;AAAA,iBACG;AACH,kBAAI,4BAA4B,KAAK,SAAQ;AAC3C,qBAAK;AACL;AAAA;AAEF;AAAA;AAEF,cAAI,KAAK,QAAQ,mBAAmB,GAAG;AACrC,iBAAK;AACL;AAAA;AAEF,cAAI,KAAK,QAAQ,YAAY,GAAG;AAC9B;AAAA;AAEF,cAAI,oBAAoB,YAAY,SAAS,eAAe,YAAY,OAAO;AAC7E,yBAAa,QAAQ;AAAA;AAAA;AAGzB,YAAI,UAAU,KAAK,aAAa,iBAAiB;AAC/C,iBAAO,aAAa;AACpB,eAAK,KAAK,IAAI,SAAS,KAAK;AAAA;AAE9B,YAAI,YAAY,KAAK,aAAa,gBAAgB;AAChD,iBAAO,aAAa;AACpB,eAAK,KAAK,IAAI,SAAS,KAAK;AAAA;AAE9B,YAAI,cAAc,OAAO,IAAI,eAAe,cAAc,KAAK;AAC/D,YAAI,aAAa;AACf,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,oBAAoB,SAAU,QAAQ,SAAS;AACjD,YAAI;AACJ,YAAI,wBAAwB,oBAAoB;AAChD,YAAI,uBAAuB;AACzB,wBAAc,SAAS,QAAQ,sBAAsB,MAAM;AAAA;AAE7D,kBAAU,OAAO,SAAS;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA;AAAA,UAEF;AAAA,YACE;AAAA,YACA;AAAA;AAAA,UAEF;AAAA,YACE;AAAA,YACA,SAAU,KAAK,QAAQ;AACrB,qBAAO,OAAO,SAAS,IAAI,OAAO,QAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,OAAO,SAAS,IAAI,MAAM,IAAI,KAAK,QAAQ;AAAA;AAAA;AAAA;AAItH,YAAI,gBAAgB,qBAAqB;AACzC,YAAI,SAAS,SAAS;AAAA,UACpB,gBAAgB;AAAA,UAChB,gBAAgB;AAAA;AAElB,iBAAS,KAAK,OAAO,UAAU,SAAU,MAAM;AAC7C,cAAI,CAAC,KAAK,WAAW,OAAO;AAC1B,iBAAK,WAAW,QAAQ;AACxB,iBAAK,gBAAgB,KAAK;AAAA;AAE5B,cAAI,CAAC,KAAK,WAAW,OAAO;AAC1B,iBAAK,WAAW,QAAQ;AACxB,iBAAK,gBAAgB,KAAK;AAAA;AAAA;AAG9B,YAAI,YAAY,SAAS,IAAI;AAC7B,kBAAU,mBAAmB,SAAS,SAAU,OAAO;AACrD,cAAI,IAAI,MAAM,QAAQ;AACtB,iBAAO,KAAK;AACV,mBAAO,MAAM;AACb,iBAAK,KAAK,SAAS,aAAa,QAAQ,aAAa,MAAM,KAAK,KAAK;AACrE,gBAAI,KAAK,SAAS,UAAU,KAAK,UAAU,CAAC,KAAK,WAAW,QAAQ;AAClE,mBAAK;AAAA;AAAA;AAAA;AAIX,kBAAU,mBAAmB,SAAS,SAAU,OAAO;AACrD,cAAI,IAAI,MAAM,QAAQ,MAAM;AAC5B,iBAAO,KAAK;AACV,mBAAO,MAAM;AACb,wBAAY,KAAK,KAAK;AACtB,gBAAI,iDAAiD,KAAK,YAAY;AACpE,mBAAK;AAAA;AAEP,iBAAK,KAAK,SAAS;AAAA;AAAA;AAGvB,kBAAU,cAAc,OAAO,SAAU,OAAO;AAC9C,cAAI,IAAI,MAAM;AACd,iBAAO,KAAK;AACV,kBAAM,GAAG;AAAA;AAAA;AAGb,kBAAU,cAAc,KAAK,SAAU,OAAO;AAC5C,cAAI,IAAI,MAAM,QAAQ,MAAM,MAAM;AAClC,iBAAO,KAAK;AACV,mBAAO,MAAM;AACb,mBAAO,KAAK,KAAK;AACjB,mBAAO,KAAK,KAAK;AACjB,gBAAI,QAAQ,KAAK,QAAQ,iBAAiB,IAAI;AAC5C,mBAAK;AACL;AAAA;AAEF,gBAAI,QAAQ,KAAK,QAAQ,eAAe,GAAG;AACzC,qBAAO,KAAK,MAAM,KAAK;AACvB,kBAAI,MAAM;AACR,uBAAO,MAAM;AAAA;AAAA;AAGjB,gBAAI,CAAC,QAAQ,CAAC,MAAM;AAClB,mBAAK;AAAA,mBACA;AACL,kBAAI,QAAQ,CAAC,sBAAsB,KAAK,OAAO;AAC7C,qBAAK;AACL;AAAA;AAEF,mBAAK,KAAK;AAAA,gBACR;AAAA,gBACA;AAAA;AAAA;AAAA;AAAA;AAKR,YAAI,WAAW,UAAU,MAAM;AAC/B,YAAI,2BAA2B,SAAS;AACtC,wCAA8B;AAAA;AAEhC,kBAAU,SAAS,EAAE,UAAU,YAAY,WAAW,QAAQ,UAAU;AACxE,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,QAAQ,SAAS;AAC5C,eAAO,wBAAwB,UAAU,kBAAkB,QAAQ,WAAW;AAAA;AAGhF,UAAI,aAAa,SAAU,QAAQ,MAAM;AACvC,YAAI,SAAS,SAAS,IAAI,OAAO;AACjC,eAAO,cAAc,QAAQ,SAAU,OAAO;AAC5C,mBAAS,KAAK,OAAO,SAAU,MAAM;AACnC,iBAAK;AAAA;AAAA;AAGT,YAAI,WAAW,OAAO,MAAM,MAAM;AAAA,UAChC,mBAAmB;AAAA,UACnB,eAAe;AAAA;AAEjB,eAAO,SAAS,EAAE,UAAU,YAAY,WAAW,OAAO,QAAQ,UAAU;AAAA;AAE9E,UAAI,gBAAgB,SAAU,SAAS,WAAW;AAChD,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,oBAAoB,SAAU,QAAQ,MAAM,UAAU,YAAY;AACpE,YAAI,WAAW,OAAO,IAAI,OAAO,OAAO,EAAE,OAAO,kBAAkB;AACnE,YAAI,kBAAkB,qBAAqB,QAAQ,UAAU,UAAU;AACvE,eAAO,cAAc,gBAAgB,KAAK,WAAW,gBAAgB;AAAA;AAEvE,UAAI,gBAAgB,SAAU,QAAQ,SAAS,UAAU,YAAY;AACnE,YAAI,iBAAiB,oBAAoB,QAAQ,SAAS,UAAU;AACpE,YAAI,kBAAkB,WAAW,QAAQ,eAAe;AACxD,YAAI,OAAO,kBAAkB,uBAAuB,CAAC,eAAe,sBAAsB;AACxF,iBAAO,kBAAkB,QAAQ,iBAAiB,UAAU;AAAA,eACvD;AACL,iBAAO,cAAc,iBAAiB,eAAe;AAAA;AAAA;AAGzD,UAAI,UAAU,SAAU,QAAQ,MAAM,UAAU;AAC9C,YAAI,aAAa,cAAc;AAC/B,YAAI,UAAU,aAAa,aAAa,QAAQ,QAAQ;AACxD,eAAO,cAAc,QAAQ,SAAS,UAAU;AAAA;AAGlD,UAAI,cAAc,SAAU,QAAQ,MAAM;AACxC,eAAO,cAAc,MAAM;AAAA,UACzB,OAAO,mBAAmB;AAAA,UAC1B,OAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,KAAK;AACjC,eAAO,4CAA4C,KAAK;AAAA;AAE1D,UAAI,aAAa,SAAU,QAAQ,KAAK;AACtC,eAAO,cAAc,QAAQ,OAAO,yBAAyB,SAAS,SAAU,MAAM;AACpF,iBAAO,SAAS,IAAI,eAAe,MAAM,KAAK;AAAA;AAAA;AAGlD,UAAI,cAAc,SAAU,QAAQ,KAAK,aAAa;AACpD,eAAO,YAAY,MAAM,WAAY;AACnC,sBAAY,QAAQ;AAAA,WACnB,WAAY;AACb,iBAAO,cAAc,eAAe,MAAM;AAAA;AAE5C,eAAO;AAAA;AAET,UAAI,aAAa,SAAU,QAAQ,KAAK,aAAa;AACnD,eAAO,YAAY,MAAM,WAAY;AACnC,sBAAY,QAAQ;AAAA,WACnB,WAAY;AACb,iBAAO,YAAY,iBAAiB,OAAO;AAAA;AAE7C,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,QAAQ,MAAM,aAAa;AACvD,eAAO,OAAO,UAAU,kBAAkB,SAAS,cAAc,QAAQ,WAAW,QAAQ,MAAM,eAAe;AAAA;AAEnH,UAAI,cAAc,SAAU,QAAQ,MAAM,aAAa;AACrD,eAAO,WAAW,QAAQ,QAAQ,YAAY,QAAQ,MAAM,eAAe;AAAA;AAE7E,UAAI,qBAAqB,SAAU,QAAQ,MAAM;AAC/C,iBAAS,KAAK;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,WACC,SAAU,QAAQ;AACnB,iBAAO,OAAO,QAAQ,MAAM,iBAAiB;AAAA;AAAA;AAGjD,UAAI,gBAAgB,SAAU,QAAQ,MAAM,aAAa;AACvD,YAAI,eAAe,oBAAoB,YAAY,OAAO;AACxD,sBAAY,QAAQ;AAAA,eACf;AACL,6BAAmB,QAAQ;AAAA;AAAA;AAI/B,UAAI,0BAA0B,SAAU,GAAG;AACzC,eAAO,SAAY,QAAQ,OAAO;AAAA;AAEpC,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,MAAM,QAAQ,MAAM;AAAA;AAE7B,UAAI,YAAY,SAAU,MAAM,KAAK;AACnC,eAAO,MAAM,KAAK,UAAU,OAAO,IAAI,cAAc,KAAK,QAAQ;AAAA;AAEpE,UAAI,sBAAsB,SAAU,QAAQ,MAAM;AAChD,YAAI,WAAW,OAAO,KAAK,aAAa;AACxC,YAAI,iBAAiB,KAAK,QAAQ,OAAO;AACzC,YAAI,SAAS,MAAM,gBAAgB,SAAU,KAAK,GAAG;AACnD,cAAI,wBAAwB,MAAM,MAAM,MAAM;AAC5C,gBAAI,IAAI,aAAa,IAAI,QAAQ,MAAM,IAAI,IAAI,WAAW,eAAe,SAAS,KAAK,UAAU,gBAAgB,IAAI,IAAI,SAAS,IAAI;AACpI,qBAAO;AAAA,gBACL,WAAW;AAAA,gBACX,KAAK,IAAI,MAAM;AAAA;AAAA,mBAEZ;AACL,qBAAO;AAAA,gBACL,WAAW;AAAA,gBACX,KAAK,IAAI,MAAM;AAAA;AAAA;AAAA,iBAGd;AACL,mBAAO;AAAA,cACL,WAAW,cAAc;AAAA,cACzB,KAAK,IAAI,MAAM;AAAA;AAAA;AAAA,WAGlB;AAAA,UACD,WAAW;AAAA,UACX,KAAK;AAAA;AAEP,eAAO,OAAO;AAAA;AAGhB,UAAI,UAAU,SAAU,QAAQ,SAAS,UAAU,aAAa;AAC9D,YAAI,OAAO,QAAQ,QAAQ,SAAS;AACpC,YAAI,KAAK,cAAc,OAAO;AAC5B,wBAAc,QAAQ,KAAK,SAAS;AAAA;AAAA;AAGxC,UAAI,YAAY,SAAU,QAAQ,MAAM,cAAc;AACpD,YAAI,WAAW,eAAe,eAAe,SAAS;AACtD,gBAAQ,QAAQ,OAAO,OAAO,UAAU;AAAA;AAE1C,UAAI,YAAY,SAAU,QAAQ,MAAM;AACtC,YAAI,cAAc,OAAO,IAAI,OAAO,MAAM,QAAQ,SAAS;AAC3D,YAAI,iBAAiB,oBAAoB,QAAQ;AACjD,YAAI,OAAO,QAAQ,gBAAgB,mBAAmB,SAAS,wBAAwB;AACvF,gBAAQ,QAAQ,MAAM,OAAO;AAAA;AAE/B,UAAI,uBAAuB,SAAU,cAAc;AACjD,YAAI,QAAQ;AACZ,YAAI,uBAAuB;AAC3B,YAAI,cAAc;AAChB,cAAI,aAAa,SAAS;AACxB,gBAAI,aAAa,aAAa,QAAQ;AACtC,gBAAI,cAAc,WAAW,SAAS,GAAG;AACvC,kBAAI,WAAW,QAAQ,0BAA0B,IAAI;AACnD,sBAAM,gBAAgB;AAAA;AAAA;AAAA;AAI5B,cAAI,aAAa,OAAO;AACtB,qBAAS,IAAI,GAAG,IAAI,aAAa,MAAM,QAAQ,KAAK;AAClD,kBAAI,cAAc,aAAa,MAAM;AACrC,kBAAI;AACF,sBAAM,eAAe,aAAa,QAAQ;AAAA,uBACnC,IAAP;AACA,sBAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAK7B,eAAO;AAAA;AAET,UAAI,sBAAsB,SAAU,QAAQ,gBAAgB;AAC1D,eAAO,qBAAqB,eAAe,iBAAiB,OAAO,SAAS;AAAA;AAE9E,UAAI,iBAAiB,SAAU,kBAAkB,UAAU;AACzD,eAAO,YAAY,oBAAoB,iBAAiB,UAAU,SAAS;AAAA;AAE7E,UAAI,gBAAgB,SAAU,SAAS;AACrC,eAAO,eAAe,SAAS,gBAAgB,eAAe,SAAS;AAAA;AAEzE,UAAI,eAAe,SAAU,KAAK;AAChC,YAAI,UAAU,wCAAwC,KAAK;AAC3D,YAAI,SAAS;AACX,iBAAO;AAAA,YACL,MAAM,QAAQ;AAAA,YACd,MAAM,mBAAmB,QAAQ;AAAA;AAAA,eAE9B;AACL,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,MAAM;AAAA;AAAA;AAAA;AAIZ,UAAI,sBAAsB,SAAU,QAAQ,QAAQ;AAClD,YAAI,UAAS,uBAAuB;AACpC,eAAO,UAAS,QAAO,UAAU;AAAA;AAEnC,UAAI,kBAAkB,SAAU,QAAQ,KAAK;AAC3C,YAAI,IAAI,IAAI,MAAM;AAClB,eAAO,cAAc,KAAK,OAAO,IAAI,OAAO,EAAE,MAAM;AAAA;AAEtD,UAAI,WAAW,kBAAkB;AACjC,UAAI,aAAa,SAAU,QAAQ,WAAW;AAC5C,YAAI,KAAK,aAAa,UAAU,MAAM,SAAS,GAAG,MAAM,OAAO,GAAG;AAClE,YAAI,KAAK;AACT,YAAI,OAAO,UAAU;AACrB,YAAI,MAAM,IAAI;AACd,YAAI,MAAM,UAAU;AACpB,YAAI,oBAAoB,QAAQ,MAAM;AACpC,cAAI,YAAY,OAAO,aAAa;AACpC,cAAI,WAAW;AACf,cAAI,mBAAmB,UAAU,UAAU,QAAQ;AACnD,cAAI,CAAC,kBAAkB;AACrB,gBAAI,cAAc,uBAAuB,WAAW,cAAc,KAAK;AACvE,gBAAI,SAAS,cAAc,gBAAgB,QAAQ,KAAK,QAAQ;AAChE,gBAAI,WAAW,cAAc,KAAK,OAAO;AACzC,uBAAW,UAAU,OAAO,IAAI,MAAM,QAAQ,QAAQ;AACtD,sBAAU,IAAI;AAAA,iBACT;AACL,uBAAW;AAAA;AAEb,oBAAU,QAAQ,eAAe,SAAS,YAAY,MAAM;AAAA,eACvD;AACL,oBAAU,QAAQ,eAAe,UAAU,MAAM,MAAM;AAAA;AAAA;AAG3D,UAAI,mBAAmB,SAAU,OAAO;AACtC,eAAO,MAAM,SAAS;AAAA;AAExB,UAAI,qBAAqB,SAAU,MAAM;AACvC,eAAO,cAAc,KAAK;AAAA;AAE5B,UAAI,sBAAsB,SAAU,OAAO;AACzC,eAAO,SAAS,IAAI,IAAI,OAAO,SAAU,MAAM;AAC7C,iBAAO,IAAI,SAAS,SAAU,SAAS;AACrC,gBAAI,OAAO,mBAAmB,QAAQ,KAAK,cAAc;AACzD,gBAAI,SAAS,IAAI,OAAO;AACxB,mBAAO,SAAS,WAAY;AAC1B,sBAAQ;AAAA,gBACN;AAAA,gBACA,KAAK,OAAO;AAAA;AAAA;AAGhB,mBAAO,cAAc;AAAA;AAAA;AAAA;AAI3B,UAAI,UAAU,SAAU,QAAQ;AAC9B,YAAI,oBAAoB,yBAAyB;AACjD,eAAO,SAAU,MAAM;AACrB,iBAAO,WAAW,KAAK,MAAM,aAAa,OAAO,mBAAmB,SAAU,WAAW;AACvF,mBAAO,iBAAiB,eAAe,KAAK;AAAA;AAAA;AAAA;AAIlD,UAAI,4BAA4B,SAAU,QAAQ,cAAc;AAC9D,YAAI,QAAQ,aAAa,QAAQ,KAAK,KAAK,aAAa,QAAQ,SAAU,MAAM;AAC9E,iBAAO,KAAK,SAAS,SAAS,CAAC,KAAK,eAAe;AAAA,aAChD;AACL,YAAI,QAAQ,aAAa,QAAQ,KAAK,aAAa,SAAS;AAC5D,eAAO,SAAS,MAAM,SAAS,IAAI,QAAQ,OAAO,QAAQ;AAAA;AAE5D,UAAI,iBAAiB,SAAU,QAAQ,GAAG,KAAK;AAC7C,YAAI,eAAe,iBAAiB,KAAK,EAAE,gBAAgB,EAAE;AAC7D,YAAI,mBAAmB,WAAW,cAAc;AAC9C,cAAI,SAAS,0BAA0B,QAAQ;AAC/C,cAAI,OAAO,SAAS,GAAG;AACrB,cAAE;AACF,gCAAoB,QAAQ,KAAK,SAAU,aAAa;AACtD,kBAAI,KAAK;AACP,uBAAO,UAAU,OAAO;AAAA;AAE1B,mBAAK,aAAa,SAAU,QAAQ;AAClC,2BAAW,QAAQ;AAAA;AAAA;AAGvB,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,gCAAgC,SAAU,GAAG;AAC/C,YAAI,gBAAgB,EAAE;AACtB,eAAO,UAAU,UAAU,QAAQ,eAAe,MAAM,iBAAiB,cAAc,SAAS,cAAc,MAAM,WAAW;AAAA;AAEjI,UAAI,uBAAuB,SAAU,GAAG;AACtC,eAAO,SAAS,eAAe,MAAM,EAAE,YAAY,MAAM,EAAE,YAAY,EAAE,YAAY;AAAA;AAEvF,UAAI,wBAAwB,SAAU,QAAQ,UAAU,aAAa;AACnE,YAAI,qBAAqB;AACzB,YAAI,uBAAuB;AAC3B,YAAI;AACJ,eAAO,GAAG,SAAS,qBAAqB;AACxC,eAAO,GAAG,WAAW,SAAU,GAAG;AAChC,cAAI,wBAAwB,SAAU,IAAG;AACvC,gBAAI,qBAAqB,OAAM,CAAC,GAAE,sBAAsB;AACtD,uBAAS;AAAA;AAAA;AAGb,cAAI,qBAAqB,MAAM,CAAC,EAAE,sBAAsB;AACtD,0CAA8B,EAAE,YAAY,EAAE,YAAY;AAC1D,gBAAI,+BAA+B,SAAS,UAAU,UAAU,UAAU,QAAQ,gBAAgB,IAAI;AACpG;AAAA;AAEF,cAAE;AACF,+BAAmB,IAAI;AACvB,iCAAqB,IAAI;AACzB,gBAAI,SAAS,MAAM,6BAA6B;AAC9C,gBAAE;AACF,wBAAU,QAAQ;AAClB;AAAA;AAEF,qBAAS;AACT,qBAAS;AACT,mBAAO,KAAK,SAAS;AACrB,mBAAO,KAAK,SAAS,WAAY;AAC/B,qBAAO,IAAI,SAAS;AAAA;AAAA;AAAA;AAI1B,YAAI,yBAAyB,SAAU,SAAQ,kBAAkB,iBAAiB,eAAe,UAAU;AACzG,cAAI;AACJ,cAAI,eAAe,kBAAkB,cAAc;AACjD,sBAAU,iBAAiB;AAAA,iBACtB;AACL,sBAAU,SAAS;AACnB,uBAAW,WAAW,WAAW,SAAS;AAC1C,gBAAI,SAAS,iBAAiB,UAAU;AACtC,8BAAgB;AAAA;AAAA;AAGpB,oBAAU,SAAS;AACnB,mBAAS;AACT,cAAI,kBAAkB,aAAa,SAAS,YAAY;AACxD,cAAI,kBAAkB,cAAc;AACpC,cAAI,CAAC,QAAQ,UAAU,mBAAmB,CAAC,iBAAiB;AAC1D,4BAAgB;AAAA;AAElB,cAAI,iBAAiB,iBAAiB;AACpC,gBAAI,eAAe,kBAAkB,iBAAiB,iBAAiB;AACrE,wBAAU,iBAAiB;AAAA,mBACtB;AACL,wBAAU,UAAU;AAAA;AAAA;AAGxB,cAAI,SAAS,iBAAiB,UAAU;AACtC,gBAAI,CAAC,iBAAiB;AACpB,sBAAO,cAAc,MAAM;AAAA;AAE7B;AAAA;AAEF,cAAI,eAAe;AACjB,sBAAU,SAAQ;AAAA,iBACb;AACL,sBAAU,SAAQ,SAAS;AAAA;AAAA;AAG/B,YAAI,aAAa,WAAY;AAC3B,iBAAO,SAAS,gBAAgB,OAAO,UAAU;AAAA;AAEnD,eAAO,GAAG,SAAS,SAAU,GAAG;AAC9B,cAAI,kBAAkB,mBAAmB,WAAW,qBAAqB;AACzE,cAAI,iBAAiB;AACnB,+BAAmB;AAAA;AAErB,cAAI,mBAAmB,oBAAoB,QAAQ;AACnD,cAAI,gBAAgB,YAAY,UAAU,UAAU;AACpD,cAAI,WAAW,eAAe,kBAAkB;AAChD,wCAA8B;AAC9B,cAAI,EAAE,wBAAwB,8BAA8B,IAAI;AAC9D,qBAAS;AACT;AAAA;AAEF,cAAI,CAAC,cAAc,qBAAqB,eAAe,QAAQ,GAAG,eAAe;AAC/E,qBAAS;AACT;AAAA;AAEF,cAAI,CAAC,iBAAiB;AACpB,cAAE;AAAA;AAEJ,cAAI,SAAS,MAAO,EAAC,mBAAmB,EAAE,WAAW,CAAC,eAAe,kBAAkB,cAAc;AACnG,qBAAS;AACT,mBAAO,IAAI,KAAK,SAAS,SAAS,SAAS,SAAU,IAAG;AACtD,iBAAE;AAAA;AAEJ,mBAAO,SAAS,YAAY,SAAS,OAAO;AAC5C,6BAAiB,eAAe,SAAS;AAAA;AAE3C,cAAI,eAAe,kBAAkB,cAAc;AACjD,cAAE;AACF,gBAAI,CAAC,UAAU;AACb,yBAAW,SAAS,iBAAiB;AAAA;AAEvC,mCAAuB,QAAQ,kBAAkB,iBAAiB,eAAe;AAAA,iBAC5E;AACL,qBAAS,iBAAiB,QAAQ,WAAY;AAC5C,qCAAuB,QAAQ,kBAAkB,iBAAiB,eAAe;AAAA,eAChF;AAAA;AAAA;AAAA;AAIT,UAAI,2BAA2B,SAAU,QAAQ,UAAU,aAAa;AACtE,8BAAsB,QAAQ,UAAU;AACxC,YAAI;AACJ,eAAO,OAAO,cAAc,OAAO,SAAU,OAAO,MAAM,MAAM;AAC9D,cAAI,gBAAgB,SAAU,OAAM;AAClC,mBAAO,MAAK,QAAQ,MAAK,KAAK,UAAU;AAAA;AAE1C,cAAI,UAAS,SAAU,MAAM;AAC3B,gBAAI,CAAC,KAAK,KAAK,sBAAsB,QAAQ,SAAS,gBAAgB;AACpE,mBAAK;AAAA;AAAA;AAGT,cAAI,kBAAkB,SAAU,MAAK;AACnC,mBAAO,KAAI,QAAQ,uBAAuB;AAAA;AAE5C,cAAI,YAAY,SAAU,MAAK;AAC7B,mBAAO,KAAI,QAAQ,aAAa;AAAA;AAElC,cAAI,CAAC,mBAAmB,WAAW,cAAc,OAAO;AACtD,gBAAI,IAAI,MAAM;AACd,mBAAO,KAAK;AACV,oBAAM,MAAM,GAAG,KAAK;AACpB,kBAAI,CAAC,KAAK;AACR;AAAA;AAEF,kBAAI,gBAAgB,MAAM;AACxB,wBAAO,MAAM;AAAA,yBACJ,CAAC,qBAAqB,WAAW,UAAU,MAAM;AAC1D,wBAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAOvB,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,SAAS,MAAM,OAAO,SAAS,SAAS,OAAO,OAAO;AAAA;AAE/D,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,kBAAkB,YAAY,OAAO;AAAA;AAE9C,UAAI,sBAAsB,SAAU,QAAQ,aAAa,wBAAwB;AAC/E,YAAI,mBAAmB,SAAS;AAC9B,iBAAO,IAAI,KAAK,aAAa,eAAe,SAAU,IAAI;AACxD,gBAAI,CAAC,UAAU,QAAQ,yBAAyB;AAC9C,qBAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAKpB,UAAI,SAAS,SAAU,QAAQ,aAAa,wBAAwB;AAClE,YAAI,MAAM,OAAO,KAAK,OAAO,OAAO;AACpC,oBAAY,IAAI,OAAO,UAAU;AACjC,YAAI,cAAc,OAAO,IAAI,IAAI,kBAAkB,SAAS,OAAO;AAAA,UACjE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,mBAAmB;AAAA,UACnB,kBAAkB;AAAA,UAClB,SAAS;AAAA,WACR;AACH,YAAI,SAAS,MAAM,SAAS,OAAO;AACjC,cAAI,SAAS,aAAa,QAAQ,IAAI,SAAS,MAAM,aAAa,UAAU,QAAQ,QAAQ;AAAA;AAE9F,YAAI,KAAK,aAAa,qCAAqC,SAAU,GAAG;AACtE,YAAE;AAAA;AAEJ,4BAAoB,QAAQ,aAAa;AACzC,oBAAY;AACZ,eAAO,UAAU,OAAO,aAAa;AAAA;AAEvC,UAAI,SAAS,SAAU,QAAQ,aAAa;AAC1C,YAAI,MAAM,SAAS;AACjB,cAAI,gBAAgB;AACpB,cAAI,UAAU,YAAY;AAC1B,iBAAO,gBAAgB,OAAO,IAAI,IAAI,gBAAgB;AACpD,mBAAO,IAAI,OAAO;AAClB,mBAAO,IAAI,OAAO;AAAA;AAEpB,cAAI,SAAS;AACX,mBAAO,UAAU,OAAO;AAAA;AAAA;AAG5B,oBAAY,IAAI;AAAA;AAElB,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,OAAO,IAAI,IAAI;AAAA;AAExB,UAAI,UAAU,SAAU,QAAQ;AAC9B,YAAI,gBAAgB,SAAU,OAAO,SAAS;AAC5C,gBAAM,YAAY;AAClB,iBAAO,IAAI,OAAO,SAAS;AAAA;AAE7B,YAAI,iBAAiB,SAAS,KAAK,kBAAkB,QAAQ,YAAY,SAAU,KAAK;AACtF,iBAAO,IAAI,OAAO;AAAA;AAEpB,YAAI,cAAc,eAAe;AACjC,iBAAS,KAAK,gBAAgB,SAAU,eAAe;AACrD,wBAAc,aAAa;AAAA;AAE7B,YAAI,gBAAgB,OAAO,IAAI,OAAO,uBAAuB;AAC7D,iBAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AAClD,cAAI,eAAe,OAAO,IAAI,OAAO;AACrC,sBAAY,aAAa,cAAc,cAAc;AACrD,wBAAc,cAAc,cAAc;AAAA;AAE5C,eAAO,cAAc,YAAY,YAAY;AAAA;AAE/C,UAAI,mBAAmB,SAAU,wBAAwB,SAAS;AAChE,eAAO,YAAY;AAAA;AAErB,UAAI,aAAa,SAAU,KAAK;AAC9B,eAAO,OAAO,IAAI,OAAO;AAAA;AAE3B,UAAI,YAAY,SAAU,QAAQ,wBAAwB;AACxD,YAAI,cAAc,MAAM;AACxB,eAAO,WAAW,gBAAgB,iBAAiB,wBAAwB,YAAY;AAAA;AAEzF,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,UAAU,KAAK;AACnB,YAAI,yBAAyB;AAC7B,eAAO;AAAA,UACL,QAAQ,WAAY;AAClB,mBAAO,OAAO,QAAQ,SAAS;AAAA;AAAA,UAEjC,QAAQ,WAAY;AAClB,mBAAO,OAAO,QAAQ;AAAA;AAAA,UAExB,OAAO,WAAY;AACjB,mBAAO,MAAM;AAAA;AAAA,UAEf,SAAS,WAAY;AACnB,mBAAO,QAAQ;AAAA;AAAA,UAEjB,YAAY,QAAQ;AAAA,UACpB,WAAW,WAAY;AACrB,mBAAO,UAAU,QAAQ;AAAA;AAAA,UAE3B,kBAAkB,SAAU,SAAS;AACnC,mBAAO,iBAAiB,wBAAwB;AAAA;AAAA;AAAA;AAKtD,UAAI,YAAY,SAAU,QAAQ,aAAa;AAC7C,YAAI,WAAW,SAAS;AACxB,eAAO,GAAG,WAAW,WAAY;AAC/B,iBAAO,yBAAyB,QAAQ,UAAU;AAAA;AAEpD,eAAO;AAAA,UACL;AAAA,UACA,WAAW,SAAU,MAAM,cAAc;AACvC,mBAAO,UAAU,QAAQ,MAAM;AAAA;AAAA,UAEjC,WAAW,SAAU,MAAM;AACzB,mBAAO,UAAU,QAAQ;AAAA;AAAA,UAE3B,gBAAgB,SAAU,GAAG,KAAK;AAChC,mBAAO,eAAe,QAAQ,GAAG;AAAA;AAAA,UAEnC;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,uBAAuB,SAAU,QAAQ,WAAW;AACtD,YAAI,UAAU,YAAY,UAAU,QAAQ;AAC1C,oBAAU,YAAY,IAAI;AAC1B,mCAAyB,QAAQ;AAAA,eAC5B;AACL,oBAAU,YAAY,IAAI;AAC1B,mCAAyB,QAAQ;AAAA;AAEnC,eAAO;AAAA;AAGT,UAAI,aAAa,SAAU,QAAQ,WAAW;AAC5C,eAAO,WAAW,2BAA2B,WAAY;AACvD,+BAAqB,QAAQ;AAAA;AAE/B,eAAO,WAAW,6BAA6B,SAAU,IAAI,QAAO;AAClE,cAAI,OAAM,SAAS;AACjB,sBAAU,UAAU,OAAM,SAAS,OAAM;AAAA;AAE3C,cAAI,OAAM,MAAM;AACd,sBAAU,UAAU,OAAM;AAAA;AAAA;AAAA;AAKhC,UAAI,yBAAyB,SAAU,eAAe;AACpD,eAAO,SAAS,QAAQ,SAAS,OAAQ,mBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,aAAa;AAAA;AAEpI,UAAI,oBAAoB,SAAU,eAAe,MAAM,MAAM;AAC3D,YAAI,uBAAuB,gBAAgB;AACzC,cAAI;AACF,0BAAc;AACd,0BAAc,QAAQ,aAAa;AACnC,0BAAc,QAAQ,cAAc;AACpC,0BAAc,QAAQ,oBAAoB;AAC1C,mBAAO;AAAA,mBACA,GAAP;AACA,mBAAO;AAAA;AAAA,eAEJ;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,mBAAmB,SAAU,KAAK,MAAM,WAAU,MAAM;AAC1D,YAAI,kBAAkB,IAAI,eAAe,KAAK,MAAM,KAAK,OAAO;AAC9D,cAAI;AACJ;AAAA,eACK;AACL,oBAAS,KAAK,MAAM;AAAA;AAAA;AAGxB,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,SAAU,MAAM,MAAM;AAC3B,cAAI,aAAa,KAAK;AACtB,cAAI,QAAQ,OAAO,IAAI,OAAO,OAAO;AAAA,YACnC,mBAAmB;AAAA,YACnB,kBAAkB;AAAA;AAEpB,cAAI,QAAQ,OAAO,IAAI,OAAO,OAAO,EAAE,iBAAiB,UAAU;AAClE,iBAAO,IAAI,UAAU,OAAO;AAAA,YAC1B,UAAU;AAAA,YACV,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA;AAEZ,gBAAM,YAAY;AAClB,iBAAO,IAAI,IAAI,OAAO,WAAW;AACjC,cAAI,QAAQ,OAAO,UAAU;AAC7B,gBAAM;AACN,cAAI,iBAAiB,OAAO,IAAI;AAChC,yBAAe,mBAAmB;AAClC,iBAAO,UAAU,OAAO;AACxB,mBAAS,WAAW,WAAY;AAC9B,mBAAO,UAAU,OAAO;AACxB,kBAAM,WAAW,YAAY;AAC7B;AAAA,aACC;AAAA;AAAA;AAGP,UAAI,UAAU,SAAU,QAAQ;AAC9B,eAAO;AAAA,UACL,MAAM,OAAO,UAAU,WAAW,EAAE,YAAY;AAAA,UAChD,MAAM,OAAO,UAAU,WAAW,EAAE,QAAQ;AAAA;AAAA;AAGhD,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,CAAC,CAAC,OAAO,IAAI,UAAU,OAAO,UAAU,YAAY,+CAA+C,OAAO;AAAA;AAEnH,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,CAAC,OAAO,UAAU,iBAAiB,iBAAiB;AAAA;AAE7D,UAAI,MAAM,SAAU,QAAQ;AAC1B,eAAO,SAAU,KAAK;AACpB,cAAI,mBAAmB,SAAS;AAC9B,6BAAiB,KAAK,QAAQ,SAAS,SAAS,SAAS,WAAY;AACnE,kBAAI,SAAS,QAAQ,cAAc,SAAS,QAAQ,aAAa;AAC/D,oBAAI,QAAQ,OAAO,UAAU;AAC7B,yBAAS,iBAAiB,QAAQ,WAAY;AAC5C,yBAAO,UAAU,OAAO;AACxB,yBAAO,YAAY;AAAA,mBAClB;AAAA,qBACE;AACL,uBAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7B,UAAI,OAAO,SAAU,QAAQ;AAC3B,eAAO,SAAU,KAAK;AACpB,cAAI,mBAAmB,SAAS;AAC9B,6BAAiB,KAAK,QAAQ,SAAS,SAAS,SAAS;AAAA;AAAA;AAAA;AAI/D,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,GAAG,OAAO,IAAI;AACrB,eAAO,GAAG,QAAQ,KAAK;AAAA;AAGzB,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,yBAAyB,SAAU,QAAQ,GAAG;AAChD,eAAO,OAAO,uBAAuB,EAAE,SAAS,EAAE,SAAS,OAAO;AAAA;AAEpE,UAAI,qBAAqB,SAAU,SAAS;AAC1C,YAAI,mBAAmB,QAAQ;AAC/B,eAAO,mBAAmB,iBAAiB,QAAQ,eAAe,IAAI;AAAA;AAExE,UAAI,kBAAkB,SAAU,QAAQ,KAAK;AAC3C,eAAO;AACP,eAAO,UAAU,OAAO;AAAA;AAE1B,UAAI,UAAU,SAAU,QAAQ,WAAW,yBAAyB;AAClE,YAAI,gBAAgB,SAAS;AAC3B,iBAAO,GAAG,mDAAmD,SAAU,GAAG;AACxE,cAAE;AACF,cAAE;AAAA;AAAA;AAGN,YAAI,CAAC,sBAAsB,SAAS;AAClC,iBAAO,GAAG,QAAQ,SAAU,GAAG;AAC7B,gBAAI,eAAe,EAAE;AACrB,gBAAI,gBAAgB,aAAa,SAAS,aAAa,MAAM,SAAS,GAAG;AACvE,gBAAE;AAAA;AAAA;AAAA;AAIR,eAAO,GAAG,QAAQ,SAAU,GAAG;AAC7B,cAAI,MAAM,uBAAuB,QAAQ;AACzC,cAAI,EAAE,wBAAwB,wBAAwB,OAAO;AAC3D;AAAA;AAEF,cAAI,cAAc,UAAU,qBAAqB,EAAE;AACnD,cAAI,WAAW,UAAU,eAAe,aAAa;AACrD,cAAK,EAAC,UAAU,cAAc,gBAAgB,mBAAmB,iBAAiB,UAAU,eAAe,GAAG,MAAM;AAClH;AAAA;AAEF,cAAI,OAAO,iBAAiB,SAAS;AACnC,gBAAI,YAAY,YAAY,mBAAmB,YAAY,gBAAgB,YAAY;AACvF,gBAAI,WAAW;AACb,gBAAE;AACF,uBAAS,iBAAiB,QAAQ,WAAY;AAC5C,uBAAO,YAAY,SAAS,WAAY;AACtC,sBAAI,YAAY,iBAAiB;AAC/B,2BAAO,YAAY;AAAA;AAErB,kCAAgB,QAAQ;AACxB,8BAAY,SAAS;AACrB,sBAAI,CAAC,YAAY,cAAc;AAC7B,8BAAU,UAAU;AAAA,yBACf;AACL,8BAAU,UAAU,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAO3C,eAAO,GAAG,aAAa,SAAU,IAAI;AACnC,kCAAwB,IAAI;AAAA;AAE9B,eAAO,GAAG,oBAAoB,SAAU,GAAG;AACzC,cAAI,sBAAsB,WAAW,wBAAwB,UAAU,OAAO;AAC5E,cAAE;AACF,4BAAgB,QAAQ,uBAAuB,QAAQ;AAAA;AAEzD,cAAI,EAAE,SAAS,WAAW;AACxB,oCAAwB,IAAI;AAAA;AAAA;AAAA;AAKlC,UAAI,UAAU,SAAU,QAAQ;AAC9B,YAAI,SAAS,OAAO,QAAQ;AAC5B,YAAI,cAAa,cAAc;AAC/B,YAAI,aAAY;AACd,iBAAO,GAAG,mBAAmB,SAAU,GAAG;AACxC,wBAAW,KAAK,QAAQ,QAAQ;AAAA;AAAA;AAGpC,YAAI,cAAc,eAAe;AACjC,YAAI,aAAa;AACf,iBAAO,GAAG,oBAAoB,SAAU,GAAG;AACzC,wBAAY,KAAK,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAKvC,UAAI,sBAAsB,SAAU,QAAQ,YAAY;AACtD,eAAO,GAAG,mBAAmB,SAAU,GAAG;AACxC,YAAE,UAAU,WAAW,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE;AAAA;AAAA;AAG5D,UAAI,uBAAuB,SAAU,QAAQ,YAAY;AACvD,eAAO,GAAG,oBAAoB,SAAU,GAAG;AACzC,qBAAW,QAAQ,EAAE;AAAA;AAAA;AAGzB,UAAI,sCAAsC,SAAU,QAAQ,MAAM;AAChE,YAAI,CAAC,cAAc,OAAO;AACxB,iBAAO;AAAA;AAET,YAAI,gBAAgB;AACpB,iBAAS,KAAK,OAAO,OAAO,oBAAoB,SAAU,OAAO,WAAW;AAC1E,wBAAc,KAAK;AAAA;AAErB,YAAI,uBAAuB,IAAI,OAAO,4CAA4C,cAAc,KAAK,OAAO,4CAA4C;AACxJ,eAAO,OAAO,MAAM,CAAC;AAAA,UACjB;AAAA,UACA;AAAA;AAEJ,eAAO,OAAO,MAAM;AAAA,UAClB;AAAA,YACE;AAAA,YACA;AAAA;AAAA,UAEF;AAAA,YACE;AAAA,YACA;AAAA;AAAA,UAEF;AAAA,YACE;AAAA,YACA;AAAA;AAAA;AAGJ,eAAO;AAAA;AAET,UAAI,qBAAqB,SAAU,QAAQ,SAAS,UAAU,YAAY;AACxE,YAAI,cAAc,UAAU;AAC1B,iBAAO;AAAA;AAET,YAAI,sBAAsB,gBAAgB;AAC1C,YAAI;AACJ,YAAI,yBAAyB,YAAY,SAAS,wBAAwB,OAAO;AAC/E,iBAAO;AAAA;AAET,YAAI,qBAAqB;AACvB,yBAAe,oBAAoB,MAAM;AAAA;AAE3C,YAAI,cAAc;AAChB,cAAI,QAAQ,OAAO,KAAK,SAAS,OAAO,UAAU;AAClD,oBAAU,QAAQ,QAAQ,sCAAsC,SAAU,KAAK,QAAQ,QAAO,OAAO;AACnG,gBAAI,cAAc,MAAM,WAAW,MAAM,OAAO;AAChD,gBAAI,eAAe;AACnB,gBAAI,iBAAiB,QAAQ;AAC3B,qBAAO,SAAS;AAAA;AAElB,qBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,kBAAI,aAAa,YAAY,aAAa,KAAK,eAAe,MAAM,SAAS,QAAQ,aAAa,IAAI;AACtG,kBAAI,QAAQ,KAAK,aAAa,KAAK;AACjC,6BAAa,MAAM,MAAM;AACzB,+BAAe,MAAM,MAAM;AAAA;AAE7B,kBAAI,iBAAiB,YAAY;AAC/B,6BAAa,aAAa,MAAM;AAAA;AAAA;AAGpC,gBAAI,cAAc,MAAM,eAAe,cAAc;AACrD,gBAAI,aAAa;AACf,qBAAO,SAAS,aAAa,cAAc,MAAM;AAAA;AAEnD,mBAAO,SAAS;AAAA;AAAA,eAEb;AACL,oBAAU,QAAQ,QAAQ,sCAAsC;AAAA;AAElE,kBAAU,QAAQ,QAAQ,+CAA+C,SAAU,KAAK,QAAQ,QAAO,OAAO;AAC5G,iBAAO,SAAS,aAAa,SAAQ,MAAM;AAAA;AAE7C,eAAO;AAAA;AAET,UAAI,iCAAiC,SAAU,QAAQ,MAAM;AAC3D,eAAO,EAAE,KAAK,MAAM,KAAK,UAAU,KAAK,SAAU,GAAG,MAAM;AACzD,iBAAO,IAAI,OAAO,MAAM;AAAA;AAAA;AAG5B,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI,SAAS,QAAQ;AACnB,8BAAoB,QAAQ;AAAA;AAE9B,YAAI,SAAS,IAAI;AACf,8BAAoB,QAAQ;AAC5B,+BAAqB,QAAQ;AAAA;AAAA;AAIjC,UAAI,mBAAmB,SAAU,QAAQ,WAAW;AAClD,eAAO,SAAU,KAAK;AACpB,cAAI,UAAU,UAAU,YAAY,UAAU;AAC9C,cAAI,8BAA8B,SAAU,GAAG;AAC7C,mBAAO,IAAI,UAAU,EAAE;AAAA;AAEzB,iBAAO,GAAG,wBAAwB;AAClC,iBAAO,WAAY;AACjB,mBAAO,OAAO,IAAI,wBAAwB;AAAA;AAAA;AAAA;AAIhD,UAAI,WAAW,SAAU,QAAQ,WAAW;AAC1C,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,gBAAgB,aAAa;AAAA,UAC9C,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA,SAAS,iBAAiB,QAAQ;AAAA;AAEpC,eAAO,GAAG,SAAS,kBAAkB,aAAa;AAAA,UAChD,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,SAAS,iBAAiB,QAAQ;AAAA;AAAA;AAItC,wBAAmB;AACjB,iBAAS,IAAI,SAAS,SAAU,QAAQ;AACtC,cAAI,aAAa,YAAY,OAAO;AAClC,gBAAI,0BAA0B,KAAK;AACnC,gBAAI,cAAc,KAAK,qBAAqB,UAAU,SAAS;AAC/D,gBAAI,YAAY,UAAU,QAAQ;AAClC,kBAAM;AACN,qBAAS,QAAQ;AACjB,uBAAW,QAAQ;AACnB,oBAAQ;AACR,uBAAW;AACX,oBAAQ,QAAQ,WAAW;AAC3B,mBAAO,IAAI;AAAA;AAAA;AAAA;AAKjB;AAAA;AAAA;AAAA;;;ACxwDJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,gCAAQ;", "names": []}