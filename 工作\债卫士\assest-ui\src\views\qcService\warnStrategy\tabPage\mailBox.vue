<template>
    <div class="ml20">
        <div class="operation-revealing-area mb20">
            <div class="operation-revealing-area-btn">
                <el-button :loading="loading" type="primary" @click="update()">添加邮箱</el-button>
            </div>
            <right-toolbar :columns="columns" @queryTable="getList" :types="[2, 3]" />
        </div>
        <div class="table-box">
            <el-table :data="dataList" v-loading="loading">
                <el-table-column label="邮箱名称" v-if="columns[0].visible" align="center" prop="emailName" />
                <el-table-column label="邮箱账号" v-if="columns[1].visible" align="center" prop="emailAccount" />
                <el-table-column label="服务密码" v-if="columns[2].visible" align="center" prop="password" />
                <el-table-column label="状态" v-if="columns[3].visible" align="center" prop="status"
                    :formatter="row => switchStatusEnum[row.status]" />
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <div>
                            <el-button :loading="loading" type="text" @click="update(row)">编辑</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
        <mailBoxUpdateInfo :getList="getList" ref="mailBoxUpdateInfoRef" />
    </div>
</template>

<script setup>
import { switchStatusEnum } from '@/utils/enum';
import mailBoxUpdateInfo from '../dialog/mailBoxUpdateInfo';
import { warnStrategyEmailListApi } from '@/api/qcService/warnStrategy';
const { proxy } = getCurrentInstance()
const queryParams = ref({
    pageNum: 1, pageSize: 10
})
const total = ref(1)
const dataList = ref([])
const loading = ref(false)
const columns = ref([
    { "key": 0, "label": "邮箱名称", "visible": true },
    { "key": 1, "label": "邮箱账号", "visible": true },
    { "key": 2, "label": "服务密码", "visible": true },
    { "key": 3, "label": "状态", "visible": true },
])
getList()
function getList() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    loading.value = true
    for (const key in reqForm) {
        if (Array.isArray(reqForm[key])) {
            reqForm[key] = String(reqForm[key])
        }
    }
    warnStrategyEmailListApi(reqForm).then(res => {
        total.value = res.total
        dataList.value = res.rows
    }).finally(() => loading.value = false)
}
function update(row) {
    const title = row ? '编辑' : '添加'
    const data = row ? JSON.parse(JSON.stringify(row)) : {}
    proxy.$refs['mailBoxUpdateInfoRef'].openDialog({ row: data, title })
}
</script>

<style lang="scss" scoped>
.operation-revealing-area-btn {
    display: flex;
    align-items: center;
}
</style>