<template>
  <div class="app-container">
    <!-- 筛选表达 -->
    <el-form label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef" class="search-form">
          <el-form-item prop="projectId" label="项目ID"               
            style="width:320px">
            <MultiSelect
              v-model="queryParams.projectId"
              :options="searchOptions.projectId"
              placeholder="项目ID"
            />
          </el-form-item>
          <el-form-item prop="projectName" label="项目名称" 
          style="width:320px">
            <MultiSelect
              v-model="queryParams.projectName"
              :options="searchOptions.projectName"
              placeholder="项目名称"
            />
          </el-form-item>
          <el-form-item prop="productType" label="产品类型" 
          style="width:320px">
            <MultiSelect
              v-model="queryParams.productType"
              :options="searchOptions.productType"
              placeholder="产品类型"
            />
          </el-form-item>
          <el-form-item prop="transferor" label="资产转让方" style="width:320px">
            <MultiSelect
              v-model="queryParams.transferor"
              :options="searchOptions.transferor"
              placeholder="资产转让方"
            />
          </el-form-item>
          <el-form-item prop="biddingMethod" label="投标方式" 
          style="width:320px">
            <MultiSelect
              v-model="queryParams.biddingMethod"
              :options="searchOptions.biddingMethod"
              placeholder="投标方式"
            />
          </el-form-item>
          <el-form-item prop="debtTotalAmount" label="债权总金额">
            <div class="range-area" style="width:320px">
              <el-input
                v-model="queryParams.debtTotalAmountMin"
              />
              <span >—</span>
              <el-input
                v-model="queryParams.debtTotalAmountMax"
              />
            </div>
          </el-form-item>
          <el-form-item prop="principalAmount" label="债权本金">
            <div class="range-area" style="width:320px">
              <el-input
                v-model="queryParams.principalAmountMin"
              />
              <span >—</span>
              <el-input
                v-model="queryParams.principalAmountMax"
              />
            </div>
          </el-form-item>
          <el-form-item prop="projectManager" label="立项人" style="width:320px">
            <MultiSelect
              v-model="queryParams.projectManager"
              :options="searchOptions.projectManager"
            />
          </el-form-item>
          <el-form-item prop="projectTime" label="立项时间"   style="width:320px">
            <el-date-picker
              v-model="queryParams.projectTime"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            
            />
          </el-form-item>
          <el-form-item prop="baselineDate" label="基准日"   style="width:320px">
            <el-date-picker
              v-model="queryParams.baselineDate"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width:320px"
            />
          </el-form-item>
          <el-form-item prop="expectedBiddingDate" label="预计竞价日期"   style="width:320px">
            <el-date-picker
              v-model="queryParams.expectedBiddingDate"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />
          </el-form-item>
    </el-form>
    <!-- 过滤按钮 -->
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <!-- 新增按钮 -->
    <div class="operation-revealing-area mb20">
      <el-button type="primary" @click="handleOpenDailog('applayRef')">提交立项</el-button>
      <el-button type="primary" @click="handleAdd()">新增立项</el-button>
      <el-button type="primary" @click="handleOpenDailog('applayRef')">删除</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <!-- 全选功能 -->
    <SelectedAll :dataList="dataList" v-model:selectedArr="selectedArr" v-model="queryParams.allQuery">
        <template #content>
            <div class="ml20">
                <span>项目量（件）：<i class="danger">222</i></span>
            </div>
        </template>
    </SelectedAll>
    <!-- 切换标签 -->
    <el-tabs v-model="activeName" @tab-change="antiShake(resetQuery)">
      <el-tab-pane v-for="(item, index) in erectNapeEnum" :key="index" :label="item" :name="index" />
    </el-tabs>
    <div class="table-box">
      <!-- 表单 -->
      <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" :selectable="selectable" width="30px" align="right" />
        <el-table-column label="项目ID" v-if="columns[0].visible" width="160" align="center" prop="projectId" />
        <el-table-column label="项目名称" v-if="columns[1].visible" width="200" align="center" prop="projectName" />
        <el-table-column label="产品类型" v-if="columns[2].visible" width="120" align="center" prop="type" />
        <el-table-column label="立项状态" v-if="columns[3].visible" width="120" align="center" prop="status" />
        <el-table-column label="资产转让方" v-if="columns[4].visible" width="120" align="center" prop="transferor" />
        <el-table-column label="债权总金额" v-if="columns[5].visible" width="120" align="center" prop="debtTotalAmount" />
        <el-table-column label="债权本金" v-if="columns[6].visible" width="120" align="center" prop="principalAmount" />
        <el-table-column label="户数" v-if="columns[7].visible" width="120" align="center" prop="debtCount" />
        <el-table-column label="基准日" v-if="columns[8].visible" width="120" align="center" prop="baselineDate" />
        <el-table-column label="预计竞价日期" v-if="columns[9].visible" width="120" align="center"
          prop="expectedAuctionDate" />
        <el-table-column label="报价上限" v-if="columns[10].visible" width="120" align="center" prop="priceLimit" />
        <el-table-column label="资产评估表" v-if="columns[11].visible" width="120" align="center">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="立项报告" v-if="columns[12].visible" width="120" align="center">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleOpenDailog('perviewFileRef', row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="其他附件" v-if="columns[13].visible" width="120" align="center" prop="surface" />
        <el-table-column label="立项人" v-if="columns[14].visible" width="120" align="center" prop="createBy" />
        <el-table-column label="立项时间" v-if="columns[15].visible" width="160" align="center" prop="createTime" />
        <el-table-column fixed="right" width="220" label="操作">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleSubmit(row)">提交</el-button>
              <el-button type="text" @click="handleDetails(row)">详情</el-button>
              <el-button type="text" @click="handleDel(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <applay ref="applayRef" />
    <perviewFile ref="perviewFileRef" />
  </div>
</template>

<script setup name="StartNapeList">
import perviewFile from '../startNapeList copy/dialog/perviewFile';
import { erectNapeEnum } from "@/utils/enum";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 搜索参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  allQuery: false,
  projectId: [], // 项目ID
  projectName: [], // 项目名称
  productType: [], // 产品类型
  transferor: [], // 资产转让方
  biddingMethod: [], // 投标方式
  debtTotalAmountMin: '', // 债权总金额最小值
  debtTotalAmountMax: '', // 债权总金额最大值
  principalAmountMin: '', // 债权本金最小值
  principalAmountMax: '', // 债权本金最大值
  projectManager: [], // 立项人
  projectTime: '', // 立项时间
  baselineDate: '', // 基准日
  expectedBiddingDate: '' // 预计竞价日期
});

// 选项数据
const searchOptions = reactive({
  projectId: [
    { value: 'zcb202411220001', label: 'zcb202411220001' },
    { value: 'zcb202411220002', label: 'zcb202411220002' },
    { value: 'zcb202411220003', label: 'zcb202411220003' },
    { value: 'zcb202411220004', label: 'zcb202411220004' },
    { value: 'zcb202411220005', label: 'zcb202411220005' }
  ],
  projectName: [
    { value: '2024不良资产收购项目1', label: '2024不良资产收购项目1' },
    { value: '2024不良资产收购项目2', label: '2024不良资产收购项目2' },
    { value: '2024不良资产收购项目3', label: '2024不良资产收购项目3' },
    { value: '2024信贷资产处置项目', label: '2024信贷资产处置项目' },
    { value: '2024抵押资产收购项目', label: '2024抵押资产收购项目' }
  ],
  productType: [
    { value: '信用贷', label: '信用贷' },
    { value: '抵押贷', label: '抵押贷' },
    { value: '担保贷', label: '担保贷' },
    { value: '信用卡', label: '信用卡' },
    { value: '消费贷', label: '消费贷' }
  ],
  transferor: [
    { value: '东莞银行', label: '东莞银行' },
    { value: '招商银行', label: '招商银行' },
    { value: '工商银行', label: '工商银行' },
    { value: '建设银行', label: '建设银行' },
    { value: '农业银行', label: '农业银行' }
  ],
  biddingMethod: [
    { value: '公开招标', label: '公开招标' },
    { value: '邀请招标', label: '邀请招标' },
    { value: '竞争性谈判', label: '竞争性谈判' },
    { value: '单一来源', label: '单一来源' },
    { value: '询价采购', label: '询价采购' }
  ],
  projectManager: [
    { value: '张三', label: '张三' },
    { value: '李四', label: '李四' },
    { value: '王五', label: '王五' },
    { value: '赵六', label: '赵六' },
    { value: '陈七', label: '陈七' }
  ]
});

const total = ref(1);

// 表单数据
const dataList = ref([
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目1',
    type: '信用贷',
    status: '新增立项',
    transferor: '东莞银行',
    debtTotalAmount: '10,000.00',
    principalAmount: '9,000.00',
    debtCount: '1',
    baselineDate: '2025-01-09',
    expectedAuctionDate: '2025-02-09',
    priceLimit: '公开招标',
    surface: '5,000.00',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220002',
    projectName: '2024不良资产收购项目2',
    type: '房贷',
    status: '新增立项',
    transferor: '东莞银行',
    debtTotalAmount: '10,000.00',
    principalAmount: '9,000.00',
    debtCount: '1',
    baselineDate: '2025-01-09',
    expectedAuctionDate: '2025-02-09',
    priceLimit: '公开招标',
    surface: '5,000.00',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220003',
    projectName: '2024不良资产收购项目3',
    type: '房贷',
    status: '已提交',
    transferor: '东莞银行',
    debtTotalAmount: '10,000.00',
    principalAmount: '9,000.00',
    debtCount: '1',
    baselineDate: '2025-01-09',
    expectedAuctionDate: '2025-02-09',
    priceLimit: '公开招标',
    surface: '5,000.00',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220004',
    projectName: '2024不良资产收购项目4',
    type: '房贷',
    status: '已提交',
    transferor: '东莞银行',
    debtTotalAmount: '10,000.00',
    principalAmount: '9,000.00',
    debtCount: '1',
    baselineDate: '2025-01-09',
    expectedAuctionDate: '2025-02-09',
    priceLimit: '公开招标',
    surface: '5,000.00',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
]);
const loading = ref(false);
const showSearch = ref(false);
const selectedArr = ref([])
// 表单列数据
const columns = ref([
  { "key": 0, "label": "项目ID", "visible": true },
  { "key": 1, "label": "项目名称", "visible": true },
  { "key": 2, "label": "产品类型", "visible": true },
  { "key": 3, "label": "立项状态", "visible": true },
  { "key": 4, "label": "资产转让方", "visible": true },
  { "key": 5, "label": "债权总金额", "visible": true },
  { "key": 6, "label": "债权本金", "visible": true },
  { "key": 7, "label": "户数", "visible": true },
  { "key": 8, "label": "基准日", "visible": true },
  { "key": 9, "label": "预计竞价日期", "visible": true },
  { "key": 10, "label": "报价上限", "visible": true },
  { "key": 11, "label": "资产评估表", "visible": true },
  { "key": 12, "label": "立项报告", "visible": true },
  { "key": 13, "label": "其他附件", "visible": true },
  { "key": 14, "label": "立项人", "visible": true },
  { "key": 15, "label": "立项时间", "visible": true }
]);


// 获取列表数据
function getList() {
  console.log('查询参数:', queryParams);
  // TODO: 调用API获取数据
  // 这里应该调用后端接口，传入 queryParams 作为查询条件
}

// 重置查询条件
function resetQuery() {
  // 重置所有查询参数
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    allQuery: false,
    projectId: [],
    projectName: [],
    productType: [],
    transferor: [],
    biddingMethod: [],
    debtTotalAmountMin: '',
    debtTotalAmountMax: '',
    principalAmountMin: '',
    principalAmountMax: '',
    projectManager: [],
    projectTime: '',
    baselineDate: '',
    expectedBiddingDate: ''
  });

  // 重置表单验证状态
  if (proxy.$refs.queryRef) {
    proxy.$refs.queryRef.resetFields();
  }

  // 重新获取数据
  getList();
}

// 搜索查询
function handleQuery() {
  // 重置页码到第一页
  queryParams.pageNum = 1;

  // 打印查询条件（调试用）
  console.log('搜索条件:', {
    多选字段: {
      项目ID: queryParams.projectId,
      项目名称: queryParams.projectName,
      产品类型: queryParams.productType,
      资产转让方: queryParams.transferor,
      投标方式: queryParams.biddingMethod,
      立项人: queryParams.projectManager
    },
    金额范围: {
      债权总金额: `${queryParams.debtTotalAmountMin} - ${queryParams.debtTotalAmountMax}`,
      债权本金: `${queryParams.principalAmountMin} - ${queryParams.principalAmountMax}`
    },
    时间范围: {
      立项时间: queryParams.projectTime,
      基准日: queryParams.baselineDate,
      预计竞价日期: queryParams.expectedBiddingDate
    }
  });

  // 执行查询
  getList();

  ElMessage.success('查询完成');
}

function handleOpenDailog(refName) {
  // proxy.$refs[refName].openDialog();
}
function handleDetails(row) {
  const query = { path: route.path, pageType: 'startNapeList', progressStatus: 0 }
  router.push({ path: `/dueDiligence/projectInfo`, query })
}
function handleAdd(row) {
  const query = { path: route.path, pageType: 'startNapeList', progressStatus: 0, isDetails: 0 }
  router.push({ path: `/dueDiligence/projectInfo`, query })
}
function handleCheck(row) {
  router.push({
    path: `/assessment/view-evaluation/operate/1`,
    query: { path: route.path },
  });
}

function handleSubmit(row) {
    const tipMsg = '该操作将会提交所选项目立项，确认进行操作吗?'
    proxy.$modal.confirm(tipMsg).then(function () {
      loading.value = true;
      // api
    }.then(() => {
        proxy.$modal.msgSuccess("操作成功！");
        getList();
      }).catch(() => {
        loading.value = false;
      })
    )
}

function handleDel(row) {
    const tipMsg = '确认是否删除？'
    proxy.$modal.confirm(tipMsg).then(function () {
      loading.value = true;
      // api
    }.then(() => {
        proxy.$modal.msgSuccess("操作成功！");
        getList();
      }).catch(() => {
        loading.value = false;
      })
    )
}

</script>

<style lang="scss" scoped>
</style>