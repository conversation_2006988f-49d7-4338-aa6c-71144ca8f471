{"version": 3, "sources": ["../tinymce/plugins/codesample/plugin.js", "../tinymce/plugins/codesample/index.js", "dep:tinymce_plugins_codesample"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var get$1 = function (xs, i) {\n      return i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    };\n    var head = function (xs) {\n      return get$1(xs, 0);\n    };\n\n    var someIf = function (b, a) {\n      return b ? Optional.some(a) : Optional.none();\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var isCodeSample = function (elm) {\n      return elm && elm.nodeName === 'PRE' && elm.className.indexOf('language-') !== -1;\n    };\n    var trimArg = function (predicateFn) {\n      return function (arg1, arg2) {\n        return predicateFn(arg2);\n      };\n    };\n\n    var Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var exports$1 = {}, module = { exports: exports$1 }, global = {};\n    (function (define, exports, module, require) {\n      var oldprism = window.Prism;\n      window.Prism = { manual: true };\n      (function (global, factory) {\n        typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() : typeof define === 'function' && define.amd ? define(factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.EphoxContactWrapper = factory());\n      }(this, function () {\n        var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n        var prismCore = { exports: {} };\n        (function (module) {\n          var _self = typeof window !== 'undefined' ? window : typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope ? self : {};\n          var Prism = function (_self) {\n            var lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n            var uniqueId = 0;\n            var plainTextGrammar = {};\n            var _ = {\n              manual: _self.Prism && _self.Prism.manual,\n              disableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n              util: {\n                encode: function encode(tokens) {\n                  if (tokens instanceof Token) {\n                    return new Token(tokens.type, encode(tokens.content), tokens.alias);\n                  } else if (Array.isArray(tokens)) {\n                    return tokens.map(encode);\n                  } else {\n                    return tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n                  }\n                },\n                type: function (o) {\n                  return Object.prototype.toString.call(o).slice(8, -1);\n                },\n                objId: function (obj) {\n                  if (!obj['__id']) {\n                    Object.defineProperty(obj, '__id', { value: ++uniqueId });\n                  }\n                  return obj['__id'];\n                },\n                clone: function deepClone(o, visited) {\n                  visited = visited || {};\n                  var clone;\n                  var id;\n                  switch (_.util.type(o)) {\n                  case 'Object':\n                    id = _.util.objId(o);\n                    if (visited[id]) {\n                      return visited[id];\n                    }\n                    clone = {};\n                    visited[id] = clone;\n                    for (var key in o) {\n                      if (o.hasOwnProperty(key)) {\n                        clone[key] = deepClone(o[key], visited);\n                      }\n                    }\n                    return clone;\n                  case 'Array':\n                    id = _.util.objId(o);\n                    if (visited[id]) {\n                      return visited[id];\n                    }\n                    clone = [];\n                    visited[id] = clone;\n                    o.forEach(function (v, i) {\n                      clone[i] = deepClone(v, visited);\n                    });\n                    return clone;\n                  default:\n                    return o;\n                  }\n                },\n                getLanguage: function (element) {\n                  while (element) {\n                    var m = lang.exec(element.className);\n                    if (m) {\n                      return m[1].toLowerCase();\n                    }\n                    element = element.parentElement;\n                  }\n                  return 'none';\n                },\n                setLanguage: function (element, language) {\n                  element.className = element.className.replace(RegExp(lang.source, 'gi'), '');\n                  element.classList.add('language-' + language);\n                },\n                currentScript: function () {\n                  if (typeof document === 'undefined') {\n                    return null;\n                  }\n                  if ('currentScript' in document && 1 < 2) {\n                    return document.currentScript;\n                  }\n                  try {\n                    throw new Error();\n                  } catch (err) {\n                    var src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n                    if (src) {\n                      var scripts = document.getElementsByTagName('script');\n                      for (var i in scripts) {\n                        if (scripts[i].src == src) {\n                          return scripts[i];\n                        }\n                      }\n                    }\n                    return null;\n                  }\n                },\n                isActive: function (element, className, defaultActivation) {\n                  var no = 'no-' + className;\n                  while (element) {\n                    var classList = element.classList;\n                    if (classList.contains(className)) {\n                      return true;\n                    }\n                    if (classList.contains(no)) {\n                      return false;\n                    }\n                    element = element.parentElement;\n                  }\n                  return !!defaultActivation;\n                }\n              },\n              languages: {\n                plain: plainTextGrammar,\n                plaintext: plainTextGrammar,\n                text: plainTextGrammar,\n                txt: plainTextGrammar,\n                extend: function (id, redef) {\n                  var lang = _.util.clone(_.languages[id]);\n                  for (var key in redef) {\n                    lang[key] = redef[key];\n                  }\n                  return lang;\n                },\n                insertBefore: function (inside, before, insert, root) {\n                  root = root || _.languages;\n                  var grammar = root[inside];\n                  var ret = {};\n                  for (var token in grammar) {\n                    if (grammar.hasOwnProperty(token)) {\n                      if (token == before) {\n                        for (var newToken in insert) {\n                          if (insert.hasOwnProperty(newToken)) {\n                            ret[newToken] = insert[newToken];\n                          }\n                        }\n                      }\n                      if (!insert.hasOwnProperty(token)) {\n                        ret[token] = grammar[token];\n                      }\n                    }\n                  }\n                  var old = root[inside];\n                  root[inside] = ret;\n                  _.languages.DFS(_.languages, function (key, value) {\n                    if (value === old && key != inside) {\n                      this[key] = ret;\n                    }\n                  });\n                  return ret;\n                },\n                DFS: function DFS(o, callback, type, visited) {\n                  visited = visited || {};\n                  var objId = _.util.objId;\n                  for (var i in o) {\n                    if (o.hasOwnProperty(i)) {\n                      callback.call(o, i, o[i], type || i);\n                      var property = o[i];\n                      var propertyType = _.util.type(property);\n                      if (propertyType === 'Object' && !visited[objId(property)]) {\n                        visited[objId(property)] = true;\n                        DFS(property, callback, null, visited);\n                      } else if (propertyType === 'Array' && !visited[objId(property)]) {\n                        visited[objId(property)] = true;\n                        DFS(property, callback, i, visited);\n                      }\n                    }\n                  }\n                }\n              },\n              plugins: {},\n              highlightAll: function (async, callback) {\n                _.highlightAllUnder(document, async, callback);\n              },\n              highlightAllUnder: function (container, async, callback) {\n                var env = {\n                  callback: callback,\n                  container: container,\n                  selector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n                };\n                _.hooks.run('before-highlightall', env);\n                env.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n                _.hooks.run('before-all-elements-highlight', env);\n                for (var i = 0, element; element = env.elements[i++];) {\n                  _.highlightElement(element, async === true, env.callback);\n                }\n              },\n              highlightElement: function (element, async, callback) {\n                var language = _.util.getLanguage(element);\n                var grammar = _.languages[language];\n                _.util.setLanguage(element, language);\n                var parent = element.parentElement;\n                if (parent && parent.nodeName.toLowerCase() === 'pre') {\n                  _.util.setLanguage(parent, language);\n                }\n                var code = element.textContent;\n                var env = {\n                  element: element,\n                  language: language,\n                  grammar: grammar,\n                  code: code\n                };\n                function insertHighlightedCode(highlightedCode) {\n                  env.highlightedCode = highlightedCode;\n                  _.hooks.run('before-insert', env);\n                  env.element.innerHTML = env.highlightedCode;\n                  _.hooks.run('after-highlight', env);\n                  _.hooks.run('complete', env);\n                  callback && callback.call(env.element);\n                }\n                _.hooks.run('before-sanity-check', env);\n                parent = env.element.parentElement;\n                if (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n                  parent.setAttribute('tabindex', '0');\n                }\n                if (!env.code) {\n                  _.hooks.run('complete', env);\n                  callback && callback.call(env.element);\n                  return;\n                }\n                _.hooks.run('before-highlight', env);\n                if (!env.grammar) {\n                  insertHighlightedCode(_.util.encode(env.code));\n                  return;\n                }\n                if (async && _self.Worker) {\n                  var worker = new Worker(_.filename);\n                  worker.onmessage = function (evt) {\n                    insertHighlightedCode(evt.data);\n                  };\n                  worker.postMessage(JSON.stringify({\n                    language: env.language,\n                    code: env.code,\n                    immediateClose: true\n                  }));\n                } else {\n                  insertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n                }\n              },\n              highlight: function (text, grammar, language) {\n                var env = {\n                  code: text,\n                  grammar: grammar,\n                  language: language\n                };\n                _.hooks.run('before-tokenize', env);\n                if (!env.grammar) {\n                  throw new Error('The language \"' + env.language + '\" has no grammar.');\n                }\n                env.tokens = _.tokenize(env.code, env.grammar);\n                _.hooks.run('after-tokenize', env);\n                return Token.stringify(_.util.encode(env.tokens), env.language);\n              },\n              tokenize: function (text, grammar) {\n                var rest = grammar.rest;\n                if (rest) {\n                  for (var token in rest) {\n                    grammar[token] = rest[token];\n                  }\n                  delete grammar.rest;\n                }\n                var tokenList = new LinkedList();\n                addAfter(tokenList, tokenList.head, text);\n                matchGrammar(text, tokenList, grammar, tokenList.head, 0);\n                return toArray(tokenList);\n              },\n              hooks: {\n                all: {},\n                add: function (name, callback) {\n                  var hooks = _.hooks.all;\n                  hooks[name] = hooks[name] || [];\n                  hooks[name].push(callback);\n                },\n                run: function (name, env) {\n                  var callbacks = _.hooks.all[name];\n                  if (!callbacks || !callbacks.length) {\n                    return;\n                  }\n                  for (var i = 0, callback; callback = callbacks[i++];) {\n                    callback(env);\n                  }\n                }\n              },\n              Token: Token\n            };\n            _self.Prism = _;\n            function Token(type, content, alias, matchedStr) {\n              this.type = type;\n              this.content = content;\n              this.alias = alias;\n              this.length = (matchedStr || '').length | 0;\n            }\n            Token.stringify = function stringify(o, language) {\n              if (typeof o == 'string') {\n                return o;\n              }\n              if (Array.isArray(o)) {\n                var s = '';\n                o.forEach(function (e) {\n                  s += stringify(e, language);\n                });\n                return s;\n              }\n              var env = {\n                type: o.type,\n                content: stringify(o.content, language),\n                tag: 'span',\n                classes: [\n                  'token',\n                  o.type\n                ],\n                attributes: {},\n                language: language\n              };\n              var aliases = o.alias;\n              if (aliases) {\n                if (Array.isArray(aliases)) {\n                  Array.prototype.push.apply(env.classes, aliases);\n                } else {\n                  env.classes.push(aliases);\n                }\n              }\n              _.hooks.run('wrap', env);\n              var attributes = '';\n              for (var name in env.attributes) {\n                attributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n              }\n              return '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n            };\n            function matchPattern(pattern, pos, text, lookbehind) {\n              pattern.lastIndex = pos;\n              var match = pattern.exec(text);\n              if (match && lookbehind && match[1]) {\n                var lookbehindLength = match[1].length;\n                match.index += lookbehindLength;\n                match[0] = match[0].slice(lookbehindLength);\n              }\n              return match;\n            }\n            function matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n              for (var token in grammar) {\n                if (!grammar.hasOwnProperty(token) || !grammar[token]) {\n                  continue;\n                }\n                var patterns = grammar[token];\n                patterns = Array.isArray(patterns) ? patterns : [patterns];\n                for (var j = 0; j < patterns.length; ++j) {\n                  if (rematch && rematch.cause == token + ',' + j) {\n                    return;\n                  }\n                  var patternObj = patterns[j];\n                  var inside = patternObj.inside;\n                  var lookbehind = !!patternObj.lookbehind;\n                  var greedy = !!patternObj.greedy;\n                  var alias = patternObj.alias;\n                  if (greedy && !patternObj.pattern.global) {\n                    var flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n                    patternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n                  }\n                  var pattern = patternObj.pattern || patternObj;\n                  for (var currentNode = startNode.next, pos = startPos; currentNode !== tokenList.tail; pos += currentNode.value.length, currentNode = currentNode.next) {\n                    if (rematch && pos >= rematch.reach) {\n                      break;\n                    }\n                    var str = currentNode.value;\n                    if (tokenList.length > text.length) {\n                      return;\n                    }\n                    if (str instanceof Token) {\n                      continue;\n                    }\n                    var removeCount = 1;\n                    var match;\n                    if (greedy) {\n                      match = matchPattern(pattern, pos, text, lookbehind);\n                      if (!match || match.index >= text.length) {\n                        break;\n                      }\n                      var from = match.index;\n                      var to = match.index + match[0].length;\n                      var p = pos;\n                      p += currentNode.value.length;\n                      while (from >= p) {\n                        currentNode = currentNode.next;\n                        p += currentNode.value.length;\n                      }\n                      p -= currentNode.value.length;\n                      pos = p;\n                      if (currentNode.value instanceof Token) {\n                        continue;\n                      }\n                      for (var k = currentNode; k !== tokenList.tail && (p < to || typeof k.value === 'string'); k = k.next) {\n                        removeCount++;\n                        p += k.value.length;\n                      }\n                      removeCount--;\n                      str = text.slice(pos, p);\n                      match.index -= pos;\n                    } else {\n                      match = matchPattern(pattern, 0, str, lookbehind);\n                      if (!match) {\n                        continue;\n                      }\n                    }\n                    var from = match.index;\n                    var matchStr = match[0];\n                    var before = str.slice(0, from);\n                    var after = str.slice(from + matchStr.length);\n                    var reach = pos + str.length;\n                    if (rematch && reach > rematch.reach) {\n                      rematch.reach = reach;\n                    }\n                    var removeFrom = currentNode.prev;\n                    if (before) {\n                      removeFrom = addAfter(tokenList, removeFrom, before);\n                      pos += before.length;\n                    }\n                    removeRange(tokenList, removeFrom, removeCount);\n                    var wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n                    currentNode = addAfter(tokenList, removeFrom, wrapped);\n                    if (after) {\n                      addAfter(tokenList, currentNode, after);\n                    }\n                    if (removeCount > 1) {\n                      var nestedRematch = {\n                        cause: token + ',' + j,\n                        reach: reach\n                      };\n                      matchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n                      if (rematch && nestedRematch.reach > rematch.reach) {\n                        rematch.reach = nestedRematch.reach;\n                      }\n                    }\n                  }\n                }\n              }\n            }\n            function LinkedList() {\n              var head = {\n                value: null,\n                prev: null,\n                next: null\n              };\n              var tail = {\n                value: null,\n                prev: head,\n                next: null\n              };\n              head.next = tail;\n              this.head = head;\n              this.tail = tail;\n              this.length = 0;\n            }\n            function addAfter(list, node, value) {\n              var next = node.next;\n              var newNode = {\n                value: value,\n                prev: node,\n                next: next\n              };\n              node.next = newNode;\n              next.prev = newNode;\n              list.length++;\n              return newNode;\n            }\n            function removeRange(list, node, count) {\n              var next = node.next;\n              for (var i = 0; i < count && next !== list.tail; i++) {\n                next = next.next;\n              }\n              node.next = next;\n              next.prev = node;\n              list.length -= i;\n            }\n            function toArray(list) {\n              var array = [];\n              var node = list.head.next;\n              while (node !== list.tail) {\n                array.push(node.value);\n                node = node.next;\n              }\n              return array;\n            }\n            if (!_self.document) {\n              if (!_self.addEventListener) {\n                return _;\n              }\n              if (!_.disableWorkerMessageHandler) {\n                _self.addEventListener('message', function (evt) {\n                  var message = JSON.parse(evt.data);\n                  var lang = message.language;\n                  var code = message.code;\n                  var immediateClose = message.immediateClose;\n                  _self.postMessage(_.highlight(code, _.languages[lang], lang));\n                  if (immediateClose) {\n                    _self.close();\n                  }\n                }, false);\n              }\n              return _;\n            }\n            var script = _.util.currentScript();\n            if (script) {\n              _.filename = script.src;\n              if (script.hasAttribute('data-manual')) {\n                _.manual = true;\n              }\n            }\n            function highlightAutomaticallyCallback() {\n              if (!_.manual) {\n                _.highlightAll();\n              }\n            }\n            if (!_.manual) {\n              var readyState = document.readyState;\n              if (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n                document.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n              } else {\n                if (window.requestAnimationFrame) {\n                  window.requestAnimationFrame(highlightAutomaticallyCallback);\n                } else {\n                  window.setTimeout(highlightAutomaticallyCallback, 16);\n                }\n              }\n            }\n            return _;\n          }(_self);\n          if (module.exports) {\n            module.exports = Prism;\n          }\n          if (typeof commonjsGlobal !== 'undefined') {\n            commonjsGlobal.Prism = Prism;\n          }\n        }(prismCore));\n        Prism.languages.clike = {\n          'comment': [\n            {\n              pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              pattern: /(^|[^\\\\:])\\/\\/.*/,\n              lookbehind: true,\n              greedy: true\n            }\n          ],\n          'string': {\n            pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n            greedy: true\n          },\n          'class-name': {\n            pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n            lookbehind: true,\n            inside: { 'punctuation': /[.\\\\]/ }\n          },\n          'keyword': /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n          'boolean': /\\b(?:false|true)\\b/,\n          'function': /\\b\\w+(?=\\()/,\n          'number': /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n          'operator': /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n          'punctuation': /[{}[\\];(),.:]/\n        };\n        (function (Prism) {\n          function getPlaceholder(language, index) {\n            return '___' + language.toUpperCase() + index + '___';\n          }\n          Object.defineProperties(Prism.languages['markup-templating'] = {}, {\n            buildPlaceholders: {\n              value: function (env, language, placeholderPattern, replaceFilter) {\n                if (env.language !== language) {\n                  return;\n                }\n                var tokenStack = env.tokenStack = [];\n                env.code = env.code.replace(placeholderPattern, function (match) {\n                  if (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n                    return match;\n                  }\n                  var i = tokenStack.length;\n                  var placeholder;\n                  while (env.code.indexOf(placeholder = getPlaceholder(language, i)) !== -1) {\n                    ++i;\n                  }\n                  tokenStack[i] = match;\n                  return placeholder;\n                });\n                env.grammar = Prism.languages.markup;\n              }\n            },\n            tokenizePlaceholders: {\n              value: function (env, language) {\n                if (env.language !== language || !env.tokenStack) {\n                  return;\n                }\n                env.grammar = Prism.languages[language];\n                var j = 0;\n                var keys = Object.keys(env.tokenStack);\n                function walkTokens(tokens) {\n                  for (var i = 0; i < tokens.length; i++) {\n                    if (j >= keys.length) {\n                      break;\n                    }\n                    var token = tokens[i];\n                    if (typeof token === 'string' || token.content && typeof token.content === 'string') {\n                      var k = keys[j];\n                      var t = env.tokenStack[k];\n                      var s = typeof token === 'string' ? token : token.content;\n                      var placeholder = getPlaceholder(language, k);\n                      var index = s.indexOf(placeholder);\n                      if (index > -1) {\n                        ++j;\n                        var before = s.substring(0, index);\n                        var middle = new Prism.Token(language, Prism.tokenize(t, env.grammar), 'language-' + language, t);\n                        var after = s.substring(index + placeholder.length);\n                        var replacement = [];\n                        if (before) {\n                          replacement.push.apply(replacement, walkTokens([before]));\n                        }\n                        replacement.push(middle);\n                        if (after) {\n                          replacement.push.apply(replacement, walkTokens([after]));\n                        }\n                        if (typeof token === 'string') {\n                          tokens.splice.apply(tokens, [\n                            i,\n                            1\n                          ].concat(replacement));\n                        } else {\n                          token.content = replacement;\n                        }\n                      }\n                    } else if (token.content) {\n                      walkTokens(token.content);\n                    }\n                  }\n                  return tokens;\n                }\n                walkTokens(env.tokens);\n              }\n            }\n          });\n        }(Prism));\n        Prism.languages.c = Prism.languages.extend('clike', {\n          'comment': {\n            pattern: /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n            greedy: true\n          },\n          'string': {\n            pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n            greedy: true\n          },\n          'class-name': {\n            pattern: /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n            lookbehind: true\n          },\n          'keyword': /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n          'function': /\\b[a-z_]\\w*(?=\\s*\\()/i,\n          'number': /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n          'operator': />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n        });\n        Prism.languages.insertBefore('c', 'string', {\n          'char': {\n            pattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n            greedy: true\n          }\n        });\n        Prism.languages.insertBefore('c', 'string', {\n          'macro': {\n            pattern: /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n            lookbehind: true,\n            greedy: true,\n            alias: 'property',\n            inside: {\n              'string': [\n                {\n                  pattern: /^(#\\s*include\\s*)<[^>]+>/,\n                  lookbehind: true\n                },\n                Prism.languages.c['string']\n              ],\n              'char': Prism.languages.c['char'],\n              'comment': Prism.languages.c['comment'],\n              'macro-name': [\n                {\n                  pattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n                  lookbehind: true\n                },\n                {\n                  pattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n                  lookbehind: true,\n                  alias: 'function'\n                }\n              ],\n              'directive': {\n                pattern: /^(#\\s*)[a-z]+/,\n                lookbehind: true,\n                alias: 'keyword'\n              },\n              'directive-hash': /^#/,\n              'punctuation': /##|\\\\(?=[\\r\\n])/,\n              'expression': {\n                pattern: /\\S[\\s\\S]*/,\n                inside: Prism.languages.c\n              }\n            }\n          }\n        });\n        Prism.languages.insertBefore('c', 'function', { 'constant': /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/ });\n        delete Prism.languages.c['boolean'];\n        (function (Prism) {\n          var keyword = /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/;\n          var modName = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(/<keyword>/g, function () {\n            return keyword.source;\n          });\n          Prism.languages.cpp = Prism.languages.extend('c', {\n            'class-name': [\n              {\n                pattern: RegExp(/(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source.replace(/<keyword>/g, function () {\n                  return keyword.source;\n                })),\n                lookbehind: true\n              },\n              /\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/,\n              /\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i,\n              /\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/\n            ],\n            'keyword': keyword,\n            'number': {\n              pattern: /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,\n              greedy: true\n            },\n            'operator': />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,\n            'boolean': /\\b(?:false|true)\\b/\n          });\n          Prism.languages.insertBefore('cpp', 'string', {\n            'module': {\n              pattern: RegExp(/(\\b(?:import|module)\\s+)/.source + '(?:' + /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source + '|' + /<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(/<mod-name>/g, function () {\n                return modName;\n              }) + ')'),\n              lookbehind: true,\n              greedy: true,\n              inside: {\n                'string': /^[<\"][\\s\\S]+/,\n                'operator': /:/,\n                'punctuation': /\\./\n              }\n            },\n            'raw-string': {\n              pattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,\n              alias: 'string',\n              greedy: true\n            }\n          });\n          Prism.languages.insertBefore('cpp', 'keyword', {\n            'generic-function': {\n              pattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,\n              inside: {\n                'function': /^\\w+/,\n                'generic': {\n                  pattern: /<[\\s\\S]+/,\n                  alias: 'class-name',\n                  inside: Prism.languages.cpp\n                }\n              }\n            }\n          });\n          Prism.languages.insertBefore('cpp', 'operator', {\n            'double-colon': {\n              pattern: /::/,\n              alias: 'punctuation'\n            }\n          });\n          Prism.languages.insertBefore('cpp', 'class-name', {\n            'base-clause': {\n              pattern: /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,\n              lookbehind: true,\n              greedy: true,\n              inside: Prism.languages.extend('cpp', {})\n            }\n          });\n          Prism.languages.insertBefore('inside', 'double-colon', { 'class-name': /\\b[a-z_]\\w*\\b(?!\\s*::)/i }, Prism.languages.cpp['base-clause']);\n        }(Prism));\n        (function (Prism) {\n          function replace(pattern, replacements) {\n            return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n              return '(?:' + replacements[+index] + ')';\n            });\n          }\n          function re(pattern, replacements, flags) {\n            return RegExp(replace(pattern, replacements), flags || '');\n          }\n          function nested(pattern, depthLog2) {\n            for (var i = 0; i < depthLog2; i++) {\n              pattern = pattern.replace(/<<self>>/g, function () {\n                return '(?:' + pattern + ')';\n              });\n            }\n            return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]');\n          }\n          var keywordKinds = {\n            type: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n            typeDeclaration: 'class enum interface record struct',\n            contextual: 'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n            other: 'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n          };\n          function keywordsToPattern(words) {\n            return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b';\n          }\n          var typeDeclarationKeywords = keywordsToPattern(keywordKinds.typeDeclaration);\n          var keywords = RegExp(keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other));\n          var nonTypeKeywords = keywordsToPattern(keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other);\n          var nonContextualKeywords = keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.other);\n          var generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2);\n          var nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2);\n          var name = /@?\\b[A-Za-z_]\\w*\\b/.source;\n          var genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [\n            name,\n            generic\n          ]);\n          var identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [\n            nonTypeKeywords,\n            genericName\n          ]);\n          var array = /\\[\\s*(?:,\\s*)*\\]/.source;\n          var typeExpressionWithoutTuple = replace(/<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source, [\n            identifier,\n            array\n          ]);\n          var tupleElement = replace(/[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source, [\n            generic,\n            nestedRound,\n            array\n          ]);\n          var tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement]);\n          var typeExpression = replace(/(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source, [\n            tuple,\n            identifier,\n            array\n          ]);\n          var typeInside = {\n            'keyword': keywords,\n            'punctuation': /[<>()?,.:[\\]]/\n          };\n          var character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source;\n          var regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source;\n          var verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source;\n          Prism.languages.csharp = Prism.languages.extend('clike', {\n            'string': [\n              {\n                pattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n                lookbehind: true,\n                greedy: true\n              },\n              {\n                pattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n                lookbehind: true,\n                greedy: true\n              }\n            ],\n            'class-name': [\n              {\n                pattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [identifier]),\n                lookbehind: true,\n                inside: typeInside\n              },\n              {\n                pattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [\n                  name,\n                  typeExpression\n                ]),\n                lookbehind: true,\n                inside: typeInside\n              },\n              {\n                pattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n                lookbehind: true\n              },\n              {\n                pattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [\n                  typeDeclarationKeywords,\n                  genericName\n                ]),\n                lookbehind: true,\n                inside: typeInside\n              },\n              {\n                pattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n                lookbehind: true,\n                inside: typeInside\n              },\n              {\n                pattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n                lookbehind: true\n              },\n              {\n                pattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [typeExpressionWithoutTuple]),\n                lookbehind: true,\n                inside: typeInside\n              },\n              {\n                pattern: re(/\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/.source, [\n                  typeExpression,\n                  nonContextualKeywords,\n                  name\n                ]),\n                inside: typeInside\n              }\n            ],\n            'keyword': keywords,\n            'number': /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n            'operator': />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n            'punctuation': /\\?\\.?|::|[{}[\\];(),.:]/\n          });\n          Prism.languages.insertBefore('csharp', 'number', {\n            'range': {\n              pattern: /\\.\\./,\n              alias: 'operator'\n            }\n          });\n          Prism.languages.insertBefore('csharp', 'punctuation', {\n            'named-parameter': {\n              pattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n              lookbehind: true,\n              alias: 'punctuation'\n            }\n          });\n          Prism.languages.insertBefore('csharp', 'class-name', {\n            'namespace': {\n              pattern: re(/(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source, [name]),\n              lookbehind: true,\n              inside: { 'punctuation': /\\./ }\n            },\n            'type-expression': {\n              pattern: re(/(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/.source, [nestedRound]),\n              lookbehind: true,\n              alias: 'class-name',\n              inside: typeInside\n            },\n            'return-type': {\n              pattern: re(/<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source, [\n                typeExpression,\n                identifier\n              ]),\n              inside: typeInside,\n              alias: 'class-name'\n            },\n            'constructor-invocation': {\n              pattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n              lookbehind: true,\n              inside: typeInside,\n              alias: 'class-name'\n            },\n            'generic-method': {\n              pattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [\n                name,\n                generic\n              ]),\n              inside: {\n                'function': re(/^<<0>>/.source, [name]),\n                'generic': {\n                  pattern: RegExp(generic),\n                  alias: 'class-name',\n                  inside: typeInside\n                }\n              }\n            },\n            'type-list': {\n              pattern: re(/\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/.source, [\n                typeDeclarationKeywords,\n                genericName,\n                name,\n                typeExpression,\n                keywords.source,\n                nestedRound,\n                /\\bnew\\s*\\(\\s*\\)/.source\n              ]),\n              lookbehind: true,\n              inside: {\n                'record-arguments': {\n                  pattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [\n                    genericName,\n                    nestedRound\n                  ]),\n                  lookbehind: true,\n                  greedy: true,\n                  inside: Prism.languages.csharp\n                },\n                'keyword': keywords,\n                'class-name': {\n                  pattern: RegExp(typeExpression),\n                  greedy: true,\n                  inside: typeInside\n                },\n                'punctuation': /[,()]/\n              }\n            },\n            'preprocessor': {\n              pattern: /(^[\\t ]*)#.*/m,\n              lookbehind: true,\n              alias: 'property',\n              inside: {\n                'directive': {\n                  pattern: /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n                  lookbehind: true,\n                  alias: 'keyword'\n                }\n              }\n            }\n          });\n          var regularStringOrCharacter = regularString + '|' + character;\n          var regularStringCharacterOrComment = replace(/\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source, [regularStringOrCharacter]);\n          var roundExpression = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n          var attrTarget = /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/.source;\n          var attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [\n            identifier,\n            roundExpression\n          ]);\n          Prism.languages.insertBefore('csharp', 'class-name', {\n            'attribute': {\n              pattern: re(/((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/.source, [\n                attrTarget,\n                attr\n              ]),\n              lookbehind: true,\n              greedy: true,\n              inside: {\n                'target': {\n                  pattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n                  alias: 'keyword'\n                },\n                'attribute-arguments': {\n                  pattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n                  inside: Prism.languages.csharp\n                },\n                'class-name': {\n                  pattern: RegExp(identifier),\n                  inside: { 'punctuation': /\\./ }\n                },\n                'punctuation': /[:,]/\n              }\n            }\n          });\n          var formatString = /:[^}\\r\\n]+/.source;\n          var mInterpolationRound = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n          var mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n            mInterpolationRound,\n            formatString\n          ]);\n          var sInterpolationRound = nested(replace(/[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/.source, [regularStringOrCharacter]), 2);\n          var sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n            sInterpolationRound,\n            formatString\n          ]);\n          function createInterpolationInside(interpolation, interpolationRound) {\n            return {\n              'interpolation': {\n                pattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n                lookbehind: true,\n                inside: {\n                  'format-string': {\n                    pattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [\n                      interpolationRound,\n                      formatString\n                    ]),\n                    lookbehind: true,\n                    inside: { 'punctuation': /^:/ }\n                  },\n                  'punctuation': /^\\{|\\}$/,\n                  'expression': {\n                    pattern: /[\\s\\S]+/,\n                    alias: 'language-csharp',\n                    inside: Prism.languages.csharp\n                  }\n                }\n              },\n              'string': /[\\s\\S]+/\n            };\n          }\n          Prism.languages.insertBefore('csharp', 'string', {\n            'interpolation-string': [\n              {\n                pattern: re(/(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source, [mInterpolation]),\n                lookbehind: true,\n                greedy: true,\n                inside: createInterpolationInside(mInterpolation, mInterpolationRound)\n              },\n              {\n                pattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [sInterpolation]),\n                lookbehind: true,\n                greedy: true,\n                inside: createInterpolationInside(sInterpolation, sInterpolationRound)\n              }\n            ],\n            'char': {\n              pattern: RegExp(character),\n              greedy: true\n            }\n          });\n          Prism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp;\n        }(Prism));\n        (function (Prism) {\n          var string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n          Prism.languages.css = {\n            'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n            'atrule': {\n              pattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n              inside: {\n                'rule': /^@[\\w-]+/,\n                'selector-function-argument': {\n                  pattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n                  lookbehind: true,\n                  alias: 'selector'\n                },\n                'keyword': {\n                  pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n                  lookbehind: true\n                }\n              }\n            },\n            'url': {\n              pattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n              greedy: true,\n              inside: {\n                'function': /^url/i,\n                'punctuation': /^\\(|\\)$/,\n                'string': {\n                  pattern: RegExp('^' + string.source + '$'),\n                  alias: 'url'\n                }\n              }\n            },\n            'selector': {\n              pattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n              lookbehind: true\n            },\n            'string': {\n              pattern: string,\n              greedy: true\n            },\n            'property': {\n              pattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n              lookbehind: true\n            },\n            'important': /!important\\b/i,\n            'function': {\n              pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n              lookbehind: true\n            },\n            'punctuation': /[(){};:,]/\n          };\n          Prism.languages.css['atrule'].inside.rest = Prism.languages.css;\n          var markup = Prism.languages.markup;\n          if (markup) {\n            markup.tag.addInlined('style', 'css');\n            markup.tag.addAttribute('style', 'css');\n          }\n        }(Prism));\n        (function (Prism) {\n          var keywords = /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\\s*[(){}[\\]<>=%~.:,;?+\\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/;\n          var classNamePrefix = /(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/.source;\n          var className = {\n            pattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n            lookbehind: true,\n            inside: {\n              'namespace': {\n                pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n                inside: { 'punctuation': /\\./ }\n              },\n              'punctuation': /\\./\n            }\n          };\n          Prism.languages.java = Prism.languages.extend('clike', {\n            'string': {\n              pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n              lookbehind: true,\n              greedy: true\n            },\n            'class-name': [\n              className,\n              {\n                pattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()]|\\s*(?:\\[[\\s,]*\\]\\s*)?::\\s*new\\b)/.source),\n                lookbehind: true,\n                inside: className.inside\n              },\n              {\n                pattern: RegExp(/(\\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\\s+)/.source + classNamePrefix + /[A-Z]\\w*\\b/.source),\n                lookbehind: true,\n                inside: className.inside\n              }\n            ],\n            'keyword': keywords,\n            'function': [\n              Prism.languages.clike.function,\n              {\n                pattern: /(::\\s*)[a-z_]\\w*/,\n                lookbehind: true\n              }\n            ],\n            'number': /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n            'operator': {\n              pattern: /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n              lookbehind: true\n            },\n            'constant': /\\b[A-Z][A-Z_\\d]+\\b/\n          });\n          Prism.languages.insertBefore('java', 'string', {\n            'triple-quoted-string': {\n              pattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n              greedy: true,\n              alias: 'string'\n            },\n            'char': {\n              pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n              greedy: true\n            }\n          });\n          Prism.languages.insertBefore('java', 'class-name', {\n            'annotation': {\n              pattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n              lookbehind: true,\n              alias: 'punctuation'\n            },\n            'generics': {\n              pattern: /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n              inside: {\n                'class-name': className,\n                'keyword': keywords,\n                'punctuation': /[<>(),.:]/,\n                'operator': /[?&|]/\n              }\n            },\n            'import': [\n              {\n                pattern: RegExp(/(\\bimport\\s+)/.source + classNamePrefix + /(?:[A-Z]\\w*|\\*)(?=\\s*;)/.source),\n                lookbehind: true,\n                inside: {\n                  'namespace': className.inside.namespace,\n                  'punctuation': /\\./,\n                  'operator': /\\*/,\n                  'class-name': /\\w+/\n                }\n              },\n              {\n                pattern: RegExp(/(\\bimport\\s+static\\s+)/.source + classNamePrefix + /(?:\\w+|\\*)(?=\\s*;)/.source),\n                lookbehind: true,\n                alias: 'static',\n                inside: {\n                  'namespace': className.inside.namespace,\n                  'static': /\\b\\w+$/,\n                  'punctuation': /\\./,\n                  'operator': /\\*/,\n                  'class-name': /\\w+/\n                }\n              }\n            ],\n            'namespace': {\n              pattern: RegExp(/(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/.source.replace(/<keyword>/g, function () {\n                return keywords.source;\n              })),\n              lookbehind: true,\n              inside: { 'punctuation': /\\./ }\n            }\n          });\n        }(Prism));\n        Prism.languages.javascript = Prism.languages.extend('clike', {\n          'class-name': [\n            Prism.languages.clike['class-name'],\n            {\n              pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n              lookbehind: true\n            }\n          ],\n          'keyword': [\n            {\n              pattern: /((?:^|\\})\\s*)catch\\b/,\n              lookbehind: true\n            },\n            {\n              pattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n              lookbehind: true\n            }\n          ],\n          'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n          'number': {\n            pattern: RegExp(/(^|[^\\w$])/.source + '(?:' + (/NaN|Infinity/.source + '|' + /0[bB][01]+(?:_[01]+)*n?/.source + '|' + /0[oO][0-7]+(?:_[0-7]+)*n?/.source + '|' + /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source + '|' + /\\d+(?:_\\d+)*n/.source + '|' + /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source) + ')' + /(?![\\w$])/.source),\n            lookbehind: true\n          },\n          'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n        });\n        Prism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\n        Prism.languages.insertBefore('javascript', 'keyword', {\n          'regex': {\n            pattern: RegExp(/((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source + /\\//.source + '(?:' + /(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source + '|' + /(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source + ')' + /(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source),\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              'regex-source': {\n                pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n                lookbehind: true,\n                alias: 'language-regex',\n                inside: Prism.languages.regex\n              },\n              'regex-delimiter': /^\\/|\\/$/,\n              'regex-flags': /^[a-z]+$/\n            }\n          },\n          'function-variable': {\n            pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n            alias: 'function'\n          },\n          'parameter': [\n            {\n              pattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n              lookbehind: true,\n              inside: Prism.languages.javascript\n            },\n            {\n              pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n              lookbehind: true,\n              inside: Prism.languages.javascript\n            },\n            {\n              pattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n              lookbehind: true,\n              inside: Prism.languages.javascript\n            },\n            {\n              pattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n              lookbehind: true,\n              inside: Prism.languages.javascript\n            }\n          ],\n          'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n        });\n        Prism.languages.insertBefore('javascript', 'string', {\n          'hashbang': {\n            pattern: /^#!.*/,\n            greedy: true,\n            alias: 'comment'\n          },\n          'template-string': {\n            pattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n            greedy: true,\n            inside: {\n              'template-punctuation': {\n                pattern: /^`|`$/,\n                alias: 'string'\n              },\n              'interpolation': {\n                pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n                lookbehind: true,\n                inside: {\n                  'interpolation-punctuation': {\n                    pattern: /^\\$\\{|\\}$/,\n                    alias: 'punctuation'\n                  },\n                  rest: Prism.languages.javascript\n                }\n              },\n              'string': /[\\s\\S]+/\n            }\n          },\n          'string-property': {\n            pattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n            lookbehind: true,\n            greedy: true,\n            alias: 'property'\n          }\n        });\n        Prism.languages.insertBefore('javascript', 'operator', {\n          'literal-property': {\n            pattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n            lookbehind: true,\n            alias: 'property'\n          }\n        });\n        if (Prism.languages.markup) {\n          Prism.languages.markup.tag.addInlined('script', 'javascript');\n          Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source, 'javascript');\n        }\n        Prism.languages.js = Prism.languages.javascript;\n        Prism.languages.markup = {\n          'comment': {\n            pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n            greedy: true\n          },\n          'prolog': {\n            pattern: /<\\?[\\s\\S]+?\\?>/,\n            greedy: true\n          },\n          'doctype': {\n            pattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n            greedy: true,\n            inside: {\n              'internal-subset': {\n                pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n                lookbehind: true,\n                greedy: true,\n                inside: null\n              },\n              'string': {\n                pattern: /\"[^\"]*\"|'[^']*'/,\n                greedy: true\n              },\n              'punctuation': /^<!|>$|[[\\]]/,\n              'doctype-tag': /^DOCTYPE/i,\n              'name': /[^\\s<>'\"]+/\n            }\n          },\n          'cdata': {\n            pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n            greedy: true\n          },\n          'tag': {\n            pattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n            greedy: true,\n            inside: {\n              'tag': {\n                pattern: /^<\\/?[^\\s>\\/]+/,\n                inside: {\n                  'punctuation': /^<\\/?/,\n                  'namespace': /^[^\\s>\\/:]+:/\n                }\n              },\n              'special-attr': [],\n              'attr-value': {\n                pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n                inside: {\n                  'punctuation': [\n                    {\n                      pattern: /^=/,\n                      alias: 'attr-equals'\n                    },\n                    {\n                      pattern: /^(\\s*)[\"']|[\"']$/,\n                      lookbehind: true\n                    }\n                  ]\n                }\n              },\n              'punctuation': /\\/?>/,\n              'attr-name': {\n                pattern: /[^\\s>\\/]+/,\n                inside: { 'namespace': /^[^\\s>\\/:]+:/ }\n              }\n            }\n          },\n          'entity': [\n            {\n              pattern: /&[\\da-z]{1,8};/i,\n              alias: 'named-entity'\n            },\n            /&#x?[\\da-f]{1,8};/i\n          ]\n        };\n        Prism.languages.markup['tag'].inside['attr-value'].inside['entity'] = Prism.languages.markup['entity'];\n        Prism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n        Prism.hooks.add('wrap', function (env) {\n          if (env.type === 'entity') {\n            env.attributes['title'] = env.content.replace(/&amp;/, '&');\n          }\n        });\n        Object.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n          value: function addInlined(tagName, lang) {\n            var includedCdataInside = {};\n            includedCdataInside['language-' + lang] = {\n              pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n              lookbehind: true,\n              inside: Prism.languages[lang]\n            };\n            includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n            var inside = {\n              'included-cdata': {\n                pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n                inside: includedCdataInside\n              }\n            };\n            inside['language-' + lang] = {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages[lang]\n            };\n            var def = {};\n            def[tagName] = {\n              pattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () {\n                return tagName;\n              }), 'i'),\n              lookbehind: true,\n              greedy: true,\n              inside: inside\n            };\n            Prism.languages.insertBefore('markup', 'cdata', def);\n          }\n        });\n        Object.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n          value: function (attrName, lang) {\n            Prism.languages.markup.tag.inside['special-attr'].push({\n              pattern: RegExp(/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source, 'i'),\n              lookbehind: true,\n              inside: {\n                'attr-name': /^[^\\s=]+/,\n                'attr-value': {\n                  pattern: /=[\\s\\S]+/,\n                  inside: {\n                    'value': {\n                      pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n                      lookbehind: true,\n                      alias: [\n                        lang,\n                        'language-' + lang\n                      ],\n                      inside: Prism.languages[lang]\n                    },\n                    'punctuation': [\n                      {\n                        pattern: /^=/,\n                        alias: 'attr-equals'\n                      },\n                      /\"|'/\n                    ]\n                  }\n                }\n              }\n            });\n          }\n        });\n        Prism.languages.html = Prism.languages.markup;\n        Prism.languages.mathml = Prism.languages.markup;\n        Prism.languages.svg = Prism.languages.markup;\n        Prism.languages.xml = Prism.languages.extend('markup', {});\n        Prism.languages.ssml = Prism.languages.xml;\n        Prism.languages.atom = Prism.languages.xml;\n        Prism.languages.rss = Prism.languages.xml;\n        (function (Prism) {\n          var comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/;\n          var constant = [\n            {\n              pattern: /\\b(?:false|true)\\b/i,\n              alias: 'boolean'\n            },\n            {\n              pattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n              greedy: true,\n              lookbehind: true\n            },\n            /\\b(?:null)\\b/i,\n            /\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/\n          ];\n          var number = /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i;\n          var operator = /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/;\n          var punctuation = /[{}\\[\\](),:;]/;\n          Prism.languages.php = {\n            'delimiter': {\n              pattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n              alias: 'important'\n            },\n            'comment': comment,\n            'variable': /\\$+(?:\\w+\\b|(?=\\{))/,\n            'package': {\n              pattern: /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n              lookbehind: true,\n              inside: { 'punctuation': /\\\\/ }\n            },\n            'class-name-definition': {\n              pattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n              lookbehind: true,\n              alias: 'class-name'\n            },\n            'function-definition': {\n              pattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n              lookbehind: true,\n              alias: 'function'\n            },\n            'keyword': [\n              {\n                pattern: /(\\(\\s*)\\b(?:array|bool|boolean|float|int|integer|object|string)\\b(?=\\s*\\))/i,\n                alias: 'type-casting',\n                greedy: true,\n                lookbehind: true\n              },\n              {\n                pattern: /([(,?]\\s*)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string)\\b(?=\\s*\\$)/i,\n                alias: 'type-hint',\n                greedy: true,\n                lookbehind: true\n              },\n              {\n                pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|never|object|self|static|string|void)\\b/i,\n                alias: 'return-type',\n                greedy: true,\n                lookbehind: true\n              },\n              {\n                pattern: /\\b(?:array(?!\\s*\\()|bool|float|int|iterable|mixed|object|string|void)\\b/i,\n                alias: 'type-declaration',\n                greedy: true\n              },\n              {\n                pattern: /(\\|\\s*)(?:false|null)\\b|\\b(?:false|null)(?=\\s*\\|)/i,\n                alias: 'type-declaration',\n                greedy: true,\n                lookbehind: true\n              },\n              {\n                pattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n                alias: 'static-context',\n                greedy: true\n              },\n              {\n                pattern: /(\\byield\\s+)from\\b/i,\n                lookbehind: true\n              },\n              /\\bclass\\b/i,\n              {\n                pattern: /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|never|new|or|parent|print|private|protected|public|readonly|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\\b/i,\n                lookbehind: true\n              }\n            ],\n            'argument-name': {\n              pattern: /([(,]\\s*)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n              lookbehind: true\n            },\n            'class-name': [\n              {\n                pattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                greedy: true,\n                lookbehind: true\n              },\n              {\n                pattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                greedy: true,\n                lookbehind: true\n              },\n              {\n                pattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n                greedy: true\n              },\n              {\n                pattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n                alias: 'class-name-fully-qualified',\n                greedy: true,\n                lookbehind: true,\n                inside: { 'punctuation': /\\\\/ }\n              },\n              {\n                pattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n                alias: 'class-name-fully-qualified',\n                greedy: true,\n                inside: { 'punctuation': /\\\\/ }\n              },\n              {\n                pattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n                alias: 'class-name-fully-qualified',\n                greedy: true,\n                lookbehind: true,\n                inside: { 'punctuation': /\\\\/ }\n              },\n              {\n                pattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n                alias: 'type-declaration',\n                greedy: true\n              },\n              {\n                pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n                alias: [\n                  'class-name-fully-qualified',\n                  'type-declaration'\n                ],\n                greedy: true,\n                inside: { 'punctuation': /\\\\/ }\n              },\n              {\n                pattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n                alias: 'static-context',\n                greedy: true\n              },\n              {\n                pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n                alias: [\n                  'class-name-fully-qualified',\n                  'static-context'\n                ],\n                greedy: true,\n                inside: { 'punctuation': /\\\\/ }\n              },\n              {\n                pattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n                alias: 'type-hint',\n                greedy: true,\n                lookbehind: true\n              },\n              {\n                pattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n                alias: [\n                  'class-name-fully-qualified',\n                  'type-hint'\n                ],\n                greedy: true,\n                lookbehind: true,\n                inside: { 'punctuation': /\\\\/ }\n              },\n              {\n                pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                alias: 'return-type',\n                greedy: true,\n                lookbehind: true\n              },\n              {\n                pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n                alias: [\n                  'class-name-fully-qualified',\n                  'return-type'\n                ],\n                greedy: true,\n                lookbehind: true,\n                inside: { 'punctuation': /\\\\/ }\n              }\n            ],\n            'constant': constant,\n            'function': {\n              pattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n              lookbehind: true,\n              inside: { 'punctuation': /\\\\/ }\n            },\n            'property': {\n              pattern: /(->\\s*)\\w+/,\n              lookbehind: true\n            },\n            'number': number,\n            'operator': operator,\n            'punctuation': punctuation\n          };\n          var string_interpolation = {\n            pattern: /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n            lookbehind: true,\n            inside: Prism.languages.php\n          };\n          var string = [\n            {\n              pattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n              alias: 'nowdoc-string',\n              greedy: true,\n              inside: {\n                'delimiter': {\n                  pattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n                  alias: 'symbol',\n                  inside: { 'punctuation': /^<<<'?|[';]$/ }\n                }\n              }\n            },\n            {\n              pattern: /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n              alias: 'heredoc-string',\n              greedy: true,\n              inside: {\n                'delimiter': {\n                  pattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n                  alias: 'symbol',\n                  inside: { 'punctuation': /^<<<\"?|[\";]$/ }\n                },\n                'interpolation': string_interpolation\n              }\n            },\n            {\n              pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n              alias: 'backtick-quoted-string',\n              greedy: true\n            },\n            {\n              pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n              alias: 'single-quoted-string',\n              greedy: true\n            },\n            {\n              pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n              alias: 'double-quoted-string',\n              greedy: true,\n              inside: { 'interpolation': string_interpolation }\n            }\n          ];\n          Prism.languages.insertBefore('php', 'variable', {\n            'string': string,\n            'attribute': {\n              pattern: /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n              greedy: true,\n              inside: {\n                'attribute-content': {\n                  pattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n                  lookbehind: true,\n                  inside: {\n                    'comment': comment,\n                    'string': string,\n                    'attribute-class-name': [\n                      {\n                        pattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                        alias: 'class-name',\n                        greedy: true,\n                        lookbehind: true\n                      },\n                      {\n                        pattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n                        alias: [\n                          'class-name',\n                          'class-name-fully-qualified'\n                        ],\n                        greedy: true,\n                        lookbehind: true,\n                        inside: { 'punctuation': /\\\\/ }\n                      }\n                    ],\n                    'constant': constant,\n                    'number': number,\n                    'operator': operator,\n                    'punctuation': punctuation\n                  }\n                },\n                'delimiter': {\n                  pattern: /^#\\[|\\]$/,\n                  alias: 'punctuation'\n                }\n              }\n            }\n          });\n          Prism.hooks.add('before-tokenize', function (env) {\n            if (!/<\\?/.test(env.code)) {\n              return;\n            }\n            var phpPattern = /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/g;\n            Prism.languages['markup-templating'].buildPlaceholders(env, 'php', phpPattern);\n          });\n          Prism.hooks.add('after-tokenize', function (env) {\n            Prism.languages['markup-templating'].tokenizePlaceholders(env, 'php');\n          });\n        }(Prism));\n        Prism.languages.python = {\n          'comment': {\n            pattern: /(^|[^\\\\])#.*/,\n            lookbehind: true,\n            greedy: true\n          },\n          'string-interpolation': {\n            pattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n            greedy: true,\n            inside: {\n              'interpolation': {\n                pattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n                lookbehind: true,\n                inside: {\n                  'format-spec': {\n                    pattern: /(:)[^:(){}]+(?=\\}$)/,\n                    lookbehind: true\n                  },\n                  'conversion-option': {\n                    pattern: /![sra](?=[:}]$)/,\n                    alias: 'punctuation'\n                  },\n                  rest: null\n                }\n              },\n              'string': /[\\s\\S]+/\n            }\n          },\n          'triple-quoted-string': {\n            pattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n            greedy: true,\n            alias: 'string'\n          },\n          'string': {\n            pattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n            greedy: true\n          },\n          'function': {\n            pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n            lookbehind: true\n          },\n          'class-name': {\n            pattern: /(\\bclass\\s+)\\w+/i,\n            lookbehind: true\n          },\n          'decorator': {\n            pattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n            lookbehind: true,\n            alias: [\n              'annotation',\n              'punctuation'\n            ],\n            inside: { 'punctuation': /\\./ }\n          },\n          'keyword': /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n          'builtin': /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n          'boolean': /\\b(?:False|None|True)\\b/,\n          'number': /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n          'operator': /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n          'punctuation': /[{}[\\];(),.:]/\n        };\n        Prism.languages.python['string-interpolation'].inside['interpolation'].inside.rest = Prism.languages.python;\n        Prism.languages.py = Prism.languages.python;\n        (function (Prism) {\n          Prism.languages.ruby = Prism.languages.extend('clike', {\n            'comment': {\n              pattern: /#.*|^=begin\\s[\\s\\S]*?^=end/m,\n              greedy: true\n            },\n            'class-name': {\n              pattern: /(\\b(?:class|module)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+|\\b[A-Z_]\\w*(?=\\s*\\.\\s*new\\b)/,\n              lookbehind: true,\n              inside: { 'punctuation': /[.\\\\]/ }\n            },\n            'keyword': /\\b(?:BEGIN|END|alias|and|begin|break|case|class|def|define_method|defined|do|each|else|elsif|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|private|protected|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\\b/,\n            'operator': /\\.{2,3}|&\\.|===|<?=>|[!=]?~|(?:&&|\\|\\||<<|>>|\\*\\*|[+\\-*/%<>!^&|=])=?|[?:]/,\n            'punctuation': /[(){}[\\].,;]/\n          });\n          Prism.languages.insertBefore('ruby', 'operator', {\n            'double-colon': {\n              pattern: /::/,\n              alias: 'punctuation'\n            }\n          });\n          var interpolation = {\n            pattern: /((?:^|[^\\\\])(?:\\\\{2})*)#\\{(?:[^{}]|\\{[^{}]*\\})*\\}/,\n            lookbehind: true,\n            inside: {\n              'content': {\n                pattern: /^(#\\{)[\\s\\S]+(?=\\}$)/,\n                lookbehind: true,\n                inside: Prism.languages.ruby\n              },\n              'delimiter': {\n                pattern: /^#\\{|\\}$/,\n                alias: 'punctuation'\n              }\n            }\n          };\n          delete Prism.languages.ruby.function;\n          var percentExpression = '(?:' + [\n            /([^a-zA-Z0-9\\s{(\\[<=])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source,\n            /\\((?:[^()\\\\]|\\\\[\\s\\S]|\\((?:[^()\\\\]|\\\\[\\s\\S])*\\))*\\)/.source,\n            /\\{(?:[^{}\\\\]|\\\\[\\s\\S]|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\})*\\}/.source,\n            /\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S]|\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S])*\\])*\\]/.source,\n            /<(?:[^<>\\\\]|\\\\[\\s\\S]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)*>/.source\n          ].join('|') + ')';\n          var symbolName = /(?:\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|(?:\\b[a-zA-Z_]\\w*|[^\\s\\0-\\x7F]+)[?!]?|\\$.)/.source;\n          Prism.languages.insertBefore('ruby', 'keyword', {\n            'regex-literal': [\n              {\n                pattern: RegExp(/%r/.source + percentExpression + /[egimnosux]{0,6}/.source),\n                greedy: true,\n                inside: {\n                  'interpolation': interpolation,\n                  'regex': /[\\s\\S]+/\n                }\n              },\n              {\n                pattern: /(^|[^/])\\/(?!\\/)(?:\\[[^\\r\\n\\]]+\\]|\\\\.|[^[/\\\\\\r\\n])+\\/[egimnosux]{0,6}(?=\\s*(?:$|[\\r\\n,.;})#]))/,\n                lookbehind: true,\n                greedy: true,\n                inside: {\n                  'interpolation': interpolation,\n                  'regex': /[\\s\\S]+/\n                }\n              }\n            ],\n            'variable': /[@$]+[a-zA-Z_]\\w*(?:[?!]|\\b)/,\n            'symbol': [\n              {\n                pattern: RegExp(/(^|[^:]):/.source + symbolName),\n                lookbehind: true,\n                greedy: true\n              },\n              {\n                pattern: RegExp(/([\\r\\n{(,][ \\t]*)/.source + symbolName + /(?=:(?!:))/.source),\n                lookbehind: true,\n                greedy: true\n              }\n            ],\n            'method-definition': {\n              pattern: /(\\bdef\\s+)\\w+(?:\\s*\\.\\s*\\w+)?/,\n              lookbehind: true,\n              inside: {\n                'function': /\\b\\w+$/,\n                'keyword': /^self\\b/,\n                'class-name': /^\\w+/,\n                'punctuation': /\\./\n              }\n            }\n          });\n          Prism.languages.insertBefore('ruby', 'string', {\n            'string-literal': [\n              {\n                pattern: RegExp(/%[qQiIwWs]?/.source + percentExpression),\n                greedy: true,\n                inside: {\n                  'interpolation': interpolation,\n                  'string': /[\\s\\S]+/\n                }\n              },\n              {\n                pattern: /(\"|')(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\#\\r\\n])*\\1/,\n                greedy: true,\n                inside: {\n                  'interpolation': interpolation,\n                  'string': /[\\s\\S]+/\n                }\n              },\n              {\n                pattern: /<<[-~]?([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n                alias: 'heredoc-string',\n                greedy: true,\n                inside: {\n                  'delimiter': {\n                    pattern: /^<<[-~]?[a-z_]\\w*|\\b[a-z_]\\w*$/i,\n                    inside: {\n                      'symbol': /\\b\\w+/,\n                      'punctuation': /^<<[-~]?/\n                    }\n                  },\n                  'interpolation': interpolation,\n                  'string': /[\\s\\S]+/\n                }\n              },\n              {\n                pattern: /<<[-~]?'([a-z_]\\w*)'[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n                alias: 'heredoc-string',\n                greedy: true,\n                inside: {\n                  'delimiter': {\n                    pattern: /^<<[-~]?'[a-z_]\\w*'|\\b[a-z_]\\w*$/i,\n                    inside: {\n                      'symbol': /\\b\\w+/,\n                      'punctuation': /^<<[-~]?'|'$/\n                    }\n                  },\n                  'string': /[\\s\\S]+/\n                }\n              }\n            ],\n            'command-literal': [\n              {\n                pattern: RegExp(/%x/.source + percentExpression),\n                greedy: true,\n                inside: {\n                  'interpolation': interpolation,\n                  'command': {\n                    pattern: /[\\s\\S]+/,\n                    alias: 'string'\n                  }\n                }\n              },\n              {\n                pattern: /`(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|[^\\\\`#\\r\\n])*`/,\n                greedy: true,\n                inside: {\n                  'interpolation': interpolation,\n                  'command': {\n                    pattern: /[\\s\\S]+/,\n                    alias: 'string'\n                  }\n                }\n              }\n            ]\n          });\n          delete Prism.languages.ruby.string;\n          Prism.languages.insertBefore('ruby', 'number', {\n            'builtin': /\\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Fixnum|Float|Hash|IO|Integer|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|Stat|String|Struct|Symbol|TMS|Thread|ThreadGroup|Time|TrueClass)\\b/,\n            'constant': /\\b[A-Z][A-Z0-9_]*(?:[?!]|\\b)/\n          });\n          Prism.languages.rb = Prism.languages.ruby;\n        }(Prism));\n        var Prism$1 = prismCore.exports;\n        var prismjs = { boltExport: Prism$1 };\n        return prismjs;\n      }));\n      var prism = window.Prism;\n      window.Prism = oldprism;\n      return prism;\n    }(undefined, exports$1, module));\n    var Prism$1 = module.exports.boltExport;\n\n    var getLanguages$1 = function (editor) {\n      return editor.getParam('codesample_languages');\n    };\n    var useGlobalPrismJS = function (editor) {\n      return editor.getParam('codesample_global_prismjs', false, 'boolean');\n    };\n\n    var get = function (editor) {\n      return Global.Prism && useGlobalPrismJS(editor) ? Global.Prism : Prism$1;\n    };\n\n    var getSelectedCodeSample = function (editor) {\n      var node = editor.selection ? editor.selection.getNode() : null;\n      return someIf(isCodeSample(node), node);\n    };\n    var insertCodeSample = function (editor, language, code) {\n      editor.undoManager.transact(function () {\n        var node = getSelectedCodeSample(editor);\n        code = global$1.DOM.encode(code);\n        return node.fold(function () {\n          editor.insertContent('<pre id=\"__new\" class=\"language-' + language + '\">' + code + '</pre>');\n          editor.selection.select(editor.$('#__new').removeAttr('id')[0]);\n        }, function (n) {\n          editor.dom.setAttrib(n, 'class', 'language-' + language);\n          n.innerHTML = code;\n          get(editor).highlightElement(n);\n          editor.selection.select(n);\n        });\n      });\n    };\n    var getCurrentCode = function (editor) {\n      var node = getSelectedCodeSample(editor);\n      return node.fold(constant(''), function (n) {\n        return n.textContent;\n      });\n    };\n\n    var getLanguages = function (editor) {\n      var defaultLanguages = [\n        {\n          text: 'HTML/XML',\n          value: 'markup'\n        },\n        {\n          text: 'JavaScript',\n          value: 'javascript'\n        },\n        {\n          text: 'CSS',\n          value: 'css'\n        },\n        {\n          text: 'PHP',\n          value: 'php'\n        },\n        {\n          text: 'Ruby',\n          value: 'ruby'\n        },\n        {\n          text: 'Python',\n          value: 'python'\n        },\n        {\n          text: 'Java',\n          value: 'java'\n        },\n        {\n          text: 'C',\n          value: 'c'\n        },\n        {\n          text: 'C#',\n          value: 'csharp'\n        },\n        {\n          text: 'C++',\n          value: 'cpp'\n        }\n      ];\n      var customLanguages = getLanguages$1(editor);\n      return customLanguages ? customLanguages : defaultLanguages;\n    };\n    var getCurrentLanguage = function (editor, fallback) {\n      var node = getSelectedCodeSample(editor);\n      return node.fold(function () {\n        return fallback;\n      }, function (n) {\n        var matches = n.className.match(/language-(\\w+)/);\n        return matches ? matches[1] : fallback;\n      });\n    };\n\n    var open = function (editor) {\n      var languages = getLanguages(editor);\n      var defaultLanguage = head(languages).fold(constant(''), function (l) {\n        return l.value;\n      });\n      var currentLanguage = getCurrentLanguage(editor, defaultLanguage);\n      var currentCode = getCurrentCode(editor);\n      editor.windowManager.open({\n        title: 'Insert/Edit Code Sample',\n        size: 'large',\n        body: {\n          type: 'panel',\n          items: [\n            {\n              type: 'selectbox',\n              name: 'language',\n              label: 'Language',\n              items: languages\n            },\n            {\n              type: 'textarea',\n              name: 'code',\n              label: 'Code view'\n            }\n          ]\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: {\n          language: currentLanguage,\n          code: currentCode\n        },\n        onSubmit: function (api) {\n          var data = api.getData();\n          insertCodeSample(editor, data.language, data.code);\n          api.close();\n        }\n      });\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('codesample', function () {\n        var node = editor.selection.getNode();\n        if (editor.selection.isCollapsed() || isCodeSample(node)) {\n          open(editor);\n        } else {\n          editor.formatter.toggle('code');\n        }\n      });\n    };\n\n    var setup = function (editor) {\n      var $ = editor.$;\n      editor.on('PreProcess', function (e) {\n        $('pre[contenteditable=false]', e.node).filter(trimArg(isCodeSample)).each(function (idx, elm) {\n          var $elm = $(elm), code = elm.textContent;\n          $elm.attr('class', $.trim($elm.attr('class')));\n          $elm.removeAttr('contentEditable');\n          $elm.empty().append($('<code></code>').each(function () {\n            this.textContent = code;\n          }));\n        });\n      });\n      editor.on('SetContent', function () {\n        var unprocessedCodeSamples = $('pre').filter(trimArg(isCodeSample)).filter(function (idx, elm) {\n          return elm.contentEditable !== 'false';\n        });\n        if (unprocessedCodeSamples.length) {\n          editor.undoManager.transact(function () {\n            unprocessedCodeSamples.each(function (idx, elm) {\n              $(elm).find('br').each(function (idx, elm) {\n                elm.parentNode.replaceChild(editor.getDoc().createTextNode('\\n'), elm);\n              });\n              elm.contentEditable = 'false';\n              elm.innerHTML = editor.dom.encode(elm.textContent);\n              get(editor).highlightElement(elm);\n              elm.className = $.trim(elm.className);\n            });\n          });\n        }\n      });\n    };\n\n    var isCodeSampleSelection = function (editor) {\n      var node = editor.selection.getStart();\n      return editor.dom.is(node, 'pre[class*=\"language-\"]');\n    };\n    var register = function (editor) {\n      var onAction = function () {\n        return editor.execCommand('codesample');\n      };\n      editor.ui.registry.addToggleButton('codesample', {\n        icon: 'code-sample',\n        tooltip: 'Insert/edit code sample',\n        onAction: onAction,\n        onSetup: function (api) {\n          var nodeChangeHandler = function () {\n            api.setActive(isCodeSampleSelection(editor));\n          };\n          editor.on('NodeChange', nodeChangeHandler);\n          return function () {\n            return editor.off('NodeChange', nodeChangeHandler);\n          };\n        }\n      });\n      editor.ui.registry.addMenuItem('codesample', {\n        text: 'Code sample...',\n        icon: 'code-sample',\n        onAction: onAction\n      });\n    };\n\n    function Plugin () {\n      global$2.add('codesample', function (editor) {\n        setup(editor);\n        register(editor);\n        register$1(editor);\n        editor.on('dblclick', function (ev) {\n          if (isCodeSample(ev.target)) {\n            open(editor);\n          }\n        });\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"codesample\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/codesample')\n//   ES2015:\n//     import 'tinymce/plugins/codesample'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/codesample/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,QAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,QAAQ,SAAU,IAAI,GAAG;AAC3B,eAAO,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,MAAM,SAAS;AAAA;AAEnE,UAAI,OAAO,SAAU,IAAI;AACvB,eAAO,MAAM,IAAI;AAAA;AAGnB,UAAI,SAAS,SAAU,GAAG,GAAG;AAC3B,eAAO,IAAI,SAAS,KAAK,KAAK,SAAS;AAAA;AAGzC,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,eAAe,SAAU,KAAK;AAChC,eAAO,OAAO,IAAI,aAAa,SAAS,IAAI,UAAU,QAAQ,iBAAiB;AAAA;AAEjF,UAAI,UAAU,SAAU,aAAa;AACnC,eAAO,SAAU,MAAM,MAAM;AAC3B,iBAAO,YAAY;AAAA;AAAA;AAIvB,UAAI,SAAS,OAAO,WAAW,cAAc,SAAS,SAAS;AAE/D,UAAI,YAAY,IAAI,UAAS,EAAE,SAAS,aAAa,SAAS;AAC9D,MAAC,UAAU,QAAQ,UAAS,SAAQ,UAAS;AAC3C,YAAI,WAAW,OAAO;AACtB,eAAO,QAAQ,EAAE,QAAQ;AACzB,QAAC,UAAU,SAAQ,SAAS;AAC1B,iBAAO,aAAY,YAAY,OAAO,YAAW,cAAc,QAAO,UAAU,YAAY,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,WAAY,WAAS,OAAO,eAAe,cAAc,aAAa,WAAU,MAAM,QAAO,sBAAsB;AAAA,WACnQ,MAAM,WAAY;AAClB,cAAI,iBAAiB,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO;AAC7L,cAAI,YAAY,EAAE,SAAS;AAC3B,UAAC,UAAU,SAAQ;AACjB,gBAAI,QAAQ,OAAO,WAAW,cAAc,SAAS,OAAO,sBAAsB,eAAe,gBAAgB,oBAAoB,OAAO;AAC5I,gBAAI,SAAQ,SAAU,QAAO;AAC3B,kBAAI,OAAO;AACX,kBAAI,WAAW;AACf,kBAAI,mBAAmB;AACvB,kBAAI,IAAI;AAAA,gBACN,QAAQ,OAAM,SAAS,OAAM,MAAM;AAAA,gBACnC,6BAA6B,OAAM,SAAS,OAAM,MAAM;AAAA,gBACxD,MAAM;AAAA,kBACJ,QAAQ,gBAAgB,QAAQ;AAC9B,wBAAI,kBAAkB,OAAO;AAC3B,6BAAO,IAAI,MAAM,OAAO,MAAM,OAAO,OAAO,UAAU,OAAO;AAAA,+BACpD,MAAM,QAAQ,SAAS;AAChC,6BAAO,OAAO,IAAI;AAAA,2BACb;AACL,6BAAO,OAAO,QAAQ,MAAM,SAAS,QAAQ,MAAM,QAAQ,QAAQ,WAAW;AAAA;AAAA;AAAA,kBAGlF,MAAM,SAAU,GAAG;AACjB,2BAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,GAAG;AAAA;AAAA,kBAEpD,OAAO,SAAU,KAAK;AACpB,wBAAI,CAAC,IAAI,SAAS;AAChB,6BAAO,eAAe,KAAK,QAAQ,EAAE,OAAO,EAAE;AAAA;AAEhD,2BAAO,IAAI;AAAA;AAAA,kBAEb,OAAO,mBAAmB,GAAG,SAAS;AACpC,8BAAU,WAAW;AACrB,wBAAI;AACJ,wBAAI;AACJ,4BAAQ,EAAE,KAAK,KAAK;AAAA,2BACf;AACH,6BAAK,EAAE,KAAK,MAAM;AAClB,4BAAI,QAAQ,KAAK;AACf,iCAAO,QAAQ;AAAA;AAEjB,gCAAQ;AACR,gCAAQ,MAAM;AACd,iCAAS,OAAO,GAAG;AACjB,8BAAI,EAAE,eAAe,MAAM;AACzB,kCAAM,OAAO,UAAU,EAAE,MAAM;AAAA;AAAA;AAGnC,+BAAO;AAAA,2BACJ;AACH,6BAAK,EAAE,KAAK,MAAM;AAClB,4BAAI,QAAQ,KAAK;AACf,iCAAO,QAAQ;AAAA;AAEjB,gCAAQ;AACR,gCAAQ,MAAM;AACd,0BAAE,QAAQ,SAAU,GAAG,GAAG;AACxB,gCAAM,KAAK,UAAU,GAAG;AAAA;AAE1B,+BAAO;AAAA;AAEP,+BAAO;AAAA;AAAA;AAAA,kBAGX,aAAa,SAAU,SAAS;AAC9B,2BAAO,SAAS;AACd,0BAAI,IAAI,KAAK,KAAK,QAAQ;AAC1B,0BAAI,GAAG;AACL,+BAAO,EAAE,GAAG;AAAA;AAEd,gCAAU,QAAQ;AAAA;AAEpB,2BAAO;AAAA;AAAA,kBAET,aAAa,SAAU,SAAS,UAAU;AACxC,4BAAQ,YAAY,QAAQ,UAAU,QAAQ,OAAO,KAAK,QAAQ,OAAO;AACzE,4BAAQ,UAAU,IAAI,cAAc;AAAA;AAAA,kBAEtC,eAAe,WAAY;AACzB,wBAAI,OAAO,aAAa,aAAa;AACnC,6BAAO;AAAA;AAET,wBAAI,mBAAmB,YAAY,IAAI,GAAG;AACxC,6BAAO,SAAS;AAAA;AAElB,wBAAI;AACF,4BAAM,IAAI;AAAA,6BACH,KAAP;AACA,0BAAI,MAAO,sCAAqC,KAAK,IAAI,UAAU,IAAI;AACvE,0BAAI,KAAK;AACP,4BAAI,UAAU,SAAS,qBAAqB;AAC5C,iCAAS,KAAK,SAAS;AACrB,8BAAI,QAAQ,GAAG,OAAO,KAAK;AACzB,mCAAO,QAAQ;AAAA;AAAA;AAAA;AAIrB,6BAAO;AAAA;AAAA;AAAA,kBAGX,UAAU,SAAU,SAAS,WAAW,mBAAmB;AACzD,wBAAI,KAAK,QAAQ;AACjB,2BAAO,SAAS;AACd,0BAAI,YAAY,QAAQ;AACxB,0BAAI,UAAU,SAAS,YAAY;AACjC,+BAAO;AAAA;AAET,0BAAI,UAAU,SAAS,KAAK;AAC1B,+BAAO;AAAA;AAET,gCAAU,QAAQ;AAAA;AAEpB,2BAAO,CAAC,CAAC;AAAA;AAAA;AAAA,gBAGb,WAAW;AAAA,kBACT,OAAO;AAAA,kBACP,WAAW;AAAA,kBACX,MAAM;AAAA,kBACN,KAAK;AAAA,kBACL,QAAQ,SAAU,IAAI,OAAO;AAC3B,wBAAI,QAAO,EAAE,KAAK,MAAM,EAAE,UAAU;AACpC,6BAAS,OAAO,OAAO;AACrB,4BAAK,OAAO,MAAM;AAAA;AAEpB,2BAAO;AAAA;AAAA,kBAET,cAAc,SAAU,QAAQ,QAAQ,QAAQ,MAAM;AACpD,2BAAO,QAAQ,EAAE;AACjB,wBAAI,UAAU,KAAK;AACnB,wBAAI,MAAM;AACV,6BAAS,SAAS,SAAS;AACzB,0BAAI,QAAQ,eAAe,QAAQ;AACjC,4BAAI,SAAS,QAAQ;AACnB,mCAAS,YAAY,QAAQ;AAC3B,gCAAI,OAAO,eAAe,WAAW;AACnC,kCAAI,YAAY,OAAO;AAAA;AAAA;AAAA;AAI7B,4BAAI,CAAC,OAAO,eAAe,QAAQ;AACjC,8BAAI,SAAS,QAAQ;AAAA;AAAA;AAAA;AAI3B,wBAAI,MAAM,KAAK;AACf,yBAAK,UAAU;AACf,sBAAE,UAAU,IAAI,EAAE,WAAW,SAAU,KAAK,OAAO;AACjD,0BAAI,UAAU,OAAO,OAAO,QAAQ;AAClC,6BAAK,OAAO;AAAA;AAAA;AAGhB,2BAAO;AAAA;AAAA,kBAET,KAAK,aAAa,GAAG,UAAU,MAAM,SAAS;AAC5C,8BAAU,WAAW;AACrB,wBAAI,QAAQ,EAAE,KAAK;AACnB,6BAAS,KAAK,GAAG;AACf,0BAAI,EAAE,eAAe,IAAI;AACvB,iCAAS,KAAK,GAAG,GAAG,EAAE,IAAI,QAAQ;AAClC,4BAAI,WAAW,EAAE;AACjB,4BAAI,eAAe,EAAE,KAAK,KAAK;AAC/B,4BAAI,iBAAiB,YAAY,CAAC,QAAQ,MAAM,YAAY;AAC1D,kCAAQ,MAAM,aAAa;AAC3B,8BAAI,UAAU,UAAU,MAAM;AAAA,mCACrB,iBAAiB,WAAW,CAAC,QAAQ,MAAM,YAAY;AAChE,kCAAQ,MAAM,aAAa;AAC3B,8BAAI,UAAU,UAAU,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAMrC,SAAS;AAAA,gBACT,cAAc,SAAU,OAAO,UAAU;AACvC,oBAAE,kBAAkB,UAAU,OAAO;AAAA;AAAA,gBAEvC,mBAAmB,SAAU,WAAW,OAAO,UAAU;AACvD,sBAAI,MAAM;AAAA,oBACR;AAAA,oBACA;AAAA,oBACA,UAAU;AAAA;AAEZ,oBAAE,MAAM,IAAI,uBAAuB;AACnC,sBAAI,WAAW,MAAM,UAAU,MAAM,MAAM,IAAI,UAAU,iBAAiB,IAAI;AAC9E,oBAAE,MAAM,IAAI,iCAAiC;AAC7C,2BAAS,IAAI,GAAG,SAAS,UAAU,IAAI,SAAS,QAAO;AACrD,sBAAE,iBAAiB,SAAS,UAAU,MAAM,IAAI;AAAA;AAAA;AAAA,gBAGpD,kBAAkB,SAAU,SAAS,OAAO,UAAU;AACpD,sBAAI,WAAW,EAAE,KAAK,YAAY;AAClC,sBAAI,UAAU,EAAE,UAAU;AAC1B,oBAAE,KAAK,YAAY,SAAS;AAC5B,sBAAI,SAAS,QAAQ;AACrB,sBAAI,UAAU,OAAO,SAAS,kBAAkB,OAAO;AACrD,sBAAE,KAAK,YAAY,QAAQ;AAAA;AAE7B,sBAAI,OAAO,QAAQ;AACnB,sBAAI,MAAM;AAAA,oBACR;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA;AAEF,iDAA+B,iBAAiB;AAC9C,wBAAI,kBAAkB;AACtB,sBAAE,MAAM,IAAI,iBAAiB;AAC7B,wBAAI,QAAQ,YAAY,IAAI;AAC5B,sBAAE,MAAM,IAAI,mBAAmB;AAC/B,sBAAE,MAAM,IAAI,YAAY;AACxB,gCAAY,SAAS,KAAK,IAAI;AAAA;AAEhC,oBAAE,MAAM,IAAI,uBAAuB;AACnC,2BAAS,IAAI,QAAQ;AACrB,sBAAI,UAAU,OAAO,SAAS,kBAAkB,SAAS,CAAC,OAAO,aAAa,aAAa;AACzF,2BAAO,aAAa,YAAY;AAAA;AAElC,sBAAI,CAAC,IAAI,MAAM;AACb,sBAAE,MAAM,IAAI,YAAY;AACxB,gCAAY,SAAS,KAAK,IAAI;AAC9B;AAAA;AAEF,oBAAE,MAAM,IAAI,oBAAoB;AAChC,sBAAI,CAAC,IAAI,SAAS;AAChB,0CAAsB,EAAE,KAAK,OAAO,IAAI;AACxC;AAAA;AAEF,sBAAI,SAAS,OAAM,QAAQ;AACzB,wBAAI,SAAS,IAAI,OAAO,EAAE;AAC1B,2BAAO,YAAY,SAAU,KAAK;AAChC,4CAAsB,IAAI;AAAA;AAE5B,2BAAO,YAAY,KAAK,UAAU;AAAA,sBAChC,UAAU,IAAI;AAAA,sBACd,MAAM,IAAI;AAAA,sBACV,gBAAgB;AAAA;AAAA,yBAEb;AACL,0CAAsB,EAAE,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI;AAAA;AAAA;AAAA,gBAGjE,WAAW,SAAU,MAAM,SAAS,UAAU;AAC5C,sBAAI,MAAM;AAAA,oBACR,MAAM;AAAA,oBACN;AAAA,oBACA;AAAA;AAEF,oBAAE,MAAM,IAAI,mBAAmB;AAC/B,sBAAI,CAAC,IAAI,SAAS;AAChB,0BAAM,IAAI,MAAM,mBAAmB,IAAI,WAAW;AAAA;AAEpD,sBAAI,SAAS,EAAE,SAAS,IAAI,MAAM,IAAI;AACtC,oBAAE,MAAM,IAAI,kBAAkB;AAC9B,yBAAO,MAAM,UAAU,EAAE,KAAK,OAAO,IAAI,SAAS,IAAI;AAAA;AAAA,gBAExD,UAAU,SAAU,MAAM,SAAS;AACjC,sBAAI,OAAO,QAAQ;AACnB,sBAAI,MAAM;AACR,6BAAS,SAAS,MAAM;AACtB,8BAAQ,SAAS,KAAK;AAAA;AAExB,2BAAO,QAAQ;AAAA;AAEjB,sBAAI,YAAY,IAAI;AACpB,2BAAS,WAAW,UAAU,MAAM;AACpC,+BAAa,MAAM,WAAW,SAAS,UAAU,MAAM;AACvD,yBAAO,QAAQ;AAAA;AAAA,gBAEjB,OAAO;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK,SAAU,MAAM,UAAU;AAC7B,wBAAI,QAAQ,EAAE,MAAM;AACpB,0BAAM,QAAQ,MAAM,SAAS;AAC7B,0BAAM,MAAM,KAAK;AAAA;AAAA,kBAEnB,KAAK,SAAU,MAAM,KAAK;AACxB,wBAAI,YAAY,EAAE,MAAM,IAAI;AAC5B,wBAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACnC;AAAA;AAEF,6BAAS,IAAI,GAAG,UAAU,WAAW,UAAU,QAAO;AACpD,+BAAS;AAAA;AAAA;AAAA;AAAA,gBAIf;AAAA;AAEF,qBAAM,QAAQ;AACd,6BAAe,MAAM,SAAS,OAAO,YAAY;AAC/C,qBAAK,OAAO;AACZ,qBAAK,UAAU;AACf,qBAAK,QAAQ;AACb,qBAAK,SAAU,eAAc,IAAI,SAAS;AAAA;AAE5C,oBAAM,YAAY,mBAAmB,GAAG,UAAU;AAChD,oBAAI,OAAO,KAAK,UAAU;AACxB,yBAAO;AAAA;AAET,oBAAI,MAAM,QAAQ,IAAI;AACpB,sBAAI,IAAI;AACR,oBAAE,QAAQ,SAAU,GAAG;AACrB,yBAAK,UAAU,GAAG;AAAA;AAEpB,yBAAO;AAAA;AAET,oBAAI,MAAM;AAAA,kBACR,MAAM,EAAE;AAAA,kBACR,SAAS,UAAU,EAAE,SAAS;AAAA,kBAC9B,KAAK;AAAA,kBACL,SAAS;AAAA,oBACP;AAAA,oBACA,EAAE;AAAA;AAAA,kBAEJ,YAAY;AAAA,kBACZ;AAAA;AAEF,oBAAI,UAAU,EAAE;AAChB,oBAAI,SAAS;AACX,sBAAI,MAAM,QAAQ,UAAU;AAC1B,0BAAM,UAAU,KAAK,MAAM,IAAI,SAAS;AAAA,yBACnC;AACL,wBAAI,QAAQ,KAAK;AAAA;AAAA;AAGrB,kBAAE,MAAM,IAAI,QAAQ;AACpB,oBAAI,aAAa;AACjB,yBAAS,QAAQ,IAAI,YAAY;AAC/B,gCAAc,MAAM,OAAO,OAAQ,KAAI,WAAW,SAAS,IAAI,QAAQ,MAAM,YAAY;AAAA;AAE3F,uBAAO,MAAM,IAAI,MAAM,aAAa,IAAI,QAAQ,KAAK,OAAO,MAAM,aAAa,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM;AAAA;AAEtH,oCAAsB,SAAS,KAAK,MAAM,YAAY;AACpD,wBAAQ,YAAY;AACpB,oBAAI,QAAQ,QAAQ,KAAK;AACzB,oBAAI,SAAS,cAAc,MAAM,IAAI;AACnC,sBAAI,mBAAmB,MAAM,GAAG;AAChC,wBAAM,SAAS;AACf,wBAAM,KAAK,MAAM,GAAG,MAAM;AAAA;AAE5B,uBAAO;AAAA;AAET,oCAAsB,MAAM,WAAW,SAAS,WAAW,UAAU,SAAS;AAC5E,yBAAS,SAAS,SAAS;AACzB,sBAAI,CAAC,QAAQ,eAAe,UAAU,CAAC,QAAQ,QAAQ;AACrD;AAAA;AAEF,sBAAI,WAAW,QAAQ;AACvB,6BAAW,MAAM,QAAQ,YAAY,WAAW,CAAC;AACjD,2BAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,wBAAI,WAAW,QAAQ,SAAS,QAAQ,MAAM,GAAG;AAC/C;AAAA;AAEF,wBAAI,aAAa,SAAS;AAC1B,wBAAI,SAAS,WAAW;AACxB,wBAAI,aAAa,CAAC,CAAC,WAAW;AAC9B,wBAAI,SAAS,CAAC,CAAC,WAAW;AAC1B,wBAAI,QAAQ,WAAW;AACvB,wBAAI,UAAU,CAAC,WAAW,QAAQ,QAAQ;AACxC,0BAAI,QAAQ,WAAW,QAAQ,WAAW,MAAM,aAAa;AAC7D,iCAAW,UAAU,OAAO,WAAW,QAAQ,QAAQ,QAAQ;AAAA;AAEjE,wBAAI,UAAU,WAAW,WAAW;AACpC,6BAAS,cAAc,UAAU,MAAM,MAAM,UAAU,gBAAgB,UAAU,MAAM,OAAO,YAAY,MAAM,QAAQ,cAAc,YAAY,MAAM;AACtJ,0BAAI,WAAW,OAAO,QAAQ,OAAO;AACnC;AAAA;AAEF,0BAAI,MAAM,YAAY;AACtB,0BAAI,UAAU,SAAS,KAAK,QAAQ;AAClC;AAAA;AAEF,0BAAI,eAAe,OAAO;AACxB;AAAA;AAEF,0BAAI,cAAc;AAClB,0BAAI;AACJ,0BAAI,QAAQ;AACV,gCAAQ,aAAa,SAAS,KAAK,MAAM;AACzC,4BAAI,CAAC,SAAS,MAAM,SAAS,KAAK,QAAQ;AACxC;AAAA;AAEF,4BAAI,QAAO,MAAM;AACjB,4BAAI,KAAK,MAAM,QAAQ,MAAM,GAAG;AAChC,4BAAI,IAAI;AACR,6BAAK,YAAY,MAAM;AACvB,+BAAO,SAAQ,GAAG;AAChB,wCAAc,YAAY;AAC1B,+BAAK,YAAY,MAAM;AAAA;AAEzB,6BAAK,YAAY,MAAM;AACvB,8BAAM;AACN,4BAAI,YAAY,iBAAiB,OAAO;AACtC;AAAA;AAEF,iCAAS,IAAI,aAAa,MAAM,UAAU,QAAS,KAAI,MAAM,OAAO,EAAE,UAAU,WAAW,IAAI,EAAE,MAAM;AACrG;AACA,+BAAK,EAAE,MAAM;AAAA;AAEf;AACA,8BAAM,KAAK,MAAM,KAAK;AACtB,8BAAM,SAAS;AAAA,6BACV;AACL,gCAAQ,aAAa,SAAS,GAAG,KAAK;AACtC,4BAAI,CAAC,OAAO;AACV;AAAA;AAAA;AAGJ,0BAAI,QAAO,MAAM;AACjB,0BAAI,WAAW,MAAM;AACrB,0BAAI,SAAS,IAAI,MAAM,GAAG;AAC1B,0BAAI,QAAQ,IAAI,MAAM,QAAO,SAAS;AACtC,0BAAI,QAAQ,MAAM,IAAI;AACtB,0BAAI,WAAW,QAAQ,QAAQ,OAAO;AACpC,gCAAQ,QAAQ;AAAA;AAElB,0BAAI,aAAa,YAAY;AAC7B,0BAAI,QAAQ;AACV,qCAAa,SAAS,WAAW,YAAY;AAC7C,+BAAO,OAAO;AAAA;AAEhB,kCAAY,WAAW,YAAY;AACnC,0BAAI,UAAU,IAAI,MAAM,OAAO,SAAS,EAAE,SAAS,UAAU,UAAU,UAAU,OAAO;AACxF,oCAAc,SAAS,WAAW,YAAY;AAC9C,0BAAI,OAAO;AACT,iCAAS,WAAW,aAAa;AAAA;AAEnC,0BAAI,cAAc,GAAG;AACnB,4BAAI,gBAAgB;AAAA,0BAClB,OAAO,QAAQ,MAAM;AAAA,0BACrB;AAAA;AAEF,qCAAa,MAAM,WAAW,SAAS,YAAY,MAAM,KAAK;AAC9D,4BAAI,WAAW,cAAc,QAAQ,QAAQ,OAAO;AAClD,kCAAQ,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAO1C,oCAAsB;AACpB,oBAAI,QAAO;AAAA,kBACT,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,MAAM;AAAA;AAER,oBAAI,OAAO;AAAA,kBACT,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,MAAM;AAAA;AAER,sBAAK,OAAO;AACZ,qBAAK,OAAO;AACZ,qBAAK,OAAO;AACZ,qBAAK,SAAS;AAAA;AAEhB,gCAAkB,MAAM,MAAM,OAAO;AACnC,oBAAI,OAAO,KAAK;AAChB,oBAAI,UAAU;AAAA,kBACZ;AAAA,kBACA,MAAM;AAAA,kBACN;AAAA;AAEF,qBAAK,OAAO;AACZ,qBAAK,OAAO;AACZ,qBAAK;AACL,uBAAO;AAAA;AAET,mCAAqB,MAAM,MAAM,OAAO;AACtC,oBAAI,OAAO,KAAK;AAChB,yBAAS,IAAI,GAAG,IAAI,SAAS,SAAS,KAAK,MAAM,KAAK;AACpD,yBAAO,KAAK;AAAA;AAEd,qBAAK,OAAO;AACZ,qBAAK,OAAO;AACZ,qBAAK,UAAU;AAAA;AAEjB,+BAAiB,MAAM;AACrB,oBAAI,QAAQ;AACZ,oBAAI,OAAO,KAAK,KAAK;AACrB,uBAAO,SAAS,KAAK,MAAM;AACzB,wBAAM,KAAK,KAAK;AAChB,yBAAO,KAAK;AAAA;AAEd,uBAAO;AAAA;AAET,kBAAI,CAAC,OAAM,UAAU;AACnB,oBAAI,CAAC,OAAM,kBAAkB;AAC3B,yBAAO;AAAA;AAET,oBAAI,CAAC,EAAE,6BAA6B;AAClC,yBAAM,iBAAiB,WAAW,SAAU,KAAK;AAC/C,wBAAI,UAAU,KAAK,MAAM,IAAI;AAC7B,wBAAI,QAAO,QAAQ;AACnB,wBAAI,OAAO,QAAQ;AACnB,wBAAI,iBAAiB,QAAQ;AAC7B,2BAAM,YAAY,EAAE,UAAU,MAAM,EAAE,UAAU,QAAO;AACvD,wBAAI,gBAAgB;AAClB,6BAAM;AAAA;AAAA,qBAEP;AAAA;AAEL,uBAAO;AAAA;AAET,kBAAI,SAAS,EAAE,KAAK;AACpB,kBAAI,QAAQ;AACV,kBAAE,WAAW,OAAO;AACpB,oBAAI,OAAO,aAAa,gBAAgB;AACtC,oBAAE,SAAS;AAAA;AAAA;AAGf,wDAA0C;AACxC,oBAAI,CAAC,EAAE,QAAQ;AACb,oBAAE;AAAA;AAAA;AAGN,kBAAI,CAAC,EAAE,QAAQ;AACb,oBAAI,aAAa,SAAS;AAC1B,oBAAI,eAAe,aAAa,eAAe,iBAAiB,UAAU,OAAO,OAAO;AACtF,2BAAS,iBAAiB,oBAAoB;AAAA,uBACzC;AACL,sBAAI,OAAO,uBAAuB;AAChC,2BAAO,sBAAsB;AAAA,yBACxB;AACL,2BAAO,WAAW,gCAAgC;AAAA;AAAA;AAAA;AAIxD,qBAAO;AAAA,cACP;AACF,gBAAI,QAAO,SAAS;AAClB,sBAAO,UAAU;AAAA;AAEnB,gBAAI,OAAO,mBAAmB,aAAa;AACzC,6BAAe,QAAQ;AAAA;AAAA,aAEzB;AACF,gBAAM,UAAU,QAAQ;AAAA,YACtB,WAAW;AAAA,cACT;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA;AAAA,cAEV;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA;AAAA;AAAA,YAGZ,UAAU;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA,YAEV,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,YAE3B,WAAW;AAAA,YACX,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,eAAe;AAAA;AAEjB,UAAC,UAAU,QAAO;AAChB,oCAAwB,UAAU,OAAO;AACvC,qBAAO,QAAQ,SAAS,gBAAgB,QAAQ;AAAA;AAElD,mBAAO,iBAAiB,OAAM,UAAU,uBAAuB,IAAI;AAAA,cACjE,mBAAmB;AAAA,gBACjB,OAAO,SAAU,KAAK,UAAU,oBAAoB,eAAe;AACjE,sBAAI,IAAI,aAAa,UAAU;AAC7B;AAAA;AAEF,sBAAI,aAAa,IAAI,aAAa;AAClC,sBAAI,OAAO,IAAI,KAAK,QAAQ,oBAAoB,SAAU,OAAO;AAC/D,wBAAI,OAAO,kBAAkB,cAAc,CAAC,cAAc,QAAQ;AAChE,6BAAO;AAAA;AAET,wBAAI,IAAI,WAAW;AACnB,wBAAI;AACJ,2BAAO,IAAI,KAAK,QAAQ,cAAc,eAAe,UAAU,QAAQ,IAAI;AACzE,wBAAE;AAAA;AAEJ,+BAAW,KAAK;AAChB,2BAAO;AAAA;AAET,sBAAI,UAAU,OAAM,UAAU;AAAA;AAAA;AAAA,cAGlC,sBAAsB;AAAA,gBACpB,OAAO,SAAU,KAAK,UAAU;AAC9B,sBAAI,IAAI,aAAa,YAAY,CAAC,IAAI,YAAY;AAChD;AAAA;AAEF,sBAAI,UAAU,OAAM,UAAU;AAC9B,sBAAI,IAAI;AACR,sBAAI,OAAO,OAAO,KAAK,IAAI;AAC3B,sCAAoB,QAAQ;AAC1B,6BAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,0BAAI,KAAK,KAAK,QAAQ;AACpB;AAAA;AAEF,0BAAI,QAAQ,OAAO;AACnB,0BAAI,OAAO,UAAU,YAAY,MAAM,WAAW,OAAO,MAAM,YAAY,UAAU;AACnF,4BAAI,IAAI,KAAK;AACb,4BAAI,IAAI,IAAI,WAAW;AACvB,4BAAI,IAAI,OAAO,UAAU,WAAW,QAAQ,MAAM;AAClD,4BAAI,cAAc,eAAe,UAAU;AAC3C,4BAAI,QAAQ,EAAE,QAAQ;AACtB,4BAAI,QAAQ,IAAI;AACd,4BAAE;AACF,8BAAI,SAAS,EAAE,UAAU,GAAG;AAC5B,8BAAI,SAAS,IAAI,OAAM,MAAM,UAAU,OAAM,SAAS,GAAG,IAAI,UAAU,cAAc,UAAU;AAC/F,8BAAI,QAAQ,EAAE,UAAU,QAAQ,YAAY;AAC5C,8BAAI,cAAc;AAClB,8BAAI,QAAQ;AACV,wCAAY,KAAK,MAAM,aAAa,WAAW,CAAC;AAAA;AAElD,sCAAY,KAAK;AACjB,8BAAI,OAAO;AACT,wCAAY,KAAK,MAAM,aAAa,WAAW,CAAC;AAAA;AAElD,8BAAI,OAAO,UAAU,UAAU;AAC7B,mCAAO,OAAO,MAAM,QAAQ;AAAA,8BAC1B;AAAA,8BACA;AAAA,8BACA,OAAO;AAAA,iCACJ;AACL,kCAAM,UAAU;AAAA;AAAA;AAAA,iCAGX,MAAM,SAAS;AACxB,mCAAW,MAAM;AAAA;AAAA;AAGrB,2BAAO;AAAA;AAET,6BAAW,IAAI;AAAA;AAAA;AAAA;AAAA,aAIrB;AACF,gBAAM,UAAU,IAAI,MAAM,UAAU,OAAO,SAAS;AAAA,YAClD,WAAW;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA,YAEV,UAAU;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA,YAEV,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA;AAAA,YAEd,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,YAAY;AAAA;AAEd,gBAAM,UAAU,aAAa,KAAK,UAAU;AAAA,YAC1C,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA;AAGZ,gBAAM,UAAU,aAAa,KAAK,UAAU;AAAA,YAC1C,SAAS;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,UAAU;AAAA,kBACR;AAAA,oBACE,SAAS;AAAA,oBACT,YAAY;AAAA;AAAA,kBAEd,MAAM,UAAU,EAAE;AAAA;AAAA,gBAEpB,QAAQ,MAAM,UAAU,EAAE;AAAA,gBAC1B,WAAW,MAAM,UAAU,EAAE;AAAA,gBAC7B,cAAc;AAAA,kBACZ;AAAA,oBACE,SAAS;AAAA,oBACT,YAAY;AAAA;AAAA,kBAEd;AAAA,oBACE,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,OAAO;AAAA;AAAA;AAAA,gBAGX,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA;AAAA,gBAET,kBAAkB;AAAA,gBAClB,eAAe;AAAA,gBACf,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,QAAQ,MAAM,UAAU;AAAA;AAAA;AAAA;AAAA;AAKhC,gBAAM,UAAU,aAAa,KAAK,YAAY,EAAE,YAAY;AAC5D,iBAAO,MAAM,UAAU,EAAE;AACzB,UAAC,UAAU,QAAO;AAChB,gBAAI,UAAU;AACd,gBAAI,UAAU,uCAAuC,OAAO,QAAQ,cAAc,WAAY;AAC5F,qBAAO,QAAQ;AAAA;AAEjB,mBAAM,UAAU,MAAM,OAAM,UAAU,OAAO,KAAK;AAAA,cAChD,cAAc;AAAA,gBACZ;AAAA,kBACE,SAAS,OAAO,gEAAgE,OAAO,QAAQ,cAAc,WAAY;AACvH,2BAAO,QAAQ;AAAA;AAAA,kBAEjB,YAAY;AAAA;AAAA,gBAEd;AAAA,gBACA;AAAA,gBACA;AAAA;AAAA,cAEF,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,SAAS;AAAA,gBACT,QAAQ;AAAA;AAAA,cAEV,YAAY;AAAA,cACZ,WAAW;AAAA;AAEb,mBAAM,UAAU,aAAa,OAAO,UAAU;AAAA,cAC5C,UAAU;AAAA,gBACR,SAAS,OAAO,2BAA2B,SAAS,QAAQ,mDAAmD,SAAS,MAAM,kDAAkD,OAAO,QAAQ,eAAe,WAAY;AACxN,yBAAO;AAAA,qBACJ;AAAA,gBACL,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,UAAU;AAAA,kBACV,YAAY;AAAA,kBACZ,eAAe;AAAA;AAAA;AAAA,cAGnB,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA;AAAA;AAGZ,mBAAM,UAAU,aAAa,OAAO,WAAW;AAAA,cAC7C,oBAAoB;AAAA,gBAClB,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,YAAY;AAAA,kBACZ,WAAW;AAAA,oBACT,SAAS;AAAA,oBACT,OAAO;AAAA,oBACP,QAAQ,OAAM,UAAU;AAAA;AAAA;AAAA;AAAA;AAKhC,mBAAM,UAAU,aAAa,OAAO,YAAY;AAAA,cAC9C,gBAAgB;AAAA,gBACd,SAAS;AAAA,gBACT,OAAO;AAAA;AAAA;AAGX,mBAAM,UAAU,aAAa,OAAO,cAAc;AAAA,cAChD,eAAe;AAAA,gBACb,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ,OAAM,UAAU,OAAO,OAAO;AAAA;AAAA;AAG1C,mBAAM,UAAU,aAAa,UAAU,gBAAgB,EAAE,cAAc,6BAA6B,OAAM,UAAU,IAAI;AAAA,aACxH;AACF,UAAC,UAAU,QAAO;AAChB,6BAAiB,SAAS,cAAc;AACtC,qBAAO,QAAQ,QAAQ,cAAc,SAAU,GAAG,OAAO;AACvD,uBAAO,QAAQ,aAAa,CAAC,SAAS;AAAA;AAAA;AAG1C,wBAAY,SAAS,cAAc,OAAO;AACxC,qBAAO,OAAO,QAAQ,SAAS,eAAe,SAAS;AAAA;AAEzD,4BAAgB,SAAS,WAAW;AAClC,uBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,0BAAU,QAAQ,QAAQ,aAAa,WAAY;AACjD,yBAAO,QAAQ,UAAU;AAAA;AAAA;AAG7B,qBAAO,QAAQ,QAAQ,aAAa;AAAA;AAEtC,gBAAI,eAAe;AAAA,cACjB,MAAM;AAAA,cACN,iBAAiB;AAAA,cACjB,YAAY;AAAA,cACZ,OAAO;AAAA;AAET,uCAA2B,OAAO;AAChC,qBAAO,WAAW,MAAM,OAAO,QAAQ,MAAM,OAAO;AAAA;AAEtD,gBAAI,0BAA0B,kBAAkB,aAAa;AAC7D,gBAAI,WAAW,OAAO,kBAAkB,aAAa,OAAO,MAAM,aAAa,kBAAkB,MAAM,aAAa,aAAa,MAAM,aAAa;AACpJ,gBAAI,kBAAkB,kBAAkB,aAAa,kBAAkB,MAAM,aAAa,aAAa,MAAM,aAAa;AAC1H,gBAAI,wBAAwB,kBAAkB,aAAa,OAAO,MAAM,aAAa,kBAAkB,MAAM,aAAa;AAC1H,gBAAI,UAAU,OAAO,mCAAmC,QAAQ;AAChE,gBAAI,cAAc,OAAO,0BAA0B,QAAQ;AAC3D,gBAAI,OAAO,qBAAqB;AAChC,gBAAI,cAAc,QAAQ,qBAAqB,QAAQ;AAAA,cACrD;AAAA,cACA;AAAA;AAEF,gBAAI,aAAa,QAAQ,mCAAmC,QAAQ;AAAA,cAClE;AAAA,cACA;AAAA;AAEF,gBAAI,QAAQ,mBAAmB;AAC/B,gBAAI,6BAA6B,QAAQ,yCAAyC,QAAQ;AAAA,cACxF;AAAA,cACA;AAAA;AAEF,gBAAI,eAAe,QAAQ,2CAA2C,QAAQ;AAAA,cAC5E;AAAA,cACA;AAAA,cACA;AAAA;AAEF,gBAAI,QAAQ,QAAQ,yBAAyB,QAAQ,CAAC;AACtD,gBAAI,iBAAiB,QAAQ,mDAAmD,QAAQ;AAAA,cACtF;AAAA,cACA;AAAA,cACA;AAAA;AAEF,gBAAI,aAAa;AAAA,cACf,WAAW;AAAA,cACX,eAAe;AAAA;AAEjB,gBAAI,YAAY,8CAA8C;AAC9D,gBAAI,gBAAgB,wBAAwB;AAC5C,gBAAI,iBAAiB,kCAAkC;AACvD,mBAAM,UAAU,SAAS,OAAM,UAAU,OAAO,SAAS;AAAA,cACvD,UAAU;AAAA,gBACR;AAAA,kBACE,SAAS,GAAG,kBAAkB,QAAQ,CAAC;AAAA,kBACvC,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS,GAAG,mBAAmB,QAAQ,CAAC;AAAA,kBACxC,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA;AAAA,cAGZ,cAAc;AAAA,gBACZ;AAAA,kBACE,SAAS,GAAG,qCAAqC,QAAQ,CAAC;AAAA,kBAC1D,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS,GAAG,wCAAwC,QAAQ;AAAA,oBAC1D;AAAA,oBACA;AAAA;AAAA,kBAEF,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS,GAAG,4BAA4B,QAAQ,CAAC;AAAA,kBACjD,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS,GAAG,oBAAoB,QAAQ;AAAA,oBACtC;AAAA,oBACA;AAAA;AAAA,kBAEF,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS,GAAG,yBAAyB,QAAQ,CAAC;AAAA,kBAC9C,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS,GAAG,oBAAoB,QAAQ,CAAC;AAAA,kBACzC,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS,GAAG,mCAAmC,QAAQ,CAAC;AAAA,kBACxD,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS,GAAG,2EAA2E,QAAQ;AAAA,oBAC7F;AAAA,oBACA;AAAA,oBACA;AAAA;AAAA,kBAEF,QAAQ;AAAA;AAAA;AAAA,cAGZ,WAAW;AAAA,cACX,UAAU;AAAA,cACV,YAAY;AAAA,cACZ,eAAe;AAAA;AAEjB,mBAAM,UAAU,aAAa,UAAU,UAAU;AAAA,cAC/C,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,OAAO;AAAA;AAAA;AAGX,mBAAM,UAAU,aAAa,UAAU,eAAe;AAAA,cACpD,mBAAmB;AAAA,gBACjB,SAAS,GAAG,yBAAyB,QAAQ,CAAC;AAAA,gBAC9C,YAAY;AAAA,gBACZ,OAAO;AAAA;AAAA;AAGX,mBAAM,UAAU,aAAa,UAAU,cAAc;AAAA,cACnD,aAAa;AAAA,gBACX,SAAS,GAAG,+DAA+D,QAAQ,CAAC;AAAA,gBACpF,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,cAE3B,mBAAmB;AAAA,gBACjB,SAAS,GAAG,kFAAkF,QAAQ,CAAC;AAAA,gBACvG,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQ;AAAA;AAAA,cAEV,eAAe;AAAA,gBACb,SAAS,GAAG,+DAA+D,QAAQ;AAAA,kBACjF;AAAA,kBACA;AAAA;AAAA,gBAEF,QAAQ;AAAA,gBACR,OAAO;AAAA;AAAA,cAET,0BAA0B;AAAA,gBACxB,SAAS,GAAG,8BAA8B,QAAQ,CAAC;AAAA,gBACnD,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,OAAO;AAAA;AAAA,cAET,kBAAkB;AAAA,gBAChB,SAAS,GAAG,yBAAyB,QAAQ;AAAA,kBAC3C;AAAA,kBACA;AAAA;AAAA,gBAEF,QAAQ;AAAA,kBACN,YAAY,GAAG,SAAS,QAAQ,CAAC;AAAA,kBACjC,WAAW;AAAA,oBACT,SAAS,OAAO;AAAA,oBAChB,OAAO;AAAA,oBACP,QAAQ;AAAA;AAAA;AAAA;AAAA,cAId,aAAa;AAAA,gBACX,SAAS,GAAG,kKAAkK,QAAQ;AAAA,kBACpL;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,SAAS;AAAA,kBACT;AAAA,kBACA,kBAAkB;AAAA;AAAA,gBAEpB,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,oBAAoB;AAAA,oBAClB,SAAS,GAAG,+BAA+B,QAAQ;AAAA,sBACjD;AAAA,sBACA;AAAA;AAAA,oBAEF,YAAY;AAAA,oBACZ,QAAQ;AAAA,oBACR,QAAQ,OAAM,UAAU;AAAA;AAAA,kBAE1B,WAAW;AAAA,kBACX,cAAc;AAAA,oBACZ,SAAS,OAAO;AAAA,oBAChB,QAAQ;AAAA,oBACR,QAAQ;AAAA;AAAA,kBAEV,eAAe;AAAA;AAAA;AAAA,cAGnB,gBAAgB;AAAA,gBACd,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,aAAa;AAAA,oBACX,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,OAAO;AAAA;AAAA;AAAA;AAAA;AAKf,gBAAI,2BAA2B,gBAAgB,MAAM;AACrD,gBAAI,kCAAkC,QAAQ,iEAAiE,QAAQ,CAAC;AACxH,gBAAI,kBAAkB,OAAO,QAAQ,+BAA+B,QAAQ,CAAC,mCAAmC;AAChH,gBAAI,aAAa,wEAAwE;AACzF,gBAAI,OAAO,QAAQ,0BAA0B,QAAQ;AAAA,cACnD;AAAA,cACA;AAAA;AAEF,mBAAM,UAAU,aAAa,UAAU,cAAc;AAAA,cACnD,aAAa;AAAA,gBACX,SAAS,GAAG,6EAA6E,QAAQ;AAAA,kBAC/F;AAAA,kBACA;AAAA;AAAA,gBAEF,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,UAAU;AAAA,oBACR,SAAS,GAAG,iBAAiB,QAAQ,CAAC;AAAA,oBACtC,OAAO;AAAA;AAAA,kBAET,uBAAuB;AAAA,oBACrB,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,oBAClC,QAAQ,OAAM,UAAU;AAAA;AAAA,kBAE1B,cAAc;AAAA,oBACZ,SAAS,OAAO;AAAA,oBAChB,QAAQ,EAAE,eAAe;AAAA;AAAA,kBAE3B,eAAe;AAAA;AAAA;AAAA;AAIrB,gBAAI,eAAe,aAAa;AAChC,gBAAI,sBAAsB,OAAO,QAAQ,+BAA+B,QAAQ,CAAC,mCAAmC;AACpH,gBAAI,iBAAiB,QAAQ,qCAAqC,QAAQ;AAAA,cACxE;AAAA,cACA;AAAA;AAEF,gBAAI,sBAAsB,OAAO,QAAQ,mEAAmE,QAAQ,CAAC,4BAA4B;AACjJ,gBAAI,iBAAiB,QAAQ,qCAAqC,QAAQ;AAAA,cACxE;AAAA,cACA;AAAA;AAEF,+CAAmC,eAAe,oBAAoB;AACpE,qBAAO;AAAA,gBACL,iBAAiB;AAAA,kBACf,SAAS,GAAG,6BAA6B,QAAQ,CAAC;AAAA,kBAClD,YAAY;AAAA,kBACZ,QAAQ;AAAA,oBACN,iBAAiB;AAAA,sBACf,SAAS,GAAG,sCAAsC,QAAQ;AAAA,wBACxD;AAAA,wBACA;AAAA;AAAA,sBAEF,YAAY;AAAA,sBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,oBAE3B,eAAe;AAAA,oBACf,cAAc;AAAA,sBACZ,SAAS;AAAA,sBACT,OAAO;AAAA,sBACP,QAAQ,OAAM,UAAU;AAAA;AAAA;AAAA;AAAA,gBAI9B,UAAU;AAAA;AAAA;AAGd,mBAAM,UAAU,aAAa,UAAU,UAAU;AAAA,cAC/C,wBAAwB;AAAA,gBACtB;AAAA,kBACE,SAAS,GAAG,4DAA4D,QAAQ,CAAC;AAAA,kBACjF,YAAY;AAAA,kBACZ,QAAQ;AAAA,kBACR,QAAQ,0BAA0B,gBAAgB;AAAA;AAAA,gBAEpD;AAAA,kBACE,SAAS,GAAG,4CAA4C,QAAQ,CAAC;AAAA,kBACjE,YAAY;AAAA,kBACZ,QAAQ;AAAA,kBACR,QAAQ,0BAA0B,gBAAgB;AAAA;AAAA;AAAA,cAGtD,QAAQ;AAAA,gBACN,SAAS,OAAO;AAAA,gBAChB,QAAQ;AAAA;AAAA;AAGZ,mBAAM,UAAU,SAAS,OAAM,UAAU,KAAK,OAAM,UAAU;AAAA,aAC9D;AACF,UAAC,UAAU,QAAO;AAChB,gBAAI,SAAS;AACb,mBAAM,UAAU,MAAM;AAAA,cACpB,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,SAAS,OAAO,eAAe,sBAAsB,SAAS,MAAM,OAAO,SAAS,QAAQ,kBAAkB;AAAA,gBAC9G,QAAQ;AAAA,kBACN,QAAQ;AAAA,kBACR,8BAA8B;AAAA,oBAC5B,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,OAAO;AAAA;AAAA,kBAET,WAAW;AAAA,oBACT,SAAS;AAAA,oBACT,YAAY;AAAA;AAAA;AAAA;AAAA,cAIlB,OAAO;AAAA,gBACL,SAAS,OAAO,iBAAiB,OAAO,SAAS,MAAM,8BAA8B,SAAS,QAAQ;AAAA,gBACtG,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,YAAY;AAAA,kBACZ,eAAe;AAAA,kBACf,UAAU;AAAA,oBACR,SAAS,OAAO,MAAM,OAAO,SAAS;AAAA,oBACtC,OAAO;AAAA;AAAA;AAAA;AAAA,cAIb,YAAY;AAAA,gBACV,SAAS,OAAO,sDAAuD,OAAO,SAAS;AAAA,gBACvF,YAAY;AAAA;AAAA,cAEd,UAAU;AAAA,gBACR,SAAS;AAAA,gBACT,QAAQ;AAAA;AAAA,cAEV,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA,cAEd,aAAa;AAAA,cACb,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA,cAEd,eAAe;AAAA;AAEjB,mBAAM,UAAU,IAAI,UAAU,OAAO,OAAO,OAAM,UAAU;AAC5D,gBAAI,SAAS,OAAM,UAAU;AAC7B,gBAAI,QAAQ;AACV,qBAAO,IAAI,WAAW,SAAS;AAC/B,qBAAO,IAAI,aAAa,SAAS;AAAA;AAAA,aAEnC;AACF,UAAC,UAAU,QAAO;AAChB,gBAAI,WAAW;AACf,gBAAI,kBAAkB,6CAA6C;AACnE,gBAAI,YAAY;AAAA,cACd,SAAS,OAAO,aAAa,SAAS,kBAAkB,gCAAgC;AAAA,cACxF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,QAAQ,EAAE,eAAe;AAAA;AAAA,gBAE3B,eAAe;AAAA;AAAA;AAGnB,mBAAM,UAAU,OAAO,OAAM,UAAU,OAAO,SAAS;AAAA,cACrD,UAAU;AAAA,gBACR,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA;AAAA,cAEV,cAAc;AAAA,gBACZ;AAAA,gBACA;AAAA,kBACE,SAAS,OAAO,aAAa,SAAS,kBAAkB,+DAA+D;AAAA,kBACvH,YAAY;AAAA,kBACZ,QAAQ,UAAU;AAAA;AAAA,gBAEpB;AAAA,kBACE,SAAS,OAAO,kFAAkF,SAAS,kBAAkB,aAAa;AAAA,kBAC1I,YAAY;AAAA,kBACZ,QAAQ,UAAU;AAAA;AAAA;AAAA,cAGtB,WAAW;AAAA,cACX,YAAY;AAAA,gBACV,OAAM,UAAU,MAAM;AAAA,gBACtB;AAAA,kBACE,SAAS;AAAA,kBACT,YAAY;AAAA;AAAA;AAAA,cAGhB,UAAU;AAAA,cACV,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA,cAEd,YAAY;AAAA;AAEd,mBAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,cAC7C,wBAAwB;AAAA,gBACtB,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,OAAO;AAAA;AAAA,cAET,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,QAAQ;AAAA;AAAA;AAGZ,mBAAM,UAAU,aAAa,QAAQ,cAAc;AAAA,cACjD,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA;AAAA,cAET,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,cAAc;AAAA,kBACd,WAAW;AAAA,kBACX,eAAe;AAAA,kBACf,YAAY;AAAA;AAAA;AAAA,cAGhB,UAAU;AAAA,gBACR;AAAA,kBACE,SAAS,OAAO,gBAAgB,SAAS,kBAAkB,0BAA0B;AAAA,kBACrF,YAAY;AAAA,kBACZ,QAAQ;AAAA,oBACN,aAAa,UAAU,OAAO;AAAA,oBAC9B,eAAe;AAAA,oBACf,YAAY;AAAA,oBACZ,cAAc;AAAA;AAAA;AAAA,gBAGlB;AAAA,kBACE,SAAS,OAAO,yBAAyB,SAAS,kBAAkB,qBAAqB;AAAA,kBACzF,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,QAAQ;AAAA,oBACN,aAAa,UAAU,OAAO;AAAA,oBAC9B,UAAU;AAAA,oBACV,eAAe;AAAA,oBACf,YAAY;AAAA,oBACZ,cAAc;AAAA;AAAA;AAAA;AAAA,cAIpB,aAAa;AAAA,gBACX,SAAS,OAAO,qJAAqJ,OAAO,QAAQ,cAAc,WAAY;AAC5M,yBAAO,SAAS;AAAA;AAAA,gBAElB,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA;AAAA,aAG7B;AACF,gBAAM,UAAU,aAAa,MAAM,UAAU,OAAO,SAAS;AAAA,YAC3D,cAAc;AAAA,cACZ,MAAM,UAAU,MAAM;AAAA,cACtB;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA;AAAA,YAGhB,WAAW;AAAA,cACT;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA,cAEd;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA;AAAA,YAGhB,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,SAAS,OAAO,aAAa,SAAS,QAAS,gBAAe,SAAS,MAAM,0BAA0B,SAAS,MAAM,4BAA4B,SAAS,MAAM,sCAAsC,SAAS,MAAM,gBAAgB,SAAS,MAAM,oFAAoF,UAAU,MAAM,YAAY;AAAA,cACrW,YAAY;AAAA;AAAA,YAEd,YAAY;AAAA;AAEd,gBAAM,UAAU,WAAW,cAAc,GAAG,UAAU;AACtD,gBAAM,UAAU,aAAa,cAAc,WAAW;AAAA,YACpD,SAAS;AAAA,cACP,SAAS,OAAO,0DAA0D,SAAS,KAAK,SAAS,QAAQ,iEAAiE,SAAS,MAAM,qIAAqI,SAAS,MAAM,kEAAkE;AAAA,cAC/Y,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,gBAAgB;AAAA,kBACd,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,QAAQ,MAAM,UAAU;AAAA;AAAA,gBAE1B,mBAAmB;AAAA,gBACnB,eAAe;AAAA;AAAA;AAAA,YAGnB,qBAAqB;AAAA,cACnB,SAAS;AAAA,cACT,OAAO;AAAA;AAAA,YAET,aAAa;AAAA,cACX;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,MAAM,UAAU;AAAA;AAAA,cAE1B;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,MAAM,UAAU;AAAA;AAAA,cAE1B;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,MAAM,UAAU;AAAA;AAAA,cAE1B;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,MAAM,UAAU;AAAA;AAAA;AAAA,YAG5B,YAAY;AAAA;AAEd,gBAAM,UAAU,aAAa,cAAc,UAAU;AAAA,YACnD,YAAY;AAAA,cACV,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA;AAAA,YAET,mBAAmB;AAAA,cACjB,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,wBAAwB;AAAA,kBACtB,SAAS;AAAA,kBACT,OAAO;AAAA;AAAA,gBAET,iBAAiB;AAAA,kBACf,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA,oBACN,6BAA6B;AAAA,sBAC3B,SAAS;AAAA,sBACT,OAAO;AAAA;AAAA,oBAET,MAAM,MAAM,UAAU;AAAA;AAAA;AAAA,gBAG1B,UAAU;AAAA;AAAA;AAAA,YAGd,mBAAmB;AAAA,cACjB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA;AAAA;AAGX,gBAAM,UAAU,aAAa,cAAc,YAAY;AAAA,YACrD,oBAAoB;AAAA,cAClB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA;AAAA;AAGX,cAAI,MAAM,UAAU,QAAQ;AAC1B,kBAAM,UAAU,OAAO,IAAI,WAAW,UAAU;AAChD,kBAAM,UAAU,OAAO,IAAI,aAAa,yNAAyN,QAAQ;AAAA;AAE3Q,gBAAM,UAAU,KAAK,MAAM,UAAU;AACrC,gBAAM,UAAU,SAAS;AAAA,YACvB,WAAW;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA,YAEV,UAAU;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA,YAEV,WAAW;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,mBAAmB;AAAA,kBACjB,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA,kBACR,QAAQ;AAAA;AAAA,gBAEV,UAAU;AAAA,kBACR,SAAS;AAAA,kBACT,QAAQ;AAAA;AAAA,gBAEV,eAAe;AAAA,gBACf,eAAe;AAAA,gBACf,QAAQ;AAAA;AAAA;AAAA,YAGZ,SAAS;AAAA,cACP,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA,YAEV,OAAO;AAAA,cACL,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,eAAe;AAAA,oBACf,aAAa;AAAA;AAAA;AAAA,gBAGjB,gBAAgB;AAAA,gBAChB,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,eAAe;AAAA,sBACb;AAAA,wBACE,SAAS;AAAA,wBACT,OAAO;AAAA;AAAA,sBAET;AAAA,wBACE,SAAS;AAAA,wBACT,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKpB,eAAe;AAAA,gBACf,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,QAAQ,EAAE,aAAa;AAAA;AAAA;AAAA;AAAA,YAI7B,UAAU;AAAA,cACR;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA;AAAA,cAET;AAAA;AAAA;AAGJ,gBAAM,UAAU,OAAO,OAAO,OAAO,cAAc,OAAO,YAAY,MAAM,UAAU,OAAO;AAC7F,gBAAM,UAAU,OAAO,WAAW,OAAO,mBAAmB,SAAS,MAAM,UAAU;AACrF,gBAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,gBAAI,IAAI,SAAS,UAAU;AACzB,kBAAI,WAAW,WAAW,IAAI,QAAQ,QAAQ,SAAS;AAAA;AAAA;AAG3D,iBAAO,eAAe,MAAM,UAAU,OAAO,KAAK,cAAc;AAAA,YAC9D,OAAO,oBAAoB,SAAS,MAAM;AACxC,kBAAI,sBAAsB;AAC1B,kCAAoB,cAAc,QAAQ;AAAA,gBACxC,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,MAAM,UAAU;AAAA;AAE1B,kCAAoB,WAAW;AAC/B,kBAAI,SAAS;AAAA,gBACX,kBAAkB;AAAA,kBAChB,SAAS;AAAA,kBACT,QAAQ;AAAA;AAAA;AAGZ,qBAAO,cAAc,QAAQ;AAAA,gBAC3B,SAAS;AAAA,gBACT,QAAQ,MAAM,UAAU;AAAA;AAE1B,kBAAI,MAAM;AACV,kBAAI,WAAW;AAAA,gBACb,SAAS,OAAO,wFAAwF,OAAO,QAAQ,OAAO,WAAY;AACxI,yBAAO;AAAA,oBACL;AAAA,gBACJ,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR;AAAA;AAEF,oBAAM,UAAU,aAAa,UAAU,SAAS;AAAA;AAAA;AAGpD,iBAAO,eAAe,MAAM,UAAU,OAAO,KAAK,gBAAgB;AAAA,YAChE,OAAO,SAAU,UAAU,MAAM;AAC/B,oBAAM,UAAU,OAAO,IAAI,OAAO,gBAAgB,KAAK;AAAA,gBACrD,SAAS,OAAO,aAAa,SAAS,QAAQ,WAAW,MAAM,iDAAiD,QAAQ;AAAA,gBACxH,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,aAAa;AAAA,kBACb,cAAc;AAAA,oBACZ,SAAS;AAAA,oBACT,QAAQ;AAAA,sBACN,SAAS;AAAA,wBACP,SAAS;AAAA,wBACT,YAAY;AAAA,wBACZ,OAAO;AAAA,0BACL;AAAA,0BACA,cAAc;AAAA;AAAA,wBAEhB,QAAQ,MAAM,UAAU;AAAA;AAAA,sBAE1B,eAAe;AAAA,wBACb;AAAA,0BACE,SAAS;AAAA,0BACT,OAAO;AAAA;AAAA,wBAET;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQd,gBAAM,UAAU,OAAO,MAAM,UAAU;AACvC,gBAAM,UAAU,SAAS,MAAM,UAAU;AACzC,gBAAM,UAAU,MAAM,MAAM,UAAU;AACtC,gBAAM,UAAU,MAAM,MAAM,UAAU,OAAO,UAAU;AACvD,gBAAM,UAAU,OAAO,MAAM,UAAU;AACvC,gBAAM,UAAU,OAAO,MAAM,UAAU;AACvC,gBAAM,UAAU,MAAM,MAAM,UAAU;AACtC,UAAC,UAAU,QAAO;AAChB,gBAAI,UAAU;AACd,gBAAI,YAAW;AAAA,cACb;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA;AAAA,cAET;AAAA,gBACE,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,YAAY;AAAA;AAAA,cAEd;AAAA,gBACE,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,YAAY;AAAA;AAAA,cAEd;AAAA,cACA;AAAA;AAEF,gBAAI,SAAS;AACb,gBAAI,WAAW;AACf,gBAAI,cAAc;AAClB,mBAAM,UAAU,MAAM;AAAA,cACpB,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,OAAO;AAAA;AAAA,cAET,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,cAE3B,yBAAyB;AAAA,gBACvB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA;AAAA,cAET,uBAAuB;AAAA,gBACrB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA;AAAA,cAET,WAAW;AAAA,gBACT;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS;AAAA,kBACT,YAAY;AAAA;AAAA,gBAEd;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,YAAY;AAAA;AAAA;AAAA,cAGhB,iBAAiB;AAAA,gBACf,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA,cAEd,cAAc;AAAA,gBACZ;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,YAAY;AAAA,kBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,gBAE3B;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,QAAQ,EAAE,eAAe;AAAA;AAAA,gBAE3B;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,YAAY;AAAA,kBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,gBAE3B;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL;AAAA,oBACA;AAAA;AAAA,kBAEF,QAAQ;AAAA,kBACR,QAAQ,EAAE,eAAe;AAAA;AAAA,gBAE3B;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL;AAAA,oBACA;AAAA;AAAA,kBAEF,QAAQ;AAAA,kBACR,QAAQ,EAAE,eAAe;AAAA;AAAA,gBAE3B;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL;AAAA,oBACA;AAAA;AAAA,kBAEF,QAAQ;AAAA,kBACR,YAAY;AAAA,kBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,gBAE3B;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,YAAY;AAAA;AAAA,gBAEd;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL;AAAA,oBACA;AAAA;AAAA,kBAEF,QAAQ;AAAA,kBACR,YAAY;AAAA,kBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA;AAAA,cAG7B,YAAY;AAAA,cACZ,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,cAE3B,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA,cAEd,UAAU;AAAA,cACV,YAAY;AAAA,cACZ,eAAe;AAAA;AAEjB,gBAAI,uBAAuB;AAAA,cACzB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,OAAM,UAAU;AAAA;AAE1B,gBAAI,SAAS;AAAA,cACX;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,aAAa;AAAA,oBACX,SAAS;AAAA,oBACT,OAAO;AAAA,oBACP,QAAQ,EAAE,eAAe;AAAA;AAAA;AAAA;AAAA,cAI/B;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,aAAa;AAAA,oBACX,SAAS;AAAA,oBACT,OAAO;AAAA,oBACP,QAAQ,EAAE,eAAe;AAAA;AAAA,kBAE3B,iBAAiB;AAAA;AAAA;AAAA,cAGrB;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA;AAAA,cAEV;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA;AAAA,cAEV;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,QAAQ,EAAE,iBAAiB;AAAA;AAAA;AAG/B,mBAAM,UAAU,aAAa,OAAO,YAAY;AAAA,cAC9C,UAAU;AAAA,cACV,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,qBAAqB;AAAA,oBACnB,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,QAAQ;AAAA,sBACN,WAAW;AAAA,sBACX,UAAU;AAAA,sBACV,wBAAwB;AAAA,wBACtB;AAAA,0BACE,SAAS;AAAA,0BACT,OAAO;AAAA,0BACP,QAAQ;AAAA,0BACR,YAAY;AAAA;AAAA,wBAEd;AAAA,0BACE,SAAS;AAAA,0BACT,OAAO;AAAA,4BACL;AAAA,4BACA;AAAA;AAAA,0BAEF,QAAQ;AAAA,0BACR,YAAY;AAAA,0BACZ,QAAQ,EAAE,eAAe;AAAA;AAAA;AAAA,sBAG7B,YAAY;AAAA,sBACZ,UAAU;AAAA,sBACV,YAAY;AAAA,sBACZ,eAAe;AAAA;AAAA;AAAA,kBAGnB,aAAa;AAAA,oBACX,SAAS;AAAA,oBACT,OAAO;AAAA;AAAA;AAAA;AAAA;AAKf,mBAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,kBAAI,CAAC,MAAM,KAAK,IAAI,OAAO;AACzB;AAAA;AAEF,kBAAI,aAAa;AACjB,qBAAM,UAAU,qBAAqB,kBAAkB,KAAK,OAAO;AAAA;AAErE,mBAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,qBAAM,UAAU,qBAAqB,qBAAqB,KAAK;AAAA;AAAA,aAEjE;AACF,gBAAM,UAAU,SAAS;AAAA,YACvB,WAAW;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA;AAAA,YAEV,wBAAwB;AAAA,cACtB,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,iBAAiB;AAAA,kBACf,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA,oBACN,eAAe;AAAA,sBACb,SAAS;AAAA,sBACT,YAAY;AAAA;AAAA,oBAEd,qBAAqB;AAAA,sBACnB,SAAS;AAAA,sBACT,OAAO;AAAA;AAAA,oBAET,MAAM;AAAA;AAAA;AAAA,gBAGV,UAAU;AAAA;AAAA;AAAA,YAGd,wBAAwB;AAAA,cACtB,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA;AAAA,YAET,UAAU;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA,YAEV,YAAY;AAAA,cACV,SAAS;AAAA,cACT,YAAY;AAAA;AAAA,YAEd,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA;AAAA,YAEd,aAAa;AAAA,cACX,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,gBACL;AAAA,gBACA;AAAA;AAAA,cAEF,QAAQ,EAAE,eAAe;AAAA;AAAA,YAE3B,WAAW;AAAA,YACX,WAAW;AAAA,YACX,WAAW;AAAA,YACX,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,eAAe;AAAA;AAEjB,gBAAM,UAAU,OAAO,wBAAwB,OAAO,iBAAiB,OAAO,OAAO,MAAM,UAAU;AACrG,gBAAM,UAAU,KAAK,MAAM,UAAU;AACrC,UAAC,UAAU,QAAO;AAChB,mBAAM,UAAU,OAAO,OAAM,UAAU,OAAO,SAAS;AAAA,cACrD,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,QAAQ;AAAA;AAAA,cAEV,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe;AAAA;AAAA,cAE3B,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,eAAe;AAAA;AAEjB,mBAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,cAC/C,gBAAgB;AAAA,gBACd,SAAS;AAAA,gBACT,OAAO;AAAA;AAAA;AAGX,gBAAI,gBAAgB;AAAA,cAClB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ,OAAM,UAAU;AAAA;AAAA,gBAE1B,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,OAAO;AAAA;AAAA;AAAA;AAIb,mBAAO,OAAM,UAAU,KAAK;AAC5B,gBAAI,oBAAoB,QAAQ;AAAA,cAC9B,oDAAoD;AAAA,cACpD,sDAAsD;AAAA,cACtD,sDAAsD;AAAA,cACtD,0DAA0D;AAAA,cAC1D,kDAAkD;AAAA,cAClD,KAAK,OAAO;AACd,gBAAI,aAAa,sEAAsE;AACvF,mBAAM,UAAU,aAAa,QAAQ,WAAW;AAAA,cAC9C,iBAAiB;AAAA,gBACf;AAAA,kBACE,SAAS,OAAO,KAAK,SAAS,oBAAoB,mBAAmB;AAAA,kBACrE,QAAQ;AAAA,kBACR,QAAQ;AAAA,oBACN,iBAAiB;AAAA,oBACjB,SAAS;AAAA;AAAA;AAAA,gBAGb;AAAA,kBACE,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA,kBACR,QAAQ;AAAA,oBACN,iBAAiB;AAAA,oBACjB,SAAS;AAAA;AAAA;AAAA;AAAA,cAIf,YAAY;AAAA,cACZ,UAAU;AAAA,gBACR;AAAA,kBACE,SAAS,OAAO,YAAY,SAAS;AAAA,kBACrC,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA,gBAEV;AAAA,kBACE,SAAS,OAAO,oBAAoB,SAAS,aAAa,aAAa;AAAA,kBACvE,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA;AAAA,cAGZ,qBAAqB;AAAA,gBACnB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,YAAY;AAAA,kBACZ,WAAW;AAAA,kBACX,cAAc;AAAA,kBACd,eAAe;AAAA;AAAA;AAAA;AAIrB,mBAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,cAC7C,kBAAkB;AAAA,gBAChB;AAAA,kBACE,SAAS,OAAO,cAAc,SAAS;AAAA,kBACvC,QAAQ;AAAA,kBACR,QAAQ;AAAA,oBACN,iBAAiB;AAAA,oBACjB,UAAU;AAAA;AAAA;AAAA,gBAGd;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,QAAQ;AAAA,oBACN,iBAAiB;AAAA,oBACjB,UAAU;AAAA;AAAA;AAAA,gBAGd;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,QAAQ;AAAA,oBACN,aAAa;AAAA,sBACX,SAAS;AAAA,sBACT,QAAQ;AAAA,wBACN,UAAU;AAAA,wBACV,eAAe;AAAA;AAAA;AAAA,oBAGnB,iBAAiB;AAAA,oBACjB,UAAU;AAAA;AAAA;AAAA,gBAGd;AAAA,kBACE,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,QAAQ;AAAA,oBACN,aAAa;AAAA,sBACX,SAAS;AAAA,sBACT,QAAQ;AAAA,wBACN,UAAU;AAAA,wBACV,eAAe;AAAA;AAAA;AAAA,oBAGnB,UAAU;AAAA;AAAA;AAAA;AAAA,cAIhB,mBAAmB;AAAA,gBACjB;AAAA,kBACE,SAAS,OAAO,KAAK,SAAS;AAAA,kBAC9B,QAAQ;AAAA,kBACR,QAAQ;AAAA,oBACN,iBAAiB;AAAA,oBACjB,WAAW;AAAA,sBACT,SAAS;AAAA,sBACT,OAAO;AAAA;AAAA;AAAA;AAAA,gBAIb;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,QAAQ;AAAA,oBACN,iBAAiB;AAAA,oBACjB,WAAW;AAAA,sBACT,SAAS;AAAA,sBACT,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAMjB,mBAAO,OAAM,UAAU,KAAK;AAC5B,mBAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,cAC7C,WAAW;AAAA,cACX,YAAY;AAAA;AAEd,mBAAM,UAAU,KAAK,OAAM,UAAU;AAAA,aACrC;AACF,cAAI,WAAU,UAAU;AACxB,cAAI,UAAU,EAAE,YAAY;AAC5B,iBAAO;AAAA;AAET,YAAI,QAAQ,OAAO;AACnB,eAAO,QAAQ;AACf,eAAO;AAAA,SACP,QAAW,WAAW;AACxB,UAAI,UAAU,QAAO,QAAQ;AAE7B,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,SAAS,6BAA6B,OAAO;AAAA;AAG7D,UAAI,MAAM,SAAU,QAAQ;AAC1B,eAAO,OAAO,SAAS,iBAAiB,UAAU,OAAO,QAAQ;AAAA;AAGnE,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,YAAI,OAAO,OAAO,YAAY,OAAO,UAAU,YAAY;AAC3D,eAAO,OAAO,aAAa,OAAO;AAAA;AAEpC,UAAI,mBAAmB,SAAU,QAAQ,UAAU,MAAM;AACvD,eAAO,YAAY,SAAS,WAAY;AACtC,cAAI,OAAO,sBAAsB;AACjC,iBAAO,SAAS,IAAI,OAAO;AAC3B,iBAAO,KAAK,KAAK,WAAY;AAC3B,mBAAO,cAAc,qCAAqC,WAAW,OAAO,OAAO;AACnF,mBAAO,UAAU,OAAO,OAAO,EAAE,UAAU,WAAW,MAAM;AAAA,aAC3D,SAAU,GAAG;AACd,mBAAO,IAAI,UAAU,GAAG,SAAS,cAAc;AAC/C,cAAE,YAAY;AACd,gBAAI,QAAQ,iBAAiB;AAC7B,mBAAO,UAAU,OAAO;AAAA;AAAA;AAAA;AAI9B,UAAI,iBAAiB,SAAU,QAAQ;AACrC,YAAI,OAAO,sBAAsB;AACjC,eAAO,KAAK,KAAK,SAAS,KAAK,SAAU,GAAG;AAC1C,iBAAO,EAAE;AAAA;AAAA;AAIb,UAAI,eAAe,SAAU,QAAQ;AACnC,YAAI,mBAAmB;AAAA,UACrB;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,UAET;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA;AAAA;AAGX,YAAI,kBAAkB,eAAe;AACrC,eAAO,kBAAkB,kBAAkB;AAAA;AAE7C,UAAI,qBAAqB,SAAU,QAAQ,UAAU;AACnD,YAAI,OAAO,sBAAsB;AACjC,eAAO,KAAK,KAAK,WAAY;AAC3B,iBAAO;AAAA,WACN,SAAU,GAAG;AACd,cAAI,UAAU,EAAE,UAAU,MAAM;AAChC,iBAAO,UAAU,QAAQ,KAAK;AAAA;AAAA;AAIlC,UAAI,OAAO,SAAU,QAAQ;AAC3B,YAAI,YAAY,aAAa;AAC7B,YAAI,kBAAkB,KAAK,WAAW,KAAK,SAAS,KAAK,SAAU,GAAG;AACpE,iBAAO,EAAE;AAAA;AAEX,YAAI,kBAAkB,mBAAmB,QAAQ;AACjD,YAAI,cAAc,eAAe;AACjC,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,cACL;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,OAAO;AAAA;AAAA,cAET;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA;AAAA;AAAA;AAAA,UAIb,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA;AAAA,YAER;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,UAGb,aAAa;AAAA,YACX,UAAU;AAAA,YACV,MAAM;AAAA;AAAA,UAER,UAAU,SAAU,KAAK;AACvB,gBAAI,OAAO,IAAI;AACf,6BAAiB,QAAQ,KAAK,UAAU,KAAK;AAC7C,gBAAI;AAAA;AAAA;AAAA;AAKV,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,cAAc,WAAY;AAC1C,cAAI,OAAO,OAAO,UAAU;AAC5B,cAAI,OAAO,UAAU,iBAAiB,aAAa,OAAO;AACxD,iBAAK;AAAA,iBACA;AACL,mBAAO,UAAU,OAAO;AAAA;AAAA;AAAA;AAK9B,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI,IAAI,OAAO;AACf,eAAO,GAAG,cAAc,SAAU,GAAG;AACnC,YAAE,8BAA8B,EAAE,MAAM,OAAO,QAAQ,eAAe,KAAK,SAAU,KAAK,KAAK;AAC7F,gBAAI,OAAO,EAAE,MAAM,OAAO,IAAI;AAC9B,iBAAK,KAAK,SAAS,EAAE,KAAK,KAAK,KAAK;AACpC,iBAAK,WAAW;AAChB,iBAAK,QAAQ,OAAO,EAAE,iBAAiB,KAAK,WAAY;AACtD,mBAAK,cAAc;AAAA;AAAA;AAAA;AAIzB,eAAO,GAAG,cAAc,WAAY;AAClC,cAAI,yBAAyB,EAAE,OAAO,OAAO,QAAQ,eAAe,OAAO,SAAU,KAAK,KAAK;AAC7F,mBAAO,IAAI,oBAAoB;AAAA;AAEjC,cAAI,uBAAuB,QAAQ;AACjC,mBAAO,YAAY,SAAS,WAAY;AACtC,qCAAuB,KAAK,SAAU,KAAK,KAAK;AAC9C,kBAAE,KAAK,KAAK,MAAM,KAAK,SAAU,MAAK,MAAK;AACzC,uBAAI,WAAW,aAAa,OAAO,SAAS,eAAe,OAAO;AAAA;AAEpE,oBAAI,kBAAkB;AACtB,oBAAI,YAAY,OAAO,IAAI,OAAO,IAAI;AACtC,oBAAI,QAAQ,iBAAiB;AAC7B,oBAAI,YAAY,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAOrC,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,YAAI,OAAO,OAAO,UAAU;AAC5B,eAAO,OAAO,IAAI,GAAG,MAAM;AAAA;AAE7B,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,gBAAgB,cAAc;AAAA,UAC/C,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA,SAAS,SAAU,KAAK;AACtB,gBAAI,oBAAoB,WAAY;AAClC,kBAAI,UAAU,sBAAsB;AAAA;AAEtC,mBAAO,GAAG,cAAc;AACxB,mBAAO,WAAY;AACjB,qBAAO,OAAO,IAAI,cAAc;AAAA;AAAA;AAAA;AAItC,eAAO,GAAG,SAAS,YAAY,cAAc;AAAA,UAC3C,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA;AAAA;AAIJ,wBAAmB;AACjB,iBAAS,IAAI,cAAc,SAAU,QAAQ;AAC3C,gBAAM;AACN,mBAAS;AACT,qBAAW;AACX,iBAAO,GAAG,YAAY,SAAU,IAAI;AAClC,gBAAI,aAAa,GAAG,SAAS;AAC3B,mBAAK;AAAA;AAAA;AAAA;AAAA;AAMb;AAAA;AAAA;AAAA;;;AC35EJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,qCAAQ;", "names": []}