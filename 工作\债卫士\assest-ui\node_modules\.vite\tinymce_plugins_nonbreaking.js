import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/nonbreaking/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/nonbreaking/plugin.js"() {
    (function() {
      "use strict";
      var global$1 = tinymce.util.Tools.resolve("tinymce.PluginManager");
      var getKeyboardSpaces = function(editor) {
        var spaces = editor.getParam("nonbreaking_force_tab", 0);
        if (typeof spaces === "boolean") {
          return spaces === true ? 3 : 0;
        } else {
          return spaces;
        }
      };
      var wrapNbsps = function(editor) {
        return editor.getParam("nonbreaking_wrap", true, "boolean");
      };
      var stringRepeat = function(string, repeats) {
        var str = "";
        for (var index = 0; index < repeats; index++) {
          str += string;
        }
        return str;
      };
      var isVisualCharsEnabled = function(editor) {
        return editor.plugins.visualchars ? editor.plugins.visualchars.isEnabled() : false;
      };
      var insertNbsp = function(editor, times) {
        var classes = function() {
          return isVisualCharsEnabled(editor) ? "mce-nbsp-wrap mce-nbsp" : "mce-nbsp-wrap";
        };
        var nbspSpan = function() {
          return '<span class="' + classes() + '" contenteditable="false">' + stringRepeat("&nbsp;", times) + "</span>";
        };
        var shouldWrap = wrapNbsps(editor);
        var html = shouldWrap || editor.plugins.visualchars ? nbspSpan() : stringRepeat("&nbsp;", times);
        editor.undoManager.transact(function() {
          return editor.insertContent(html);
        });
      };
      var register$1 = function(editor) {
        editor.addCommand("mceNonBreaking", function() {
          insertNbsp(editor, 1);
        });
      };
      var global = tinymce.util.Tools.resolve("tinymce.util.VK");
      var setup = function(editor) {
        var spaces = getKeyboardSpaces(editor);
        if (spaces > 0) {
          editor.on("keydown", function(e) {
            if (e.keyCode === global.TAB && !e.isDefaultPrevented()) {
              if (e.shiftKey) {
                return;
              }
              e.preventDefault();
              e.stopImmediatePropagation();
              insertNbsp(editor, spaces);
            }
          });
        }
      };
      var register = function(editor) {
        var onAction = function() {
          return editor.execCommand("mceNonBreaking");
        };
        editor.ui.registry.addButton("nonbreaking", {
          icon: "non-breaking",
          tooltip: "Nonbreaking space",
          onAction
        });
        editor.ui.registry.addMenuItem("nonbreaking", {
          icon: "non-breaking",
          text: "Nonbreaking space",
          onAction
        });
      };
      function Plugin() {
        global$1.add("nonbreaking", function(editor) {
          register$1(editor);
          register(editor);
          setup(editor);
        });
      }
      Plugin();
    })();
  }
});

// node_modules/tinymce/plugins/nonbreaking/index.js
var require_nonbreaking = __commonJS({
  "node_modules/tinymce/plugins/nonbreaking/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_nonbreaking
var tinymce_plugins_nonbreaking_default = require_nonbreaking();
export {
  tinymce_plugins_nonbreaking_default as default
};
//# sourceMappingURL=tinymce_plugins_nonbreaking.js.map
