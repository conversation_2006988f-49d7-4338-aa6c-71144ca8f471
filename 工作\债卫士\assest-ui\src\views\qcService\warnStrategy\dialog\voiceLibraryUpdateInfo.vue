<template>
    <el-dialog :title="title" v-model="open" append-to-body width="550" :before-close="cancel">
        <el-form inline ref="formRef" label-width="auto" :model="form" :rules="rules">
            <el-form-item prop="accountName" label="账号名称">
                <el-input v-model="form.accountName" style="width:320px;" placeholder="请输入账号名称" />
            </el-form-item>
            <el-form-item prop="appKey" label="appkey">
                <el-input v-model="form.appKey" style="width:320px;" placeholder="请输入appkey" />
            </el-form-item>
            <el-form-item prop="accessKeyId" label="accessKeyId">
                <el-input v-model="form.accessKeyId" style="width:320px;" placeholder="请输入accessKeyId" />
            </el-form-item>
            <el-form-item prop="accessKeySecret" label="accessKeySecret">
                <el-input v-model="form.accessKeySecret" style="width:320px;" placeholder="请输入accessKeySecret" />
            </el-form-item>
            <el-form-item prop="duration" label="通话最短时长(s)">
                <el-input v-model="form.duration" style="width:320px;" placeholder="请输入通话最短时长(s)" />
                <span>注：质检最短录音时长的设置</span>
            </el-form-item>
            <el-form-item prop="status" label="状态">
                <el-radio-group v-model="form.status">
                    <el-radio :label="0">启用</el-radio>
                    <el-radio :label="1">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" @click="submit" type="primary"> 确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { addEmailWarnStrategyApi, editEmailWarnStrategyApi } from '@/api/qcService/warnStrategy';
const props = defineProps({
    getList: { type: Function, default: () => { } }
})
const { proxy } = getCurrentInstance()
const open = ref(false)
const loading = ref(false)
const title = ref('添加')
const data = reactive({
    form: { status: 0 },
    rules: {
        accountName: [{ required: true, message: '请输入账号名称', trigger: 'blur' }],
        appKey: [{ required: true, message: '请输入appkey', trigger: 'blur' }],
        accessKeyId: [{ required: true, message: '请输入accessKeyId', trigger: 'blur' }],
        accessKeySecret: [{ required: true, message: '请输入accessKeySecret', trigger: 'blur' }],
        duration: [{ required: true, message: '请输入通话最短时长(s)', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    },
})
const { form, rules } = toRefs(data)

function submit() {
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            const reqForm = JSON.parse(JSON.stringify(form.value))
            const reqApi = reqForm.id ? editEmailWarnStrategyApi : addEmailWarnStrategyApi
            reqApi(reqForm).then(() => {
                cancel()
                props.getList && props.getList()
                proxy.$modal.msgSuccess('操作成功！')
            })
        }
    })
}

function openDialog(data) {
    open.value = true
    title.value = data.title
    form.value = data.row ? data.row : {}
}

function cancel() {
    open.value = false
}
defineExpose({ openDialog })
</script>
<style lang="scss" scoped></style>