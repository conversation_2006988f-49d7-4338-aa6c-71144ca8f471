<template>
    <div>
        <el-table :data="dataList" v-loading="loading">
            <el-table-column label="减免场景" align="center" min-width="120" prop="scene" />
            <el-table-column label="减免类型" align="center" min-width="120" prop="type" />
            <el-table-column label="减免金额" align="center" min-width="120" prop="money" />
            <el-table-column label="减免期数" align="center" min-width="120" prop="stage" />
            <el-table-column label="减免比例" align="center" min-width="120" prop="ratio" />
            <el-table-column label="减免服务费" align="center" min-width="120" prop="serviceCharge" />
            <el-table-column label="减免违约次数" align="center" min-width="120" prop="violateNum" />
            <el-table-column label="减免原因" align="center" min-width="120" prop="remarks" />
            <el-table-column label="证明材料类别" align="center" min-width="120" prop="fileType" />
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>
const dataList = ref([
    {
        scene: '还款能力低',
        type: '电催减免',
        money: '1 元',
        stage: '整笔减免',
        ratio: '0%',
        serviceCharge: '0',
        violateNum: '0',
        remarks: '123',
        fileType: '贫困证明.png'
    }
])
const loading = ref(false)

const queryParams = ref({
    allQuery: false,
    pageNum: 1,
    pageSize: 10,
})
const total = ref(1)
</script>

<style lang="scss" scoped></style>