{"version": 3, "sources": ["../vue-cropper/dist/vue-cropper.es.js", "dep:vue-cropper"], "sourcesContent": ["import{defineComponent as t,pushScopeId as e,popScopeId as i,openBlock as s,createElementBlock as h,withDirectives as o,createElementVNode as r,normalizeStyle as a,vShow as c,createCommentVNode as n,normalizeClass as p,toDisplayString as l}from\"vue\";const u={};u.getData=t=>new Promise(((e,i)=>{let s={};(function(t){let e=null;return new Promise(((i,s)=>{if(t.src)if(/^data\\:/i.test(t.src))e=function(t){t=t.replace(/^data\\:([^\\;]+)\\;base64,/gim,\"\");for(var e=atob(t),i=e.length,s=new ArrayBuffer(i),h=new Uint8Array(s),o=0;o<i;o++)h[o]=e.charCodeAt(o);return s}(t.src),i(e);else if(/^blob\\:/i.test(t.src)){var h=new FileReader;h.onload=function(t){e=t.target.result,i(e)},function(t,e){var i=new XMLHttpRequest;i.open(\"GET\",t,!0),i.responseType=\"blob\",i.onload=function(t){200!=this.status&&0!==this.status||e(this.response)},i.send()}(t.src,(function(t){h.readAsArrayBuffer(t)}))}else{var o=new XMLHttpRequest;o.onload=function(){if(200!=this.status&&0!==this.status)throw\"Could not load image\";e=o.response,i(e),o=null},o.open(\"GET\",t.src,!0),o.responseType=\"arraybuffer\",o.send(null)}else s(\"img error\")}))})(t).then((t=>{s.arrayBuffer=t,s.orientation=function(t){var e,i,s,h,o,r,a,c,n,p=new DataView(t),l=p.byteLength;if(255===p.getUint8(0)&&216===p.getUint8(1))for(c=2;c<l;){if(255===p.getUint8(c)&&225===p.getUint8(c+1)){r=c;break}c++}r&&(i=r+10,\"Exif\"===function(t,e,i){var s,h=\"\";for(s=e,i+=e;s<i;s++)h+=String.fromCharCode(t.getUint8(s));return h}(p,r+4,4)&&((h=18761===(o=p.getUint16(i)))||19789===o)&&42===p.getUint16(i+2,h)&&(s=p.getUint32(i+4,h))>=8&&(a=i+s));if(a)for(l=p.getUint16(a,h),n=0;n<l;n++)if(c=a+12*n+2,274===p.getUint16(c,h)){c+=8,e=p.getUint16(c,h);break}return e}(t),e(s)})).catch((t=>{i(t)}))}));const g=t({data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:\"\",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:\"\",coe:.2,scaling:!1,scalingSet:\"\",coeStatus:\"\",isCanShow:!0}},props:{img:{type:[String,Blob,null,File],default:\"\"},outputSize:{type:Number,default:1},outputType:{type:String,default:\"jpeg\"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:()=>[1,1]},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:\"contain\"},limitMinSize:{type:[Number,Array,String],default:()=>10}},computed:{cropInfo(){let t={};if(t.top=this.cropOffsertY>21?\"-21px\":\"0px\",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){let e=1;this.high&&!this.full&&(e=window.devicePixelRatio),1!==this.enlarge&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE:()=>!!window.ActiveXObject||\"ActiveXObject\"in window,passive(){return this.isIE?null:{passive:!1}}},watch:{img(){this.checkedImg()},imgs(t){\"\"!==t&&this.reload()},cropW(){this.showPreview()},cropH(){this.showPreview()},cropOffsertX(){this.showPreview()},cropOffsertY(){this.showPreview()},scale(t,e){this.showPreview()},x(){this.showPreview()},y(){this.showPreview()},autoCrop(t){t&&this.goAutoCrop()},autoCropWidth(){this.autoCrop&&this.goAutoCrop()},autoCropHeight(){this.autoCrop&&this.goAutoCrop()},mode(){this.checkedImg()},rotate(){this.showPreview(),(this.autoCrop||this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion(t){var e=navigator.userAgent.split(\" \"),i=\"\";let s=0;const h=new RegExp(t,\"i\");for(var o=0;o<e.length;o++)h.test(e[o])&&(i=e[o]);return s=i?i.split(\"/\")[1].split(\".\"):[\"0\",\"0\",\"0\"],s},checkOrientationImage(t,e,i,s){if(this.getVersion(\"chrome\")[0]>=81)e=-1;else if(this.getVersion(\"safari\")[0]>=605){const t=this.getVersion(\"version\");t[0]>13&&t[1]>1&&(e=-1)}else{const t=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(t){let i=t[1];i=i.split(\"_\"),(i[0]>13||i[0]>=13&&i[1]>=4)&&(e=-1)}}let h=document.createElement(\"canvas\"),o=h.getContext(\"2d\");switch(o.save(),e){case 2:h.width=i,h.height=s,o.translate(i,0),o.scale(-1,1);break;case 3:h.width=i,h.height=s,o.translate(i/2,s/2),o.rotate(180*Math.PI/180),o.translate(-i/2,-s/2);break;case 4:h.width=i,h.height=s,o.translate(0,s),o.scale(1,-1);break;case 5:h.height=i,h.width=s,o.rotate(.5*Math.PI),o.scale(1,-1);break;case 6:h.width=s,h.height=i,o.translate(s/2,i/2),o.rotate(90*Math.PI/180),o.translate(-i/2,-s/2);break;case 7:h.height=i,h.width=s,o.rotate(.5*Math.PI),o.translate(i,-s),o.scale(-1,1);break;case 8:h.height=i,h.width=s,o.translate(s/2,i/2),o.rotate(-90*Math.PI/180),o.translate(-i/2,-s/2);break;default:h.width=i,h.height=s}o.drawImage(t,0,0,i,s),o.restore(),h.toBlob((t=>{let e=URL.createObjectURL(t);URL.revokeObjectURL(this.imgs),this.imgs=e}),\"image/\"+this.outputType,1)},checkedImg(){if(null===this.img||\"\"===this.img)return this.imgs=\"\",void this.clearCrop();this.loading=!0,this.scale=1,this.rotate=0,this.clearCrop();let t=new Image;if(t.onload=()=>{if(\"\"===this.img)return this.$emit(\"imgLoad\",\"error\"),this.$emit(\"img-load\",\"error\"),!1;let e=t.width,i=t.height;u.getData(t).then((s=>{this.orientation=s.orientation||1;let h=Number(this.maxImgSize);!this.orientation&&e<h&i<h?this.imgs=this.img:(e>h&&(i=i/e*h,e=h),i>h&&(e=e/i*h,i=h),this.checkOrientationImage(t,this.orientation,e,i))}))},t.onerror=()=>{this.$emit(\"imgLoad\",\"error\"),this.$emit(\"img-load\",\"error\")},\"data\"!==this.img.substr(0,4)&&(t.crossOrigin=\"\"),this.isIE){var e=new XMLHttpRequest;e.onload=function(){var e=URL.createObjectURL(this.response);t.src=e},e.open(\"GET\",this.img,!0),e.responseType=\"blob\",e.send()}else t.src=this.img},startMove(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=(\"clientX\"in t?t.clientX:t.touches[0].clientX)-this.x,this.moveY=(\"clientY\"in t?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener(\"touchmove\",this.moveImg),window.addEventListener(\"touchend\",this.leaveImg),2==t.touches.length&&(this.touches=t.touches,window.addEventListener(\"touchmove\",this.touchScale),window.addEventListener(\"touchend\",this.cancelTouchScale))):(window.addEventListener(\"mousemove\",this.moveImg),window.addEventListener(\"mouseup\",this.leaveImg)),this.$emit(\"imgMoving\",{moving:!0,axis:this.getImgAxis()}),this.$emit(\"img-moving\",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener(\"mousemove\",this.createCrop),window.addEventListener(\"mouseup\",this.endCrop),window.addEventListener(\"touchmove\",this.createCrop),window.addEventListener(\"touchend\",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX=\"clientX\"in t?t.clientX:t.touches[0].clientX,this.cropY=\"clientY\"in t?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale(t){t.preventDefault();let e=this.scale;var i=this.touches[0].clientX,s=this.touches[0].clientY,h=t.touches[0].clientX,o=t.touches[0].clientY,r=this.touches[1].clientX,a=this.touches[1].clientY,c=t.touches[1].clientX,n=t.touches[1].clientY,p=Math.sqrt(Math.pow(i-r,2)+Math.pow(s-a,2)),l=Math.sqrt(Math.pow(h-c,2)+Math.pow(o-n,2))-p,u=1,g=(u=(u=u/this.trueWidth>u/this.trueHeight?u/this.trueHeight:u/this.trueWidth)>.1?.1:u)*l;if(!this.touchNow){if(this.touchNow=!0,l>0?e+=Math.abs(g):l<0&&e>Math.abs(g)&&(e-=Math.abs(g)),this.touches=t.touches,setTimeout((()=>{this.touchNow=!1}),8),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e}},cancelTouchScale(t){window.removeEventListener(\"touchmove\",this.touchScale)},moveImg(t){if(t.preventDefault(),t.touches&&2===t.touches.length)return this.touches=t.touches,window.addEventListener(\"touchmove\",this.touchScale),window.addEventListener(\"touchend\",this.cancelTouchScale),window.removeEventListener(\"touchmove\",this.moveImg),!1;let e,i,s=\"clientX\"in t?t.clientX:t.touches[0].clientX,h=\"clientY\"in t?t.clientY:t.touches[0].clientY;e=s-this.moveX,i=h-this.moveY,this.$nextTick((()=>{if(this.centerBox){let t,s,h,o,r=this.getImgAxis(e,i,this.scale),a=this.getCropAxis(),c=this.trueHeight*this.scale,n=this.trueWidth*this.scale;switch(this.rotate){case 1:case-1:case 3:case-3:t=this.cropOffsertX-this.trueWidth*(1-this.scale)/2+(c-n)/2,s=this.cropOffsertY-this.trueHeight*(1-this.scale)/2+(n-c)/2,h=t-c+this.cropW,o=s-n+this.cropH;break;default:t=this.cropOffsertX-this.trueWidth*(1-this.scale)/2,s=this.cropOffsertY-this.trueHeight*(1-this.scale)/2,h=t-n+this.cropW,o=s-c+this.cropH}r.x1>=a.x1&&(e=t),r.y1>=a.y1&&(i=s),r.x2<=a.x2&&(e=h),r.y2<=a.y2&&(i=o)}this.x=e,this.y=i,this.$emit(\"imgMoving\",{moving:!0,axis:this.getImgAxis()}),this.$emit(\"img-moving\",{moving:!0,axis:this.getImgAxis()})}))},leaveImg(t){window.removeEventListener(\"mousemove\",this.moveImg),window.removeEventListener(\"touchmove\",this.moveImg),window.removeEventListener(\"mouseup\",this.leaveImg),window.removeEventListener(\"touchend\",this.leaveImg),this.$emit(\"imgMoving\",{moving:!1,axis:this.getImgAxis()}),this.$emit(\"img-moving\",{moving:!1,axis:this.getImgAxis()})},scaleImg(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize(t){t.preventDefault();let e=this.scale;var i=t.deltaY||t.wheelDelta;i=navigator.userAgent.indexOf(\"Firefox\")>0?30*i:i,this.isIE&&(i=-i);var s=this.coe,h=(s=s/this.trueWidth>s/this.trueHeight?s/this.trueHeight:s/this.trueWidth)*i;h<0?e+=Math.abs(h):e>Math.abs(h)&&(e-=Math.abs(h));let o=h<0?\"add\":\"reduce\";if(o!==this.coeStatus&&(this.coeStatus=o,this.coe=.2),this.scaling||(this.scalingSet=setTimeout((()=>{this.scaling=!1,this.coe=this.coe+=.01}),50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},changeScale(t){let e=this.scale;t=t||1;var i=20;if((t*=i=i/this.trueWidth>i/this.trueHeight?i/this.trueHeight:i/this.trueWidth)>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop(t){t.preventDefault();var e=\"clientX\"in t?t.clientX:t.touches?t.touches[0].clientX:0,i=\"clientY\"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick((()=>{var t=e-this.cropX,s=i-this.cropY;if(t>0?(this.cropW=t+this.cropChangeX>this.w?this.w-this.cropChangeX:t,this.cropOffsertX=this.cropChangeX):(this.cropW=this.w-this.cropChangeX+Math.abs(t)>this.w?this.cropChangeX:Math.abs(t),this.cropOffsertX=this.cropChangeX+t>0?this.cropChangeX+t:0),this.fixed){var h=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];h+this.cropOffsertY>this.h?(this.cropH=this.h-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],this.cropOffsertX=t>0?this.cropChangeX:this.cropChangeX-this.cropW):this.cropH=h,this.cropOffsertY=this.cropOffsertY}else s>0?(this.cropH=s+this.cropChangeY>this.h?this.h-this.cropChangeY:s,this.cropOffsertY=this.cropChangeY):(this.cropH=this.h-this.cropChangeY+Math.abs(s)>this.h?this.cropChangeY:Math.abs(s),this.cropOffsertY=this.cropChangeY+s>0?this.cropChangeY+s:0)}))},changeCropSize(t,e,i,s,h){t.preventDefault(),window.addEventListener(\"mousemove\",this.changeCropNow),window.addEventListener(\"mouseup\",this.changeCropEnd),window.addEventListener(\"touchmove\",this.changeCropNow),window.addEventListener(\"touchend\",this.changeCropEnd),this.canChangeX=e,this.canChangeY=i,this.changeCropTypeX=s,this.changeCropTypeY=h,this.cropX=\"clientX\"in t?t.clientX:t.touches[0].clientX,this.cropY=\"clientY\"in t?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit(\"change-crop-size\",{width:this.cropW,height:this.cropH})},changeCropNow(t){t.preventDefault();var e=\"clientX\"in t?t.clientX:t.touches?t.touches[0].clientX:0,i=\"clientY\"in t?t.clientY:t.touches?t.touches[0].clientY:0;let s=this.w,h=this.h,o=0,r=0;if(this.centerBox){let t=this.getImgAxis(),e=t.x2,i=t.y2;o=t.x1>0?t.x1:0,r=t.y1>0?t.y1:0,s>e&&(s=e),h>i&&(h=i)}this.$nextTick((()=>{var t=e-this.cropX,a=i-this.cropY;if(this.canChangeX&&(1===this.changeCropTypeX?this.cropOldW-t>0?(this.cropW=s-this.cropChangeX-t<=s-o?this.cropOldW-t:this.cropOldW+this.cropChangeX-o,this.cropOffsertX=s-this.cropChangeX-t<=s-o?this.cropChangeX+t:o):(this.cropW=Math.abs(t)+this.cropChangeX<=s?Math.abs(t)-this.cropOldW:s-this.cropOldW-this.cropChangeX,this.cropOffsertX=this.cropChangeX+this.cropOldW):2===this.changeCropTypeX&&(this.cropOldW+t>0?(this.cropW=this.cropOldW+t+this.cropOffsertX<=s?this.cropOldW+t:s-this.cropOffsertX,this.cropOffsertX=this.cropChangeX):(this.cropW=s-this.cropChangeX+Math.abs(t+this.cropOldW)<=s-o?Math.abs(t+this.cropOldW):this.cropChangeX-o,this.cropOffsertX=s-this.cropChangeX+Math.abs(t+this.cropOldW)<=s-o?this.cropChangeX-Math.abs(t+this.cropOldW):o))),this.canChangeY&&(1===this.changeCropTypeY?this.cropOldH-a>0?(this.cropH=h-this.cropChangeY-a<=h-r?this.cropOldH-a:this.cropOldH+this.cropChangeY-r,this.cropOffsertY=h-this.cropChangeY-a<=h-r?this.cropChangeY+a:r):(this.cropH=Math.abs(a)+this.cropChangeY<=h?Math.abs(a)-this.cropOldH:h-this.cropOldH-this.cropChangeY,this.cropOffsertY=this.cropChangeY+this.cropOldH):2===this.changeCropTypeY&&(this.cropOldH+a>0?(this.cropH=this.cropOldH+a+this.cropOffsertY<=h?this.cropOldH+a:h-this.cropOffsertY,this.cropOffsertY=this.cropChangeY):(this.cropH=h-this.cropChangeY+Math.abs(a+this.cropOldH)<=h-r?Math.abs(a+this.cropOldH):this.cropChangeY-r,this.cropOffsertY=h-this.cropChangeY+Math.abs(a+this.cropOldH)<=h-r?this.cropChangeY-Math.abs(a+this.cropOldH):r))),this.canChangeX&&this.fixed){var c=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];c+this.cropOffsertY>h?(this.cropH=h-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0]):this.cropH=c}if(this.canChangeY&&this.fixed){var n=this.cropH/this.fixedNumber[1]*this.fixedNumber[0];n+this.cropOffsertX>s?(this.cropW=s-this.cropOffsertX,this.cropH=this.cropW/this.fixedNumber[0]*this.fixedNumber[1]):this.cropW=n}}))},checkCropLimitSize(){let{cropW:t,cropH:e,limitMinSize:i}=this,s=new Array;return s=Array.isArray[i]?i:[i,i],t=parseFloat(s[0]),e=parseFloat(s[1]),[t,e]},changeCropEnd(t){window.removeEventListener(\"mousemove\",this.changeCropNow),window.removeEventListener(\"mouseup\",this.changeCropEnd),window.removeEventListener(\"touchmove\",this.changeCropNow),window.removeEventListener(\"touchend\",this.changeCropEnd)},endCrop(){0===this.cropW&&0===this.cropH&&(this.cropping=!1),window.removeEventListener(\"mousemove\",this.createCrop),window.removeEventListener(\"mouseup\",this.endCrop),window.removeEventListener(\"touchmove\",this.createCrop),window.removeEventListener(\"touchend\",this.endCrop)},startCrop(){this.crop=!0},stopCrop(){this.crop=!1},clearCrop(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&2===t.touches.length)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener(\"mousemove\",this.moveCrop),window.addEventListener(\"mouseup\",this.leaveCrop),window.addEventListener(\"touchmove\",this.moveCrop),window.addEventListener(\"touchend\",this.leaveCrop);let e,i,s=\"clientX\"in t?t.clientX:t.touches[0].clientX,h=\"clientY\"in t?t.clientY:t.touches[0].clientY;e=s-this.cropOffsertX,i=h-this.cropOffsertY,this.cropX=e,this.cropY=i,this.$emit(\"cropMoving\",{moving:!0,axis:this.getCropAxis()}),this.$emit(\"crop-moving\",{moving:!0,axis:this.getCropAxis()})},moveCrop(t,e){let i=0,s=0;t&&(t.preventDefault(),i=\"clientX\"in t?t.clientX:t.touches[0].clientX,s=\"clientY\"in t?t.clientY:t.touches[0].clientY),this.$nextTick((()=>{let t,h,o=i-this.cropX,r=s-this.cropY;if(e&&(o=this.cropOffsertX,r=this.cropOffsertY),t=o<=0?0:o+this.cropW>this.w?this.w-this.cropW:o,h=r<=0?0:r+this.cropH>this.h?this.h-this.cropH:r,this.centerBox){let e=this.getImgAxis();t<=e.x1&&(t=e.x1),t+this.cropW>e.x2&&(t=e.x2-this.cropW),h<=e.y1&&(h=e.y1),h+this.cropH>e.y2&&(h=e.y2-this.cropH)}this.cropOffsertX=t,this.cropOffsertY=h,this.$emit(\"cropMoving\",{moving:!0,axis:this.getCropAxis()}),this.$emit(\"crop-moving\",{moving:!0,axis:this.getCropAxis()})}))},getImgAxis(t,e,i){t=t||this.x,e=e||this.y,i=i||this.scale;let s={x1:0,x2:0,y1:0,y2:0},h=this.trueWidth*i,o=this.trueHeight*i;switch(this.rotate){case 0:s.x1=t+this.trueWidth*(1-i)/2,s.x2=s.x1+this.trueWidth*i,s.y1=e+this.trueHeight*(1-i)/2,s.y2=s.y1+this.trueHeight*i;break;case 1:case-1:case 3:case-3:s.x1=t+this.trueWidth*(1-i)/2+(h-o)/2,s.x2=s.x1+this.trueHeight*i,s.y1=e+this.trueHeight*(1-i)/2+(o-h)/2,s.y2=s.y1+this.trueWidth*i;break;default:s.x1=t+this.trueWidth*(1-i)/2,s.x2=s.x1+this.trueWidth*i,s.y1=e+this.trueHeight*(1-i)/2,s.y2=s.y1+this.trueHeight*i}return s},getCropAxis(){let t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop(t){window.removeEventListener(\"mousemove\",this.moveCrop),window.removeEventListener(\"mouseup\",this.leaveCrop),window.removeEventListener(\"touchmove\",this.moveCrop),window.removeEventListener(\"touchend\",this.leaveCrop),this.$emit(\"cropMoving\",{moving:!1,axis:this.getCropAxis()}),this.$emit(\"crop-moving\",{moving:!1,axis:this.getCropAxis()})},getCropChecked(t){let e=document.createElement(\"canvas\"),i=new Image,s=this.rotate,h=this.trueWidth,o=this.trueHeight,r=this.cropOffsertX,a=this.cropOffsertY;function c(t,i){e.width=Math.round(t),e.height=Math.round(i)}i.onload=()=>{if(0!==this.cropW){let t=e.getContext(\"2d\"),n=1;this.high&!this.full&&(n=window.devicePixelRatio),1!==this.enlarge&!this.full&&(n=Math.abs(Number(this.enlarge)));let p=this.cropW*n,l=this.cropH*n,u=h*this.scale*n,g=o*this.scale*n,d=(this.x-r+this.trueWidth*(1-this.scale)/2)*n,m=(this.y-a+this.trueHeight*(1-this.scale)/2)*n;switch(c(p,l),t.save(),s){case 0:this.full?(c(p/this.scale,l/this.scale),t.drawImage(i,d/this.scale,m/this.scale,u/this.scale,g/this.scale)):t.drawImage(i,d,m,u,g);break;case 1:case-3:this.full?(c(p/this.scale,l/this.scale),d=d/this.scale+(u/this.scale-g/this.scale)/2,m=m/this.scale+(g/this.scale-u/this.scale)/2,t.rotate(90*s*Math.PI/180),t.drawImage(i,m,-d-g/this.scale,u/this.scale,g/this.scale)):(d+=(u-g)/2,m+=(g-u)/2,t.rotate(90*s*Math.PI/180),t.drawImage(i,m,-d-g,u,g));break;case 2:case-2:this.full?(c(p/this.scale,l/this.scale),t.rotate(90*s*Math.PI/180),d/=this.scale,m/=this.scale,t.drawImage(i,-d-u/this.scale,-m-g/this.scale,u/this.scale,g/this.scale)):(t.rotate(90*s*Math.PI/180),t.drawImage(i,-d-u,-m-g,u,g));break;case 3:case-1:this.full?(c(p/this.scale,l/this.scale),d=d/this.scale+(u/this.scale-g/this.scale)/2,m=m/this.scale+(g/this.scale-u/this.scale)/2,t.rotate(90*s*Math.PI/180),t.drawImage(i,-m-u/this.scale,d,u/this.scale,g/this.scale)):(d+=(u-g)/2,m+=(g-u)/2,t.rotate(90*s*Math.PI/180),t.drawImage(i,-m-u,d,u,g));break;default:this.full?(c(p/this.scale,l/this.scale),t.drawImage(i,d/this.scale,m/this.scale,u/this.scale,g/this.scale)):t.drawImage(i,d,m,u,g)}t.restore()}else{let t=h*this.scale,r=o*this.scale,a=e.getContext(\"2d\");switch(a.save(),s){case 0:c(t,r),a.drawImage(i,0,0,t,r);break;case 1:case-3:c(r,t),a.rotate(90*s*Math.PI/180),a.drawImage(i,0,-r,t,r);break;case 2:case-2:c(t,r),a.rotate(90*s*Math.PI/180),a.drawImage(i,-t,-r,t,r);break;case 3:case-1:c(r,t),a.rotate(90*s*Math.PI/180),a.drawImage(i,-t,0,t,r);break;default:c(t,r),a.drawImage(i,0,0,t,r)}a.restore()}t(e)},\"data\"!==this.img.substr(0,4)&&(i.crossOrigin=\"Anonymous\"),i.src=this.imgs},getCropData(t){this.getCropChecked((e=>{t(e.toDataURL(\"image/\"+this.outputType,this.outputSize))}))},getCropBlob(t){this.getCropChecked((e=>{e.toBlob((e=>t(e)),\"image/\"+this.outputType,this.outputSize)}))},showPreview(){if(!this.isCanShow)return!1;this.isCanShow=!1,setTimeout((()=>{this.isCanShow=!0}),16);let t=this.cropW,e=this.cropH,i=this.scale;var s={};s.div={width:`${t}px`,height:`${e}px`};let h=(this.x-this.cropOffsertX)/i,o=(this.y-this.cropOffsertY)/i;s.w=t,s.h=e,s.url=this.imgs,s.img={width:`${this.trueWidth}px`,height:`${this.trueHeight}px`,transform:`scale(${i})translate3d(${h}px, ${o}px, 0px)rotateZ(${90*this.rotate}deg)`},s.html=`\\n      <div class=\"show-preview\" style=\"width: ${s.w}px; height: ${s.h}px,; overflow: hidden\">\\n        <div style=\"width: ${t}px; height: ${e}px\">\\n          <img src=${s.url} style=\"width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:\\n          scale(${i})translate3d(${h}px, ${o}px, 0px)rotateZ(${90*this.rotate}deg)\">\\n        </div>\\n      </div>`,this.$emit(\"realTime\",s),this.$emit(\"real-time\",s)},reload(){let t=new Image;t.onload=()=>{this.w=parseFloat(window.getComputedStyle(this.$refs.cropper).width),this.h=parseFloat(window.getComputedStyle(this.$refs.cropper).height),this.trueWidth=t.width,this.trueHeight=t.height,this.original?this.scale=1:this.scale=this.checkedMode(),this.$nextTick((()=>{this.x=-(this.trueWidth-this.trueWidth*this.scale)/2+(this.w-this.trueWidth*this.scale)/2,this.y=-(this.trueHeight-this.trueHeight*this.scale)/2+(this.h-this.trueHeight*this.scale)/2,this.loading=!1,this.autoCrop&&this.goAutoCrop(),this.$emit(\"img-load\",\"success\"),this.$emit(\"imgLoad\",\"success\"),setTimeout((()=>{this.showPreview()}),20)}))},t.onerror=()=>{this.$emit(\"imgLoad\",\"error\"),this.$emit(\"img-load\",\"error\")},t.src=this.imgs},checkedMode(){let t=1,e=this.trueWidth,i=this.trueHeight;const s=this.mode.split(\" \");switch(s[0]){case\"contain\":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case\"cover\":e=this.w,t=e/this.trueWidth,i*=t,i<this.h&&(i=this.h,t=i/this.trueHeight);break;default:try{let h=s[0];if(-1!==h.search(\"px\")){h=h.replace(\"px\",\"\"),e=parseFloat(h);const o=e/this.trueWidth;let r=1,a=s[1];-1!==a.search(\"px\")&&(a=a.replace(\"px\",\"\"),i=parseFloat(a),r=i/this.trueHeight),t=Math.min(o,r)}if(-1!==h.search(\"%\")&&(h=h.replace(\"%\",\"\"),e=parseFloat(h)/100*this.w,t=e/this.trueWidth),2===s.length&&\"auto\"===h){let e=s[1];-1!==e.search(\"px\")&&(e=e.replace(\"px\",\"\"),i=parseFloat(e),t=i/this.trueHeight),-1!==e.search(\"%\")&&(e=e.replace(\"%\",\"\"),i=parseFloat(e)/100*this.h,t=i/this.trueHeight)}}catch(h){t=1}}return t},goAutoCrop(t,e){if(\"\"===this.imgs||null===this.imgs)return;this.clearCrop(),this.cropping=!0;let i=this.w,s=this.h;if(this.centerBox){const t=Math.abs(this.rotate)%2>0;let e=(t?this.trueHeight:this.trueWidth)*this.scale,h=(t?this.trueWidth:this.trueHeight)*this.scale;i=e<i?e:i,s=h<s?h:s}var h=t||parseFloat(this.autoCropWidth),o=e||parseFloat(this.autoCropHeight);0!==h&&0!==o||(h=.8*i,o=.8*s),h=h>i?i:h,o=o>s?s:o,this.fixed&&(o=h/this.fixedNumber[0]*this.fixedNumber[1]),o>this.h&&(h=(o=this.h)/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(h,o)},changeCrop(t,e){if(this.centerBox){let i=this.getImgAxis();t>i.x2-i.x1&&(e=(t=i.x2-i.x1)/this.fixedNumber[0]*this.fixedNumber[1]),e>i.y2-i.y1&&(t=(e=i.y2-i.y1)/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick((()=>{this.cropOffsertX=(this.w-this.cropW)/2,this.cropOffsertY=(this.h-this.cropH)/2,this.centerBox&&this.moveCrop(null,!0)}))},refresh(){this.img,this.imgs=\"\",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.clearCrop(),this.$nextTick((()=>{this.checkedImg()}))},rotateLeft(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear(){this.rotate=0},checkoutImgAxis(t,e,i){t=t||this.x,e=e||this.y,i=i||this.scale;let s=!0;if(this.centerBox){let h=this.getImgAxis(t,e,i),o=this.getCropAxis();h.x1>=o.x1&&(s=!1),h.x2<=o.x2&&(s=!1),h.y1>=o.y1&&(s=!1),h.y2<=o.y2&&(s=!1)}return s}},mounted(){this.support=\"onwheel\"in document.createElement(\"div\")?\"wheel\":void 0!==document.onmousewheel?\"mousewheel\":\"DOMMouseScroll\";let t=this;var e=navigator.userAgent;this.isIOS=!!e.match(/\\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,\"toBlob\",{value:function(e,i,s){for(var h=atob(this.toDataURL(i,s).split(\",\")[1]),o=h.length,r=new Uint8Array(o),a=0;a<o;a++)r[a]=h.charCodeAt(a);e(new Blob([r],{type:t.type||\"image/png\"}))}}),this.showPreview(),this.checkedImg()},destroyed(){window.removeEventListener(\"mousemove\",this.moveCrop),window.removeEventListener(\"mouseup\",this.leaveCrop),window.removeEventListener(\"touchmove\",this.moveCrop),window.removeEventListener(\"touchend\",this.leaveCrop),this.cancelScale()}});e(\"data-v-48aab112\");const d={key:0,class:\"cropper-box\"},m=[\"src\"],f={class:\"cropper-view-box\"},w=[\"src\"],v={key:1};i(),g.render=function(t,e,i,u,g,C){return s(),h(\"div\",{class:\"vue-cropper\",ref:\"cropper\",onMouseover:e[28]||(e[28]=(...e)=>t.scaleImg&&t.scaleImg(...e)),onMouseout:e[29]||(e[29]=(...e)=>t.cancelScale&&t.cancelScale(...e))},[t.imgs?(s(),h(\"div\",d,[o(r(\"div\",{class:\"cropper-box-canvas\",style:a({width:t.trueWidth+\"px\",height:t.trueHeight+\"px\",transform:\"scale(\"+t.scale+\",\"+t.scale+\") translate3d(\"+t.x/t.scale+\"px,\"+t.y/t.scale+\"px,0)rotateZ(\"+90*t.rotate+\"deg)\"})},[r(\"img\",{src:t.imgs,alt:\"cropper-img\",ref:\"cropperImg\"},null,8,m)],4),[[c,!t.loading]])])):n(\"\",!0),r(\"div\",{class:p([\"cropper-drag-box\",{\"cropper-move\":t.move&&!t.crop,\"cropper-crop\":t.crop,\"cropper-modal\":t.cropping}]),onMousedown:e[0]||(e[0]=(...e)=>t.startMove&&t.startMove(...e)),onTouchstart:e[1]||(e[1]=(...e)=>t.startMove&&t.startMove(...e))},null,34),o(r(\"div\",{class:\"cropper-crop-box\",style:a({width:t.cropW+\"px\",height:t.cropH+\"px\",transform:\"translate3d(\"+t.cropOffsertX+\"px,\"+t.cropOffsertY+\"px,0)\"})},[r(\"span\",f,[r(\"img\",{style:a({width:t.trueWidth+\"px\",height:t.trueHeight+\"px\",transform:\"scale(\"+t.scale+\",\"+t.scale+\") translate3d(\"+(t.x-t.cropOffsertX)/t.scale+\"px,\"+(t.y-t.cropOffsertY)/t.scale+\"px,0)rotateZ(\"+90*t.rotate+\"deg)\"}),src:t.imgs,alt:\"cropper-img\"},null,12,w)]),r(\"span\",{class:\"cropper-face cropper-move\",onMousedown:e[2]||(e[2]=(...e)=>t.cropMove&&t.cropMove(...e)),onTouchstart:e[3]||(e[3]=(...e)=>t.cropMove&&t.cropMove(...e))},null,32),t.info?(s(),h(\"span\",{key:0,class:\"crop-info\",style:a({top:t.cropInfo.top})},l(t.cropInfo.width)+\" × \"+l(t.cropInfo.height),5)):n(\"\",!0),t.fixedBox?n(\"\",!0):(s(),h(\"span\",v,[r(\"span\",{class:\"crop-line line-w\",onMousedown:e[4]||(e[4]=e=>t.changeCropSize(e,!1,!0,0,1)),onTouchstart:e[5]||(e[5]=e=>t.changeCropSize(e,!1,!0,0,1))},null,32),r(\"span\",{class:\"crop-line line-a\",onMousedown:e[6]||(e[6]=e=>t.changeCropSize(e,!0,!1,1,0)),onTouchstart:e[7]||(e[7]=e=>t.changeCropSize(e,!0,!1,1,0))},null,32),r(\"span\",{class:\"crop-line line-s\",onMousedown:e[8]||(e[8]=e=>t.changeCropSize(e,!1,!0,0,2)),onTouchstart:e[9]||(e[9]=e=>t.changeCropSize(e,!1,!0,0,2))},null,32),r(\"span\",{class:\"crop-line line-d\",onMousedown:e[10]||(e[10]=e=>t.changeCropSize(e,!0,!1,2,0)),onTouchstart:e[11]||(e[11]=e=>t.changeCropSize(e,!0,!1,2,0))},null,32),r(\"span\",{class:\"crop-point point1\",onMousedown:e[12]||(e[12]=e=>t.changeCropSize(e,!0,!0,1,1)),onTouchstart:e[13]||(e[13]=e=>t.changeCropSize(e,!0,!0,1,1))},null,32),r(\"span\",{class:\"crop-point point2\",onMousedown:e[14]||(e[14]=e=>t.changeCropSize(e,!1,!0,0,1)),onTouchstart:e[15]||(e[15]=e=>t.changeCropSize(e,!1,!0,0,1))},null,32),r(\"span\",{class:\"crop-point point3\",onMousedown:e[16]||(e[16]=e=>t.changeCropSize(e,!0,!0,2,1)),onTouchstart:e[17]||(e[17]=e=>t.changeCropSize(e,!0,!0,2,1))},null,32),r(\"span\",{class:\"crop-point point4\",onMousedown:e[18]||(e[18]=e=>t.changeCropSize(e,!0,!1,1,0)),onTouchstart:e[19]||(e[19]=e=>t.changeCropSize(e,!0,!1,1,0))},null,32),r(\"span\",{class:\"crop-point point5\",onMousedown:e[20]||(e[20]=e=>t.changeCropSize(e,!0,!1,2,0)),onTouchstart:e[21]||(e[21]=e=>t.changeCropSize(e,!0,!1,2,0))},null,32),r(\"span\",{class:\"crop-point point6\",onMousedown:e[22]||(e[22]=e=>t.changeCropSize(e,!0,!0,1,2)),onTouchstart:e[23]||(e[23]=e=>t.changeCropSize(e,!0,!0,1,2))},null,32),r(\"span\",{class:\"crop-point point7\",onMousedown:e[24]||(e[24]=e=>t.changeCropSize(e,!1,!0,0,2)),onTouchstart:e[25]||(e[25]=e=>t.changeCropSize(e,!1,!0,0,2))},null,32),r(\"span\",{class:\"crop-point point8\",onMousedown:e[26]||(e[26]=e=>t.changeCropSize(e,!0,!0,2,2)),onTouchstart:e[27]||(e[27]=e=>t.changeCropSize(e,!0,!0,2,2))},null,32)]))],4),[[c,t.cropping]])],544)},g.__scopeId=\"data-v-48aab112\";\"undefined\"!=typeof window&&window.Vue&&window.Vue.createApp({}).component(\"VueCropper\",g);const C={version:\"1.0.2\",install:function(t){t.component(\"VueCropper\",g)},VueCropper:g};export{g as VueCropper,C as default,C as globalCropper};\n", "import d from \"./node_modules/vue-cropper/dist/vue-cropper.es.js\";export default d;\nexport * from \"./node_modules/vue-cropper/dist/vue-cropper.es.js\""], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAA0P,IAAM,IAAE;AAAG,EAAE,UAAQ,OAAG,IAAI,QAAS,CAAC,GAAE,MAAI;AAAC,MAAI,IAAE;AAAG,EAAC,UAAS,IAAE;AAAC,QAAI,KAAE;AAAK,WAAO,IAAI,QAAS,CAAC,IAAE,OAAI;AAAC,UAAG,GAAE;AAAI,YAAG,WAAW,KAAK,GAAE;AAAK,eAAE,SAAS,IAAE;AAAC,iBAAE,GAAE,QAAQ,+BAA8B;AAAI,qBAAQ,KAAE,KAAK,KAAG,KAAE,GAAE,QAAO,KAAE,IAAI,YAAY,KAAG,KAAE,IAAI,WAAW,KAAG,KAAE,GAAE,KAAE,IAAE;AAAI,iBAAE,MAAG,GAAE,WAAW;AAAG,mBAAO;AAAA,YAAG,GAAE,MAAK,GAAE;AAAA,iBAAW,WAAW,KAAK,GAAE,MAAK;AAAC,cAAI,IAAE,IAAI;AAAW,YAAE,SAAO,SAAS,IAAE;AAAC,iBAAE,GAAE,OAAO,QAAO,GAAE;AAAA,aAAI,SAAS,IAAE,IAAE;AAAC,gBAAI,KAAE,IAAI;AAAe,eAAE,KAAK,OAAM,IAAE,OAAI,GAAE,eAAa,QAAO,GAAE,SAAO,SAAS,IAAE;AAAC,cAAK,KAAK,UAAV,OAAkB,AAAI,KAAK,WAAT,KAAiB,GAAE,KAAK;AAAA,eAAW,GAAE;AAAA,YAAQ,GAAE,KAAK,SAAS,IAAE;AAAC,cAAE,kBAAkB;AAAA;AAAA,eAAU;AAAC,cAAI,IAAE,IAAI;AAAe,YAAE,SAAO,WAAU;AAAC,gBAAG,AAAK,KAAK,UAAV,OAAkB,AAAI,KAAK,WAAT;AAAgB,oBAAK;AAAuB,iBAAE,EAAE,UAAS,GAAE,KAAG,IAAE;AAAA,aAAM,EAAE,KAAK,OAAM,GAAE,KAAI,OAAI,EAAE,eAAa,eAAc,EAAE,KAAK;AAAA;AAAA;AAAW,WAAE;AAAA;AAAA,KAAkB,GAAG,KAAM,QAAG;AAAC,MAAE,cAAY,IAAE,EAAE,cAAY,SAAS,IAAE;AAAC,UAAI,IAAE,IAAE,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,SAAS,KAAG,IAAE,EAAE;AAAW,UAAG,AAAM,EAAE,SAAS,OAAjB,OAAqB,AAAM,EAAE,SAAS,OAAjB;AAAoB,aAAI,IAAE,GAAE,IAAE,KAAG;AAAC,cAAG,AAAM,EAAE,SAAS,OAAjB,OAAqB,AAAM,EAAE,SAAS,IAAE,OAAnB,KAAsB;AAAC,gBAAE;AAAE;AAAA;AAAM;AAAA;AAAI,WAAI,MAAE,IAAE,IAAG,AAAS,SAAS,IAAE,IAAE,IAAE;AAAC,YAAI,IAAE,KAAE;AAAG,aAAI,KAAE,IAAE,MAAG,IAAE,KAAE,IAAE;AAAI,gBAAG,OAAO,aAAa,GAAE,SAAS;AAAI,eAAO;AAAA,QAAG,GAAE,IAAE,GAAE,OAA/G,UAAqH,MAAE,AAAS,KAAE,EAAE,UAAU,SAAvB,UAA6B,AAAQ,MAAR,UAAY,AAAK,EAAE,UAAU,KAAE,GAAE,OAArB,MAA0B,MAAE,EAAE,UAAU,KAAE,GAAE,OAAK,KAAI,KAAE,KAAE;AAAI,UAAG;AAAE,aAAI,IAAE,EAAE,UAAU,GAAE,IAAG,IAAE,GAAE,IAAE,GAAE;AAAI,cAAG,IAAE,IAAE,KAAG,IAAE,GAAE,AAAM,EAAE,UAAU,GAAE,OAApB,KAAuB;AAAC,iBAAG,GAAE,KAAE,EAAE,UAAU,GAAE;AAAG;AAAA;AAAA;AAAM,aAAO;AAAA,MAAG,KAAG,EAAE;AAAA,KAAM,MAAO,QAAG;AAAC,MAAE;AAAA;AAAA;AAAS,IAAM,IAAE,gBAAE,EAAC,MAAK,WAAU;AAAC,SAAM,EAAC,GAAE,GAAE,GAAE,GAAE,OAAM,GAAE,GAAE,GAAE,GAAE,GAAE,SAAQ,MAAG,WAAU,GAAE,YAAW,GAAE,MAAK,MAAG,OAAM,GAAE,OAAM,GAAE,MAAK,OAAG,UAAS,OAAG,OAAM,GAAE,OAAM,GAAE,UAAS,GAAE,UAAS,GAAE,YAAW,OAAG,YAAW,OAAG,iBAAgB,GAAE,iBAAgB,GAAE,OAAM,GAAE,OAAM,GAAE,aAAY,GAAE,aAAY,GAAE,cAAa,GAAE,cAAa,GAAE,SAAQ,IAAG,SAAQ,IAAG,UAAS,OAAG,QAAO,GAAE,OAAM,OAAG,aAAY,GAAE,MAAK,IAAG,KAAI,KAAG,SAAQ,OAAG,YAAW,IAAG,WAAU,IAAG,WAAU;AAAA,GAAK,OAAM,EAAC,KAAI,EAAC,MAAK,CAAC,QAAO,MAAK,MAAK,OAAM,SAAQ,MAAI,YAAW,EAAC,MAAK,QAAO,SAAQ,KAAG,YAAW,EAAC,MAAK,QAAO,SAAQ,UAAQ,MAAK,EAAC,MAAK,SAAQ,SAAQ,QAAI,UAAS,EAAC,MAAK,SAAQ,SAAQ,QAAI,UAAS,EAAC,MAAK,SAAQ,SAAQ,SAAI,eAAc,EAAC,MAAK,CAAC,QAAO,SAAQ,SAAQ,KAAG,gBAAe,EAAC,MAAK,CAAC,QAAO,SAAQ,SAAQ,KAAG,OAAM,EAAC,MAAK,SAAQ,SAAQ,SAAI,aAAY,EAAC,MAAK,OAAM,SAAQ,MAAI,CAAC,GAAE,MAAI,UAAS,EAAC,MAAK,SAAQ,SAAQ,SAAI,MAAK,EAAC,MAAK,SAAQ,SAAQ,SAAI,SAAQ,EAAC,MAAK,SAAQ,SAAQ,QAAI,YAAW,EAAC,MAAK,SAAQ,SAAQ,QAAI,UAAS,EAAC,MAAK,SAAQ,SAAQ,SAAI,WAAU,EAAC,MAAK,SAAQ,SAAQ,SAAI,MAAK,EAAC,MAAK,SAAQ,SAAQ,QAAI,UAAS,EAAC,MAAK,SAAQ,SAAQ,SAAI,YAAW,EAAC,MAAK,CAAC,QAAO,SAAQ,SAAQ,OAAK,SAAQ,EAAC,MAAK,CAAC,QAAO,SAAQ,SAAQ,KAAG,MAAK,EAAC,MAAK,CAAC,QAAO,SAAQ,SAAQ,KAAG,MAAK,EAAC,MAAK,QAAO,SAAQ,aAAW,cAAa,EAAC,MAAK,CAAC,QAAO,OAAM,SAAQ,SAAQ,MAAI,QAAK,UAAS,EAAC,WAAU;AAAC,MAAI,IAAE;AAAG,MAAG,EAAE,MAAI,KAAK,eAAa,KAAG,UAAQ,OAAM,EAAE,QAAM,KAAK,QAAM,IAAE,KAAK,QAAM,GAAE,EAAE,SAAO,KAAK,QAAM,IAAE,KAAK,QAAM,GAAE,KAAK,UAAS;AAAC,QAAI,IAAE;AAAE,SAAK,QAAM,CAAC,KAAK,QAAO,KAAE,OAAO,mBAAkB,AAAI,KAAK,YAAT,IAAiB,CAAC,KAAK,QAAO,KAAE,KAAK,IAAI,OAAO,KAAK,YAAW,EAAE,QAAM,EAAE,QAAM,GAAE,EAAE,SAAO,EAAE,SAAO,GAAE,KAAK,QAAO,GAAE,QAAM,EAAE,QAAM,KAAK,OAAM,EAAE,SAAO,EAAE,SAAO,KAAK;AAAA;AAAO,SAAO,EAAE,QAAM,EAAE,MAAM,QAAQ,IAAG,EAAE,SAAO,EAAE,OAAO,QAAQ,IAAG;AAAA,GAAG,MAAK,MAAI,CAAC,CAAC,OAAO,iBAAe,mBAAkB,QAAO,UAAS;AAAC,SAAO,KAAK,OAAK,OAAK,EAAC,SAAQ;AAAA,KAAM,OAAM,EAAC,MAAK;AAAC,OAAK;AAAA,GAAc,KAAK,GAAE;AAAC,EAAK,MAAL,MAAQ,KAAK;AAAA,GAAU,QAAO;AAAC,OAAK;AAAA,GAAe,QAAO;AAAC,OAAK;AAAA,GAAe,eAAc;AAAC,OAAK;AAAA,GAAe,eAAc;AAAC,OAAK;AAAA,GAAe,MAAM,GAAE,GAAE;AAAC,OAAK;AAAA,GAAe,IAAG;AAAC,OAAK;AAAA,GAAe,IAAG;AAAC,OAAK;AAAA,GAAe,SAAS,GAAE;AAAC,OAAG,KAAK;AAAA,GAAc,gBAAe;AAAC,OAAK,YAAU,KAAK;AAAA,GAAc,iBAAgB;AAAC,OAAK,YAAU,KAAK;AAAA,GAAc,OAAM;AAAC,OAAK;AAAA,GAAc,SAAQ;AAAC,OAAK,eAAe,MAAK,YAAU,KAAK,QAAM,KAAG,KAAK,QAAM,MAAI,KAAK,WAAW,KAAK,OAAM,KAAK;AAAA,KAAS,SAAQ,EAAC,WAAW,GAAE;AAAC,MAAI,IAAE,UAAU,UAAU,MAAM,MAAK,IAAE;AAAG,MAAI,IAAE;AAAE,QAAM,IAAE,IAAI,OAAO,GAAE;AAAK,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO;AAAI,MAAE,KAAK,EAAE,OAAM,KAAE,EAAE;AAAI,SAAO,IAAE,IAAE,EAAE,MAAM,KAAK,GAAG,MAAM,OAAK,CAAC,KAAI,KAAI,MAAK;AAAA,GAAG,sBAAsB,GAAE,GAAE,GAAE,GAAE;AAAC,MAAG,KAAK,WAAW,UAAU,MAAI;AAAG,QAAE;AAAA,WAAW,KAAK,WAAW,UAAU,MAAI,KAAI;AAAC,UAAM,KAAE,KAAK,WAAW;AAAW,OAAE,KAAG,MAAI,GAAE,KAAG,KAAI,KAAE;AAAA,SAAQ;AAAC,UAAM,KAAE,UAAU,UAAU,cAAc,MAAM;AAAmC,QAAG,IAAE;AAAC,UAAI,KAAE,GAAE;AAAG,WAAE,GAAE,MAAM,MAAM,IAAE,KAAG,MAAI,GAAE,MAAI,MAAI,GAAE,MAAI,MAAK,KAAE;AAAA;AAAA;AAAK,MAAI,IAAE,SAAS,cAAc,WAAU,IAAE,EAAE,WAAW;AAAM,UAAO,EAAE,QAAO;AAAA,SAAQ;AAAE,QAAE,QAAM,GAAE,EAAE,SAAO,GAAE,EAAE,UAAU,GAAE,IAAG,EAAE,MAAM,IAAG;AAAG;AAAA,SAAW;AAAE,QAAE,QAAM,GAAE,EAAE,SAAO,GAAE,EAAE,UAAU,IAAE,GAAE,IAAE,IAAG,EAAE,OAAO,MAAI,KAAK,KAAG,MAAK,EAAE,UAAU,CAAC,IAAE,GAAE,CAAC,IAAE;AAAG;AAAA,SAAW;AAAE,QAAE,QAAM,GAAE,EAAE,SAAO,GAAE,EAAE,UAAU,GAAE,IAAG,EAAE,MAAM,GAAE;AAAI;AAAA,SAAW;AAAE,QAAE,SAAO,GAAE,EAAE,QAAM,GAAE,EAAE,OAAO,MAAG,KAAK,KAAI,EAAE,MAAM,GAAE;AAAI;AAAA,SAAW;AAAE,QAAE,QAAM,GAAE,EAAE,SAAO,GAAE,EAAE,UAAU,IAAE,GAAE,IAAE,IAAG,EAAE,OAAO,KAAG,KAAK,KAAG,MAAK,EAAE,UAAU,CAAC,IAAE,GAAE,CAAC,IAAE;AAAG;AAAA,SAAW;AAAE,QAAE,SAAO,GAAE,EAAE,QAAM,GAAE,EAAE,OAAO,MAAG,KAAK,KAAI,EAAE,UAAU,GAAE,CAAC,IAAG,EAAE,MAAM,IAAG;AAAG;AAAA,SAAW;AAAE,QAAE,SAAO,GAAE,EAAE,QAAM,GAAE,EAAE,UAAU,IAAE,GAAE,IAAE,IAAG,EAAE,OAAO,MAAI,KAAK,KAAG,MAAK,EAAE,UAAU,CAAC,IAAE,GAAE,CAAC,IAAE;AAAG;AAAA;AAAc,QAAE,QAAM,GAAE,EAAE,SAAO;AAAA;AAAE,IAAE,UAAU,GAAE,GAAE,GAAE,GAAE,IAAG,EAAE,WAAU,EAAE,OAAQ,QAAG;AAAC,QAAI,KAAE,IAAI,gBAAgB;AAAG,QAAI,gBAAgB,KAAK,OAAM,KAAK,OAAK;AAAA,KAAI,WAAS,KAAK,YAAW;AAAA,GAAI,aAAY;AAAC,MAAG,AAAO,KAAK,QAAZ,QAAiB,AAAK,KAAK,QAAV;AAAc,WAAO,KAAK,OAAK,IAAG,KAAK,KAAK;AAAY,OAAK,UAAQ,MAAG,KAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK;AAAY,MAAI,IAAE,IAAI;AAAM,MAAG,EAAE,SAAO,MAAI;AAAC,QAAG,AAAK,KAAK,QAAV;AAAc,aAAO,KAAK,MAAM,WAAU,UAAS,KAAK,MAAM,YAAW,UAAS;AAAG,QAAI,KAAE,EAAE,OAAM,IAAE,EAAE;AAAO,MAAE,QAAQ,GAAG,KAAM,OAAG;AAAC,WAAK,cAAY,EAAE,eAAa;AAAE,UAAI,IAAE,OAAO,KAAK;AAAY,OAAC,KAAK,eAAa,KAAE,IAAE,IAAE,IAAE,KAAK,OAAK,KAAK,MAAK,MAAE,KAAI,KAAE,IAAE,KAAE,GAAE,KAAE,IAAG,IAAE,KAAI,MAAE,KAAE,IAAE,GAAE,IAAE,IAAG,KAAK,sBAAsB,GAAE,KAAK,aAAY,IAAE;AAAA;AAAA,KAAQ,EAAE,UAAQ,MAAI;AAAC,SAAK,MAAM,WAAU,UAAS,KAAK,MAAM,YAAW;AAAA,KAAU,AAAS,KAAK,IAAI,OAAO,GAAE,OAA3B,UAAgC,GAAE,cAAY,KAAI,KAAK,MAAK;AAAC,QAAI,IAAE,IAAI;AAAe,MAAE,SAAO,WAAU;AAAC,UAAI,KAAE,IAAI,gBAAgB,KAAK;AAAU,QAAE,MAAI;AAAA,OAAG,EAAE,KAAK,OAAM,KAAK,KAAI,OAAI,EAAE,eAAa,QAAO,EAAE;AAAA;AAAY,MAAE,MAAI,KAAK;AAAA,GAAK,UAAU,GAAE;AAAC,MAAG,EAAE,kBAAiB,KAAK,QAAM,CAAC,KAAK,MAAK;AAAC,QAAG,CAAC,KAAK;AAAQ,aAAM;AAAG,SAAK,QAAO,cAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,WAAS,KAAK,GAAE,KAAK,QAAO,cAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,WAAS,KAAK,GAAE,EAAE,UAAS,QAAO,iBAAiB,aAAY,KAAK,UAAS,OAAO,iBAAiB,YAAW,KAAK,WAAU,AAAG,EAAE,QAAQ,UAAb,KAAsB,MAAK,UAAQ,EAAE,SAAQ,OAAO,iBAAiB,aAAY,KAAK,aAAY,OAAO,iBAAiB,YAAW,KAAK,sBAAqB,QAAO,iBAAiB,aAAY,KAAK,UAAS,OAAO,iBAAiB,WAAU,KAAK,YAAW,KAAK,MAAM,aAAY,EAAC,QAAO,MAAG,MAAK,KAAK,iBAAe,KAAK,MAAM,cAAa,EAAC,QAAO,MAAG,MAAK,KAAK;AAAA;AAAoB,SAAK,WAAS,MAAG,OAAO,iBAAiB,aAAY,KAAK,aAAY,OAAO,iBAAiB,WAAU,KAAK,UAAS,OAAO,iBAAiB,aAAY,KAAK,aAAY,OAAO,iBAAiB,YAAW,KAAK,UAAS,KAAK,eAAa,EAAE,UAAQ,EAAE,UAAQ,EAAE,QAAQ,GAAG,QAAM,KAAK,MAAM,QAAQ,YAAW,KAAK,eAAa,EAAE,UAAQ,EAAE,UAAQ,EAAE,QAAQ,GAAG,QAAM,KAAK,MAAM,QAAQ,WAAU,KAAK,QAAM,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,SAAQ,KAAK,QAAM,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,SAAQ,KAAK,cAAY,KAAK,cAAa,KAAK,cAAY,KAAK,cAAa,KAAK,QAAM,GAAE,KAAK,QAAM;AAAA,GAAG,WAAW,GAAE;AAAC,IAAE;AAAiB,MAAI,IAAE,KAAK;AAAM,MAAI,IAAE,KAAK,QAAQ,GAAG,SAAQ,IAAE,KAAK,QAAQ,GAAG,SAAQ,IAAE,EAAE,QAAQ,GAAG,SAAQ,IAAE,EAAE,QAAQ,GAAG,SAAQ,IAAE,KAAK,QAAQ,GAAG,SAAQ,IAAE,KAAK,QAAQ,GAAG,SAAQ,IAAE,EAAE,QAAQ,GAAG,SAAQ,IAAE,EAAE,QAAQ,GAAG,SAAQ,IAAE,KAAK,KAAK,KAAK,IAAI,IAAE,GAAE,KAAG,KAAK,IAAI,IAAE,GAAE,KAAI,IAAE,KAAK,KAAK,KAAK,IAAI,IAAE,GAAE,KAAG,KAAK,IAAI,IAAE,GAAE,MAAI,GAAE,KAAE,GAAE,KAAG,MAAG,MAAE,KAAE,KAAK,YAAU,KAAE,KAAK,aAAW,KAAE,KAAK,aAAW,KAAE,KAAK,aAAW,MAAG,MAAG,MAAG;AAAE,MAAG,CAAC,KAAK,UAAS;AAAC,QAAG,KAAK,WAAS,MAAG,IAAE,IAAE,KAAG,KAAK,IAAI,MAAG,IAAE,KAAG,IAAE,KAAK,IAAI,OAAK,MAAG,KAAK,IAAI,MAAI,KAAK,UAAQ,EAAE,SAAQ,WAAY,MAAI;AAAC,WAAK,WAAS;AAAA,OAAK,IAAG,CAAC,KAAK,gBAAgB,KAAK,GAAE,KAAK,GAAE;AAAG,aAAM;AAAG,SAAK,QAAM;AAAA;AAAA,GAAI,iBAAiB,GAAE;AAAC,SAAO,oBAAoB,aAAY,KAAK;AAAA,GAAa,QAAQ,GAAE;AAAC,MAAG,EAAE,kBAAiB,EAAE,WAAS,AAAI,EAAE,QAAQ,WAAd;AAAqB,WAAO,KAAK,UAAQ,EAAE,SAAQ,OAAO,iBAAiB,aAAY,KAAK,aAAY,OAAO,iBAAiB,YAAW,KAAK,mBAAkB,OAAO,oBAAoB,aAAY,KAAK,UAAS;AAAG,MAAI,GAAE,GAAE,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,SAAQ,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG;AAAQ,MAAE,IAAE,KAAK,OAAM,IAAE,IAAE,KAAK,OAAM,KAAK,UAAW,MAAI;AAAC,QAAG,KAAK,WAAU;AAAC,UAAI,IAAE,IAAE,IAAE,GAAE,IAAE,KAAK,WAAW,GAAE,GAAE,KAAK,QAAO,IAAE,KAAK,eAAc,IAAE,KAAK,aAAW,KAAK,OAAM,IAAE,KAAK,YAAU,KAAK;AAAM,cAAO,KAAK;AAAA,aAAa;AAAA,aAAM;AAAA,aAAQ;AAAA,aAAM;AAAG,eAAE,KAAK,eAAa,KAAK,YAAW,KAAE,KAAK,SAAO,IAAG,KAAE,KAAG,GAAE,KAAE,KAAK,eAAa,KAAK,aAAY,KAAE,KAAK,SAAO,IAAG,KAAE,KAAG,GAAE,KAAE,KAAE,IAAE,KAAK,OAAM,IAAE,KAAE,IAAE,KAAK;AAAM;AAAA;AAAc,eAAE,KAAK,eAAa,KAAK,YAAW,KAAE,KAAK,SAAO,GAAE,KAAE,KAAK,eAAa,KAAK,aAAY,KAAE,KAAK,SAAO,GAAE,KAAE,KAAE,IAAE,KAAK,OAAM,IAAE,KAAE,IAAE,KAAK;AAAA;AAAM,QAAE,MAAI,EAAE,MAAK,KAAE,KAAG,EAAE,MAAI,EAAE,MAAK,KAAE,KAAG,EAAE,MAAI,EAAE,MAAK,KAAE,KAAG,EAAE,MAAI,EAAE,MAAK,KAAE;AAAA;AAAG,SAAK,IAAE,GAAE,KAAK,IAAE,GAAE,KAAK,MAAM,aAAY,EAAC,QAAO,MAAG,MAAK,KAAK,iBAAe,KAAK,MAAM,cAAa,EAAC,QAAO,MAAG,MAAK,KAAK;AAAA;AAAA,GAAmB,SAAS,GAAE;AAAC,SAAO,oBAAoB,aAAY,KAAK,UAAS,OAAO,oBAAoB,aAAY,KAAK,UAAS,OAAO,oBAAoB,WAAU,KAAK,WAAU,OAAO,oBAAoB,YAAW,KAAK,WAAU,KAAK,MAAM,aAAY,EAAC,QAAO,OAAG,MAAK,KAAK,iBAAe,KAAK,MAAM,cAAa,EAAC,QAAO,OAAG,MAAK,KAAK;AAAA,GAAgB,WAAU;AAAC,OAAK,YAAU,OAAO,iBAAiB,KAAK,SAAQ,KAAK,YAAW,KAAK;AAAA,GAAU,cAAa;AAAC,OAAK,YAAU,OAAO,oBAAoB,KAAK,SAAQ,KAAK;AAAA,GAAa,WAAW,GAAE;AAAC,IAAE;AAAiB,MAAI,IAAE,KAAK;AAAM,MAAI,IAAE,EAAE,UAAQ,EAAE;AAAW,MAAE,UAAU,UAAU,QAAQ,aAAW,IAAE,KAAG,IAAE,GAAE,KAAK,QAAO,KAAE,CAAC;AAAG,MAAI,IAAE,KAAK,KAAI,IAAG,KAAE,IAAE,KAAK,YAAU,IAAE,KAAK,aAAW,IAAE,KAAK,aAAW,IAAE,KAAK,aAAW;AAAE,MAAE,IAAE,KAAG,KAAK,IAAI,KAAG,IAAE,KAAK,IAAI,MAAK,MAAG,KAAK,IAAI;AAAI,MAAI,IAAE,IAAE,IAAE,QAAM;AAAS,MAAG,MAAI,KAAK,aAAY,MAAK,YAAU,GAAE,KAAK,MAAI,MAAI,KAAK,WAAU,MAAK,aAAW,WAAY,MAAI;AAAC,SAAK,UAAQ,OAAG,KAAK,MAAI,KAAK,OAAK;AAAA,KAAM,MAAK,KAAK,UAAQ,MAAG,CAAC,KAAK,gBAAgB,KAAK,GAAE,KAAK,GAAE;AAAG,WAAM;AAAG,OAAK,QAAM;AAAA,GAAG,YAAY,GAAE;AAAC,MAAI,IAAE,KAAK;AAAM,MAAE,KAAG;AAAE,MAAI,IAAE;AAAG,MAAI,MAAG,IAAE,IAAE,KAAK,YAAU,IAAE,KAAK,aAAW,IAAE,KAAK,aAAW,IAAE,KAAK,aAAW,IAAE,KAAG,KAAK,IAAI,KAAG,IAAE,KAAK,IAAI,MAAK,MAAG,KAAK,IAAI,KAAI,CAAC,KAAK,gBAAgB,KAAK,GAAE,KAAK,GAAE;AAAG,WAAM;AAAG,OAAK,QAAM;AAAA,GAAG,WAAW,GAAE;AAAC,IAAE;AAAiB,MAAI,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,UAAQ,EAAE,QAAQ,GAAG,UAAQ,GAAE,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,UAAQ,EAAE,QAAQ,GAAG,UAAQ;AAAE,OAAK,UAAW,MAAI;AAAC,QAAI,KAAE,IAAE,KAAK,OAAM,IAAE,IAAE,KAAK;AAAM,QAAG,KAAE,IAAG,MAAK,QAAM,KAAE,KAAK,cAAY,KAAK,IAAE,KAAK,IAAE,KAAK,cAAY,IAAE,KAAK,eAAa,KAAK,eAAc,MAAK,QAAM,KAAK,IAAE,KAAK,cAAY,KAAK,IAAI,MAAG,KAAK,IAAE,KAAK,cAAY,KAAK,IAAI,KAAG,KAAK,eAAa,KAAK,cAAY,KAAE,IAAE,KAAK,cAAY,KAAE,IAAG,KAAK,OAAM;AAAC,UAAI,IAAE,KAAK,QAAM,KAAK,YAAY,KAAG,KAAK,YAAY;AAAG,UAAE,KAAK,eAAa,KAAK,IAAG,MAAK,QAAM,KAAK,IAAE,KAAK,cAAa,KAAK,QAAM,KAAK,QAAM,KAAK,YAAY,KAAG,KAAK,YAAY,IAAG,KAAK,eAAa,KAAE,IAAE,KAAK,cAAY,KAAK,cAAY,KAAK,SAAO,KAAK,QAAM,GAAE,KAAK,eAAa,KAAK;AAAA;AAAkB,UAAE,IAAG,MAAK,QAAM,IAAE,KAAK,cAAY,KAAK,IAAE,KAAK,IAAE,KAAK,cAAY,GAAE,KAAK,eAAa,KAAK,eAAc,MAAK,QAAM,KAAK,IAAE,KAAK,cAAY,KAAK,IAAI,KAAG,KAAK,IAAE,KAAK,cAAY,KAAK,IAAI,IAAG,KAAK,eAAa,KAAK,cAAY,IAAE,IAAE,KAAK,cAAY,IAAE;AAAA;AAAA,GAAO,eAAe,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,IAAE,kBAAiB,OAAO,iBAAiB,aAAY,KAAK,gBAAe,OAAO,iBAAiB,WAAU,KAAK,gBAAe,OAAO,iBAAiB,aAAY,KAAK,gBAAe,OAAO,iBAAiB,YAAW,KAAK,gBAAe,KAAK,aAAW,GAAE,KAAK,aAAW,GAAE,KAAK,kBAAgB,GAAE,KAAK,kBAAgB,GAAE,KAAK,QAAM,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,SAAQ,KAAK,QAAM,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,SAAQ,KAAK,WAAS,KAAK,OAAM,KAAK,WAAS,KAAK,OAAM,KAAK,cAAY,KAAK,cAAa,KAAK,cAAY,KAAK,cAAa,KAAK,SAAO,KAAK,cAAY,KAAK,cAAa,MAAK,aAAW,IAAG,KAAK,MAAM,oBAAmB,EAAC,OAAM,KAAK,OAAM,QAAO,KAAK;AAAA,GAAS,cAAc,GAAE;AAAC,IAAE;AAAiB,MAAI,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,UAAQ,EAAE,QAAQ,GAAG,UAAQ,GAAE,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,UAAQ,EAAE,QAAQ,GAAG,UAAQ;AAAE,MAAI,IAAE,KAAK,GAAE,IAAE,KAAK,GAAE,IAAE,GAAE,IAAE;AAAE,MAAG,KAAK,WAAU;AAAC,QAAI,KAAE,KAAK,cAAa,KAAE,GAAE,IAAG,KAAE,GAAE;AAAG,QAAE,GAAE,KAAG,IAAE,GAAE,KAAG,GAAE,IAAE,GAAE,KAAG,IAAE,GAAE,KAAG,GAAE,IAAE,MAAI,KAAE,KAAG,IAAE,MAAI,KAAE;AAAA;AAAG,OAAK,UAAW,MAAI;AAAC,QAAI,KAAE,IAAE,KAAK,OAAM,IAAE,IAAE,KAAK;AAAM,QAAG,KAAK,cAAa,CAAI,KAAK,oBAAT,IAAyB,KAAK,WAAS,KAAE,IAAG,MAAK,QAAM,IAAE,KAAK,cAAY,MAAG,IAAE,IAAE,KAAK,WAAS,KAAE,KAAK,WAAS,KAAK,cAAY,GAAE,KAAK,eAAa,IAAE,KAAK,cAAY,MAAG,IAAE,IAAE,KAAK,cAAY,KAAE,KAAI,MAAK,QAAM,KAAK,IAAI,MAAG,KAAK,eAAa,IAAE,KAAK,IAAI,MAAG,KAAK,WAAS,IAAE,KAAK,WAAS,KAAK,aAAY,KAAK,eAAa,KAAK,cAAY,KAAK,YAAU,AAAI,KAAK,oBAAT,KAA2B,MAAK,WAAS,KAAE,IAAG,MAAK,QAAM,KAAK,WAAS,KAAE,KAAK,gBAAc,IAAE,KAAK,WAAS,KAAE,IAAE,KAAK,cAAa,KAAK,eAAa,KAAK,eAAc,MAAK,QAAM,IAAE,KAAK,cAAY,KAAK,IAAI,KAAE,KAAK,aAAW,IAAE,IAAE,KAAK,IAAI,KAAE,KAAK,YAAU,KAAK,cAAY,GAAE,KAAK,eAAa,IAAE,KAAK,cAAY,KAAK,IAAI,KAAE,KAAK,aAAW,IAAE,IAAE,KAAK,cAAY,KAAK,IAAI,KAAE,KAAK,YAAU,MAAK,KAAK,cAAa,CAAI,KAAK,oBAAT,IAAyB,KAAK,WAAS,IAAE,IAAG,MAAK,QAAM,IAAE,KAAK,cAAY,KAAG,IAAE,IAAE,KAAK,WAAS,IAAE,KAAK,WAAS,KAAK,cAAY,GAAE,KAAK,eAAa,IAAE,KAAK,cAAY,KAAG,IAAE,IAAE,KAAK,cAAY,IAAE,KAAI,MAAK,QAAM,KAAK,IAAI,KAAG,KAAK,eAAa,IAAE,KAAK,IAAI,KAAG,KAAK,WAAS,IAAE,KAAK,WAAS,KAAK,aAAY,KAAK,eAAa,KAAK,cAAY,KAAK,YAAU,AAAI,KAAK,oBAAT,KAA2B,MAAK,WAAS,IAAE,IAAG,MAAK,QAAM,KAAK,WAAS,IAAE,KAAK,gBAAc,IAAE,KAAK,WAAS,IAAE,IAAE,KAAK,cAAa,KAAK,eAAa,KAAK,eAAc,MAAK,QAAM,IAAE,KAAK,cAAY,KAAK,IAAI,IAAE,KAAK,aAAW,IAAE,IAAE,KAAK,IAAI,IAAE,KAAK,YAAU,KAAK,cAAY,GAAE,KAAK,eAAa,IAAE,KAAK,cAAY,KAAK,IAAI,IAAE,KAAK,aAAW,IAAE,IAAE,KAAK,cAAY,KAAK,IAAI,IAAE,KAAK,YAAU,MAAK,KAAK,cAAY,KAAK,OAAM;AAAC,UAAI,IAAE,KAAK,QAAM,KAAK,YAAY,KAAG,KAAK,YAAY;AAAG,UAAE,KAAK,eAAa,IAAG,MAAK,QAAM,IAAE,KAAK,cAAa,KAAK,QAAM,KAAK,QAAM,KAAK,YAAY,KAAG,KAAK,YAAY,MAAI,KAAK,QAAM;AAAA;AAAE,QAAG,KAAK,cAAY,KAAK,OAAM;AAAC,UAAI,IAAE,KAAK,QAAM,KAAK,YAAY,KAAG,KAAK,YAAY;AAAG,UAAE,KAAK,eAAa,IAAG,MAAK,QAAM,IAAE,KAAK,cAAa,KAAK,QAAM,KAAK,QAAM,KAAK,YAAY,KAAG,KAAK,YAAY,MAAI,KAAK,QAAM;AAAA;AAAA;AAAA,GAAO,qBAAoB;AAAC,MAAG,EAAC,OAAM,GAAE,OAAM,GAAE,cAAa,MAAG,MAAK,IAAE,IAAI;AAAM,SAAO,IAAE,MAAM,QAAQ,KAAG,IAAE,CAAC,GAAE,IAAG,IAAE,WAAW,EAAE,KAAI,IAAE,WAAW,EAAE,KAAI,CAAC,GAAE;AAAA,GAAI,cAAc,GAAE;AAAC,SAAO,oBAAoB,aAAY,KAAK,gBAAe,OAAO,oBAAoB,WAAU,KAAK,gBAAe,OAAO,oBAAoB,aAAY,KAAK,gBAAe,OAAO,oBAAoB,YAAW,KAAK;AAAA,GAAgB,UAAS;AAAC,EAAI,KAAK,UAAT,KAAgB,AAAI,KAAK,UAAT,KAAiB,MAAK,WAAS,QAAI,OAAO,oBAAoB,aAAY,KAAK,aAAY,OAAO,oBAAoB,WAAU,KAAK,UAAS,OAAO,oBAAoB,aAAY,KAAK,aAAY,OAAO,oBAAoB,YAAW,KAAK;AAAA,GAAU,YAAW;AAAC,OAAK,OAAK;AAAA,GAAI,WAAU;AAAC,OAAK,OAAK;AAAA,GAAI,YAAW;AAAC,OAAK,WAAS,OAAG,KAAK,QAAM,GAAE,KAAK,QAAM;AAAA,GAAG,SAAS,GAAE;AAAC,MAAG,EAAE,kBAAiB,CAAC,KAAK;AAAW,WAAO,KAAK,OAAK,OAAG,KAAK,UAAU,IAAG;AAAG,MAAG,EAAE,WAAS,AAAI,EAAE,QAAQ,WAAd;AAAqB,WAAO,KAAK,OAAK,OAAG,KAAK,UAAU,IAAG,KAAK,aAAY;AAAG,SAAO,iBAAiB,aAAY,KAAK,WAAU,OAAO,iBAAiB,WAAU,KAAK,YAAW,OAAO,iBAAiB,aAAY,KAAK,WAAU,OAAO,iBAAiB,YAAW,KAAK;AAAW,MAAI,GAAE,GAAE,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,SAAQ,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG;AAAQ,MAAE,IAAE,KAAK,cAAa,IAAE,IAAE,KAAK,cAAa,KAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,MAAM,cAAa,EAAC,QAAO,MAAG,MAAK,KAAK,kBAAgB,KAAK,MAAM,eAAc,EAAC,QAAO,MAAG,MAAK,KAAK;AAAA,GAAiB,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,GAAE,IAAE;AAAE,OAAI,GAAE,kBAAiB,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,SAAQ,IAAE,aAAY,IAAE,EAAE,UAAQ,EAAE,QAAQ,GAAG,UAAS,KAAK,UAAW,MAAI;AAAC,QAAI,IAAE,GAAE,IAAE,IAAE,KAAK,OAAM,IAAE,IAAE,KAAK;AAAM,QAAG,KAAI,KAAE,KAAK,cAAa,IAAE,KAAK,eAAc,KAAE,KAAG,IAAE,IAAE,IAAE,KAAK,QAAM,KAAK,IAAE,KAAK,IAAE,KAAK,QAAM,GAAE,IAAE,KAAG,IAAE,IAAE,IAAE,KAAK,QAAM,KAAK,IAAE,KAAK,IAAE,KAAK,QAAM,GAAE,KAAK,WAAU;AAAC,UAAI,KAAE,KAAK;AAAa,YAAG,GAAE,MAAK,MAAE,GAAE,KAAI,KAAE,KAAK,QAAM,GAAE,MAAK,MAAE,GAAE,KAAG,KAAK,QAAO,KAAG,GAAE,MAAK,KAAE,GAAE,KAAI,IAAE,KAAK,QAAM,GAAE,MAAK,KAAE,GAAE,KAAG,KAAK;AAAA;AAAO,SAAK,eAAa,IAAE,KAAK,eAAa,GAAE,KAAK,MAAM,cAAa,EAAC,QAAO,MAAG,MAAK,KAAK,kBAAgB,KAAK,MAAM,eAAc,EAAC,QAAO,MAAG,MAAK,KAAK;AAAA;AAAA,GAAoB,WAAW,GAAE,GAAE,GAAE;AAAC,MAAE,KAAG,KAAK,GAAE,IAAE,KAAG,KAAK,GAAE,IAAE,KAAG,KAAK;AAAM,MAAI,IAAE,EAAC,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,KAAG,IAAE,KAAK,YAAU,GAAE,IAAE,KAAK,aAAW;AAAE,UAAO,KAAK;AAAA,SAAa;AAAE,QAAE,KAAG,IAAE,KAAK,YAAW,KAAE,KAAG,GAAE,EAAE,KAAG,EAAE,KAAG,KAAK,YAAU,GAAE,EAAE,KAAG,IAAE,KAAK,aAAY,KAAE,KAAG,GAAE,EAAE,KAAG,EAAE,KAAG,KAAK,aAAW;AAAE;AAAA,SAAW;AAAA,SAAM;AAAA,SAAQ;AAAA,SAAM;AAAG,QAAE,KAAG,IAAE,KAAK,YAAW,KAAE,KAAG,IAAG,KAAE,KAAG,GAAE,EAAE,KAAG,EAAE,KAAG,KAAK,aAAW,GAAE,EAAE,KAAG,IAAE,KAAK,aAAY,KAAE,KAAG,IAAG,KAAE,KAAG,GAAE,EAAE,KAAG,EAAE,KAAG,KAAK,YAAU;AAAE;AAAA;AAAc,QAAE,KAAG,IAAE,KAAK,YAAW,KAAE,KAAG,GAAE,EAAE,KAAG,EAAE,KAAG,KAAK,YAAU,GAAE,EAAE,KAAG,IAAE,KAAK,aAAY,KAAE,KAAG,GAAE,EAAE,KAAG,EAAE,KAAG,KAAK,aAAW;AAAA;AAAE,SAAO;AAAA,GAAG,cAAa;AAAC,MAAI,IAAE,EAAC,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG;AAAG,SAAO,EAAE,KAAG,KAAK,cAAa,EAAE,KAAG,EAAE,KAAG,KAAK,OAAM,EAAE,KAAG,KAAK,cAAa,EAAE,KAAG,EAAE,KAAG,KAAK,OAAM;AAAA,GAAG,UAAU,GAAE;AAAC,SAAO,oBAAoB,aAAY,KAAK,WAAU,OAAO,oBAAoB,WAAU,KAAK,YAAW,OAAO,oBAAoB,aAAY,KAAK,WAAU,OAAO,oBAAoB,YAAW,KAAK,YAAW,KAAK,MAAM,cAAa,EAAC,QAAO,OAAG,MAAK,KAAK,kBAAgB,KAAK,MAAM,eAAc,EAAC,QAAO,OAAG,MAAK,KAAK;AAAA,GAAiB,eAAe,GAAE;AAAC,MAAI,IAAE,SAAS,cAAc,WAAU,IAAE,IAAI,SAAM,IAAE,KAAK,QAAO,IAAE,KAAK,WAAU,IAAE,KAAK,YAAW,IAAE,KAAK,cAAa,IAAE,KAAK;AAAa,aAAW,IAAE,IAAE;AAAC,MAAE,QAAM,KAAK,MAAM,KAAG,EAAE,SAAO,KAAK,MAAM;AAAA;AAAG,IAAE,SAAO,MAAI;AAAC,QAAG,AAAI,KAAK,UAAT,GAAe;AAAC,UAAI,KAAE,EAAE,WAAW,OAAM,IAAE;AAAE,WAAK,OAAK,CAAC,KAAK,QAAO,KAAE,OAAO,mBAAkB,AAAI,KAAK,YAAT,IAAiB,CAAC,KAAK,QAAO,KAAE,KAAK,IAAI,OAAO,KAAK;AAAW,UAAI,IAAE,KAAK,QAAM,GAAE,IAAE,KAAK,QAAM,GAAE,KAAE,IAAE,KAAK,QAAM,GAAE,KAAE,IAAE,KAAK,QAAM,GAAE,KAAG,MAAK,IAAE,IAAE,KAAK,YAAW,KAAE,KAAK,SAAO,KAAG,GAAE,KAAG,MAAK,IAAE,IAAE,KAAK,aAAY,KAAE,KAAK,SAAO,KAAG;AAAE,cAAO,EAAE,GAAE,IAAG,GAAE,QAAO;AAAA,aAAQ;AAAE,eAAK,OAAM,GAAE,IAAE,KAAK,OAAM,IAAE,KAAK,QAAO,GAAE,UAAU,GAAE,KAAE,KAAK,OAAM,KAAE,KAAK,OAAM,KAAE,KAAK,OAAM,KAAE,KAAK,UAAQ,GAAE,UAAU,GAAE,IAAE,IAAE,IAAE;AAAG;AAAA,aAAW;AAAA,aAAM;AAAG,eAAK,OAAM,GAAE,IAAE,KAAK,OAAM,IAAE,KAAK,QAAO,KAAE,KAAE,KAAK,QAAO,MAAE,KAAK,QAAM,KAAE,KAAK,SAAO,GAAE,KAAE,KAAE,KAAK,QAAO,MAAE,KAAK,QAAM,KAAE,KAAK,SAAO,GAAE,GAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,GAAE,UAAU,GAAE,IAAE,CAAC,KAAE,KAAE,KAAK,OAAM,KAAE,KAAK,OAAM,KAAE,KAAK,UAAS,OAAI,MAAE,MAAG,GAAE,MAAI,MAAE,MAAG,GAAE,GAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,GAAE,UAAU,GAAE,IAAE,CAAC,KAAE,IAAE,IAAE;AAAI;AAAA,aAAW;AAAA,aAAM;AAAG,eAAK,OAAM,GAAE,IAAE,KAAK,OAAM,IAAE,KAAK,QAAO,GAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,MAAG,KAAK,OAAM,MAAG,KAAK,OAAM,GAAE,UAAU,GAAE,CAAC,KAAE,KAAE,KAAK,OAAM,CAAC,KAAE,KAAE,KAAK,OAAM,KAAE,KAAK,OAAM,KAAE,KAAK,UAAS,IAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,GAAE,UAAU,GAAE,CAAC,KAAE,IAAE,CAAC,KAAE,IAAE,IAAE;AAAI;AAAA,aAAW;AAAA,aAAM;AAAG,eAAK,OAAM,GAAE,IAAE,KAAK,OAAM,IAAE,KAAK,QAAO,KAAE,KAAE,KAAK,QAAO,MAAE,KAAK,QAAM,KAAE,KAAK,SAAO,GAAE,KAAE,KAAE,KAAK,QAAO,MAAE,KAAK,QAAM,KAAE,KAAK,SAAO,GAAE,GAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,GAAE,UAAU,GAAE,CAAC,KAAE,KAAE,KAAK,OAAM,IAAE,KAAE,KAAK,OAAM,KAAE,KAAK,UAAS,OAAI,MAAE,MAAG,GAAE,MAAI,MAAE,MAAG,GAAE,GAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,GAAE,UAAU,GAAE,CAAC,KAAE,IAAE,IAAE,IAAE;AAAI;AAAA;AAAc,eAAK,OAAM,GAAE,IAAE,KAAK,OAAM,IAAE,KAAK,QAAO,GAAE,UAAU,GAAE,KAAE,KAAK,OAAM,KAAE,KAAK,OAAM,KAAE,KAAK,OAAM,KAAE,KAAK,UAAQ,GAAE,UAAU,GAAE,IAAE,IAAE,IAAE;AAAA;AAAG,SAAE;AAAA,WAAc;AAAC,UAAI,KAAE,IAAE,KAAK,OAAM,KAAE,IAAE,KAAK,OAAM,KAAE,EAAE,WAAW;AAAM,cAAO,GAAE,QAAO;AAAA,aAAQ;AAAE,YAAE,IAAE,KAAG,GAAE,UAAU,GAAE,GAAE,GAAE,IAAE;AAAG;AAAA,aAAW;AAAA,aAAM;AAAG,YAAE,IAAE,KAAG,GAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,GAAE,UAAU,GAAE,GAAE,CAAC,IAAE,IAAE;AAAG;AAAA,aAAW;AAAA,aAAM;AAAG,YAAE,IAAE,KAAG,GAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,GAAE,UAAU,GAAE,CAAC,IAAE,CAAC,IAAE,IAAE;AAAG;AAAA,aAAW;AAAA,aAAM;AAAG,YAAE,IAAE,KAAG,GAAE,OAAO,KAAG,IAAE,KAAK,KAAG,MAAK,GAAE,UAAU,GAAE,CAAC,IAAE,GAAE,IAAE;AAAG;AAAA;AAAc,YAAE,IAAE,KAAG,GAAE,UAAU,GAAE,GAAE,GAAE,IAAE;AAAA;AAAG,SAAE;AAAA;AAAU,MAAE;AAAA,KAAI,AAAS,KAAK,IAAI,OAAO,GAAE,OAA3B,UAAgC,GAAE,cAAY,cAAa,EAAE,MAAI,KAAK;AAAA,GAAM,YAAY,GAAE;AAAC,OAAK,eAAgB,OAAG;AAAC,MAAE,EAAE,UAAU,WAAS,KAAK,YAAW,KAAK;AAAA;AAAA,GAAiB,YAAY,GAAE;AAAC,OAAK,eAAgB,OAAG;AAAC,MAAE,OAAQ,QAAG,EAAE,KAAI,WAAS,KAAK,YAAW,KAAK;AAAA;AAAA,GAAgB,cAAa;AAAC,MAAG,CAAC,KAAK;AAAU,WAAM;AAAG,OAAK,YAAU,OAAG,WAAY,MAAI;AAAC,SAAK,YAAU;AAAA,KAAK;AAAI,MAAI,IAAE,KAAK,OAAM,IAAE,KAAK,OAAM,IAAE,KAAK;AAAM,MAAI,IAAE;AAAG,IAAE,MAAI,EAAC,OAAM,GAAG,OAAM,QAAO,GAAG;AAAO,MAAI,IAAG,MAAK,IAAE,KAAK,gBAAc,GAAE,IAAG,MAAK,IAAE,KAAK,gBAAc;AAAE,IAAE,IAAE,GAAE,EAAE,IAAE,GAAE,EAAE,MAAI,KAAK,MAAK,EAAE,MAAI,EAAC,OAAM,GAAG,KAAK,eAAc,QAAO,GAAG,KAAK,gBAAe,WAAU,SAAS,iBAAiB,QAAQ,oBAAoB,KAAG,KAAK,gBAAc,EAAE,OAAK;AAAA,gDAAmD,EAAE,gBAAgB,EAAE;AAAA,6BAAwD,gBAAgB;AAAA,qBAA6B,EAAE,qBAAqB,KAAK,wBAAwB,KAAK;AAAA,kBAA6C,iBAAiB,QAAQ,oBAAoB,KAAG,KAAK;AAAA;AAAA,eAA6C,KAAK,MAAM,YAAW,IAAG,KAAK,MAAM,aAAY;AAAA,GAAI,SAAQ;AAAC,MAAI,IAAE,IAAI;AAAM,IAAE,SAAO,MAAI;AAAC,SAAK,IAAE,WAAW,OAAO,iBAAiB,KAAK,MAAM,SAAS,QAAO,KAAK,IAAE,WAAW,OAAO,iBAAiB,KAAK,MAAM,SAAS,SAAQ,KAAK,YAAU,EAAE,OAAM,KAAK,aAAW,EAAE,QAAO,KAAK,WAAS,KAAK,QAAM,IAAE,KAAK,QAAM,KAAK,eAAc,KAAK,UAAW,MAAI;AAAC,WAAK,IAAE,CAAE,MAAK,YAAU,KAAK,YAAU,KAAK,SAAO,IAAG,MAAK,IAAE,KAAK,YAAU,KAAK,SAAO,GAAE,KAAK,IAAE,CAAE,MAAK,aAAW,KAAK,aAAW,KAAK,SAAO,IAAG,MAAK,IAAE,KAAK,aAAW,KAAK,SAAO,GAAE,KAAK,UAAQ,OAAG,KAAK,YAAU,KAAK,cAAa,KAAK,MAAM,YAAW,YAAW,KAAK,MAAM,WAAU,YAAW,WAAY,MAAI;AAAC,aAAK;AAAA,SAAgB;AAAA;AAAA,KAAQ,EAAE,UAAQ,MAAI;AAAC,SAAK,MAAM,WAAU,UAAS,KAAK,MAAM,YAAW;AAAA,KAAU,EAAE,MAAI,KAAK;AAAA,GAAM,cAAa;AAAC,MAAI,IAAE,GAAE,IAAE,KAAK,WAAU,IAAE,KAAK;AAAW,QAAM,IAAE,KAAK,KAAK,MAAM;AAAK,UAAO,EAAE;AAAA,SAAQ;AAAU,WAAK,YAAU,KAAK,KAAI,KAAE,KAAK,IAAE,KAAK,YAAW,KAAK,aAAW,IAAE,KAAK,KAAI,KAAE,KAAK,IAAE,KAAK;AAAY;AAAA,SAAU;AAAQ,UAAE,KAAK,GAAE,IAAE,IAAE,KAAK,WAAU,KAAG,GAAE,IAAE,KAAK,KAAI,KAAE,KAAK,GAAE,IAAE,IAAE,KAAK;AAAY;AAAA;AAAc,UAAG;AAAC,YAAI,IAAE,EAAE;AAAG,YAAG,AAAK,EAAE,OAAO,UAAd,IAAoB;AAAC,cAAE,EAAE,QAAQ,MAAK,KAAI,IAAE,WAAW;AAAG,gBAAM,IAAE,IAAE,KAAK;AAAU,cAAI,IAAE,GAAE,IAAE,EAAE;AAAG,UAAK,EAAE,OAAO,UAAd,MAAsB,KAAE,EAAE,QAAQ,MAAK,KAAI,IAAE,WAAW,IAAG,IAAE,IAAE,KAAK,aAAY,IAAE,KAAK,IAAI,GAAE;AAAA;AAAG,YAAG,AAAK,EAAE,OAAO,SAAd,MAAqB,KAAE,EAAE,QAAQ,KAAI,KAAI,IAAE,WAAW,KAAG,MAAI,KAAK,GAAE,IAAE,IAAE,KAAK,YAAW,AAAI,EAAE,WAAN,KAAc,AAAS,MAAT,QAAW;AAAC,cAAI,KAAE,EAAE;AAAG,UAAK,GAAE,OAAO,UAAd,MAAsB,MAAE,GAAE,QAAQ,MAAK,KAAI,IAAE,WAAW,KAAG,IAAE,IAAE,KAAK,aAAY,AAAK,GAAE,OAAO,SAAd,MAAqB,MAAE,GAAE,QAAQ,KAAI,KAAI,IAAE,WAAW,MAAG,MAAI,KAAK,GAAE,IAAE,IAAE,KAAK;AAAA;AAAA,eAAmB,GAAN;AAAS,YAAE;AAAA;AAAA;AAAG,SAAO;AAAA,GAAG,WAAW,GAAE,GAAE;AAAC,MAAG,AAAK,KAAK,SAAV,MAAgB,AAAO,KAAK,SAAZ;AAAiB;AAAO,OAAK,aAAY,KAAK,WAAS;AAAG,MAAI,IAAE,KAAK,GAAE,IAAE,KAAK;AAAE,MAAG,KAAK,WAAU;AAAC,UAAM,KAAE,KAAK,IAAI,KAAK,UAAQ,IAAE;AAAE,QAAI,KAAG,MAAE,KAAK,aAAW,KAAK,aAAW,KAAK,OAAM,KAAG,MAAE,KAAK,YAAU,KAAK,cAAY,KAAK;AAAM,QAAE,KAAE,IAAE,KAAE,GAAE,IAAE,KAAE,IAAE,KAAE;AAAA;AAAE,MAAI,IAAE,KAAG,WAAW,KAAK,gBAAe,IAAE,KAAG,WAAW,KAAK;AAAgB,EAAI,MAAJ,KAAO,AAAI,MAAJ,KAAQ,KAAE,MAAG,GAAE,IAAE,MAAG,IAAG,IAAE,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE,IAAE,IAAE,GAAE,KAAK,SAAQ,KAAE,IAAE,KAAK,YAAY,KAAG,KAAK,YAAY,KAAI,IAAE,KAAK,KAAI,KAAG,KAAE,KAAK,KAAG,KAAK,YAAY,KAAG,KAAK,YAAY,KAAI,KAAK,WAAW,GAAE;AAAA,GAAI,WAAW,GAAE,GAAE;AAAC,MAAG,KAAK,WAAU;AAAC,QAAI,IAAE,KAAK;AAAa,QAAE,EAAE,KAAG,EAAE,MAAK,KAAG,KAAE,EAAE,KAAG,EAAE,MAAI,KAAK,YAAY,KAAG,KAAK,YAAY,KAAI,IAAE,EAAE,KAAG,EAAE,MAAK,KAAG,KAAE,EAAE,KAAG,EAAE,MAAI,KAAK,YAAY,KAAG,KAAK,YAAY;AAAA;AAAI,OAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,sBAAqB,KAAK,UAAW,MAAI;AAAC,SAAK,eAAc,MAAK,IAAE,KAAK,SAAO,GAAE,KAAK,eAAc,MAAK,IAAE,KAAK,SAAO,GAAE,KAAK,aAAW,KAAK,SAAS,MAAK;AAAA;AAAA,GAAQ,UAAS;AAAC,OAAK,KAAI,KAAK,OAAK,IAAG,KAAK,QAAM,GAAE,KAAK,OAAK,OAAG,KAAK,SAAO,GAAE,KAAK,IAAE,GAAE,KAAK,IAAE,GAAE,KAAK,YAAU,GAAE,KAAK,aAAW,GAAE,KAAK,aAAY,KAAK,UAAW,MAAI;AAAC,SAAK;AAAA;AAAA,GAAiB,aAAY;AAAC,OAAK,SAAO,KAAK,UAAQ,KAAG,IAAE,KAAK,SAAO;AAAA,GAAG,cAAa;AAAC,OAAK,SAAO,KAAK,UAAQ,IAAE,IAAE,KAAK,SAAO;AAAA,GAAG,cAAa;AAAC,OAAK,SAAO;AAAA,GAAG,gBAAgB,GAAE,GAAE,GAAE;AAAC,MAAE,KAAG,KAAK,GAAE,IAAE,KAAG,KAAK,GAAE,IAAE,KAAG,KAAK;AAAM,MAAI,IAAE;AAAG,MAAG,KAAK,WAAU;AAAC,QAAI,IAAE,KAAK,WAAW,GAAE,GAAE,IAAG,IAAE,KAAK;AAAc,MAAE,MAAI,EAAE,MAAK,KAAE,QAAI,EAAE,MAAI,EAAE,MAAK,KAAE,QAAI,EAAE,MAAI,EAAE,MAAK,KAAE,QAAI,EAAE,MAAI,EAAE,MAAK,KAAE;AAAA;AAAI,SAAO;AAAA,KAAI,UAAS;AAAC,OAAK,UAAQ,aAAY,SAAS,cAAc,SAAO,UAAQ,AAAS,SAAS,iBAAlB,SAA+B,eAAa;AAAiB,MAAI,IAAE;AAAK,MAAI,IAAE,UAAU;AAAU,OAAK,QAAM,CAAC,CAAC,EAAE,MAAM,kCAAiC,kBAAkB,UAAU,UAAQ,OAAO,eAAe,kBAAkB,WAAU,UAAS,EAAC,OAAM,SAAS,IAAE,GAAE,GAAE;AAAC,aAAQ,IAAE,KAAK,KAAK,UAAU,GAAE,GAAG,MAAM,KAAK,KAAI,IAAE,EAAE,QAAO,IAAE,IAAI,WAAW,IAAG,IAAE,GAAE,IAAE,GAAE;AAAI,QAAE,KAAG,EAAE,WAAW;AAAG,OAAE,IAAI,KAAK,CAAC,IAAG,EAAC,MAAK,EAAE,QAAM;AAAA,QAAkB,KAAK,eAAc,KAAK;AAAA,GAAc,YAAW;AAAC,SAAO,oBAAoB,aAAY,KAAK,WAAU,OAAO,oBAAoB,WAAU,KAAK,YAAW,OAAO,oBAAoB,aAAY,KAAK,WAAU,OAAO,oBAAoB,YAAW,KAAK,YAAW,KAAK;AAAA;AAAiB,YAAE;AAAmB,IAAM,IAAE,EAAC,KAAI,GAAE,OAAM;AAArB,IAAoC,IAAE,CAAC;AAAvC,IAA8C,IAAE,EAAC,OAAM;AAAvD,IAA2E,IAAE,CAAC;AAA9E,IAAqF,IAAE,EAAC,KAAI;AAAG,cAAI,EAAE,SAAO,SAAS,GAAE,GAAE,GAAE,IAAE,IAAE,IAAE;AAAC,SAAO,aAAI,mBAAE,OAAM,EAAC,OAAM,eAAc,KAAI,WAAU,aAAY,EAAE,OAAM,GAAE,MAAI,IAAI,OAAI,EAAE,YAAU,EAAE,SAAS,GAAG,MAAI,YAAW,EAAE,OAAM,GAAE,MAAI,IAAI,OAAI,EAAE,eAAa,EAAE,YAAY,GAAG,QAAK,CAAC,EAAE,OAAM,cAAI,mBAAE,OAAM,GAAE,CAAC,eAAE,gBAAE,OAAM,EAAC,OAAM,sBAAqB,OAAM,eAAE,EAAC,OAAM,EAAE,YAAU,MAAK,QAAO,EAAE,aAAW,MAAK,WAAU,WAAS,EAAE,QAAM,MAAI,EAAE,QAAM,mBAAiB,EAAE,IAAE,EAAE,QAAM,QAAM,EAAE,IAAE,EAAE,QAAM,kBAAgB,KAAG,EAAE,SAAO,aAAU,CAAC,gBAAE,OAAM,EAAC,KAAI,EAAE,MAAK,KAAI,eAAc,KAAI,gBAAc,MAAK,GAAE,KAAI,IAAG,CAAC,CAAC,OAAE,CAAC,EAAE,gBAAc,mBAAE,IAAG,OAAI,gBAAE,OAAM,EAAC,OAAM,eAAE,CAAC,oBAAmB,EAAC,gBAAe,EAAE,QAAM,CAAC,EAAE,MAAK,gBAAe,EAAE,MAAK,iBAAgB,EAAE,cAAY,aAAY,EAAE,MAAK,GAAE,KAAG,IAAI,OAAI,EAAE,aAAW,EAAE,UAAU,GAAG,MAAI,cAAa,EAAE,MAAK,GAAE,KAAG,IAAI,OAAI,EAAE,aAAW,EAAE,UAAU,GAAG,QAAK,MAAK,KAAI,eAAE,gBAAE,OAAM,EAAC,OAAM,oBAAmB,OAAM,eAAE,EAAC,OAAM,EAAE,QAAM,MAAK,QAAO,EAAE,QAAM,MAAK,WAAU,iBAAe,EAAE,eAAa,QAAM,EAAE,eAAa,cAAW,CAAC,gBAAE,QAAO,GAAE,CAAC,gBAAE,OAAM,EAAC,OAAM,eAAE,EAAC,OAAM,EAAE,YAAU,MAAK,QAAO,EAAE,aAAW,MAAK,WAAU,WAAS,EAAE,QAAM,MAAI,EAAE,QAAM,mBAAkB,GAAE,IAAE,EAAE,gBAAc,EAAE,QAAM,QAAO,GAAE,IAAE,EAAE,gBAAc,EAAE,QAAM,kBAAgB,KAAG,EAAE,SAAO,WAAS,KAAI,EAAE,MAAK,KAAI,iBAAe,MAAK,IAAG,MAAK,gBAAE,QAAO,EAAC,OAAM,6BAA4B,aAAY,EAAE,MAAK,GAAE,KAAG,IAAI,OAAI,EAAE,YAAU,EAAE,SAAS,GAAG,MAAI,cAAa,EAAE,MAAK,GAAE,KAAG,IAAI,OAAI,EAAE,YAAU,EAAE,SAAS,GAAG,QAAK,MAAK,KAAI,EAAE,OAAM,cAAI,mBAAE,QAAO,EAAC,KAAI,GAAE,OAAM,aAAY,OAAM,eAAE,EAAC,KAAI,EAAE,SAAS,UAAO,gBAAE,EAAE,SAAS,SAAO,WAAM,gBAAE,EAAE,SAAS,SAAQ,MAAI,mBAAE,IAAG,OAAI,EAAE,WAAS,mBAAE,IAAG,QAAK,cAAI,mBAAE,QAAO,GAAE,CAAC,gBAAE,QAAO,EAAC,OAAM,oBAAmB,aAAY,EAAE,MAAK,GAAE,KAAG,QAAG,EAAE,eAAe,IAAE,OAAG,MAAG,GAAE,KAAI,cAAa,EAAE,MAAK,GAAE,KAAG,QAAG,EAAE,eAAe,IAAE,OAAG,MAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,oBAAmB,aAAY,EAAE,MAAK,GAAE,KAAG,QAAG,EAAE,eAAe,IAAE,MAAG,OAAG,GAAE,KAAI,cAAa,EAAE,MAAK,GAAE,KAAG,QAAG,EAAE,eAAe,IAAE,MAAG,OAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,oBAAmB,aAAY,EAAE,MAAK,GAAE,KAAG,QAAG,EAAE,eAAe,IAAE,OAAG,MAAG,GAAE,KAAI,cAAa,EAAE,MAAK,GAAE,KAAG,QAAG,EAAE,eAAe,IAAE,OAAG,MAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,oBAAmB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,OAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,OAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,qBAAoB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,MAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,MAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,qBAAoB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,OAAG,MAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,OAAG,MAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,qBAAoB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,MAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,MAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,qBAAoB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,OAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,OAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,qBAAoB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,OAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,OAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,qBAAoB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,MAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,MAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,qBAAoB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,OAAG,MAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,OAAG,MAAG,GAAE,OAAK,MAAK,KAAI,gBAAE,QAAO,EAAC,OAAM,qBAAoB,aAAY,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,MAAG,GAAE,KAAI,cAAa,EAAE,OAAM,GAAE,MAAI,QAAG,EAAE,eAAe,IAAE,MAAG,MAAG,GAAE,OAAK,MAAK,SAAQ,IAAG,CAAC,CAAC,OAAE,EAAE,cAAa;AAAA,GAAM,EAAE,YAAU;AAAkB,AAAa,OAAO,UAApB,eAA4B,OAAO,OAAK,OAAO,IAAI,UAAU,IAAI,UAAU,cAAa;AAAG,IAAM,IAAE,EAAC,SAAQ,SAAQ,SAAQ,SAAS,GAAE;AAAC,IAAE,UAAU,cAAa;AAAA,GAAI,YAAW;;;ACA7j5B,IAAO,sBAAQ;", "names": []}