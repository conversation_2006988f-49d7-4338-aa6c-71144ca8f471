<template>
    <div>
        <el-form :class="{ 'h-50': !showSearch }" :model="queryParams" ref="queryRef" inline label-width="100px">
            <el-form-item label="主叫号码" prop="callFrom">
                <el-input v-model="queryParams.callFrom" placeholder="请输入主叫号码" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="被叫号码" prop="callTo">
                <el-input v-model="queryParams.callTo" placeholder="请输入被叫号码" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="机构名称" prop="teamName">
                <el-input v-model="queryParams.teamName" placeholder="请输入机构名称" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="呼叫类型" prop="callroter">
                <el-select v-model="queryParams.callroter" placeholder="请选择呼叫类型" clearable style="width: 240px">
                    <el-option label="全部" value="全部" />
                    <el-option label="呼入" value="0" />
                    <el-option label="呼出" value="1" />
                </el-select>
            </el-form-item>
            <el-form-item label="催收员" prop="odvName">
                <el-input v-model="queryParams.odvName" placeholder="请输入催收员" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="呼叫时间" prop="callTime">
                <el-date-picker v-model="queryParams.callTime" style="width: 240px" value-format="YYYY-MM-DD"
                    type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item label="通话时长">
                <div class="range-area" style="width: 240px">
                    <el-input v-model="queryParams.agentDuration1" clearable />
                    <span>-</span>
                    <el-input v-model="queryParams.agentDuration2" clearable />
                </div>
            </el-form-item>
            <el-form-item label="质检状态" prop="qualityStatus">
                <el-select v-model="queryParams.qualityStatus" placeholder="请选择质检状态" clearable style="width: 240px">
                    <el-option v-for="(v, i) in qcStatusOptions" :label="v.info" :value="v.code" :key="i" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>

        <div class="mb8">
            <el-button v-if="checkPermi(['callLog:callRecord:createtask']) && activeTab == 'y'" plain :disabled="single"
                type="primary" @click="addDownTask">创建下载录音任务</el-button>
            <el-button v-hasPermi="['callLog:callRecord:export']" plain :disabled="single" type="info"
                @click="downloadRecord">导出记录</el-button>
            <span v-if="checkPermi(['callLog:callRecord:recordQc']) && activeTab == 'y'" class="ml20 mr20">
                <el-button plain :disabled="single" type="warning" @click="handleRecordQc()">录音质检 </el-button>
                <el-tooltip effect="dark" content="所选录音按优先顺序进行质检" placement="top">
                    <el-icon class="icon-question" color="#f59a24">
                        <QuestionFilled />
                    </el-icon>
                </el-tooltip>
            </span>
        </div>

        <el-row class="hint">
            <el-checkbox-group v-model="checkedType" @change="checkedTypeChange">
                <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
                    :indeterminate="item.indeterminate" :disabled="dataList.length === 0" />
            </el-checkbox-group>

            <el-tooltip effect="light" placement="bottom-start">
                <div>
                    <svg-icon class="hint-item text-warning" icon-class="question" />
                </div>
                <template #content>
                    <div class="info-tip">
                        <el-icon class="info-tip-icon" :size="16">
                            <warning-filled />
                        </el-icon>
                        <div>
                            <p>注：</p>
                            <p>
                                1、默认只展示当月的通话记录,请于晚22:00前创建任务，否则无法下载；录音下载任务创建后，将于次日（24小时后）进行批量下载；
                            </p>
                            <p>
                                2、录音按照双方合同约束默认保留储存期，服务器将自动彻底删除录音，请系统使用方即时保留录音到本地，避免造成录音丢失。
                            </p>
                        </div>
                    </div>
                </template>
            </el-tooltip>
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"
                :types="[1, 2, 3]" />
        </el-row>

        <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
            <el-tab-pane v-for="item in answerOptions" :key="item.code" :label="item.info" :name="item.code" />
        </el-tabs>

        <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" :selectable="checkSelectable" align="right" width="30px" />
            <el-table-column label="案件ID" key="id" prop="id" align="left" v-if="columns[0].visible" :width="120">
                <template #default="{ row }">
                    <router-link v-if="row.caseId" class="text-primary"
                        :to="`/risk/calllog-details/caseDetails/${row.caseId}`">{{
                            row.caseId }}</router-link>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column label="呼叫时间" align="center" key="callTime" prop="callTime" width="160"
                v-if="columns[1].visible" show-overflow-tooltip />
            <el-table-column label="通话时长（s）" align="center" key="agentDuration" prop="agentDuration" :width="120"
                v-if="columns[2].visible" />
            <el-table-column label="主叫号码" align="center" key="callFrom" prop="callFrom" :width="120"
                v-if="columns[3].visible" />
            <el-table-column label="被叫号码" align="center" key="callTo" prop="callTo" :width="120"
                v-if="columns[4].visible" />
            <el-table-column label="借款人" align="center" key="borrower" prop="borrower" :width="120"
                v-if="columns[5].visible" />
            <el-table-column label="外显号码" align="center" key="number" prop="number" :width="120"
                v-if="columns[6].visible" />
            <el-table-column label="呼叫类型" align="center" key="callroter" prop="callroter" :width="120"
                :formatter="callroterChange" v-if="columns[7].visible" />
            <el-table-column label="机构名称" align="center" key="teamName" prop="teamName" :min-width="140"
                show-overflow-tooltip v-if="columns[8].visible" />
            <el-table-column label="催收员" align="center" key="odvName" prop="odvName" :width="120"
                v-if="columns[9].visible" />
            <el-table-column label="操作" width="400" fixed="right">
                <template #default="scope">
                    <div class="play-button"
                        :key="`audioRef${queryParams.pageNum + queryParams.pageSize + scope.$index}`">
                        <musicPlayer v-if="checkPermi(['callLog:callRecord:play']) && scope.row.recordUrl"
                            :ref="`audioRef${scope.$index}`" @stopCheck="stopCheck(scope.$index)"
                            :audioSrc="scope.row.serviceHost + scope.row.recordUrl" v-model:audioObj="scope.row" />
                    </div>
                    <el-button v-show="scope.row.recordUrl" type="primary" v-hasPermi="['callLog:callRecord:download']"
                        link @click="downVideo(scope.row.serviceHost + scope.row.recordUrl)">下载</el-button>
                    <el-button v-if="[1, 2].includes(scope.row.qualityStatus)" type="primary"
                        v-hasPermi="['callLog:callRecord:textPlay']" link
                        @click="handleTextPlay(scope.row)">文本播放</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="Calllog">
import { checkPermi } from "@/utils/permission";
import { getQualityStatusApi } from "@/api/options";
import musicPlayer from "@/components/MusicPlay/musicPlayer";
import { calllogs, getAnswerStatus, createDownloadTask, qualityOrderApi } from "@/api/seat/calllog";
const router = useRouter()
const route = useRoute()
const { proxy } = getCurrentInstance();
const single = ref(true); //能否操作
const selectedArr = ref([]); //表格选中数据
const ids = ref([]);
const answerOptions = ref([]);
const qcStatusOptions = ref([]);
const activeTab = ref(undefined);
const showSearch = ref(false);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const multipleTableRef = ref();
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    callType: 0,
    recycle: false, //是否搜索结果全选
    allQuery: false, //是否搜索结果全选
});

getAnswerStatus().then((res) => {
    answerOptions.value = res.data;
    activeTab.value = res.data[0].code;
    answerOptions.value.push({ code: "全部", info: "全部" });
    getList();
});

const handledForm = ref({}); //处理后的搜索条件
const rangFields = ["callTime"];
const columns = ref([
    { key: 0, label: `案件ID`, visible: true },
    { key: 1, label: `呼叫时间`, visible: true },
    { key: 2, label: `通话时长（s）`, visible: true },
    { key: 3, label: `主叫号码`, visible: true },
    { key: 4, label: `被叫号码`, visible: true },
    { key: 5, label: `借款人`, visible: true },
    { key: 6, label: `外显号码`, visible: true },
    { key: 7, label: `呼叫类型`, visible: true },
    { key: 8, label: `机构名称`, visible: true },
    { key: 9, label: `催收员`, visible: true },
]);

const checkedType = ref([]);
const checkStatus = ref([
    { label: "本页选中", is_settle: "1", indeterminate: false },
    { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);
getList()
//获取列表数据
function getList() {
    loading.value = true;
    handledForm.value = proxy.addFieldsRange(queryParams.value, rangFields);
    handledForm.value.answer = activeTab.value == "全部" ? undefined : activeTab.value;
    handledForm.value.callroter =
        handledForm.value.callroter == "全部" ? undefined : handledForm.value.callroter;
    calllogs(handledForm.value).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    }).finally(() => loading.value = false);
}

// 录音质检
function handleRecordQc() {
    const title = '提交优先质检'
    const content = `该操作将会对所选录音进行优先质检，确认进行操作吗?`
    proxy.$modal.confirm(content, title).then(() => {
        let reqForm = JSON.parse(JSON.stringify(handledForm.value));
        if (!queryParams.value.allQuery) {
            reqForm.ids = ids.value;
            reqForm.allQuery = false;
        }
        qualityOrderApi(reqForm).then((res) => {
            proxy.$modal.confirm(res.msg, "操作成功")
        });

    })
}

//搜索
function handleQuery() {
    ids.value = [];
    selectedArr.value = [];
    queryParams.value.pageNum = 1;
    getList();
}

//重置
function resetQuery() {
    proxy.resetForm("queryRef");
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        callType: 0,
        recycle: false,
        allQuery: false, //是否搜索结果全选
    };
    selectedArr.value = [];
    ids.value = [];
    getList();
}
getQualityStatusFun()
function getQualityStatusFun() {
    getQualityStatusApi().then((res) => {
        qcStatusOptions.value = res.data
    })
}
//当有录音播放的时候停止其他录音
function stopCheck(count) {
    dataList.value.forEach((item, index) => {
        if (index !== count) {
            proxy.$refs[`audioRef${index}`].stopAudio();
        }
    });
}

//创建下载录音任务
function addDownTask() {
    let form = {};
    if (queryParams.value.allQuery) {
        form = handledForm.value;
    } else {
        form.ids = ids.value;
        form.allQuery = false;
    }
    createDownloadTask(form).then((res) => {
        proxy.$modal.confirm(res.msg, "操作成功")
    });
}

//导出记录
function downloadRecord() {
    let form = handledForm.value;
    if (!queryParams.value.allQuery) {
        form = { ids: ids.value, allQuery: false }
    }
    const apiPath = "caseManage/call/record/export"
    proxy.downloadforjson(apiPath, { ...form }, `通话记录_${new Date().getTime()}.xlsx`);
}

function handleTextPlay(row) {
    const query = { callRecordId: row.id, path: route.path }
    const path = `/risk/calllog-details/textPlay`
    router.push({ path, query })
}

//tab选择
function tabChange() {
    selectedArr.value = [];
    ids.value = [];
    nextTick(() => {
        getList();
    });
}

//表格选中
function selectTable() {
    return new Promise((reslove, reject) => {
        try {
            dataList.value.map((item, index) => {
                multipleTableRef.value.toggleRowSelection(item, true);
            });
            reslove(true);
        } catch (error) {
            reject(error);
        }
    });
}

//全选类型
function checkedTypeChange(val) {
    checkedType.value.length > 1 && checkedType.value.shift(); //单选
    if (checkedType.value.length === 0) {
        //全不选
        multipleTableRef.value.clearSelection();
        checkStatus.value[0].indeterminate = false;
    } else {
        dataList.value.length > 0 &&
            dataList.value.map((item) => {
                multipleTableRef.value.toggleRowSelection(item, true);
            });
    }
    if (checkedType.value[0] == "搜索结果全选") {
        checkStatus.value[0].indeterminate = false;
        queryParams.value.allQuery = true;
        handledForm.value.allQuery = true;
    } else {
        queryParams.value.allQuery = false;
        handledForm.value.allQuery = false;
    }
}

//选择列表
function handleSelectionChange(selection) {
    selectedArr.value = selection;
    if (checkedType.value[0] != "搜索结果全选") {
        if (selectedArr.value.length == dataList.value?.length) {
            checkedType.value[0] = "本页选中";
        } else {
            checkedType.value = [];
        }
    }
}

//表格行能否选择
function checkSelectable(row, index) {
    return !queryParams.value.allQuery;
}

watch(dataList, (newval, preval) => {
    if (newval.length > 0) {
        //处理禁用表格复选框时无法选中的情况
        if (queryParams.value.allQuery) {
            queryParams.value.allQuery = false;
            handledForm.value.allQuery = false;
            nextTick(() => {
                selectTable().finally(() => {
                    queryParams.value.allQuery = true;
                    handledForm.value.allQuery = true;
                });
            });
        } else {
            dataList.value?.forEach((item, index) => {
                if (ids.value?.includes(item.id)) {
                    proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
                }
            });
        }
    }
});

watch(selectedArr, (newval) => {
    nextTick(() => {
        let select = newval;
        let pageIds = select.map((item) => item.id).filter((item) => item);
        //添加选择的数据
        let caseTableIds = dataList.value.map((item) => item.id);
        ids.value = ids.value.filter((item) => !caseTableIds.includes(item));
        ids.value = [...new Set([...ids.value, ...pageIds])];
        single.value = !(ids.value?.length > 0);
    });
});

//呼叫类型转换
function callroterChange(row) {
    //手动呼叫：callOutManual，API点击呼叫：clickCallOut，呼入：inbound
    if (row.callroter) {
        return ["呼入", "呼出", "", "磋商", "内呼"][row.callroter];
    } else {
        return "--";
    }
}

//下载录音
function downVideo(url) {
    window.open(url);
}
</script>

<style lang="scss" scoped>
.h-50 {
    height: 50px;
    overflow: hidden;
}

.h-auto {
    height: auto !important;
}

:deep(.hint .el-tooltip__trigger) {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
}

.hint-item {
    font-size: 18px;
    // color: #5a5e66;
    cursor: pointer;
}

.info-tip {
    border: unset;
}

:deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
}

.play-button {
    display: inline-block;
    width: 70%;
    vertical-align: middle;
}

.download-button {
    display: inline-block;
    width: 30%;
    vertical-align: middle;
    margin-top: 5px;
}
</style>