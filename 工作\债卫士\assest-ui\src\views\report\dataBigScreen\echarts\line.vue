<template>
    <div :id="idName" class="echarts-area"> </div>
</template>

<script setup>
import * as echarts from 'echarts';
const props = defineProps({
    dataInfo: { type: Object, default: {} },
    idName: { type: String, default: 'line-main' },
})
function setEcharts() {
    var chartDom = document.getElementById(props.idName);
    var myChart = echarts.init(chartDom);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        legend: {
            textStyle: {
                color: '#ffffff' // 设置图例文字颜色为红色
            },
            data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                axisLabel: {
                    textStyle: {
                        color: '#ffffff' // 设置颜色为红色
                    }
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    textStyle: {
                        color: '#ffffff' // 设置颜色为红色
                    }
                }
            }
        ],
        series: [
            {
                name: 'Email',
                type: 'line',
                stack: 'Total',
                itemStyle: {
                    color: '#28a6cc',
                    backgroundColor: 'red'
                },
                smooth: true,// 开启平滑曲线
                areaStyle: {},
                emphasis: {
                    focus: 'series'
                },
                data: [120, 132, 101, 134, 90, 230, 210]
            },
            {
                name: 'Union Ads',
                type: 'line',
                stack: 'Total',
                itemStyle: {
                    opacity: 1,
                    color: '#2456bd',
                    backgroundColor: 'red'
                },
                emphasis: {
                    focus: 'series'
                },
                smooth: true,// 开启平滑曲线
                areaStyle: {},
                data: [220, 182, 191, 234, 290, 330, 310]
            },
        ]
    };

    option && myChart.setOption(option);
}
onMounted(() => {
    setEcharts()
})
</script>

<style lang="scss" scoped>
.echarts-area {
    width: 100%;
    height: 250px;
}
</style>