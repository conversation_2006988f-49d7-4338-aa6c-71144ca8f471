import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/autoresize/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/autoresize/plugin.js"() {
    (function() {
      "use strict";
      var Cell = function(initial) {
        var value = initial;
        var get = function() {
          return value;
        };
        var set = function(v) {
          value = v;
        };
        return {
          get,
          set
        };
      };
      var hasOwnProperty = Object.hasOwnProperty;
      var has = function(obj, key) {
        return hasOwnProperty.call(obj, key);
      };
      var global$2 = tinymce.util.Tools.resolve("tinymce.PluginManager");
      var global$1 = tinymce.util.Tools.resolve("tinymce.Env");
      var global = tinymce.util.Tools.resolve("tinymce.util.Delay");
      var fireResizeEditor = function(editor) {
        return editor.fire("ResizeEditor");
      };
      var getAutoResizeMinHeight = function(editor) {
        return editor.getParam("min_height", editor.getElement().offsetHeight, "number");
      };
      var getAutoResizeMaxHeight = function(editor) {
        return editor.getParam("max_height", 0, "number");
      };
      var getAutoResizeOverflowPadding = function(editor) {
        return editor.getParam("autoresize_overflow_padding", 1, "number");
      };
      var getAutoResizeBottomMargin = function(editor) {
        return editor.getParam("autoresize_bottom_margin", 50, "number");
      };
      var shouldAutoResizeOnInit = function(editor) {
        return editor.getParam("autoresize_on_init", true, "boolean");
      };
      var isFullscreen = function(editor) {
        return editor.plugins.fullscreen && editor.plugins.fullscreen.isFullscreen();
      };
      var wait = function(editor, oldSize, times, interval, callback) {
        global.setEditorTimeout(editor, function() {
          resize(editor, oldSize);
          if (times--) {
            wait(editor, oldSize, times, interval, callback);
          } else if (callback) {
            callback();
          }
        }, interval);
      };
      var toggleScrolling = function(editor, state) {
        var body = editor.getBody();
        if (body) {
          body.style.overflowY = state ? "" : "hidden";
          if (!state) {
            body.scrollTop = 0;
          }
        }
      };
      var parseCssValueToInt = function(dom, elm, name, computed) {
        var value = parseInt(dom.getStyle(elm, name, computed), 10);
        return isNaN(value) ? 0 : value;
      };
      var shouldScrollIntoView = function(trigger) {
        if ((trigger === null || trigger === void 0 ? void 0 : trigger.type.toLowerCase()) === "setcontent") {
          var setContentEvent = trigger;
          return setContentEvent.selection === true || setContentEvent.paste === true;
        } else {
          return false;
        }
      };
      var resize = function(editor, oldSize, trigger) {
        var dom = editor.dom;
        var doc = editor.getDoc();
        if (!doc) {
          return;
        }
        if (isFullscreen(editor)) {
          toggleScrolling(editor, true);
          return;
        }
        var docEle = doc.documentElement;
        var resizeBottomMargin = getAutoResizeBottomMargin(editor);
        var resizeHeight = getAutoResizeMinHeight(editor);
        var marginTop = parseCssValueToInt(dom, docEle, "margin-top", true);
        var marginBottom = parseCssValueToInt(dom, docEle, "margin-bottom", true);
        var contentHeight = docEle.offsetHeight + marginTop + marginBottom + resizeBottomMargin;
        if (contentHeight < 0) {
          contentHeight = 0;
        }
        var containerHeight = editor.getContainer().offsetHeight;
        var contentAreaHeight = editor.getContentAreaContainer().offsetHeight;
        var chromeHeight = containerHeight - contentAreaHeight;
        if (contentHeight + chromeHeight > getAutoResizeMinHeight(editor)) {
          resizeHeight = contentHeight + chromeHeight;
        }
        var maxHeight = getAutoResizeMaxHeight(editor);
        if (maxHeight && resizeHeight > maxHeight) {
          resizeHeight = maxHeight;
          toggleScrolling(editor, true);
        } else {
          toggleScrolling(editor, false);
        }
        if (resizeHeight !== oldSize.get()) {
          var deltaSize = resizeHeight - oldSize.get();
          dom.setStyle(editor.getContainer(), "height", resizeHeight + "px");
          oldSize.set(resizeHeight);
          fireResizeEditor(editor);
          if (global$1.browser.isSafari() && global$1.mac) {
            var win = editor.getWin();
            win.scrollTo(win.pageXOffset, win.pageYOffset);
          }
          if (editor.hasFocus() && shouldScrollIntoView(trigger)) {
            editor.selection.scrollIntoView();
          }
          if (global$1.webkit && deltaSize < 0) {
            resize(editor, oldSize, trigger);
          }
        }
      };
      var setup = function(editor, oldSize) {
        editor.on("init", function() {
          var overflowPadding = getAutoResizeOverflowPadding(editor);
          var dom = editor.dom;
          dom.setStyles(editor.getDoc().documentElement, { height: "auto" });
          dom.setStyles(editor.getBody(), {
            "paddingLeft": overflowPadding,
            "paddingRight": overflowPadding,
            "min-height": 0
          });
        });
        editor.on("NodeChange SetContent keyup FullscreenStateChanged ResizeContent", function(e) {
          resize(editor, oldSize, e);
        });
        if (shouldAutoResizeOnInit(editor)) {
          editor.on("init", function() {
            wait(editor, oldSize, 20, 100, function() {
              wait(editor, oldSize, 5, 1e3);
            });
          });
        }
      };
      var register = function(editor, oldSize) {
        editor.addCommand("mceAutoResize", function() {
          resize(editor, oldSize);
        });
      };
      function Plugin() {
        global$2.add("autoresize", function(editor) {
          if (!has(editor.settings, "resize")) {
            editor.settings.resize = false;
          }
          if (!editor.inline) {
            var oldSize = Cell(0);
            register(editor, oldSize);
            setup(editor, oldSize);
          }
        });
      }
      Plugin();
    })();
  }
});

// node_modules/tinymce/plugins/autoresize/index.js
var require_autoresize = __commonJS({
  "node_modules/tinymce/plugins/autoresize/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_autoresize
var tinymce_plugins_autoresize_default = require_autoresize();
export {
  tinymce_plugins_autoresize_default as default
};
//# sourceMappingURL=tinymce_plugins_autoresize.js.map
