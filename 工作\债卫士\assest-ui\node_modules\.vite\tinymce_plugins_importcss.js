import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/importcss/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/importcss/plugin.js"() {
    (function() {
      "use strict";
      var global$4 = tinymce.util.Tools.resolve("tinymce.PluginManager");
      var typeOf = function(x) {
        var t = typeof x;
        if (x === null) {
          return "null";
        } else if (t === "object" && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === "Array")) {
          return "array";
        } else if (t === "object" && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === "String")) {
          return "string";
        } else {
          return t;
        }
      };
      var isType = function(type) {
        return function(value) {
          return typeOf(value) === type;
        };
      };
      var isString = isType("string");
      var isArray = isType("array");
      var global$3 = tinymce.util.Tools.resolve("tinymce.dom.DOMUtils");
      var global$2 = tinymce.util.Tools.resolve("tinymce.EditorManager");
      var global$1 = tinymce.util.Tools.resolve("tinymce.Env");
      var global = tinymce.util.Tools.resolve("tinymce.util.Tools");
      var shouldMergeClasses = function(editor) {
        return editor.getParam("importcss_merge_classes");
      };
      var shouldImportExclusive = function(editor) {
        return editor.getParam("importcss_exclusive");
      };
      var getSelectorConverter = function(editor) {
        return editor.getParam("importcss_selector_converter");
      };
      var getSelectorFilter = function(editor) {
        return editor.getParam("importcss_selector_filter");
      };
      var getCssGroups = function(editor) {
        return editor.getParam("importcss_groups");
      };
      var shouldAppend = function(editor) {
        return editor.getParam("importcss_append");
      };
      var getFileFilter = function(editor) {
        return editor.getParam("importcss_file_filter");
      };
      var getSkin = function(editor) {
        var skin = editor.getParam("skin");
        return skin !== false ? skin || "oxide" : false;
      };
      var getSkinUrl = function(editor) {
        return editor.getParam("skin_url");
      };
      var nativePush = Array.prototype.push;
      var map = function(xs, f) {
        var len = xs.length;
        var r = new Array(len);
        for (var i = 0; i < len; i++) {
          var x = xs[i];
          r[i] = f(x, i);
        }
        return r;
      };
      var flatten = function(xs) {
        var r = [];
        for (var i = 0, len = xs.length; i < len; ++i) {
          if (!isArray(xs[i])) {
            throw new Error("Arr.flatten item " + i + " was not an array, input: " + xs);
          }
          nativePush.apply(r, xs[i]);
        }
        return r;
      };
      var bind = function(xs, f) {
        return flatten(map(xs, f));
      };
      var generate = function() {
        var ungroupedOrder = [];
        var groupOrder = [];
        var groups = {};
        var addItemToGroup = function(groupTitle, itemInfo) {
          if (groups[groupTitle]) {
            groups[groupTitle].push(itemInfo);
          } else {
            groupOrder.push(groupTitle);
            groups[groupTitle] = [itemInfo];
          }
        };
        var addItem = function(itemInfo) {
          ungroupedOrder.push(itemInfo);
        };
        var toFormats = function() {
          var groupItems = bind(groupOrder, function(g) {
            var items = groups[g];
            return items.length === 0 ? [] : [{
              title: g,
              items
            }];
          });
          return groupItems.concat(ungroupedOrder);
        };
        return {
          addItemToGroup,
          addItem,
          toFormats
        };
      };
      var internalEditorStyle = /^\.(?:ephox|tiny-pageembed|mce)(?:[.-]+\w+)+$/;
      var removeCacheSuffix = function(url) {
        var cacheSuffix = global$1.cacheSuffix;
        if (isString(url)) {
          url = url.replace("?" + cacheSuffix, "").replace("&" + cacheSuffix, "");
        }
        return url;
      };
      var isSkinContentCss = function(editor, href) {
        var skin = getSkin(editor);
        if (skin) {
          var skinUrlBase = getSkinUrl(editor);
          var skinUrl = skinUrlBase ? editor.documentBaseURI.toAbsolute(skinUrlBase) : global$2.baseURL + "/skins/ui/" + skin;
          var contentSkinUrlPart = global$2.baseURL + "/skins/content/";
          return href === skinUrl + "/content" + (editor.inline ? ".inline" : "") + ".min.css" || href.indexOf(contentSkinUrlPart) !== -1;
        }
        return false;
      };
      var compileFilter = function(filter) {
        if (isString(filter)) {
          return function(value) {
            return value.indexOf(filter) !== -1;
          };
        } else if (filter instanceof RegExp) {
          return function(value) {
            return filter.test(value);
          };
        }
        return filter;
      };
      var isCssImportRule = function(rule) {
        return rule.styleSheet;
      };
      var isCssPageRule = function(rule) {
        return rule.selectorText;
      };
      var getSelectors = function(editor, doc, fileFilter) {
        var selectors = [];
        var contentCSSUrls = {};
        var append = function(styleSheet, imported) {
          var href = styleSheet.href, rules;
          href = removeCacheSuffix(href);
          if (!href || !fileFilter(href, imported) || isSkinContentCss(editor, href)) {
            return;
          }
          global.each(styleSheet.imports, function(styleSheet2) {
            append(styleSheet2, true);
          });
          try {
            rules = styleSheet.cssRules || styleSheet.rules;
          } catch (e) {
          }
          global.each(rules, function(cssRule) {
            if (isCssImportRule(cssRule)) {
              append(cssRule.styleSheet, true);
            } else if (isCssPageRule(cssRule)) {
              global.each(cssRule.selectorText.split(","), function(selector) {
                selectors.push(global.trim(selector));
              });
            }
          });
        };
        global.each(editor.contentCSS, function(url) {
          contentCSSUrls[url] = true;
        });
        if (!fileFilter) {
          fileFilter = function(href, imported) {
            return imported || contentCSSUrls[href];
          };
        }
        try {
          global.each(doc.styleSheets, function(styleSheet) {
            append(styleSheet);
          });
        } catch (e) {
        }
        return selectors;
      };
      var defaultConvertSelectorToFormat = function(editor, selectorText) {
        var format;
        var selector = /^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(selectorText);
        if (!selector) {
          return;
        }
        var elementName = selector[1];
        var classes = selector[2].substr(1).split(".").join(" ");
        var inlineSelectorElements = global.makeMap("a,img");
        if (selector[1]) {
          format = { title: selectorText };
          if (editor.schema.getTextBlockElements()[elementName]) {
            format.block = elementName;
          } else if (editor.schema.getBlockElements()[elementName] || inlineSelectorElements[elementName.toLowerCase()]) {
            format.selector = elementName;
          } else {
            format.inline = elementName;
          }
        } else if (selector[2]) {
          format = {
            inline: "span",
            title: selectorText.substr(1),
            classes
          };
        }
        if (shouldMergeClasses(editor) !== false) {
          format.classes = classes;
        } else {
          format.attributes = { class: classes };
        }
        return format;
      };
      var getGroupsBySelector = function(groups, selector) {
        return global.grep(groups, function(group) {
          return !group.filter || group.filter(selector);
        });
      };
      var compileUserDefinedGroups = function(groups) {
        return global.map(groups, function(group) {
          return global.extend({}, group, {
            original: group,
            selectors: {},
            filter: compileFilter(group.filter)
          });
        });
      };
      var isExclusiveMode = function(editor, group) {
        return group === null || shouldImportExclusive(editor) !== false;
      };
      var isUniqueSelector = function(editor, selector, group, globallyUniqueSelectors) {
        return !(isExclusiveMode(editor, group) ? selector in globallyUniqueSelectors : selector in group.selectors);
      };
      var markUniqueSelector = function(editor, selector, group, globallyUniqueSelectors) {
        if (isExclusiveMode(editor, group)) {
          globallyUniqueSelectors[selector] = true;
        } else {
          group.selectors[selector] = true;
        }
      };
      var convertSelectorToFormat = function(editor, plugin, selector, group) {
        var selectorConverter;
        if (group && group.selector_converter) {
          selectorConverter = group.selector_converter;
        } else if (getSelectorConverter(editor)) {
          selectorConverter = getSelectorConverter(editor);
        } else {
          selectorConverter = function() {
            return defaultConvertSelectorToFormat(editor, selector);
          };
        }
        return selectorConverter.call(plugin, selector, group);
      };
      var setup = function(editor) {
        editor.on("init", function() {
          var model = generate();
          var globallyUniqueSelectors = {};
          var selectorFilter = compileFilter(getSelectorFilter(editor));
          var groups = compileUserDefinedGroups(getCssGroups(editor));
          var processSelector = function(selector, group) {
            if (isUniqueSelector(editor, selector, group, globallyUniqueSelectors)) {
              markUniqueSelector(editor, selector, group, globallyUniqueSelectors);
              var format = convertSelectorToFormat(editor, editor.plugins.importcss, selector, group);
              if (format) {
                var formatName = format.name || global$3.DOM.uniqueId();
                editor.formatter.register(formatName, format);
                return {
                  title: format.title,
                  format: formatName
                };
              }
            }
            return null;
          };
          global.each(getSelectors(editor, editor.getDoc(), compileFilter(getFileFilter(editor))), function(selector) {
            if (!internalEditorStyle.test(selector)) {
              if (!selectorFilter || selectorFilter(selector)) {
                var selectorGroups = getGroupsBySelector(groups, selector);
                if (selectorGroups.length > 0) {
                  global.each(selectorGroups, function(group) {
                    var menuItem2 = processSelector(selector, group);
                    if (menuItem2) {
                      model.addItemToGroup(group.title, menuItem2);
                    }
                  });
                } else {
                  var menuItem = processSelector(selector, null);
                  if (menuItem) {
                    model.addItem(menuItem);
                  }
                }
              }
            }
          });
          var items = model.toFormats();
          editor.fire("addStyleModifications", {
            items,
            replace: !shouldAppend(editor)
          });
        });
      };
      var get = function(editor) {
        var convertSelectorToFormat2 = function(selectorText) {
          return defaultConvertSelectorToFormat(editor, selectorText);
        };
        return { convertSelectorToFormat: convertSelectorToFormat2 };
      };
      function Plugin() {
        global$4.add("importcss", function(editor) {
          setup(editor);
          return get(editor);
        });
      }
      Plugin();
    })();
  }
});

// node_modules/tinymce/plugins/importcss/index.js
var require_importcss = __commonJS({
  "node_modules/tinymce/plugins/importcss/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_importcss
var tinymce_plugins_importcss_default = require_importcss();
export {
  tinymce_plugins_importcss_default as default
};
//# sourceMappingURL=tinymce_plugins_importcss.js.map
