<template>
    <div>
        <div v-if="activeStep == 4">
            <el-form inline label-width="auto">
                <el-form-item label="案件ID" prop="caseId">
                    <el-input v-model="queryParams.caseId" style="width:320px" placeholder="请输入案件ID" />
                </el-form-item>
                <el-form-item label="姓名" prop="name">
                    <el-input v-model="queryParams.name" style="width:320px" placeholder="请输入姓名" />
                </el-form-item>
                <el-form-item v-loading="loading">
                    <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
                    <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
                </el-form-item>
            </el-form>
            <SelectedAll class="mb10" v-model:allQuery="queryParams.allQuery" v-model:selectedArr="selectedArr"
                :dataList="dataList">
                <template #content>
                    <div>
                        <span class="ml20">债权总金额：<em>--</em></span>
                        <span class="ml20">剩余债权总额：<em>--</em></span>
                        <span class="ml20">剩余债权本金：<em>--</em></span>
                        <span class="ml20">案件数量：<em>--</em></span>
                    </div>
                </template>
            </SelectedAll>
        </div>
        <div class="tip-msg" v-if="activeStep == 5">
            <el-icon>
                <Warning />
            </el-icon>
            即将提交下方数据组成资产包，请确认数据
        </div>
        <el-table :data="dataList" v-loading="loading" ref=" multipleTableRef" class="multiple-table"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="44px" :selectable="selectable" align="right"
                v-if="activeStep == 4" />
            <el-table-column label="案件ID" align="center" prop="caseId" min-width="120" />
            <el-table-column label="批次号" align="center" prop="batchNum" min-width="220" />
            <el-table-column label="资产包名称" align="center" prop="packageName" min-width="160" />
            <el-table-column label="产品类型" align="center" prop="productName" min-width="120" />
            <el-table-column label="转让方" align="center" prop="entrustingPartyName" min-width="160" />
            <el-table-column label="姓名" align="center" prop="clientName" min-width="120" />
            <el-table-column label="证件类型" align="center" prop="clientIdType" min-width="120" />
            <el-table-column label="证件号码【户籍地】" align="center" prop="" min-width="180">
                <template #default="{ row }">
                    <div>{{ row.clientIdcard }}<br />【{{ row.clientCensusRegister || '--' }}】 </div>
                </template>
            </el-table-column>
            <el-table-column label="手机号码" align="center" prop="clientPhone" min-width="120" />
            <el-table-column label="债权总金额" align="center" prop="clientMoney" min-width="120" />
            <el-table-column label="剩余应还债权金额" align="center" prop="clientResidualPrincipal" min-width="160" />
            <el-table-column label="剩余债权本金" align="center" prop="syYhPrincipal" min-width="120" />
            <el-table-column label="逾期日期（末次）" align="center" prop="clientOverdueStart" min-width="160" />
            <el-table-column label="实际逾期天数" align="center" prop="commissionedDays" min-width="120" />
        </el-table>
    </div>
</template>

<script setup>
const props = defineProps({
    activeStep: { type: [String, Number], default: 4 }
})
const selectedArr = ref([])
const queryParams = ref({ allQuery: false })
const dataList = ref([
    {
        "caseId": 142270,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "林淑芬",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406040533",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "orderStatusCode": 0,
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142269,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "林淑芬",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406040533",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142268,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "林依婷",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406042336",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142267,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "黄徐颖",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406041931",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142266,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "林依婷",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406042336",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142265,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "林淑芬",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406040533",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142264,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "黄徐颖",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406041931",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142263,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "林淑芬",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406040533",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142262,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "林淑芬",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406040533",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    },
    {
        "caseId": 142261,
        "odv": "未分配",
        "allocatedState": "0",
        "caseState": "0",
        "settlementStatus": 0,
        "entrustingPartyName": "招商银行佛山分行",
        "productName": "测试贷",
        "batchNum": "zsyhfoshan202502CSD0002",
        "clientName": "黄徐颖",
        "clientIdType": "身份证",
        "clientIdcard": "520422197406041931",
        "clientCensusRegister": "贵州省安********",
        "clientPhone": "***********",
        "permanentlyStop": 1,
        "clientMoney": 3000,
        "clientResidualPrincipal": 2000,
        "clientOverdueStart": "2025-04-01",
        "accountPeriod": "M3",
        "followUpState": "待跟进",
        "notFollowUpDay": 0,
        "urgePower": "0",
        "clientOverdueDays": 77,
        "allocatedStateStr": "资产未分配",
        "caseStateStr": "未分配",
        "remainingDue": 3000,
        "syYhPrincipal": 2000,
        "commissionedDays": 0,
        "packageName": "zsyh002",
    }
])
const loading = ref(false)
function resetQuery() {
    queryParams.value = {}
}
function handleQuery() {

}
function handleSelectionChange(selection) {
    selectedArr.value = selection
}
function selectable() {
    return !queryParams.value.allQuery
}
</script>

<style lang="scss" scoped>
:deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
}

.tip-msg {
    display: flex;
    color: #5a5951;
    padding: 5px 20px;
    align-items: center;
    margin-bottom: 10px;
    border-radius: 5px;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;

    .el-icon {
        color: #fab221;
        margin-right: 10px;
    }
}
</style>