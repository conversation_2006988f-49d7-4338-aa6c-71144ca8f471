{"version": 3, "sources": ["../tinymce/plugins/visualblocks/plugin.js", "../tinymce/plugins/visualblocks/index.js", "dep:tinymce_plugins_visualblocks"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var fireVisualBlocks = function (editor, state) {\n      editor.fire('VisualBlocks', { state: state });\n    };\n\n    var toggleVisualBlocks = function (editor, pluginUrl, enabledState) {\n      var dom = editor.dom;\n      dom.toggleClass(editor.getBody(), 'mce-visualblocks');\n      enabledState.set(!enabledState.get());\n      fireVisualBlocks(editor, enabledState.get());\n    };\n\n    var register$1 = function (editor, pluginUrl, enabledState) {\n      editor.addCommand('mceVisualBlocks', function () {\n        toggleVisualBlocks(editor, pluginUrl, enabledState);\n      });\n    };\n\n    var isEnabledByDefault = function (editor) {\n      return editor.getParam('visualblocks_default_state', false, 'boolean');\n    };\n\n    var setup = function (editor, pluginUrl, enabledState) {\n      editor.on('PreviewFormats AfterPreviewFormats', function (e) {\n        if (enabledState.get()) {\n          editor.dom.toggleClass(editor.getBody(), 'mce-visualblocks', e.type === 'afterpreviewformats');\n        }\n      });\n      editor.on('init', function () {\n        if (isEnabledByDefault(editor)) {\n          toggleVisualBlocks(editor, pluginUrl, enabledState);\n        }\n      });\n    };\n\n    var toggleActiveState = function (editor, enabledState) {\n      return function (api) {\n        api.setActive(enabledState.get());\n        var editorEventCallback = function (e) {\n          return api.setActive(e.state);\n        };\n        editor.on('VisualBlocks', editorEventCallback);\n        return function () {\n          return editor.off('VisualBlocks', editorEventCallback);\n        };\n      };\n    };\n    var register = function (editor, enabledState) {\n      var onAction = function () {\n        return editor.execCommand('mceVisualBlocks');\n      };\n      editor.ui.registry.addToggleButton('visualblocks', {\n        icon: 'visualblocks',\n        tooltip: 'Show blocks',\n        onAction: onAction,\n        onSetup: toggleActiveState(editor, enabledState)\n      });\n      editor.ui.registry.addToggleMenuItem('visualblocks', {\n        text: 'Show blocks',\n        icon: 'visualblocks',\n        onAction: onAction,\n        onSetup: toggleActiveState(editor, enabledState)\n      });\n    };\n\n    function Plugin () {\n      global.add('visualblocks', function (editor, pluginUrl) {\n        var enabledState = Cell(false);\n        register$1(editor, pluginUrl, enabledState);\n        register(editor, enabledState);\n        setup(editor, pluginUrl, enabledState);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"visualblocks\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/visualblocks')\n//   ES2015:\n//     import 'tinymce/plugins/visualblocks'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/visualblocks/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,QAAQ;AACZ,YAAI,MAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,kBAAQ;AAAA;AAEV,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,mBAAmB,SAAU,QAAQ,OAAO;AAC9C,eAAO,KAAK,gBAAgB,EAAE;AAAA;AAGhC,UAAI,qBAAqB,SAAU,QAAQ,WAAW,cAAc;AAClE,YAAI,MAAM,OAAO;AACjB,YAAI,YAAY,OAAO,WAAW;AAClC,qBAAa,IAAI,CAAC,aAAa;AAC/B,yBAAiB,QAAQ,aAAa;AAAA;AAGxC,UAAI,aAAa,SAAU,QAAQ,WAAW,cAAc;AAC1D,eAAO,WAAW,mBAAmB,WAAY;AAC/C,6BAAmB,QAAQ,WAAW;AAAA;AAAA;AAI1C,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS,8BAA8B,OAAO;AAAA;AAG9D,UAAI,QAAQ,SAAU,QAAQ,WAAW,cAAc;AACrD,eAAO,GAAG,sCAAsC,SAAU,GAAG;AAC3D,cAAI,aAAa,OAAO;AACtB,mBAAO,IAAI,YAAY,OAAO,WAAW,oBAAoB,EAAE,SAAS;AAAA;AAAA;AAG5E,eAAO,GAAG,QAAQ,WAAY;AAC5B,cAAI,mBAAmB,SAAS;AAC9B,+BAAmB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAK5C,UAAI,oBAAoB,SAAU,QAAQ,cAAc;AACtD,eAAO,SAAU,KAAK;AACpB,cAAI,UAAU,aAAa;AAC3B,cAAI,sBAAsB,SAAU,GAAG;AACrC,mBAAO,IAAI,UAAU,EAAE;AAAA;AAEzB,iBAAO,GAAG,gBAAgB;AAC1B,iBAAO,WAAY;AACjB,mBAAO,OAAO,IAAI,gBAAgB;AAAA;AAAA;AAAA;AAIxC,UAAI,WAAW,SAAU,QAAQ,cAAc;AAC7C,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,gBAAgB,gBAAgB;AAAA,UACjD,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA,SAAS,kBAAkB,QAAQ;AAAA;AAErC,eAAO,GAAG,SAAS,kBAAkB,gBAAgB;AAAA,UACnD,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,SAAS,kBAAkB,QAAQ;AAAA;AAAA;AAIvC,wBAAmB;AACjB,eAAO,IAAI,gBAAgB,SAAU,QAAQ,WAAW;AACtD,cAAI,eAAe,KAAK;AACxB,qBAAW,QAAQ,WAAW;AAC9B,mBAAS,QAAQ;AACjB,gBAAM,QAAQ,WAAW;AAAA;AAAA;AAI7B;AAAA;AAAA;AAAA;;;ACpGJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,uCAAQ;", "names": []}