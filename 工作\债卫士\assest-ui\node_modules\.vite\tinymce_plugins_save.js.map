{"version": 3, "sources": ["../tinymce/plugins/save/plugin.js", "../tinymce/plugins/save/index.js", "dep:tinymce_plugins_save"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var enableWhenDirty = function (editor) {\n      return editor.getParam('save_enablewhendirty', true);\n    };\n    var hasOnSaveCallback = function (editor) {\n      return !!editor.getParam('save_onsavecallback');\n    };\n    var hasOnCancelCallback = function (editor) {\n      return !!editor.getParam('save_oncancelcallback');\n    };\n\n    var displayErrorMessage = function (editor, message) {\n      editor.notificationManager.open({\n        text: message,\n        type: 'error'\n      });\n    };\n    var save = function (editor) {\n      var formObj = global$1.DOM.getParent(editor.id, 'form');\n      if (enableWhenDirty(editor) && !editor.isDirty()) {\n        return;\n      }\n      editor.save();\n      if (hasOnSaveCallback(editor)) {\n        editor.execCallback('save_onsavecallback', editor);\n        editor.nodeChanged();\n        return;\n      }\n      if (formObj) {\n        editor.setDirty(false);\n        if (!formObj.onsubmit || formObj.onsubmit()) {\n          if (typeof formObj.submit === 'function') {\n            formObj.submit();\n          } else {\n            displayErrorMessage(editor, 'Error: Form submit field collision.');\n          }\n        }\n        editor.nodeChanged();\n      } else {\n        displayErrorMessage(editor, 'Error: No form element found.');\n      }\n    };\n    var cancel = function (editor) {\n      var h = global.trim(editor.startContent);\n      if (hasOnCancelCallback(editor)) {\n        editor.execCallback('save_oncancelcallback', editor);\n        return;\n      }\n      editor.resetContent(h);\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mceSave', function () {\n        save(editor);\n      });\n      editor.addCommand('mceCancel', function () {\n        cancel(editor);\n      });\n    };\n\n    var stateToggle = function (editor) {\n      return function (api) {\n        var handler = function () {\n          api.setDisabled(enableWhenDirty(editor) && !editor.isDirty());\n        };\n        handler();\n        editor.on('NodeChange dirty', handler);\n        return function () {\n          return editor.off('NodeChange dirty', handler);\n        };\n      };\n    };\n    var register = function (editor) {\n      editor.ui.registry.addButton('save', {\n        icon: 'save',\n        tooltip: 'Save',\n        disabled: true,\n        onAction: function () {\n          return editor.execCommand('mceSave');\n        },\n        onSetup: stateToggle(editor)\n      });\n      editor.ui.registry.addButton('cancel', {\n        icon: 'cancel',\n        tooltip: 'Cancel',\n        disabled: true,\n        onAction: function () {\n          return editor.execCommand('mceCancel');\n        },\n        onSetup: stateToggle(editor)\n      });\n      editor.addShortcut('Meta+S', '', 'mceSave');\n    };\n\n    function Plugin () {\n      global$2.add('save', function (editor) {\n        register(editor);\n        register$1(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"save\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/save')\n//   ES2015:\n//     import 'tinymce/plugins/save'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/save/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,OAAO,SAAS,wBAAwB;AAAA;AAEjD,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,CAAC,CAAC,OAAO,SAAS;AAAA;AAE3B,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,CAAC,CAAC,OAAO,SAAS;AAAA;AAG3B,UAAI,sBAAsB,SAAU,QAAQ,SAAS;AACnD,eAAO,oBAAoB,KAAK;AAAA,UAC9B,MAAM;AAAA,UACN,MAAM;AAAA;AAAA;AAGV,UAAI,OAAO,SAAU,QAAQ;AAC3B,YAAI,UAAU,SAAS,IAAI,UAAU,OAAO,IAAI;AAChD,YAAI,gBAAgB,WAAW,CAAC,OAAO,WAAW;AAChD;AAAA;AAEF,eAAO;AACP,YAAI,kBAAkB,SAAS;AAC7B,iBAAO,aAAa,uBAAuB;AAC3C,iBAAO;AACP;AAAA;AAEF,YAAI,SAAS;AACX,iBAAO,SAAS;AAChB,cAAI,CAAC,QAAQ,YAAY,QAAQ,YAAY;AAC3C,gBAAI,OAAO,QAAQ,WAAW,YAAY;AACxC,sBAAQ;AAAA,mBACH;AACL,kCAAoB,QAAQ;AAAA;AAAA;AAGhC,iBAAO;AAAA,eACF;AACL,8BAAoB,QAAQ;AAAA;AAAA;AAGhC,UAAI,SAAS,SAAU,QAAQ;AAC7B,YAAI,IAAI,OAAO,KAAK,OAAO;AAC3B,YAAI,oBAAoB,SAAS;AAC/B,iBAAO,aAAa,yBAAyB;AAC7C;AAAA;AAEF,eAAO,aAAa;AAAA;AAGtB,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,WAAW,WAAY;AACvC,eAAK;AAAA;AAEP,eAAO,WAAW,aAAa,WAAY;AACzC,iBAAO;AAAA;AAAA;AAIX,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,SAAU,KAAK;AACpB,cAAI,UAAU,WAAY;AACxB,gBAAI,YAAY,gBAAgB,WAAW,CAAC,OAAO;AAAA;AAErD;AACA,iBAAO,GAAG,oBAAoB;AAC9B,iBAAO,WAAY;AACjB,mBAAO,OAAO,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAI5C,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,GAAG,SAAS,UAAU,QAAQ;AAAA,UACnC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA,UAE5B,SAAS,YAAY;AAAA;AAEvB,eAAO,GAAG,SAAS,UAAU,UAAU;AAAA,UACrC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA,UAE5B,SAAS,YAAY;AAAA;AAEvB,eAAO,YAAY,UAAU,IAAI;AAAA;AAGnC,wBAAmB;AACjB,iBAAS,IAAI,QAAQ,SAAU,QAAQ;AACrC,mBAAS;AACT,qBAAW;AAAA;AAAA;AAIf;AAAA;AAAA;AAAA;;;ACrHJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,+BAAQ;", "names": []}