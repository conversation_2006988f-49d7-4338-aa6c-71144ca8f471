{"version": 3, "sources": ["../tinymce/plugins/importcss/plugin.js", "../tinymce/plugins/importcss/index.js", "dep:tinymce_plugins_importcss"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isString = isType('string');\n    var isArray = isType('array');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.EditorManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var shouldMergeClasses = function (editor) {\n      return editor.getParam('importcss_merge_classes');\n    };\n    var shouldImportExclusive = function (editor) {\n      return editor.getParam('importcss_exclusive');\n    };\n    var getSelectorConverter = function (editor) {\n      return editor.getParam('importcss_selector_converter');\n    };\n    var getSelectorFilter = function (editor) {\n      return editor.getParam('importcss_selector_filter');\n    };\n    var getCssGroups = function (editor) {\n      return editor.getParam('importcss_groups');\n    };\n    var shouldAppend = function (editor) {\n      return editor.getParam('importcss_append');\n    };\n    var getFileFilter = function (editor) {\n      return editor.getParam('importcss_file_filter');\n    };\n    var getSkin = function (editor) {\n      var skin = editor.getParam('skin');\n      return skin !== false ? skin || 'oxide' : false;\n    };\n    var getSkinUrl = function (editor) {\n      return editor.getParam('skin_url');\n    };\n\n    var nativePush = Array.prototype.push;\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var flatten = function (xs) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    var bind = function (xs, f) {\n      return flatten(map(xs, f));\n    };\n\n    var generate = function () {\n      var ungroupedOrder = [];\n      var groupOrder = [];\n      var groups = {};\n      var addItemToGroup = function (groupTitle, itemInfo) {\n        if (groups[groupTitle]) {\n          groups[groupTitle].push(itemInfo);\n        } else {\n          groupOrder.push(groupTitle);\n          groups[groupTitle] = [itemInfo];\n        }\n      };\n      var addItem = function (itemInfo) {\n        ungroupedOrder.push(itemInfo);\n      };\n      var toFormats = function () {\n        var groupItems = bind(groupOrder, function (g) {\n          var items = groups[g];\n          return items.length === 0 ? [] : [{\n              title: g,\n              items: items\n            }];\n        });\n        return groupItems.concat(ungroupedOrder);\n      };\n      return {\n        addItemToGroup: addItemToGroup,\n        addItem: addItem,\n        toFormats: toFormats\n      };\n    };\n\n    var internalEditorStyle = /^\\.(?:ephox|tiny-pageembed|mce)(?:[.-]+\\w+)+$/;\n    var removeCacheSuffix = function (url) {\n      var cacheSuffix = global$1.cacheSuffix;\n      if (isString(url)) {\n        url = url.replace('?' + cacheSuffix, '').replace('&' + cacheSuffix, '');\n      }\n      return url;\n    };\n    var isSkinContentCss = function (editor, href) {\n      var skin = getSkin(editor);\n      if (skin) {\n        var skinUrlBase = getSkinUrl(editor);\n        var skinUrl = skinUrlBase ? editor.documentBaseURI.toAbsolute(skinUrlBase) : global$2.baseURL + '/skins/ui/' + skin;\n        var contentSkinUrlPart = global$2.baseURL + '/skins/content/';\n        return href === skinUrl + '/content' + (editor.inline ? '.inline' : '') + '.min.css' || href.indexOf(contentSkinUrlPart) !== -1;\n      }\n      return false;\n    };\n    var compileFilter = function (filter) {\n      if (isString(filter)) {\n        return function (value) {\n          return value.indexOf(filter) !== -1;\n        };\n      } else if (filter instanceof RegExp) {\n        return function (value) {\n          return filter.test(value);\n        };\n      }\n      return filter;\n    };\n    var isCssImportRule = function (rule) {\n      return rule.styleSheet;\n    };\n    var isCssPageRule = function (rule) {\n      return rule.selectorText;\n    };\n    var getSelectors = function (editor, doc, fileFilter) {\n      var selectors = [];\n      var contentCSSUrls = {};\n      var append = function (styleSheet, imported) {\n        var href = styleSheet.href, rules;\n        href = removeCacheSuffix(href);\n        if (!href || !fileFilter(href, imported) || isSkinContentCss(editor, href)) {\n          return;\n        }\n        global.each(styleSheet.imports, function (styleSheet) {\n          append(styleSheet, true);\n        });\n        try {\n          rules = styleSheet.cssRules || styleSheet.rules;\n        } catch (e) {\n        }\n        global.each(rules, function (cssRule) {\n          if (isCssImportRule(cssRule)) {\n            append(cssRule.styleSheet, true);\n          } else if (isCssPageRule(cssRule)) {\n            global.each(cssRule.selectorText.split(','), function (selector) {\n              selectors.push(global.trim(selector));\n            });\n          }\n        });\n      };\n      global.each(editor.contentCSS, function (url) {\n        contentCSSUrls[url] = true;\n      });\n      if (!fileFilter) {\n        fileFilter = function (href, imported) {\n          return imported || contentCSSUrls[href];\n        };\n      }\n      try {\n        global.each(doc.styleSheets, function (styleSheet) {\n          append(styleSheet);\n        });\n      } catch (e) {\n      }\n      return selectors;\n    };\n    var defaultConvertSelectorToFormat = function (editor, selectorText) {\n      var format;\n      var selector = /^(?:([a-z0-9\\-_]+))?(\\.[a-z0-9_\\-\\.]+)$/i.exec(selectorText);\n      if (!selector) {\n        return;\n      }\n      var elementName = selector[1];\n      var classes = selector[2].substr(1).split('.').join(' ');\n      var inlineSelectorElements = global.makeMap('a,img');\n      if (selector[1]) {\n        format = { title: selectorText };\n        if (editor.schema.getTextBlockElements()[elementName]) {\n          format.block = elementName;\n        } else if (editor.schema.getBlockElements()[elementName] || inlineSelectorElements[elementName.toLowerCase()]) {\n          format.selector = elementName;\n        } else {\n          format.inline = elementName;\n        }\n      } else if (selector[2]) {\n        format = {\n          inline: 'span',\n          title: selectorText.substr(1),\n          classes: classes\n        };\n      }\n      if (shouldMergeClasses(editor) !== false) {\n        format.classes = classes;\n      } else {\n        format.attributes = { class: classes };\n      }\n      return format;\n    };\n    var getGroupsBySelector = function (groups, selector) {\n      return global.grep(groups, function (group) {\n        return !group.filter || group.filter(selector);\n      });\n    };\n    var compileUserDefinedGroups = function (groups) {\n      return global.map(groups, function (group) {\n        return global.extend({}, group, {\n          original: group,\n          selectors: {},\n          filter: compileFilter(group.filter)\n        });\n      });\n    };\n    var isExclusiveMode = function (editor, group) {\n      return group === null || shouldImportExclusive(editor) !== false;\n    };\n    var isUniqueSelector = function (editor, selector, group, globallyUniqueSelectors) {\n      return !(isExclusiveMode(editor, group) ? selector in globallyUniqueSelectors : selector in group.selectors);\n    };\n    var markUniqueSelector = function (editor, selector, group, globallyUniqueSelectors) {\n      if (isExclusiveMode(editor, group)) {\n        globallyUniqueSelectors[selector] = true;\n      } else {\n        group.selectors[selector] = true;\n      }\n    };\n    var convertSelectorToFormat = function (editor, plugin, selector, group) {\n      var selectorConverter;\n      if (group && group.selector_converter) {\n        selectorConverter = group.selector_converter;\n      } else if (getSelectorConverter(editor)) {\n        selectorConverter = getSelectorConverter(editor);\n      } else {\n        selectorConverter = function () {\n          return defaultConvertSelectorToFormat(editor, selector);\n        };\n      }\n      return selectorConverter.call(plugin, selector, group);\n    };\n    var setup = function (editor) {\n      editor.on('init', function () {\n        var model = generate();\n        var globallyUniqueSelectors = {};\n        var selectorFilter = compileFilter(getSelectorFilter(editor));\n        var groups = compileUserDefinedGroups(getCssGroups(editor));\n        var processSelector = function (selector, group) {\n          if (isUniqueSelector(editor, selector, group, globallyUniqueSelectors)) {\n            markUniqueSelector(editor, selector, group, globallyUniqueSelectors);\n            var format = convertSelectorToFormat(editor, editor.plugins.importcss, selector, group);\n            if (format) {\n              var formatName = format.name || global$3.DOM.uniqueId();\n              editor.formatter.register(formatName, format);\n              return {\n                title: format.title,\n                format: formatName\n              };\n            }\n          }\n          return null;\n        };\n        global.each(getSelectors(editor, editor.getDoc(), compileFilter(getFileFilter(editor))), function (selector) {\n          if (!internalEditorStyle.test(selector)) {\n            if (!selectorFilter || selectorFilter(selector)) {\n              var selectorGroups = getGroupsBySelector(groups, selector);\n              if (selectorGroups.length > 0) {\n                global.each(selectorGroups, function (group) {\n                  var menuItem = processSelector(selector, group);\n                  if (menuItem) {\n                    model.addItemToGroup(group.title, menuItem);\n                  }\n                });\n              } else {\n                var menuItem = processSelector(selector, null);\n                if (menuItem) {\n                  model.addItem(menuItem);\n                }\n              }\n            }\n          }\n        });\n        var items = model.toFormats();\n        editor.fire('addStyleModifications', {\n          items: items,\n          replace: !shouldAppend(editor)\n        });\n      });\n    };\n\n    var get = function (editor) {\n      var convertSelectorToFormat = function (selectorText) {\n        return defaultConvertSelectorToFormat(editor, selectorText);\n      };\n      return { convertSelectorToFormat: convertSelectorToFormat };\n    };\n\n    function Plugin () {\n      global$4.add('importcss', function (editor) {\n        setup(editor);\n        return get(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"importcss\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/importcss')\n//   ES2015:\n//     import 'tinymce/plugins/importcss'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/importcss/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,WAAW;AAAA;AAAA;AAG7B,UAAI,WAAW,OAAO;AACtB,UAAI,UAAU,OAAO;AAErB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,UAAU,SAAU,QAAQ;AAC9B,YAAI,OAAO,OAAO,SAAS;AAC3B,eAAO,SAAS,QAAQ,QAAQ,UAAU;AAAA;AAE5C,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,OAAO,SAAS;AAAA;AAGzB,UAAI,aAAa,MAAM,UAAU;AACjC,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,KAAK;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;AAAA;AAE3E,qBAAW,MAAM,GAAG,GAAG;AAAA;AAEzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,eAAO,QAAQ,IAAI,IAAI;AAAA;AAGzB,UAAI,WAAW,WAAY;AACzB,YAAI,iBAAiB;AACrB,YAAI,aAAa;AACjB,YAAI,SAAS;AACb,YAAI,iBAAiB,SAAU,YAAY,UAAU;AACnD,cAAI,OAAO,aAAa;AACtB,mBAAO,YAAY,KAAK;AAAA,iBACnB;AACL,uBAAW,KAAK;AAChB,mBAAO,cAAc,CAAC;AAAA;AAAA;AAG1B,YAAI,UAAU,SAAU,UAAU;AAChC,yBAAe,KAAK;AAAA;AAEtB,YAAI,YAAY,WAAY;AAC1B,cAAI,aAAa,KAAK,YAAY,SAAU,GAAG;AAC7C,gBAAI,QAAQ,OAAO;AACnB,mBAAO,MAAM,WAAW,IAAI,KAAK,CAAC;AAAA,cAC9B,OAAO;AAAA,cACP;AAAA;AAAA;AAGN,iBAAO,WAAW,OAAO;AAAA;AAE3B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,sBAAsB;AAC1B,UAAI,oBAAoB,SAAU,KAAK;AACrC,YAAI,cAAc,SAAS;AAC3B,YAAI,SAAS,MAAM;AACjB,gBAAM,IAAI,QAAQ,MAAM,aAAa,IAAI,QAAQ,MAAM,aAAa;AAAA;AAEtE,eAAO;AAAA;AAET,UAAI,mBAAmB,SAAU,QAAQ,MAAM;AAC7C,YAAI,OAAO,QAAQ;AACnB,YAAI,MAAM;AACR,cAAI,cAAc,WAAW;AAC7B,cAAI,UAAU,cAAc,OAAO,gBAAgB,WAAW,eAAe,SAAS,UAAU,eAAe;AAC/G,cAAI,qBAAqB,SAAS,UAAU;AAC5C,iBAAO,SAAS,UAAU,aAAc,QAAO,SAAS,YAAY,MAAM,cAAc,KAAK,QAAQ,wBAAwB;AAAA;AAE/H,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,QAAQ;AACpC,YAAI,SAAS,SAAS;AACpB,iBAAO,SAAU,OAAO;AACtB,mBAAO,MAAM,QAAQ,YAAY;AAAA;AAAA,mBAE1B,kBAAkB,QAAQ;AACnC,iBAAO,SAAU,OAAO;AACtB,mBAAO,OAAO,KAAK;AAAA;AAAA;AAGvB,eAAO;AAAA;AAET,UAAI,kBAAkB,SAAU,MAAM;AACpC,eAAO,KAAK;AAAA;AAEd,UAAI,gBAAgB,SAAU,MAAM;AAClC,eAAO,KAAK;AAAA;AAEd,UAAI,eAAe,SAAU,QAAQ,KAAK,YAAY;AACpD,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,SAAS,SAAU,YAAY,UAAU;AAC3C,cAAI,OAAO,WAAW,MAAM;AAC5B,iBAAO,kBAAkB;AACzB,cAAI,CAAC,QAAQ,CAAC,WAAW,MAAM,aAAa,iBAAiB,QAAQ,OAAO;AAC1E;AAAA;AAEF,iBAAO,KAAK,WAAW,SAAS,SAAU,aAAY;AACpD,mBAAO,aAAY;AAAA;AAErB,cAAI;AACF,oBAAQ,WAAW,YAAY,WAAW;AAAA,mBACnC,GAAP;AAAA;AAEF,iBAAO,KAAK,OAAO,SAAU,SAAS;AACpC,gBAAI,gBAAgB,UAAU;AAC5B,qBAAO,QAAQ,YAAY;AAAA,uBAClB,cAAc,UAAU;AACjC,qBAAO,KAAK,QAAQ,aAAa,MAAM,MAAM,SAAU,UAAU;AAC/D,0BAAU,KAAK,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAKnC,eAAO,KAAK,OAAO,YAAY,SAAU,KAAK;AAC5C,yBAAe,OAAO;AAAA;AAExB,YAAI,CAAC,YAAY;AACf,uBAAa,SAAU,MAAM,UAAU;AACrC,mBAAO,YAAY,eAAe;AAAA;AAAA;AAGtC,YAAI;AACF,iBAAO,KAAK,IAAI,aAAa,SAAU,YAAY;AACjD,mBAAO;AAAA;AAAA,iBAEF,GAAP;AAAA;AAEF,eAAO;AAAA;AAET,UAAI,iCAAiC,SAAU,QAAQ,cAAc;AACnE,YAAI;AACJ,YAAI,WAAW,2CAA2C,KAAK;AAC/D,YAAI,CAAC,UAAU;AACb;AAAA;AAEF,YAAI,cAAc,SAAS;AAC3B,YAAI,UAAU,SAAS,GAAG,OAAO,GAAG,MAAM,KAAK,KAAK;AACpD,YAAI,yBAAyB,OAAO,QAAQ;AAC5C,YAAI,SAAS,IAAI;AACf,mBAAS,EAAE,OAAO;AAClB,cAAI,OAAO,OAAO,uBAAuB,cAAc;AACrD,mBAAO,QAAQ;AAAA,qBACN,OAAO,OAAO,mBAAmB,gBAAgB,uBAAuB,YAAY,gBAAgB;AAC7G,mBAAO,WAAW;AAAA,iBACb;AACL,mBAAO,SAAS;AAAA;AAAA,mBAET,SAAS,IAAI;AACtB,mBAAS;AAAA,YACP,QAAQ;AAAA,YACR,OAAO,aAAa,OAAO;AAAA,YAC3B;AAAA;AAAA;AAGJ,YAAI,mBAAmB,YAAY,OAAO;AACxC,iBAAO,UAAU;AAAA,eACZ;AACL,iBAAO,aAAa,EAAE,OAAO;AAAA;AAE/B,eAAO;AAAA;AAET,UAAI,sBAAsB,SAAU,QAAQ,UAAU;AACpD,eAAO,OAAO,KAAK,QAAQ,SAAU,OAAO;AAC1C,iBAAO,CAAC,MAAM,UAAU,MAAM,OAAO;AAAA;AAAA;AAGzC,UAAI,2BAA2B,SAAU,QAAQ;AAC/C,eAAO,OAAO,IAAI,QAAQ,SAAU,OAAO;AACzC,iBAAO,OAAO,OAAO,IAAI,OAAO;AAAA,YAC9B,UAAU;AAAA,YACV,WAAW;AAAA,YACX,QAAQ,cAAc,MAAM;AAAA;AAAA;AAAA;AAIlC,UAAI,kBAAkB,SAAU,QAAQ,OAAO;AAC7C,eAAO,UAAU,QAAQ,sBAAsB,YAAY;AAAA;AAE7D,UAAI,mBAAmB,SAAU,QAAQ,UAAU,OAAO,yBAAyB;AACjF,eAAO,CAAE,iBAAgB,QAAQ,SAAS,YAAY,0BAA0B,YAAY,MAAM;AAAA;AAEpG,UAAI,qBAAqB,SAAU,QAAQ,UAAU,OAAO,yBAAyB;AACnF,YAAI,gBAAgB,QAAQ,QAAQ;AAClC,kCAAwB,YAAY;AAAA,eAC/B;AACL,gBAAM,UAAU,YAAY;AAAA;AAAA;AAGhC,UAAI,0BAA0B,SAAU,QAAQ,QAAQ,UAAU,OAAO;AACvE,YAAI;AACJ,YAAI,SAAS,MAAM,oBAAoB;AACrC,8BAAoB,MAAM;AAAA,mBACjB,qBAAqB,SAAS;AACvC,8BAAoB,qBAAqB;AAAA,eACpC;AACL,8BAAoB,WAAY;AAC9B,mBAAO,+BAA+B,QAAQ;AAAA;AAAA;AAGlD,eAAO,kBAAkB,KAAK,QAAQ,UAAU;AAAA;AAElD,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,GAAG,QAAQ,WAAY;AAC5B,cAAI,QAAQ;AACZ,cAAI,0BAA0B;AAC9B,cAAI,iBAAiB,cAAc,kBAAkB;AACrD,cAAI,SAAS,yBAAyB,aAAa;AACnD,cAAI,kBAAkB,SAAU,UAAU,OAAO;AAC/C,gBAAI,iBAAiB,QAAQ,UAAU,OAAO,0BAA0B;AACtE,iCAAmB,QAAQ,UAAU,OAAO;AAC5C,kBAAI,SAAS,wBAAwB,QAAQ,OAAO,QAAQ,WAAW,UAAU;AACjF,kBAAI,QAAQ;AACV,oBAAI,aAAa,OAAO,QAAQ,SAAS,IAAI;AAC7C,uBAAO,UAAU,SAAS,YAAY;AACtC,uBAAO;AAAA,kBACL,OAAO,OAAO;AAAA,kBACd,QAAQ;AAAA;AAAA;AAAA;AAId,mBAAO;AAAA;AAET,iBAAO,KAAK,aAAa,QAAQ,OAAO,UAAU,cAAc,cAAc,WAAW,SAAU,UAAU;AAC3G,gBAAI,CAAC,oBAAoB,KAAK,WAAW;AACvC,kBAAI,CAAC,kBAAkB,eAAe,WAAW;AAC/C,oBAAI,iBAAiB,oBAAoB,QAAQ;AACjD,oBAAI,eAAe,SAAS,GAAG;AAC7B,yBAAO,KAAK,gBAAgB,SAAU,OAAO;AAC3C,wBAAI,YAAW,gBAAgB,UAAU;AACzC,wBAAI,WAAU;AACZ,4BAAM,eAAe,MAAM,OAAO;AAAA;AAAA;AAAA,uBAGjC;AACL,sBAAI,WAAW,gBAAgB,UAAU;AACzC,sBAAI,UAAU;AACZ,0BAAM,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAMxB,cAAI,QAAQ,MAAM;AAClB,iBAAO,KAAK,yBAAyB;AAAA,YACnC;AAAA,YACA,SAAS,CAAC,aAAa;AAAA;AAAA;AAAA;AAK7B,UAAI,MAAM,SAAU,QAAQ;AAC1B,YAAI,2BAA0B,SAAU,cAAc;AACpD,iBAAO,+BAA+B,QAAQ;AAAA;AAEhD,eAAO,EAAE,yBAAyB;AAAA;AAGpC,wBAAmB;AACjB,iBAAS,IAAI,aAAa,SAAU,QAAQ;AAC1C,gBAAM;AACN,iBAAO,IAAI;AAAA;AAAA;AAIf;AAAA;AAAA;AAAA;;;ACnVJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,oCAAQ;", "names": []}