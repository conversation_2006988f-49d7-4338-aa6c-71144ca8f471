{"version": 3, "sources": ["../tinymce/plugins/anchor/plugin.js", "../tinymce/plugins/anchor/index.js", "dep:tinymce_plugins_anchor"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.RangeUtils');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var allowHtmlInNamedAnchor = function (editor) {\n      return editor.getParam('allow_html_in_named_anchor', false, 'boolean');\n    };\n\n    var namedAnchorSelector = 'a:not([href])';\n    var isEmptyString = function (str) {\n      return !str;\n    };\n    var getIdFromAnchor = function (elm) {\n      var id = elm.getAttribute('id') || elm.getAttribute('name');\n      return id || '';\n    };\n    var isAnchor = function (elm) {\n      return elm && elm.nodeName.toLowerCase() === 'a';\n    };\n    var isNamedAnchor = function (elm) {\n      return isAnchor(elm) && !elm.getAttribute('href') && getIdFromAnchor(elm) !== '';\n    };\n    var isEmptyNamedAnchor = function (elm) {\n      return isNamedAnchor(elm) && !elm.firstChild;\n    };\n\n    var removeEmptyNamedAnchorsInSelection = function (editor) {\n      var dom = editor.dom;\n      global$1(dom).walk(editor.selection.getRng(), function (nodes) {\n        global.each(nodes, function (node) {\n          if (isEmptyNamedAnchor(node)) {\n            dom.remove(node, false);\n          }\n        });\n      });\n    };\n    var isValidId = function (id) {\n      return /^[A-Za-z][A-Za-z0-9\\-:._]*$/.test(id);\n    };\n    var getNamedAnchor = function (editor) {\n      return editor.dom.getParent(editor.selection.getStart(), namedAnchorSelector);\n    };\n    var getId = function (editor) {\n      var anchor = getNamedAnchor(editor);\n      if (anchor) {\n        return getIdFromAnchor(anchor);\n      } else {\n        return '';\n      }\n    };\n    var createAnchor = function (editor, id) {\n      editor.undoManager.transact(function () {\n        if (!allowHtmlInNamedAnchor(editor)) {\n          editor.selection.collapse(true);\n        }\n        if (editor.selection.isCollapsed()) {\n          editor.insertContent(editor.dom.createHTML('a', { id: id }));\n        } else {\n          removeEmptyNamedAnchorsInSelection(editor);\n          editor.formatter.remove('namedAnchor', null, null, true);\n          editor.formatter.apply('namedAnchor', { value: id });\n          editor.addVisual();\n        }\n      });\n    };\n    var updateAnchor = function (editor, id, anchorElement) {\n      anchorElement.removeAttribute('name');\n      anchorElement.id = id;\n      editor.addVisual();\n      editor.undoManager.add();\n    };\n    var insert = function (editor, id) {\n      var anchor = getNamedAnchor(editor);\n      if (anchor) {\n        updateAnchor(editor, id, anchor);\n      } else {\n        createAnchor(editor, id);\n      }\n      editor.focus();\n    };\n\n    var insertAnchor = function (editor, newId) {\n      if (!isValidId(newId)) {\n        editor.windowManager.alert('Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.');\n        return false;\n      } else {\n        insert(editor, newId);\n        return true;\n      }\n    };\n    var open = function (editor) {\n      var currentId = getId(editor);\n      editor.windowManager.open({\n        title: 'Anchor',\n        size: 'normal',\n        body: {\n          type: 'panel',\n          items: [{\n              name: 'id',\n              type: 'input',\n              label: 'ID',\n              placeholder: 'example'\n            }]\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: { id: currentId },\n        onSubmit: function (api) {\n          if (insertAnchor(editor, api.getData().id)) {\n            api.close();\n          }\n        }\n      });\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mceAnchor', function () {\n        open(editor);\n      });\n    };\n\n    var isNamedAnchorNode = function (node) {\n      return node && isEmptyString(node.attr('href')) && !isEmptyString(node.attr('id') || node.attr('name'));\n    };\n    var isEmptyNamedAnchorNode = function (node) {\n      return isNamedAnchorNode(node) && !node.firstChild;\n    };\n    var setContentEditable = function (state) {\n      return function (nodes) {\n        for (var i = 0; i < nodes.length; i++) {\n          var node = nodes[i];\n          if (isEmptyNamedAnchorNode(node)) {\n            node.attr('contenteditable', state);\n          }\n        }\n      };\n    };\n    var setup = function (editor) {\n      editor.on('PreInit', function () {\n        editor.parser.addNodeFilter('a', setContentEditable('false'));\n        editor.serializer.addNodeFilter('a', setContentEditable(null));\n      });\n    };\n\n    var registerFormats = function (editor) {\n      editor.formatter.register('namedAnchor', {\n        inline: 'a',\n        selector: namedAnchorSelector,\n        remove: 'all',\n        split: true,\n        deep: true,\n        attributes: { id: '%value' },\n        onmatch: function (node, _fmt, _itemName) {\n          return isNamedAnchor(node);\n        }\n      });\n    };\n\n    var register = function (editor) {\n      editor.ui.registry.addToggleButton('anchor', {\n        icon: 'bookmark',\n        tooltip: 'Anchor',\n        onAction: function () {\n          return editor.execCommand('mceAnchor');\n        },\n        onSetup: function (buttonApi) {\n          return editor.selection.selectorChangedWithUnbind('a:not([href])', buttonApi.setActive).unbind;\n        }\n      });\n      editor.ui.registry.addMenuItem('anchor', {\n        icon: 'bookmark',\n        text: 'Anchor...',\n        onAction: function () {\n          return editor.execCommand('mceAnchor');\n        }\n      });\n    };\n\n    function Plugin () {\n      global$2.add('anchor', function (editor) {\n        setup(editor);\n        register$1(editor);\n        register(editor);\n        editor.on('PreInit', function () {\n          registerFormats(editor);\n        });\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"anchor\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/anchor')\n//   ES2015:\n//     import 'tinymce/plugins/anchor'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/anchor/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS,8BAA8B,OAAO;AAAA;AAG9D,UAAI,sBAAsB;AAC1B,UAAI,gBAAgB,SAAU,KAAK;AACjC,eAAO,CAAC;AAAA;AAEV,UAAI,kBAAkB,SAAU,KAAK;AACnC,YAAI,KAAK,IAAI,aAAa,SAAS,IAAI,aAAa;AACpD,eAAO,MAAM;AAAA;AAEf,UAAI,WAAW,SAAU,KAAK;AAC5B,eAAO,OAAO,IAAI,SAAS,kBAAkB;AAAA;AAE/C,UAAI,gBAAgB,SAAU,KAAK;AACjC,eAAO,SAAS,QAAQ,CAAC,IAAI,aAAa,WAAW,gBAAgB,SAAS;AAAA;AAEhF,UAAI,qBAAqB,SAAU,KAAK;AACtC,eAAO,cAAc,QAAQ,CAAC,IAAI;AAAA;AAGpC,UAAI,qCAAqC,SAAU,QAAQ;AACzD,YAAI,MAAM,OAAO;AACjB,iBAAS,KAAK,KAAK,OAAO,UAAU,UAAU,SAAU,OAAO;AAC7D,iBAAO,KAAK,OAAO,SAAU,MAAM;AACjC,gBAAI,mBAAmB,OAAO;AAC5B,kBAAI,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAKzB,UAAI,YAAY,SAAU,IAAI;AAC5B,eAAO,8BAA8B,KAAK;AAAA;AAE5C,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,OAAO,IAAI,UAAU,OAAO,UAAU,YAAY;AAAA;AAE3D,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI,SAAS,eAAe;AAC5B,YAAI,QAAQ;AACV,iBAAO,gBAAgB;AAAA,eAClB;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,eAAe,SAAU,QAAQ,IAAI;AACvC,eAAO,YAAY,SAAS,WAAY;AACtC,cAAI,CAAC,uBAAuB,SAAS;AACnC,mBAAO,UAAU,SAAS;AAAA;AAE5B,cAAI,OAAO,UAAU,eAAe;AAClC,mBAAO,cAAc,OAAO,IAAI,WAAW,KAAK,EAAE;AAAA,iBAC7C;AACL,+CAAmC;AACnC,mBAAO,UAAU,OAAO,eAAe,MAAM,MAAM;AACnD,mBAAO,UAAU,MAAM,eAAe,EAAE,OAAO;AAC/C,mBAAO;AAAA;AAAA;AAAA;AAIb,UAAI,eAAe,SAAU,QAAQ,IAAI,eAAe;AACtD,sBAAc,gBAAgB;AAC9B,sBAAc,KAAK;AACnB,eAAO;AACP,eAAO,YAAY;AAAA;AAErB,UAAI,SAAS,SAAU,QAAQ,IAAI;AACjC,YAAI,SAAS,eAAe;AAC5B,YAAI,QAAQ;AACV,uBAAa,QAAQ,IAAI;AAAA,eACpB;AACL,uBAAa,QAAQ;AAAA;AAEvB,eAAO;AAAA;AAGT,UAAI,eAAe,SAAU,QAAQ,OAAO;AAC1C,YAAI,CAAC,UAAU,QAAQ;AACrB,iBAAO,cAAc,MAAM;AAC3B,iBAAO;AAAA,eACF;AACL,iBAAO,QAAQ;AACf,iBAAO;AAAA;AAAA;AAGX,UAAI,OAAO,SAAU,QAAQ;AAC3B,YAAI,YAAY,MAAM;AACtB,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA;AAAA;AAAA,UAGnB,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA;AAAA,YAER;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,UAGb,aAAa,EAAE,IAAI;AAAA,UACnB,UAAU,SAAU,KAAK;AACvB,gBAAI,aAAa,QAAQ,IAAI,UAAU,KAAK;AAC1C,kBAAI;AAAA;AAAA;AAAA;AAAA;AAMZ,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,aAAa,WAAY;AACzC,eAAK;AAAA;AAAA;AAIT,UAAI,oBAAoB,SAAU,MAAM;AACtC,eAAO,QAAQ,cAAc,KAAK,KAAK,YAAY,CAAC,cAAc,KAAK,KAAK,SAAS,KAAK,KAAK;AAAA;AAEjG,UAAI,yBAAyB,SAAU,MAAM;AAC3C,eAAO,kBAAkB,SAAS,CAAC,KAAK;AAAA;AAE1C,UAAI,qBAAqB,SAAU,OAAO;AACxC,eAAO,SAAU,OAAO;AACtB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,OAAO,MAAM;AACjB,gBAAI,uBAAuB,OAAO;AAChC,mBAAK,KAAK,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAKrC,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,GAAG,WAAW,WAAY;AAC/B,iBAAO,OAAO,cAAc,KAAK,mBAAmB;AACpD,iBAAO,WAAW,cAAc,KAAK,mBAAmB;AAAA;AAAA;AAI5D,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,UAAU,SAAS,eAAe;AAAA,UACvC,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY,EAAE,IAAI;AAAA,UAClB,SAAS,SAAU,MAAM,MAAM,WAAW;AACxC,mBAAO,cAAc;AAAA;AAAA;AAAA;AAK3B,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,GAAG,SAAS,gBAAgB,UAAU;AAAA,UAC3C,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA,UAE5B,SAAS,SAAU,WAAW;AAC5B,mBAAO,OAAO,UAAU,0BAA0B,iBAAiB,UAAU,WAAW;AAAA;AAAA;AAG5F,eAAO,GAAG,SAAS,YAAY,UAAU;AAAA,UACvC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA;AAAA;AAKhC,wBAAmB;AACjB,iBAAS,IAAI,UAAU,SAAU,QAAQ;AACvC,gBAAM;AACN,qBAAW;AACX,mBAAS;AACT,iBAAO,GAAG,WAAW,WAAY;AAC/B,4BAAgB;AAAA;AAAA;AAAA;AAKtB;AAAA;AAAA;AAAA;;;ACrNJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,iCAAQ;", "names": []}