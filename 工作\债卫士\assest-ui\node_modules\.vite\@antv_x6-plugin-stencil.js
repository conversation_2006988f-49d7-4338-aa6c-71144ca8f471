import {
  GeometryUtil,
  Graph,
  Model,
  Node,
  Rectangle,
  View,
  loader_exports,
  main_exports,
  main_exports3 as main_exports2
} from "./chunk-7DXKJCQD.js";
import "./chunk-NPNDKBDY.js";
import "./chunk-WC6BDPVA.js";

// node_modules/@antv/x6-plugin-dnd/es/style/raw.js
var content = `.x6-widget-dnd {
  position: absolute;
  top: -10000px;
  left: -10000px;
  z-index: 999999;
  display: none;
  cursor: move;
  opacity: 0.7;
  pointer-events: 'cursor';
}
.x6-widget-dnd.dragging {
  display: inline-block;
}
.x6-widget-dnd.dragging * {
  pointer-events: none !important;
}
.x6-widget-dnd .x6-graph {
  background: transparent;
  box-shadow: none;
}
`;

// node_modules/@antv/x6-plugin-dnd/es/index.js
var __decorate = function(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var Dnd = class extends View {
  get targetScroller() {
    const target = this.options.target;
    const scroller = target.getPlugin("scroller");
    return scroller;
  }
  get targetGraph() {
    return this.options.target;
  }
  get targetModel() {
    return this.targetGraph.model;
  }
  get snapline() {
    const target = this.options.target;
    const snapline = target.getPlugin("snapline");
    return snapline;
  }
  constructor(options) {
    super();
    this.name = "dnd";
    this.options = Object.assign(Object.assign({}, Dnd.defaults), options);
    this.init();
  }
  init() {
    loader_exports.ensure(this.name, content);
    this.container = document.createElement("div");
    main_exports2.addClass(this.container, this.prefixClassName("widget-dnd"));
    this.draggingGraph = new Graph(Object.assign(Object.assign({}, this.options.delegateGraphOptions), { container: document.createElement("div"), width: 1, height: 1, async: false }));
    main_exports2.append(this.container, this.draggingGraph.container);
  }
  start(node, evt) {
    const e = evt;
    e.preventDefault();
    this.targetModel.startBatch("dnd");
    main_exports2.addClass(this.container, "dragging");
    main_exports2.appendTo(this.container, this.options.draggingContainer || document.body);
    this.sourceNode = node;
    this.prepareDragging(node, e.clientX, e.clientY);
    const local = this.updateNodePosition(e.clientX, e.clientY);
    if (this.isSnaplineEnabled()) {
      this.snapline.captureCursorOffset({
        e,
        node,
        cell: node,
        view: this.draggingView,
        x: local.x,
        y: local.y
      });
      this.draggingNode.on("change:position", this.snap, this);
    }
    this.delegateDocumentEvents(Dnd.documentEvents, e.data);
  }
  isSnaplineEnabled() {
    return this.snapline && this.snapline.isEnabled();
  }
  prepareDragging(sourceNode, clientX, clientY) {
    const draggingGraph = this.draggingGraph;
    const draggingModel = draggingGraph.model;
    const draggingNode = this.options.getDragNode(sourceNode, {
      sourceNode,
      draggingGraph,
      targetGraph: this.targetGraph
    });
    draggingNode.position(0, 0);
    let padding = 5;
    if (this.isSnaplineEnabled()) {
      padding += this.snapline.options.tolerance || 0;
    }
    if (this.isSnaplineEnabled() || this.options.scaled) {
      const scale = this.targetGraph.transform.getScale();
      draggingGraph.scale(scale.sx, scale.sy);
      padding *= Math.max(scale.sx, scale.sy);
    } else {
      draggingGraph.scale(1, 1);
    }
    this.clearDragging();
    draggingModel.resetCells([draggingNode]);
    const delegateView = draggingGraph.findViewByCell(draggingNode);
    delegateView.undelegateEvents();
    delegateView.cell.off("changed");
    draggingGraph.fitToContent({
      padding,
      allowNewOrigin: "any",
      useCellGeometry: false
    });
    const bbox = delegateView.getBBox();
    this.geometryBBox = delegateView.getBBox({ useCellGeometry: true });
    this.delta = this.geometryBBox.getTopLeft().diff(bbox.getTopLeft());
    this.draggingNode = draggingNode;
    this.draggingView = delegateView;
    this.draggingBBox = draggingNode.getBBox();
    this.padding = padding;
    this.originOffset = this.updateGraphPosition(clientX, clientY);
  }
  updateGraphPosition(clientX, clientY) {
    const scrollTop = document.body.scrollTop || document.documentElement.scrollTop;
    const scrollLeft = document.body.scrollLeft || document.documentElement.scrollLeft;
    const delta = this.delta;
    const nodeBBox = this.geometryBBox;
    const padding = this.padding || 5;
    const offset = {
      left: clientX - delta.x - nodeBBox.width / 2 - padding + scrollLeft,
      top: clientY - delta.y - nodeBBox.height / 2 - padding + scrollTop
    };
    if (this.draggingGraph) {
      main_exports2.css(this.container, {
        left: `${offset.left}px`,
        top: `${offset.top}px`
      });
    }
    return offset;
  }
  updateNodePosition(x, y) {
    const local = this.targetGraph.clientToLocal(x, y);
    const bbox = this.draggingBBox;
    local.x -= bbox.width / 2;
    local.y -= bbox.height / 2;
    this.draggingNode.position(local.x, local.y);
    return local;
  }
  snap({ cell, current, options }) {
    const node = cell;
    if (options.snapped) {
      const bbox = this.draggingBBox;
      node.position(bbox.x + options.tx, bbox.y + options.ty, { silent: true });
      this.draggingView.translate();
      node.position(current.x, current.y, { silent: true });
      this.snapOffset = {
        x: options.tx,
        y: options.ty
      };
    } else {
      this.snapOffset = null;
    }
  }
  onDragging(evt) {
    const draggingView = this.draggingView;
    if (draggingView) {
      evt.preventDefault();
      const e = this.normalizeEvent(evt);
      const clientX = e.clientX;
      const clientY = e.clientY;
      this.updateGraphPosition(clientX, clientY);
      const local = this.updateNodePosition(clientX, clientY);
      const embeddingMode = this.targetGraph.options.embedding.enabled;
      const isValidArea = (embeddingMode || this.isSnaplineEnabled()) && this.isInsideValidArea({
        x: clientX,
        y: clientY
      });
      if (embeddingMode) {
        draggingView.setEventData(e, {
          graph: this.targetGraph,
          candidateEmbedView: this.candidateEmbedView
        });
        const data = draggingView.getEventData(e);
        if (isValidArea) {
          draggingView.processEmbedding(e, data);
        } else {
          draggingView.clearEmbedding(data);
        }
        this.candidateEmbedView = data.candidateEmbedView;
      }
      if (this.isSnaplineEnabled()) {
        if (isValidArea) {
          this.snapline.snapOnMoving({
            e,
            view: draggingView,
            x: local.x,
            y: local.y
          });
        } else {
          this.snapline.hide();
        }
      }
    }
  }
  onDragEnd(evt) {
    const draggingNode = this.draggingNode;
    if (draggingNode) {
      const e = this.normalizeEvent(evt);
      const draggingView = this.draggingView;
      const draggingBBox = this.draggingBBox;
      const snapOffset = this.snapOffset;
      let x = draggingBBox.x;
      let y = draggingBBox.y;
      if (snapOffset) {
        x += snapOffset.x;
        y += snapOffset.y;
      }
      draggingNode.position(x, y, { silent: true });
      const ret = this.drop(draggingNode, { x: e.clientX, y: e.clientY });
      const callback = (node) => {
        if (node) {
          this.onDropped(draggingNode);
          if (this.targetGraph.options.embedding.enabled && draggingView) {
            draggingView.setEventData(e, {
              cell: node,
              graph: this.targetGraph,
              candidateEmbedView: this.candidateEmbedView
            });
            draggingView.finalizeEmbedding(e, draggingView.getEventData(e));
          }
        } else {
          this.onDropInvalid();
        }
        this.candidateEmbedView = null;
        this.targetModel.stopBatch("dnd");
      };
      if (main_exports.isAsync(ret)) {
        this.undelegateDocumentEvents();
        ret.then(callback);
      } else {
        callback(ret);
      }
    }
  }
  clearDragging() {
    if (this.draggingNode) {
      this.sourceNode = null;
      this.draggingNode.remove();
      this.draggingNode = null;
      this.draggingView = null;
      this.delta = null;
      this.padding = null;
      this.snapOffset = null;
      this.originOffset = null;
      this.undelegateDocumentEvents();
    }
  }
  onDropped(draggingNode) {
    if (this.draggingNode === draggingNode) {
      this.clearDragging();
      main_exports2.removeClass(this.container, "dragging");
      main_exports2.remove(this.container);
    }
  }
  onDropInvalid() {
    const draggingNode = this.draggingNode;
    if (draggingNode) {
      this.onDropped(draggingNode);
    }
  }
  isInsideValidArea(p) {
    let targetRect;
    let dndRect = null;
    const targetGraph = this.targetGraph;
    const targetScroller = this.targetScroller;
    if (this.options.dndContainer) {
      dndRect = this.getDropArea(this.options.dndContainer);
    }
    const isInsideDndRect = dndRect && dndRect.containsPoint(p);
    if (targetScroller) {
      if (targetScroller.options.autoResize) {
        targetRect = this.getDropArea(targetScroller.container);
      } else {
        const outter = this.getDropArea(targetScroller.container);
        targetRect = this.getDropArea(targetGraph.container).intersectsWithRect(outter);
      }
    } else {
      targetRect = this.getDropArea(targetGraph.container);
    }
    return !isInsideDndRect && targetRect && targetRect.containsPoint(p);
  }
  getDropArea(elem) {
    const offset = main_exports2.offset(elem);
    const scrollTop = document.body.scrollTop || document.documentElement.scrollTop;
    const scrollLeft = document.body.scrollLeft || document.documentElement.scrollLeft;
    return Rectangle.create({
      x: offset.left + parseInt(main_exports2.css(elem, "border-left-width"), 10) - scrollLeft,
      y: offset.top + parseInt(main_exports2.css(elem, "border-top-width"), 10) - scrollTop,
      width: elem.clientWidth,
      height: elem.clientHeight
    });
  }
  drop(draggingNode, pos) {
    if (this.isInsideValidArea(pos)) {
      const targetGraph = this.targetGraph;
      const targetModel = targetGraph.model;
      const local = targetGraph.clientToLocal(pos);
      const sourceNode = this.sourceNode;
      const droppingNode = this.options.getDropNode(draggingNode, {
        sourceNode,
        draggingNode,
        targetGraph: this.targetGraph,
        draggingGraph: this.draggingGraph
      });
      const bbox = droppingNode.getBBox();
      local.x += bbox.x - bbox.width / 2;
      local.y += bbox.y - bbox.height / 2;
      const gridSize = this.snapOffset ? 1 : targetGraph.getGridSize();
      droppingNode.position(GeometryUtil.snapToGrid(local.x, gridSize), GeometryUtil.snapToGrid(local.y, gridSize));
      droppingNode.removeZIndex();
      const validateNode = this.options.validateNode;
      const ret = validateNode ? validateNode(droppingNode, {
        sourceNode,
        draggingNode,
        droppingNode,
        targetGraph,
        draggingGraph: this.draggingGraph
      }) : true;
      if (typeof ret === "boolean") {
        if (ret) {
          targetModel.addCell(droppingNode, { stencil: this.cid });
          return droppingNode;
        }
        return null;
      }
      return main_exports.toDeferredBoolean(ret).then((valid) => {
        if (valid) {
          targetModel.addCell(droppingNode, { stencil: this.cid });
          return droppingNode;
        }
        return null;
      });
    }
    return null;
  }
  onRemove() {
    if (this.draggingGraph) {
      this.draggingGraph.view.remove();
      this.draggingGraph.dispose();
    }
  }
  dispose() {
    this.remove();
    loader_exports.clean(this.name);
  }
};
__decorate([
  View.dispose()
], Dnd.prototype, "dispose", null);
(function(Dnd2) {
  Dnd2.defaults = {
    getDragNode: (sourceNode) => sourceNode.clone(),
    getDropNode: (draggingNode) => draggingNode.clone()
  };
  Dnd2.documentEvents = {
    mousemove: "onDragging",
    touchmove: "onDragging",
    mouseup: "onDragEnd",
    touchend: "onDragEnd",
    touchcancel: "onDragEnd"
  };
})(Dnd || (Dnd = {}));

// node_modules/@antv/x6-plugin-stencil/es/grid.js
function grid(cells, options = {}) {
  const model = Model.isModel(cells) ? cells : new Model().resetCells(cells, {
    sort: false,
    dryrun: true
  });
  const nodes = model.getNodes();
  const columns = options.columns || 1;
  const rows = Math.ceil(nodes.length / columns);
  const dx = options.dx || 0;
  const dy = options.dy || 0;
  const centre = options.center !== false;
  const resizeToFit = options.resizeToFit === true;
  const marginX = options.marginX || 0;
  const marginY = options.marginY || 0;
  const columnWidths = [];
  let columnWidth = options.columnWidth;
  if (columnWidth === "compact") {
    for (let j = 0; j < columns; j += 1) {
      const items = GridLayout.getNodesInColumn(nodes, j, columns);
      columnWidths.push(GridLayout.getMaxDim(items, "width") + dx);
    }
  } else {
    if (columnWidth == null || columnWidth === "auto") {
      columnWidth = GridLayout.getMaxDim(nodes, "width") + dx;
    }
    for (let i = 0; i < columns; i += 1) {
      columnWidths.push(columnWidth);
    }
  }
  const columnLefts = GridLayout.accumulate(columnWidths, marginX);
  const rowHeights = [];
  let rowHeight = options.rowHeight;
  if (rowHeight === "compact") {
    for (let i = 0; i < rows; i += 1) {
      const items = GridLayout.getNodesInRow(nodes, i, columns);
      rowHeights.push(GridLayout.getMaxDim(items, "height") + dy);
    }
  } else {
    if (rowHeight == null || rowHeight === "auto") {
      rowHeight = GridLayout.getMaxDim(nodes, "height") + dy;
    }
    for (let i = 0; i < rows; i += 1) {
      rowHeights.push(rowHeight);
    }
  }
  const rowTops = GridLayout.accumulate(rowHeights, marginY);
  model.startBatch("layout");
  nodes.forEach((node, index) => {
    const rowIndex = index % columns;
    const columnIndex = Math.floor(index / columns);
    const columnWidth2 = columnWidths[rowIndex];
    const rowHeight2 = rowHeights[columnIndex];
    let cx = 0;
    let cy = 0;
    let size = node.getSize();
    if (resizeToFit) {
      let width = columnWidth2 - 2 * dx;
      let height = rowHeight2 - 2 * dy;
      const calcHeight = size.height * (size.width ? width / size.width : 1);
      const calcWidth = size.width * (size.height ? height / size.height : 1);
      if (rowHeight2 < calcHeight) {
        width = calcWidth;
      } else {
        height = calcHeight;
      }
      size = {
        width,
        height
      };
      node.setSize(size, options);
    }
    if (centre) {
      cx = (columnWidth2 - size.width) / 2;
      cy = (rowHeight2 - size.height) / 2;
    }
    node.position(columnLefts[rowIndex] + dx + cx, rowTops[columnIndex] + dy + cy, options);
  });
  model.stopBatch("layout");
}
var GridLayout;
(function(GridLayout2) {
  function getMaxDim(nodes, name) {
    return nodes.reduce((memo, node) => Math.max(node === null || node === void 0 ? void 0 : node.getSize()[name], memo), 0);
  }
  GridLayout2.getMaxDim = getMaxDim;
  function getNodesInRow(nodes, rowIndex, columnCount) {
    const res = [];
    for (let i = columnCount * rowIndex, ii = i + columnCount; i < ii; i += 1) {
      if (nodes[i])
        res.push(nodes[i]);
    }
    return res;
  }
  GridLayout2.getNodesInRow = getNodesInRow;
  function getNodesInColumn(nodes, columnIndex, columnCount) {
    const res = [];
    for (let i = columnIndex, ii = nodes.length; i < ii; i += columnCount) {
      if (nodes[i])
        res.push(nodes[i]);
    }
    return res;
  }
  GridLayout2.getNodesInColumn = getNodesInColumn;
  function accumulate(items, start) {
    return items.reduce((memo, item, i) => {
      memo.push(memo[i] + item);
      return memo;
    }, [start || 0]);
  }
  GridLayout2.accumulate = accumulate;
})(GridLayout || (GridLayout = {}));

// node_modules/@antv/x6-plugin-stencil/es/style/raw.js
var content2 = `.x6-widget-dnd {
  position: absolute;
  top: -10000px;
  left: -10000px;
  z-index: 999999;
  display: none;
  cursor: move;
  opacity: 0.7;
  pointer-events: 'cursor';
}
.x6-widget-dnd.dragging {
  display: inline-block;
}
.x6-widget-dnd.dragging * {
  pointer-events: none !important;
}
.x6-widget-dnd .x6-graph {
  background: transparent;
  box-shadow: none;
}
.x6-widget-stencil {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.x6-widget-stencil::after {
  position: absolute;
  top: 0;
  display: block;
  width: 100%;
  height: 20px;
  padding: 8px 0;
  line-height: 20px;
  text-align: center;
  opacity: 0;
  transition: top 0.1s linear, opacity 0.1s linear;
  content: ' ';
  pointer-events: none;
}
.x6-widget-stencil-content {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: auto;
  overflow-x: hidden;
  overflow-y: auto;
}
.x6-widget-stencil .x6-node [magnet]:not([magnet='passive']) {
  pointer-events: none;
}
.x6-widget-stencil-group {
  padding: 0;
  padding-bottom: 8px;
  overflow: hidden;
  user-select: none;
}
.x6-widget-stencil-group.collapsed {
  height: auto;
  padding-bottom: 0;
}
.x6-widget-stencil-group-title {
  position: relative;
  margin-top: 0;
  margin-bottom: 0;
  padding: 4px;
  cursor: pointer;
}
.x6-widget-stencil-title,
.x6-widget-stencil-group > .x6-widget-stencil-group-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  user-select: none;
}
.x6-widget-stencil .unmatched {
  opacity: 0.3;
}
.x6-widget-stencil .x6-node.unmatched {
  display: none;
}
.x6-widget-stencil-group.unmatched {
  display: none;
}
.x6-widget-stencil-search-text {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  width: 100%;
  height: 30px;
  max-height: 30px;
  line-height: 30px;
  outline: 0;
}
.x6-widget-stencil.not-found::after {
  opacity: 1;
  content: attr(data-not-found-text);
}
.x6-widget-stencil.not-found.searchable::after {
  top: 30px;
}
.x6-widget-stencil.not-found.searchable.collapsable::after {
  top: 50px;
}
.x6-widget-stencil {
  color: #333;
  background: #f5f5f5;
}
.x6-widget-stencil-content {
  position: absolute;
}
.x6-widget-stencil.collapsable > .x6-widget-stencil-content {
  top: 32px;
}
.x6-widget-stencil.searchable > .x6-widget-stencil-content {
  top: 80px;
}
.x6-widget-stencil.not-found::after {
  position: absolute;
}
.x6-widget-stencil.not-found.searchable.collapsable::after {
  top: 80px;
}
.x6-widget-stencil.not-found.searchable::after {
  top: 60px;
}
.x6-widget-stencil-group {
  height: auto;
  margin-bottom: 1px;
  padding: 0;
  transition: none;
}
.x6-widget-stencil-group .x6-graph {
  background: transparent;
  box-shadow: none;
}
.x6-widget-stencil-group.collapsed {
  height: auto;
  max-height: 31px;
}
.x6-widget-stencil-title,
.x6-widget-stencil-group > .x6-widget-stencil-group-title {
  position: relative;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  height: 32px;
  padding: 0 5px 0 8px;
  color: #666;
  font-weight: 700;
  font-size: 12px;
  line-height: 32px;
  cursor: default;
  transition: all 0.3;
}
.x6-widget-stencil-title:hover,
.x6-widget-stencil-group > .x6-widget-stencil-group-title:hover {
  color: #444;
}
.x6-widget-stencil-title {
  background: #e9e9e9;
}
.x6-widget-stencil-group > .x6-widget-stencil-group-title {
  background: #ededed;
}
.x6-widget-stencil.collapsable > .x6-widget-stencil-title,
.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title {
  padding-left: 32px;
  cursor: pointer;
}
.x6-widget-stencil.collapsable > .x6-widget-stencil-title::before,
.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title::before {
  position: absolute;
  top: 6px;
  left: 8px;
  display: block;
  width: 18px;
  height: 18px;
  margin: 0;
  padding: 0;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: 0 0;
  border: none;
  content: ' ';
}
.x6-widget-stencil.collapsable > .x6-widget-stencil-title::before,
.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title::before {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iIzAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj48cGF0aCBkPSJNOS4zNzUuNUM0LjY4Ny41Ljg3NSA0LjMxMy44NzUgOWMwIDQuNjg4IDMuODEyIDguNSA4LjUgOC41IDQuNjg3IDAgOC41LTMuODEyIDguNS04LjUgMC00LjY4Ny0zLjgxMy04LjUtOC41LTguNXptMCAxNS44ODZDNS4zMDMgMTYuMzg2IDEuOTkgMTMuMDcyIDEuOTkgOXMzLjMxMi03LjM4NSA3LjM4NS03LjM4NVMxNi43NiA0LjkyOCAxNi43NiA5YzAgNC4wNzItMy4zMTMgNy4zODYtNy4zODUgNy4zODZ6Ii8+PHBhdGggZD0iTTEyLjc1MyA4LjQ0M0g1Ljk5N2EuNTU4LjU1OCAwIDAwMCAxLjExNmg2Ljc1NmEuNTU4LjU1OCAwIDAwMC0xLjExNnoiLz48L2c+PC9zdmc+');
  opacity: 0.4;
  transition: all 0.3s;
}
.x6-widget-stencil.collapsable > .x6-widget-stencil-title:hover::before,
.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title:hover::before {
  opacity: 0.6;
}
.x6-widget-stencil.collapsable.collapsed > .x6-widget-stencil-title::before,
.x6-widget-stencil-group.collapsable.collapsed > .x6-widget-stencil-group-title::before {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iIzAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj48cGF0aCBkPSJNOS4zNzUuNUM0LjY4Ny41Ljg3NSA0LjMxMy44NzUgOWMwIDQuNjg4IDMuODEyIDguNSA4LjUgOC41IDQuNjg3IDAgOC41LTMuODEyIDguNS04LjUgMC00LjY4Ny0zLjgxMy04LjUtOC41LTguNXptMCAxNS44ODZDNS4zMDMgMTYuMzg2IDEuOTkgMTMuMDcyIDEuOTkgOXMzLjMxMi03LjM4NSA3LjM4NS03LjM4NVMxNi43NiA0LjkyOCAxNi43NiA5YzAgNC4wNzItMy4zMTMgNy4zODYtNy4zODUgNy4zODZ6Ii8+PHBhdGggZD0iTTEyLjc1MyA4LjQ0M0g1Ljk5N2EuNTU4LjU1OCAwIDAwMCAxLjExNmg2Ljc1NmEuNTU4LjU1OCAwIDAwMC0xLjExNnoiLz48cGF0aCBkPSJNOC44MTcgNS42MjN2Ni43NTZhLjU1OC41NTggMCAwMDEuMTE2IDBWNS42MjNhLjU1OC41NTggMCAxMC0xLjExNiAweiIvPjwvZz48L3N2Zz4=');
  opacity: 0.4;
}
.x6-widget-stencil.collapsable.collapsed > .x6-widget-stencil-title:hover::before,
.x6-widget-stencil-group.collapsable.collapsed > .x6-widget-stencil-group-title:hover::before {
  opacity: 0.6;
}
.x6-widget-stencil input[type='search'] {
  appearance: textfield;
}
.x6-widget-stencil input[type='search']::-webkit-search-cancel-button,
.x6-widget-stencil input[type='search']::-webkit-search-decoration {
  appearance: none;
}
.x6-widget-stencil-search-text {
  display: block;
  width: 90%;
  margin: 8px 5%;
  padding-left: 8px;
  color: #333;
  background: #fff;
  border: 1px solid #e9e9e9;
  border-radius: 12px;
  outline: 0;
}
.x6-widget-stencil-search-text:focus {
  outline: 0;
}
.x6-widget-stencil::after {
  color: #808080;
  font-weight: 600;
  font-size: 12px;
  background: 0 0;
}
`;

// node_modules/@antv/x6-plugin-stencil/es/index.js
var __decorate2 = function(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var Stencil = class extends View {
  get targetScroller() {
    const target = this.options.target;
    const scroller = target.getPlugin("scroller");
    return scroller;
  }
  get targetGraph() {
    return this.options.target;
  }
  get targetModel() {
    return this.targetGraph.model;
  }
  constructor(options = {}) {
    super();
    this.name = "stencil";
    loader_exports.ensure(this.name, content2);
    this.graphs = {};
    this.groups = {};
    this.options = Object.assign(Object.assign({}, Stencil.defaultOptions), options);
    this.init();
  }
  init() {
    this.dnd = new Dnd(this.options);
    this.onSearch = main_exports.debounce(this.onSearch, 200);
    this.initContainer();
    this.initSearch();
    this.initContent();
    this.initGroups();
    this.setTitle();
    this.startListening();
  }
  load(data, groupName) {
    if (Array.isArray(data)) {
      this.loadGroup(data, groupName);
    } else if (this.options.groups) {
      Object.keys(this.options.groups).forEach((groupName2) => {
        if (data[groupName2]) {
          this.loadGroup(data[groupName2], groupName2);
        }
      });
    }
    return this;
  }
  unload(data, groupName) {
    if (Array.isArray(data)) {
      this.loadGroup(data, groupName, true);
    } else if (this.options.groups) {
      Object.keys(this.options.groups).forEach((groupName2) => {
        if (data[groupName2]) {
          this.loadGroup(data[groupName2], groupName2, true);
        }
      });
    }
    return this;
  }
  toggleGroup(groupName) {
    if (this.isGroupCollapsed(groupName)) {
      this.expandGroup(groupName);
    } else {
      this.collapseGroup(groupName);
    }
    return this;
  }
  collapseGroup(groupName) {
    if (this.isGroupCollapsable(groupName)) {
      const group = this.groups[groupName];
      if (group && !this.isGroupCollapsed(groupName)) {
        this.trigger("group:collapse", { name: groupName });
        main_exports2.addClass(group, "collapsed");
      }
    }
    return this;
  }
  expandGroup(groupName) {
    if (this.isGroupCollapsable(groupName)) {
      const group = this.groups[groupName];
      if (group && this.isGroupCollapsed(groupName)) {
        this.trigger("group:expand", { name: groupName });
        main_exports2.removeClass(group, "collapsed");
      }
    }
    return this;
  }
  isGroupCollapsable(groupName) {
    const group = this.groups[groupName];
    return main_exports2.hasClass(group, "collapsable");
  }
  isGroupCollapsed(groupName) {
    const group = this.groups[groupName];
    return group && main_exports2.hasClass(group, "collapsed");
  }
  collapseGroups() {
    Object.keys(this.groups).forEach((groupName) => this.collapseGroup(groupName));
    return this;
  }
  expandGroups() {
    Object.keys(this.groups).forEach((groupName) => this.expandGroup(groupName));
    return this;
  }
  resizeGroup(groupName, size) {
    const graph = this.graphs[groupName];
    if (graph) {
      graph.resize(size.width, size.height);
    }
    return this;
  }
  addGroup(group) {
    const groups = Array.isArray(group) ? group : [group];
    if (this.options.groups) {
      this.options.groups.push(...groups);
    } else {
      this.options.groups = groups;
    }
    groups.forEach((group2) => this.initGroup(group2));
  }
  removeGroup(groupName) {
    const groupNames = Array.isArray(groupName) ? groupName : [groupName];
    if (this.options.groups) {
      this.options.groups = this.options.groups.filter((group) => !groupNames.includes(group.name));
      groupNames.forEach((groupName2) => {
        const graph = this.graphs[groupName2];
        this.unregisterGraphEvents(graph);
        graph.dispose();
        delete this.graphs[groupName2];
        const elem = this.groups[groupName2];
        main_exports2.remove(elem);
        delete this.groups[groupName2];
      });
    }
  }
  initContainer() {
    this.container = document.createElement("div");
    main_exports2.addClass(this.container, this.prefixClassName(ClassNames.base));
    main_exports2.attr(this.container, "data-not-found-text", this.options.notFoundText || "No matches found");
  }
  initContent() {
    this.content = document.createElement("div");
    main_exports2.addClass(this.content, this.prefixClassName(ClassNames.content));
    main_exports2.appendTo(this.content, this.container);
  }
  initSearch() {
    if (this.options.search) {
      main_exports2.addClass(this.container, "searchable");
      main_exports2.append(this.container, this.renderSearch());
    }
  }
  initGroup(group) {
    const globalGraphOptions = this.options.stencilGraphOptions || {};
    const groupElem = document.createElement("div");
    main_exports2.addClass(groupElem, this.prefixClassName(ClassNames.group));
    main_exports2.attr(groupElem, "data-name", group.name);
    if (group.collapsable == null && this.options.collapsable || group.collapsable !== false) {
      main_exports2.addClass(groupElem, "collapsable");
    }
    main_exports2.toggleClass(groupElem, "collapsed", group.collapsed === true);
    const title = document.createElement("h3");
    main_exports2.addClass(title, this.prefixClassName(ClassNames.groupTitle));
    title.innerHTML = group.title || group.name;
    const content3 = document.createElement("div");
    main_exports2.addClass(content3, this.prefixClassName(ClassNames.groupContent));
    const graphOptionsInGroup = group.graphOptions;
    const graph = new Graph(Object.assign(Object.assign(Object.assign({}, globalGraphOptions), graphOptionsInGroup), { container: document.createElement("div"), model: globalGraphOptions.model || new Model(), width: group.graphWidth || this.options.stencilGraphWidth, height: group.graphHeight || this.options.stencilGraphHeight, interacting: false, preventDefaultBlankAction: false }));
    this.registerGraphEvents(graph);
    main_exports2.append(content3, graph.container);
    main_exports2.append(groupElem, [title, content3]);
    main_exports2.appendTo(groupElem, this.content);
    this.groups[group.name] = groupElem;
    this.graphs[group.name] = graph;
  }
  initGroups() {
    this.clearGroups();
    this.setCollapsableState();
    if (this.options.groups && this.options.groups.length) {
      this.options.groups.forEach((group) => {
        this.initGroup(group);
      });
    } else {
      const globalGraphOptions = this.options.stencilGraphOptions || {};
      const graph = new Graph(Object.assign(Object.assign({}, globalGraphOptions), { container: document.createElement("div"), model: globalGraphOptions.model || new Model(), width: this.options.stencilGraphWidth, height: this.options.stencilGraphHeight, interacting: false, preventDefaultBlankAction: false }));
      main_exports2.append(this.content, graph.container);
      this.graphs[Private.defaultGroupName] = graph;
    }
  }
  setCollapsableState() {
    this.options.collapsable = this.options.collapsable && this.options.groups && this.options.groups.some((group) => group.collapsable !== false);
    if (this.options.collapsable) {
      main_exports2.addClass(this.container, "collapsable");
      const collapsed = this.options.groups && this.options.groups.every((group) => group.collapsed || group.collapsable === false);
      if (collapsed) {
        main_exports2.addClass(this.container, "collapsed");
      } else {
        main_exports2.removeClass(this.container, "collapsed");
      }
    } else {
      main_exports2.removeClass(this.container, "collapsable");
    }
  }
  setTitle() {
    const title = document.createElement("div");
    main_exports2.addClass(title, this.prefixClassName(ClassNames.title));
    title.innerHTML = this.options.title;
    main_exports2.appendTo(title, this.container);
  }
  renderSearch() {
    const elem = document.createElement("div");
    main_exports2.addClass(elem, this.prefixClassName(ClassNames.search));
    const input = document.createElement("input");
    main_exports2.attr(input, {
      type: "search",
      placeholder: this.options.placeholder || "Search"
    });
    main_exports2.addClass(input, this.prefixClassName(ClassNames.searchText));
    main_exports2.append(elem, input);
    return elem;
  }
  startListening() {
    const title = this.prefixClassName(ClassNames.title);
    const searchText = this.prefixClassName(ClassNames.searchText);
    const groupTitle = this.prefixClassName(ClassNames.groupTitle);
    this.delegateEvents({
      [`click .${title}`]: "onTitleClick",
      [`touchstart .${title}`]: "onTitleClick",
      [`click .${groupTitle}`]: "onGroupTitleClick",
      [`touchstart .${groupTitle}`]: "onGroupTitleClick",
      [`input .${searchText}`]: "onSearch",
      [`focusin .${searchText}`]: "onSearchFocusIn",
      [`focusout .${searchText}`]: "onSearchFocusOut"
    });
  }
  stopListening() {
    this.undelegateEvents();
  }
  registerGraphEvents(graph) {
    graph.on("cell:mousedown", this.onDragStart, this);
  }
  unregisterGraphEvents(graph) {
    graph.off("cell:mousedown", this.onDragStart, this);
  }
  loadGroup(cells, groupName, reverse) {
    const model = this.getModel(groupName);
    if (model) {
      const nodes = cells.map((cell) => Node.isNode(cell) ? cell : Node.create(cell));
      if (reverse === true) {
        model.removeCells(nodes);
      } else {
        model.resetCells(nodes);
      }
    }
    const group = this.getGroup(groupName);
    let height = this.options.stencilGraphHeight;
    if (group && group.graphHeight != null) {
      height = group.graphHeight;
    }
    const layout = group && group.layout || this.options.layout;
    if (layout && model) {
      main_exports.call(layout, this, model, group);
    }
    if (!height) {
      const graph = this.getGraph(groupName);
      graph.fitToContent({
        minWidth: graph.options.width,
        gridHeight: 1,
        padding: group && group.graphPadding || this.options.stencilGraphPadding || 10
      });
    }
    return this;
  }
  onDragStart(args) {
    const { e, node } = args;
    const group = this.getGroupByNode(node);
    if (group && group.nodeMovable === false) {
      return;
    }
    this.dnd.start(node, e);
  }
  filter(keyword, filter) {
    const found = Object.keys(this.graphs).reduce((memo, groupName) => {
      const graph = this.graphs[groupName];
      const name = groupName === Private.defaultGroupName ? null : groupName;
      const items = graph.model.getNodes().filter((cell) => {
        let matched = false;
        if (typeof filter === "function") {
          matched = main_exports.call(filter, this, cell, keyword, name, this);
        } else if (typeof filter === "boolean") {
          matched = filter;
        } else {
          matched = this.isCellMatched(cell, keyword, filter, keyword.toLowerCase() !== keyword);
        }
        const view = graph.renderer.findViewByCell(cell);
        if (view) {
          main_exports2.toggleClass(view.container, "unmatched", !matched);
        }
        return matched;
      });
      const found2 = items.length > 0;
      const options = this.options;
      const model = new Model();
      model.resetCells(items);
      if (options.layout) {
        main_exports.call(options.layout, this, model, this.getGroup(groupName));
      }
      if (this.groups[groupName]) {
        main_exports2.toggleClass(this.groups[groupName], "unmatched", !found2);
      }
      graph.fitToContent({
        gridWidth: 1,
        gridHeight: 1,
        padding: options.stencilGraphPadding || 10
      });
      return memo || found2;
    }, false);
    main_exports2.toggleClass(this.container, "not-found", !found);
  }
  isCellMatched(cell, keyword, filters, ignoreCase) {
    if (keyword && filters) {
      return Object.keys(filters).some((shape) => {
        if (shape === "*" || cell.shape === shape) {
          const filter = filters[shape];
          if (typeof filter === "boolean") {
            return filter;
          }
          const paths = Array.isArray(filter) ? filter : [filter];
          return paths.some((path) => {
            let val = cell.getPropByPath(path);
            if (val != null) {
              val = `${val}`;
              if (!ignoreCase) {
                val = val.toLowerCase();
              }
              return val.indexOf(keyword) >= 0;
            }
            return false;
          });
        }
        return false;
      });
    }
    return true;
  }
  onSearch(evt) {
    this.filter(evt.target.value, this.options.search);
  }
  onSearchFocusIn() {
    main_exports2.addClass(this.container, "is-focused");
  }
  onSearchFocusOut() {
    main_exports2.removeClass(this.container, "is-focused");
  }
  onTitleClick() {
    if (this.options.collapsable) {
      main_exports2.toggleClass(this.container, "collapsed");
      if (main_exports2.hasClass(this.container, "collapsed")) {
        this.collapseGroups();
      } else {
        this.expandGroups();
      }
    }
  }
  onGroupTitleClick(evt) {
    const group = evt.target.closest(`.${this.prefixClassName(ClassNames.group)}`);
    if (group) {
      this.toggleGroup(main_exports2.attr(group, "data-name") || "");
    }
    const allCollapsed = Object.keys(this.groups).every((name) => {
      const group2 = this.getGroup(name);
      const groupElem = this.groups[name];
      return group2 && group2.collapsable === false || main_exports2.hasClass(groupElem, "collapsed");
    });
    main_exports2.toggleClass(this.container, "collapsed", allCollapsed);
  }
  getModel(groupName) {
    const graph = this.getGraph(groupName);
    return graph ? graph.model : null;
  }
  getGraph(groupName) {
    return this.graphs[groupName || Private.defaultGroupName];
  }
  getGroup(groupName) {
    const groups = this.options.groups;
    if (groupName != null && groups && groups.length) {
      return groups.find((group) => group.name === groupName);
    }
    return null;
  }
  getGroupByNode(node) {
    const groups = this.options.groups;
    if (groups) {
      return groups.find((group) => {
        const model = this.getModel(group.name);
        if (model) {
          return model.has(node.id);
        }
        return false;
      });
    }
    return null;
  }
  clearGroups() {
    Object.keys(this.graphs).forEach((groupName) => {
      const graph = this.graphs[groupName];
      this.unregisterGraphEvents(graph);
      graph.dispose();
    });
    Object.keys(this.groups).forEach((groupName) => {
      const elem = this.groups[groupName];
      main_exports2.remove(elem);
    });
    this.graphs = {};
    this.groups = {};
  }
  onRemove() {
    this.clearGroups();
    this.dnd.remove();
    this.stopListening();
    this.undelegateDocumentEvents();
  }
  dispose() {
    this.remove();
    loader_exports.clean(this.name);
  }
};
__decorate2([
  View.dispose()
], Stencil.prototype, "dispose", null);
(function(Stencil2) {
  Stencil2.defaultOptions = Object.assign({ stencilGraphWidth: 200, stencilGraphHeight: 800, title: "Stencil", collapsable: false, placeholder: "Search", notFoundText: "No matches found", layout(model, group) {
    const options = {
      columnWidth: this.options.stencilGraphWidth / 2 - 10,
      columns: 2,
      rowHeight: 80,
      resizeToFit: false,
      dx: 10,
      dy: 10
    };
    grid(model, Object.assign(Object.assign(Object.assign({}, options), this.options.layoutOptions), group ? group.layoutOptions : {}));
  } }, Dnd.defaults);
})(Stencil || (Stencil = {}));
var ClassNames;
(function(ClassNames2) {
  ClassNames2.base = "widget-stencil";
  ClassNames2.title = `${ClassNames2.base}-title`;
  ClassNames2.search = `${ClassNames2.base}-search`;
  ClassNames2.searchText = `${ClassNames2.search}-text`;
  ClassNames2.content = `${ClassNames2.base}-content`;
  ClassNames2.group = `${ClassNames2.base}-group`;
  ClassNames2.groupTitle = `${ClassNames2.group}-title`;
  ClassNames2.groupContent = `${ClassNames2.group}-content`;
})(ClassNames || (ClassNames = {}));
var Private;
(function(Private2) {
  Private2.defaultGroupName = "__default__";
})(Private || (Private = {}));
export {
  Stencil
};
//# sourceMappingURL=@antv_x6-plugin-stencil.js.map
