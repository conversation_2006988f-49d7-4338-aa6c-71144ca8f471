<template>
    <div class="mt20">
        <el-form inline label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef">
            <el-form-item prop="caseId" label="资产名称">
                <el-input style="width:240px;" v-model="queryParams.caseId" placeholder="请输入资产名称" />
            </el-form-item>
            <el-form-item prop="caseId" label="项目名称">
                <el-input style="width:240px;" v-model="queryParams.caseId" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item prop="caseId" label="立项人">
                <el-input style="width:240px;" v-model="queryParams.caseId" placeholder="请输入立项人" />
            </el-form-item>
            <el-form-item prop="caseId" label="立项时间">
                <el-input style="width:240px;" v-model="queryParams.caseId" placeholder="请输入立项时间" />
            </el-form-item>
            <el-form-item prop="caseId" label="审核状态">
                <el-select v-model="queryParams.caseId" style="width:240px;" placeholder="请选择审核状态">
                    <el-option />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button @click="antiShake(resetQuery)">重置</el-button>
            <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
        </div>
        <div class="operation-revealing-area mb20">
            <el-button type="success">通过</el-button>
            <el-button type="warning">不通过</el-button>
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <div class="table-box">
            <el-table :data="dataList" :loading="loading">
                <el-table-column v-if="columns[0].visible" align="center" prop="asssetName" sortable label="资产名称" />
                <el-table-column v-if="columns[1].visible" align="center" prop="projectName" sortable label="项目名称" />
                <el-table-column v-if="columns[2].visible" align="center" prop="type" sortable label="类型" />
                <el-table-column v-if="columns[3].visible" align="center" label="资产评估表">
                    <template #default="{ row }">
                        <div>
                            <el-button type="text" @click="handleCheck(row)">查看</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column v-if="columns[4].visible" align="center" prop="" label="立项报告">
                    <template #default="{ row }">
                        <div>
                            <el-button type="text" @click="handleOpenDailog('perviewFileRef', row)">查看</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column v-if="columns[5].visible" align="center" prop="surface" sortable label="申请原因" />
                <el-table-column v-if="columns[6].visible" align="center" prop="createBy" sortable label="立项人" />
                <el-table-column v-if="columns[7].visible" align="center" prop="createTime" sortable label="立项时间" />
                <el-table-column v-if="columns[8].visible" align="center" prop="state" sortable label="处理状态" />
                <el-table-column v-if="columns[9].visible" align="center" prop="updateTime" sortable label="处理时间" />
                <el-table-column v-if="columns[10].visible" align="center" prop="stutas" sortable label="审核状态" />
                <el-table-column fixed="right" label="操作">
                    <template #default="{ row }">
                        <div>
                            <el-button type="text" @click="check(row)">详情</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
        <perviewFile ref="perviewFileRef" />
    </div>
</template>

<script setup name="StartNapeApproval">
// import perviewFile from '@/views/dueDiligence/startNapeList/dialog/perviewFile'
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const data = reactive({
    queryParams: { pageNum: 1, pageSize: 10 }
})
const total = ref(1)
const dataList = ref([{
    asssetName: '民生资产包',
    projectName: '民生银行',
    type: '银行个贷',
    surface: '原因原因原因',
    createBy: '曾花花',
    createTime: '2024/1/15 12:00:21',
    updateTime: '2024/1/15 12:00:21',
    stutas: '审核中',
}])
const loading = ref(false)
const showSearch = ref(false)
const { queryParams } = toRefs(data)
const columns = ref([
    { "key": 0, "label": "资产名称", "visible": true },
    { "key": 1, "label": "项目名称", "visible": true },
    { "key": 2, "label": "类型", "visible": true },
    { "key": 3, "label": "资产评估表", "visible": true },
    { "key": 4, "label": "立项报告", "visible": true },
    { "key": 5, "label": "申请原因", "visible": true },
    { "key": 6, "label": "立项人", "visible": true },
    { "key": 7, "label": "立项时间", "visible": true },
    { "key": 8, "label": "处理状态", "visible": true },
    { "key": 9, "label": "处理时间", "visible": true },
    { "key": 10, "label": "审核状态", "visible": true }
])
function getList() {

}
function resetQuery() {
    queryParams.value = { pageNum: 1, pageSize: 10 }
    getList()
}
function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
}
function handleOpenDailog(refName) {
    proxy.$refs[refName].openDialog()
}
function check(row) {
    router.push({ path: `/dueDiligence/startNapeDetails`, query: { path: route.path } })
}
function handleCheck(row) {
    router.push({ path: `/assessment/view-evaluation/operate/1`, query: { path: route.path } })
}
</script>

<style lang="scss" scoped></style>