<template>
    <el-dialog :title="title" v-model="open" append-to-body width="650px" :before-close="cancel">
        <el-form ref="formRef" label-width="100px" :model="form" :rules="rules">
            <el-form-item prop="modelName" label="模型名称">
                <el-input v-model="form.modelName" @input="handleInputModelName" style="width:80%" show-word-limit maxlength="20"
                    placeholder="请输入模型名称" />
            </el-form-item>
            <el-form-item prop="label" label="标签">
                <el-input v-model="form.label" type="textarea" show-word-limit maxlength="500" style="width:80%"
                    placeholder="请输入标签" />
                <span style="color:red;">注：如标签和邮箱需输入多个，请使用英文逗号分隔；</span>
            </el-form-item>
            <!-- <el-form-item prop="email" label="预警邮箱">
                <el-input v-model="form.email" type="textarea" maxlength="200" show-word-limit style="width:80%"
                    placeholder="请输入预警邮箱" />
            </el-form-item> -->
            <el-form-item prop="status" label="状态">
                <el-radio-group v-model="form.status">
                    <el-radio v-for="(v, i) in switchStatusEnum" :key="+i" :label="+i" :name="+i" :value="+i">
                        {{ v }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" type="primary" @click="sumbit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { switchStatusEnum } from '@/utils/enum'
import { addTaskApi, editTaskApi } from '@/api/qcService/modelQc';
const props = defineProps({
    getList: { type: Function, }
})
const { proxy } = getCurrentInstance()
const data = reactive({
    form: {},
    rules: {
        modelName: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
        label: [{ required: true, message: '请输入标签', trigger: 'blur' }],
        email: [{ required: true, message: '请输入预警邮箱', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
    }
})
const title = ref('')
const open = ref(false)
const loading = ref(false)
const { form, rules } = toRefs(data)
function sumbit() {
    proxy.$refs['formRef'].validate(valid => {
        if (valid) {
            const reqForm = JSON.parse(JSON.stringify(form.value))
            const reqApi = reqForm.id ? editTaskApi : addTaskApi
            loading.value = true
            reqApi(reqForm).then(() => {
                cancel()
                props.getList && props.getList()
                proxy.$modal.msgSuccess('操作成功！')
            }).finally(() => loading.value = false)
        }
    })
}

// 模型名称输入限制
function handleInputModelName(val){
    form.value.modelName=val.replace(/[,，]/g,'')
}

function openDialog(data) {
    open.value = true
    form.value = data.row ? data.row : { status: 0 }
    form.value.type = data.type
    title.value = data.title

}
function cancel() {
    form.value = {}
    open.value = false
}

defineExpose({ openDialog })
</script>