{"version": 3, "sources": ["../tinymce/plugins/pagebreak/plugin.js", "../tinymce/plugins/pagebreak/index.js", "dep:tinymce_plugins_pagebreak"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var getSeparatorHtml = function (editor) {\n      return editor.getParam('pagebreak_separator', '<!-- pagebreak -->');\n    };\n    var shouldSplitBlock = function (editor) {\n      return editor.getParam('pagebreak_split_block', false);\n    };\n\n    var pageBreakClass = 'mce-pagebreak';\n    var getPlaceholderHtml = function (shouldSplitBlock) {\n      var html = '<img src=\"' + global.transparentSrc + '\" class=\"' + pageBreakClass + '\" data-mce-resize=\"false\" data-mce-placeholder />';\n      return shouldSplitBlock ? '<p>' + html + '</p>' : html;\n    };\n    var setup$1 = function (editor) {\n      var separatorHtml = getSeparatorHtml(editor);\n      var shouldSplitBlock$1 = function () {\n        return shouldSplitBlock(editor);\n      };\n      var pageBreakSeparatorRegExp = new RegExp(separatorHtml.replace(/[\\?\\.\\*\\[\\]\\(\\)\\{\\}\\+\\^\\$\\:]/g, function (a) {\n        return '\\\\' + a;\n      }), 'gi');\n      editor.on('BeforeSetContent', function (e) {\n        e.content = e.content.replace(pageBreakSeparatorRegExp, getPlaceholderHtml(shouldSplitBlock$1()));\n      });\n      editor.on('PreInit', function () {\n        editor.serializer.addNodeFilter('img', function (nodes) {\n          var i = nodes.length, node, className;\n          while (i--) {\n            node = nodes[i];\n            className = node.attr('class');\n            if (className && className.indexOf(pageBreakClass) !== -1) {\n              var parentNode = node.parent;\n              if (editor.schema.getBlockElements()[parentNode.name] && shouldSplitBlock$1()) {\n                parentNode.type = 3;\n                parentNode.value = separatorHtml;\n                parentNode.raw = true;\n                node.remove();\n                continue;\n              }\n              node.type = 3;\n              node.value = separatorHtml;\n              node.raw = true;\n            }\n          }\n        });\n      });\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mcePageBreak', function () {\n        editor.insertContent(getPlaceholderHtml(shouldSplitBlock(editor)));\n      });\n    };\n\n    var setup = function (editor) {\n      editor.on('ResolveName', function (e) {\n        if (e.target.nodeName === 'IMG' && editor.dom.hasClass(e.target, pageBreakClass)) {\n          e.name = 'pagebreak';\n        }\n      });\n    };\n\n    var register = function (editor) {\n      var onAction = function () {\n        return editor.execCommand('mcePageBreak');\n      };\n      editor.ui.registry.addButton('pagebreak', {\n        icon: 'page-break',\n        tooltip: 'Page break',\n        onAction: onAction\n      });\n      editor.ui.registry.addMenuItem('pagebreak', {\n        text: 'Page break',\n        icon: 'page-break',\n        onAction: onAction\n      });\n    };\n\n    function Plugin () {\n      global$1.add('pagebreak', function (editor) {\n        register$1(editor);\n        register(editor);\n        setup$1(editor);\n        setup(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"pagebreak\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/pagebreak')\n//   ES2015:\n//     import 'tinymce/plugins/pagebreak'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/pagebreak/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,SAAS,uBAAuB;AAAA;AAEhD,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,SAAS,yBAAyB;AAAA;AAGlD,UAAI,iBAAiB;AACrB,UAAI,qBAAqB,SAAU,mBAAkB;AACnD,YAAI,OAAO,eAAe,OAAO,iBAAiB,cAAc,iBAAiB;AACjF,eAAO,oBAAmB,QAAQ,OAAO,SAAS;AAAA;AAEpD,UAAI,UAAU,SAAU,QAAQ;AAC9B,YAAI,gBAAgB,iBAAiB;AACrC,YAAI,qBAAqB,WAAY;AACnC,iBAAO,iBAAiB;AAAA;AAE1B,YAAI,2BAA2B,IAAI,OAAO,cAAc,QAAQ,iCAAiC,SAAU,GAAG;AAC5G,iBAAO,OAAO;AAAA,YACZ;AACJ,eAAO,GAAG,oBAAoB,SAAU,GAAG;AACzC,YAAE,UAAU,EAAE,QAAQ,QAAQ,0BAA0B,mBAAmB;AAAA;AAE7E,eAAO,GAAG,WAAW,WAAY;AAC/B,iBAAO,WAAW,cAAc,OAAO,SAAU,OAAO;AACtD,gBAAI,IAAI,MAAM,QAAQ,MAAM;AAC5B,mBAAO,KAAK;AACV,qBAAO,MAAM;AACb,0BAAY,KAAK,KAAK;AACtB,kBAAI,aAAa,UAAU,QAAQ,oBAAoB,IAAI;AACzD,oBAAI,aAAa,KAAK;AACtB,oBAAI,OAAO,OAAO,mBAAmB,WAAW,SAAS,sBAAsB;AAC7E,6BAAW,OAAO;AAClB,6BAAW,QAAQ;AACnB,6BAAW,MAAM;AACjB,uBAAK;AACL;AAAA;AAEF,qBAAK,OAAO;AACZ,qBAAK,QAAQ;AACb,qBAAK,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAOrB,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,gBAAgB,WAAY;AAC5C,iBAAO,cAAc,mBAAmB,iBAAiB;AAAA;AAAA;AAI7D,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,GAAG,eAAe,SAAU,GAAG;AACpC,cAAI,EAAE,OAAO,aAAa,SAAS,OAAO,IAAI,SAAS,EAAE,QAAQ,iBAAiB;AAChF,cAAE,OAAO;AAAA;AAAA;AAAA;AAKf,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,UAAU,aAAa;AAAA,UACxC,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA;AAEF,eAAO,GAAG,SAAS,YAAY,aAAa;AAAA,UAC1C,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA;AAAA;AAIJ,wBAAmB;AACjB,iBAAS,IAAI,aAAa,SAAU,QAAQ;AAC1C,qBAAW;AACX,mBAAS;AACT,kBAAQ;AACR,gBAAM;AAAA;AAAA;AAIV;AAAA;AAAA;AAAA;;;ACrGJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,oCAAQ;", "names": []}