<template>
    <div :id="idName" class="echarts-area mt20"> </div>
</template>

<script setup>
import * as echarts from 'echarts';
const props = defineProps({
    dataInfo: { type: Object, default: {} },
    idName: { type: String, default: 'pie-main' },
})
function setEcharts() {
    var chartDom = document.getElementById(props.idName);
    var myChart = echarts.init(chartDom);
    const option = {
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: 10,
            top: 0,
            bottom: 20,
            textStyle: {
                color: '#ffffff' // 设置图例文字颜色为红色
            }
        },
        series: [
            {
                name: 'Nightingale Chart',
                type: 'pie',
                radius: [25, 125],
                center: ['40%', '50%'],
                roseType: 'area',
                label: {
                    show: false,
                },
                data: [
                    { value: 40, name: 'rose 1' },
                    { value: 38, name: 'rose 2' },
                    { value: 32, name: 'rose 3' },
                    { value: 30, name: 'rose 4' },
                    { value: 28, name: 'rose 5' },
                    { value: 26, name: 'rose 6' },
                    { value: 22, name: 'rose 7' },
                    { value: 18, name: 'rose 8' }
                ]
            }
        ]
    };
    option && myChart.setOption(option);
}
onMounted(() => {
    setEcharts()
})
</script>

<style lang="scss" scoped>
.echarts-area {
    width: 100%;
    height: 250px;
}
</style>