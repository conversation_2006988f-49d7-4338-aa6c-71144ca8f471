{"version": 3, "sources": ["../js-cookie/dist/js.cookie.mjs", "dep:js-cookie"], "sourcesContent": ["/*! js-cookie v3.0.1 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (key, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    key = encodeURIComponent(key)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      key + '=' + converter.write(value, key) + stringifiedAttributes)\n  }\n\n  function get (key) {\n    if (typeof document === 'undefined' || (arguments.length && !key)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var foundKey = decodeURIComponent(parts[0]);\n        jar[foundKey] = converter.read(value, foundKey);\n\n        if (key === foundKey) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return key ? jar[key] : jar\n  }\n\n  return Object.create(\n    {\n      set: set,\n      get: get,\n      remove: function (key, attributes) {\n        set(\n          key,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\nexport default api;\n", "import d from \"./node_modules/js-cookie/dist/js.cookie.mjs\";export default d;"], "mappings": ";;;AAAA,AAEA,gBAAiB,QAAQ;AACvB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU;AACvB,aAAS,OAAO,QAAQ;AACtB,aAAO,OAAO,OAAO;AAAA;AAAA;AAGzB,SAAO;AAAA;AAKT,IAAI,mBAAmB;AAAA,EACrB,MAAM,SAAU,OAAO;AACrB,QAAI,MAAM,OAAO,KAAK;AACpB,cAAQ,MAAM,MAAM,GAAG;AAAA;AAEzB,WAAO,MAAM,QAAQ,oBAAoB;AAAA;AAAA,EAE3C,OAAO,SAAU,OAAO;AACtB,WAAO,mBAAmB,OAAO,QAC/B,4CACA;AAAA;AAAA;AAQN,cAAe,WAAW,mBAAmB;AAC3C,eAAc,KAAK,OAAO,YAAY;AACpC,QAAI,OAAO,aAAa,aAAa;AACnC;AAAA;AAGF,iBAAa,OAAO,IAAI,mBAAmB;AAE3C,QAAI,OAAO,WAAW,YAAY,UAAU;AAC1C,iBAAW,UAAU,IAAI,KAAK,KAAK,QAAQ,WAAW,UAAU;AAAA;AAElE,QAAI,WAAW,SAAS;AACtB,iBAAW,UAAU,WAAW,QAAQ;AAAA;AAG1C,UAAM,mBAAmB,KACtB,QAAQ,wBAAwB,oBAChC,QAAQ,SAAS;AAEpB,QAAI,wBAAwB;AAC5B,aAAS,iBAAiB,YAAY;AACpC,UAAI,CAAC,WAAW,gBAAgB;AAC9B;AAAA;AAGF,+BAAyB,OAAO;AAEhC,UAAI,WAAW,mBAAmB,MAAM;AACtC;AAAA;AAUF,+BAAyB,MAAM,WAAW,eAAe,MAAM,KAAK;AAAA;AAGtE,WAAQ,SAAS,SACf,MAAM,MAAM,UAAU,MAAM,OAAO,OAAO;AAAA;AAG9C,eAAc,KAAK;AACjB,QAAI,OAAO,aAAa,eAAgB,UAAU,UAAU,CAAC,KAAM;AACjE;AAAA;AAKF,QAAI,UAAU,SAAS,SAAS,SAAS,OAAO,MAAM,QAAQ;AAC9D,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,QAAQ,QAAQ,GAAG,MAAM;AAC7B,UAAI,QAAQ,MAAM,MAAM,GAAG,KAAK;AAEhC,UAAI;AACF,YAAI,WAAW,mBAAmB,MAAM;AACxC,YAAI,YAAY,UAAU,KAAK,OAAO;AAEtC,YAAI,QAAQ,UAAU;AACpB;AAAA;AAAA,eAEK,GAAP;AAAA;AAAA;AAGJ,WAAO,MAAM,IAAI,OAAO;AAAA;AAG1B,SAAO,OAAO,OACZ;AAAA,IACE;AAAA,IACA;AAAA,IACA,QAAQ,SAAU,KAAK,YAAY;AACjC,UACE,KACA,IACA,OAAO,IAAI,YAAY;AAAA,QACrB,SAAS;AAAA;AAAA;AAAA,IAIf,gBAAgB,SAAU,YAAY;AACpC,aAAO,KAAK,KAAK,WAAW,OAAO,IAAI,KAAK,YAAY;AAAA;AAAA,IAE1D,eAAe,SAAU,YAAW;AAClC,aAAO,KAAK,OAAO,IAAI,KAAK,WAAW,aAAY,KAAK;AAAA;AAAA,KAG5D;AAAA,IACE,YAAY,EAAE,OAAO,OAAO,OAAO;AAAA,IACnC,WAAW,EAAE,OAAO,OAAO,OAAO;AAAA;AAAA;AAKxC,IAAI,MAAM,KAAK,kBAAkB,EAAE,MAAM;AAGzC,IAAO,oBAAQ;;;ACrI6C,IAAO,qBAAQ;", "names": []}