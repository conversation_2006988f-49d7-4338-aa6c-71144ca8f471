export const textDataList = [
    { "text": "然后通过本地银行嗯这种啊都可以啊", "endTime": 55789, "beginTime": 29960, "channelId": 1 },
    { "text": "还有今天装备好", "endTime": 55860, "beginTime": 30150, "channelId": 0 },
    { "text": "那就行", "endTime": 56219, "beginTime": 55789, "channelId": 1 },
    { "text": "那你我我把这个这个好吧", "endTime": 63170, "beginTime": 55860, "channelId": 0 },
    { "text": "我我把这个这个我把这个那到时候到时候你没做的时候", "endTime": 66160, "beginTime": 60880, "channelId": 1 },
    { "text": "一前主要就是行啊", "endTime": 79610, "beginTime": 66160, "channelId": 1 },
    { "text": "就是很久人之前把那个呃那个好像就难道知道挂便采购不是什么吧", "endTime": 110630, "beginTime": 70159, "channelId": 0 },
    { "text": "他共同的话共同啊就是然后嗯呃管理中不是前端的", "endTime": 110540, "beginTime": 80640, "channelId": 1 },
    { "text": "就是我后面他他的后就是被配置的东西的话在吗", "endTime": 114660, "beginTime": 110540, "channelId": 1 },
    { "text": "就你后被打了", "endTime": 111905, "beginTime": 110630, "channelId": 0 },
    { "text": "对", "endTime": 112750, "beginTime": 112570, "channelId": 0 },
    { "text": "这个证据要看一下那个", "endTime": 121000, "beginTime": 112750, "channelId": 0 },
    { "text": "然后我上面估计是每个点看不到", "endTime": 127830, "beginTime": 123210, "channelId": 1 },
    { "text": "然后我知道你那天我这边啊", "endTime": 127659, "beginTime": 124810, "channelId": 0 },
    { "text": "对你说的这是就是嗯会维持集团说15年没有你", "endTime": 292540, "beginTime": 127659, "channelId": 0 },
    { "text": "你要要不你是什么", "endTime": 156320, "beginTime": 127830, "channelId": 1 },
    { "text": "对阿姨被回复更新结算", "endTime": 285070, "beginTime": 270330, "channelId": 1 },
    { "text": "你有做过吗", "endTime": 285880, "beginTime": 285070, "channelId": 1 },
    { "text": "行", "endTime": 287200, "beginTime": 286620, "channelId": 1 },
    { "text": "提诉名进行结算啊", "endTime": 289080, "beginTime": 287200, "channelId": 1 },
    { "text": "没有我没我没做过那个你好", "endTime": 300250, "beginTime": 289080, "channelId": 1 },
    { "text": "我没跟我说就是", "endTime": 296925, "beginTime": 292540, "channelId": 0 }
]
export const detailsData = {
    "createBy": null,
    "createTime": null,
    "updateBy": null,
    "updateTime": null,
    "remark": null,
    "id": 11579,
    "callId": "1d756adc-edec-4266-844b-e80a5d9a347d",
    "callCenter": 10,
    "startStamp": "2024-06-04 10:17:22",
    "beginStartStamp": null,
    "endStartStamp": null,
    "answerStamp": "2024-06-04 10:17:25",
    "endStamp": "2024-06-04 10:22:36",
    "billsec": 311,
    "extDuration": 306,
    "totalDuration": null,
    "month": null,
    "callfrom": "*********",
    "callto": "17324507126",
    "type": "outbound",
    "hangupCause": "NORMAL_CLEARING",
    "recording": "20240604/202406041017-1d756adc-edec-4266-844b-e80a5d9a347d-*********-17324507126.wav",
    "companyNum": "1056",
    "companyNums": null,
    "companyName": "KFFY430中控台",
    "trunkName": "LT-SJXL",
    "number": "16509223606",
    "qualityStatus": 1,
    "reCheck": 0,
    "nway": 0,
    "taskId": "0f0abb36-5645-4ae8-9fcc-1a11c9af95c7",
    "taskTime": "2024-06-04 11:31:16",
    "agentModel": null,
    "agentModels": null,
    "agentText": "然后通过本地银行嗯这种啊都可以啊,那就行,我我把这个这个我把这个那到时候到时候你没做的时候,一前主要就是行啊,他共同的话共同啊就是然后嗯呃管理中不是前端的,就是我后面他他的后就是被配置的东西的话在吗,然后我上面估计是每个点看不到,你要要不你是什么,对阿姨被回复更新结算,你有做过吗,行,提诉名进行结算啊,没有我没我没做过那个你好,",
    "customerModel": null,
    "customerModels": null,
    "customerText": "还有今天装备好,那你我我把这个这个好吧,就是很久人之前把那个呃那个好像就难道知道挂便采购不是什么吧,就你后被打了,对,这个证据要看一下那个,然后我知道你那天我这边啊,对你说的这是就是嗯会维持集团说15年没有你,我没跟我说就是,",
    "qualityId": 1,
    "qualityIds": null,
    "artificialName": null,
    "artificialTime": null,
    "callTextPath": "/www/wwwroot/quality/backend/2024/6/4/0f0abb36-5645-4ae8-9fcc-1a11c9af95c7.txt",
    "agentLabel": null,
    "customerLabel": null,
    "deptId": 1
}
export const seatOption = [
    { "id": 1, "modleName": "涉嫌冒充公检法", },
    { "id": 2, "modleName": "线路违规业务", },
    { "id": 3, "modleName": "运营商小号黑名单", },
    { "id": 4, "modleName": "无违规", },
    { "id": 5, "modleName": "备案的签名", },
    { "id": 6, "modleName": "714高炮", },
    { "id": 7, "modleName": "疑似私收代还", },
    { "id": 8, "modleName": "怂恿债务人或第三方投诉", },
    { "id": 9, "modleName": "催收名义违规", },
    { "id": 10, "modleName": "挖苦讽刺", },
    { "id": 11, "modleName": "恐吓威胁", },
    { "id": 12, "modleName": "辱骂债务人", },
    { "id": 13, "modleName": "敏感标签", }
]
export const clientOption = [
    { "id": 14, "modleName": "辱骂工作人员", },
    { "id": 15, "modleName": "表示要投诉（有投诉风险）", }
]