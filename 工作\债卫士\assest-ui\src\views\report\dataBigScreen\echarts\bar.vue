<template>
    <div :id="idName" class="echarts-area"> </div>
</template>

<script setup>
import * as echarts from 'echarts';
const props = defineProps({
    dataInfo: { type: Object, default: {} },
    idName: { type: String, default: 'bar-main' },
})
function setEcharts() {
    var chartDom = document.getElementById(props.idName);
    var myChart = echarts.init(chartDom);
    const { } = props.dataInfo
    const yAxisData = ['Brazil', 'Indonesia', 'USA', 'India', 'China', 'Brazil', 'Indonesia', 'USA', 'India', 'China']
    const seriesData = [18203, 23489, 29034, 104970, 131744, 630230, 23489, 29034, 104970, 131744,]
    const option = {
        grid: {
            left: '3%',
            right: '10%',
            bottom: '5%',
            height:220,
            containLabel: true
        },
        xAxis: {
            type: 'value',
            axisLabel: {
                textStyle: {
                    color: '#ffffff'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: yAxisData,
            axisLabel: {
                textStyle: {
                    color: '#ffffff'
                }
            }
        },
        series: [
            {
                name: '2011',
                type: 'bar',
                data: seriesData.sort((a, b) => a - b),
                label: {
                    show: true,
                    position: 'right'
                },
                itemStyle: {
                    // 设置柱状图的颜色
                    color: function (params) {
                        // 这里可以根据你的需求自定义颜色，例如使用数据值来决定颜色
                        var colorList = [
                            setLinearGradient('#16378d', '#38e1ff'),
                            setLinearGradient('#16378d', '#38e1ff'),
                            setLinearGradient('#16378d', '#38e1ff'),
                            setLinearGradient('#16378d', '#38e1ff'),
                            setLinearGradient('#16378d', '#38e1ff'),
                            setLinearGradient('#16378d', '#38e1ff'),
                            setLinearGradient('#16378d', '#38e1ff'),
                            setLinearGradient('#0491ac', '#3ede6a'),
                            setLinearGradient('#2c878a', '#f77d02'),
                            setLinearGradient('#3f35d3', '#ee313d'),
                        ];
                        return colorList[params.dataIndex]
                    }
                }
            },
        ]
    };
    option && myChart.setOption(option);
}

function setLinearGradient(startColor, endColor) {
    return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
        offset: 0,
        color: startColor // 起始颜色
    }, {
        offset: 1,
        color: endColor // 结束颜色
    }])
}
onMounted(() => {
    setEcharts()
})
</script>

<style lang="scss" scoped>
.echarts-area {
    width: 100%;
    height: 250px;
}
</style>