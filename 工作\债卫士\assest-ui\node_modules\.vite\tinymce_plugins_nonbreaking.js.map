{"version": 3, "sources": ["../tinymce/plugins/nonbreaking/plugin.js", "../tinymce/plugins/nonbreaking/index.js", "dep:tinymce_plugins_nonbreaking"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var getKeyboardSpaces = function (editor) {\n      var spaces = editor.getParam('nonbreaking_force_tab', 0);\n      if (typeof spaces === 'boolean') {\n        return spaces === true ? 3 : 0;\n      } else {\n        return spaces;\n      }\n    };\n    var wrapNbsps = function (editor) {\n      return editor.getParam('nonbreaking_wrap', true, 'boolean');\n    };\n\n    var stringRepeat = function (string, repeats) {\n      var str = '';\n      for (var index = 0; index < repeats; index++) {\n        str += string;\n      }\n      return str;\n    };\n    var isVisualCharsEnabled = function (editor) {\n      return editor.plugins.visualchars ? editor.plugins.visualchars.isEnabled() : false;\n    };\n    var insertNbsp = function (editor, times) {\n      var classes = function () {\n        return isVisualCharsEnabled(editor) ? 'mce-nbsp-wrap mce-nbsp' : 'mce-nbsp-wrap';\n      };\n      var nbspSpan = function () {\n        return '<span class=\"' + classes() + '\" contenteditable=\"false\">' + stringRepeat('&nbsp;', times) + '</span>';\n      };\n      var shouldWrap = wrapNbsps(editor);\n      var html = shouldWrap || editor.plugins.visualchars ? nbspSpan() : stringRepeat('&nbsp;', times);\n      editor.undoManager.transact(function () {\n        return editor.insertContent(html);\n      });\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mceNonBreaking', function () {\n        insertNbsp(editor, 1);\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    var setup = function (editor) {\n      var spaces = getKeyboardSpaces(editor);\n      if (spaces > 0) {\n        editor.on('keydown', function (e) {\n          if (e.keyCode === global.TAB && !e.isDefaultPrevented()) {\n            if (e.shiftKey) {\n              return;\n            }\n            e.preventDefault();\n            e.stopImmediatePropagation();\n            insertNbsp(editor, spaces);\n          }\n        });\n      }\n    };\n\n    var register = function (editor) {\n      var onAction = function () {\n        return editor.execCommand('mceNonBreaking');\n      };\n      editor.ui.registry.addButton('nonbreaking', {\n        icon: 'non-breaking',\n        tooltip: 'Nonbreaking space',\n        onAction: onAction\n      });\n      editor.ui.registry.addMenuItem('nonbreaking', {\n        icon: 'non-breaking',\n        text: 'Nonbreaking space',\n        onAction: onAction\n      });\n    };\n\n    function Plugin () {\n      global$1.add('nonbreaking', function (editor) {\n        register$1(editor);\n        register(editor);\n        setup(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"nonbreaking\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/nonbreaking')\n//   ES2015:\n//     import 'tinymce/plugins/nonbreaking'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/nonbreaking/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,oBAAoB,SAAU,QAAQ;AACxC,YAAI,SAAS,OAAO,SAAS,yBAAyB;AACtD,YAAI,OAAO,WAAW,WAAW;AAC/B,iBAAO,WAAW,OAAO,IAAI;AAAA,eACxB;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,YAAY,SAAU,QAAQ;AAChC,eAAO,OAAO,SAAS,oBAAoB,MAAM;AAAA;AAGnD,UAAI,eAAe,SAAU,QAAQ,SAAS;AAC5C,YAAI,MAAM;AACV,iBAAS,QAAQ,GAAG,QAAQ,SAAS,SAAS;AAC5C,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,OAAO,QAAQ,cAAc,OAAO,QAAQ,YAAY,cAAc;AAAA;AAE/E,UAAI,aAAa,SAAU,QAAQ,OAAO;AACxC,YAAI,UAAU,WAAY;AACxB,iBAAO,qBAAqB,UAAU,2BAA2B;AAAA;AAEnE,YAAI,WAAW,WAAY;AACzB,iBAAO,kBAAkB,YAAY,+BAA+B,aAAa,UAAU,SAAS;AAAA;AAEtG,YAAI,aAAa,UAAU;AAC3B,YAAI,OAAO,cAAc,OAAO,QAAQ,cAAc,aAAa,aAAa,UAAU;AAC1F,eAAO,YAAY,SAAS,WAAY;AACtC,iBAAO,OAAO,cAAc;AAAA;AAAA;AAIhC,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,kBAAkB,WAAY;AAC9C,qBAAW,QAAQ;AAAA;AAAA;AAIvB,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI,SAAS,kBAAkB;AAC/B,YAAI,SAAS,GAAG;AACd,iBAAO,GAAG,WAAW,SAAU,GAAG;AAChC,gBAAI,EAAE,YAAY,OAAO,OAAO,CAAC,EAAE,sBAAsB;AACvD,kBAAI,EAAE,UAAU;AACd;AAAA;AAEF,gBAAE;AACF,gBAAE;AACF,yBAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAM3B,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,UAAU,eAAe;AAAA,UAC1C,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA;AAEF,eAAO,GAAG,SAAS,YAAY,eAAe;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA;AAAA;AAIJ,wBAAmB;AACjB,iBAAS,IAAI,eAAe,SAAU,QAAQ;AAC5C,qBAAW;AACX,mBAAS;AACT,gBAAM;AAAA;AAAA;AAIV;AAAA;AAAA;AAAA;;;ACjGJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,sCAAQ;", "names": []}