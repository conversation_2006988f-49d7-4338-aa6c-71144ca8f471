{"version": 3, "sources": ["../tinymce/plugins/tabfocus/plugin.js", "../tinymce/plugins/tabfocus/index.js", "dep:tinymce_plugins_tabfocus"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.EditorManager');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    var getTabFocusElements = function (editor) {\n      return editor.getParam('tabfocus_elements', ':prev,:next');\n    };\n    var getTabFocus = function (editor) {\n      return editor.getParam('tab_focus', getTabFocusElements(editor));\n    };\n\n    var DOM = global$5.DOM;\n    var tabCancel = function (e) {\n      if (e.keyCode === global.TAB && !e.ctrlKey && !e.altKey && !e.metaKey) {\n        e.preventDefault();\n      }\n    };\n    var setup = function (editor) {\n      var tabHandler = function (e) {\n        var x;\n        if (e.keyCode !== global.TAB || e.ctrlKey || e.altKey || e.metaKey || e.isDefaultPrevented()) {\n          return;\n        }\n        var find = function (direction) {\n          var el = DOM.select(':input:enabled,*[tabindex]:not(iframe)');\n          var canSelectRecursive = function (e) {\n            var castElem = e;\n            return e.nodeName === 'BODY' || castElem.type !== 'hidden' && castElem.style.display !== 'none' && castElem.style.visibility !== 'hidden' && canSelectRecursive(e.parentNode);\n          };\n          var canSelect = function (el) {\n            return /INPUT|TEXTAREA|BUTTON/.test(el.tagName) && global$4.get(e.id) && el.tabIndex !== -1 && canSelectRecursive(el);\n          };\n          global$1.each(el, function (e, i) {\n            if (e.id === editor.id) {\n              x = i;\n              return false;\n            }\n          });\n          if (direction > 0) {\n            for (var i = x + 1; i < el.length; i++) {\n              if (canSelect(el[i])) {\n                return el[i];\n              }\n            }\n          } else {\n            for (var i = x - 1; i >= 0; i--) {\n              if (canSelect(el[i])) {\n                return el[i];\n              }\n            }\n          }\n          return null;\n        };\n        var v = global$1.explode(getTabFocus(editor));\n        if (v.length === 1) {\n          v[1] = v[0];\n          v[0] = ':prev';\n        }\n        var el;\n        if (e.shiftKey) {\n          if (v[0] === ':prev') {\n            el = find(-1);\n          } else {\n            el = DOM.get(v[0]);\n          }\n        } else {\n          if (v[1] === ':next') {\n            el = find(1);\n          } else {\n            el = DOM.get(v[1]);\n          }\n        }\n        if (el) {\n          var focusEditor = global$4.get(el.id || el.name);\n          if (el.id && focusEditor) {\n            focusEditor.focus();\n          } else {\n            global$2.setTimeout(function () {\n              if (!global$3.webkit) {\n                window.focus();\n              }\n              el.focus();\n            }, 10);\n          }\n          e.preventDefault();\n        }\n      };\n      editor.on('init', function () {\n        if (editor.inline) {\n          DOM.setAttrib(editor.getBody(), 'tabIndex', null);\n        }\n        editor.on('keyup', tabCancel);\n        if (global$3.gecko) {\n          editor.on('keypress keydown', tabHandler);\n        } else {\n          editor.on('keydown', tabHandler);\n        }\n      });\n    };\n\n    function Plugin () {\n      global$6.add('tabfocus', function (editor) {\n        setup(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"tabfocus\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/tabfocus')\n//   ES2015:\n//     import 'tinymce/plugins/tabfocus'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/tabfocus/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,OAAO,SAAS,qBAAqB;AAAA;AAE9C,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,OAAO,SAAS,aAAa,oBAAoB;AAAA;AAG1D,UAAI,MAAM,SAAS;AACnB,UAAI,YAAY,SAAU,GAAG;AAC3B,YAAI,EAAE,YAAY,OAAO,OAAO,CAAC,EAAE,WAAW,CAAC,EAAE,UAAU,CAAC,EAAE,SAAS;AACrE,YAAE;AAAA;AAAA;AAGN,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI,aAAa,SAAU,GAAG;AAC5B,cAAI;AACJ,cAAI,EAAE,YAAY,OAAO,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,sBAAsB;AAC5F;AAAA;AAEF,cAAI,OAAO,SAAU,WAAW;AAC9B,gBAAI,MAAK,IAAI,OAAO;AACpB,gBAAI,qBAAqB,SAAU,IAAG;AACpC,kBAAI,WAAW;AACf,qBAAO,GAAE,aAAa,UAAU,SAAS,SAAS,YAAY,SAAS,MAAM,YAAY,UAAU,SAAS,MAAM,eAAe,YAAY,mBAAmB,GAAE;AAAA;AAEpK,gBAAI,YAAY,SAAU,KAAI;AAC5B,qBAAO,wBAAwB,KAAK,IAAG,YAAY,SAAS,IAAI,EAAE,OAAO,IAAG,aAAa,MAAM,mBAAmB;AAAA;AAEpH,qBAAS,KAAK,KAAI,SAAU,IAAG,IAAG;AAChC,kBAAI,GAAE,OAAO,OAAO,IAAI;AACtB,oBAAI;AACJ,uBAAO;AAAA;AAAA;AAGX,gBAAI,YAAY,GAAG;AACjB,uBAAS,IAAI,IAAI,GAAG,IAAI,IAAG,QAAQ,KAAK;AACtC,oBAAI,UAAU,IAAG,KAAK;AACpB,yBAAO,IAAG;AAAA;AAAA;AAAA,mBAGT;AACL,uBAAS,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC/B,oBAAI,UAAU,IAAG,KAAK;AACpB,yBAAO,IAAG;AAAA;AAAA;AAAA;AAIhB,mBAAO;AAAA;AAET,cAAI,IAAI,SAAS,QAAQ,YAAY;AACrC,cAAI,EAAE,WAAW,GAAG;AAClB,cAAE,KAAK,EAAE;AACT,cAAE,KAAK;AAAA;AAET,cAAI;AACJ,cAAI,EAAE,UAAU;AACd,gBAAI,EAAE,OAAO,SAAS;AACpB,mBAAK,KAAK;AAAA,mBACL;AACL,mBAAK,IAAI,IAAI,EAAE;AAAA;AAAA,iBAEZ;AACL,gBAAI,EAAE,OAAO,SAAS;AACpB,mBAAK,KAAK;AAAA,mBACL;AACL,mBAAK,IAAI,IAAI,EAAE;AAAA;AAAA;AAGnB,cAAI,IAAI;AACN,gBAAI,cAAc,SAAS,IAAI,GAAG,MAAM,GAAG;AAC3C,gBAAI,GAAG,MAAM,aAAa;AACxB,0BAAY;AAAA,mBACP;AACL,uBAAS,WAAW,WAAY;AAC9B,oBAAI,CAAC,SAAS,QAAQ;AACpB,yBAAO;AAAA;AAET,mBAAG;AAAA,iBACF;AAAA;AAEL,cAAE;AAAA;AAAA;AAGN,eAAO,GAAG,QAAQ,WAAY;AAC5B,cAAI,OAAO,QAAQ;AACjB,gBAAI,UAAU,OAAO,WAAW,YAAY;AAAA;AAE9C,iBAAO,GAAG,SAAS;AACnB,cAAI,SAAS,OAAO;AAClB,mBAAO,GAAG,oBAAoB;AAAA,iBACzB;AACL,mBAAO,GAAG,WAAW;AAAA;AAAA;AAAA;AAK3B,wBAAmB;AACjB,iBAAS,IAAI,YAAY,SAAU,QAAQ;AACzC,gBAAM;AAAA;AAAA;AAIV;AAAA;AAAA;AAAA;;;AC/HJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,mCAAQ;", "names": []}