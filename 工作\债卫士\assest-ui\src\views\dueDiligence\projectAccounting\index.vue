<template>
    <div class="app-container">
        <el-form inline label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef">
            <el-form-item label="项目ID" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入项目ID" />
            </el-form-item>
            <el-form-item label="项目名称" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item label="资产转让方" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入资产转让方" />
            </el-form-item>
            <el-form-item label="产品类型" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入产品类型" />
            </el-form-item>
            <el-form-item label="付款单状态" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入付款单状态" />
            </el-form-item>
            <el-form-item label="付款单申请流水号" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入付款单申请流水号" />
            </el-form-item>
            <el-form-item label="收款人" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入收款人" />
            </el-form-item>
            <el-form-item label="付款类型" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入付款类型" />
            </el-form-item>
            <el-form-item label="申请人" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入申请人" />
            </el-form-item>
            <el-form-item label="申请时间" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入申请时间" />
            </el-form-item>
            <el-form-item label="收款账号" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入收款账号" />
            </el-form-item>
            <el-form-item label="账户名" prop="">
                <el-input v-model="queryParams.aaa" style="width: 320px;" placeholder="请输入账户名" />
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>
        <div class="operation-revealing-area">
            <el-button :loading="loading" type="primary" plain @click="handleRevoke()">撤销</el-button>
            <right-toolbar @queryTable="getList" :columns="columns" v-model:showSearch="showSearch" />
        </div>
        <SelectedAll :dataList="dataList" v-model::selectedArr="selectedArr" v-model:allQuery="queryParams.allQuery">
            <template #content>
                <div>
                    <span>项目量（件）：1111</span>
                </div>
            </template>
        </SelectedAll>
        <el-tabs v-model="activeTab" @tab-click="antiShake(handleQuery)">
            <el-tab-pane v-for="(v, i) in tabList" :key="i" :label="`${v.label}(${v.num})`" :name="v.value" />
        </el-tabs>
        <el-table v-loading="loading" ref="multipleTableRef" class="multiple-table" :data="dataList"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" :selectable="selectable" width="30px" fixed="left" />
            <el-table-column label="项目ID" prop="prejectId" shoaaaa align="center" />
            <el-table-column label="项目名称" prop="prejectName" shoaaaa align="center" />
            <el-table-column label="资产转让方" prop="" shoaaaa align="center" />
            <el-table-column label="产品类型" prop="" shoaaaa align="center" />
            <el-table-column label="建账状态" prop="status" shoaaaa align="center" />
            <el-table-column label="付款单申请流水号" prop="orederNo" shoaaaa align="center" />
            <el-table-column label="付款单状态" prop="" shoaaaa align="center" />
            <el-table-column label="付款类型" prop="" payType shoaaaa align="center" />
            <el-table-column label="收款账号" prop="" shoaaaa align="center" />
            <el-table-column label="账户名" prop="" shoaaaa align="center" />
            <el-table-column label="收款人" prop="name" shoaaaa align="center" />
            <el-table-column label="开户行" prop="" shoaaaa align="center" />
            <el-table-column label="合同金额" prop="money" shoaaaa align="center" />
            <el-table-column label="申请金额" prop="" shoaaaa align="center" />
            <el-table-column label="备注" prop="remarks" shoaaaa align="center" />
            <el-table-column label="申请人" prop="applayName" shoaaaa align="center" />
            <el-table-column label="申请时间" prop="applayTime" shoaaaa align="center" />
            <el-table-column fixed="right" label="操作" width="240">
                <template #default="{ row }">
                    <div>
                        <el-button v-if="['待项目建账'].includes(row.status)" @click="handleAdd(row)"
                            type="text">新增项目建账</el-button>
                        <el-button v-if="['建账待审批'].includes(row.status)" @click="handleRevoke(row)"
                            type="text">撤销</el-button>
                        <el-button v-if="!['待项目建账'].includes(row.status)" @click="handleDetails(row)"
                            type="text">详情</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList()" />
    </div>
</template>

<script setup>
const route = useRoute()
const router = useRouter()
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    allQuery: false
})
const total = ref(0)
const showSearch = ref(false)
const dataList = ref([
    {
        prejectId: 23434455,
        prejectName: '2024不良资产收购项目',
        status: '待项目建账',
        orederNo: '**********',
        payStatus: '已支付',
        payType: '全款',
        name: '胡图图',
        money: 323434,
        remarks: '情况属实',
        applayName: '胡图图',
        applayTime: '2024-11-10 12:00:00',
    },
    {
        prejectId: 23434455,
        prejectName: '2024不良资产收购项目',
        status: '建账待审批',
        orederNo: '**********',
        payStatus: '已支付',
        payType: '全款',
        name: '胡图图',
        money: '全款',
        remarks: '情况属实',
        payType: '全款',
        applayName: '胡图图',
        applayTime: '2024-11-10 12:00:00',
    },
    {
        prejectId: 23434455,
        prejectName: '2024不良资产收购项目',
        status: '建账中',
        orederNo: '**********',
        payStatus: '已支付',
        payType: '全款',
        name: '胡图图',
        money: '全款',
        remarks: '情况属实',
        payType: '全款',
        applayName: '胡图图',
        applayTime: '2024-11-10 12:00:00',
    },
    {
        prejectId: 23434455,
        prejectName: '2024不良资产收购项目',
        status: '建账成功',
        orederNo: '**********',
        payStatus: '已支付',
        payType: '全款',
        name: '胡图图',
        money: '全款',
        remarks: '情况属实',
        payType: '全款',
        applayName: '胡图图',
        applayTime: '2024-11-10 12:00:00',
    },
    {
        prejectId: 23434455,
        prejectName: '2024不良资产收购项目',
        status: '建账失败',
        orederNo: '**********',
        payStatus: '已支付',
        payType: '全款',
        name: '胡图图',
        money: '全款',
        remarks: '情况属实',
        payType: '全款',
        applayName: '胡图图',
        applayTime: '2024-11-10 12:00:00',
    },
    {
        prejectId: 23434455,
        prejectName: '2024不良资产收购项目',
        status: '已撤销',
        orederNo: '**********',
        payStatus: '已支付',
        payType: '全款',
        name: '胡图图',
        money: '全款',
        remarks: '情况属实',
        payType: '全款',
        applayName: '胡图图',
        applayTime: '2024-11-10 12:00:00',
    },
])
const selectedArr = ref([])
const tabList = ref([
    { label: '待项目建账', value: '0', num: 32343 },
    { label: '建账待审批', value: '1', num: 32343 },
    { label: '建账审批中', value: '2', num: 32343 },
    { label: '建账成功', value: '3', num: 32343 },
    { label: '建账失败', value: '4', num: 32343 },
    { label: '已撤销', value: '5', num: 32343 },
    { label: '全部', value: 'all', num: 32343 },
])
const columns = ref([
    { "key": 0, "label": "项目ID", "visible": true },
    { "key": 1, "label": "项目名称", "visible": true },
    { "key": 2, "label": "资产转让方", "visible": true },
    { "key": 3, "label": "产品类型", "visible": true },
    { "key": 4, "label": "建账状态", "visible": true },
    { "key": 5, "label": "付款单申请流水号", "visible": true },
    { "key": 6, "label": "付款单状态", "visible": true },
    { "key": 7, "label": "付款类型", "visible": true },
    { "key": 8, "label": "收款账号", "visible": true },
    { "key": 9, "label": "账户名", "visible": true },
    { "key": 10, "label": "收款人", "visible": true },
    { "key": 11, "label": "开户行", "visible": true },
    { "key": 12, "label": "合同金额", "visible": true },
    { "key": 13, "label": "申请金额", "visible": true },
    { "key": 14, "label": "备注", "visible": true },
    { "key": 15, "label": "申请人", "visible": true },
    { "key": 16, "label": "申请时间", "visible": true }
])
function getList() {

}
function handleAdd(row) {

}
function handleRevoke(row) {

}
function handleDetails(row) {
    const query = { path: route.path, pageType: 'projectAccounting', progressStatus: 3 }
    router.push({ path: `/dueDiligence/projectInfo`, query })
}
function handleSelectionChange(selection) {
    selectedArr.value = selection
}
function selectable() {
    return !queryParams.value.allQuery;
}
</script>

<style lang="scss" scoped></style>