<template>
    <div class="app-container" v-loading="loading">
        <el-row :getter="24">
            <el-col :span="7" :xs="24" :class="`pr20 ${form.qualityStatus == 2 && 'recheck'}`">
                <el-form label-width="100px" ref="formRef" :model="form">
                    <el-form-item prop="agentModel" label="坐席模型">
                        <el-select v-model="recheckForm.agentModel" style="width:100%;" multiple collapse-tags
                            collapse-tags-tooltip clearable filterable placeholder="请选择坐席模型">
                            <el-option v-for="item in agentOption" :key="item.modelName" :value="item.modelName"
                                :label="item.modleName" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="customerModel" label="客户模型">
                        <el-select v-model="recheckForm.customerModel" style="width:100%;" multiple collapse-tags
                            clearable filterable collapse-tags-tooltip placeholder="请选择客户模型">
                            <el-option v-for="item in customerOption" :key="item.modelName" :value="item.modelName"
                                :label="item.modleName" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="type" label="左侧通道模型">
                        <el-radio-group v-model="recheckForm.type">
                            <el-radio :label="0">客户</el-radio>
                            <el-radio :label="1">坐席</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
                <div class="tip-msg">注：系统默认左侧通道为客户，如需修改，请重新设置后提交复检。 </div>
                <div class="text-right mt20">
                    <el-button type="primary" @click="handleRecheck">提交复检</el-button>
                </div>
                <div>
                    质检结果：{{ recheckEnum[form.artificialResult] }}
                    <span v-if="form.artificialResult == 2">({{ getRecheckNum(form.agentHit, form.customerHit)
                    }})</span>
                </div>
                <div class="remark-list">
                    <div class="remark-item" v-for="(item, index) in form.agentHit" :key="index">
                        <span class="remark-item-info">
                            <el-button type="text" plain icon="WarningFilled" style="color:orange" />
                            坐席：{{ item.modelName }}</span>
                        <template v-if="item.modelLabel">
                            <div class="tag-sign mr10" v-for="v in item.modelLabel.split(',')" :key="v"
                                @click="toContent(v, recheckForm.type == 0 ? 'agent-info' : 'client-info')">
                                {{ v }}
                            </div>
                        </template>
                    </div>
                    <div class="remark-item" v-for="(item, index) in form.customerHit" :key="index">
                        <span class="remark-item-info">
                            <el-button type="text" plain icon="WarningFilled" style="color:orange" />
                            客戶：{{ item.modelName }}</span>
                        <template v-if="item.modelLabel">
                            <div class="tag-sign mr10" v-for="v in item.modelLabel.split(',')" :key="v"
                                @click="toContent(v, recheckForm.type == 0 ? 'client-info' : 'agent-info')">
                                {{ v }}
                            </div>
                        </template>
                    </div>
                </div>
            </el-col>
            <el-col :span="17" :xs="24">
                <div class="df-jc-sb mb10">
                    <div class="title">
                        <svg-icon icon-class="recording" class="title-icon" />录音播放
                    </div>
                    <div class="call-info">呼叫时间：{{ form.callTime }}</div>
                    <div class="call-info">主叫号码：{{ form.callFrom }}</div>
                    <div class="call-info">被叫号码：{{ form.callTo }}</div>
                    <div class="oprea-btn">
                        <el-button type="primary" @click="handleDownloadText">导出文本</el-button>
                        <el-button type="primary" @click="handleDownloadRecord">录音下载</el-button>
                        <el-button icon="ArrowLeft" @click="toBack">返回上一级</el-button>
                    </div>
                </div>
                <wavesurfer ref="wavesurferRef" :wavesurfer-options="wavesurferOptions" show-hover
                    v-model:currentAudioTime="currentAudioTime" />
                <div class="recording-txt mt20" id="recording-txt-area">
                    <template v-for="(item, index) in textDataList || []" :key="index">
                        <div :class="`${item.type == 0 ? 'df client-info' : 'df-ai-fe agent-info'}  mb20`">
                            <div :class="`head-portrait ${recheckForm.type == 0 ? 'client' : 'seat'}`"
                                v-if="item.type == 0">{{ recheckForm.type == 0 ? '客' : '坐' }}
                            </div>
                            <div :class="`text-box mt10 ml10 `">
                                <div class="time text-right mb5">{{ getChatTime(item.beginTime) }}</div>
                                <div :class="`text `">
                                    {{ item.text }}
                                </div>
                            </div>
                            <div :class="`head-portrait ${recheckForm.type == 0 ? 'seat' : 'client'}`"
                                v-if="item.type == 1">{{ recheckForm.type == 0 ? '坐' : '客' }}
                            </div>
                        </div>
                    </template>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import wavesurfer from "@/components/Wavesurfer";
import { getInfoTaskApi, getTextTaskApi, artificialTaskApi } from "@/api/qcService/modelQc";
import { getAgentOptionApi, getCustomerOptionApi } from "@/api/options";
import { recheckEnum } from "@/utils/enum";
const { proxy } = getCurrentInstance();
const route = useRoute()
const form = ref({ type: 0, agentHit: [], customerHit: [] })
const wavesurferOptions = ref({
    progressColor: "#409eff",
    cursorColor: "#409eff",
    waveColor: "#409eff",
    cursorWidth: 1,
    autoplay: false,
    autoCenter: true,
    minPxPerSec: 20,
});
const loading = ref(false)
const agentOption = ref([])
const customerOption = ref([])
const textDataList = ref([])
const currentAudioTime = ref(0); //当前播放时间
const recheckForm = ref({})
// 提交复检
function handleRecheck() {
    const title = '提交复检'
    const content = `该操作将会对所选录音进行复检，确认进行操作吗?`
    proxy.$modal.confirm(content, title).then(() => {
        const reqForm = {
            type: recheckForm.value.type,
            callRecordId: route.query.callRecordId,
            agentText: form.value.agentText,
            customerText: form.value.customerText,
            agentModel: String(recheckForm.value.agentModel),
            customerModel: String(recheckForm.value.customerModel),
        }
        loading.value = true
        artificialTaskApi(reqForm).then(res => {
            getData()
            proxy.$modal.msgSuccess('操作成功！')
        }).finally(() => loading.value = false)
    })
}

// 录音下载
function handleDownloadRecord() {
    window.open(form.value.download)
}
// 导出文本
function handleDownloadText() {
    const reqForm = { callRecordId: route.query.callRecordId }
    const recordName = `${form.value.recordName}.docx`
    proxy.downloadforjson('/quality/quality/export', reqForm, recordName)
}

// 获取数据
getData()
async function getData() {
    try {
        loading.value = true
        const reqForm = { callRecordId: route.query.callRecordId }
        const res1 = await getInfoTaskApi(reqForm)
        form.value = res1.data
        recheckForm.value.agentModel = res1.data.agentHit?.map(v => v.modelName)
        recheckForm.value.customerModel = res1.data.customerHit?.map(v => v.modelName)
        proxy.$refs['wavesurferRef'].initWaveSurfer(res1.data.download)
        recheckForm.value.type = res1.data.type || 0
        const res2 = await getTextTaskApi(reqForm)
        textDataList.value = res2.data
        loading.value = false
    } catch (error) {
        console.error(error);

        loading.value = false
    }

}

function toContent(content, className) {
    const textList = document.querySelector('#recording-txt-area')
    const domList = textList.querySelectorAll(`.${className}`)
    let domInfo = null
    for (let i = 0; i < domList.length; i++) {
        if (domList[i].innerText.includes(content)) {
            domInfo = domList[i]
        }
    }
    domInfo && textList.scrollTo({ behavior: 'smooth', top: (domInfo.offsetTop - 250), });
}

function toBack() {
    proxy.$tab.closeOpenPage({ path: route.query.path });
}
/** 获取时间 */
function getChatTime(time) {
    try {
        let startStamp = new Date(form.value.callTime);
        startStamp = startStamp.getTime();
        startStamp += time;
        return proxy.parseTime(startStamp);
    } catch (error) {
        return "--";
    }
}

getAgentOptionFun()
function getAgentOptionFun() {
    getAgentOptionApi().then(res => {
        agentOption.value = res.data
    })

}
getCustomerOptionFun()
function getCustomerOptionFun() {
    getCustomerOptionApi().then(res => {
        customerOption.value = res.data
    })
}

function getRecheckNum(arr1, arr2) {
    const newArr = [...arr1, ...arr2].filter(v => v.modelLabel)
    return newArr.length
}

</script>

<style lang="scss" scoped>
.recheck {
    position: relative;

    &::after {
        content: "";
        position: absolute;
        top: 192px;
        right: -60px;
        width: 200px;
        height: 200px;
        background-image: url(@/assets/images/recheck.png);
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 1;
        pointer-events: none;
    }
}

.tip-msg {
    color: #666;
    font-size: 14px;
}

.call-info {
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
}

.remark-list {
    margin-top: 10px;
    max-height: 55vh;
    overflow: auto;

    .remark-item {
        margin-top: 10px;

        .remark-item-info {
            display: flex;
            align-items: center;

            .el-button {
                margin-right: 5px;
            }
        }
    }

}

.title {
    display: flex;
    align-items: center;

    .title-icon {
        margin-right: 5px;
        font-size: 24px;
        color: #409eff;
    }
}

.recording-txt {
    height: 60vh;
    overflow: auto;
    padding: 10px;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
}

.head-portrait {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    text-align: center;
    line-height: 44px;
    font-weight: 900;
}

.client {
    color: #f86e6f;
    background-color: #feebec;

    &+.text-box .text {
        background-color: #f5f5f5;
    }
}

.seat {
    color: #25b9b3;
    background-color: #e1f6f4;
}

.seat-text {
    background-color: #e1f6f4;
}

.time {
    font-size: 14px;
    color: #aaa;
}

.tag-sign {
    cursor: pointer;
    font-size: 14px;
    display: inline-block;
    padding: 5px 10px;
    color: #f7aa62;
    border-radius: 5px;
    background-color: #fdf6ec;
}

.text {
    max-width: 450px;
    padding: 5px 10px;
    border-radius: 5px;
}

.df {
    display: flex;
}

.df-ai-fe {
    display: flex;
    // flex-direction: column;
    justify-content: flex-end;
}

.df-jc-sb {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>