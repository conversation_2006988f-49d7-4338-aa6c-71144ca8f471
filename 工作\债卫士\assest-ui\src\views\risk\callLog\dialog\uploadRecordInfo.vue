<template>
    <el-dialog title="上传录音" v-model="open" append-to-body width="750px" :before-close="cancel">
        <div v-if="active == 0" class="uploadRecordInfo">
            <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
                <el-form-item label="录音类型" prop="recordingType">
                    <el-radio-group v-model="form.recordingType">
                        <el-radio v-for="(v, i) in recordingTypeEnum" :key="i" :label="+i">{{ v }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" :rows="5" show-word-limit :maxlength="500"
                        style="width: 90%;" placeholder="请输入备注" />
                </el-form-item>
                <el-form-item label="上传录音" prop="uploadParam">
                    <FileUpload v-model:fileList="fileList" :limit="10" uploadFileUrl="/file/uploadFiles"
                        :fileType="fileType" :fileSize="50" drag>
                        <template #drag-tip-msg>
                            <div class="drag-tip-msg">
                                <span>注意：1.单个文件需小于50M.</span>
                                <p class="drag-tip-msg-item">
                                    <br /> 2.上传文件仅支持zip格式.
                                    <br /> 3.上传单个录音时，需将录音打包到以案件ID命名的子文件夹内；上传多个案件的录音时，需创建多个以案件ID命名的子文件夹；上传时将最外层母文件夹压缩成zip格式进行上传.
                                    <br /> 4.文件夹内录音格式仅支持MP3、MP4、WAV、M4A、WMV、AMR、AAC、FLAC、OGG、AIFF、DSD.
                                    <br /> 5.单次支持上传文件数≤10个.
                                </p>
                            </div>
                        </template>
                    </FileUpload>
                </el-form-item>
            </el-form>
        </div>
        <div v-if="active == 1" class="text-center to-page">
            <el-button type="success" size="large" class="mb20" icon="Check" circle /><br />
            录音上传已完成，可至 <span @click="handleToPage">导入日志</span>页面查看导入结果！
        </div>
        <template #footer v-if="active == 0">
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" type="primary" @click="sumbit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import FileUpload from "@/components/FileUpload"
import { uploadFileListApi } from '@/api/seat/calllog'
import { recordingTypeEnum } from '@/utils/enum'
const { proxy } = getCurrentInstance()
const router = useRouter()
const active = ref(0)
const props = defineProps({
    getList: { type: Function }
})
const fileType = ref(['zip'])
const open = ref(false)
const loading = ref(false)
const fileList = ref([])
const data = reactive({
    form: {
        recordingType: 0
    },
    rules: {
        recordingType: [{ required: true, message: '请选择录音类型', trigger: 'change' }],
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
        uploadParam: [{ required: true, message: '请上传录音', trigger: 'change' }],
    },
})
const { form, rules } = toRefs(data)
function sumbit() {
    form.value.uploadParam = fileList.value?.map(v => v.response.data[0])
    nextTick(() => {
        proxy.$refs['formRef'].validate((valid) => {
            if (valid) {
                const reqForm = JSON.parse(JSON.stringify(form.value))
                reqForm.whetherSearch = false
                loading.value = true
                uploadFileListApi(reqForm).then(() => {
                    active.value++
                    props.getList && props.getList()
                }).finally(() => loading.value = false)
            }
        })
    })

}
function openDialog() {
    open.value = true
}

function handleToPage() {
    cancel()
    const query = { type: '6' }
    router.push({ path: '/assets/importLog', query })
}
function cancel() {
    open.value = false
    form.value = {}
    active.value = 0
    fileList.value = []
}
defineExpose({ openDialog })
</script>

<style lang="scss" scoped>
.drag-tip-msg {
    text-align: left;
    padding: 0 20%;
    color: #aaaaaa;

    .drag-tip-msg-item {
        position: relative;
        padding-left: 45px;
        line-height: 24px;
        top: -31px;
    }
}

.to-page {
    font-size: 18px;

    span {
        cursor: pointer;
        color: #409eff;
    }
}
</style>
<style lang="scss">
.uploadRecordInfo {
    max-height: 65vh;
    overflow: auto;

    .el-upload-dragger {
        padding: 20px 10px 0 !important;
    }
}
</style>