{"version": 3, "sources": ["../vue-demi/lib/index.mjs"], "sourcesContent": ["import * as Vue from 'vue'\n\nvar isVue2 = false\nvar isVue3 = true\nvar Vue2 = undefined\n\nfunction install() {}\n\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key)\n    target.splice(key, 1, val)\n    return val\n  }\n  target[key] = val\n  return val\n}\n\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1)\n    return\n  }\n  delete target[key]\n}\n\nexport * from 'vue'\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n  install,\n}\n"], "mappings": ";AAEA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,OAAO;AAIJ,aAAa,QAAQ,KAAK,KAAK;AACpC,MAAI,MAAM,QAAQ,SAAS;AACzB,WAAO,SAAS,KAAK,IAAI,OAAO,QAAQ;AACxC,WAAO,OAAO,KAAK,GAAG;AACtB,WAAO;AAAA;AAET,SAAO,OAAO;AACd,SAAO;AAAA;AAGF,aAAa,QAAQ,KAAK;AAC/B,MAAI,MAAM,QAAQ,SAAS;AACzB,WAAO,OAAO,KAAK;AACnB;AAAA;AAEF,SAAO,OAAO;AAAA;", "names": []}