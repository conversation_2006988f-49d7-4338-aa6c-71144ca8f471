<template>
    <div class="content-area">
        <el-form :model="form" :rules="rules" label-width="auto">
            <el-form-item label="选择产品组合" prop="productGroup">
                <el-transfer filterable :titles="['产品库', '预选产品']" v-model="form.productGroup" :data="productList" />
            </el-form-item>
            <el-form-item label="案件总量" prop="caseNum">
                <div>
                    <el-input-number v-model="form.caseNum" />
                    <div class="product-info-list">
                        <span class="product-info-item mr20" v-for="(v, i) in selectProductArr">
                            产品{{ `${i + 1}：${v.label}` }}
                        </span>
                    </div>
                    <div class="product-input-list">
                        <div class="product-info-item mr20 mb10" v-for="(v, i) in selectProductArr">
                            <span class="mt10 mb10">产品{{ i + 1 }}权重配比：</span>&nbsp;
                            <InputNum v-model="v.weight" style="width: 120px;" @change="handleChangeInput(i)" />&nbsp;%
                            &nbsp;&nbsp;&nbsp;=
                            {{ parseInt((form.caseNum || 0) * ((v.weight || 0) / 100)) }}笔
                        </div>
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup>

const { proxy } = getCurrentInstance()
const data = reactive({
    form: {
        productGroup: [],
        productArr: [],
    },
    rules: {
        productGroup: [{ required: true, message: '请选择产品组合', trigger: 'blur' }],
        caseNum: [{ required: true, message: '请输入案件数量', trigger: 'blur' }],
    },
})
const { form, rules } = toRefs(data)
const productList = ref([
    { key: 111, label: '精英贷' },
    { key: 222, label: '成长钱包' },
    { key: 333, label: '成就贷' },
    { key: 444, label: '小鱼福卡' },
    { key: 11, label: '沃享极速分期', },
    { key: 12, label: '智鹿秒批贷', },
    { key: 13, label: '风云循环贷', },
    { key: 14, label: '信用付 PLUS', },
    { key: 15, label: '零零花青春版', },
    { key: 16, label: '教育智分期', },
    { key: 17, label: '家装无忧贷', },
    { key: 18, label: '旅行白条', },
    { key: 19, label: '医享付', },
    { key: 111, label: '绿享贷', },
    { key: 112, label: '乐享贷' },
])

// 
function handleChangeInput(index) {
    nextTick(() => {
        const sum = selectProductArr.value.reduce((s, v) => s += +(v.weight || 0), 0)
        if (sum > 100) {
            proxy.$modal.msgWarning('权重配比不能超过一百！请重新输入')
            selectProductArr.value[index].weight = null
        }
    })
}
const selectProductArr = computed(() => productList.value.filter(v => form.value.productGroup.includes(v.key)) || [])
</script>

<style lang="scss" scoped>
.content-area {
    width: 65%;
    margin: 0 auto;
}

:deep(.el-transfer) {
    width: 80%;

    .el-transfer-panel__header {
        .el-checkbox {
            display: flex;
            padding-right: 10px;
            justify-content: space-between;

            .el-checkbox__label {
                font-size: 14px;

                span {
                    left: 18px;

                    &::after {
                        content: '条';
                    }
                }
            }
        }
    }

    .el-transfer__buttons {
        .el-button {
            display: block;

            &:first-of-type {
                margin-bottom: 10px;
            }
        }

        .el-button+.el-button {
            margin: 0;
        }
    }

    .el-transfer-panel .el-transfer-panel__header {
        background-color: #fff;
    }
}

.product-info-item {
    color: #999;
}
</style>