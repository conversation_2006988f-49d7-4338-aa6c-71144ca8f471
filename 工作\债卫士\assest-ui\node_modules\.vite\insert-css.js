import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/insert-css/index.js
var require_insert_css = __commonJS({
  "node_modules/insert-css/index.js"(exports, module) {
    var containers = [];
    var styleElements = [];
    var usage = "insert-css: You need to provide a CSS string. Usage: insertCss(cssString[, options]).";
    function insertCss(css, options) {
      options = options || {};
      if (css === void 0) {
        throw new Error(usage);
      }
      var position = options.prepend === true ? "prepend" : "append";
      var container = options.container !== void 0 ? options.container : document.querySelector("head");
      var containerId = containers.indexOf(container);
      if (containerId === -1) {
        containerId = containers.push(container) - 1;
        styleElements[containerId] = {};
      }
      var styleElement;
      if (styleElements[containerId] !== void 0 && styleElements[containerId][position] !== void 0) {
        styleElement = styleElements[containerId][position];
      } else {
        styleElement = styleElements[containerId][position] = createStyleElement();
        if (position === "prepend") {
          container.insertBefore(styleElement, container.childNodes[0]);
        } else {
          container.appendChild(styleElement);
        }
      }
      if (css.charCodeAt(0) === 65279) {
        css = css.substr(1, css.length);
      }
      if (styleElement.styleSheet) {
        styleElement.styleSheet.cssText += css;
      } else {
        styleElement.textContent += css;
      }
      return styleElement;
    }
    function createStyleElement() {
      var styleElement = document.createElement("style");
      styleElement.setAttribute("type", "text/css");
      return styleElement;
    }
    module.exports = insertCss;
    module.exports.insertCss = insertCss;
  }
});

// dep:insert-css
var insert_css_default = require_insert_css();
export {
  insert_css_default as default
};
//# sourceMappingURL=insert-css.js.map
