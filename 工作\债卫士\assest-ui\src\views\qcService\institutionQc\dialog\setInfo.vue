<template>
  <el-dialog title="质检设置" v-model="open" append-to-body width="650px" :before-close="cancel">
    <el-form ref="formRef" label-width="120px" :model="form" :rules="rules">
      <el-form-item prop="agentModelId" label="坐席模型">
        <el-select style="width:80%;" v-model="form.agentModelId" multiple collapse-tags collapse-tags-tooltip clearable
          filterable placeholder="请选择坐席模型">
          <el-option v-for="item in agentOption" :key="item.id" :value="item.id" :label="item.modleName" />
        </el-select>
      </el-form-item>
      <el-form-item prop="customerModelId" label="客户模型">
        <el-select style="width:80%;" v-model="form.customerModelId" multiple collapse-tags collapse-tags-tooltip
          clearable filterable placeholder="请选择客户模型">
          <el-option v-for="item in customerOption" :key="item.id" :value="item.id" :label="item.modleName" />
        </el-select>
      </el-form-item>
      <el-form-item prop="type" label="质检时段及数量">
        <div style="width:100%">
          <el-radio-group v-model="form.type">
            <el-radio :label="0">平均质检</el-radio>
            <el-radio :label="1">自定义</el-radio>
          </el-radio-group>
          <div style="color:#b02537;">注：输入数字 0 为全量质检；</div>
        </div>
      </el-form-item>
      <template v-if="form.type == 0">
        <el-form-item v-for="(v, i) in form.teamConfigList" :key="v.key" :prop="`teamConfigList.${i}.section`"
          :rules="{ required: true, message: '请选择时间段', trigger: 'blur', }">
          <div class="time-section">
            <div style="width:180px;">
              <el-time-picker style="width:180px;" v-model="v.section" is-range range-separator=" "
                @blur="handleChangeTime(i)" start-placeholder="开始时间" end-placeholder="结束时间" format="HH:mm"
                value-format="HH:mm" />
            </div>
            <span class="ml20">
              每小时均质检
              <el-input v-model="v.recordNum" @input="handleInputNum(v, i)" style="width:120px;"
                placeholder="请输入质检数量" />&nbsp;个
              <el-button :loading="loading" class="ml20" type="text" @click="add(v, i)" icon="Plus" />
              <el-button :loading="loading" type="text" v-if="i != 0" @click="remove(v, i)" icon="Delete" />
            </span>
          </div>
        </el-form-item>
      </template>
      <el-form-item prop="teamConfigList" v-if="form.type == 1">
        <el-table style="width:100%" :loading="loading" :data="form.teamConfigList">
          <el-table-column align="center" prop="section" label="质检时段">
            <template #default="{ row, $index }">
              <el-time-picker style="width:180px;" v-model="row.section" is-range range-separator=" "
                @blur="handleChangeTime($index)" start-placeholder="开始时间" end-placeholder="结束时间" format="HH:mm"
                value-format="HH:mm" />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="recordNum" label="质检数量" width="140">
            <template #default="{ row, $index }">
              <el-input v-model="row.recordNum" @input="handleInputNum(row, $index)" placeholder="请输入质检数量" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="60">
            <template #default="{ row, $index }">
              <div>
                <el-button :loading="loading" type="text" v-if="$index == 0" @click="add(row, $index)" icon="Plus" />
                <el-button :loading="loading" type="text" v-if="$index != 0" @click="remove(row, $index)"
                  icon="Delete" />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { getAgentOptionApi, getCustomerOptionApi } from "@/api/options";
import { insertConfigApi, getConfigApi } from "@/api/qcService/institutionQc";
const props = defineProps({
  getList: { type: Function, default: () => { } }
})
const { proxy } = getCurrentInstance()
const data = reactive({
  form: {
    type: 0,
    teamConfigList: [{ key: +new Date(), }]
  },
  rules: {
    qualityInspector: [{ required: true, message: '请选择质检员', trigger: 'blur' }],
    agentModelId: [{ required: true, message: '请选择坐席模型', trigger: 'blur' }],
    customerModelId: [{ required: true, message: '请选择客户模型', trigger: 'blur' }],
    type: [{ required: true, message: '质检时段及数量', trigger: 'blur' }],
  }
})
const agentOption = ref([])
const customerOption = ref([])
const open = ref(false)
const loading = ref(false)
const { form, rules } = toRefs(data)

function submit() {
  const reqForm = JSON.parse(JSON.stringify(form.value))
  if (!reqForm.teamConfigList.length) {
    return proxy.$modal.msgWarning('请增加质检时段选择！')
  }
  for (let i = 0; i < reqForm.teamConfigList.length; i++) {
    const v = reqForm.teamConfigList[i];
    if (!v.section || !v.section.length) {
      return proxy.$modal.msgWarning('请选择质检时段！')
    }
    if (v.recordNum == null || v.recordNum == undefined) {
      return proxy.$modal.msgWarning('请输入质检数量！')
    }
  }

  proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      reqForm.agentModelId = String(reqForm.agentModelId)
      reqForm.customerModelId = String(reqForm.customerModelId)
      reqForm.teamConfigList.forEach(v => {
        delete v.key
        v.agentModelId = reqForm.agentModelId
        v.customerModelId = reqForm.customerModelId
        v.section = v.section.join('-')
      })
      insertConfigApi(reqForm).then(() => {
        cancel()
        props.getList && props.getList()
        proxy.$modal.msgSuccess('操作成功！')
      })
    }
  })
}

// 输入质检数量
function handleInputNum(row, index) {
  nextTick(() => {
    if (!/\d/.test(row.recordNum)) {
      const teamConfigInfo = form.value.teamConfigList[index]
      teamConfigInfo.recordNum = teamConfigInfo.recordNum.replace(/[^\d]/g, '')
    }
  })
}

function add(row, index) {
  if (form.value.teamConfigList.length > 12) {
    return false
  }
  const obj = { key: +new Date() }
  form.value.teamConfigList.splice(index + 1, 0, obj)
}
function remove(row, index) {
  if (form.value.teamConfigList.length == 1) {
    return false
  }
  form.value.teamConfigList.splice(index, 1)
}
function openDialog(data) {
  open.value = true
  form.value.teamId = data.teamId
  form.value.teamConfigList = [{ key: +new Date() }]
  if (data.id) {
    getConfigFun(data)
  }
}
function cancel() {
  form.value = {}
  open.value = false
}

// 选择时间
function handleChangeTime(index) {
  nextTick(() => {
    const arr = form.value.teamConfigList
    const date = new Date()
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate() + 1
    for (let i = 0; i < arr.length; index++) {
      if (!arr[i] || !arr[index] || !arr[i].section || !arr[index].section) {
        return false
      }
      if (i < index) {
        const upEndTime = `${year}-${month}-${day} ${arr[i].section[1]}`
        const CurEndTime = `${year}-${month}-${day} ${arr[index].section[0]}`
        if (upEndTime > CurEndTime) {
          arr[index].section = undefined
          proxy.$modal.msgWarning('开始时间不能小于上一个时间段的结束时间，请重新选择时间范围！')
          return false
        }
      }
    }
  })
}

function getConfigFun(data) {
  getConfigApi(data).then(res => {
    form.value.teamConfigList = res.data.map(v => {
      form.value.agentModelId = v.agentModelId ? v.agentModelId.split(',') : null
      form.value.customerModelId = v.customerModelId ? v.customerModelId.split(',') : null
      return { ...v, key: +new Date(), section: v.section.split('-') }
    })
  })
}

getAgentOptionFun()
function getAgentOptionFun() {
  getAgentOptionApi().then(res => {
    agentOption.value = res.data
  })

}
getCustomerOptionFun()
function getCustomerOptionFun() {
  getCustomerOptionApi().then(res => {
    customerOption.value = res.data
  })
}
defineExpose({ openDialog })
</script>
<style lang="scss" scoped>
.time-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>