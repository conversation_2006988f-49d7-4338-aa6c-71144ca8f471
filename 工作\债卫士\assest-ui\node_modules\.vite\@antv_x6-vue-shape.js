import {
  Graph,
  Markup,
  Node,
  NodeView,
  object_exports
} from "./chunk-7DXKJCQD.js";
import "./chunk-NPNDKBDY.js";
import {
  Vue2,
  isVue2,
  isVue3
} from "./chunk-IVNEGUN7.js";
import "./chunk-JGAWL3HB.js";
import {
  createApp
} from "./chunk-UOUDGCKJ.js";
import {
  Fragment,
  Teleport,
  defineComponent,
  h
} from "./chunk-MZ7ANLUJ.js";
import {
  markRaw,
  reactive
} from "./chunk-XLSXXTZ3.js";
import "./chunk-WC6BDPVA.js";

// node_modules/@antv/x6-vue-shape/es/node.js
var VueShape = class extends Node {
};
(function(VueShape2) {
  function getMarkup(primer) {
    const markup = [];
    const content = Markup.getForeignObjectMarkup();
    if (primer) {
      markup.push(...[
        {
          tagName: primer,
          selector: "body"
        },
        content
      ]);
    } else {
      markup.push(content);
    }
    return markup;
  }
  VueShape2.config({
    view: "vue-shape-view",
    markup: getMarkup(),
    attrs: {
      body: {
        fill: "none",
        stroke: "none",
        refWidth: "100%",
        refHeight: "100%"
      },
      fo: {
        refWidth: "100%",
        refHeight: "100%"
      }
    },
    propHooks(metadata) {
      if (metadata.markup == null) {
        const primer = metadata.primer;
        if (primer) {
          metadata.markup = getMarkup(primer);
          let attrs = {};
          switch (primer) {
            case "circle":
              attrs = {
                refCx: "50%",
                refCy: "50%",
                refR: "50%"
              };
              break;
            case "ellipse":
              attrs = {
                refCx: "50%",
                refCy: "50%",
                refRx: "50%",
                refRy: "50%"
              };
              break;
            default:
              break;
          }
          metadata.attrs = object_exports.merge({}, {
            body: Object.assign({ refWidth: null, refHeight: null }, attrs)
          }, metadata.attrs || {});
        }
      }
      return metadata;
    }
  });
  Node.registry.register("vue-shape", VueShape2, true);
})(VueShape || (VueShape = {}));

// node_modules/@antv/x6-vue-shape/es/registry.js
var __rest = function(s, e) {
  var t = {};
  for (var p in s)
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
      t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
};
var shapeMaps = {};
function register(config) {
  const { shape, component, inherit } = config, others = __rest(config, ["shape", "component", "inherit"]);
  if (!shape) {
    throw new Error("should specify shape in config");
  }
  shapeMaps[shape] = {
    component
  };
  Graph.registerNode(shape, Object.assign({ inherit: inherit || "vue-shape" }, others), true);
}

// node_modules/@antv/x6-vue-shape/es/teleport.js
var active = false;
var items = reactive({});
function connect(id, component, container, node, graph) {
  if (active) {
    items[id] = markRaw(defineComponent({
      render: () => h(Teleport, { to: container }, [h(component, { node, graph })]),
      provide: () => ({
        getNode: () => node,
        getGraph: () => graph
      })
    }));
  }
}
function disconnect(id) {
  if (active) {
    delete items[id];
  }
}
function isActive() {
  return active;
}
function getTeleport() {
  if (!isVue3) {
    throw new Error("teleport is only available in Vue3");
  }
  active = true;
  return defineComponent({
    setup() {
      return () => h(Fragment, {}, Object.keys(items).map((id) => h(items[id])));
    }
  });
}

// node_modules/@antv/x6-vue-shape/es/view.js
var VueShapeView = class extends NodeView {
  getComponentContainer() {
    return this.selectors && this.selectors.foContent;
  }
  confirmUpdate(flag) {
    const ret = super.confirmUpdate(flag);
    return this.handleAction(ret, VueShapeView.action, () => {
      this.renderVueComponent();
    });
  }
  targetId() {
    return `${this.graph.view.cid}:${this.cell.id}`;
  }
  renderVueComponent() {
    this.unmountVueComponent();
    const root = this.getComponentContainer();
    const node = this.cell;
    const graph = this.graph;
    if (root) {
      const { component } = shapeMaps[node.shape];
      if (component) {
        if (isVue2) {
          const Vue = Vue2;
          this.vm = new Vue({
            el: root,
            render(h2) {
              return h2(component, { node, graph });
            },
            provide() {
              return {
                getNode: () => node,
                getGraph: () => graph
              };
            }
          });
        } else if (isVue3) {
          if (isActive()) {
            connect(this.targetId(), component, root, node, graph);
          } else {
            this.vm = createApp({
              render() {
                return h(component, { node, graph });
              },
              provide() {
                return {
                  getNode: () => node,
                  getGraph: () => graph
                };
              }
            });
            this.vm.mount(root);
          }
        }
      }
    }
  }
  unmountVueComponent() {
    const root = this.getComponentContainer();
    if (this.vm) {
      isVue2 && this.vm.$destroy();
      isVue3 && this.vm.unmount();
      this.vm = null;
    }
    if (root) {
      root.innerHTML = "";
    }
    return root;
  }
  onMouseDown(e, x, y) {
    const target = e.target;
    const tagName = target.tagName.toLowerCase();
    if (tagName === "input") {
      const type = target.getAttribute("type");
      if (type == null || [
        "text",
        "password",
        "number",
        "email",
        "search",
        "tel",
        "url"
      ].includes(type)) {
        return;
      }
    }
    super.onMouseDown(e, x, y);
  }
  unmount() {
    if (isActive()) {
      disconnect(this.targetId());
    }
    this.unmountVueComponent();
    super.unmount();
    return this;
  }
};
(function(VueShapeView2) {
  VueShapeView2.action = "vue";
  VueShapeView2.config({
    bootstrap: [VueShapeView2.action],
    actions: {
      component: VueShapeView2.action
    }
  });
  NodeView.registry.register("vue-shape-view", VueShapeView2, true);
})(VueShapeView || (VueShapeView = {}));
export {
  VueShape,
  VueShapeView,
  connect,
  disconnect,
  getTeleport,
  isActive,
  register,
  shapeMaps
};
//# sourceMappingURL=@antv_x6-vue-shape.js.map
