{"version": 3, "sources": ["../tinymce/plugins/preview/plugin.js", "../tinymce/plugins/preview/index.js", "dep:tinymce_plugins_preview"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var getContentStyle = function (editor) {\n      return editor.getParam('content_style', '', 'string');\n    };\n    var shouldUseContentCssCors = function (editor) {\n      return editor.getParam('content_css_cors', false, 'boolean');\n    };\n    var getBodyClassByHash = function (editor) {\n      var bodyClass = editor.getParam('body_class', '', 'hash');\n      return bodyClass[editor.id] || '';\n    };\n    var getBodyClass = function (editor) {\n      var bodyClass = editor.getParam('body_class', '', 'string');\n      if (bodyClass.indexOf('=') === -1) {\n        return bodyClass;\n      } else {\n        return getBodyClassByHash(editor);\n      }\n    };\n    var getBodyIdByHash = function (editor) {\n      var bodyId = editor.getParam('body_id', '', 'hash');\n      return bodyId[editor.id] || bodyId;\n    };\n    var getBodyId = function (editor) {\n      var bodyId = editor.getParam('body_id', 'tinymce', 'string');\n      if (bodyId.indexOf('=') === -1) {\n        return bodyId;\n      } else {\n        return getBodyIdByHash(editor);\n      }\n    };\n\n    var getPreviewHtml = function (editor) {\n      var headHtml = '';\n      var encode = editor.dom.encode;\n      var contentStyle = getContentStyle(editor);\n      headHtml += '<base href=\"' + encode(editor.documentBaseURI.getURI()) + '\">';\n      var cors = shouldUseContentCssCors(editor) ? ' crossorigin=\"anonymous\"' : '';\n      global.each(editor.contentCSS, function (url) {\n        headHtml += '<link type=\"text/css\" rel=\"stylesheet\" href=\"' + encode(editor.documentBaseURI.toAbsolute(url)) + '\"' + cors + '>';\n      });\n      if (contentStyle) {\n        headHtml += '<style type=\"text/css\">' + contentStyle + '</style>';\n      }\n      var bodyId = getBodyId(editor);\n      var bodyClass = getBodyClass(editor);\n      var isMetaKeyPressed = global$1.mac ? 'e.metaKey' : 'e.ctrlKey && !e.altKey';\n      var preventClicksOnLinksScript = '<script>' + 'document.addEventListener && document.addEventListener(\"click\", function(e) {' + 'for (var elm = e.target; elm; elm = elm.parentNode) {' + 'if (elm.nodeName === \"A\" && !(' + isMetaKeyPressed + ')) {' + 'e.preventDefault();' + '}' + '}' + '}, false);' + '</script> ';\n      var directionality = editor.getBody().dir;\n      var dirAttr = directionality ? ' dir=\"' + encode(directionality) + '\"' : '';\n      var previewHtml = '<!DOCTYPE html>' + '<html>' + '<head>' + headHtml + '</head>' + '<body id=\"' + encode(bodyId) + '\" class=\"mce-content-body ' + encode(bodyClass) + '\"' + dirAttr + '>' + editor.getContent() + preventClicksOnLinksScript + '</body>' + '</html>';\n      return previewHtml;\n    };\n\n    var open = function (editor) {\n      var content = getPreviewHtml(editor);\n      var dataApi = editor.windowManager.open({\n        title: 'Preview',\n        size: 'large',\n        body: {\n          type: 'panel',\n          items: [{\n              name: 'preview',\n              type: 'iframe',\n              sandboxed: true\n            }]\n        },\n        buttons: [{\n            type: 'cancel',\n            name: 'close',\n            text: 'Close',\n            primary: true\n          }],\n        initialData: { preview: content }\n      });\n      dataApi.focus('close');\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mcePreview', function () {\n        open(editor);\n      });\n    };\n\n    var register = function (editor) {\n      var onAction = function () {\n        return editor.execCommand('mcePreview');\n      };\n      editor.ui.registry.addButton('preview', {\n        icon: 'preview',\n        tooltip: 'Preview',\n        onAction: onAction\n      });\n      editor.ui.registry.addMenuItem('preview', {\n        icon: 'preview',\n        text: 'Preview',\n        onAction: onAction\n      });\n    };\n\n    function Plugin () {\n      global$2.add('preview', function (editor) {\n        register$1(editor);\n        register(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"preview\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/preview')\n//   ES2015:\n//     import 'tinymce/plugins/preview'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/preview/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,OAAO,SAAS,iBAAiB,IAAI;AAAA;AAE9C,UAAI,0BAA0B,SAAU,QAAQ;AAC9C,eAAO,OAAO,SAAS,oBAAoB,OAAO;AAAA;AAEpD,UAAI,qBAAqB,SAAU,QAAQ;AACzC,YAAI,YAAY,OAAO,SAAS,cAAc,IAAI;AAClD,eAAO,UAAU,OAAO,OAAO;AAAA;AAEjC,UAAI,eAAe,SAAU,QAAQ;AACnC,YAAI,YAAY,OAAO,SAAS,cAAc,IAAI;AAClD,YAAI,UAAU,QAAQ,SAAS,IAAI;AACjC,iBAAO;AAAA,eACF;AACL,iBAAO,mBAAmB;AAAA;AAAA;AAG9B,UAAI,kBAAkB,SAAU,QAAQ;AACtC,YAAI,SAAS,OAAO,SAAS,WAAW,IAAI;AAC5C,eAAO,OAAO,OAAO,OAAO;AAAA;AAE9B,UAAI,YAAY,SAAU,QAAQ;AAChC,YAAI,SAAS,OAAO,SAAS,WAAW,WAAW;AACnD,YAAI,OAAO,QAAQ,SAAS,IAAI;AAC9B,iBAAO;AAAA,eACF;AACL,iBAAO,gBAAgB;AAAA;AAAA;AAI3B,UAAI,iBAAiB,SAAU,QAAQ;AACrC,YAAI,WAAW;AACf,YAAI,SAAS,OAAO,IAAI;AACxB,YAAI,eAAe,gBAAgB;AACnC,oBAAY,iBAAiB,OAAO,OAAO,gBAAgB,YAAY;AACvE,YAAI,OAAO,wBAAwB,UAAU,6BAA6B;AAC1E,eAAO,KAAK,OAAO,YAAY,SAAU,KAAK;AAC5C,sBAAY,kDAAkD,OAAO,OAAO,gBAAgB,WAAW,QAAQ,MAAM,OAAO;AAAA;AAE9H,YAAI,cAAc;AAChB,sBAAY,4BAA4B,eAAe;AAAA;AAEzD,YAAI,SAAS,UAAU;AACvB,YAAI,YAAY,aAAa;AAC7B,YAAI,mBAAmB,SAAS,MAAM,cAAc;AACpD,YAAI,6BAA6B,6KAA4L,mBAAmB;AAChP,YAAI,iBAAiB,OAAO,UAAU;AACtC,YAAI,UAAU,iBAAiB,WAAW,OAAO,kBAAkB,MAAM;AACzE,YAAI,cAAc,gCAA0C,WAAW,sBAA2B,OAAO,UAAU,+BAA+B,OAAO,aAAa,MAAM,UAAU,MAAM,OAAO,eAAe,6BAA6B;AAC/O,eAAO;AAAA;AAGT,UAAI,OAAO,SAAU,QAAQ;AAC3B,YAAI,UAAU,eAAe;AAC7B,YAAI,UAAU,OAAO,cAAc,KAAK;AAAA,UACtC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,WAAW;AAAA;AAAA;AAAA,UAGjB,SAAS,CAAC;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA;AAAA,UAEb,aAAa,EAAE,SAAS;AAAA;AAE1B,gBAAQ,MAAM;AAAA;AAGhB,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,cAAc,WAAY;AAC1C,eAAK;AAAA;AAAA;AAIT,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,UAAU,WAAW;AAAA,UACtC,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA;AAEF,eAAO,GAAG,SAAS,YAAY,WAAW;AAAA,UACxC,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA;AAAA;AAIJ,wBAAmB;AACjB,iBAAS,IAAI,WAAW,SAAU,QAAQ;AACxC,qBAAW;AACX,mBAAS;AAAA;AAAA;AAIb;AAAA;AAAA;AAAA;;;AC3HJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,kCAAQ;", "names": []}