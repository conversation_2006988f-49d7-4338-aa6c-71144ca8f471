{"version": 3, "sources": ["../wavesurfer.js/dist/plugins/hover.js", "dep:wavesurfer_js_dist_plugins_hover_js"], "sourcesContent": ["class t{constructor(){this.listeners={}}on(t,e,s){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),null==s?void 0:s.once){const s=()=>{this.un(t,s),this.un(t,e)};return this.on(t,s),s}return()=>this.un(t,e)}un(t,e){var s;null===(s=this.listeners[t])||void 0===s||s.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach((t=>t(...e)))}}class e extends t{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}_init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t()))}}function s(t,e){const i=e.xmlns?document.createElementNS(e.xmlns,t):document.createElement(t);for(const[t,n]of Object.entries(e))if(\"children\"===t&&n)for(const[t,e]of Object.entries(n))e instanceof Node?i.appendChild(e):\"string\"==typeof e?i.appendChild(document.createTextNode(e)):i.appendChild(s(t,e));else\"style\"===t?Object.assign(i.style,n):\"textContent\"===t?i.textContent=n:i.setAttribute(t,n.toString());return i}function i(t,e,i){const n=s(t,e||{});return null==i||i.appendChild(n),n}const n={lineWidth:1,labelSize:11,labelPreferLeft:!1,formatTimeCallback:t=>`${Math.floor(t/60)}:${`0${Math.floor(t)%60}`.slice(-2)}`};class o extends e{constructor(t){super(t||{}),this.unsubscribe=()=>{},this.onPointerMove=t=>{if(!this.wavesurfer)return;const e=this.wavesurfer.getWrapper().getBoundingClientRect(),{width:s}=e,i=t.clientX-e.left,n=Math.min(1,Math.max(0,i/s)),o=Math.min(s-this.options.lineWidth-1,i);this.wrapper.style.transform=`translateX(${o}px)`,this.wrapper.style.opacity=\"1\";const r=this.wavesurfer.getDuration()||0;this.label.textContent=this.options.formatTimeCallback(r*n);const a=this.label.offsetWidth,l=this.options.labelPreferLeft?o-a>0:o+a>s;this.label.style.transform=l?`translateX(-${a+this.options.lineWidth}px)`:\"\",this.emit(\"hover\",n)},this.onPointerLeave=()=>{this.wrapper.style.opacity=\"0\"},this.options=Object.assign({},n,t),this.wrapper=i(\"div\",{part:\"hover\"}),this.label=i(\"span\",{part:\"hover-label\"},this.wrapper)}static create(t){return new o(t)}addUnits(t){return`${t}${\"number\"==typeof t?\"px\":\"\"}`}onInit(){if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");const t=this.wavesurfer.options,e=this.options.lineColor||t.cursorColor||t.progressColor;Object.assign(this.wrapper.style,{position:\"absolute\",zIndex:10,left:0,top:0,height:\"100%\",pointerEvents:\"none\",borderLeft:`${this.addUnits(this.options.lineWidth)} solid ${e}`,opacity:\"0\",transition:\"opacity .1s ease-in\"}),Object.assign(this.label.style,{display:\"block\",backgroundColor:this.options.labelBackground,color:this.options.labelColor,fontSize:`${this.addUnits(this.options.labelSize)}`,transition:\"transform .1s ease-in\",padding:\"2px 3px\"});const s=this.wavesurfer.getWrapper();s.appendChild(this.wrapper),s.addEventListener(\"pointermove\",this.onPointerMove),s.addEventListener(\"pointerleave\",this.onPointerLeave),s.addEventListener(\"wheel\",this.onPointerMove),this.unsubscribe=()=>{s.removeEventListener(\"pointermove\",this.onPointerMove),s.removeEventListener(\"pointerleave\",this.onPointerLeave),s.removeEventListener(\"wheel\",this.onPointerMove)}}destroy(){super.destroy(),this.unsubscribe(),this.wrapper.remove()}}export{o as default};\n", "import d from \"./node_modules/wavesurfer.js/dist/plugins/hover.js\";export default d;"], "mappings": ";;;AAAA,cAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU;AAAA;AAAA,EAAG,GAAG,IAAE,IAAE,IAAE;AAAC,QAAG,KAAK,UAAU,OAAK,MAAK,UAAU,MAAG,IAAI,QAAK,KAAK,UAAU,IAAG,IAAI,KAAG,AAAM,MAAN,OAAQ,SAAO,GAAE,MAAK;AAAC,YAAM,KAAE,MAAI;AAAC,aAAK,GAAG,IAAE,KAAG,KAAK,GAAG,IAAE;AAAA;AAAI,aAAO,KAAK,GAAG,IAAE,KAAG;AAAA;AAAE,WAAM,MAAI,KAAK,GAAG,IAAE;AAAA;AAAA,EAAG,GAAG,IAAE,IAAE;AAAC,QAAI;AAAE,IAAQ,MAAE,KAAK,UAAU,SAAzB,QAA8B,AAAS,OAAT,UAAY,GAAE,OAAO;AAAA;AAAA,EAAG,KAAK,IAAE,IAAE;AAAC,WAAO,KAAK,GAAG,IAAE,IAAE,EAAC,MAAK;AAAA;AAAA,EAAK,QAAO;AAAC,SAAK,YAAU;AAAA;AAAA,EAAG,KAAK,OAAK,IAAE;AAAC,SAAK,UAAU,OAAI,KAAK,UAAU,IAAG,QAAS,QAAG,GAAE,GAAG;AAAA;AAAA;AAAM,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE;AAAC,aAAQ,KAAK,gBAAc,IAAG,KAAK,UAAQ;AAAA;AAAA,EAAE,SAAQ;AAAA;AAAA,EAAE,MAAM,IAAE;AAAC,SAAK,aAAW,IAAE,KAAK;AAAA;AAAA,EAAS,UAAS;AAAC,SAAK,KAAK,YAAW,KAAK,cAAc,QAAS,QAAG;AAAA;AAAA;AAAO,WAAW,IAAE,IAAE;AAAC,QAAM,KAAE,GAAE,QAAM,SAAS,gBAAgB,GAAE,OAAM,MAAG,SAAS,cAAc;AAAG,aAAS,CAAC,IAAE,OAAK,OAAO,QAAQ;AAAG,QAAG,AAAa,OAAb,cAAgB;AAAE,iBAAS,CAAC,IAAE,OAAK,OAAO,QAAQ;AAAG,sBAAa,OAAK,GAAE,YAAY,MAAG,AAAU,OAAO,MAAjB,WAAmB,GAAE,YAAY,SAAS,eAAe,OAAI,GAAE,YAAY,EAAE,IAAE;AAAA;AAAQ,MAAU,OAAV,UAAY,OAAO,OAAO,GAAE,OAAM,MAAG,AAAgB,OAAhB,gBAAkB,GAAE,cAAY,KAAE,GAAE,aAAa,IAAE,GAAE;AAAY,SAAO;AAAA;AAAE,WAAW,IAAE,IAAE,IAAE;AAAC,QAAM,KAAE,EAAE,IAAE,MAAG;AAAI,SAAO,AAAM,MAAN,QAAS,GAAE,YAAY,KAAG;AAAA;AAAE,IAAM,IAAE,EAAC,WAAU,GAAE,WAAU,IAAG,iBAAgB,OAAG,oBAAmB,QAAG,GAAG,KAAK,MAAM,KAAE,OAAO,IAAI,KAAK,MAAM,MAAG,KAAK,MAAM;AAAO,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE;AAAC,UAAM,MAAG,KAAI,KAAK,cAAY,MAAI;AAAA,OAAG,KAAK,gBAAc,QAAG;AAAC,UAAG,CAAC,KAAK;AAAW;AAAO,YAAM,KAAE,KAAK,WAAW,aAAa,yBAAwB,EAAC,OAAM,OAAG,IAAE,KAAE,GAAE,UAAQ,GAAE,MAAK,KAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAE,KAAE,MAAI,KAAE,KAAK,IAAI,KAAE,KAAK,QAAQ,YAAU,GAAE;AAAG,WAAK,QAAQ,MAAM,YAAU,cAAc,SAAO,KAAK,QAAQ,MAAM,UAAQ;AAAI,YAAM,IAAE,KAAK,WAAW,iBAAe;AAAE,WAAK,MAAM,cAAY,KAAK,QAAQ,mBAAmB,IAAE;AAAG,YAAM,IAAE,KAAK,MAAM,aAAY,IAAE,KAAK,QAAQ,kBAAgB,KAAE,IAAE,IAAE,KAAE,IAAE;AAAE,WAAK,MAAM,MAAM,YAAU,IAAE,eAAe,IAAE,KAAK,QAAQ,iBAAe,IAAG,KAAK,KAAK,SAAQ;AAAA,OAAI,KAAK,iBAAe,MAAI;AAAC,WAAK,QAAQ,MAAM,UAAQ;AAAA,OAAK,KAAK,UAAQ,OAAO,OAAO,IAAG,GAAE,KAAG,KAAK,UAAQ,EAAE,OAAM,EAAC,MAAK,YAAU,KAAK,QAAM,EAAE,QAAO,EAAC,MAAK,iBAAe,KAAK;AAAA;AAAA,SAAgB,OAAO,IAAE;AAAC,WAAO,IAAI,EAAE;AAAA;AAAA,EAAG,SAAS,IAAE;AAAC,WAAM,GAAG,KAAI,AAAU,OAAO,MAAjB,WAAmB,OAAK;AAAA;AAAA,EAAK,SAAQ;AAAC,QAAG,CAAC,KAAK;AAAW,YAAM,MAAM;AAAiC,UAAM,KAAE,KAAK,WAAW,SAAQ,KAAE,KAAK,QAAQ,aAAW,GAAE,eAAa,GAAE;AAAc,WAAO,OAAO,KAAK,QAAQ,OAAM,EAAC,UAAS,YAAW,QAAO,IAAG,MAAK,GAAE,KAAI,GAAE,QAAO,QAAO,eAAc,QAAO,YAAW,GAAG,KAAK,SAAS,KAAK,QAAQ,oBAAoB,MAAI,SAAQ,KAAI,YAAW,0BAAwB,OAAO,OAAO,KAAK,MAAM,OAAM,EAAC,SAAQ,SAAQ,iBAAgB,KAAK,QAAQ,iBAAgB,OAAM,KAAK,QAAQ,YAAW,UAAS,GAAG,KAAK,SAAS,KAAK,QAAQ,cAAa,YAAW,yBAAwB,SAAQ;AAAY,UAAM,KAAE,KAAK,WAAW;AAAa,OAAE,YAAY,KAAK,UAAS,GAAE,iBAAiB,eAAc,KAAK,gBAAe,GAAE,iBAAiB,gBAAe,KAAK,iBAAgB,GAAE,iBAAiB,SAAQ,KAAK,gBAAe,KAAK,cAAY,MAAI;AAAC,SAAE,oBAAoB,eAAc,KAAK,gBAAe,GAAE,oBAAoB,gBAAe,KAAK,iBAAgB,GAAE,oBAAoB,SAAQ,KAAK;AAAA;AAAA;AAAA,EAAgB,UAAS;AAAC,UAAM,WAAU,KAAK,eAAc,KAAK,QAAQ;AAAA;AAAA;;;ACAlnG,IAAO,8CAAQ;", "names": []}