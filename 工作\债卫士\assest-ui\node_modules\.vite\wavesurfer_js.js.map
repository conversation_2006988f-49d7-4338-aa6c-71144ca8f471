{"version": 3, "sources": ["../wavesurfer.js/dist/wavesurfer.esm.js", "dep:wavesurfer_js"], "sourcesContent": ["function t(t,e,i,s){return new(i||(i=Promise))((function(n,r){function o(t){try{h(s.next(t))}catch(t){r(t)}}function a(t){try{h(s.throw(t))}catch(t){r(t)}}function h(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}h((s=s.apply(t,e||[])).next())}))}\"function\"==typeof SuppressedError&&SuppressedError;class e{constructor(){this.listeners={}}on(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),null==i?void 0:i.once){const i=()=>{this.un(t,i),this.un(t,e)};return this.on(t,i),i}return()=>this.un(t,e)}un(t,e){var i;null===(i=this.listeners[t])||void 0===i||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach((t=>t(...e)))}}const i={decode:function(e,i){return t(this,void 0,void 0,(function*(){const t=new AudioContext({sampleRate:i});return t.decodeAudioData(e).finally((()=>t.close()))}))},createBuffer:function(t,e){return\"number\"==typeof t[0]&&(t=[t]),function(t){const e=t[0];if(e.some((t=>t>1||t<-1))){const i=e.length;let s=0;for(let t=0;t<i;t++){const i=Math.abs(e[t]);i>s&&(s=i)}for(const e of t)for(let t=0;t<i;t++)e[t]/=s}}(t),{duration:e,length:t[0].length,sampleRate:t[0].length/e,numberOfChannels:t.length,getChannelData:e=>null==t?void 0:t[e],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}};function s(t,e){const i=e.xmlns?document.createElementNS(e.xmlns,t):document.createElement(t);for(const[t,n]of Object.entries(e))if(\"children\"===t&&n)for(const[t,e]of Object.entries(n))e instanceof Node?i.appendChild(e):\"string\"==typeof e?i.appendChild(document.createTextNode(e)):i.appendChild(s(t,e));else\"style\"===t?Object.assign(i.style,n):\"textContent\"===t?i.textContent=n:i.setAttribute(t,n.toString());return i}function n(t,e,i){const n=s(t,e||{});return null==i||i.appendChild(n),n}var r=Object.freeze({__proto__:null,createElement:n,default:n});const o={fetchBlob:function(e,i,s){return t(this,void 0,void 0,(function*(){const n=yield fetch(e,s);if(n.status>=400)throw new Error(`Failed to fetch ${e}: ${n.status} (${n.statusText})`);return function(e,i){t(this,void 0,void 0,(function*(){if(!e.body||!e.headers)return;const s=e.body.getReader(),n=Number(e.headers.get(\"Content-Length\"))||0;let r=0;const o=e=>t(this,void 0,void 0,(function*(){r+=(null==e?void 0:e.length)||0;const t=Math.round(r/n*100);i(t)})),a=()=>t(this,void 0,void 0,(function*(){let t;try{t=yield s.read()}catch(t){return}t.done||(o(t.value),yield a())}));a()}))}(n.clone(),i),n.blob()}))}};class a extends e{constructor(t){super(),this.isExternalMedia=!1,t.media?(this.media=t.media,this.isExternalMedia=!0):this.media=document.createElement(\"audio\"),t.mediaControls&&(this.media.controls=!0),t.autoplay&&(this.media.autoplay=!0),null!=t.playbackRate&&this.onMediaEvent(\"canplay\",(()=>{null!=t.playbackRate&&(this.media.playbackRate=t.playbackRate)}),{once:!0})}onMediaEvent(t,e,i){return this.media.addEventListener(t,e,i),()=>this.media.removeEventListener(t,e,i)}getSrc(){return this.media.currentSrc||this.media.src||\"\"}revokeSrc(){const t=this.getSrc();t.startsWith(\"blob:\")&&URL.revokeObjectURL(t)}canPlayType(t){return\"\"!==this.media.canPlayType(t)}setSrc(t,e){const i=this.getSrc();if(t&&i===t)return;this.revokeSrc();const s=e instanceof Blob&&(this.canPlayType(e.type)||!t)?URL.createObjectURL(e):t;if(i&&this.media.removeAttribute(\"src\"),s||t)try{this.media.src=s}catch(e){this.media.src=t}}destroy(){this.isExternalMedia||(this.media.pause(),this.media.remove(),this.revokeSrc(),this.media.removeAttribute(\"src\"),this.media.load())}setMediaElement(t){this.media=t}play(){return t(this,void 0,void 0,(function*(){return this.media.play()}))}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(t){this.media.currentTime=Math.max(0,Math.min(t,this.getDuration()))}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(t){this.media.volume=t}getMuted(){return this.media.muted}setMuted(t){this.media.muted=t}getPlaybackRate(){return this.media.playbackRate}isSeeking(){return this.media.seeking}setPlaybackRate(t,e){null!=e&&(this.media.preservesPitch=e),this.media.playbackRate=t}getMediaElement(){return this.media}setSinkId(t){return this.media.setSinkId(t)}}class h extends e{constructor(t,e){super(),this.timeouts=[],this.isScrollable=!1,this.audioData=null,this.resizeObserver=null,this.lastContainerWidth=0,this.isDragging=!1,this.subscriptions=[],this.unsubscribeOnScroll=[],this.subscriptions=[],this.options=t;const i=this.parentFromOptionsContainer(t.container);this.parent=i;const[s,n]=this.initHtml();i.appendChild(s),this.container=s,this.scrollContainer=n.querySelector(\".scroll\"),this.wrapper=n.querySelector(\".wrapper\"),this.canvasWrapper=n.querySelector(\".canvases\"),this.progressWrapper=n.querySelector(\".progress\"),this.cursor=n.querySelector(\".cursor\"),e&&n.appendChild(e),this.initEvents()}parentFromOptionsContainer(t){let e;if(\"string\"==typeof t?e=document.querySelector(t):t instanceof HTMLElement&&(e=t),!e)throw new Error(\"Container not found\");return e}initEvents(){const t=t=>{const e=this.wrapper.getBoundingClientRect(),i=t.clientX-e.left,s=t.clientY-e.top;return[i/e.width,s/e.height]};if(this.wrapper.addEventListener(\"click\",(e=>{const[i,s]=t(e);this.emit(\"click\",i,s)})),this.wrapper.addEventListener(\"dblclick\",(e=>{const[i,s]=t(e);this.emit(\"dblclick\",i,s)})),!0!==this.options.dragToSeek&&\"object\"!=typeof this.options.dragToSeek||this.initDrag(),this.scrollContainer.addEventListener(\"scroll\",(()=>{const{scrollLeft:t,scrollWidth:e,clientWidth:i}=this.scrollContainer,s=t/e,n=(t+i)/e;this.emit(\"scroll\",s,n,t,t+i)})),\"function\"==typeof ResizeObserver){const t=this.createDelay(100);this.resizeObserver=new ResizeObserver((()=>{t().then((()=>this.onContainerResize())).catch((()=>{}))})),this.resizeObserver.observe(this.scrollContainer)}}onContainerResize(){const t=this.parent.clientWidth;t===this.lastContainerWidth&&\"auto\"!==this.options.height||(this.lastContainerWidth=t,this.reRender())}initDrag(){this.subscriptions.push(function(t,e,i,s,n=3,r=0,o=100){if(!t)return()=>{};const a=matchMedia(\"(pointer: coarse)\").matches;let h=()=>{};const l=l=>{if(l.button!==r)return;l.preventDefault(),l.stopPropagation();let d=l.clientX,c=l.clientY,u=!1;const p=Date.now(),m=s=>{if(s.preventDefault(),s.stopPropagation(),a&&Date.now()-p<o)return;const r=s.clientX,h=s.clientY,l=r-d,m=h-c;if(u||Math.abs(l)>n||Math.abs(m)>n){const s=t.getBoundingClientRect(),{left:n,top:o}=s;u||(null==i||i(d-n,c-o),u=!0),e(l,m,r-n,h-o),d=r,c=h}},f=e=>{if(u){const i=e.clientX,n=e.clientY,r=t.getBoundingClientRect(),{left:o,top:a}=r;null==s||s(i-o,n-a)}h()},g=t=>{t.relatedTarget&&t.relatedTarget!==document.documentElement||f(t)},v=t=>{u&&(t.stopPropagation(),t.preventDefault())},b=t=>{u&&t.preventDefault()};document.addEventListener(\"pointermove\",m),document.addEventListener(\"pointerup\",f),document.addEventListener(\"pointerout\",g),document.addEventListener(\"pointercancel\",g),document.addEventListener(\"touchmove\",b,{passive:!1}),document.addEventListener(\"click\",v,{capture:!0}),h=()=>{document.removeEventListener(\"pointermove\",m),document.removeEventListener(\"pointerup\",f),document.removeEventListener(\"pointerout\",g),document.removeEventListener(\"pointercancel\",g),document.removeEventListener(\"touchmove\",b),setTimeout((()=>{document.removeEventListener(\"click\",v,{capture:!0})}),10)}};return t.addEventListener(\"pointerdown\",l),()=>{h(),t.removeEventListener(\"pointerdown\",l)}}(this.wrapper,((t,e,i)=>{this.emit(\"drag\",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))}),(t=>{this.isDragging=!0,this.emit(\"dragstart\",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))}),(t=>{this.isDragging=!1,this.emit(\"dragend\",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))})))}getHeight(t,e){var i;const s=(null===(i=this.audioData)||void 0===i?void 0:i.numberOfChannels)||1;if(null==t)return 128;if(!isNaN(Number(t)))return Number(t);if(\"auto\"===t){const t=this.parent.clientHeight||128;return(null==e?void 0:e.every((t=>!t.overlay)))?t/s:t}return 128}initHtml(){const t=document.createElement(\"div\"),e=t.attachShadow({mode:\"open\"}),i=this.options.cspNonce&&\"string\"==typeof this.options.cspNonce?this.options.cspNonce.replace(/\"/g,\"\"):\"\";return e.innerHTML=`\\n      <style${i?` nonce=\"${i}\"`:\"\"}>\\n        :host {\\n          user-select: none;\\n          min-width: 1px;\\n        }\\n        :host audio {\\n          display: block;\\n          width: 100%;\\n        }\\n        :host .scroll {\\n          overflow-x: auto;\\n          overflow-y: hidden;\\n          width: 100%;\\n          position: relative;\\n        }\\n        :host .noScrollbar {\\n          scrollbar-color: transparent;\\n          scrollbar-width: none;\\n        }\\n        :host .noScrollbar::-webkit-scrollbar {\\n          display: none;\\n          -webkit-appearance: none;\\n        }\\n        :host .wrapper {\\n          position: relative;\\n          overflow: visible;\\n          z-index: 2;\\n        }\\n        :host .canvases {\\n          min-height: ${this.getHeight(this.options.height,this.options.splitChannels)}px;\\n        }\\n        :host .canvases > div {\\n          position: relative;\\n        }\\n        :host canvas {\\n          display: block;\\n          position: absolute;\\n          top: 0;\\n          image-rendering: pixelated;\\n        }\\n        :host .progress {\\n          pointer-events: none;\\n          position: absolute;\\n          z-index: 2;\\n          top: 0;\\n          left: 0;\\n          width: 0;\\n          height: 100%;\\n          overflow: hidden;\\n        }\\n        :host .progress > div {\\n          position: relative;\\n        }\\n        :host .cursor {\\n          pointer-events: none;\\n          position: absolute;\\n          z-index: 5;\\n          top: 0;\\n          left: 0;\\n          height: 100%;\\n          border-radius: 2px;\\n        }\\n      </style>\\n\\n      <div class=\"scroll\" part=\"scroll\">\\n        <div class=\"wrapper\" part=\"wrapper\">\\n          <div class=\"canvases\" part=\"canvases\"></div>\\n          <div class=\"progress\" part=\"progress\"></div>\\n          <div class=\"cursor\" part=\"cursor\"></div>\\n        </div>\\n      </div>\\n    `,[t,e]}setOptions(t){if(this.options.container!==t.container){const e=this.parentFromOptionsContainer(t.container);e.appendChild(this.container),this.parent=e}!0!==t.dragToSeek&&\"object\"!=typeof this.options.dragToSeek||this.initDrag(),this.options=t,this.reRender()}getWrapper(){return this.wrapper}getWidth(){return this.scrollContainer.clientWidth}getScroll(){return this.scrollContainer.scrollLeft}setScroll(t){this.scrollContainer.scrollLeft=t}setScrollPercentage(t){const{scrollWidth:e}=this.scrollContainer,i=e*t;this.setScroll(i)}destroy(){var t,e;this.subscriptions.forEach((t=>t())),this.container.remove(),null===(t=this.resizeObserver)||void 0===t||t.disconnect(),null===(e=this.unsubscribeOnScroll)||void 0===e||e.forEach((t=>t())),this.unsubscribeOnScroll=[]}createDelay(t=10){let e,i;const s=()=>{e&&clearTimeout(e),i&&i()};return this.timeouts.push(s),()=>new Promise(((n,r)=>{s(),i=r,e=setTimeout((()=>{e=void 0,i=void 0,n()}),t)}))}convertColorValues(t){if(!Array.isArray(t))return t||\"\";if(t.length<2)return t[0]||\"\";const e=document.createElement(\"canvas\"),i=e.getContext(\"2d\"),s=e.height*(window.devicePixelRatio||1),n=i.createLinearGradient(0,0,0,s),r=1/(t.length-1);return t.forEach(((t,e)=>{const i=e*r;n.addColorStop(i,t)})),n}getPixelRatio(){return Math.max(1,window.devicePixelRatio||1)}renderBarWaveform(t,e,i,s){const n=t[0],r=t[1]||t[0],o=n.length,{width:a,height:h}=i.canvas,l=h/2,d=this.getPixelRatio(),c=e.barWidth?e.barWidth*d:1,u=e.barGap?e.barGap*d:e.barWidth?c/2:0,p=e.barRadius||0,m=a/(c+u)/o,f=p&&\"roundRect\"in i?\"roundRect\":\"rect\";i.beginPath();let g=0,v=0,b=0;for(let t=0;t<=o;t++){const o=Math.round(t*m);if(o>g){const t=Math.round(v*l*s),n=t+Math.round(b*l*s)||1;let r=l-t;\"top\"===e.barAlign?r=0:\"bottom\"===e.barAlign&&(r=h-n),i[f](g*(c+u),r,c,n,p),g=o,v=0,b=0}const a=Math.abs(n[t]||0),d=Math.abs(r[t]||0);a>v&&(v=a),d>b&&(b=d)}i.fill(),i.closePath()}renderLineWaveform(t,e,i,s){const n=e=>{const n=t[e]||t[0],r=n.length,{height:o}=i.canvas,a=o/2,h=i.canvas.width/r;i.moveTo(0,a);let l=0,d=0;for(let t=0;t<=r;t++){const r=Math.round(t*h);if(r>l){const t=a+(Math.round(d*a*s)||1)*(0===e?-1:1);i.lineTo(l,t),l=r,d=0}const o=Math.abs(n[t]||0);o>d&&(d=o)}i.lineTo(l,a)};i.beginPath(),n(0),n(1),i.fill(),i.closePath()}renderWaveform(t,e,i){if(i.fillStyle=this.convertColorValues(e.waveColor),e.renderFunction)return void e.renderFunction(t,i);let s=e.barHeight||1;if(e.normalize){const e=Array.from(t[0]).reduce(((t,e)=>Math.max(t,Math.abs(e))),0);s=e?1/e:1}e.barWidth||e.barGap||e.barAlign?this.renderBarWaveform(t,e,i,s):this.renderLineWaveform(t,e,i,s)}renderSingleCanvas(t,e,i,s,n,r,o){const a=this.getPixelRatio(),h=document.createElement(\"canvas\");h.width=Math.round(i*a),h.height=Math.round(s*a),h.style.width=`${i}px`,h.style.height=`${s}px`,h.style.left=`${Math.round(n)}px`,r.appendChild(h);const l=h.getContext(\"2d\");if(this.renderWaveform(t,e,l),h.width>0&&h.height>0){const t=h.cloneNode(),i=t.getContext(\"2d\");i.drawImage(h,0,0),i.globalCompositeOperation=\"source-in\",i.fillStyle=this.convertColorValues(e.progressColor),i.fillRect(0,0,h.width,h.height),o.appendChild(t)}}renderMultiCanvas(t,e,i,s,n,r){const o=this.getPixelRatio(),{clientWidth:a}=this.scrollContainer,l=i/o;let d=Math.min(h.MAX_CANVAS_WIDTH,a,l),c={};if(e.barWidth||e.barGap){const t=e.barWidth||.5,i=t+(e.barGap||t/2);d%i!=0&&(d=Math.floor(d/i)*i)}if(0===d)return;const u=i=>{if(i<0||i>=p)return;if(c[i])return;c[i]=!0;const o=i*d;let a=Math.min(l-o,d);if(e.barWidth||e.barGap){const t=e.barWidth||.5,i=t+(e.barGap||t/2);a=Math.floor(a/i)*i}if(a<=0)return;const h=t.map((t=>{const e=Math.floor(o/l*t.length),i=Math.floor((o+a)/l*t.length);return t.slice(e,i)}));this.renderSingleCanvas(h,e,a,s,o,n,r)},p=Math.ceil(l/d);if(!this.isScrollable){for(let t=0;t<p;t++)u(t);return}const m=this.scrollContainer.scrollLeft/l,f=Math.floor(m*p);if(u(f-1),u(f),u(f+1),p>1){const t=this.on(\"scroll\",(()=>{const{scrollLeft:t}=this.scrollContainer,e=Math.floor(t/l*p);Object.keys(c).length>h.MAX_NODES&&(n.innerHTML=\"\",r.innerHTML=\"\",c={}),u(e-1),u(e),u(e+1)}));this.unsubscribeOnScroll.push(t)}}renderChannel(t,e,i,s){var{overlay:n}=e,r=function(t,e){var i={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(i[s]=t[s]);if(null!=t&&\"function\"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(t);n<s.length;n++)e.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(t,s[n])&&(i[s[n]]=t[s[n]])}return i}(e,[\"overlay\"]);const o=document.createElement(\"div\"),a=this.getHeight(r.height,r.splitChannels);o.style.height=`${a}px`,n&&s>0&&(o.style.marginTop=`-${a}px`),this.canvasWrapper.style.minHeight=`${a}px`,this.canvasWrapper.appendChild(o);const h=o.cloneNode();this.progressWrapper.appendChild(h),this.renderMultiCanvas(t,r,i,a,o,h)}render(e){return t(this,void 0,void 0,(function*(){var t;this.timeouts.forEach((t=>t())),this.timeouts=[],this.canvasWrapper.innerHTML=\"\",this.progressWrapper.innerHTML=\"\",null!=this.options.width&&(this.scrollContainer.style.width=\"number\"==typeof this.options.width?`${this.options.width}px`:this.options.width);const i=this.getPixelRatio(),s=this.scrollContainer.clientWidth,n=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrollable=n>s;const r=this.options.fillParent&&!this.isScrollable,o=(r?s:n)*i;if(this.wrapper.style.width=r?\"100%\":`${n}px`,this.scrollContainer.style.overflowX=this.isScrollable?\"auto\":\"hidden\",this.scrollContainer.classList.toggle(\"noScrollbar\",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.audioData=e,this.emit(\"render\"),this.options.splitChannels)for(let i=0;i<e.numberOfChannels;i++){const s=Object.assign(Object.assign({},this.options),null===(t=this.options.splitChannels)||void 0===t?void 0:t[i]);this.renderChannel([e.getChannelData(i)],s,o,i)}else{const t=[e.getChannelData(0)];e.numberOfChannels>1&&t.push(e.getChannelData(1)),this.renderChannel(t,this.options,o,0)}Promise.resolve().then((()=>this.emit(\"rendered\")))}))}reRender(){if(this.unsubscribeOnScroll.forEach((t=>t())),this.unsubscribeOnScroll=[],!this.audioData)return;const{scrollWidth:t}=this.scrollContainer,{right:e}=this.progressWrapper.getBoundingClientRect();if(this.render(this.audioData),this.isScrollable&&t!==this.scrollContainer.scrollWidth){const{right:t}=this.progressWrapper.getBoundingClientRect();let i=t-e;i*=2,i=i<0?Math.floor(i):Math.ceil(i),i/=2,this.scrollContainer.scrollLeft+=i}}zoom(t){this.options.minPxPerSec=t,this.reRender()}scrollIntoView(t,e=!1){const{scrollLeft:i,scrollWidth:s,clientWidth:n}=this.scrollContainer,r=t*s,o=i,a=i+n,h=n/2;if(this.isDragging){const t=30;r+t>a?this.scrollContainer.scrollLeft+=t:r-t<o&&(this.scrollContainer.scrollLeft-=t)}else{(r<o||r>a)&&(this.scrollContainer.scrollLeft=r-(this.options.autoCenter?h:0));const t=r-i-h;e&&this.options.autoCenter&&t>0&&(this.scrollContainer.scrollLeft+=Math.min(t,10))}{const t=this.scrollContainer.scrollLeft,e=t/s,i=(t+n)/s;this.emit(\"scroll\",e,i,t,t+n)}}renderProgress(t,e){if(isNaN(t))return;const i=100*t;this.canvasWrapper.style.clipPath=`polygon(${i}% 0%, 100% 0%, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.transform=`translateX(-${100===Math.round(i)?this.options.cursorWidth:0}px)`,this.isScrollable&&this.options.autoScroll&&this.scrollIntoView(t,e)}exportImage(e,i,s){return t(this,void 0,void 0,(function*(){const t=this.canvasWrapper.querySelectorAll(\"canvas\");if(!t.length)throw new Error(\"No waveform data\");if(\"dataURL\"===s){const s=Array.from(t).map((t=>t.toDataURL(e,i)));return Promise.resolve(s)}return Promise.all(Array.from(t).map((t=>new Promise(((s,n)=>{t.toBlob((t=>{t?s(t):n(new Error(\"Could not export image\"))}),e,i)})))))}))}}h.MAX_CANVAS_WIDTH=8e3,h.MAX_NODES=10;class l extends e{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on(\"tick\",(()=>{requestAnimationFrame((()=>{this.emit(\"tick\")}))})),this.emit(\"tick\")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}class d extends e{constructor(t=new AudioContext){super(),this.bufferNode=null,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this._playbackRate=1,this._duration=void 0,this.buffer=null,this.currentSrc=\"\",this.paused=!0,this.crossOrigin=null,this.seeking=!1,this.autoplay=!1,this.addEventListener=this.on,this.removeEventListener=this.un,this.audioContext=t,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return t(this,void 0,void 0,(function*(){}))}get src(){return this.currentSrc}set src(t){if(this.currentSrc=t,this._duration=void 0,!t)return this.buffer=null,void this.emit(\"emptied\");fetch(t).then((e=>{if(e.status>=400)throw new Error(`Failed to fetch ${t}: ${e.status} (${e.statusText})`);return e.arrayBuffer()})).then((e=>this.currentSrc!==t?null:this.audioContext.decodeAudioData(e))).then((e=>{this.currentSrc===t&&(this.buffer=e,this.emit(\"loadedmetadata\"),this.emit(\"canplay\"),this.autoplay&&this.play())}))}_play(){var t;if(!this.paused)return;this.paused=!1,null===(t=this.bufferNode)||void 0===t||t.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.buffer&&(this.bufferNode.buffer=this.buffer),this.bufferNode.playbackRate.value=this._playbackRate,this.bufferNode.connect(this.gainNode);let e=this.playedDuration*this._playbackRate;(e>=this.duration||e<0)&&(e=0,this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,e),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit(\"ended\"))}}_pause(){var t;this.paused=!0,null===(t=this.bufferNode)||void 0===t||t.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime}play(){return t(this,void 0,void 0,(function*(){this.paused&&(this._play(),this.emit(\"play\"))}))}pause(){this.paused||(this._pause(),this.emit(\"pause\"))}stopAt(t){const e=t-this.currentTime,i=this.bufferNode;null==i||i.stop(this.audioContext.currentTime+e),null==i||i.addEventListener(\"ended\",(()=>{i===this.bufferNode&&(this.bufferNode=null,this.pause())}),{once:!0})}setSinkId(e){return t(this,void 0,void 0,(function*(){return this.audioContext.setSinkId(e)}))}get playbackRate(){return this._playbackRate}set playbackRate(t){this._playbackRate=t,this.bufferNode&&(this.bufferNode.playbackRate.value=t)}get currentTime(){return(this.paused?this.playedDuration:this.playedDuration+(this.audioContext.currentTime-this.playStartTime))*this._playbackRate}set currentTime(t){const e=!this.paused;e&&this._pause(),this.playedDuration=t/this._playbackRate,e&&this._play(),this.emit(\"seeking\"),this.emit(\"timeupdate\")}get duration(){var t,e;return null!==(t=this._duration)&&void 0!==t?t:(null===(e=this.buffer)||void 0===e?void 0:e.duration)||0}set duration(t){this._duration=t}get volume(){return this.gainNode.gain.value}set volume(t){this.gainNode.gain.value=t,this.emit(\"volumechange\")}get muted(){return this._muted}set muted(t){this._muted!==t&&(this._muted=t,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}canPlayType(t){return/^(audio|video)\\//.test(t)}getGainNode(){return this.gainNode}getChannelData(){const t=[];if(!this.buffer)return t;const e=this.buffer.numberOfChannels;for(let i=0;i<e;i++)t.push(this.buffer.getChannelData(i));return t}}const c={waveColor:\"#999\",progressColor:\"#555\",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class u extends a{static create(t){return new u(t)}constructor(t){const e=t.media||(\"WebAudio\"===t.backend?new d:void 0);super({media:e,mediaControls:t.mediaControls,autoplay:t.autoplay,playbackRate:t.audioRate}),this.plugins=[],this.decodedData=null,this.stopAtPosition=null,this.subscriptions=[],this.mediaSubscriptions=[],this.abortController=null,this.options=Object.assign({},c,t),this.timer=new l;const i=e?void 0:this.getMediaElement();this.renderer=new h(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const s=this.options.url||this.getSrc()||\"\";Promise.resolve().then((()=>{this.emit(\"init\");const{peaks:t,duration:e}=this.options;(s||t&&e)&&this.load(s,t,e).catch((()=>null))}))}updateProgress(t=this.getCurrentTime()){return this.renderer.renderProgress(t/this.getDuration(),this.isPlaying()),t}initTimerEvents(){this.subscriptions.push(this.timer.on(\"tick\",(()=>{if(!this.isSeeking()){const t=this.updateProgress();this.emit(\"timeupdate\",t),this.emit(\"audioprocess\",t),null!=this.stopAtPosition&&this.isPlaying()&&t>=this.stopAtPosition&&this.pause()}})))}initPlayerEvents(){this.isPlaying()&&(this.emit(\"play\"),this.timer.start()),this.mediaSubscriptions.push(this.onMediaEvent(\"timeupdate\",(()=>{const t=this.updateProgress();this.emit(\"timeupdate\",t)})),this.onMediaEvent(\"play\",(()=>{this.emit(\"play\"),this.timer.start()})),this.onMediaEvent(\"pause\",(()=>{this.emit(\"pause\"),this.timer.stop(),this.stopAtPosition=null})),this.onMediaEvent(\"emptied\",(()=>{this.timer.stop(),this.stopAtPosition=null})),this.onMediaEvent(\"ended\",(()=>{this.emit(\"timeupdate\",this.getDuration()),this.emit(\"finish\"),this.stopAtPosition=null})),this.onMediaEvent(\"seeking\",(()=>{this.emit(\"seeking\",this.getCurrentTime())})),this.onMediaEvent(\"error\",(()=>{var t;this.emit(\"error\",null!==(t=this.getMediaElement().error)&&void 0!==t?t:new Error(\"Media error\")),this.stopAtPosition=null})))}initRendererEvents(){this.subscriptions.push(this.renderer.on(\"click\",((t,e)=>{this.options.interact&&(this.seekTo(t),this.emit(\"interaction\",t*this.getDuration()),this.emit(\"click\",t,e))})),this.renderer.on(\"dblclick\",((t,e)=>{this.emit(\"dblclick\",t,e)})),this.renderer.on(\"scroll\",((t,e,i,s)=>{const n=this.getDuration();this.emit(\"scroll\",t*n,e*n,i,s)})),this.renderer.on(\"render\",(()=>{this.emit(\"redraw\")})),this.renderer.on(\"rendered\",(()=>{this.emit(\"redrawcomplete\")})),this.renderer.on(\"dragstart\",(t=>{this.emit(\"dragstart\",t)})),this.renderer.on(\"dragend\",(t=>{this.emit(\"dragend\",t)})));{let t;this.subscriptions.push(this.renderer.on(\"drag\",(e=>{if(!this.options.interact)return;let i;this.renderer.renderProgress(e),clearTimeout(t),this.isPlaying()?i=0:!0===this.options.dragToSeek?i=200:\"object\"==typeof this.options.dragToSeek&&void 0!==this.options.dragToSeek&&(i=this.options.dragToSeek.debounceTime),t=setTimeout((()=>{this.seekTo(e)}),i),this.emit(\"interaction\",e*this.getDuration()),this.emit(\"drag\",e)})))}}initPlugins(){var t;(null===(t=this.options.plugins)||void 0===t?void 0:t.length)&&this.options.plugins.forEach((t=>{this.registerPlugin(t)}))}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach((t=>t())),this.mediaSubscriptions=[]}setOptions(t){this.options=Object.assign({},this.options,t),t.duration&&!t.peaks&&(this.decodedData=i.createBuffer(this.exportPeaks(),t.duration)),t.peaks&&t.duration&&(this.decodedData=i.createBuffer(t.peaks,t.duration)),this.renderer.setOptions(this.options),t.audioRate&&this.setPlaybackRate(t.audioRate),null!=t.mediaControls&&(this.getMediaElement().controls=t.mediaControls)}registerPlugin(t){t._init(this),this.plugins.push(t);const e=t.once(\"destroy\",(()=>{this.plugins=this.plugins.filter((e=>e!==t)),this.subscriptions=this.subscriptions.filter((t=>t!==e))}));return this.subscriptions.push(e),t}getWrapper(){return this.renderer.getWrapper()}getWidth(){return this.renderer.getWidth()}getScroll(){return this.renderer.getScroll()}setScroll(t){return this.renderer.setScroll(t)}setScrollTime(t){const e=t/this.getDuration();this.renderer.setScrollPercentage(e)}getActivePlugins(){return this.plugins}loadAudio(e,s,n,r){return t(this,void 0,void 0,(function*(){var t;if(this.emit(\"load\",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,this.stopAtPosition=null,!s&&!n){const i=this.options.fetchParams||{};window.AbortController&&!i.signal&&(this.abortController=new AbortController,i.signal=null===(t=this.abortController)||void 0===t?void 0:t.signal);const n=t=>this.emit(\"loading\",t);s=yield o.fetchBlob(e,n,i);const r=this.options.blobMimeType;r&&(s=new Blob([s],{type:r}))}this.setSrc(e,s);const a=yield new Promise((t=>{const e=r||this.getDuration();e?t(e):this.mediaSubscriptions.push(this.onMediaEvent(\"loadedmetadata\",(()=>t(this.getDuration())),{once:!0}))}));if(!e&&!s){const t=this.getMediaElement();t instanceof d&&(t.duration=a)}if(n)this.decodedData=i.createBuffer(n,a||0);else if(s){const t=yield s.arrayBuffer();this.decodedData=yield i.decode(t,this.options.sampleRate)}this.decodedData&&(this.emit(\"decode\",this.getDuration()),this.renderer.render(this.decodedData)),this.emit(\"ready\",this.getDuration())}))}load(e,i,s){return t(this,void 0,void 0,(function*(){try{return yield this.loadAudio(e,void 0,i,s)}catch(t){throw this.emit(\"error\",t),t}}))}loadBlob(e,i,s){return t(this,void 0,void 0,(function*(){try{return yield this.loadAudio(\"\",e,i,s)}catch(t){throw this.emit(\"error\",t),t}}))}zoom(t){if(!this.decodedData)throw new Error(\"No audio loaded\");this.renderer.zoom(t),this.emit(\"zoom\",t)}getDecodedData(){return this.decodedData}exportPeaks({channels:t=2,maxLength:e=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error(\"The audio has not been decoded yet\");const s=Math.min(t,this.decodedData.numberOfChannels),n=[];for(let t=0;t<s;t++){const s=this.decodedData.getChannelData(t),r=[],o=s.length/e;for(let t=0;t<e;t++){const e=s.slice(Math.floor(t*o),Math.ceil((t+1)*o));let n=0;for(let t=0;t<e.length;t++){const i=e[t];Math.abs(i)>Math.abs(n)&&(n=i)}r.push(Math.round(n*i)/i)}n.push(r)}return n}getDuration(){let t=super.getDuration()||0;return 0!==t&&t!==1/0||!this.decodedData||(t=this.decodedData.duration),t}toggleInteraction(t){this.options.interact=t}setTime(t){this.stopAtPosition=null,super.setTime(t),this.updateProgress(t),this.emit(\"timeupdate\",t)}seekTo(t){const e=this.getDuration()*t;this.setTime(e)}play(e,i){const s=Object.create(null,{play:{get:()=>super.play}});return t(this,void 0,void 0,(function*(){null!=e&&this.setTime(e);const t=yield s.play.call(this);return null!=i&&(this.media instanceof d?this.media.stopAt(i):this.stopAtPosition=i),t}))}playPause(){return t(this,void 0,void 0,(function*(){return this.isPlaying()?this.pause():this.play()}))}stop(){this.pause(),this.setTime(0)}skip(t){this.setTime(this.getCurrentTime()+t)}empty(){this.load(\"\",[[0]],.001)}setMediaElement(t){this.unsubscribePlayerEvents(),super.setMediaElement(t),this.initPlayerEvents()}exportImage(){return t(this,arguments,void 0,(function*(t=\"image/png\",e=1,i=\"dataURL\"){return this.renderer.exportImage(t,e,i)}))}destroy(){var t;this.emit(\"destroy\"),null===(t=this.abortController)||void 0===t||t.abort(),this.plugins.forEach((t=>t.destroy())),this.subscriptions.forEach((t=>t())),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}u.BasePlugin=class extends e{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}_init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t()))}},u.dom=r;export{u as default};\n", "import d from \"./node_modules/wavesurfer.js/dist/wavesurfer.esm.js\";export default d;"], "mappings": ";;;AAAA,WAAW,IAAE,IAAE,IAAE,IAAE;AAAC,SAAO,IAAI,OAAI,MAAE,UAAW,SAAS,IAAE,IAAE;AAAC,gBAAW,IAAE;AAAC,UAAG;AAAC,WAAE,GAAE,KAAK;AAAA,eAAU,IAAN;AAAS,WAAE;AAAA;AAAA;AAAI,gBAAW,IAAE;AAAC,UAAG;AAAC,WAAE,GAAE,MAAM;AAAA,eAAU,IAAN;AAAS,WAAE;AAAA;AAAA;AAAI,gBAAW,IAAE;AAAC,UAAI;AAAE,SAAE,OAAK,GAAE,GAAE,SAAQ,MAAE,GAAE,OAAM,cAAa,KAAE,KAAE,IAAI,GAAG,SAAS,IAAE;AAAC,WAAE;AAAA,UAAO,KAAK,IAAE;AAAA;AAAG,OAAG,MAAE,GAAE,MAAM,IAAE,MAAG,KAAK;AAAA;AAAA;AAA+D,cAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU;AAAA;AAAA,EAAG,GAAG,IAAE,IAAE,IAAE;AAAC,QAAG,KAAK,UAAU,OAAK,MAAK,UAAU,MAAG,IAAI,QAAK,KAAK,UAAU,IAAG,IAAI,KAAG,AAAM,MAAN,OAAQ,SAAO,GAAE,MAAK;AAAC,YAAM,KAAE,MAAI;AAAC,aAAK,GAAG,IAAE,KAAG,KAAK,GAAG,IAAE;AAAA;AAAI,aAAO,KAAK,GAAG,IAAE,KAAG;AAAA;AAAE,WAAM,MAAI,KAAK,GAAG,IAAE;AAAA;AAAA,EAAG,GAAG,IAAE,IAAE;AAAC,QAAI;AAAE,IAAQ,MAAE,KAAK,UAAU,SAAzB,QAA8B,AAAS,OAAT,UAAY,GAAE,OAAO;AAAA;AAAA,EAAG,KAAK,IAAE,IAAE;AAAC,WAAO,KAAK,GAAG,IAAE,IAAE,EAAC,MAAK;AAAA;AAAA,EAAK,QAAO;AAAC,SAAK,YAAU;AAAA;AAAA,EAAG,KAAK,OAAK,IAAE;AAAC,SAAK,UAAU,OAAI,KAAK,UAAU,IAAG,QAAS,QAAG,GAAE,GAAG;AAAA;AAAA;AAAM,IAAM,IAAE,EAAC,QAAO,SAAS,IAAE,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAM,KAAE,IAAI,aAAa,EAAC,YAAW;AAAI,WAAO,GAAE,gBAAgB,IAAG,QAAS,MAAI,GAAE;AAAA;AAAA,GAAc,cAAa,SAAS,IAAE,IAAE;AAAC,SAAM,AAAU,OAAO,GAAE,MAAnB,YAAwB,MAAE,CAAC,MAAI,SAAS,IAAE;AAAC,UAAM,KAAE,GAAE;AAAG,QAAG,GAAE,KAAM,QAAG,KAAE,KAAG,KAAE,KAAK;AAAC,YAAM,KAAE,GAAE;AAAO,UAAI,KAAE;AAAE,eAAQ,KAAE,GAAE,KAAE,IAAE,MAAI;AAAC,cAAM,KAAE,KAAK,IAAI,GAAE;AAAI,aAAE,MAAI,MAAE;AAAA;AAAG,iBAAU,MAAK;AAAE,iBAAQ,KAAE,GAAE,KAAE,IAAE;AAAI,aAAE,OAAI;AAAA;AAAA,IAAI,KAAG,EAAC,UAAS,IAAE,QAAO,GAAE,GAAG,QAAO,YAAW,GAAE,GAAG,SAAO,IAAE,kBAAiB,GAAE,QAAO,gBAAe,QAAG,AAAM,MAAN,OAAQ,SAAO,GAAE,KAAG,iBAAgB,YAAY,UAAU,iBAAgB,eAAc,YAAY,UAAU;AAAA;AAAiB,WAAW,IAAE,IAAE;AAAC,QAAM,KAAE,GAAE,QAAM,SAAS,gBAAgB,GAAE,OAAM,MAAG,SAAS,cAAc;AAAG,aAAS,CAAC,IAAE,OAAK,OAAO,QAAQ;AAAG,QAAG,AAAa,OAAb,cAAgB;AAAE,iBAAS,CAAC,IAAE,OAAK,OAAO,QAAQ;AAAG,sBAAa,OAAK,GAAE,YAAY,MAAG,AAAU,OAAO,MAAjB,WAAmB,GAAE,YAAY,SAAS,eAAe,OAAI,GAAE,YAAY,EAAE,IAAE;AAAA;AAAQ,MAAU,OAAV,UAAY,OAAO,OAAO,GAAE,OAAM,MAAG,AAAgB,OAAhB,gBAAkB,GAAE,cAAY,KAAE,GAAE,aAAa,IAAE,GAAE;AAAY,SAAO;AAAA;AAAE,WAAW,IAAE,IAAE,IAAE;AAAC,QAAM,KAAE,EAAE,IAAE,MAAG;AAAI,SAAO,AAAM,MAAN,QAAS,GAAE,YAAY,KAAG;AAAA;AAAE,IAAI,IAAE,OAAO,OAAO,EAAC,WAAU,MAAK,eAAc,GAAE,SAAQ;AAAI,IAAM,IAAE,EAAC,WAAU,SAAS,IAAE,IAAE,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAM,KAAE,MAAM,MAAM,IAAE;AAAG,QAAG,GAAE,UAAQ;AAAI,YAAM,IAAI,MAAM,mBAAmB,OAAM,GAAE,WAAW,GAAE;AAAe,WAAO,SAAS,IAAE,IAAE;AAAC,QAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,YAAG,CAAC,GAAE,QAAM,CAAC,GAAE;AAAQ;AAAO,cAAM,KAAE,GAAE,KAAK,aAAY,KAAE,OAAO,GAAE,QAAQ,IAAI,sBAAoB;AAAE,YAAI,KAAE;AAAE,cAAM,KAAE,QAAG,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,gBAAI,CAAM,MAAN,OAAQ,SAAO,GAAE,WAAS;AAAE,gBAAM,KAAE,KAAK,MAAM,KAAE,KAAE;AAAK,aAAE;AAAA,YAAM,KAAE,MAAI,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,cAAI;AAAE,cAAG;AAAC,iBAAE,MAAM,GAAE;AAAA,mBAAa,IAAN;AAAS;AAAA;AAAO,aAAE,QAAO,IAAE,GAAE,QAAO,MAAM;AAAA;AAAQ;AAAA;AAAA,MAAQ,GAAE,SAAQ,KAAG,GAAE;AAAA;AAAA;AAAY,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE;AAAC,aAAQ,KAAK,kBAAgB,OAAG,GAAE,QAAO,MAAK,QAAM,GAAE,OAAM,KAAK,kBAAgB,QAAI,KAAK,QAAM,SAAS,cAAc,UAAS,GAAE,iBAAgB,MAAK,MAAM,WAAS,OAAI,GAAE,YAAW,MAAK,MAAM,WAAS,OAAI,AAAM,GAAE,gBAAR,QAAsB,KAAK,aAAa,WAAW,MAAI;AAAC,MAAM,GAAE,gBAAR,QAAuB,MAAK,MAAM,eAAa,GAAE;AAAA,OAAgB,EAAC,MAAK;AAAA;AAAA,EAAK,aAAa,IAAE,IAAE,IAAE;AAAC,WAAO,KAAK,MAAM,iBAAiB,IAAE,IAAE,KAAG,MAAI,KAAK,MAAM,oBAAoB,IAAE,IAAE;AAAA;AAAA,EAAG,SAAQ;AAAC,WAAO,KAAK,MAAM,cAAY,KAAK,MAAM,OAAK;AAAA;AAAA,EAAG,YAAW;AAAC,UAAM,KAAE,KAAK;AAAS,OAAE,WAAW,YAAU,IAAI,gBAAgB;AAAA;AAAA,EAAG,YAAY,IAAE;AAAC,WAAM,AAAK,KAAK,MAAM,YAAY,QAA5B;AAAA;AAAA,EAA+B,OAAO,IAAE,IAAE;AAAC,UAAM,KAAE,KAAK;AAAS,QAAG,MAAG,OAAI;AAAE;AAAO,SAAK;AAAY,UAAM,KAAE,cAAa,QAAO,MAAK,YAAY,GAAE,SAAO,CAAC,MAAG,IAAI,gBAAgB,MAAG;AAAE,QAAG,MAAG,KAAK,MAAM,gBAAgB,QAAO,MAAG;AAAE,UAAG;AAAC,aAAK,MAAM,MAAI;AAAA,eAAQ,IAAN;AAAS,aAAK,MAAM,MAAI;AAAA;AAAA;AAAA,EAAG,UAAS;AAAC,SAAK,mBAAkB,MAAK,MAAM,SAAQ,KAAK,MAAM,UAAS,KAAK,aAAY,KAAK,MAAM,gBAAgB,QAAO,KAAK,MAAM;AAAA;AAAA,EAAQ,gBAAgB,IAAE;AAAC,SAAK,QAAM;AAAA;AAAA,EAAE,OAAM;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,aAAO,KAAK,MAAM;AAAA;AAAA;AAAA,EAAU,QAAO;AAAC,SAAK,MAAM;AAAA;AAAA,EAAQ,YAAW;AAAC,WAAM,CAAC,KAAK,MAAM,UAAQ,CAAC,KAAK,MAAM;AAAA;AAAA,EAAM,QAAQ,IAAE;AAAC,SAAK,MAAM,cAAY,KAAK,IAAI,GAAE,KAAK,IAAI,IAAE,KAAK;AAAA;AAAA,EAAgB,cAAa;AAAC,WAAO,KAAK,MAAM;AAAA;AAAA,EAAS,iBAAgB;AAAC,WAAO,KAAK,MAAM;AAAA;AAAA,EAAY,YAAW;AAAC,WAAO,KAAK,MAAM;AAAA;AAAA,EAAO,UAAU,IAAE;AAAC,SAAK,MAAM,SAAO;AAAA;AAAA,EAAE,WAAU;AAAC,WAAO,KAAK,MAAM;AAAA;AAAA,EAAM,SAAS,IAAE;AAAC,SAAK,MAAM,QAAM;AAAA;AAAA,EAAE,kBAAiB;AAAC,WAAO,KAAK,MAAM;AAAA;AAAA,EAAa,YAAW;AAAC,WAAO,KAAK,MAAM;AAAA;AAAA,EAAQ,gBAAgB,IAAE,IAAE;AAAC,IAAM,MAAN,QAAU,MAAK,MAAM,iBAAe,KAAG,KAAK,MAAM,eAAa;AAAA;AAAA,EAAE,kBAAiB;AAAC,WAAO,KAAK;AAAA;AAAA,EAAM,UAAU,IAAE;AAAC,WAAO,KAAK,MAAM,UAAU;AAAA;AAAA;AAAI,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE,IAAE;AAAC,aAAQ,KAAK,WAAS,IAAG,KAAK,eAAa,OAAG,KAAK,YAAU,MAAK,KAAK,iBAAe,MAAK,KAAK,qBAAmB,GAAE,KAAK,aAAW,OAAG,KAAK,gBAAc,IAAG,KAAK,sBAAoB,IAAG,KAAK,gBAAc,IAAG,KAAK,UAAQ;AAAE,UAAM,KAAE,KAAK,2BAA2B,GAAE;AAAW,SAAK,SAAO;AAAE,UAAK,CAAC,IAAE,MAAG,KAAK;AAAW,OAAE,YAAY,KAAG,KAAK,YAAU,IAAE,KAAK,kBAAgB,GAAE,cAAc,YAAW,KAAK,UAAQ,GAAE,cAAc,aAAY,KAAK,gBAAc,GAAE,cAAc,cAAa,KAAK,kBAAgB,GAAE,cAAc,cAAa,KAAK,SAAO,GAAE,cAAc,YAAW,MAAG,GAAE,YAAY,KAAG,KAAK;AAAA;AAAA,EAAa,2BAA2B,IAAE;AAAC,QAAI;AAAE,QAAG,AAAU,OAAO,MAAjB,WAAmB,KAAE,SAAS,cAAc,MAAG,cAAa,eAAc,MAAE,KAAG,CAAC;AAAE,YAAM,IAAI,MAAM;AAAuB,WAAO;AAAA;AAAA,EAAE,aAAY;AAAC,UAAM,KAAE,QAAG;AAAC,YAAM,KAAE,KAAK,QAAQ,yBAAwB,KAAE,GAAE,UAAQ,GAAE,MAAK,KAAE,GAAE,UAAQ,GAAE;AAAI,aAAM,CAAC,KAAE,GAAE,OAAM,KAAE,GAAE;AAAA;AAAS,QAAG,KAAK,QAAQ,iBAAiB,SAAS,QAAG;AAAC,YAAK,CAAC,IAAE,MAAG,GAAE;AAAG,WAAK,KAAK,SAAQ,IAAE;AAAA,QAAM,KAAK,QAAQ,iBAAiB,YAAY,QAAG;AAAC,YAAK,CAAC,IAAE,MAAG,GAAE;AAAG,WAAK,KAAK,YAAW,IAAE;AAAA,QAAM,AAAK,KAAK,QAAQ,eAAlB,QAA8B,AAAU,OAAO,KAAK,QAAQ,cAA9B,YAA0C,KAAK,YAAW,KAAK,gBAAgB,iBAAiB,UAAU,MAAI;AAAC,YAAK,EAAC,YAAW,IAAE,aAAY,IAAE,aAAY,OAAG,KAAK,iBAAgB,KAAE,KAAE,IAAE,KAAG,MAAE,MAAG;AAAE,WAAK,KAAK,UAAS,IAAE,IAAE,IAAE,KAAE;AAAA,QAAM,AAAY,OAAO,kBAAnB,YAAkC;AAAC,YAAM,KAAE,KAAK,YAAY;AAAK,WAAK,iBAAe,IAAI,eAAgB,MAAI;AAAC,aAAI,KAAM,MAAI,KAAK,qBAAsB,MAAO,MAAI;AAAA;AAAA,UAAQ,KAAK,eAAe,QAAQ,KAAK;AAAA;AAAA;AAAA,EAAkB,oBAAmB;AAAC,UAAM,KAAE,KAAK,OAAO;AAAY,WAAI,KAAK,sBAAoB,AAAS,KAAK,QAAQ,WAAtB,UAA+B,MAAK,qBAAmB,IAAE,KAAK;AAAA;AAAA,EAAY,WAAU;AAAC,SAAK,cAAc,KAAK,SAAS,IAAE,IAAE,IAAE,IAAE,KAAE,GAAE,KAAE,GAAE,KAAE,KAAI;AAAC,UAAG,CAAC;AAAE,eAAM,MAAI;AAAA;AAAG,YAAM,KAAE,WAAW,qBAAqB;AAAQ,UAAI,KAAE,MAAI;AAAA;AAAG,YAAM,KAAE,QAAG;AAAC,YAAG,GAAE,WAAS;AAAE;AAAO,WAAE,kBAAiB,GAAE;AAAkB,YAAI,KAAE,GAAE,SAAQ,KAAE,GAAE,SAAQ,KAAE;AAAG,cAAM,IAAE,KAAK,OAAM,IAAE,QAAG;AAAC,cAAG,GAAE,kBAAiB,GAAE,mBAAkB,MAAG,KAAK,QAAM,IAAE;AAAE;AAAO,gBAAM,KAAE,GAAE,SAAQ,KAAE,GAAE,SAAQ,KAAE,KAAE,IAAE,KAAE,KAAE;AAAE,cAAG,MAAG,KAAK,IAAI,MAAG,MAAG,KAAK,IAAI,MAAG,IAAE;AAAC,kBAAM,KAAE,GAAE,yBAAwB,EAAC,MAAK,IAAE,KAAI,OAAG;AAAE,kBAAI,CAAM,MAAN,QAAS,GAAE,KAAE,IAAE,KAAE,KAAG,KAAE,OAAI,GAAE,IAAE,IAAE,KAAE,IAAE,KAAE,KAAG,KAAE,IAAE,KAAE;AAAA;AAAA,WAAI,IAAE,QAAG;AAAC,cAAG,IAAE;AAAC,kBAAM,KAAE,GAAE,SAAQ,KAAE,GAAE,SAAQ,KAAE,GAAE,yBAAwB,EAAC,MAAK,IAAE,KAAI,OAAG;AAAE,YAAM,MAAN,QAAS,GAAE,KAAE,IAAE,KAAE;AAAA;AAAG;AAAA,WAAK,IAAE,QAAG;AAAC,aAAE,iBAAe,GAAE,kBAAgB,SAAS,mBAAiB,EAAE;AAAA,WAAI,IAAE,QAAG;AAAC,gBAAI,IAAE,mBAAkB,GAAE;AAAA,WAAmB,IAAE,QAAG;AAAC,gBAAG,GAAE;AAAA;AAAkB,iBAAS,iBAAiB,eAAc,IAAG,SAAS,iBAAiB,aAAY,IAAG,SAAS,iBAAiB,cAAa,IAAG,SAAS,iBAAiB,iBAAgB,IAAG,SAAS,iBAAiB,aAAY,GAAE,EAAC,SAAQ,UAAK,SAAS,iBAAiB,SAAQ,GAAE,EAAC,SAAQ,SAAK,KAAE,MAAI;AAAC,mBAAS,oBAAoB,eAAc,IAAG,SAAS,oBAAoB,aAAY,IAAG,SAAS,oBAAoB,cAAa,IAAG,SAAS,oBAAoB,iBAAgB,IAAG,SAAS,oBAAoB,aAAY,IAAG,WAAY,MAAI;AAAC,qBAAS,oBAAoB,SAAQ,GAAE,EAAC,SAAQ;AAAA,aAAO;AAAA;AAAA;AAAM,aAAO,GAAE,iBAAiB,eAAc,KAAG,MAAI;AAAC,cAAI,GAAE,oBAAoB,eAAc;AAAA;AAAA,MAAK,KAAK,SAAS,CAAC,IAAE,IAAE,OAAI;AAAC,WAAK,KAAK,QAAO,KAAK,IAAI,GAAE,KAAK,IAAI,GAAE,KAAE,KAAK,QAAQ,wBAAwB;AAAA,OAAY,QAAG;AAAC,WAAK,aAAW,MAAG,KAAK,KAAK,aAAY,KAAK,IAAI,GAAE,KAAK,IAAI,GAAE,KAAE,KAAK,QAAQ,wBAAwB;AAAA,OAAY,QAAG;AAAC,WAAK,aAAW,OAAG,KAAK,KAAK,WAAU,KAAK,IAAI,GAAE,KAAK,IAAI,GAAE,KAAE,KAAK,QAAQ,wBAAwB;AAAA;AAAA;AAAA,EAAa,UAAU,IAAE,IAAE;AAAC,QAAI;AAAE,UAAM,KAAG,CAAQ,MAAE,KAAK,eAAf,QAA2B,AAAS,OAAT,SAAW,SAAO,GAAE,qBAAmB;AAAE,QAAG,AAAM,MAAN;AAAQ,aAAO;AAAI,QAAG,CAAC,MAAM,OAAO;AAAI,aAAO,OAAO;AAAG,QAAG,AAAS,OAAT,QAAW;AAAC,YAAM,KAAE,KAAK,OAAO,gBAAc;AAAI,aAAO,CAAM,MAAN,OAAQ,SAAO,GAAE,MAAO,QAAG,CAAC,GAAE,YAAW,KAAE,KAAE;AAAA;AAAE,WAAO;AAAA;AAAA,EAAI,WAAU;AAAC,UAAM,KAAE,SAAS,cAAc,QAAO,KAAE,GAAE,aAAa,EAAC,MAAK,WAAS,KAAE,KAAK,QAAQ,YAAU,AAAU,OAAO,KAAK,QAAQ,YAA9B,WAAuC,KAAK,QAAQ,SAAS,QAAQ,MAAK,MAAI;AAAG,WAAO,GAAE,YAAU;AAAA,cAAiB,KAAE,WAAW,QAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAAkuB,KAAK,UAAU,KAAK,QAAQ,QAAO,KAAK,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAukC,CAAC,IAAE;AAAA;AAAA,EAAG,WAAW,IAAE;AAAC,QAAG,KAAK,QAAQ,cAAY,GAAE,WAAU;AAAC,YAAM,KAAE,KAAK,2BAA2B,GAAE;AAAW,SAAE,YAAY,KAAK,YAAW,KAAK,SAAO;AAAA;AAAE,IAAK,GAAE,eAAP,QAAmB,AAAU,OAAO,KAAK,QAAQ,cAA9B,YAA0C,KAAK,YAAW,KAAK,UAAQ,IAAE,KAAK;AAAA;AAAA,EAAW,aAAY;AAAC,WAAO,KAAK;AAAA;AAAA,EAAQ,WAAU;AAAC,WAAO,KAAK,gBAAgB;AAAA;AAAA,EAAY,YAAW;AAAC,WAAO,KAAK,gBAAgB;AAAA;AAAA,EAAW,UAAU,IAAE;AAAC,SAAK,gBAAgB,aAAW;AAAA;AAAA,EAAE,oBAAoB,IAAE;AAAC,UAAK,EAAC,aAAY,OAAG,KAAK,iBAAgB,KAAE,KAAE;AAAE,SAAK,UAAU;AAAA;AAAA,EAAG,UAAS;AAAC,QAAI,IAAE;AAAE,SAAK,cAAc,QAAS,QAAG,OAAM,KAAK,UAAU,UAAS,AAAQ,MAAE,KAAK,oBAAf,QAAgC,AAAS,OAAT,UAAY,GAAE,cAAa,AAAQ,MAAE,KAAK,yBAAf,QAAqC,AAAS,OAAT,UAAY,GAAE,QAAS,QAAG,OAAM,KAAK,sBAAoB;AAAA;AAAA,EAAG,YAAY,KAAE,IAAG;AAAC,QAAI,IAAE;AAAE,UAAM,KAAE,MAAI;AAAC,YAAG,aAAa,KAAG,MAAG;AAAA;AAAK,WAAO,KAAK,SAAS,KAAK,KAAG,MAAI,IAAI,QAAS,CAAC,IAAE,OAAI;AAAC,YAAI,KAAE,IAAE,KAAE,WAAY,MAAI;AAAC,aAAE,QAAO,KAAE,QAAO;AAAA,SAAM;AAAA;AAAA;AAAA,EAAM,mBAAmB,IAAE;AAAC,QAAG,CAAC,MAAM,QAAQ;AAAG,aAAO,MAAG;AAAG,QAAG,GAAE,SAAO;AAAE,aAAO,GAAE,MAAI;AAAG,UAAM,KAAE,SAAS,cAAc,WAAU,KAAE,GAAE,WAAW,OAAM,KAAE,GAAE,SAAQ,QAAO,oBAAkB,IAAG,KAAE,GAAE,qBAAqB,GAAE,GAAE,GAAE,KAAG,KAAE,IAAG,IAAE,SAAO;AAAG,WAAO,GAAE,QAAS,CAAC,IAAE,OAAI;AAAC,YAAM,KAAE,KAAE;AAAE,SAAE,aAAa,IAAE;AAAA,QAAM;AAAA;AAAA,EAAE,gBAAe;AAAC,WAAO,KAAK,IAAI,GAAE,OAAO,oBAAkB;AAAA;AAAA,EAAG,kBAAkB,IAAE,IAAE,IAAE,IAAE;AAAC,UAAM,KAAE,GAAE,IAAG,KAAE,GAAE,MAAI,GAAE,IAAG,KAAE,GAAE,QAAO,EAAC,OAAM,IAAE,QAAO,OAAG,GAAE,QAAO,KAAE,KAAE,GAAE,KAAE,KAAK,iBAAgB,KAAE,GAAE,WAAS,GAAE,WAAS,KAAE,GAAE,KAAE,GAAE,SAAO,GAAE,SAAO,KAAE,GAAE,WAAS,KAAE,IAAE,GAAE,IAAE,GAAE,aAAW,GAAE,IAAE,KAAG,MAAE,MAAG,IAAE,IAAE,KAAG,eAAc,KAAE,cAAY;AAAO,OAAE;AAAY,QAAI,IAAE,GAAE,IAAE,GAAE,IAAE;AAAE,aAAQ,KAAE,GAAE,MAAG,IAAE,MAAI;AAAC,YAAM,KAAE,KAAK,MAAM,KAAE;AAAG,UAAG,KAAE,GAAE;AAAC,cAAM,KAAE,KAAK,MAAM,IAAE,KAAE,KAAG,KAAE,KAAE,KAAK,MAAM,IAAE,KAAE,OAAI;AAAE,YAAI,KAAE,KAAE;AAAE,QAAQ,GAAE,aAAV,QAAmB,KAAE,IAAE,AAAW,GAAE,aAAb,YAAwB,MAAE,KAAE,KAAG,GAAE,GAAG,IAAG,MAAE,KAAG,IAAE,IAAE,IAAE,IAAG,IAAE,IAAE,IAAE,GAAE,IAAE;AAAA;AAAE,YAAM,KAAE,KAAK,IAAI,GAAE,OAAI,IAAG,KAAE,KAAK,IAAI,GAAE,OAAI;AAAG,WAAE,KAAI,KAAE,KAAG,KAAE,KAAI,KAAE;AAAA;AAAG,OAAE,QAAO,GAAE;AAAA;AAAA,EAAY,mBAAmB,IAAE,IAAE,IAAE,IAAE;AAAC,UAAM,KAAE,QAAG;AAAC,YAAM,KAAE,GAAE,OAAI,GAAE,IAAG,KAAE,GAAE,QAAO,EAAC,QAAO,OAAG,GAAE,QAAO,KAAE,KAAE,GAAE,KAAE,GAAE,OAAO,QAAM;AAAE,SAAE,OAAO,GAAE;AAAG,UAAI,KAAE,GAAE,KAAE;AAAE,eAAQ,KAAE,GAAE,MAAG,IAAE,MAAI;AAAC,cAAM,KAAE,KAAK,MAAM,KAAE;AAAG,YAAG,KAAE,IAAE;AAAC,gBAAM,KAAE,KAAG,MAAK,MAAM,KAAE,KAAE,OAAI,KAAI,CAAI,OAAJ,IAAM,KAAG;AAAG,aAAE,OAAO,IAAE,KAAG,KAAE,IAAE,KAAE;AAAA;AAAE,cAAM,KAAE,KAAK,IAAI,GAAE,OAAI;AAAG,aAAE,MAAI,MAAE;AAAA;AAAG,SAAE,OAAO,IAAE;AAAA;AAAI,OAAE,aAAY,GAAE,IAAG,GAAE,IAAG,GAAE,QAAO,GAAE;AAAA;AAAA,EAAY,eAAe,IAAE,IAAE,IAAE;AAAC,QAAG,GAAE,YAAU,KAAK,mBAAmB,GAAE,YAAW,GAAE;AAAe,aAAO,KAAK,GAAE,eAAe,IAAE;AAAG,QAAI,KAAE,GAAE,aAAW;AAAE,QAAG,GAAE,WAAU;AAAC,YAAM,KAAE,MAAM,KAAK,GAAE,IAAI,OAAQ,CAAC,IAAE,OAAI,KAAK,IAAI,IAAE,KAAK,IAAI,MAAK;AAAG,WAAE,KAAE,IAAE,KAAE;AAAA;AAAE,OAAE,YAAU,GAAE,UAAQ,GAAE,WAAS,KAAK,kBAAkB,IAAE,IAAE,IAAE,MAAG,KAAK,mBAAmB,IAAE,IAAE,IAAE;AAAA;AAAA,EAAG,mBAAmB,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE;AAAC,UAAM,KAAE,KAAK,iBAAgB,KAAE,SAAS,cAAc;AAAU,OAAE,QAAM,KAAK,MAAM,KAAE,KAAG,GAAE,SAAO,KAAK,MAAM,KAAE,KAAG,GAAE,MAAM,QAAM,GAAG,QAAM,GAAE,MAAM,SAAO,GAAG,QAAM,GAAE,MAAM,OAAK,GAAG,KAAK,MAAM,SAAO,GAAE,YAAY;AAAG,UAAM,KAAE,GAAE,WAAW;AAAM,QAAG,KAAK,eAAe,IAAE,IAAE,KAAG,GAAE,QAAM,KAAG,GAAE,SAAO,GAAE;AAAC,YAAM,KAAE,GAAE,aAAY,KAAE,GAAE,WAAW;AAAM,SAAE,UAAU,IAAE,GAAE,IAAG,GAAE,2BAAyB,aAAY,GAAE,YAAU,KAAK,mBAAmB,GAAE,gBAAe,GAAE,SAAS,GAAE,GAAE,GAAE,OAAM,GAAE,SAAQ,GAAE,YAAY;AAAA;AAAA;AAAA,EAAI,kBAAkB,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE;AAAC,UAAM,KAAE,KAAK,iBAAgB,EAAC,aAAY,OAAG,KAAK,iBAAgB,KAAE,KAAE;AAAE,QAAI,KAAE,KAAK,IAAI,EAAE,kBAAiB,IAAE,KAAG,KAAE;AAAG,QAAG,GAAE,YAAU,GAAE,QAAO;AAAC,YAAM,KAAE,GAAE,YAAU,KAAG,KAAE,KAAG,IAAE,UAAQ,KAAE;AAAG,WAAE,MAAG,KAAI,MAAE,KAAK,MAAM,KAAE,MAAG;AAAA;AAAG,QAAG,AAAI,OAAJ;AAAM;AAAO,UAAM,KAAE,QAAG;AAAC,UAAG,KAAE,KAAG,MAAG;AAAE;AAAO,UAAG,GAAE;AAAG;AAAO,SAAE,MAAG;AAAG,YAAM,KAAE,KAAE;AAAE,UAAI,KAAE,KAAK,IAAI,KAAE,IAAE;AAAG,UAAG,GAAE,YAAU,GAAE,QAAO;AAAC,cAAM,KAAE,GAAE,YAAU,KAAG,KAAE,KAAG,IAAE,UAAQ,KAAE;AAAG,aAAE,KAAK,MAAM,KAAE,MAAG;AAAA;AAAE,UAAG,MAAG;AAAE;AAAO,YAAM,KAAE,GAAE,IAAK,QAAG;AAAC,cAAM,KAAE,KAAK,MAAM,KAAE,KAAE,GAAE,SAAQ,KAAE,KAAK,MAAO,MAAE,MAAG,KAAE,GAAE;AAAQ,eAAO,GAAE,MAAM,IAAE;AAAA;AAAM,WAAK,mBAAmB,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE;AAAA,OAAI,IAAE,KAAK,KAAK,KAAE;AAAG,QAAG,CAAC,KAAK,cAAa;AAAC,eAAQ,KAAE,GAAE,KAAE,GAAE;AAAI,WAAE;AAAG;AAAA;AAAO,UAAM,IAAE,KAAK,gBAAgB,aAAW,IAAE,IAAE,KAAK,MAAM,IAAE;AAAG,QAAG,GAAE,IAAE,IAAG,GAAE,IAAG,GAAE,IAAE,IAAG,IAAE,GAAE;AAAC,YAAM,KAAE,KAAK,GAAG,UAAU,MAAI;AAAC,cAAK,EAAC,YAAW,OAAG,KAAK,iBAAgB,KAAE,KAAK,MAAM,KAAE,KAAE;AAAG,eAAO,KAAK,IAAG,SAAO,EAAE,aAAY,IAAE,YAAU,IAAG,GAAE,YAAU,IAAG,KAAE,KAAI,GAAE,KAAE,IAAG,GAAE,KAAG,GAAE,KAAE;AAAA;AAAM,WAAK,oBAAoB,KAAK;AAAA;AAAA;AAAA,EAAI,cAAc,IAAE,IAAE,IAAE,IAAE;AAAC,QAAG,EAAC,SAAQ,OAAG,IAAE,KAAE,SAAS,IAAE,IAAE;AAAC,UAAI,KAAE;AAAG,eAAQ,MAAK;AAAE,eAAO,UAAU,eAAe,KAAK,IAAE,OAAI,GAAE,QAAQ,MAAG,KAAI,IAAE,MAAG,GAAE;AAAI,UAAG,AAAM,MAAN,QAAS,AAAY,OAAO,OAAO,yBAA1B,YAAgD;AAAC,YAAI,KAAE;AAAE,aAAI,KAAE,OAAO,sBAAsB,KAAG,KAAE,GAAE,QAAO;AAAI,aAAE,QAAQ,GAAE,OAAI,KAAG,OAAO,UAAU,qBAAqB,KAAK,IAAE,GAAE,QAAM,IAAE,GAAE,OAAI,GAAE,GAAE;AAAA;AAAK,aAAO;AAAA,MAAG,IAAE,CAAC;AAAY,UAAM,KAAE,SAAS,cAAc,QAAO,KAAE,KAAK,UAAU,GAAE,QAAO,GAAE;AAAe,OAAE,MAAM,SAAO,GAAG,QAAM,MAAG,KAAE,KAAI,IAAE,MAAM,YAAU,IAAI,SAAO,KAAK,cAAc,MAAM,YAAU,GAAG,QAAM,KAAK,cAAc,YAAY;AAAG,UAAM,KAAE,GAAE;AAAY,SAAK,gBAAgB,YAAY,KAAG,KAAK,kBAAkB,IAAE,IAAE,IAAE,IAAE,IAAE;AAAA;AAAA,EAAG,OAAO,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAI;AAAE,WAAK,SAAS,QAAS,QAAG,OAAM,KAAK,WAAS,IAAG,KAAK,cAAc,YAAU,IAAG,KAAK,gBAAgB,YAAU,IAAG,AAAM,KAAK,QAAQ,SAAnB,QAA2B,MAAK,gBAAgB,MAAM,QAAM,AAAU,OAAO,KAAK,QAAQ,SAA9B,WAAoC,GAAG,KAAK,QAAQ,YAAU,KAAK,QAAQ;AAAO,YAAM,KAAE,KAAK,iBAAgB,KAAE,KAAK,gBAAgB,aAAY,KAAE,KAAK,KAAK,GAAE,WAAU,MAAK,QAAQ,eAAa;AAAI,WAAK,eAAa,KAAE;AAAE,YAAM,KAAE,KAAK,QAAQ,cAAY,CAAC,KAAK,cAAa,KAAG,MAAE,KAAE,MAAG;AAAE,UAAG,KAAK,QAAQ,MAAM,QAAM,KAAE,SAAO,GAAG,QAAM,KAAK,gBAAgB,MAAM,YAAU,KAAK,eAAa,SAAO,UAAS,KAAK,gBAAgB,UAAU,OAAO,eAAc,CAAC,CAAC,KAAK,QAAQ,gBAAe,KAAK,OAAO,MAAM,kBAAgB,GAAG,KAAK,QAAQ,eAAa,KAAK,QAAQ,iBAAgB,KAAK,OAAO,MAAM,QAAM,GAAG,KAAK,QAAQ,iBAAgB,KAAK,YAAU,IAAE,KAAK,KAAK,WAAU,KAAK,QAAQ;AAAc,iBAAQ,KAAE,GAAE,KAAE,GAAE,kBAAiB,MAAI;AAAC,gBAAM,KAAE,OAAO,OAAO,OAAO,OAAO,IAAG,KAAK,UAAS,AAAQ,MAAE,KAAK,QAAQ,mBAAvB,QAAuC,AAAS,OAAT,SAAW,SAAO,GAAE;AAAI,eAAK,cAAc,CAAC,GAAE,eAAe,MAAI,IAAE,IAAE;AAAA;AAAA,WAAO;AAAC,cAAM,KAAE,CAAC,GAAE,eAAe;AAAI,WAAE,mBAAiB,KAAG,GAAE,KAAK,GAAE,eAAe,KAAI,KAAK,cAAc,IAAE,KAAK,SAAQ,IAAE;AAAA;AAAG,cAAQ,UAAU,KAAM,MAAI,KAAK,KAAK;AAAA;AAAA;AAAA,EAAiB,WAAU;AAAC,QAAG,KAAK,oBAAoB,QAAS,QAAG,OAAM,KAAK,sBAAoB,IAAG,CAAC,KAAK;AAAU;AAAO,UAAK,EAAC,aAAY,OAAG,KAAK,iBAAgB,EAAC,OAAM,OAAG,KAAK,gBAAgB;AAAwB,QAAG,KAAK,OAAO,KAAK,YAAW,KAAK,gBAAc,OAAI,KAAK,gBAAgB,aAAY;AAAC,YAAK,EAAC,OAAM,OAAG,KAAK,gBAAgB;AAAwB,UAAI,KAAE,KAAE;AAAE,YAAG,GAAE,KAAE,KAAE,IAAE,KAAK,MAAM,MAAG,KAAK,KAAK,KAAG,MAAG,GAAE,KAAK,gBAAgB,cAAY;AAAA;AAAA;AAAA,EAAG,KAAK,IAAE;AAAC,SAAK,QAAQ,cAAY,IAAE,KAAK;AAAA;AAAA,EAAW,eAAe,IAAE,KAAE,OAAG;AAAC,UAAK,EAAC,YAAW,IAAE,aAAY,IAAE,aAAY,OAAG,KAAK,iBAAgB,KAAE,KAAE,IAAE,KAAE,IAAE,KAAE,KAAE,IAAE,KAAE,KAAE;AAAE,QAAG,KAAK,YAAW;AAAC,YAAM,KAAE;AAAG,WAAE,KAAE,KAAE,KAAK,gBAAgB,cAAY,KAAE,KAAE,KAAE,MAAI,MAAK,gBAAgB,cAAY;AAAA,WAAO;AAAC,MAAC,MAAE,MAAG,KAAE,OAAK,MAAK,gBAAgB,aAAW,KAAG,MAAK,QAAQ,aAAW,KAAE;AAAI,YAAM,KAAE,KAAE,KAAE;AAAE,YAAG,KAAK,QAAQ,cAAY,KAAE,KAAI,MAAK,gBAAgB,cAAY,KAAK,IAAI,IAAE;AAAA;AAAK;AAAC,YAAM,KAAE,KAAK,gBAAgB,YAAW,KAAE,KAAE,IAAE,KAAG,MAAE,MAAG;AAAE,WAAK,KAAK,UAAS,IAAE,IAAE,IAAE,KAAE;AAAA;AAAA;AAAA,EAAI,eAAe,IAAE,IAAE;AAAC,QAAG,MAAM;AAAG;AAAO,UAAM,KAAE,MAAI;AAAE,SAAK,cAAc,MAAM,WAAS,WAAW,+BAA8B,aAAW,KAAK,gBAAgB,MAAM,QAAM,GAAG,OAAK,KAAK,OAAO,MAAM,OAAK,GAAG,OAAK,KAAK,OAAO,MAAM,YAAU,eAAe,AAAM,KAAK,MAAM,QAAjB,MAAoB,KAAK,QAAQ,cAAY,QAAO,KAAK,gBAAc,KAAK,QAAQ,cAAY,KAAK,eAAe,IAAE;AAAA;AAAA,EAAG,YAAY,IAAE,IAAE,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,YAAM,KAAE,KAAK,cAAc,iBAAiB;AAAU,UAAG,CAAC,GAAE;AAAO,cAAM,IAAI,MAAM;AAAoB,UAAG,AAAY,OAAZ,WAAc;AAAC,cAAM,KAAE,MAAM,KAAK,IAAG,IAAK,QAAG,GAAE,UAAU,IAAE;AAAK,eAAO,QAAQ,QAAQ;AAAA;AAAG,aAAO,QAAQ,IAAI,MAAM,KAAK,IAAG,IAAK,QAAG,IAAI,QAAS,CAAC,IAAE,OAAI;AAAC,WAAE,OAAQ,QAAG;AAAC,eAAE,GAAE,MAAG,GAAE,IAAI,MAAM;AAAA,WAA6B,IAAE;AAAA;AAAA;AAAA;AAAA;AAAa,EAAE,mBAAiB,KAAI,EAAE,YAAU;AAAG,sBAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,YAAW,KAAK,cAAY,MAAI;AAAA;AAAA;AAAA,EAAG,QAAO;AAAC,SAAK,cAAY,KAAK,GAAG,QAAQ,MAAI;AAAC,4BAAuB,MAAI;AAAC,aAAK,KAAK;AAAA;AAAA,QAAc,KAAK,KAAK;AAAA;AAAA,EAAQ,OAAM;AAAC,SAAK;AAAA;AAAA,EAAc,UAAS;AAAC,SAAK;AAAA;AAAA;AAAe,sBAAgB,EAAC;AAAA,EAAC,YAAY,KAAE,IAAI,gBAAa;AAAC,aAAQ,KAAK,aAAW,MAAK,KAAK,gBAAc,GAAE,KAAK,iBAAe,GAAE,KAAK,SAAO,OAAG,KAAK,gBAAc,GAAE,KAAK,YAAU,QAAO,KAAK,SAAO,MAAK,KAAK,aAAW,IAAG,KAAK,SAAO,MAAG,KAAK,cAAY,MAAK,KAAK,UAAQ,OAAG,KAAK,WAAS,OAAG,KAAK,mBAAiB,KAAK,IAAG,KAAK,sBAAoB,KAAK,IAAG,KAAK,eAAa,IAAE,KAAK,WAAS,KAAK,aAAa,cAAa,KAAK,SAAS,QAAQ,KAAK,aAAa;AAAA;AAAA,EAAa,OAAM;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAA;AAAA;AAAA,MAAS,MAAK;AAAC,WAAO,KAAK;AAAA;AAAA,MAAe,IAAI,IAAE;AAAC,QAAG,KAAK,aAAW,IAAE,KAAK,YAAU,QAAO,CAAC;AAAE,aAAO,KAAK,SAAO,MAAK,KAAK,KAAK,KAAK;AAAW,UAAM,IAAG,KAAM,QAAG;AAAC,UAAG,GAAE,UAAQ;AAAI,cAAM,IAAI,MAAM,mBAAmB,OAAM,GAAE,WAAW,GAAE;AAAe,aAAO,GAAE;AAAA,OAAiB,KAAM,QAAG,KAAK,eAAa,KAAE,OAAK,KAAK,aAAa,gBAAgB,KAAK,KAAM,QAAG;AAAC,WAAK,eAAa,MAAI,MAAK,SAAO,IAAE,KAAK,KAAK,mBAAkB,KAAK,KAAK,YAAW,KAAK,YAAU,KAAK;AAAA;AAAA;AAAA,EAAW,QAAO;AAAC,QAAI;AAAE,QAAG,CAAC,KAAK;AAAO;AAAO,SAAK,SAAO,OAAG,AAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,UAAY,GAAE,cAAa,KAAK,aAAW,KAAK,aAAa,sBAAqB,KAAK,UAAS,MAAK,WAAW,SAAO,KAAK,SAAQ,KAAK,WAAW,aAAa,QAAM,KAAK,eAAc,KAAK,WAAW,QAAQ,KAAK;AAAU,QAAI,KAAE,KAAK,iBAAe,KAAK;AAAc,IAAC,OAAG,KAAK,YAAU,KAAE,MAAK,MAAE,GAAE,KAAK,iBAAe,IAAG,KAAK,WAAW,MAAM,KAAK,aAAa,aAAY,KAAG,KAAK,gBAAc,KAAK,aAAa,aAAY,KAAK,WAAW,UAAQ,MAAI;AAAC,WAAK,eAAa,KAAK,YAAW,MAAK,SAAQ,KAAK,KAAK;AAAA;AAAA;AAAA,EAAW,SAAQ;AAAC,QAAI;AAAE,SAAK,SAAO,MAAG,AAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,UAAY,GAAE,QAAO,KAAK,kBAAgB,KAAK,aAAa,cAAY,KAAK;AAAA;AAAA,EAAc,OAAM;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,WAAK,UAAS,MAAK,SAAQ,KAAK,KAAK;AAAA;AAAA;AAAA,EAAY,QAAO;AAAC,SAAK,UAAS,MAAK,UAAS,KAAK,KAAK;AAAA;AAAA,EAAU,OAAO,IAAE;AAAC,UAAM,KAAE,KAAE,KAAK,aAAY,KAAE,KAAK;AAAW,IAAM,MAAN,QAAS,GAAE,KAAK,KAAK,aAAa,cAAY,KAAG,AAAM,MAAN,QAAS,GAAE,iBAAiB,SAAS,MAAI;AAAC,aAAI,KAAK,cAAa,MAAK,aAAW,MAAK,KAAK;AAAA,OAAW,EAAC,MAAK;AAAA;AAAA,EAAK,UAAU,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,aAAO,KAAK,aAAa,UAAU;AAAA;AAAA;AAAA,MAAU,eAAc;AAAC,WAAO,KAAK;AAAA;AAAA,MAAkB,aAAa,IAAE;AAAC,SAAK,gBAAc,IAAE,KAAK,cAAa,MAAK,WAAW,aAAa,QAAM;AAAA;AAAA,MAAO,cAAa;AAAC,WAAO,MAAK,SAAO,KAAK,iBAAe,KAAK,iBAAgB,MAAK,aAAa,cAAY,KAAK,kBAAgB,KAAK;AAAA;AAAA,MAAkB,YAAY,IAAE;AAAC,UAAM,KAAE,CAAC,KAAK;AAAO,UAAG,KAAK,UAAS,KAAK,iBAAe,KAAE,KAAK,eAAc,MAAG,KAAK,SAAQ,KAAK,KAAK,YAAW,KAAK,KAAK;AAAA;AAAA,MAAkB,WAAU;AAAC,QAAI,IAAE;AAAE,WAAO,AAAQ,MAAE,KAAK,eAAf,QAA2B,AAAS,OAAT,SAAW,KAAG,CAAQ,MAAE,KAAK,YAAf,QAAwB,AAAS,OAAT,SAAW,SAAO,GAAE,aAAW;AAAA;AAAA,MAAM,SAAS,IAAE;AAAC,SAAK,YAAU;AAAA;AAAA,MAAM,SAAQ;AAAC,WAAO,KAAK,SAAS,KAAK;AAAA;AAAA,MAAU,OAAO,IAAE;AAAC,SAAK,SAAS,KAAK,QAAM,IAAE,KAAK,KAAK;AAAA;AAAA,MAAoB,QAAO;AAAC,WAAO,KAAK;AAAA;AAAA,MAAW,MAAM,IAAE;AAAC,SAAK,WAAS,MAAI,MAAK,SAAO,IAAE,KAAK,SAAO,KAAK,SAAS,eAAa,KAAK,SAAS,QAAQ,KAAK,aAAa;AAAA;AAAA,EAAc,YAAY,IAAE;AAAC,WAAM,mBAAmB,KAAK;AAAA;AAAA,EAAG,cAAa;AAAC,WAAO,KAAK;AAAA;AAAA,EAAS,iBAAgB;AAAC,UAAM,KAAE;AAAG,QAAG,CAAC,KAAK;AAAO,aAAO;AAAE,UAAM,KAAE,KAAK,OAAO;AAAiB,aAAQ,KAAE,GAAE,KAAE,IAAE;AAAI,SAAE,KAAK,KAAK,OAAO,eAAe;AAAI,WAAO;AAAA;AAAA;AAAG,IAAM,IAAE,EAAC,WAAU,QAAO,eAAc,QAAO,aAAY,GAAE,aAAY,GAAE,YAAW,MAAG,UAAS,MAAG,YAAW,OAAG,YAAW,MAAG,YAAW,MAAG,YAAW;AAAK,sBAAgB,EAAC;AAAA,SAAQ,OAAO,IAAE;AAAC,WAAO,IAAI,EAAE;AAAA;AAAA,EAAG,YAAY,IAAE;AAAC,UAAM,KAAE,GAAE,SAAQ,CAAa,GAAE,YAAf,aAAuB,IAAI,MAAE;AAAQ,UAAM,EAAC,OAAM,IAAE,eAAc,GAAE,eAAc,UAAS,GAAE,UAAS,cAAa,GAAE,cAAY,KAAK,UAAQ,IAAG,KAAK,cAAY,MAAK,KAAK,iBAAe,MAAK,KAAK,gBAAc,IAAG,KAAK,qBAAmB,IAAG,KAAK,kBAAgB,MAAK,KAAK,UAAQ,OAAO,OAAO,IAAG,GAAE,KAAG,KAAK,QAAM,IAAI;AAAE,UAAM,KAAE,KAAE,SAAO,KAAK;AAAkB,SAAK,WAAS,IAAI,EAAE,KAAK,SAAQ,KAAG,KAAK,oBAAmB,KAAK,sBAAqB,KAAK,mBAAkB,KAAK;AAAc,UAAM,KAAE,KAAK,QAAQ,OAAK,KAAK,YAAU;AAAG,YAAQ,UAAU,KAAM,MAAI;AAAC,WAAK,KAAK;AAAQ,YAAK,EAAC,OAAM,IAAE,UAAS,OAAG,KAAK;AAAQ,MAAC,OAAG,MAAG,OAAI,KAAK,KAAK,IAAE,IAAE,IAAG,MAAO,MAAI;AAAA;AAAA;AAAA,EAAU,eAAe,KAAE,KAAK,kBAAiB;AAAC,WAAO,KAAK,SAAS,eAAe,KAAE,KAAK,eAAc,KAAK,cAAa;AAAA;AAAA,EAAE,kBAAiB;AAAC,SAAK,cAAc,KAAK,KAAK,MAAM,GAAG,QAAQ,MAAI;AAAC,UAAG,CAAC,KAAK,aAAY;AAAC,cAAM,KAAE,KAAK;AAAiB,aAAK,KAAK,cAAa,KAAG,KAAK,KAAK,gBAAe,KAAG,AAAM,KAAK,kBAAX,QAA2B,KAAK,eAAa,MAAG,KAAK,kBAAgB,KAAK;AAAA;AAAA;AAAA;AAAA,EAAa,mBAAkB;AAAC,SAAK,eAAc,MAAK,KAAK,SAAQ,KAAK,MAAM,UAAS,KAAK,mBAAmB,KAAK,KAAK,aAAa,cAAc,MAAI;AAAC,YAAM,KAAE,KAAK;AAAiB,WAAK,KAAK,cAAa;AAAA,QAAM,KAAK,aAAa,QAAQ,MAAI;AAAC,WAAK,KAAK,SAAQ,KAAK,MAAM;AAAA,QAAW,KAAK,aAAa,SAAS,MAAI;AAAC,WAAK,KAAK,UAAS,KAAK,MAAM,QAAO,KAAK,iBAAe;AAAA,QAAQ,KAAK,aAAa,WAAW,MAAI;AAAC,WAAK,MAAM,QAAO,KAAK,iBAAe;AAAA,QAAQ,KAAK,aAAa,SAAS,MAAI;AAAC,WAAK,KAAK,cAAa,KAAK,gBAAe,KAAK,KAAK,WAAU,KAAK,iBAAe;AAAA,QAAQ,KAAK,aAAa,WAAW,MAAI;AAAC,WAAK,KAAK,WAAU,KAAK;AAAA,QAAqB,KAAK,aAAa,SAAS,MAAI;AAAC,UAAI;AAAE,WAAK,KAAK,SAAQ,AAAQ,MAAE,KAAK,kBAAkB,WAAjC,QAAyC,AAAS,OAAT,SAAW,KAAE,IAAI,MAAM,iBAAgB,KAAK,iBAAe;AAAA;AAAA;AAAA,EAAS,qBAAoB;AAAC,SAAK,cAAc,KAAK,KAAK,SAAS,GAAG,SAAS,CAAC,IAAE,OAAI;AAAC,WAAK,QAAQ,YAAW,MAAK,OAAO,KAAG,KAAK,KAAK,eAAc,KAAE,KAAK,gBAAe,KAAK,KAAK,SAAQ,IAAE;AAAA,QAAO,KAAK,SAAS,GAAG,YAAY,CAAC,IAAE,OAAI;AAAC,WAAK,KAAK,YAAW,IAAE;AAAA,QAAM,KAAK,SAAS,GAAG,UAAU,CAAC,IAAE,IAAE,IAAE,OAAI;AAAC,YAAM,KAAE,KAAK;AAAc,WAAK,KAAK,UAAS,KAAE,IAAE,KAAE,IAAE,IAAE;AAAA,QAAM,KAAK,SAAS,GAAG,UAAU,MAAI;AAAC,WAAK,KAAK;AAAA,QAAa,KAAK,SAAS,GAAG,YAAY,MAAI;AAAC,WAAK,KAAK;AAAA,QAAqB,KAAK,SAAS,GAAG,aAAa,QAAG;AAAC,WAAK,KAAK,aAAY;AAAA,QAAM,KAAK,SAAS,GAAG,WAAW,QAAG;AAAC,WAAK,KAAK,WAAU;AAAA;AAAO;AAAC,UAAI;AAAE,WAAK,cAAc,KAAK,KAAK,SAAS,GAAG,QAAQ,QAAG;AAAC,YAAG,CAAC,KAAK,QAAQ;AAAS;AAAO,YAAI;AAAE,aAAK,SAAS,eAAe,KAAG,aAAa,KAAG,KAAK,cAAY,KAAE,IAAE,AAAK,KAAK,QAAQ,eAAlB,OAA6B,KAAE,MAAI,AAAU,OAAO,KAAK,QAAQ,cAA9B,YAA0C,AAAS,KAAK,QAAQ,eAAtB,UAAmC,MAAE,KAAK,QAAQ,WAAW,eAAc,KAAE,WAAY,MAAI;AAAC,eAAK,OAAO;AAAA,WAAK,KAAG,KAAK,KAAK,eAAc,KAAE,KAAK,gBAAe,KAAK,KAAK,QAAO;AAAA;AAAA;AAAA;AAAA,EAAQ,cAAa;AAAC,QAAI;AAAE,IAAC,CAAQ,MAAE,KAAK,QAAQ,aAAvB,QAAiC,AAAS,OAAT,SAAW,SAAO,GAAE,WAAS,KAAK,QAAQ,QAAQ,QAAS,QAAG;AAAC,WAAK,eAAe;AAAA;AAAA;AAAA,EAAM,0BAAyB;AAAC,SAAK,mBAAmB,QAAS,QAAG,OAAM,KAAK,qBAAmB;AAAA;AAAA,EAAG,WAAW,IAAE;AAAC,SAAK,UAAQ,OAAO,OAAO,IAAG,KAAK,SAAQ,KAAG,GAAE,YAAU,CAAC,GAAE,SAAQ,MAAK,cAAY,EAAE,aAAa,KAAK,eAAc,GAAE,YAAW,GAAE,SAAO,GAAE,YAAW,MAAK,cAAY,EAAE,aAAa,GAAE,OAAM,GAAE,YAAW,KAAK,SAAS,WAAW,KAAK,UAAS,GAAE,aAAW,KAAK,gBAAgB,GAAE,YAAW,AAAM,GAAE,iBAAR,QAAwB,MAAK,kBAAkB,WAAS,GAAE;AAAA;AAAA,EAAe,eAAe,IAAE;AAAC,OAAE,MAAM,OAAM,KAAK,QAAQ,KAAK;AAAG,UAAM,KAAE,GAAE,KAAK,WAAW,MAAI;AAAC,WAAK,UAAQ,KAAK,QAAQ,OAAQ,QAAG,OAAI,KAAI,KAAK,gBAAc,KAAK,cAAc,OAAQ,QAAG,OAAI;AAAA;AAAO,WAAO,KAAK,cAAc,KAAK,KAAG;AAAA;AAAA,EAAE,aAAY;AAAC,WAAO,KAAK,SAAS;AAAA;AAAA,EAAa,WAAU;AAAC,WAAO,KAAK,SAAS;AAAA;AAAA,EAAW,YAAW;AAAC,WAAO,KAAK,SAAS;AAAA;AAAA,EAAY,UAAU,IAAE;AAAC,WAAO,KAAK,SAAS,UAAU;AAAA;AAAA,EAAG,cAAc,IAAE;AAAC,UAAM,KAAE,KAAE,KAAK;AAAc,SAAK,SAAS,oBAAoB;AAAA;AAAA,EAAG,mBAAkB;AAAC,WAAO,KAAK;AAAA;AAAA,EAAQ,UAAU,IAAE,IAAE,IAAE,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAI;AAAE,UAAG,KAAK,KAAK,QAAO,KAAG,CAAC,KAAK,QAAQ,SAAO,KAAK,eAAa,KAAK,SAAQ,KAAK,cAAY,MAAK,KAAK,iBAAe,MAAK,CAAC,MAAG,CAAC,IAAE;AAAC,cAAM,KAAE,KAAK,QAAQ,eAAa;AAAG,eAAO,mBAAiB,CAAC,GAAE,UAAS,MAAK,kBAAgB,IAAI,mBAAgB,GAAE,SAAO,AAAQ,MAAE,KAAK,qBAAf,QAAiC,AAAS,OAAT,SAAW,SAAO,GAAE;AAAQ,cAAM,KAAE,QAAG,KAAK,KAAK,WAAU;AAAG,aAAE,MAAM,EAAE,UAAU,IAAE,IAAE;AAAG,cAAM,KAAE,KAAK,QAAQ;AAAa,cAAI,MAAE,IAAI,KAAK,CAAC,KAAG,EAAC,MAAK;AAAA;AAAK,WAAK,OAAO,IAAE;AAAG,YAAM,KAAE,MAAM,IAAI,QAAS,QAAG;AAAC,cAAM,KAAE,MAAG,KAAK;AAAc,aAAE,GAAE,MAAG,KAAK,mBAAmB,KAAK,KAAK,aAAa,kBAAkB,MAAI,GAAE,KAAK,gBAAgB,EAAC,MAAK;AAAA;AAAS,UAAG,CAAC,MAAG,CAAC,IAAE;AAAC,cAAM,KAAE,KAAK;AAAkB,sBAAa,KAAI,IAAE,WAAS;AAAA;AAAG,UAAG;AAAE,aAAK,cAAY,EAAE,aAAa,IAAE,MAAG;AAAA,eAAW,IAAE;AAAC,cAAM,KAAE,MAAM,GAAE;AAAc,aAAK,cAAY,MAAM,EAAE,OAAO,IAAE,KAAK,QAAQ;AAAA;AAAY,WAAK,eAAc,MAAK,KAAK,UAAS,KAAK,gBAAe,KAAK,SAAS,OAAO,KAAK,eAAc,KAAK,KAAK,SAAQ,KAAK;AAAA;AAAA;AAAA,EAAkB,KAAK,IAAE,IAAE,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAG;AAAC,eAAO,MAAM,KAAK,UAAU,IAAE,QAAO,IAAE;AAAA,eAAS,IAAN;AAAS,cAAM,KAAK,KAAK,SAAQ,KAAG;AAAA;AAAA;AAAA;AAAA,EAAM,SAAS,IAAE,IAAE,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAG;AAAC,eAAO,MAAM,KAAK,UAAU,IAAG,IAAE,IAAE;AAAA,eAAS,IAAN;AAAS,cAAM,KAAK,KAAK,SAAQ,KAAG;AAAA;AAAA;AAAA;AAAA,EAAM,KAAK,IAAE;AAAC,QAAG,CAAC,KAAK;AAAY,YAAM,IAAI,MAAM;AAAmB,SAAK,SAAS,KAAK,KAAG,KAAK,KAAK,QAAO;AAAA;AAAA,EAAG,iBAAgB;AAAC,WAAO,KAAK;AAAA;AAAA,EAAY,YAAY,EAAC,UAAS,KAAE,GAAE,WAAU,KAAE,KAAI,WAAU,KAAE,QAAK,IAAG;AAAC,QAAG,CAAC,KAAK;AAAY,YAAM,IAAI,MAAM;AAAsC,UAAM,KAAE,KAAK,IAAI,IAAE,KAAK,YAAY,mBAAkB,KAAE;AAAG,aAAQ,KAAE,GAAE,KAAE,IAAE,MAAI;AAAC,YAAM,KAAE,KAAK,YAAY,eAAe,KAAG,KAAE,IAAG,KAAE,GAAE,SAAO;AAAE,eAAQ,KAAE,GAAE,KAAE,IAAE,MAAI;AAAC,cAAM,KAAE,GAAE,MAAM,KAAK,MAAM,KAAE,KAAG,KAAK,KAAM,MAAE,KAAG;AAAI,YAAI,KAAE;AAAE,iBAAQ,KAAE,GAAE,KAAE,GAAE,QAAO,MAAI;AAAC,gBAAM,KAAE,GAAE;AAAG,eAAK,IAAI,MAAG,KAAK,IAAI,OAAK,MAAE;AAAA;AAAG,WAAE,KAAK,KAAK,MAAM,KAAE,MAAG;AAAA;AAAG,SAAE,KAAK;AAAA;AAAG,WAAO;AAAA;AAAA,EAAE,cAAa;AAAC,QAAI,KAAE,MAAM,iBAAe;AAAE,WAAO,AAAI,OAAJ,KAAO,OAAI,IAAE,KAAG,CAAC,KAAK,eAAc,MAAE,KAAK,YAAY,WAAU;AAAA;AAAA,EAAE,kBAAkB,IAAE;AAAC,SAAK,QAAQ,WAAS;AAAA;AAAA,EAAE,QAAQ,IAAE;AAAC,SAAK,iBAAe,MAAK,MAAM,QAAQ,KAAG,KAAK,eAAe,KAAG,KAAK,KAAK,cAAa;AAAA;AAAA,EAAG,OAAO,IAAE;AAAC,UAAM,KAAE,KAAK,gBAAc;AAAE,SAAK,QAAQ;AAAA;AAAA,EAAG,KAAK,IAAE,IAAE;AAAC,UAAM,KAAE,OAAO,OAAO,MAAK,EAAC,MAAK,EAAC,KAAI,MAAI,MAAM;AAAQ,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,MAAM,MAAN,QAAS,KAAK,QAAQ;AAAG,YAAM,KAAE,MAAM,GAAE,KAAK,KAAK;AAAM,aAAO,AAAM,MAAN,QAAU,MAAK,iBAAiB,IAAE,KAAK,MAAM,OAAO,MAAG,KAAK,iBAAe,KAAG;AAAA;AAAA;AAAA,EAAK,YAAW;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,aAAO,KAAK,cAAY,KAAK,UAAQ,KAAK;AAAA;AAAA;AAAA,EAAU,OAAM;AAAC,SAAK,SAAQ,KAAK,QAAQ;AAAA;AAAA,EAAG,KAAK,IAAE;AAAC,SAAK,QAAQ,KAAK,mBAAiB;AAAA;AAAA,EAAG,QAAO;AAAC,SAAK,KAAK,IAAG,CAAC,CAAC,KAAI;AAAA;AAAA,EAAM,gBAAgB,IAAE;AAAC,SAAK,2BAA0B,MAAM,gBAAgB,KAAG,KAAK;AAAA;AAAA,EAAmB,cAAa;AAAC,WAAO,EAAE,MAAK,WAAU,QAAQ,WAAU,KAAE,aAAY,KAAE,GAAE,KAAE,WAAU;AAAC,aAAO,KAAK,SAAS,YAAY,IAAE,IAAE;AAAA;AAAA;AAAA,EAAM,UAAS;AAAC,QAAI;AAAE,SAAK,KAAK,YAAW,AAAQ,MAAE,KAAK,qBAAf,QAAiC,AAAS,OAAT,UAAY,GAAE,SAAQ,KAAK,QAAQ,QAAS,QAAG,GAAE,YAAY,KAAK,cAAc,QAAS,QAAG,OAAM,KAAK,2BAA0B,KAAK,MAAM,WAAU,KAAK,SAAS,WAAU,MAAM;AAAA;AAAA;AAAW,EAAE,aAAW,cAAc,EAAC;AAAA,EAAC,YAAY,IAAE;AAAC,aAAQ,KAAK,gBAAc,IAAG,KAAK,UAAQ;AAAA;AAAA,EAAE,SAAQ;AAAA;AAAA,EAAE,MAAM,IAAE;AAAC,SAAK,aAAW,IAAE,KAAK;AAAA;AAAA,EAAS,UAAS;AAAC,SAAK,KAAK,YAAW,KAAK,cAAc,QAAS,QAAG;AAAA;AAAA,GAAQ,EAAE,MAAI;;;ACA935B,IAAO,wBAAQ;", "names": []}