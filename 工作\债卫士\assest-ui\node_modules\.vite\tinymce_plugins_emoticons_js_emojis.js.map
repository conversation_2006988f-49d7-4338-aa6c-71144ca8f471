{"version": 3, "sources": ["../tinymce/plugins/emoticons/js/emojis.js", "dep:tinymce_plugins_emoticons_js_emojis"], "sourcesContent": ["// Source: npm package: emojilib, file:emojis.json\nwindow.tinymce.Resource.add(\"tinymce.plugins.emoticons\", {\n  grinning: {\n    keywords: [ \"face\", \"smile\", \"happy\", \"joy\", \":D\", \"grin\" ],\n    char: \"\\ud83d\\ude00\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  grimacing: {\n    keywords: [ \"face\", \"grimace\", \"teeth\" ],\n    char: \"\\ud83d\\ude2c\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  grin: {\n    keywords: [ \"face\", \"happy\", \"smile\", \"joy\", \"kawaii\" ],\n    char: \"\\ud83d\\ude01\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  joy: {\n    keywords: [ \"face\", \"cry\", \"tears\", \"weep\", \"happy\", \"happytears\", \"haha\" ],\n    char: \"\\ud83d\\ude02\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  rofl: {\n    keywords: [ \"face\", \"rolling\", \"floor\", \"laughing\", \"lol\", \"haha\" ],\n    char: \"\\ud83e\\udd23\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  partying: {\n    keywords: [ \"face\", \"celebration\", \"woohoo\" ],\n    char: \"\\ud83e\\udd73\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  smiley: {\n    keywords: [ \"face\", \"happy\", \"joy\", \"haha\", \":D\", \":)\", \"smile\", \"funny\" ],\n    char: \"\\ud83d\\ude03\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  smile: {\n    keywords: [ \"face\", \"happy\", \"joy\", \"funny\", \"haha\", \"laugh\", \"like\", \":D\", \":)\" ],\n    char: \"\\ud83d\\ude04\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  sweat_smile: {\n    keywords: [ \"face\", \"hot\", \"happy\", \"laugh\", \"sweat\", \"smile\", \"relief\" ],\n    char: \"\\ud83d\\ude05\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  laughing: {\n    keywords: [ \"happy\", \"joy\", \"lol\", \"satisfied\", \"haha\", \"face\", \"glad\", \"XD\", \"laugh\" ],\n    char: \"\\ud83d\\ude06\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  innocent: {\n    keywords: [ \"face\", \"angel\", \"heaven\", \"halo\" ],\n    char: \"\\ud83d\\ude07\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  wink: {\n    keywords: [ \"face\", \"happy\", \"mischievous\", \"secret\", \";)\", \"smile\", \"eye\" ],\n    char: \"\\ud83d\\ude09\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  blush: {\n    keywords: [ \"face\", \"smile\", \"happy\", \"flushed\", \"crush\", \"embarrassed\", \"shy\", \"joy\" ],\n    char: \"\\ud83d\\ude0a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  slightly_smiling_face: {\n    keywords: [ \"face\", \"smile\" ],\n    char: \"\\ud83d\\ude42\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  upside_down_face: {\n    keywords: [ \"face\", \"flipped\", \"silly\", \"smile\" ],\n    char: \"\\ud83d\\ude43\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  relaxed: {\n    keywords: [ \"face\", \"blush\", \"massage\", \"happiness\" ],\n    char: \"\\u263a\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  yum: {\n    keywords: [ \"happy\", \"joy\", \"tongue\", \"smile\", \"face\", \"silly\", \"yummy\", \"nom\", \"delicious\", \"savouring\" ],\n    char: \"\\ud83d\\ude0b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  relieved: {\n    keywords: [ \"face\", \"relaxed\", \"phew\", \"massage\", \"happiness\" ],\n    char: \"\\ud83d\\ude0c\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  heart_eyes: {\n    keywords: [ \"face\", \"love\", \"like\", \"affection\", \"valentines\", \"infatuation\", \"crush\", \"heart\" ],\n    char: \"\\ud83d\\ude0d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  smiling_face_with_three_hearts: {\n    keywords: [ \"face\", \"love\", \"like\", \"affection\", \"valentines\", \"infatuation\", \"crush\", \"hearts\", \"adore\" ],\n    char: \"\\ud83e\\udd70\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  kissing_heart: {\n    keywords: [ \"face\", \"love\", \"like\", \"affection\", \"valentines\", \"infatuation\", \"kiss\" ],\n    char: \"\\ud83d\\ude18\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  kissing: {\n    keywords: [ \"love\", \"like\", \"face\", \"3\", \"valentines\", \"infatuation\", \"kiss\" ],\n    char: \"\\ud83d\\ude17\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  kissing_smiling_eyes: {\n    keywords: [ \"face\", \"affection\", \"valentines\", \"infatuation\", \"kiss\" ],\n    char: \"\\ud83d\\ude19\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  kissing_closed_eyes: {\n    keywords: [ \"face\", \"love\", \"like\", \"affection\", \"valentines\", \"infatuation\", \"kiss\" ],\n    char: \"\\ud83d\\ude1a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  stuck_out_tongue_winking_eye: {\n    keywords: [ \"face\", \"prank\", \"childish\", \"playful\", \"mischievous\", \"smile\", \"wink\", \"tongue\" ],\n    char: \"\\ud83d\\ude1c\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  zany: {\n    keywords: [ \"face\", \"goofy\", \"crazy\" ],\n    char: \"\\ud83e\\udd2a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  raised_eyebrow: {\n    keywords: [ \"face\", \"distrust\", \"scepticism\", \"disapproval\", \"disbelief\", \"surprise\" ],\n    char: \"\\ud83e\\udd28\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  monocle: {\n    keywords: [ \"face\", \"stuffy\", \"wealthy\" ],\n    char: \"\\ud83e\\uddd0\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  stuck_out_tongue_closed_eyes: {\n    keywords: [ \"face\", \"prank\", \"playful\", \"mischievous\", \"smile\", \"tongue\" ],\n    char: \"\\ud83d\\ude1d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  stuck_out_tongue: {\n    keywords: [ \"face\", \"prank\", \"childish\", \"playful\", \"mischievous\", \"smile\", \"tongue\" ],\n    char: \"\\ud83d\\ude1b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  money_mouth_face: {\n    keywords: [ \"face\", \"rich\", \"dollar\", \"money\" ],\n    char: \"\\ud83e\\udd11\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  nerd_face: {\n    keywords: [ \"face\", \"nerdy\", \"geek\", \"dork\" ],\n    char: \"\\ud83e\\udd13\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  sunglasses: {\n    keywords: [ \"face\", \"cool\", \"smile\", \"summer\", \"beach\", \"sunglass\" ],\n    char: \"\\ud83d\\ude0e\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  star_struck: {\n    keywords: [ \"face\", \"smile\", \"starry\", \"eyes\", \"grinning\" ],\n    char: \"\\ud83e\\udd29\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  clown_face: {\n    keywords: [ \"face\" ],\n    char: \"\\ud83e\\udd21\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  cowboy_hat_face: {\n    keywords: [ \"face\", \"cowgirl\", \"hat\" ],\n    char: \"\\ud83e\\udd20\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  hugs: {\n    keywords: [ \"face\", \"smile\", \"hug\" ],\n    char: \"\\ud83e\\udd17\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  smirk: {\n    keywords: [ \"face\", \"smile\", \"mean\", \"prank\", \"smug\", \"sarcasm\" ],\n    char: \"\\ud83d\\ude0f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  no_mouth: {\n    keywords: [ \"face\", \"hellokitty\" ],\n    char: \"\\ud83d\\ude36\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  neutral_face: {\n    keywords: [ \"indifference\", \"meh\", \":|\", \"neutral\" ],\n    char: \"\\ud83d\\ude10\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  expressionless: {\n    keywords: [ \"face\", \"indifferent\", \"-_-\", \"meh\", \"deadpan\" ],\n    char: \"\\ud83d\\ude11\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  unamused: {\n    keywords: [ \"indifference\", \"bored\", \"straight face\", \"serious\", \"sarcasm\", \"unimpressed\", \"skeptical\", \"dubious\", \"side_eye\" ],\n    char: \"\\ud83d\\ude12\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  roll_eyes: {\n    keywords: [ \"face\", \"eyeroll\", \"frustrated\" ],\n    char: \"\\ud83d\\ude44\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  thinking: {\n    keywords: [ \"face\", \"hmmm\", \"think\", \"consider\" ],\n    char: \"\\ud83e\\udd14\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  lying_face: {\n    keywords: [ \"face\", \"lie\", \"pinocchio\" ],\n    char: \"\\ud83e\\udd25\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  hand_over_mouth: {\n    keywords: [ \"face\", \"whoops\", \"shock\", \"surprise\" ],\n    char: \"\\ud83e\\udd2d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  shushing: {\n    keywords: [ \"face\", \"quiet\", \"shhh\" ],\n    char: \"\\ud83e\\udd2b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  symbols_over_mouth: {\n    keywords: [ \"face\", \"swearing\", \"cursing\", \"cussing\", \"profanity\", \"expletive\" ],\n    char: \"\\ud83e\\udd2c\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  exploding_head: {\n    keywords: [ \"face\", \"shocked\", \"mind\", \"blown\" ],\n    char: \"\\ud83e\\udd2f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  flushed: {\n    keywords: [ \"face\", \"blush\", \"shy\", \"flattered\" ],\n    char: \"\\ud83d\\ude33\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  disappointed: {\n    keywords: [ \"face\", \"sad\", \"upset\", \"depressed\", \":(\" ],\n    char: \"\\ud83d\\ude1e\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  worried: {\n    keywords: [ \"face\", \"concern\", \"nervous\", \":(\" ],\n    char: \"\\ud83d\\ude1f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  angry: {\n    keywords: [ \"mad\", \"face\", \"annoyed\", \"frustrated\" ],\n    char: \"\\ud83d\\ude20\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  rage: {\n    keywords: [ \"angry\", \"mad\", \"hate\", \"despise\" ],\n    char: \"\\ud83d\\ude21\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  pensive: {\n    keywords: [ \"face\", \"sad\", \"depressed\", \"upset\" ],\n    char: \"\\ud83d\\ude14\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  confused: {\n    keywords: [ \"face\", \"indifference\", \"huh\", \"weird\", \"hmmm\", \":/\" ],\n    char: \"\\ud83d\\ude15\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  slightly_frowning_face: {\n    keywords: [ \"face\", \"frowning\", \"disappointed\", \"sad\", \"upset\" ],\n    char: \"\\ud83d\\ude41\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  frowning_face: {\n    keywords: [ \"face\", \"sad\", \"upset\", \"frown\" ],\n    char: \"\\u2639\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  persevere: {\n    keywords: [ \"face\", \"sick\", \"no\", \"upset\", \"oops\" ],\n    char: \"\\ud83d\\ude23\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  confounded: {\n    keywords: [ \"face\", \"confused\", \"sick\", \"unwell\", \"oops\", \":S\" ],\n    char: \"\\ud83d\\ude16\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  tired_face: {\n    keywords: [ \"sick\", \"whine\", \"upset\", \"frustrated\" ],\n    char: \"\\ud83d\\ude2b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  weary: {\n    keywords: [ \"face\", \"tired\", \"sleepy\", \"sad\", \"frustrated\", \"upset\" ],\n    char: \"\\ud83d\\ude29\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  pleading: {\n    keywords: [ \"face\", \"begging\", \"mercy\" ],\n    char: \"\\ud83e\\udd7a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  triumph: {\n    keywords: [ \"face\", \"gas\", \"phew\", \"proud\", \"pride\" ],\n    char: \"\\ud83d\\ude24\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  open_mouth: {\n    keywords: [ \"face\", \"surprise\", \"impressed\", \"wow\", \"whoa\", \":O\" ],\n    char: \"\\ud83d\\ude2e\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  scream: {\n    keywords: [ \"face\", \"munch\", \"scared\", \"omg\" ],\n    char: \"\\ud83d\\ude31\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  fearful: {\n    keywords: [ \"face\", \"scared\", \"terrified\", \"nervous\", \"oops\", \"huh\" ],\n    char: \"\\ud83d\\ude28\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  cold_sweat: {\n    keywords: [ \"face\", \"nervous\", \"sweat\" ],\n    char: \"\\ud83d\\ude30\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  hushed: {\n    keywords: [ \"face\", \"woo\", \"shh\" ],\n    char: \"\\ud83d\\ude2f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  frowning: {\n    keywords: [ \"face\", \"aw\", \"what\" ],\n    char: \"\\ud83d\\ude26\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  anguished: {\n    keywords: [ \"face\", \"stunned\", \"nervous\" ],\n    char: \"\\ud83d\\ude27\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  cry: {\n    keywords: [ \"face\", \"tears\", \"sad\", \"depressed\", \"upset\", \":'(\" ],\n    char: \"\\ud83d\\ude22\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  disappointed_relieved: {\n    keywords: [ \"face\", \"phew\", \"sweat\", \"nervous\" ],\n    char: \"\\ud83d\\ude25\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  drooling_face: {\n    keywords: [ \"face\" ],\n    char: \"\\ud83e\\udd24\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  sleepy: {\n    keywords: [ \"face\", \"tired\", \"rest\", \"nap\" ],\n    char: \"\\ud83d\\ude2a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  sweat: {\n    keywords: [ \"face\", \"hot\", \"sad\", \"tired\", \"exercise\" ],\n    char: \"\\ud83d\\ude13\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  hot: {\n    keywords: [ \"face\", \"feverish\", \"heat\", \"red\", \"sweating\" ],\n    char: \"\\ud83e\\udd75\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  cold: {\n    keywords: [ \"face\", \"blue\", \"freezing\", \"frozen\", \"frostbite\", \"icicles\" ],\n    char: \"\\ud83e\\udd76\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  sob: {\n    keywords: [ \"face\", \"cry\", \"tears\", \"sad\", \"upset\", \"depressed\" ],\n    char: \"\\ud83d\\ude2d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  dizzy_face: {\n    keywords: [ \"spent\", \"unconscious\", \"xox\", \"dizzy\" ],\n    char: \"\\ud83d\\ude35\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  astonished: {\n    keywords: [ \"face\", \"xox\", \"surprised\", \"poisoned\" ],\n    char: \"\\ud83d\\ude32\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  zipper_mouth_face: {\n    keywords: [ \"face\", \"sealed\", \"zipper\", \"secret\" ],\n    char: \"\\ud83e\\udd10\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  nauseated_face: {\n    keywords: [ \"face\", \"vomit\", \"gross\", \"green\", \"sick\", \"throw up\", \"ill\" ],\n    char: \"\\ud83e\\udd22\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  sneezing_face: {\n    keywords: [ \"face\", \"gesundheit\", \"sneeze\", \"sick\", \"allergy\" ],\n    char: \"\\ud83e\\udd27\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  vomiting: {\n    keywords: [ \"face\", \"sick\" ],\n    char: \"\\ud83e\\udd2e\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  mask: {\n    keywords: [ \"face\", \"sick\", \"ill\", \"disease\" ],\n    char: \"\\ud83d\\ude37\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  face_with_thermometer: {\n    keywords: [ \"sick\", \"temperature\", \"thermometer\", \"cold\", \"fever\" ],\n    char: \"\\ud83e\\udd12\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  face_with_head_bandage: {\n    keywords: [ \"injured\", \"clumsy\", \"bandage\", \"hurt\" ],\n    char: \"\\ud83e\\udd15\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  woozy: {\n    keywords: [ \"face\", \"dizzy\", \"intoxicated\", \"tipsy\", \"wavy\" ],\n    char: \"\\ud83e\\udd74\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  sleeping: {\n    keywords: [ \"face\", \"tired\", \"sleepy\", \"night\", \"zzz\" ],\n    char: \"\\ud83d\\ude34\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  zzz: {\n    keywords: [ \"sleepy\", \"tired\", \"dream\" ],\n    char: \"\\ud83d\\udca4\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  poop: {\n    keywords: [ \"hankey\", \"shitface\", \"fail\", \"turd\", \"shit\" ],\n    char: \"\\ud83d\\udca9\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  smiling_imp: {\n    keywords: [ \"devil\", \"horns\" ],\n    char: \"\\ud83d\\ude08\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  imp: {\n    keywords: [ \"devil\", \"angry\", \"horns\" ],\n    char: \"\\ud83d\\udc7f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  japanese_ogre: {\n    keywords: [ \"monster\", \"red\", \"mask\", \"halloween\", \"scary\", \"creepy\", \"devil\", \"demon\", \"japanese\", \"ogre\" ],\n    char: \"\\ud83d\\udc79\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  japanese_goblin: {\n    keywords: [ \"red\", \"evil\", \"mask\", \"monster\", \"scary\", \"creepy\", \"japanese\", \"goblin\" ],\n    char: \"\\ud83d\\udc7a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  skull: {\n    keywords: [ \"dead\", \"skeleton\", \"creepy\", \"death\" ],\n    char: \"\\ud83d\\udc80\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  ghost: {\n    keywords: [ \"halloween\", \"spooky\", \"scary\" ],\n    char: \"\\ud83d\\udc7b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  alien: {\n    keywords: [ \"UFO\", \"paul\", \"weird\", \"outer_space\" ],\n    char: \"\\ud83d\\udc7d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  robot: {\n    keywords: [ \"computer\", \"machine\", \"bot\" ],\n    char: \"\\ud83e\\udd16\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  smiley_cat: {\n    keywords: [ \"animal\", \"cats\", \"happy\", \"smile\" ],\n    char: \"\\ud83d\\ude3a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  smile_cat: {\n    keywords: [ \"animal\", \"cats\", \"smile\" ],\n    char: \"\\ud83d\\ude38\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  joy_cat: {\n    keywords: [ \"animal\", \"cats\", \"haha\", \"happy\", \"tears\" ],\n    char: \"\\ud83d\\ude39\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  heart_eyes_cat: {\n    keywords: [ \"animal\", \"love\", \"like\", \"affection\", \"cats\", \"valentines\", \"heart\" ],\n    char: \"\\ud83d\\ude3b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  smirk_cat: {\n    keywords: [ \"animal\", \"cats\", \"smirk\" ],\n    char: \"\\ud83d\\ude3c\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  kissing_cat: {\n    keywords: [ \"animal\", \"cats\", \"kiss\" ],\n    char: \"\\ud83d\\ude3d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  scream_cat: {\n    keywords: [ \"animal\", \"cats\", \"munch\", \"scared\", \"scream\" ],\n    char: \"\\ud83d\\ude40\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  crying_cat_face: {\n    keywords: [ \"animal\", \"tears\", \"weep\", \"sad\", \"cats\", \"upset\", \"cry\" ],\n    char: \"\\ud83d\\ude3f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  pouting_cat: {\n    keywords: [ \"animal\", \"cats\" ],\n    char: \"\\ud83d\\ude3e\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  palms_up: {\n    keywords: [ \"hands\", \"gesture\", \"cupped\", \"prayer\" ],\n    char: \"\\ud83e\\udd32\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  raised_hands: {\n    keywords: [ \"gesture\", \"hooray\", \"yea\", \"celebration\", \"hands\" ],\n    char: \"\\ud83d\\ude4c\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  clap: {\n    keywords: [ \"hands\", \"praise\", \"applause\", \"congrats\", \"yay\" ],\n    char: \"\\ud83d\\udc4f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  wave: {\n    keywords: [ \"hands\", \"gesture\", \"goodbye\", \"solong\", \"farewell\", \"hello\", \"hi\", \"palm\" ],\n    char: \"\\ud83d\\udc4b\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  call_me_hand: {\n    keywords: [ \"hands\", \"gesture\" ],\n    char: \"\\ud83e\\udd19\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  \"+1\": {\n    keywords: [ \"thumbsup\", \"yes\", \"awesome\", \"good\", \"agree\", \"accept\", \"cool\", \"hand\", \"like\" ],\n    char: \"\\ud83d\\udc4d\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  \"-1\": {\n    keywords: [ \"thumbsdown\", \"no\", \"dislike\", \"hand\" ],\n    char: \"\\ud83d\\udc4e\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  facepunch: {\n    keywords: [ \"angry\", \"violence\", \"fist\", \"hit\", \"attack\", \"hand\" ],\n    char: \"\\ud83d\\udc4a\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  fist: {\n    keywords: [ \"fingers\", \"hand\", \"grasp\" ],\n    char: \"\\u270a\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  fist_left: {\n    keywords: [ \"hand\", \"fistbump\" ],\n    char: \"\\ud83e\\udd1b\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  fist_right: {\n    keywords: [ \"hand\", \"fistbump\" ],\n    char: \"\\ud83e\\udd1c\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  v: {\n    keywords: [ \"fingers\", \"ohyeah\", \"hand\", \"peace\", \"victory\", \"two\" ],\n    char: \"\\u270c\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  ok_hand: {\n    keywords: [ \"fingers\", \"limbs\", \"perfect\", \"ok\", \"okay\" ],\n    char: \"\\ud83d\\udc4c\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  raised_hand: {\n    keywords: [ \"fingers\", \"stop\", \"highfive\", \"palm\", \"ban\" ],\n    char: \"\\u270b\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  raised_back_of_hand: {\n    keywords: [ \"fingers\", \"raised\", \"backhand\" ],\n    char: \"\\ud83e\\udd1a\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  open_hands: {\n    keywords: [ \"fingers\", \"butterfly\", \"hands\", \"open\" ],\n    char: \"\\ud83d\\udc50\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  muscle: {\n    keywords: [ \"arm\", \"flex\", \"hand\", \"summer\", \"strong\", \"biceps\" ],\n    char: \"\\ud83d\\udcaa\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  pray: {\n    keywords: [ \"please\", \"hope\", \"wish\", \"namaste\", \"highfive\" ],\n    char: \"\\ud83d\\ude4f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  foot: {\n    keywords: [ \"kick\", \"stomp\" ],\n    char: \"\\ud83e\\uddb6\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  leg: {\n    keywords: [ \"kick\", \"limb\" ],\n    char: \"\\ud83e\\uddb5\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  handshake: {\n    keywords: [ \"agreement\", \"shake\" ],\n    char: \"\\ud83e\\udd1d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  point_up: {\n    keywords: [ \"hand\", \"fingers\", \"direction\", \"up\" ],\n    char: \"\\u261d\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  point_up_2: {\n    keywords: [ \"fingers\", \"hand\", \"direction\", \"up\" ],\n    char: \"\\ud83d\\udc46\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  point_down: {\n    keywords: [ \"fingers\", \"hand\", \"direction\", \"down\" ],\n    char: \"\\ud83d\\udc47\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  point_left: {\n    keywords: [ \"direction\", \"fingers\", \"hand\", \"left\" ],\n    char: \"\\ud83d\\udc48\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  point_right: {\n    keywords: [ \"fingers\", \"hand\", \"direction\", \"right\" ],\n    char: \"\\ud83d\\udc49\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  fu: {\n    keywords: [ \"hand\", \"fingers\", \"rude\", \"middle\", \"flipping\" ],\n    char: \"\\ud83d\\udd95\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  raised_hand_with_fingers_splayed: {\n    keywords: [ \"hand\", \"fingers\", \"palm\" ],\n    char: \"\\ud83d\\udd90\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  love_you: {\n    keywords: [ \"hand\", \"fingers\", \"gesture\" ],\n    char: \"\\ud83e\\udd1f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  metal: {\n    keywords: [ \"hand\", \"fingers\", \"evil_eye\", \"sign_of_horns\", \"rock_on\" ],\n    char: \"\\ud83e\\udd18\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  crossed_fingers: {\n    keywords: [ \"good\", \"lucky\" ],\n    char: \"\\ud83e\\udd1e\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  vulcan_salute: {\n    keywords: [ \"hand\", \"fingers\", \"spock\", \"star trek\" ],\n    char: \"\\ud83d\\udd96\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  writing_hand: {\n    keywords: [ \"lower_left_ballpoint_pen\", \"stationery\", \"write\", \"compose\" ],\n    char: \"\\u270d\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  selfie: {\n    keywords: [ \"camera\", \"phone\" ],\n    char: \"\\ud83e\\udd33\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  nail_care: {\n    keywords: [ \"beauty\", \"manicure\", \"finger\", \"fashion\", \"nail\" ],\n    char: \"\\ud83d\\udc85\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  lips: {\n    keywords: [ \"mouth\", \"kiss\" ],\n    char: \"\\ud83d\\udc44\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  tooth: {\n    keywords: [ \"teeth\", \"dentist\" ],\n    char: \"\\ud83e\\uddb7\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  tongue: {\n    keywords: [ \"mouth\", \"playful\" ],\n    char: \"\\ud83d\\udc45\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  ear: {\n    keywords: [ \"face\", \"hear\", \"sound\", \"listen\" ],\n    char: \"\\ud83d\\udc42\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  nose: {\n    keywords: [ \"smell\", \"sniff\" ],\n    char: \"\\ud83d\\udc43\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  eye: {\n    keywords: [ \"face\", \"look\", \"see\", \"watch\", \"stare\" ],\n    char: \"\\ud83d\\udc41\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  eyes: {\n    keywords: [ \"look\", \"watch\", \"stalk\", \"peek\", \"see\" ],\n    char: \"\\ud83d\\udc40\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  brain: {\n    keywords: [ \"smart\", \"intelligent\" ],\n    char: \"\\ud83e\\udde0\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  bust_in_silhouette: {\n    keywords: [ \"user\", \"person\", \"human\" ],\n    char: \"\\ud83d\\udc64\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  busts_in_silhouette: {\n    keywords: [ \"user\", \"person\", \"human\", \"group\", \"team\" ],\n    char: \"\\ud83d\\udc65\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  speaking_head: {\n    keywords: [ \"user\", \"person\", \"human\", \"sing\", \"say\", \"talk\" ],\n    char: \"\\ud83d\\udde3\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  baby: {\n    keywords: [ \"child\", \"boy\", \"girl\", \"toddler\" ],\n    char: \"\\ud83d\\udc76\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  child: {\n    keywords: [ \"gender-neutral\", \"young\" ],\n    char: \"\\ud83e\\uddd2\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  boy: {\n    keywords: [ \"man\", \"male\", \"guy\", \"teenager\" ],\n    char: \"\\ud83d\\udc66\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  girl: {\n    keywords: [ \"female\", \"woman\", \"teenager\" ],\n    char: \"\\ud83d\\udc67\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  adult: {\n    keywords: [ \"gender-neutral\", \"person\" ],\n    char: \"\\ud83e\\uddd1\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man: {\n    keywords: [ \"mustache\", \"father\", \"dad\", \"guy\", \"classy\", \"sir\", \"moustache\" ],\n    char: \"\\ud83d\\udc68\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman: {\n    keywords: [ \"female\", \"girls\", \"lady\" ],\n    char: \"\\ud83d\\udc69\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  blonde_woman: {\n    keywords: [ \"woman\", \"female\", \"girl\", \"blonde\", \"person\" ],\n    char: \"\\ud83d\\udc71\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  blonde_man: {\n    keywords: [ \"man\", \"male\", \"boy\", \"blonde\", \"guy\", \"person\" ],\n    char: \"\\ud83d\\udc71\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  bearded_person: {\n    keywords: [ \"person\", \"bewhiskered\" ],\n    char: \"\\ud83e\\uddd4\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  older_adult: {\n    keywords: [ \"human\", \"elder\", \"senior\", \"gender-neutral\" ],\n    char: \"\\ud83e\\uddd3\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  older_man: {\n    keywords: [ \"human\", \"male\", \"men\", \"old\", \"elder\", \"senior\" ],\n    char: \"\\ud83d\\udc74\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  older_woman: {\n    keywords: [ \"human\", \"female\", \"women\", \"lady\", \"old\", \"elder\", \"senior\" ],\n    char: \"\\ud83d\\udc75\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_with_gua_pi_mao: {\n    keywords: [ \"male\", \"boy\", \"chinese\" ],\n    char: \"\\ud83d\\udc72\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_with_headscarf: {\n    keywords: [ \"female\", \"hijab\", \"mantilla\", \"tichel\" ],\n    char: \"\\ud83e\\uddd5\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_with_turban: {\n    keywords: [ \"female\", \"indian\", \"hinduism\", \"arabs\", \"woman\" ],\n    char: \"\\ud83d\\udc73\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_with_turban: {\n    keywords: [ \"male\", \"indian\", \"hinduism\", \"arabs\" ],\n    char: \"\\ud83d\\udc73\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  policewoman: {\n    keywords: [ \"woman\", \"police\", \"law\", \"legal\", \"enforcement\", \"arrest\", \"911\", \"female\" ],\n    char: \"\\ud83d\\udc6e\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  policeman: {\n    keywords: [ \"man\", \"police\", \"law\", \"legal\", \"enforcement\", \"arrest\", \"911\" ],\n    char: \"\\ud83d\\udc6e\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  construction_worker_woman: {\n    keywords: [ \"female\", \"human\", \"wip\", \"build\", \"construction\", \"worker\", \"labor\", \"woman\" ],\n    char: \"\\ud83d\\udc77\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  construction_worker_man: {\n    keywords: [ \"male\", \"human\", \"wip\", \"guy\", \"build\", \"construction\", \"worker\", \"labor\" ],\n    char: \"\\ud83d\\udc77\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  guardswoman: {\n    keywords: [ \"uk\", \"gb\", \"british\", \"female\", \"royal\", \"woman\" ],\n    char: \"\\ud83d\\udc82\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  guardsman: {\n    keywords: [ \"uk\", \"gb\", \"british\", \"male\", \"guy\", \"royal\" ],\n    char: \"\\ud83d\\udc82\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  female_detective: {\n    keywords: [ \"human\", \"spy\", \"detective\", \"female\", \"woman\" ],\n    char: \"\\ud83d\\udd75\\ufe0f\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  male_detective: {\n    keywords: [ \"human\", \"spy\", \"detective\" ],\n    char: \"\\ud83d\\udd75\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_health_worker: {\n    keywords: [ \"doctor\", \"nurse\", \"therapist\", \"healthcare\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\u2695\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_health_worker: {\n    keywords: [ \"doctor\", \"nurse\", \"therapist\", \"healthcare\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\u2695\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_farmer: {\n    keywords: [ \"rancher\", \"gardener\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83c\\udf3e\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_farmer: {\n    keywords: [ \"rancher\", \"gardener\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83c\\udf3e\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_cook: {\n    keywords: [ \"chef\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83c\\udf73\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_cook: {\n    keywords: [ \"chef\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83c\\udf73\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_student: {\n    keywords: [ \"graduate\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83c\\udf93\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_student: {\n    keywords: [ \"graduate\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83c\\udf93\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_singer: {\n    keywords: [ \"rockstar\", \"entertainer\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83c\\udfa4\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_singer: {\n    keywords: [ \"rockstar\", \"entertainer\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83c\\udfa4\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_teacher: {\n    keywords: [ \"instructor\", \"professor\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83c\\udfeb\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_teacher: {\n    keywords: [ \"instructor\", \"professor\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83c\\udfeb\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_factory_worker: {\n    keywords: [ \"assembly\", \"industrial\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83c\\udfed\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_factory_worker: {\n    keywords: [ \"assembly\", \"industrial\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83c\\udfed\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_technologist: {\n    keywords: [ \"coder\", \"developer\", \"engineer\", \"programmer\", \"software\", \"woman\", \"human\", \"laptop\", \"computer\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udcbb\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_technologist: {\n    keywords: [ \"coder\", \"developer\", \"engineer\", \"programmer\", \"software\", \"man\", \"human\", \"laptop\", \"computer\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udcbb\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_office_worker: {\n    keywords: [ \"business\", \"manager\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udcbc\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_office_worker: {\n    keywords: [ \"business\", \"manager\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udcbc\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_mechanic: {\n    keywords: [ \"plumber\", \"woman\", \"human\", \"wrench\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udd27\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_mechanic: {\n    keywords: [ \"plumber\", \"man\", \"human\", \"wrench\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udd27\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_scientist: {\n    keywords: [ \"biologist\", \"chemist\", \"engineer\", \"physicist\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udd2c\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_scientist: {\n    keywords: [ \"biologist\", \"chemist\", \"engineer\", \"physicist\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udd2c\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_artist: {\n    keywords: [ \"painter\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83c\\udfa8\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_artist: {\n    keywords: [ \"painter\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83c\\udfa8\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_firefighter: {\n    keywords: [ \"fireman\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\ude92\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_firefighter: {\n    keywords: [ \"fireman\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\ude92\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_pilot: {\n    keywords: [ \"aviator\", \"plane\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\u2708\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_pilot: {\n    keywords: [ \"aviator\", \"plane\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\u2708\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_astronaut: {\n    keywords: [ \"space\", \"rocket\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\ude80\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_astronaut: {\n    keywords: [ \"space\", \"rocket\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\ude80\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_judge: {\n    keywords: [ \"justice\", \"court\", \"woman\", \"human\" ],\n    char: \"\\ud83d\\udc69\\u200d\\u2696\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_judge: {\n    keywords: [ \"justice\", \"court\", \"man\", \"human\" ],\n    char: \"\\ud83d\\udc68\\u200d\\u2696\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_superhero: {\n    keywords: [ \"woman\", \"female\", \"good\", \"heroine\", \"superpowers\" ],\n    char: \"\\ud83e\\uddb8\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_superhero: {\n    keywords: [ \"man\", \"male\", \"good\", \"hero\", \"superpowers\" ],\n    char: \"\\ud83e\\uddb8\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_supervillain: {\n    keywords: [ \"woman\", \"female\", \"evil\", \"bad\", \"criminal\", \"heroine\", \"superpowers\" ],\n    char: \"\\ud83e\\uddb9\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_supervillain: {\n    keywords: [ \"man\", \"male\", \"evil\", \"bad\", \"criminal\", \"hero\", \"superpowers\" ],\n    char: \"\\ud83e\\uddb9\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  mrs_claus: {\n    keywords: [ \"woman\", \"female\", \"xmas\", \"mother christmas\" ],\n    char: \"\\ud83e\\udd36\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  santa: {\n    keywords: [ \"festival\", \"man\", \"male\", \"xmas\", \"father christmas\" ],\n    char: \"\\ud83c\\udf85\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  sorceress: {\n    keywords: [ \"woman\", \"female\", \"mage\", \"witch\" ],\n    char: \"\\ud83e\\uddd9\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  wizard: {\n    keywords: [ \"man\", \"male\", \"mage\", \"sorcerer\" ],\n    char: \"\\ud83e\\uddd9\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_elf: {\n    keywords: [ \"woman\", \"female\" ],\n    char: \"\\ud83e\\udddd\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_elf: {\n    keywords: [ \"man\", \"male\" ],\n    char: \"\\ud83e\\udddd\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_vampire: {\n    keywords: [ \"woman\", \"female\" ],\n    char: \"\\ud83e\\udddb\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_vampire: {\n    keywords: [ \"man\", \"male\", \"dracula\" ],\n    char: \"\\ud83e\\udddb\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_zombie: {\n    keywords: [ \"woman\", \"female\", \"undead\", \"walking dead\" ],\n    char: \"\\ud83e\\udddf\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  man_zombie: {\n    keywords: [ \"man\", \"male\", \"dracula\", \"undead\", \"walking dead\" ],\n    char: \"\\ud83e\\udddf\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  woman_genie: {\n    keywords: [ \"woman\", \"female\" ],\n    char: \"\\ud83e\\uddde\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  man_genie: {\n    keywords: [ \"man\", \"male\" ],\n    char: \"\\ud83e\\uddde\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  mermaid: {\n    keywords: [ \"woman\", \"female\", \"merwoman\", \"ariel\" ],\n    char: \"\\ud83e\\udddc\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  merman: {\n    keywords: [ \"man\", \"male\", \"triton\" ],\n    char: \"\\ud83e\\udddc\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_fairy: {\n    keywords: [ \"woman\", \"female\" ],\n    char: \"\\ud83e\\uddda\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_fairy: {\n    keywords: [ \"man\", \"male\" ],\n    char: \"\\ud83e\\uddda\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  angel: {\n    keywords: [ \"heaven\", \"wings\", \"halo\" ],\n    char: \"\\ud83d\\udc7c\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  pregnant_woman: {\n    keywords: [ \"baby\" ],\n    char: \"\\ud83e\\udd30\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  breastfeeding: {\n    keywords: [ \"nursing\", \"baby\" ],\n    char: \"\\ud83e\\udd31\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  princess: {\n    keywords: [ \"girl\", \"woman\", \"female\", \"blond\", \"crown\", \"royal\", \"queen\" ],\n    char: \"\\ud83d\\udc78\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  prince: {\n    keywords: [ \"boy\", \"man\", \"male\", \"crown\", \"royal\", \"king\" ],\n    char: \"\\ud83e\\udd34\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  bride_with_veil: {\n    keywords: [ \"couple\", \"marriage\", \"wedding\", \"woman\", \"bride\" ],\n    char: \"\\ud83d\\udc70\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_in_tuxedo: {\n    keywords: [ \"couple\", \"marriage\", \"wedding\", \"groom\" ],\n    char: \"\\ud83e\\udd35\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  running_woman: {\n    keywords: [ \"woman\", \"walking\", \"exercise\", \"race\", \"running\", \"female\" ],\n    char: \"\\ud83c\\udfc3\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  running_man: {\n    keywords: [ \"man\", \"walking\", \"exercise\", \"race\", \"running\" ],\n    char: \"\\ud83c\\udfc3\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  walking_woman: {\n    keywords: [ \"human\", \"feet\", \"steps\", \"woman\", \"female\" ],\n    char: \"\\ud83d\\udeb6\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  walking_man: {\n    keywords: [ \"human\", \"feet\", \"steps\" ],\n    char: \"\\ud83d\\udeb6\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  dancer: {\n    keywords: [ \"female\", \"girl\", \"woman\", \"fun\" ],\n    char: \"\\ud83d\\udc83\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_dancing: {\n    keywords: [ \"male\", \"boy\", \"fun\", \"dancer\" ],\n    char: \"\\ud83d\\udd7a\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  dancing_women: {\n    keywords: [ \"female\", \"bunny\", \"women\", \"girls\" ],\n    char: \"\\ud83d\\udc6f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  dancing_men: {\n    keywords: [ \"male\", \"bunny\", \"men\", \"boys\" ],\n    char: \"\\ud83d\\udc6f\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  couple: {\n    keywords: [ \"pair\", \"people\", \"human\", \"love\", \"date\", \"dating\", \"like\", \"affection\", \"valentines\", \"marriage\" ],\n    char: \"\\ud83d\\udc6b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  two_men_holding_hands: {\n    keywords: [ \"pair\", \"couple\", \"love\", \"like\", \"bromance\", \"friendship\", \"people\", \"human\" ],\n    char: \"\\ud83d\\udc6c\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  two_women_holding_hands: {\n    keywords: [ \"pair\", \"friendship\", \"couple\", \"love\", \"like\", \"female\", \"people\", \"human\" ],\n    char: \"\\ud83d\\udc6d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  bowing_woman: {\n    keywords: [ \"woman\", \"female\", \"girl\" ],\n    char: \"\\ud83d\\ude47\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  bowing_man: {\n    keywords: [ \"man\", \"male\", \"boy\" ],\n    char: \"\\ud83d\\ude47\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_facepalming: {\n    keywords: [ \"man\", \"male\", \"boy\", \"disbelief\" ],\n    char: \"\\ud83e\\udd26\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_facepalming: {\n    keywords: [ \"woman\", \"female\", \"girl\", \"disbelief\" ],\n    char: \"\\ud83e\\udd26\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_shrugging: {\n    keywords: [ \"woman\", \"female\", \"girl\", \"confused\", \"indifferent\", \"doubt\" ],\n    char: \"\\ud83e\\udd37\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_shrugging: {\n    keywords: [ \"man\", \"male\", \"boy\", \"confused\", \"indifferent\", \"doubt\" ],\n    char: \"\\ud83e\\udd37\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  tipping_hand_woman: {\n    keywords: [ \"female\", \"girl\", \"woman\", \"human\", \"information\" ],\n    char: \"\\ud83d\\udc81\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  tipping_hand_man: {\n    keywords: [ \"male\", \"boy\", \"man\", \"human\", \"information\" ],\n    char: \"\\ud83d\\udc81\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  no_good_woman: {\n    keywords: [ \"female\", \"girl\", \"woman\", \"nope\" ],\n    char: \"\\ud83d\\ude45\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  no_good_man: {\n    keywords: [ \"male\", \"boy\", \"man\", \"nope\" ],\n    char: \"\\ud83d\\ude45\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  ok_woman: {\n    keywords: [ \"women\", \"girl\", \"female\", \"pink\", \"human\", \"woman\" ],\n    char: \"\\ud83d\\ude46\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  ok_man: {\n    keywords: [ \"men\", \"boy\", \"male\", \"blue\", \"human\", \"man\" ],\n    char: \"\\ud83d\\ude46\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  raising_hand_woman: {\n    keywords: [ \"female\", \"girl\", \"woman\" ],\n    char: \"\\ud83d\\ude4b\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  raising_hand_man: {\n    keywords: [ \"male\", \"boy\", \"man\" ],\n    char: \"\\ud83d\\ude4b\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  pouting_woman: {\n    keywords: [ \"female\", \"girl\", \"woman\" ],\n    char: \"\\ud83d\\ude4e\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  pouting_man: {\n    keywords: [ \"male\", \"boy\", \"man\" ],\n    char: \"\\ud83d\\ude4e\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  frowning_woman: {\n    keywords: [ \"female\", \"girl\", \"woman\", \"sad\", \"depressed\", \"discouraged\", \"unhappy\" ],\n    char: \"\\ud83d\\ude4d\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  frowning_man: {\n    keywords: [ \"male\", \"boy\", \"man\", \"sad\", \"depressed\", \"discouraged\", \"unhappy\" ],\n    char: \"\\ud83d\\ude4d\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  haircut_woman: {\n    keywords: [ \"female\", \"girl\", \"woman\" ],\n    char: \"\\ud83d\\udc87\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  haircut_man: {\n    keywords: [ \"male\", \"boy\", \"man\" ],\n    char: \"\\ud83d\\udc87\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  massage_woman: {\n    keywords: [ \"female\", \"girl\", \"woman\", \"head\" ],\n    char: \"\\ud83d\\udc86\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  massage_man: {\n    keywords: [ \"male\", \"boy\", \"man\", \"head\" ],\n    char: \"\\ud83d\\udc86\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  woman_in_steamy_room: {\n    keywords: [ \"female\", \"woman\", \"spa\", \"steamroom\", \"sauna\" ],\n    char: \"\\ud83e\\uddd6\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  man_in_steamy_room: {\n    keywords: [ \"male\", \"man\", \"spa\", \"steamroom\", \"sauna\" ],\n    char: \"\\ud83e\\uddd6\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"people\"\n  },\n  couple_with_heart_woman_man: {\n    keywords: [ \"pair\", \"love\", \"like\", \"affection\", \"human\", \"dating\", \"valentines\", \"marriage\" ],\n    char: \"\\ud83d\\udc91\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  couple_with_heart_woman_woman: {\n    keywords: [ \"pair\", \"love\", \"like\", \"affection\", \"human\", \"dating\", \"valentines\", \"marriage\" ],\n    char: \"\\ud83d\\udc69\\u200d\\u2764\\ufe0f\\u200d\\ud83d\\udc69\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  couple_with_heart_man_man: {\n    keywords: [ \"pair\", \"love\", \"like\", \"affection\", \"human\", \"dating\", \"valentines\", \"marriage\" ],\n    char: \"\\ud83d\\udc68\\u200d\\u2764\\ufe0f\\u200d\\ud83d\\udc68\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  couplekiss_man_woman: {\n    keywords: [ \"pair\", \"valentines\", \"love\", \"like\", \"dating\", \"marriage\" ],\n    char: \"\\ud83d\\udc8f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  couplekiss_woman_woman: {\n    keywords: [ \"pair\", \"valentines\", \"love\", \"like\", \"dating\", \"marriage\" ],\n    char: \"\\ud83d\\udc69\\u200d\\u2764\\ufe0f\\u200d\\ud83d\\udc8b\\u200d\\ud83d\\udc69\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  couplekiss_man_man: {\n    keywords: [ \"pair\", \"valentines\", \"love\", \"like\", \"dating\", \"marriage\" ],\n    char: \"\\ud83d\\udc68\\u200d\\u2764\\ufe0f\\u200d\\ud83d\\udc8b\\u200d\\ud83d\\udc68\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_woman_boy: {\n    keywords: [ \"home\", \"parents\", \"child\", \"mom\", \"dad\", \"father\", \"mother\", \"people\", \"human\" ],\n    char: \"\\ud83d\\udc6a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_woman_girl: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"child\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_woman_girl_boy: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_woman_boy_boy: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc66\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_woman_girl_girl: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_woman_boy: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_woman_girl: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_woman_girl_boy: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_woman_boy_boy: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc66\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_woman_girl_girl: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc69\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_man_boy: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc68\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_man_girl: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc68\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_man_girl_boy: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc68\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_man_boy_boy: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc68\\u200d\\ud83d\\udc66\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_man_girl_girl: {\n    keywords: [ \"home\", \"parents\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc68\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_boy: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"child\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_girl: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"child\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_girl_boy: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_boy_boy: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc66\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_woman_girl_girl: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc69\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_boy: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"child\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_girl: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"child\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_girl_boy: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_boy_boy: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc66\\u200d\\ud83d\\udc66\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  family_man_girl_girl: {\n    keywords: [ \"home\", \"parent\", \"people\", \"human\", \"children\" ],\n    char: \"\\ud83d\\udc68\\u200d\\ud83d\\udc67\\u200d\\ud83d\\udc67\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  yarn: {\n    keywords: [ \"ball\", \"crochet\", \"knit\" ],\n    char: \"\\ud83e\\uddf6\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  thread: {\n    keywords: [ \"needle\", \"sewing\", \"spool\", \"string\" ],\n    char: \"\\ud83e\\uddf5\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  coat: {\n    keywords: [ \"jacket\" ],\n    char: \"\\ud83e\\udde5\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  labcoat: {\n    keywords: [ \"doctor\", \"experiment\", \"scientist\", \"chemist\" ],\n    char: \"\\ud83e\\udd7c\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  womans_clothes: {\n    keywords: [ \"fashion\", \"shopping_bags\", \"female\" ],\n    char: \"\\ud83d\\udc5a\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  tshirt: {\n    keywords: [ \"fashion\", \"cloth\", \"casual\", \"shirt\", \"tee\" ],\n    char: \"\\ud83d\\udc55\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  jeans: {\n    keywords: [ \"fashion\", \"shopping\" ],\n    char: \"\\ud83d\\udc56\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  necktie: {\n    keywords: [ \"shirt\", \"suitup\", \"formal\", \"fashion\", \"cloth\", \"business\" ],\n    char: \"\\ud83d\\udc54\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  dress: {\n    keywords: [ \"clothes\", \"fashion\", \"shopping\" ],\n    char: \"\\ud83d\\udc57\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  bikini: {\n    keywords: [ \"swimming\", \"female\", \"woman\", \"girl\", \"fashion\", \"beach\", \"summer\" ],\n    char: \"\\ud83d\\udc59\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  kimono: {\n    keywords: [ \"dress\", \"fashion\", \"women\", \"female\", \"japanese\" ],\n    char: \"\\ud83d\\udc58\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  lipstick: {\n    keywords: [ \"female\", \"girl\", \"fashion\", \"woman\" ],\n    char: \"\\ud83d\\udc84\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  kiss: {\n    keywords: [ \"face\", \"lips\", \"love\", \"like\", \"affection\", \"valentines\" ],\n    char: \"\\ud83d\\udc8b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  footprints: {\n    keywords: [ \"feet\", \"tracking\", \"walking\", \"beach\" ],\n    char: \"\\ud83d\\udc63\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  flat_shoe: {\n    keywords: [ \"ballet\", \"slip-on\", \"slipper\" ],\n    char: \"\\ud83e\\udd7f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  high_heel: {\n    keywords: [ \"fashion\", \"shoes\", \"female\", \"pumps\", \"stiletto\" ],\n    char: \"\\ud83d\\udc60\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  sandal: {\n    keywords: [ \"shoes\", \"fashion\", \"flip flops\" ],\n    char: \"\\ud83d\\udc61\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  boot: {\n    keywords: [ \"shoes\", \"fashion\" ],\n    char: \"\\ud83d\\udc62\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  mans_shoe: {\n    keywords: [ \"fashion\", \"male\" ],\n    char: \"\\ud83d\\udc5e\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  athletic_shoe: {\n    keywords: [ \"shoes\", \"sports\", \"sneakers\" ],\n    char: \"\\ud83d\\udc5f\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  hiking_boot: {\n    keywords: [ \"backpacking\", \"camping\", \"hiking\" ],\n    char: \"\\ud83e\\udd7e\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  socks: {\n    keywords: [ \"stockings\", \"clothes\" ],\n    char: \"\\ud83e\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  gloves: {\n    keywords: [ \"hands\", \"winter\", \"clothes\" ],\n    char: \"\\ud83e\\udde4\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  scarf: {\n    keywords: [ \"neck\", \"winter\", \"clothes\" ],\n    char: \"\\ud83e\\udde3\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  womans_hat: {\n    keywords: [ \"fashion\", \"accessories\", \"female\", \"lady\", \"spring\" ],\n    char: \"\\ud83d\\udc52\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  tophat: {\n    keywords: [ \"magic\", \"gentleman\", \"classy\", \"circus\" ],\n    char: \"\\ud83c\\udfa9\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  billed_hat: {\n    keywords: [ \"cap\", \"baseball\" ],\n    char: \"\\ud83e\\udde2\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  rescue_worker_helmet: {\n    keywords: [ \"construction\", \"build\" ],\n    char: \"\\u26d1\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  mortar_board: {\n    keywords: [ \"school\", \"college\", \"degree\", \"university\", \"graduation\", \"cap\", \"hat\", \"legal\", \"learn\", \"education\" ],\n    char: \"\\ud83c\\udf93\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  crown: {\n    keywords: [ \"king\", \"kod\", \"leader\", \"royalty\", \"lord\" ],\n    char: \"\\ud83d\\udc51\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  school_satchel: {\n    keywords: [ \"student\", \"education\", \"bag\", \"backpack\" ],\n    char: \"\\ud83c\\udf92\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  luggage: {\n    keywords: [ \"packing\", \"travel\" ],\n    char: \"\\ud83e\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  pouch: {\n    keywords: [ \"bag\", \"accessories\", \"shopping\" ],\n    char: \"\\ud83d\\udc5d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  purse: {\n    keywords: [ \"fashion\", \"accessories\", \"money\", \"sales\", \"shopping\" ],\n    char: \"\\ud83d\\udc5b\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  handbag: {\n    keywords: [ \"fashion\", \"accessory\", \"accessories\", \"shopping\" ],\n    char: \"\\ud83d\\udc5c\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  briefcase: {\n    keywords: [ \"business\", \"documents\", \"work\", \"law\", \"legal\", \"job\", \"career\" ],\n    char: \"\\ud83d\\udcbc\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  eyeglasses: {\n    keywords: [ \"fashion\", \"accessories\", \"eyesight\", \"nerdy\", \"dork\", \"geek\" ],\n    char: \"\\ud83d\\udc53\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  dark_sunglasses: {\n    keywords: [ \"face\", \"cool\", \"accessories\" ],\n    char: \"\\ud83d\\udd76\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  goggles: {\n    keywords: [ \"eyes\", \"protection\", \"safety\" ],\n    char: \"\\ud83e\\udd7d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  ring: {\n    keywords: [ \"wedding\", \"propose\", \"marriage\", \"valentines\", \"diamond\", \"fashion\", \"jewelry\", \"gem\", \"engagement\" ],\n    char: \"\\ud83d\\udc8d\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  closed_umbrella: {\n    keywords: [ \"weather\", \"rain\", \"drizzle\" ],\n    char: \"\\ud83c\\udf02\",\n    fitzpatrick_scale: false,\n    category: \"people\"\n  },\n  dog: {\n    keywords: [ \"animal\", \"friend\", \"nature\", \"woof\", \"puppy\", \"pet\", \"faithful\" ],\n    char: \"\\ud83d\\udc36\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cat: {\n    keywords: [ \"animal\", \"meow\", \"nature\", \"pet\", \"kitten\" ],\n    char: \"\\ud83d\\udc31\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  mouse: {\n    keywords: [ \"animal\", \"nature\", \"cheese_wedge\", \"rodent\" ],\n    char: \"\\ud83d\\udc2d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  hamster: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83d\\udc39\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  rabbit: {\n    keywords: [ \"animal\", \"nature\", \"pet\", \"spring\", \"magic\", \"bunny\" ],\n    char: \"\\ud83d\\udc30\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  fox_face: {\n    keywords: [ \"animal\", \"nature\", \"face\" ],\n    char: \"\\ud83e\\udd8a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  bear: {\n    keywords: [ \"animal\", \"nature\", \"wild\" ],\n    char: \"\\ud83d\\udc3b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  panda_face: {\n    keywords: [ \"animal\", \"nature\", \"panda\" ],\n    char: \"\\ud83d\\udc3c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  koala: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83d\\udc28\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  tiger: {\n    keywords: [ \"animal\", \"cat\", \"danger\", \"wild\", \"nature\", \"roar\" ],\n    char: \"\\ud83d\\udc2f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  lion: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83e\\udd81\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cow: {\n    keywords: [ \"beef\", \"ox\", \"animal\", \"nature\", \"moo\", \"milk\" ],\n    char: \"\\ud83d\\udc2e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  pig: {\n    keywords: [ \"animal\", \"oink\", \"nature\" ],\n    char: \"\\ud83d\\udc37\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  pig_nose: {\n    keywords: [ \"animal\", \"oink\" ],\n    char: \"\\ud83d\\udc3d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  frog: {\n    keywords: [ \"animal\", \"nature\", \"croak\", \"toad\" ],\n    char: \"\\ud83d\\udc38\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  squid: {\n    keywords: [ \"animal\", \"nature\", \"ocean\", \"sea\" ],\n    char: \"\\ud83e\\udd91\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  octopus: {\n    keywords: [ \"animal\", \"creature\", \"ocean\", \"sea\", \"nature\", \"beach\" ],\n    char: \"\\ud83d\\udc19\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  shrimp: {\n    keywords: [ \"animal\", \"ocean\", \"nature\", \"seafood\" ],\n    char: \"\\ud83e\\udd90\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  monkey_face: {\n    keywords: [ \"animal\", \"nature\", \"circus\" ],\n    char: \"\\ud83d\\udc35\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  gorilla: {\n    keywords: [ \"animal\", \"nature\", \"circus\" ],\n    char: \"\\ud83e\\udd8d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  see_no_evil: {\n    keywords: [ \"monkey\", \"animal\", \"nature\", \"haha\" ],\n    char: \"\\ud83d\\ude48\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  hear_no_evil: {\n    keywords: [ \"animal\", \"monkey\", \"nature\" ],\n    char: \"\\ud83d\\ude49\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  speak_no_evil: {\n    keywords: [ \"monkey\", \"animal\", \"nature\", \"omg\" ],\n    char: \"\\ud83d\\ude4a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  monkey: {\n    keywords: [ \"animal\", \"nature\", \"banana\", \"circus\" ],\n    char: \"\\ud83d\\udc12\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  chicken: {\n    keywords: [ \"animal\", \"cluck\", \"nature\", \"bird\" ],\n    char: \"\\ud83d\\udc14\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  penguin: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83d\\udc27\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  bird: {\n    keywords: [ \"animal\", \"nature\", \"fly\", \"tweet\", \"spring\" ],\n    char: \"\\ud83d\\udc26\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  baby_chick: {\n    keywords: [ \"animal\", \"chicken\", \"bird\" ],\n    char: \"\\ud83d\\udc24\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  hatching_chick: {\n    keywords: [ \"animal\", \"chicken\", \"egg\", \"born\", \"baby\", \"bird\" ],\n    char: \"\\ud83d\\udc23\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  hatched_chick: {\n    keywords: [ \"animal\", \"chicken\", \"baby\", \"bird\" ],\n    char: \"\\ud83d\\udc25\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  duck: {\n    keywords: [ \"animal\", \"nature\", \"bird\", \"mallard\" ],\n    char: \"\\ud83e\\udd86\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  eagle: {\n    keywords: [ \"animal\", \"nature\", \"bird\" ],\n    char: \"\\ud83e\\udd85\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  owl: {\n    keywords: [ \"animal\", \"nature\", \"bird\", \"hoot\" ],\n    char: \"\\ud83e\\udd89\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  bat: {\n    keywords: [ \"animal\", \"nature\", \"blind\", \"vampire\" ],\n    char: \"\\ud83e\\udd87\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  wolf: {\n    keywords: [ \"animal\", \"nature\", \"wild\" ],\n    char: \"\\ud83d\\udc3a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  boar: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83d\\udc17\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  horse: {\n    keywords: [ \"animal\", \"brown\", \"nature\" ],\n    char: \"\\ud83d\\udc34\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  unicorn: {\n    keywords: [ \"animal\", \"nature\", \"mystical\" ],\n    char: \"\\ud83e\\udd84\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  honeybee: {\n    keywords: [ \"animal\", \"insect\", \"nature\", \"bug\", \"spring\", \"honey\" ],\n    char: \"\\ud83d\\udc1d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  bug: {\n    keywords: [ \"animal\", \"insect\", \"nature\", \"worm\" ],\n    char: \"\\ud83d\\udc1b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  butterfly: {\n    keywords: [ \"animal\", \"insect\", \"nature\", \"caterpillar\" ],\n    char: \"\\ud83e\\udd8b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  snail: {\n    keywords: [ \"slow\", \"animal\", \"shell\" ],\n    char: \"\\ud83d\\udc0c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  beetle: {\n    keywords: [ \"animal\", \"insect\", \"nature\", \"ladybug\" ],\n    char: \"\\ud83d\\udc1e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  ant: {\n    keywords: [ \"animal\", \"insect\", \"nature\", \"bug\" ],\n    char: \"\\ud83d\\udc1c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  grasshopper: {\n    keywords: [ \"animal\", \"cricket\", \"chirp\" ],\n    char: \"\\ud83e\\udd97\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  spider: {\n    keywords: [ \"animal\", \"arachnid\" ],\n    char: \"\\ud83d\\udd77\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  scorpion: {\n    keywords: [ \"animal\", \"arachnid\" ],\n    char: \"\\ud83e\\udd82\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  crab: {\n    keywords: [ \"animal\", \"crustacean\" ],\n    char: \"\\ud83e\\udd80\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  snake: {\n    keywords: [ \"animal\", \"evil\", \"nature\", \"hiss\", \"python\" ],\n    char: \"\\ud83d\\udc0d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  lizard: {\n    keywords: [ \"animal\", \"nature\", \"reptile\" ],\n    char: \"\\ud83e\\udd8e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  \"t-rex\": {\n    keywords: [ \"animal\", \"nature\", \"dinosaur\", \"tyrannosaurus\", \"extinct\" ],\n    char: \"\\ud83e\\udd96\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sauropod: {\n    keywords: [ \"animal\", \"nature\", \"dinosaur\", \"brachiosaurus\", \"brontosaurus\", \"diplodocus\", \"extinct\" ],\n    char: \"\\ud83e\\udd95\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  turtle: {\n    keywords: [ \"animal\", \"slow\", \"nature\", \"tortoise\" ],\n    char: \"\\ud83d\\udc22\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  tropical_fish: {\n    keywords: [ \"animal\", \"swim\", \"ocean\", \"beach\", \"nemo\" ],\n    char: \"\\ud83d\\udc20\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  fish: {\n    keywords: [ \"animal\", \"food\", \"nature\" ],\n    char: \"\\ud83d\\udc1f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  blowfish: {\n    keywords: [ \"animal\", \"nature\", \"food\", \"sea\", \"ocean\" ],\n    char: \"\\ud83d\\udc21\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  dolphin: {\n    keywords: [ \"animal\", \"nature\", \"fish\", \"sea\", \"ocean\", \"flipper\", \"fins\", \"beach\" ],\n    char: \"\\ud83d\\udc2c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  shark: {\n    keywords: [ \"animal\", \"nature\", \"fish\", \"sea\", \"ocean\", \"jaws\", \"fins\", \"beach\" ],\n    char: \"\\ud83e\\udd88\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  whale: {\n    keywords: [ \"animal\", \"nature\", \"sea\", \"ocean\" ],\n    char: \"\\ud83d\\udc33\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  whale2: {\n    keywords: [ \"animal\", \"nature\", \"sea\", \"ocean\" ],\n    char: \"\\ud83d\\udc0b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  crocodile: {\n    keywords: [ \"animal\", \"nature\", \"reptile\", \"lizard\", \"alligator\" ],\n    char: \"\\ud83d\\udc0a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  leopard: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83d\\udc06\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  zebra: {\n    keywords: [ \"animal\", \"nature\", \"stripes\", \"safari\" ],\n    char: \"\\ud83e\\udd93\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  tiger2: {\n    keywords: [ \"animal\", \"nature\", \"roar\" ],\n    char: \"\\ud83d\\udc05\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  water_buffalo: {\n    keywords: [ \"animal\", \"nature\", \"ox\", \"cow\" ],\n    char: \"\\ud83d\\udc03\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  ox: {\n    keywords: [ \"animal\", \"cow\", \"beef\" ],\n    char: \"\\ud83d\\udc02\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cow2: {\n    keywords: [ \"beef\", \"ox\", \"animal\", \"nature\", \"moo\", \"milk\" ],\n    char: \"\\ud83d\\udc04\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  deer: {\n    keywords: [ \"animal\", \"nature\", \"horns\", \"venison\" ],\n    char: \"\\ud83e\\udd8c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  dromedary_camel: {\n    keywords: [ \"animal\", \"hot\", \"desert\", \"hump\" ],\n    char: \"\\ud83d\\udc2a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  camel: {\n    keywords: [ \"animal\", \"nature\", \"hot\", \"desert\", \"hump\" ],\n    char: \"\\ud83d\\udc2b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  giraffe: {\n    keywords: [ \"animal\", \"nature\", \"spots\", \"safari\" ],\n    char: \"\\ud83e\\udd92\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  elephant: {\n    keywords: [ \"animal\", \"nature\", \"nose\", \"th\", \"circus\" ],\n    char: \"\\ud83d\\udc18\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  rhinoceros: {\n    keywords: [ \"animal\", \"nature\", \"horn\" ],\n    char: \"\\ud83e\\udd8f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  goat: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83d\\udc10\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  ram: {\n    keywords: [ \"animal\", \"sheep\", \"nature\" ],\n    char: \"\\ud83d\\udc0f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sheep: {\n    keywords: [ \"animal\", \"nature\", \"wool\", \"shipit\" ],\n    char: \"\\ud83d\\udc11\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  racehorse: {\n    keywords: [ \"animal\", \"gamble\", \"luck\" ],\n    char: \"\\ud83d\\udc0e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  pig2: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83d\\udc16\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  rat: {\n    keywords: [ \"animal\", \"mouse\", \"rodent\" ],\n    char: \"\\ud83d\\udc00\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  mouse2: {\n    keywords: [ \"animal\", \"nature\", \"rodent\" ],\n    char: \"\\ud83d\\udc01\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  rooster: {\n    keywords: [ \"animal\", \"nature\", \"chicken\" ],\n    char: \"\\ud83d\\udc13\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  turkey: {\n    keywords: [ \"animal\", \"bird\" ],\n    char: \"\\ud83e\\udd83\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  dove: {\n    keywords: [ \"animal\", \"bird\" ],\n    char: \"\\ud83d\\udd4a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  dog2: {\n    keywords: [ \"animal\", \"nature\", \"friend\", \"doge\", \"pet\", \"faithful\" ],\n    char: \"\\ud83d\\udc15\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  poodle: {\n    keywords: [ \"dog\", \"animal\", \"101\", \"nature\", \"pet\" ],\n    char: \"\\ud83d\\udc29\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cat2: {\n    keywords: [ \"animal\", \"meow\", \"pet\", \"cats\" ],\n    char: \"\\ud83d\\udc08\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  rabbit2: {\n    keywords: [ \"animal\", \"nature\", \"pet\", \"magic\", \"spring\" ],\n    char: \"\\ud83d\\udc07\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  chipmunk: {\n    keywords: [ \"animal\", \"nature\", \"rodent\", \"squirrel\" ],\n    char: \"\\ud83d\\udc3f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  hedgehog: {\n    keywords: [ \"animal\", \"nature\", \"spiny\" ],\n    char: \"\\ud83e\\udd94\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  raccoon: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83e\\udd9d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  llama: {\n    keywords: [ \"animal\", \"nature\", \"alpaca\" ],\n    char: \"\\ud83e\\udd99\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  hippopotamus: {\n    keywords: [ \"animal\", \"nature\" ],\n    char: \"\\ud83e\\udd9b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  kangaroo: {\n    keywords: [ \"animal\", \"nature\", \"australia\", \"joey\", \"hop\", \"marsupial\" ],\n    char: \"\\ud83e\\udd98\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  badger: {\n    keywords: [ \"animal\", \"nature\", \"honey\" ],\n    char: \"\\ud83e\\udda1\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  swan: {\n    keywords: [ \"animal\", \"nature\", \"bird\" ],\n    char: \"\\ud83e\\udda2\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  peacock: {\n    keywords: [ \"animal\", \"nature\", \"peahen\", \"bird\" ],\n    char: \"\\ud83e\\udd9a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  parrot: {\n    keywords: [ \"animal\", \"nature\", \"bird\", \"pirate\", \"talk\" ],\n    char: \"\\ud83e\\udd9c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  lobster: {\n    keywords: [ \"animal\", \"nature\", \"bisque\", \"claws\", \"seafood\" ],\n    char: \"\\ud83e\\udd9e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  mosquito: {\n    keywords: [ \"animal\", \"nature\", \"insect\", \"malaria\" ],\n    char: \"\\ud83e\\udd9f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  paw_prints: {\n    keywords: [ \"animal\", \"tracking\", \"footprints\", \"dog\", \"cat\", \"pet\", \"feet\" ],\n    char: \"\\ud83d\\udc3e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  dragon: {\n    keywords: [ \"animal\", \"myth\", \"nature\", \"chinese\", \"green\" ],\n    char: \"\\ud83d\\udc09\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  dragon_face: {\n    keywords: [ \"animal\", \"myth\", \"nature\", \"chinese\", \"green\" ],\n    char: \"\\ud83d\\udc32\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cactus: {\n    keywords: [ \"vegetable\", \"plant\", \"nature\" ],\n    char: \"\\ud83c\\udf35\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  christmas_tree: {\n    keywords: [ \"festival\", \"vacation\", \"december\", \"xmas\", \"celebration\" ],\n    char: \"\\ud83c\\udf84\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  evergreen_tree: {\n    keywords: [ \"plant\", \"nature\" ],\n    char: \"\\ud83c\\udf32\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  deciduous_tree: {\n    keywords: [ \"plant\", \"nature\" ],\n    char: \"\\ud83c\\udf33\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  palm_tree: {\n    keywords: [ \"plant\", \"vegetable\", \"nature\", \"summer\", \"beach\", \"mojito\", \"tropical\" ],\n    char: \"\\ud83c\\udf34\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  seedling: {\n    keywords: [ \"plant\", \"nature\", \"grass\", \"lawn\", \"spring\" ],\n    char: \"\\ud83c\\udf31\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  herb: {\n    keywords: [ \"vegetable\", \"plant\", \"medicine\", \"weed\", \"grass\", \"lawn\" ],\n    char: \"\\ud83c\\udf3f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  shamrock: {\n    keywords: [ \"vegetable\", \"plant\", \"nature\", \"irish\", \"clover\" ],\n    char: \"\\u2618\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  four_leaf_clover: {\n    keywords: [ \"vegetable\", \"plant\", \"nature\", \"lucky\", \"irish\" ],\n    char: \"\\ud83c\\udf40\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  bamboo: {\n    keywords: [ \"plant\", \"nature\", \"vegetable\", \"panda\", \"pine_decoration\" ],\n    char: \"\\ud83c\\udf8d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  tanabata_tree: {\n    keywords: [ \"plant\", \"nature\", \"branch\", \"summer\" ],\n    char: \"\\ud83c\\udf8b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  leaves: {\n    keywords: [ \"nature\", \"plant\", \"tree\", \"vegetable\", \"grass\", \"lawn\", \"spring\" ],\n    char: \"\\ud83c\\udf43\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  fallen_leaf: {\n    keywords: [ \"nature\", \"plant\", \"vegetable\", \"leaves\" ],\n    char: \"\\ud83c\\udf42\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  maple_leaf: {\n    keywords: [ \"nature\", \"plant\", \"vegetable\", \"ca\", \"fall\" ],\n    char: \"\\ud83c\\udf41\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  ear_of_rice: {\n    keywords: [ \"nature\", \"plant\" ],\n    char: \"\\ud83c\\udf3e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  hibiscus: {\n    keywords: [ \"plant\", \"vegetable\", \"flowers\", \"beach\" ],\n    char: \"\\ud83c\\udf3a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sunflower: {\n    keywords: [ \"nature\", \"plant\", \"fall\" ],\n    char: \"\\ud83c\\udf3b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  rose: {\n    keywords: [ \"flowers\", \"valentines\", \"love\", \"spring\" ],\n    char: \"\\ud83c\\udf39\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  wilted_flower: {\n    keywords: [ \"plant\", \"nature\", \"flower\" ],\n    char: \"\\ud83e\\udd40\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  tulip: {\n    keywords: [ \"flowers\", \"plant\", \"nature\", \"summer\", \"spring\" ],\n    char: \"\\ud83c\\udf37\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  blossom: {\n    keywords: [ \"nature\", \"flowers\", \"yellow\" ],\n    char: \"\\ud83c\\udf3c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cherry_blossom: {\n    keywords: [ \"nature\", \"plant\", \"spring\", \"flower\" ],\n    char: \"\\ud83c\\udf38\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  bouquet: {\n    keywords: [ \"flowers\", \"nature\", \"spring\" ],\n    char: \"\\ud83d\\udc90\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  mushroom: {\n    keywords: [ \"plant\", \"vegetable\" ],\n    char: \"\\ud83c\\udf44\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  chestnut: {\n    keywords: [ \"food\", \"squirrel\" ],\n    char: \"\\ud83c\\udf30\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  jack_o_lantern: {\n    keywords: [ \"halloween\", \"light\", \"pumpkin\", \"creepy\", \"fall\" ],\n    char: \"\\ud83c\\udf83\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  shell: {\n    keywords: [ \"nature\", \"sea\", \"beach\" ],\n    char: \"\\ud83d\\udc1a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  spider_web: {\n    keywords: [ \"animal\", \"insect\", \"arachnid\", \"silk\" ],\n    char: \"\\ud83d\\udd78\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  earth_americas: {\n    keywords: [ \"globe\", \"world\", \"USA\", \"international\" ],\n    char: \"\\ud83c\\udf0e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  earth_africa: {\n    keywords: [ \"globe\", \"world\", \"international\" ],\n    char: \"\\ud83c\\udf0d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  earth_asia: {\n    keywords: [ \"globe\", \"world\", \"east\", \"international\" ],\n    char: \"\\ud83c\\udf0f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  full_moon: {\n    keywords: [ \"nature\", \"yellow\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf15\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  waning_gibbous_moon: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\", \"waxing_gibbous_moon\" ],\n    char: \"\\ud83c\\udf16\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  last_quarter_moon: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf17\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  waning_crescent_moon: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf18\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  new_moon: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf11\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  waxing_crescent_moon: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf12\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  first_quarter_moon: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf13\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  waxing_gibbous_moon: {\n    keywords: [ \"nature\", \"night\", \"sky\", \"gray\", \"twilight\", \"planet\", \"space\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf14\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  new_moon_with_face: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf1a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  full_moon_with_face: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf1d\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  first_quarter_moon_with_face: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf1b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  last_quarter_moon_with_face: {\n    keywords: [ \"nature\", \"twilight\", \"planet\", \"space\", \"night\", \"evening\", \"sleep\" ],\n    char: \"\\ud83c\\udf1c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sun_with_face: {\n    keywords: [ \"nature\", \"morning\", \"sky\" ],\n    char: \"\\ud83c\\udf1e\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  crescent_moon: {\n    keywords: [ \"night\", \"sleep\", \"sky\", \"evening\", \"magic\" ],\n    char: \"\\ud83c\\udf19\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  star: {\n    keywords: [ \"night\", \"yellow\" ],\n    char: \"\\u2b50\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  star2: {\n    keywords: [ \"night\", \"sparkle\", \"awesome\", \"good\", \"magic\" ],\n    char: \"\\ud83c\\udf1f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  dizzy: {\n    keywords: [ \"star\", \"sparkle\", \"shoot\", \"magic\" ],\n    char: \"\\ud83d\\udcab\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sparkles: {\n    keywords: [ \"stars\", \"shine\", \"shiny\", \"cool\", \"awesome\", \"good\", \"magic\" ],\n    char: \"\\u2728\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  comet: {\n    keywords: [ \"space\" ],\n    char: \"\\u2604\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sunny: {\n    keywords: [ \"weather\", \"nature\", \"brightness\", \"summer\", \"beach\", \"spring\" ],\n    char: \"\\u2600\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sun_behind_small_cloud: {\n    keywords: [ \"weather\" ],\n    char: \"\\ud83c\\udf24\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  partly_sunny: {\n    keywords: [ \"weather\", \"nature\", \"cloudy\", \"morning\", \"fall\", \"spring\" ],\n    char: \"\\u26c5\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sun_behind_large_cloud: {\n    keywords: [ \"weather\" ],\n    char: \"\\ud83c\\udf25\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sun_behind_rain_cloud: {\n    keywords: [ \"weather\" ],\n    char: \"\\ud83c\\udf26\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cloud: {\n    keywords: [ \"weather\", \"sky\" ],\n    char: \"\\u2601\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cloud_with_rain: {\n    keywords: [ \"weather\" ],\n    char: \"\\ud83c\\udf27\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cloud_with_lightning_and_rain: {\n    keywords: [ \"weather\", \"lightning\" ],\n    char: \"\\u26c8\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cloud_with_lightning: {\n    keywords: [ \"weather\", \"thunder\" ],\n    char: \"\\ud83c\\udf29\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  zap: {\n    keywords: [ \"thunder\", \"weather\", \"lightning bolt\", \"fast\" ],\n    char: \"\\u26a1\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  fire: {\n    keywords: [ \"hot\", \"cook\", \"flame\" ],\n    char: \"\\ud83d\\udd25\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  boom: {\n    keywords: [ \"bomb\", \"explode\", \"explosion\", \"collision\", \"blown\" ],\n    char: \"\\ud83d\\udca5\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  snowflake: {\n    keywords: [ \"winter\", \"season\", \"cold\", \"weather\", \"christmas\", \"xmas\" ],\n    char: \"\\u2744\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  cloud_with_snow: {\n    keywords: [ \"weather\" ],\n    char: \"\\ud83c\\udf28\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  snowman: {\n    keywords: [ \"winter\", \"season\", \"cold\", \"weather\", \"christmas\", \"xmas\", \"frozen\", \"without_snow\" ],\n    char: \"\\u26c4\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  snowman_with_snow: {\n    keywords: [ \"winter\", \"season\", \"cold\", \"weather\", \"christmas\", \"xmas\", \"frozen\" ],\n    char: \"\\u2603\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  wind_face: {\n    keywords: [ \"gust\", \"air\" ],\n    char: \"\\ud83c\\udf2c\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  dash: {\n    keywords: [ \"wind\", \"air\", \"fast\", \"shoo\", \"fart\", \"smoke\", \"puff\" ],\n    char: \"\\ud83d\\udca8\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  tornado: {\n    keywords: [ \"weather\", \"cyclone\", \"twister\" ],\n    char: \"\\ud83c\\udf2a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  fog: {\n    keywords: [ \"weather\" ],\n    char: \"\\ud83c\\udf2b\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  open_umbrella: {\n    keywords: [ \"weather\", \"spring\" ],\n    char: \"\\u2602\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  umbrella: {\n    keywords: [ \"rainy\", \"weather\", \"spring\" ],\n    char: \"\\u2614\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  droplet: {\n    keywords: [ \"water\", \"drip\", \"faucet\", \"spring\" ],\n    char: \"\\ud83d\\udca7\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  sweat_drops: {\n    keywords: [ \"water\", \"drip\", \"oops\" ],\n    char: \"\\ud83d\\udca6\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  ocean: {\n    keywords: [ \"sea\", \"water\", \"wave\", \"nature\", \"tsunami\", \"disaster\" ],\n    char: \"\\ud83c\\udf0a\",\n    fitzpatrick_scale: false,\n    category: \"animals_and_nature\"\n  },\n  green_apple: {\n    keywords: [ \"fruit\", \"nature\" ],\n    char: \"\\ud83c\\udf4f\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  apple: {\n    keywords: [ \"fruit\", \"mac\", \"school\" ],\n    char: \"\\ud83c\\udf4e\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  pear: {\n    keywords: [ \"fruit\", \"nature\", \"food\" ],\n    char: \"\\ud83c\\udf50\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  tangerine: {\n    keywords: [ \"food\", \"fruit\", \"nature\", \"orange\" ],\n    char: \"\\ud83c\\udf4a\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  lemon: {\n    keywords: [ \"fruit\", \"nature\" ],\n    char: \"\\ud83c\\udf4b\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  banana: {\n    keywords: [ \"fruit\", \"food\", \"monkey\" ],\n    char: \"\\ud83c\\udf4c\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  watermelon: {\n    keywords: [ \"fruit\", \"food\", \"picnic\", \"summer\" ],\n    char: \"\\ud83c\\udf49\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  grapes: {\n    keywords: [ \"fruit\", \"food\", \"wine\" ],\n    char: \"\\ud83c\\udf47\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  strawberry: {\n    keywords: [ \"fruit\", \"food\", \"nature\" ],\n    char: \"\\ud83c\\udf53\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  melon: {\n    keywords: [ \"fruit\", \"nature\", \"food\" ],\n    char: \"\\ud83c\\udf48\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  cherries: {\n    keywords: [ \"food\", \"fruit\" ],\n    char: \"\\ud83c\\udf52\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  peach: {\n    keywords: [ \"fruit\", \"nature\", \"food\" ],\n    char: \"\\ud83c\\udf51\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  pineapple: {\n    keywords: [ \"fruit\", \"nature\", \"food\" ],\n    char: \"\\ud83c\\udf4d\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  coconut: {\n    keywords: [ \"fruit\", \"nature\", \"food\", \"palm\" ],\n    char: \"\\ud83e\\udd65\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  kiwi_fruit: {\n    keywords: [ \"fruit\", \"food\" ],\n    char: \"\\ud83e\\udd5d\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  mango: {\n    keywords: [ \"fruit\", \"food\", \"tropical\" ],\n    char: \"\\ud83e\\udd6d\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  avocado: {\n    keywords: [ \"fruit\", \"food\" ],\n    char: \"\\ud83e\\udd51\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  broccoli: {\n    keywords: [ \"fruit\", \"food\", \"vegetable\" ],\n    char: \"\\ud83e\\udd66\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  tomato: {\n    keywords: [ \"fruit\", \"vegetable\", \"nature\", \"food\" ],\n    char: \"\\ud83c\\udf45\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  eggplant: {\n    keywords: [ \"vegetable\", \"nature\", \"food\", \"aubergine\" ],\n    char: \"\\ud83c\\udf46\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  cucumber: {\n    keywords: [ \"fruit\", \"food\", \"pickle\" ],\n    char: \"\\ud83e\\udd52\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  carrot: {\n    keywords: [ \"vegetable\", \"food\", \"orange\" ],\n    char: \"\\ud83e\\udd55\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  hot_pepper: {\n    keywords: [ \"food\", \"spicy\", \"chilli\", \"chili\" ],\n    char: \"\\ud83c\\udf36\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  potato: {\n    keywords: [ \"food\", \"tuber\", \"vegatable\", \"starch\" ],\n    char: \"\\ud83e\\udd54\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  corn: {\n    keywords: [ \"food\", \"vegetable\", \"plant\" ],\n    char: \"\\ud83c\\udf3d\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  leafy_greens: {\n    keywords: [ \"food\", \"vegetable\", \"plant\", \"bok choy\", \"cabbage\", \"kale\", \"lettuce\" ],\n    char: \"\\ud83e\\udd6c\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  sweet_potato: {\n    keywords: [ \"food\", \"nature\" ],\n    char: \"\\ud83c\\udf60\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  peanuts: {\n    keywords: [ \"food\", \"nut\" ],\n    char: \"\\ud83e\\udd5c\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  honey_pot: {\n    keywords: [ \"bees\", \"sweet\", \"kitchen\" ],\n    char: \"\\ud83c\\udf6f\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  croissant: {\n    keywords: [ \"food\", \"bread\", \"french\" ],\n    char: \"\\ud83e\\udd50\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  bread: {\n    keywords: [ \"food\", \"wheat\", \"breakfast\", \"toast\" ],\n    char: \"\\ud83c\\udf5e\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  baguette_bread: {\n    keywords: [ \"food\", \"bread\", \"french\" ],\n    char: \"\\ud83e\\udd56\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  bagel: {\n    keywords: [ \"food\", \"bread\", \"bakery\", \"schmear\" ],\n    char: \"\\ud83e\\udd6f\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  pretzel: {\n    keywords: [ \"food\", \"bread\", \"twisted\" ],\n    char: \"\\ud83e\\udd68\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  cheese: {\n    keywords: [ \"food\", \"chadder\" ],\n    char: \"\\ud83e\\uddc0\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  egg: {\n    keywords: [ \"food\", \"chicken\", \"breakfast\" ],\n    char: \"\\ud83e\\udd5a\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  bacon: {\n    keywords: [ \"food\", \"breakfast\", \"pork\", \"pig\", \"meat\" ],\n    char: \"\\ud83e\\udd53\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  steak: {\n    keywords: [ \"food\", \"cow\", \"meat\", \"cut\", \"chop\", \"lambchop\", \"porkchop\" ],\n    char: \"\\ud83e\\udd69\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  pancakes: {\n    keywords: [ \"food\", \"breakfast\", \"flapjacks\", \"hotcakes\" ],\n    char: \"\\ud83e\\udd5e\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  poultry_leg: {\n    keywords: [ \"food\", \"meat\", \"drumstick\", \"bird\", \"chicken\", \"turkey\" ],\n    char: \"\\ud83c\\udf57\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  meat_on_bone: {\n    keywords: [ \"good\", \"food\", \"drumstick\" ],\n    char: \"\\ud83c\\udf56\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  bone: {\n    keywords: [ \"skeleton\" ],\n    char: \"\\ud83e\\uddb4\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  fried_shrimp: {\n    keywords: [ \"food\", \"animal\", \"appetizer\", \"summer\" ],\n    char: \"\\ud83c\\udf64\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  fried_egg: {\n    keywords: [ \"food\", \"breakfast\", \"kitchen\", \"egg\" ],\n    char: \"\\ud83c\\udf73\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  hamburger: {\n    keywords: [ \"meat\", \"fast food\", \"beef\", \"cheeseburger\", \"mcdonalds\", \"burger king\" ],\n    char: \"\\ud83c\\udf54\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  fries: {\n    keywords: [ \"chips\", \"snack\", \"fast food\" ],\n    char: \"\\ud83c\\udf5f\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  stuffed_flatbread: {\n    keywords: [ \"food\", \"flatbread\", \"stuffed\", \"gyro\" ],\n    char: \"\\ud83e\\udd59\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  hotdog: {\n    keywords: [ \"food\", \"frankfurter\" ],\n    char: \"\\ud83c\\udf2d\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  pizza: {\n    keywords: [ \"food\", \"party\" ],\n    char: \"\\ud83c\\udf55\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  sandwich: {\n    keywords: [ \"food\", \"lunch\", \"bread\" ],\n    char: \"\\ud83e\\udd6a\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  canned_food: {\n    keywords: [ \"food\", \"soup\" ],\n    char: \"\\ud83e\\udd6b\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  spaghetti: {\n    keywords: [ \"food\", \"italian\", \"noodle\" ],\n    char: \"\\ud83c\\udf5d\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  taco: {\n    keywords: [ \"food\", \"mexican\" ],\n    char: \"\\ud83c\\udf2e\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  burrito: {\n    keywords: [ \"food\", \"mexican\" ],\n    char: \"\\ud83c\\udf2f\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  green_salad: {\n    keywords: [ \"food\", \"healthy\", \"lettuce\" ],\n    char: \"\\ud83e\\udd57\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  shallow_pan_of_food: {\n    keywords: [ \"food\", \"cooking\", \"casserole\", \"paella\" ],\n    char: \"\\ud83e\\udd58\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  ramen: {\n    keywords: [ \"food\", \"japanese\", \"noodle\", \"chopsticks\" ],\n    char: \"\\ud83c\\udf5c\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  stew: {\n    keywords: [ \"food\", \"meat\", \"soup\" ],\n    char: \"\\ud83c\\udf72\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  fish_cake: {\n    keywords: [ \"food\", \"japan\", \"sea\", \"beach\", \"narutomaki\", \"pink\", \"swirl\", \"kamaboko\", \"surimi\", \"ramen\" ],\n    char: \"\\ud83c\\udf65\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  fortune_cookie: {\n    keywords: [ \"food\", \"prophecy\" ],\n    char: \"\\ud83e\\udd60\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  sushi: {\n    keywords: [ \"food\", \"fish\", \"japanese\", \"rice\" ],\n    char: \"\\ud83c\\udf63\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  bento: {\n    keywords: [ \"food\", \"japanese\", \"box\" ],\n    char: \"\\ud83c\\udf71\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  curry: {\n    keywords: [ \"food\", \"spicy\", \"hot\", \"indian\" ],\n    char: \"\\ud83c\\udf5b\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  rice_ball: {\n    keywords: [ \"food\", \"japanese\" ],\n    char: \"\\ud83c\\udf59\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  rice: {\n    keywords: [ \"food\", \"china\", \"asian\" ],\n    char: \"\\ud83c\\udf5a\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  rice_cracker: {\n    keywords: [ \"food\", \"japanese\" ],\n    char: \"\\ud83c\\udf58\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  oden: {\n    keywords: [ \"food\", \"japanese\" ],\n    char: \"\\ud83c\\udf62\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  dango: {\n    keywords: [ \"food\", \"dessert\", \"sweet\", \"japanese\", \"barbecue\", \"meat\" ],\n    char: \"\\ud83c\\udf61\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  shaved_ice: {\n    keywords: [ \"hot\", \"dessert\", \"summer\" ],\n    char: \"\\ud83c\\udf67\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  ice_cream: {\n    keywords: [ \"food\", \"hot\", \"dessert\" ],\n    char: \"\\ud83c\\udf68\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  icecream: {\n    keywords: [ \"food\", \"hot\", \"dessert\", \"summer\" ],\n    char: \"\\ud83c\\udf66\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  pie: {\n    keywords: [ \"food\", \"dessert\", \"pastry\" ],\n    char: \"\\ud83e\\udd67\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  cake: {\n    keywords: [ \"food\", \"dessert\" ],\n    char: \"\\ud83c\\udf70\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  cupcake: {\n    keywords: [ \"food\", \"dessert\", \"bakery\", \"sweet\" ],\n    char: \"\\ud83e\\uddc1\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  moon_cake: {\n    keywords: [ \"food\", \"autumn\" ],\n    char: \"\\ud83e\\udd6e\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  birthday: {\n    keywords: [ \"food\", \"dessert\", \"cake\" ],\n    char: \"\\ud83c\\udf82\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  custard: {\n    keywords: [ \"dessert\", \"food\" ],\n    char: \"\\ud83c\\udf6e\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  candy: {\n    keywords: [ \"snack\", \"dessert\", \"sweet\", \"lolly\" ],\n    char: \"\\ud83c\\udf6c\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  lollipop: {\n    keywords: [ \"food\", \"snack\", \"candy\", \"sweet\" ],\n    char: \"\\ud83c\\udf6d\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  chocolate_bar: {\n    keywords: [ \"food\", \"snack\", \"dessert\", \"sweet\" ],\n    char: \"\\ud83c\\udf6b\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  popcorn: {\n    keywords: [ \"food\", \"movie theater\", \"films\", \"snack\" ],\n    char: \"\\ud83c\\udf7f\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  dumpling: {\n    keywords: [ \"food\", \"empanada\", \"pierogi\", \"potsticker\" ],\n    char: \"\\ud83e\\udd5f\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  doughnut: {\n    keywords: [ \"food\", \"dessert\", \"snack\", \"sweet\", \"donut\" ],\n    char: \"\\ud83c\\udf69\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  cookie: {\n    keywords: [ \"food\", \"snack\", \"oreo\", \"chocolate\", \"sweet\", \"dessert\" ],\n    char: \"\\ud83c\\udf6a\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  milk_glass: {\n    keywords: [ \"beverage\", \"drink\", \"cow\" ],\n    char: \"\\ud83e\\udd5b\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  beer: {\n    keywords: [ \"relax\", \"beverage\", \"drink\", \"drunk\", \"party\", \"pub\", \"summer\", \"alcohol\", \"booze\" ],\n    char: \"\\ud83c\\udf7a\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  beers: {\n    keywords: [ \"relax\", \"beverage\", \"drink\", \"drunk\", \"party\", \"pub\", \"summer\", \"alcohol\", \"booze\" ],\n    char: \"\\ud83c\\udf7b\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  clinking_glasses: {\n    keywords: [ \"beverage\", \"drink\", \"party\", \"alcohol\", \"celebrate\", \"cheers\", \"wine\", \"champagne\", \"toast\" ],\n    char: \"\\ud83e\\udd42\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  wine_glass: {\n    keywords: [ \"drink\", \"beverage\", \"drunk\", \"alcohol\", \"booze\" ],\n    char: \"\\ud83c\\udf77\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  tumbler_glass: {\n    keywords: [ \"drink\", \"beverage\", \"drunk\", \"alcohol\", \"liquor\", \"booze\", \"bourbon\", \"scotch\", \"whisky\", \"glass\", \"shot\" ],\n    char: \"\\ud83e\\udd43\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  cocktail: {\n    keywords: [ \"drink\", \"drunk\", \"alcohol\", \"beverage\", \"booze\", \"mojito\" ],\n    char: \"\\ud83c\\udf78\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  tropical_drink: {\n    keywords: [ \"beverage\", \"cocktail\", \"summer\", \"beach\", \"alcohol\", \"booze\", \"mojito\" ],\n    char: \"\\ud83c\\udf79\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  champagne: {\n    keywords: [ \"drink\", \"wine\", \"bottle\", \"celebration\" ],\n    char: \"\\ud83c\\udf7e\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  sake: {\n    keywords: [ \"wine\", \"drink\", \"drunk\", \"beverage\", \"japanese\", \"alcohol\", \"booze\" ],\n    char: \"\\ud83c\\udf76\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  tea: {\n    keywords: [ \"drink\", \"bowl\", \"breakfast\", \"green\", \"british\" ],\n    char: \"\\ud83c\\udf75\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  cup_with_straw: {\n    keywords: [ \"drink\", \"soda\" ],\n    char: \"\\ud83e\\udd64\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  coffee: {\n    keywords: [ \"beverage\", \"caffeine\", \"latte\", \"espresso\" ],\n    char: \"\\u2615\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  baby_bottle: {\n    keywords: [ \"food\", \"container\", \"milk\" ],\n    char: \"\\ud83c\\udf7c\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  salt: {\n    keywords: [ \"condiment\", \"shaker\" ],\n    char: \"\\ud83e\\uddc2\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  spoon: {\n    keywords: [ \"cutlery\", \"kitchen\", \"tableware\" ],\n    char: \"\\ud83e\\udd44\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  fork_and_knife: {\n    keywords: [ \"cutlery\", \"kitchen\" ],\n    char: \"\\ud83c\\udf74\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  plate_with_cutlery: {\n    keywords: [ \"food\", \"eat\", \"meal\", \"lunch\", \"dinner\", \"restaurant\" ],\n    char: \"\\ud83c\\udf7d\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  bowl_with_spoon: {\n    keywords: [ \"food\", \"breakfast\", \"cereal\", \"oatmeal\", \"porridge\" ],\n    char: \"\\ud83e\\udd63\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  takeout_box: {\n    keywords: [ \"food\", \"leftovers\" ],\n    char: \"\\ud83e\\udd61\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  chopsticks: {\n    keywords: [ \"food\" ],\n    char: \"\\ud83e\\udd62\",\n    fitzpatrick_scale: false,\n    category: \"food_and_drink\"\n  },\n  soccer: {\n    keywords: [ \"sports\", \"football\" ],\n    char: \"\\u26bd\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  basketball: {\n    keywords: [ \"sports\", \"balls\", \"NBA\" ],\n    char: \"\\ud83c\\udfc0\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  football: {\n    keywords: [ \"sports\", \"balls\", \"NFL\" ],\n    char: \"\\ud83c\\udfc8\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  baseball: {\n    keywords: [ \"sports\", \"balls\" ],\n    char: \"\\u26be\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  softball: {\n    keywords: [ \"sports\", \"balls\" ],\n    char: \"\\ud83e\\udd4e\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  tennis: {\n    keywords: [ \"sports\", \"balls\", \"green\" ],\n    char: \"\\ud83c\\udfbe\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  volleyball: {\n    keywords: [ \"sports\", \"balls\" ],\n    char: \"\\ud83c\\udfd0\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  rugby_football: {\n    keywords: [ \"sports\", \"team\" ],\n    char: \"\\ud83c\\udfc9\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  flying_disc: {\n    keywords: [ \"sports\", \"frisbee\", \"ultimate\" ],\n    char: \"\\ud83e\\udd4f\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  \"8ball\": {\n    keywords: [ \"pool\", \"hobby\", \"game\", \"luck\", \"magic\" ],\n    char: \"\\ud83c\\udfb1\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  golf: {\n    keywords: [ \"sports\", \"business\", \"flag\", \"hole\", \"summer\" ],\n    char: \"\\u26f3\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  golfing_woman: {\n    keywords: [ \"sports\", \"business\", \"woman\", \"female\" ],\n    char: \"\\ud83c\\udfcc\\ufe0f\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  golfing_man: {\n    keywords: [ \"sports\", \"business\" ],\n    char: \"\\ud83c\\udfcc\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  ping_pong: {\n    keywords: [ \"sports\", \"pingpong\" ],\n    char: \"\\ud83c\\udfd3\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  badminton: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83c\\udff8\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  goal_net: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83e\\udd45\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  ice_hockey: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83c\\udfd2\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  field_hockey: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83c\\udfd1\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  lacrosse: {\n    keywords: [ \"sports\", \"ball\", \"stick\" ],\n    char: \"\\ud83e\\udd4d\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  cricket: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83c\\udfcf\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  ski: {\n    keywords: [ \"sports\", \"winter\", \"cold\", \"snow\" ],\n    char: \"\\ud83c\\udfbf\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  skier: {\n    keywords: [ \"sports\", \"winter\", \"snow\" ],\n    char: \"\\u26f7\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  snowboarder: {\n    keywords: [ \"sports\", \"winter\" ],\n    char: \"\\ud83c\\udfc2\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  person_fencing: {\n    keywords: [ \"sports\", \"fencing\", \"sword\" ],\n    char: \"\\ud83e\\udd3a\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  women_wrestling: {\n    keywords: [ \"sports\", \"wrestlers\" ],\n    char: \"\\ud83e\\udd3c\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  men_wrestling: {\n    keywords: [ \"sports\", \"wrestlers\" ],\n    char: \"\\ud83e\\udd3c\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  woman_cartwheeling: {\n    keywords: [ \"gymnastics\" ],\n    char: \"\\ud83e\\udd38\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  man_cartwheeling: {\n    keywords: [ \"gymnastics\" ],\n    char: \"\\ud83e\\udd38\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  woman_playing_handball: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83e\\udd3e\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  man_playing_handball: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83e\\udd3e\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  ice_skate: {\n    keywords: [ \"sports\" ],\n    char: \"\\u26f8\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  curling_stone: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83e\\udd4c\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  skateboard: {\n    keywords: [ \"board\" ],\n    char: \"\\ud83d\\udef9\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  sled: {\n    keywords: [ \"sleigh\", \"luge\", \"toboggan\" ],\n    char: \"\\ud83d\\udef7\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  bow_and_arrow: {\n    keywords: [ \"sports\" ],\n    char: \"\\ud83c\\udff9\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  fishing_pole_and_fish: {\n    keywords: [ \"food\", \"hobby\", \"summer\" ],\n    char: \"\\ud83c\\udfa3\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  boxing_glove: {\n    keywords: [ \"sports\", \"fighting\" ],\n    char: \"\\ud83e\\udd4a\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  martial_arts_uniform: {\n    keywords: [ \"judo\", \"karate\", \"taekwondo\" ],\n    char: \"\\ud83e\\udd4b\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  rowing_woman: {\n    keywords: [ \"sports\", \"hobby\", \"water\", \"ship\", \"woman\", \"female\" ],\n    char: \"\\ud83d\\udea3\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  rowing_man: {\n    keywords: [ \"sports\", \"hobby\", \"water\", \"ship\" ],\n    char: \"\\ud83d\\udea3\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  climbing_woman: {\n    keywords: [ \"sports\", \"hobby\", \"woman\", \"female\", \"rock\" ],\n    char: \"\\ud83e\\uddd7\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  climbing_man: {\n    keywords: [ \"sports\", \"hobby\", \"man\", \"male\", \"rock\" ],\n    char: \"\\ud83e\\uddd7\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  swimming_woman: {\n    keywords: [ \"sports\", \"exercise\", \"human\", \"athlete\", \"water\", \"summer\", \"woman\", \"female\" ],\n    char: \"\\ud83c\\udfca\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  swimming_man: {\n    keywords: [ \"sports\", \"exercise\", \"human\", \"athlete\", \"water\", \"summer\" ],\n    char: \"\\ud83c\\udfca\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  woman_playing_water_polo: {\n    keywords: [ \"sports\", \"pool\" ],\n    char: \"\\ud83e\\udd3d\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  man_playing_water_polo: {\n    keywords: [ \"sports\", \"pool\" ],\n    char: \"\\ud83e\\udd3d\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  woman_in_lotus_position: {\n    keywords: [ \"woman\", \"female\", \"meditation\", \"yoga\", \"serenity\", \"zen\", \"mindfulness\" ],\n    char: \"\\ud83e\\uddd8\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  man_in_lotus_position: {\n    keywords: [ \"man\", \"male\", \"meditation\", \"yoga\", \"serenity\", \"zen\", \"mindfulness\" ],\n    char: \"\\ud83e\\uddd8\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  surfing_woman: {\n    keywords: [ \"sports\", \"ocean\", \"sea\", \"summer\", \"beach\", \"woman\", \"female\" ],\n    char: \"\\ud83c\\udfc4\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  surfing_man: {\n    keywords: [ \"sports\", \"ocean\", \"sea\", \"summer\", \"beach\" ],\n    char: \"\\ud83c\\udfc4\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  bath: {\n    keywords: [ \"clean\", \"shower\", \"bathroom\" ],\n    char: \"\\ud83d\\udec0\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  basketball_woman: {\n    keywords: [ \"sports\", \"human\", \"woman\", \"female\" ],\n    char: \"\\u26f9\\ufe0f\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  basketball_man: {\n    keywords: [ \"sports\", \"human\" ],\n    char: \"\\u26f9\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  weight_lifting_woman: {\n    keywords: [ \"sports\", \"training\", \"exercise\", \"woman\", \"female\" ],\n    char: \"\\ud83c\\udfcb\\ufe0f\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  weight_lifting_man: {\n    keywords: [ \"sports\", \"training\", \"exercise\" ],\n    char: \"\\ud83c\\udfcb\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  biking_woman: {\n    keywords: [ \"sports\", \"bike\", \"exercise\", \"hipster\", \"woman\", \"female\" ],\n    char: \"\\ud83d\\udeb4\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  biking_man: {\n    keywords: [ \"sports\", \"bike\", \"exercise\", \"hipster\" ],\n    char: \"\\ud83d\\udeb4\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  mountain_biking_woman: {\n    keywords: [ \"transportation\", \"sports\", \"human\", \"race\", \"bike\", \"woman\", \"female\" ],\n    char: \"\\ud83d\\udeb5\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  mountain_biking_man: {\n    keywords: [ \"transportation\", \"sports\", \"human\", \"race\", \"bike\" ],\n    char: \"\\ud83d\\udeb5\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  horse_racing: {\n    keywords: [ \"animal\", \"betting\", \"competition\", \"gambling\", \"luck\" ],\n    char: \"\\ud83c\\udfc7\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  business_suit_levitating: {\n    keywords: [ \"suit\", \"business\", \"levitate\", \"hover\", \"jump\" ],\n    char: \"\\ud83d\\udd74\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  trophy: {\n    keywords: [ \"win\", \"award\", \"contest\", \"place\", \"ftw\", \"ceremony\" ],\n    char: \"\\ud83c\\udfc6\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  running_shirt_with_sash: {\n    keywords: [ \"play\", \"pageant\" ],\n    char: \"\\ud83c\\udfbd\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  medal_sports: {\n    keywords: [ \"award\", \"winning\" ],\n    char: \"\\ud83c\\udfc5\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  medal_military: {\n    keywords: [ \"award\", \"winning\", \"army\" ],\n    char: \"\\ud83c\\udf96\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  \"1st_place_medal\": {\n    keywords: [ \"award\", \"winning\", \"first\" ],\n    char: \"\\ud83e\\udd47\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  \"2nd_place_medal\": {\n    keywords: [ \"award\", \"second\" ],\n    char: \"\\ud83e\\udd48\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  \"3rd_place_medal\": {\n    keywords: [ \"award\", \"third\" ],\n    char: \"\\ud83e\\udd49\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  reminder_ribbon: {\n    keywords: [ \"sports\", \"cause\", \"support\", \"awareness\" ],\n    char: \"\\ud83c\\udf97\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  rosette: {\n    keywords: [ \"flower\", \"decoration\", \"military\" ],\n    char: \"\\ud83c\\udff5\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  ticket: {\n    keywords: [ \"event\", \"concert\", \"pass\" ],\n    char: \"\\ud83c\\udfab\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  tickets: {\n    keywords: [ \"sports\", \"concert\", \"entrance\" ],\n    char: \"\\ud83c\\udf9f\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  performing_arts: {\n    keywords: [ \"acting\", \"theater\", \"drama\" ],\n    char: \"\\ud83c\\udfad\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  art: {\n    keywords: [ \"design\", \"paint\", \"draw\", \"colors\" ],\n    char: \"\\ud83c\\udfa8\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  circus_tent: {\n    keywords: [ \"festival\", \"carnival\", \"party\" ],\n    char: \"\\ud83c\\udfaa\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  woman_juggling: {\n    keywords: [ \"juggle\", \"balance\", \"skill\", \"multitask\" ],\n    char: \"\\ud83e\\udd39\\u200d\\u2640\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  man_juggling: {\n    keywords: [ \"juggle\", \"balance\", \"skill\", \"multitask\" ],\n    char: \"\\ud83e\\udd39\\u200d\\u2642\\ufe0f\",\n    fitzpatrick_scale: true,\n    category: \"activity\"\n  },\n  microphone: {\n    keywords: [ \"sound\", \"music\", \"PA\", \"sing\", \"talkshow\" ],\n    char: \"\\ud83c\\udfa4\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  headphones: {\n    keywords: [ \"music\", \"score\", \"gadgets\" ],\n    char: \"\\ud83c\\udfa7\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  musical_score: {\n    keywords: [ \"treble\", \"clef\", \"compose\" ],\n    char: \"\\ud83c\\udfbc\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  musical_keyboard: {\n    keywords: [ \"piano\", \"instrument\", \"compose\" ],\n    char: \"\\ud83c\\udfb9\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  drum: {\n    keywords: [ \"music\", \"instrument\", \"drumsticks\", \"snare\" ],\n    char: \"\\ud83e\\udd41\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  saxophone: {\n    keywords: [ \"music\", \"instrument\", \"jazz\", \"blues\" ],\n    char: \"\\ud83c\\udfb7\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  trumpet: {\n    keywords: [ \"music\", \"brass\" ],\n    char: \"\\ud83c\\udfba\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  guitar: {\n    keywords: [ \"music\", \"instrument\" ],\n    char: \"\\ud83c\\udfb8\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  violin: {\n    keywords: [ \"music\", \"instrument\", \"orchestra\", \"symphony\" ],\n    char: \"\\ud83c\\udfbb\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  clapper: {\n    keywords: [ \"movie\", \"film\", \"record\" ],\n    char: \"\\ud83c\\udfac\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  video_game: {\n    keywords: [ \"play\", \"console\", \"PS4\", \"controller\" ],\n    char: \"\\ud83c\\udfae\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  space_invader: {\n    keywords: [ \"game\", \"arcade\", \"play\" ],\n    char: \"\\ud83d\\udc7e\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  dart: {\n    keywords: [ \"game\", \"play\", \"bar\", \"target\", \"bullseye\" ],\n    char: \"\\ud83c\\udfaf\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  game_die: {\n    keywords: [ \"dice\", \"random\", \"tabletop\", \"play\", \"luck\" ],\n    char: \"\\ud83c\\udfb2\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  chess_pawn: {\n    keywords: [ \"expendable\" ],\n    char: \"\\u265f\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  slot_machine: {\n    keywords: [ \"bet\", \"gamble\", \"vegas\", \"fruit machine\", \"luck\", \"casino\" ],\n    char: \"\\ud83c\\udfb0\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  jigsaw: {\n    keywords: [ \"interlocking\", \"puzzle\", \"piece\" ],\n    char: \"\\ud83e\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  bowling: {\n    keywords: [ \"sports\", \"fun\", \"play\" ],\n    char: \"\\ud83c\\udfb3\",\n    fitzpatrick_scale: false,\n    category: \"activity\"\n  },\n  red_car: {\n    keywords: [ \"red\", \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude97\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  taxi: {\n    keywords: [ \"uber\", \"vehicle\", \"cars\", \"transportation\" ],\n    char: \"\\ud83d\\ude95\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  blue_car: {\n    keywords: [ \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude99\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  bus: {\n    keywords: [ \"car\", \"vehicle\", \"transportation\" ],\n    char: \"\\ud83d\\ude8c\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  trolleybus: {\n    keywords: [ \"bart\", \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude8e\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  racing_car: {\n    keywords: [ \"sports\", \"race\", \"fast\", \"formula\", \"f1\" ],\n    char: \"\\ud83c\\udfce\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  police_car: {\n    keywords: [ \"vehicle\", \"cars\", \"transportation\", \"law\", \"legal\", \"enforcement\" ],\n    char: \"\\ud83d\\ude93\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  ambulance: {\n    keywords: [ \"health\", \"911\", \"hospital\" ],\n    char: \"\\ud83d\\ude91\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  fire_engine: {\n    keywords: [ \"transportation\", \"cars\", \"vehicle\" ],\n    char: \"\\ud83d\\ude92\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  minibus: {\n    keywords: [ \"vehicle\", \"car\", \"transportation\" ],\n    char: \"\\ud83d\\ude90\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  truck: {\n    keywords: [ \"cars\", \"transportation\" ],\n    char: \"\\ud83d\\ude9a\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  articulated_lorry: {\n    keywords: [ \"vehicle\", \"cars\", \"transportation\", \"express\" ],\n    char: \"\\ud83d\\ude9b\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  tractor: {\n    keywords: [ \"vehicle\", \"car\", \"farming\", \"agriculture\" ],\n    char: \"\\ud83d\\ude9c\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  kick_scooter: {\n    keywords: [ \"vehicle\", \"kick\", \"razor\" ],\n    char: \"\\ud83d\\udef4\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  motorcycle: {\n    keywords: [ \"race\", \"sports\", \"fast\" ],\n    char: \"\\ud83c\\udfcd\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  bike: {\n    keywords: [ \"sports\", \"bicycle\", \"exercise\", \"hipster\" ],\n    char: \"\\ud83d\\udeb2\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  motor_scooter: {\n    keywords: [ \"vehicle\", \"vespa\", \"sasha\" ],\n    char: \"\\ud83d\\udef5\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  rotating_light: {\n    keywords: [ \"police\", \"ambulance\", \"911\", \"emergency\", \"alert\", \"error\", \"pinged\", \"law\", \"legal\" ],\n    char: \"\\ud83d\\udea8\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  oncoming_police_car: {\n    keywords: [ \"vehicle\", \"law\", \"legal\", \"enforcement\", \"911\" ],\n    char: \"\\ud83d\\ude94\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  oncoming_bus: {\n    keywords: [ \"vehicle\", \"transportation\" ],\n    char: \"\\ud83d\\ude8d\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  oncoming_automobile: {\n    keywords: [ \"car\", \"vehicle\", \"transportation\" ],\n    char: \"\\ud83d\\ude98\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  oncoming_taxi: {\n    keywords: [ \"vehicle\", \"cars\", \"uber\" ],\n    char: \"\\ud83d\\ude96\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  aerial_tramway: {\n    keywords: [ \"transportation\", \"vehicle\", \"ski\" ],\n    char: \"\\ud83d\\udea1\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  mountain_cableway: {\n    keywords: [ \"transportation\", \"vehicle\", \"ski\" ],\n    char: \"\\ud83d\\udea0\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  suspension_railway: {\n    keywords: [ \"vehicle\", \"transportation\" ],\n    char: \"\\ud83d\\ude9f\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  railway_car: {\n    keywords: [ \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude83\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  train: {\n    keywords: [ \"transportation\", \"vehicle\", \"carriage\", \"public\", \"travel\" ],\n    char: \"\\ud83d\\ude8b\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  monorail: {\n    keywords: [ \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude9d\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  bullettrain_side: {\n    keywords: [ \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude84\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  bullettrain_front: {\n    keywords: [ \"transportation\", \"vehicle\", \"speed\", \"fast\", \"public\", \"travel\" ],\n    char: \"\\ud83d\\ude85\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  light_rail: {\n    keywords: [ \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude88\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  mountain_railway: {\n    keywords: [ \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude9e\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  steam_locomotive: {\n    keywords: [ \"transportation\", \"vehicle\", \"train\" ],\n    char: \"\\ud83d\\ude82\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  train2: {\n    keywords: [ \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude86\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  metro: {\n    keywords: [ \"transportation\", \"blue-square\", \"mrt\", \"underground\", \"tube\" ],\n    char: \"\\ud83d\\ude87\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  tram: {\n    keywords: [ \"transportation\", \"vehicle\" ],\n    char: \"\\ud83d\\ude8a\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  station: {\n    keywords: [ \"transportation\", \"vehicle\", \"public\" ],\n    char: \"\\ud83d\\ude89\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  flying_saucer: {\n    keywords: [ \"transportation\", \"vehicle\", \"ufo\" ],\n    char: \"\\ud83d\\udef8\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  helicopter: {\n    keywords: [ \"transportation\", \"vehicle\", \"fly\" ],\n    char: \"\\ud83d\\ude81\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  small_airplane: {\n    keywords: [ \"flight\", \"transportation\", \"fly\", \"vehicle\" ],\n    char: \"\\ud83d\\udee9\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  airplane: {\n    keywords: [ \"vehicle\", \"transportation\", \"flight\", \"fly\" ],\n    char: \"\\u2708\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  flight_departure: {\n    keywords: [ \"airport\", \"flight\", \"landing\" ],\n    char: \"\\ud83d\\udeeb\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  flight_arrival: {\n    keywords: [ \"airport\", \"flight\", \"boarding\" ],\n    char: \"\\ud83d\\udeec\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  sailboat: {\n    keywords: [ \"ship\", \"summer\", \"transportation\", \"water\", \"sailing\" ],\n    char: \"\\u26f5\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  motor_boat: {\n    keywords: [ \"ship\" ],\n    char: \"\\ud83d\\udee5\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  speedboat: {\n    keywords: [ \"ship\", \"transportation\", \"vehicle\", \"summer\" ],\n    char: \"\\ud83d\\udea4\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  ferry: {\n    keywords: [ \"boat\", \"ship\", \"yacht\" ],\n    char: \"\\u26f4\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  passenger_ship: {\n    keywords: [ \"yacht\", \"cruise\", \"ferry\" ],\n    char: \"\\ud83d\\udef3\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  rocket: {\n    keywords: [ \"launch\", \"ship\", \"staffmode\", \"NASA\", \"outer space\", \"outer_space\", \"fly\" ],\n    char: \"\\ud83d\\ude80\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  artificial_satellite: {\n    keywords: [ \"communication\", \"gps\", \"orbit\", \"spaceflight\", \"NASA\", \"ISS\" ],\n    char: \"\\ud83d\\udef0\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  seat: {\n    keywords: [ \"sit\", \"airplane\", \"transport\", \"bus\", \"flight\", \"fly\" ],\n    char: \"\\ud83d\\udcba\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  canoe: {\n    keywords: [ \"boat\", \"paddle\", \"water\", \"ship\" ],\n    char: \"\\ud83d\\udef6\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  anchor: {\n    keywords: [ \"ship\", \"ferry\", \"sea\", \"boat\" ],\n    char: \"\\u2693\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  construction: {\n    keywords: [ \"wip\", \"progress\", \"caution\", \"warning\" ],\n    char: \"\\ud83d\\udea7\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  fuelpump: {\n    keywords: [ \"gas station\", \"petroleum\" ],\n    char: \"\\u26fd\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  busstop: {\n    keywords: [ \"transportation\", \"wait\" ],\n    char: \"\\ud83d\\ude8f\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  vertical_traffic_light: {\n    keywords: [ \"transportation\", \"driving\" ],\n    char: \"\\ud83d\\udea6\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  traffic_light: {\n    keywords: [ \"transportation\", \"signal\" ],\n    char: \"\\ud83d\\udea5\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  checkered_flag: {\n    keywords: [ \"contest\", \"finishline\", \"race\", \"gokart\" ],\n    char: \"\\ud83c\\udfc1\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  ship: {\n    keywords: [ \"transportation\", \"titanic\", \"deploy\" ],\n    char: \"\\ud83d\\udea2\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  ferris_wheel: {\n    keywords: [ \"photo\", \"carnival\", \"londoneye\" ],\n    char: \"\\ud83c\\udfa1\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  roller_coaster: {\n    keywords: [ \"carnival\", \"playground\", \"photo\", \"fun\" ],\n    char: \"\\ud83c\\udfa2\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  carousel_horse: {\n    keywords: [ \"photo\", \"carnival\" ],\n    char: \"\\ud83c\\udfa0\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  building_construction: {\n    keywords: [ \"wip\", \"working\", \"progress\" ],\n    char: \"\\ud83c\\udfd7\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  foggy: {\n    keywords: [ \"photo\", \"mountain\" ],\n    char: \"\\ud83c\\udf01\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  tokyo_tower: {\n    keywords: [ \"photo\", \"japanese\" ],\n    char: \"\\ud83d\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  factory: {\n    keywords: [ \"building\", \"industry\", \"pollution\", \"smoke\" ],\n    char: \"\\ud83c\\udfed\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  fountain: {\n    keywords: [ \"photo\", \"summer\", \"water\", \"fresh\" ],\n    char: \"\\u26f2\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  rice_scene: {\n    keywords: [ \"photo\", \"japan\", \"asia\", \"tsukimi\" ],\n    char: \"\\ud83c\\udf91\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  mountain: {\n    keywords: [ \"photo\", \"nature\", \"environment\" ],\n    char: \"\\u26f0\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  mountain_snow: {\n    keywords: [ \"photo\", \"nature\", \"environment\", \"winter\", \"cold\" ],\n    char: \"\\ud83c\\udfd4\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  mount_fuji: {\n    keywords: [ \"photo\", \"mountain\", \"nature\", \"japanese\" ],\n    char: \"\\ud83d\\uddfb\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  volcano: {\n    keywords: [ \"photo\", \"nature\", \"disaster\" ],\n    char: \"\\ud83c\\udf0b\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  japan: {\n    keywords: [ \"nation\", \"country\", \"japanese\", \"asia\" ],\n    char: \"\\ud83d\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  camping: {\n    keywords: [ \"photo\", \"outdoors\", \"tent\" ],\n    char: \"\\ud83c\\udfd5\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  tent: {\n    keywords: [ \"photo\", \"camping\", \"outdoors\" ],\n    char: \"\\u26fa\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  national_park: {\n    keywords: [ \"photo\", \"environment\", \"nature\" ],\n    char: \"\\ud83c\\udfde\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  motorway: {\n    keywords: [ \"road\", \"cupertino\", \"interstate\", \"highway\" ],\n    char: \"\\ud83d\\udee3\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  railway_track: {\n    keywords: [ \"train\", \"transportation\" ],\n    char: \"\\ud83d\\udee4\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  sunrise: {\n    keywords: [ \"morning\", \"view\", \"vacation\", \"photo\" ],\n    char: \"\\ud83c\\udf05\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  sunrise_over_mountains: {\n    keywords: [ \"view\", \"vacation\", \"photo\" ],\n    char: \"\\ud83c\\udf04\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  desert: {\n    keywords: [ \"photo\", \"warm\", \"saharah\" ],\n    char: \"\\ud83c\\udfdc\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  beach_umbrella: {\n    keywords: [ \"weather\", \"summer\", \"sunny\", \"sand\", \"mojito\" ],\n    char: \"\\ud83c\\udfd6\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  desert_island: {\n    keywords: [ \"photo\", \"tropical\", \"mojito\" ],\n    char: \"\\ud83c\\udfdd\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  city_sunrise: {\n    keywords: [ \"photo\", \"good morning\", \"dawn\" ],\n    char: \"\\ud83c\\udf07\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  city_sunset: {\n    keywords: [ \"photo\", \"evening\", \"sky\", \"buildings\" ],\n    char: \"\\ud83c\\udf06\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  cityscape: {\n    keywords: [ \"photo\", \"night life\", \"urban\" ],\n    char: \"\\ud83c\\udfd9\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  night_with_stars: {\n    keywords: [ \"evening\", \"city\", \"downtown\" ],\n    char: \"\\ud83c\\udf03\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  bridge_at_night: {\n    keywords: [ \"photo\", \"sanfrancisco\" ],\n    char: \"\\ud83c\\udf09\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  milky_way: {\n    keywords: [ \"photo\", \"space\", \"stars\" ],\n    char: \"\\ud83c\\udf0c\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  stars: {\n    keywords: [ \"night\", \"photo\" ],\n    char: \"\\ud83c\\udf20\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  sparkler: {\n    keywords: [ \"stars\", \"night\", \"shine\" ],\n    char: \"\\ud83c\\udf87\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  fireworks: {\n    keywords: [ \"photo\", \"festival\", \"carnival\", \"congratulations\" ],\n    char: \"\\ud83c\\udf86\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  rainbow: {\n    keywords: [ \"nature\", \"happy\", \"unicorn_face\", \"photo\", \"sky\", \"spring\" ],\n    char: \"\\ud83c\\udf08\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  houses: {\n    keywords: [ \"buildings\", \"photo\" ],\n    char: \"\\ud83c\\udfd8\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  european_castle: {\n    keywords: [ \"building\", \"royalty\", \"history\" ],\n    char: \"\\ud83c\\udff0\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  japanese_castle: {\n    keywords: [ \"photo\", \"building\" ],\n    char: \"\\ud83c\\udfef\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  stadium: {\n    keywords: [ \"photo\", \"place\", \"sports\", \"concert\", \"venue\" ],\n    char: \"\\ud83c\\udfdf\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  statue_of_liberty: {\n    keywords: [ \"american\", \"newyork\" ],\n    char: \"\\ud83d\\uddfd\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  house: {\n    keywords: [ \"building\", \"home\" ],\n    char: \"\\ud83c\\udfe0\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  house_with_garden: {\n    keywords: [ \"home\", \"plant\", \"nature\" ],\n    char: \"\\ud83c\\udfe1\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  derelict_house: {\n    keywords: [ \"abandon\", \"evict\", \"broken\", \"building\" ],\n    char: \"\\ud83c\\udfda\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  office: {\n    keywords: [ \"building\", \"bureau\", \"work\" ],\n    char: \"\\ud83c\\udfe2\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  department_store: {\n    keywords: [ \"building\", \"shopping\", \"mall\" ],\n    char: \"\\ud83c\\udfec\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  post_office: {\n    keywords: [ \"building\", \"envelope\", \"communication\" ],\n    char: \"\\ud83c\\udfe3\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  european_post_office: {\n    keywords: [ \"building\", \"email\" ],\n    char: \"\\ud83c\\udfe4\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  hospital: {\n    keywords: [ \"building\", \"health\", \"surgery\", \"doctor\" ],\n    char: \"\\ud83c\\udfe5\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  bank: {\n    keywords: [ \"building\", \"money\", \"sales\", \"cash\", \"business\", \"enterprise\" ],\n    char: \"\\ud83c\\udfe6\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  hotel: {\n    keywords: [ \"building\", \"accomodation\", \"checkin\" ],\n    char: \"\\ud83c\\udfe8\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  convenience_store: {\n    keywords: [ \"building\", \"shopping\", \"groceries\" ],\n    char: \"\\ud83c\\udfea\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  school: {\n    keywords: [ \"building\", \"student\", \"education\", \"learn\", \"teach\" ],\n    char: \"\\ud83c\\udfeb\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  love_hotel: {\n    keywords: [ \"like\", \"affection\", \"dating\" ],\n    char: \"\\ud83c\\udfe9\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  wedding: {\n    keywords: [ \"love\", \"like\", \"affection\", \"couple\", \"marriage\", \"bride\", \"groom\" ],\n    char: \"\\ud83d\\udc92\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  classical_building: {\n    keywords: [ \"art\", \"culture\", \"history\" ],\n    char: \"\\ud83c\\udfdb\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  church: {\n    keywords: [ \"building\", \"religion\", \"christ\" ],\n    char: \"\\u26ea\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  mosque: {\n    keywords: [ \"islam\", \"worship\", \"minaret\" ],\n    char: \"\\ud83d\\udd4c\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  synagogue: {\n    keywords: [ \"judaism\", \"worship\", \"temple\", \"jewish\" ],\n    char: \"\\ud83d\\udd4d\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  kaaba: {\n    keywords: [ \"mecca\", \"mosque\", \"islam\" ],\n    char: \"\\ud83d\\udd4b\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  shinto_shrine: {\n    keywords: [ \"temple\", \"japan\", \"kyoto\" ],\n    char: \"\\u26e9\",\n    fitzpatrick_scale: false,\n    category: \"travel_and_places\"\n  },\n  watch: {\n    keywords: [ \"time\", \"accessories\" ],\n    char: \"\\u231a\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  iphone: {\n    keywords: [ \"technology\", \"apple\", \"gadgets\", \"dial\" ],\n    char: \"\\ud83d\\udcf1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  calling: {\n    keywords: [ \"iphone\", \"incoming\" ],\n    char: \"\\ud83d\\udcf2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  computer: {\n    keywords: [ \"technology\", \"laptop\", \"screen\", \"display\", \"monitor\" ],\n    char: \"\\ud83d\\udcbb\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  keyboard: {\n    keywords: [ \"technology\", \"computer\", \"type\", \"input\", \"text\" ],\n    char: \"\\u2328\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  desktop_computer: {\n    keywords: [ \"technology\", \"computing\", \"screen\" ],\n    char: \"\\ud83d\\udda5\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  printer: {\n    keywords: [ \"paper\", \"ink\" ],\n    char: \"\\ud83d\\udda8\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  computer_mouse: {\n    keywords: [ \"click\" ],\n    char: \"\\ud83d\\uddb1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  trackball: {\n    keywords: [ \"technology\", \"trackpad\" ],\n    char: \"\\ud83d\\uddb2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  joystick: {\n    keywords: [ \"game\", \"play\" ],\n    char: \"\\ud83d\\udd79\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  clamp: {\n    keywords: [ \"tool\" ],\n    char: \"\\ud83d\\udddc\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  minidisc: {\n    keywords: [ \"technology\", \"record\", \"data\", \"disk\", \"90s\" ],\n    char: \"\\ud83d\\udcbd\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  floppy_disk: {\n    keywords: [ \"oldschool\", \"technology\", \"save\", \"90s\", \"80s\" ],\n    char: \"\\ud83d\\udcbe\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  cd: {\n    keywords: [ \"technology\", \"dvd\", \"disk\", \"disc\", \"90s\" ],\n    char: \"\\ud83d\\udcbf\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  dvd: {\n    keywords: [ \"cd\", \"disk\", \"disc\" ],\n    char: \"\\ud83d\\udcc0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  vhs: {\n    keywords: [ \"record\", \"video\", \"oldschool\", \"90s\", \"80s\" ],\n    char: \"\\ud83d\\udcfc\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  camera: {\n    keywords: [ \"gadgets\", \"photography\" ],\n    char: \"\\ud83d\\udcf7\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  camera_flash: {\n    keywords: [ \"photography\", \"gadgets\" ],\n    char: \"\\ud83d\\udcf8\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  video_camera: {\n    keywords: [ \"film\", \"record\" ],\n    char: \"\\ud83d\\udcf9\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  movie_camera: {\n    keywords: [ \"film\", \"record\" ],\n    char: \"\\ud83c\\udfa5\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  film_projector: {\n    keywords: [ \"video\", \"tape\", \"record\", \"movie\" ],\n    char: \"\\ud83d\\udcfd\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  film_strip: {\n    keywords: [ \"movie\" ],\n    char: \"\\ud83c\\udf9e\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  telephone_receiver: {\n    keywords: [ \"technology\", \"communication\", \"dial\" ],\n    char: \"\\ud83d\\udcde\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  phone: {\n    keywords: [ \"technology\", \"communication\", \"dial\", \"telephone\" ],\n    char: \"\\u260e\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  pager: {\n    keywords: [ \"bbcall\", \"oldschool\", \"90s\" ],\n    char: \"\\ud83d\\udcdf\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  fax: {\n    keywords: [ \"communication\", \"technology\" ],\n    char: \"\\ud83d\\udce0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  tv: {\n    keywords: [ \"technology\", \"program\", \"oldschool\", \"show\", \"television\" ],\n    char: \"\\ud83d\\udcfa\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  radio: {\n    keywords: [ \"communication\", \"music\", \"podcast\", \"program\" ],\n    char: \"\\ud83d\\udcfb\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  studio_microphone: {\n    keywords: [ \"sing\", \"recording\", \"artist\", \"talkshow\" ],\n    char: \"\\ud83c\\udf99\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  level_slider: {\n    keywords: [ \"scale\" ],\n    char: \"\\ud83c\\udf9a\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  control_knobs: {\n    keywords: [ \"dial\" ],\n    char: \"\\ud83c\\udf9b\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  compass: {\n    keywords: [ \"magnetic\", \"navigation\", \"orienteering\" ],\n    char: \"\\ud83e\\udded\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  stopwatch: {\n    keywords: [ \"time\", \"deadline\" ],\n    char: \"\\u23f1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  timer_clock: {\n    keywords: [ \"alarm\" ],\n    char: \"\\u23f2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  alarm_clock: {\n    keywords: [ \"time\", \"wake\" ],\n    char: \"\\u23f0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  mantelpiece_clock: {\n    keywords: [ \"time\" ],\n    char: \"\\ud83d\\udd70\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  hourglass_flowing_sand: {\n    keywords: [ \"oldschool\", \"time\", \"countdown\" ],\n    char: \"\\u23f3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  hourglass: {\n    keywords: [ \"time\", \"clock\", \"oldschool\", \"limit\", \"exam\", \"quiz\", \"test\" ],\n    char: \"\\u231b\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  satellite: {\n    keywords: [ \"communication\", \"future\", \"radio\", \"space\" ],\n    char: \"\\ud83d\\udce1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  battery: {\n    keywords: [ \"power\", \"energy\", \"sustain\" ],\n    char: \"\\ud83d\\udd0b\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  electric_plug: {\n    keywords: [ \"charger\", \"power\" ],\n    char: \"\\ud83d\\udd0c\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  bulb: {\n    keywords: [ \"light\", \"electricity\", \"idea\" ],\n    char: \"\\ud83d\\udca1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  flashlight: {\n    keywords: [ \"dark\", \"camping\", \"sight\", \"night\" ],\n    char: \"\\ud83d\\udd26\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  candle: {\n    keywords: [ \"fire\", \"wax\" ],\n    char: \"\\ud83d\\udd6f\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  fire_extinguisher: {\n    keywords: [ \"quench\" ],\n    char: \"\\ud83e\\uddef\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  wastebasket: {\n    keywords: [ \"bin\", \"trash\", \"rubbish\", \"garbage\", \"toss\" ],\n    char: \"\\ud83d\\uddd1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  oil_drum: {\n    keywords: [ \"barrell\" ],\n    char: \"\\ud83d\\udee2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  money_with_wings: {\n    keywords: [ \"dollar\", \"bills\", \"payment\", \"sale\" ],\n    char: \"\\ud83d\\udcb8\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  dollar: {\n    keywords: [ \"money\", \"sales\", \"bill\", \"currency\" ],\n    char: \"\\ud83d\\udcb5\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  yen: {\n    keywords: [ \"money\", \"sales\", \"japanese\", \"dollar\", \"currency\" ],\n    char: \"\\ud83d\\udcb4\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  euro: {\n    keywords: [ \"money\", \"sales\", \"dollar\", \"currency\" ],\n    char: \"\\ud83d\\udcb6\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  pound: {\n    keywords: [ \"british\", \"sterling\", \"money\", \"sales\", \"bills\", \"uk\", \"england\", \"currency\" ],\n    char: \"\\ud83d\\udcb7\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  moneybag: {\n    keywords: [ \"dollar\", \"payment\", \"coins\", \"sale\" ],\n    char: \"\\ud83d\\udcb0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  credit_card: {\n    keywords: [ \"money\", \"sales\", \"dollar\", \"bill\", \"payment\", \"shopping\" ],\n    char: \"\\ud83d\\udcb3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  gem: {\n    keywords: [ \"blue\", \"ruby\", \"diamond\", \"jewelry\" ],\n    char: \"\\ud83d\\udc8e\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  balance_scale: {\n    keywords: [ \"law\", \"fairness\", \"weight\" ],\n    char: \"\\u2696\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  toolbox: {\n    keywords: [ \"tools\", \"diy\", \"fix\", \"maintainer\", \"mechanic\" ],\n    char: \"\\ud83e\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  wrench: {\n    keywords: [ \"tools\", \"diy\", \"ikea\", \"fix\", \"maintainer\" ],\n    char: \"\\ud83d\\udd27\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  hammer: {\n    keywords: [ \"tools\", \"build\", \"create\" ],\n    char: \"\\ud83d\\udd28\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  hammer_and_pick: {\n    keywords: [ \"tools\", \"build\", \"create\" ],\n    char: \"\\u2692\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  hammer_and_wrench: {\n    keywords: [ \"tools\", \"build\", \"create\" ],\n    char: \"\\ud83d\\udee0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  pick: {\n    keywords: [ \"tools\", \"dig\" ],\n    char: \"\\u26cf\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  nut_and_bolt: {\n    keywords: [ \"handy\", \"tools\", \"fix\" ],\n    char: \"\\ud83d\\udd29\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  gear: {\n    keywords: [ \"cog\" ],\n    char: \"\\u2699\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  brick: {\n    keywords: [ \"bricks\" ],\n    char: \"\\ud83e\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  chains: {\n    keywords: [ \"lock\", \"arrest\" ],\n    char: \"\\u26d3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  magnet: {\n    keywords: [ \"attraction\", \"magnetic\" ],\n    char: \"\\ud83e\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  gun: {\n    keywords: [ \"violence\", \"weapon\", \"pistol\", \"revolver\" ],\n    char: \"\\ud83d\\udd2b\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  bomb: {\n    keywords: [ \"boom\", \"explode\", \"explosion\", \"terrorism\" ],\n    char: \"\\ud83d\\udca3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  firecracker: {\n    keywords: [ \"dynamite\", \"boom\", \"explode\", \"explosion\", \"explosive\" ],\n    char: \"\\ud83e\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  hocho: {\n    keywords: [ \"knife\", \"blade\", \"cutlery\", \"kitchen\", \"weapon\" ],\n    char: \"\\ud83d\\udd2a\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  dagger: {\n    keywords: [ \"weapon\" ],\n    char: \"\\ud83d\\udde1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  crossed_swords: {\n    keywords: [ \"weapon\" ],\n    char: \"\\u2694\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  shield: {\n    keywords: [ \"protection\", \"security\" ],\n    char: \"\\ud83d\\udee1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  smoking: {\n    keywords: [ \"kills\", \"tobacco\", \"cigarette\", \"joint\", \"smoke\" ],\n    char: \"\\ud83d\\udeac\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  skull_and_crossbones: {\n    keywords: [ \"poison\", \"danger\", \"deadly\", \"scary\", \"death\", \"pirate\", \"evil\" ],\n    char: \"\\u2620\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  coffin: {\n    keywords: [ \"vampire\", \"dead\", \"die\", \"death\", \"rip\", \"graveyard\", \"cemetery\", \"casket\", \"funeral\", \"box\" ],\n    char: \"\\u26b0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  funeral_urn: {\n    keywords: [ \"dead\", \"die\", \"death\", \"rip\", \"ashes\" ],\n    char: \"\\u26b1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  amphora: {\n    keywords: [ \"vase\", \"jar\" ],\n    char: \"\\ud83c\\udffa\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  crystal_ball: {\n    keywords: [ \"disco\", \"party\", \"magic\", \"circus\", \"fortune_teller\" ],\n    char: \"\\ud83d\\udd2e\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  prayer_beads: {\n    keywords: [ \"dhikr\", \"religious\" ],\n    char: \"\\ud83d\\udcff\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  nazar_amulet: {\n    keywords: [ \"bead\", \"charm\" ],\n    char: \"\\ud83e\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  barber: {\n    keywords: [ \"hair\", \"salon\", \"style\" ],\n    char: \"\\ud83d\\udc88\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  alembic: {\n    keywords: [ \"distilling\", \"science\", \"experiment\", \"chemistry\" ],\n    char: \"\\u2697\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  telescope: {\n    keywords: [ \"stars\", \"space\", \"zoom\", \"science\", \"astronomy\" ],\n    char: \"\\ud83d\\udd2d\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  microscope: {\n    keywords: [ \"laboratory\", \"experiment\", \"zoomin\", \"science\", \"study\" ],\n    char: \"\\ud83d\\udd2c\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  hole: {\n    keywords: [ \"embarrassing\" ],\n    char: \"\\ud83d\\udd73\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  pill: {\n    keywords: [ \"health\", \"medicine\", \"doctor\", \"pharmacy\", \"drug\" ],\n    char: \"\\ud83d\\udc8a\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  syringe: {\n    keywords: [ \"health\", \"hospital\", \"drugs\", \"blood\", \"medicine\", \"needle\", \"doctor\", \"nurse\" ],\n    char: \"\\ud83d\\udc89\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  dna: {\n    keywords: [ \"biologist\", \"genetics\", \"life\" ],\n    char: \"\\ud83e\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  microbe: {\n    keywords: [ \"amoeba\", \"bacteria\", \"germs\" ],\n    char: \"\\ud83e\\udda0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  petri_dish: {\n    keywords: [ \"bacteria\", \"biology\", \"culture\", \"lab\" ],\n    char: \"\\ud83e\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  test_tube: {\n    keywords: [ \"chemistry\", \"experiment\", \"lab\", \"science\" ],\n    char: \"\\ud83e\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  thermometer: {\n    keywords: [ \"weather\", \"temperature\", \"hot\", \"cold\" ],\n    char: \"\\ud83c\\udf21\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  broom: {\n    keywords: [ \"cleaning\", \"sweeping\", \"witch\" ],\n    char: \"\\ud83e\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  basket: {\n    keywords: [ \"laundry\" ],\n    char: \"\\ud83e\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  toilet_paper: {\n    keywords: [ \"roll\" ],\n    char: \"\\ud83e\\uddfb\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  label: {\n    keywords: [ \"sale\", \"tag\" ],\n    char: \"\\ud83c\\udff7\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  bookmark: {\n    keywords: [ \"favorite\", \"label\", \"save\" ],\n    char: \"\\ud83d\\udd16\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  toilet: {\n    keywords: [ \"restroom\", \"wc\", \"washroom\", \"bathroom\", \"potty\" ],\n    char: \"\\ud83d\\udebd\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  shower: {\n    keywords: [ \"clean\", \"water\", \"bathroom\" ],\n    char: \"\\ud83d\\udebf\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  bathtub: {\n    keywords: [ \"clean\", \"shower\", \"bathroom\" ],\n    char: \"\\ud83d\\udec1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  soap: {\n    keywords: [ \"bar\", \"bathing\", \"cleaning\", \"lather\" ],\n    char: \"\\ud83e\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  sponge: {\n    keywords: [ \"absorbing\", \"cleaning\", \"porous\" ],\n    char: \"\\ud83e\\uddfd\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  lotion_bottle: {\n    keywords: [ \"moisturizer\", \"sunscreen\" ],\n    char: \"\\ud83e\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  key: {\n    keywords: [ \"lock\", \"door\", \"password\" ],\n    char: \"\\ud83d\\udd11\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  old_key: {\n    keywords: [ \"lock\", \"door\", \"password\" ],\n    char: \"\\ud83d\\udddd\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  couch_and_lamp: {\n    keywords: [ \"read\", \"chill\" ],\n    char: \"\\ud83d\\udecb\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  sleeping_bed: {\n    keywords: [ \"bed\", \"rest\" ],\n    char: \"\\ud83d\\udecc\",\n    fitzpatrick_scale: true,\n    category: \"objects\"\n  },\n  bed: {\n    keywords: [ \"sleep\", \"rest\" ],\n    char: \"\\ud83d\\udecf\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  door: {\n    keywords: [ \"house\", \"entry\", \"exit\" ],\n    char: \"\\ud83d\\udeaa\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  bellhop_bell: {\n    keywords: [ \"service\" ],\n    char: \"\\ud83d\\udece\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  teddy_bear: {\n    keywords: [ \"plush\", \"stuffed\" ],\n    char: \"\\ud83e\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  framed_picture: {\n    keywords: [ \"photography\" ],\n    char: \"\\ud83d\\uddbc\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  world_map: {\n    keywords: [ \"location\", \"direction\" ],\n    char: \"\\ud83d\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  parasol_on_ground: {\n    keywords: [ \"weather\", \"summer\" ],\n    char: \"\\u26f1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  moyai: {\n    keywords: [ \"rock\", \"easter island\", \"moai\" ],\n    char: \"\\ud83d\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  shopping: {\n    keywords: [ \"mall\", \"buy\", \"purchase\" ],\n    char: \"\\ud83d\\udecd\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  shopping_cart: {\n    keywords: [ \"trolley\" ],\n    char: \"\\ud83d\\uded2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  balloon: {\n    keywords: [ \"party\", \"celebration\", \"birthday\", \"circus\" ],\n    char: \"\\ud83c\\udf88\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  flags: {\n    keywords: [ \"fish\", \"japanese\", \"koinobori\", \"carp\", \"banner\" ],\n    char: \"\\ud83c\\udf8f\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  ribbon: {\n    keywords: [ \"decoration\", \"pink\", \"girl\", \"bowtie\" ],\n    char: \"\\ud83c\\udf80\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  gift: {\n    keywords: [ \"present\", \"birthday\", \"christmas\", \"xmas\" ],\n    char: \"\\ud83c\\udf81\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  confetti_ball: {\n    keywords: [ \"festival\", \"party\", \"birthday\", \"circus\" ],\n    char: \"\\ud83c\\udf8a\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  tada: {\n    keywords: [ \"party\", \"congratulations\", \"birthday\", \"magic\", \"circus\", \"celebration\" ],\n    char: \"\\ud83c\\udf89\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  dolls: {\n    keywords: [ \"japanese\", \"toy\", \"kimono\" ],\n    char: \"\\ud83c\\udf8e\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  wind_chime: {\n    keywords: [ \"nature\", \"ding\", \"spring\", \"bell\" ],\n    char: \"\\ud83c\\udf90\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  crossed_flags: {\n    keywords: [ \"japanese\", \"nation\", \"country\", \"border\" ],\n    char: \"\\ud83c\\udf8c\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  izakaya_lantern: {\n    keywords: [ \"light\", \"paper\", \"halloween\", \"spooky\" ],\n    char: \"\\ud83c\\udfee\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  red_envelope: {\n    keywords: [ \"gift\" ],\n    char: \"\\ud83e\\udde7\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  email: {\n    keywords: [ \"letter\", \"postal\", \"inbox\", \"communication\" ],\n    char: \"\\u2709\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  envelope_with_arrow: {\n    keywords: [ \"email\", \"communication\" ],\n    char: \"\\ud83d\\udce9\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  incoming_envelope: {\n    keywords: [ \"email\", \"inbox\" ],\n    char: \"\\ud83d\\udce8\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  \"e-mail\": {\n    keywords: [ \"communication\", \"inbox\" ],\n    char: \"\\ud83d\\udce7\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  love_letter: {\n    keywords: [ \"email\", \"like\", \"affection\", \"envelope\", \"valentines\" ],\n    char: \"\\ud83d\\udc8c\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  postbox: {\n    keywords: [ \"email\", \"letter\", \"envelope\" ],\n    char: \"\\ud83d\\udcee\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  mailbox_closed: {\n    keywords: [ \"email\", \"communication\", \"inbox\" ],\n    char: \"\\ud83d\\udcea\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  mailbox: {\n    keywords: [ \"email\", \"inbox\", \"communication\" ],\n    char: \"\\ud83d\\udceb\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  mailbox_with_mail: {\n    keywords: [ \"email\", \"inbox\", \"communication\" ],\n    char: \"\\ud83d\\udcec\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  mailbox_with_no_mail: {\n    keywords: [ \"email\", \"inbox\" ],\n    char: \"\\ud83d\\udced\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  package: {\n    keywords: [ \"mail\", \"gift\", \"cardboard\", \"box\", \"moving\" ],\n    char: \"\\ud83d\\udce6\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  postal_horn: {\n    keywords: [ \"instrument\", \"music\" ],\n    char: \"\\ud83d\\udcef\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  inbox_tray: {\n    keywords: [ \"email\", \"documents\" ],\n    char: \"\\ud83d\\udce5\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  outbox_tray: {\n    keywords: [ \"inbox\", \"email\" ],\n    char: \"\\ud83d\\udce4\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  scroll: {\n    keywords: [ \"documents\", \"ancient\", \"history\", \"paper\" ],\n    char: \"\\ud83d\\udcdc\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  page_with_curl: {\n    keywords: [ \"documents\", \"office\", \"paper\" ],\n    char: \"\\ud83d\\udcc3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  bookmark_tabs: {\n    keywords: [ \"favorite\", \"save\", \"order\", \"tidy\" ],\n    char: \"\\ud83d\\udcd1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  receipt: {\n    keywords: [ \"accounting\", \"expenses\" ],\n    char: \"\\ud83e\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  bar_chart: {\n    keywords: [ \"graph\", \"presentation\", \"stats\" ],\n    char: \"\\ud83d\\udcca\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  chart_with_upwards_trend: {\n    keywords: [ \"graph\", \"presentation\", \"stats\", \"recovery\", \"business\", \"economics\", \"money\", \"sales\", \"good\", \"success\" ],\n    char: \"\\ud83d\\udcc8\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  chart_with_downwards_trend: {\n    keywords: [ \"graph\", \"presentation\", \"stats\", \"recession\", \"business\", \"economics\", \"money\", \"sales\", \"bad\", \"failure\" ],\n    char: \"\\ud83d\\udcc9\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  page_facing_up: {\n    keywords: [ \"documents\", \"office\", \"paper\", \"information\" ],\n    char: \"\\ud83d\\udcc4\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  date: {\n    keywords: [ \"calendar\", \"schedule\" ],\n    char: \"\\ud83d\\udcc5\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  calendar: {\n    keywords: [ \"schedule\", \"date\", \"planning\" ],\n    char: \"\\ud83d\\udcc6\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  spiral_calendar: {\n    keywords: [ \"date\", \"schedule\", \"planning\" ],\n    char: \"\\ud83d\\uddd3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  card_index: {\n    keywords: [ \"business\", \"stationery\" ],\n    char: \"\\ud83d\\udcc7\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  card_file_box: {\n    keywords: [ \"business\", \"stationery\" ],\n    char: \"\\ud83d\\uddc3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  ballot_box: {\n    keywords: [ \"election\", \"vote\" ],\n    char: \"\\ud83d\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  file_cabinet: {\n    keywords: [ \"filing\", \"organizing\" ],\n    char: \"\\ud83d\\uddc4\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  clipboard: {\n    keywords: [ \"stationery\", \"documents\" ],\n    char: \"\\ud83d\\udccb\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  spiral_notepad: {\n    keywords: [ \"memo\", \"stationery\" ],\n    char: \"\\ud83d\\uddd2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  file_folder: {\n    keywords: [ \"documents\", \"business\", \"office\" ],\n    char: \"\\ud83d\\udcc1\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  open_file_folder: {\n    keywords: [ \"documents\", \"load\" ],\n    char: \"\\ud83d\\udcc2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  card_index_dividers: {\n    keywords: [ \"organizing\", \"business\", \"stationery\" ],\n    char: \"\\ud83d\\uddc2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  newspaper_roll: {\n    keywords: [ \"press\", \"headline\" ],\n    char: \"\\ud83d\\uddde\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  newspaper: {\n    keywords: [ \"press\", \"headline\" ],\n    char: \"\\ud83d\\udcf0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  notebook: {\n    keywords: [ \"stationery\", \"record\", \"notes\", \"paper\", \"study\" ],\n    char: \"\\ud83d\\udcd3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  closed_book: {\n    keywords: [ \"read\", \"library\", \"knowledge\", \"textbook\", \"learn\" ],\n    char: \"\\ud83d\\udcd5\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  green_book: {\n    keywords: [ \"read\", \"library\", \"knowledge\", \"study\" ],\n    char: \"\\ud83d\\udcd7\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  blue_book: {\n    keywords: [ \"read\", \"library\", \"knowledge\", \"learn\", \"study\" ],\n    char: \"\\ud83d\\udcd8\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  orange_book: {\n    keywords: [ \"read\", \"library\", \"knowledge\", \"textbook\", \"study\" ],\n    char: \"\\ud83d\\udcd9\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  notebook_with_decorative_cover: {\n    keywords: [ \"classroom\", \"notes\", \"record\", \"paper\", \"study\" ],\n    char: \"\\ud83d\\udcd4\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  ledger: {\n    keywords: [ \"notes\", \"paper\" ],\n    char: \"\\ud83d\\udcd2\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  books: {\n    keywords: [ \"literature\", \"library\", \"study\" ],\n    char: \"\\ud83d\\udcda\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  open_book: {\n    keywords: [ \"book\", \"read\", \"library\", \"knowledge\", \"literature\", \"learn\", \"study\" ],\n    char: \"\\ud83d\\udcd6\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  safety_pin: {\n    keywords: [ \"diaper\" ],\n    char: \"\\ud83e\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  link: {\n    keywords: [ \"rings\", \"url\" ],\n    char: \"\\ud83d\\udd17\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  paperclip: {\n    keywords: [ \"documents\", \"stationery\" ],\n    char: \"\\ud83d\\udcce\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  paperclips: {\n    keywords: [ \"documents\", \"stationery\" ],\n    char: \"\\ud83d\\udd87\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  scissors: {\n    keywords: [ \"stationery\", \"cut\" ],\n    char: \"\\u2702\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  triangular_ruler: {\n    keywords: [ \"stationery\", \"math\", \"architect\", \"sketch\" ],\n    char: \"\\ud83d\\udcd0\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  straight_ruler: {\n    keywords: [ \"stationery\", \"calculate\", \"length\", \"math\", \"school\", \"drawing\", \"architect\", \"sketch\" ],\n    char: \"\\ud83d\\udccf\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  abacus: {\n    keywords: [ \"calculation\" ],\n    char: \"\\ud83e\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  pushpin: {\n    keywords: [ \"stationery\", \"mark\", \"here\" ],\n    char: \"\\ud83d\\udccc\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  round_pushpin: {\n    keywords: [ \"stationery\", \"location\", \"map\", \"here\" ],\n    char: \"\\ud83d\\udccd\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  triangular_flag_on_post: {\n    keywords: [ \"mark\", \"milestone\", \"place\" ],\n    char: \"\\ud83d\\udea9\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  white_flag: {\n    keywords: [ \"losing\", \"loser\", \"lost\", \"surrender\", \"give up\", \"fail\" ],\n    char: \"\\ud83c\\udff3\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  black_flag: {\n    keywords: [ \"pirate\" ],\n    char: \"\\ud83c\\udff4\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  rainbow_flag: {\n    keywords: [ \"flag\", \"rainbow\", \"pride\", \"gay\", \"lgbt\", \"glbt\", \"queer\", \"homosexual\", \"lesbian\", \"bisexual\", \"transgender\" ],\n    char: \"\\ud83c\\udff3\\ufe0f\\u200d\\ud83c\\udf08\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  closed_lock_with_key: {\n    keywords: [ \"security\", \"privacy\" ],\n    char: \"\\ud83d\\udd10\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  lock: {\n    keywords: [ \"security\", \"password\", \"padlock\" ],\n    char: \"\\ud83d\\udd12\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  unlock: {\n    keywords: [ \"privacy\", \"security\" ],\n    char: \"\\ud83d\\udd13\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  lock_with_ink_pen: {\n    keywords: [ \"security\", \"secret\" ],\n    char: \"\\ud83d\\udd0f\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  pen: {\n    keywords: [ \"stationery\", \"writing\", \"write\" ],\n    char: \"\\ud83d\\udd8a\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  fountain_pen: {\n    keywords: [ \"stationery\", \"writing\", \"write\" ],\n    char: \"\\ud83d\\udd8b\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  black_nib: {\n    keywords: [ \"pen\", \"stationery\", \"writing\", \"write\" ],\n    char: \"\\u2712\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  memo: {\n    keywords: [ \"write\", \"documents\", \"stationery\", \"pencil\", \"paper\", \"writing\", \"legal\", \"exam\", \"quiz\", \"test\", \"study\", \"compose\" ],\n    char: \"\\ud83d\\udcdd\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  pencil2: {\n    keywords: [ \"stationery\", \"write\", \"paper\", \"writing\", \"school\", \"study\" ],\n    char: \"\\u270f\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  crayon: {\n    keywords: [ \"drawing\", \"creativity\" ],\n    char: \"\\ud83d\\udd8d\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  paintbrush: {\n    keywords: [ \"drawing\", \"creativity\", \"art\" ],\n    char: \"\\ud83d\\udd8c\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  mag: {\n    keywords: [ \"search\", \"zoom\", \"find\", \"detective\" ],\n    char: \"\\ud83d\\udd0d\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  mag_right: {\n    keywords: [ \"search\", \"zoom\", \"find\", \"detective\" ],\n    char: \"\\ud83d\\udd0e\",\n    fitzpatrick_scale: false,\n    category: \"objects\"\n  },\n  heart: {\n    keywords: [ \"love\", \"like\", \"valentines\" ],\n    char: \"\\u2764\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  orange_heart: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\" ],\n    char: \"\\ud83e\\udde1\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  yellow_heart: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\" ],\n    char: \"\\ud83d\\udc9b\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  green_heart: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\" ],\n    char: \"\\ud83d\\udc9a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  blue_heart: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\" ],\n    char: \"\\ud83d\\udc99\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  purple_heart: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\" ],\n    char: \"\\ud83d\\udc9c\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  black_heart: {\n    keywords: [ \"evil\" ],\n    char: \"\\ud83d\\udda4\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  broken_heart: {\n    keywords: [ \"sad\", \"sorry\", \"break\", \"heart\", \"heartbreak\" ],\n    char: \"\\ud83d\\udc94\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heavy_heart_exclamation: {\n    keywords: [ \"decoration\", \"love\" ],\n    char: \"\\u2763\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  two_hearts: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\", \"heart\" ],\n    char: \"\\ud83d\\udc95\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  revolving_hearts: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\" ],\n    char: \"\\ud83d\\udc9e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heartbeat: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\", \"pink\", \"heart\" ],\n    char: \"\\ud83d\\udc93\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heartpulse: {\n    keywords: [ \"like\", \"love\", \"affection\", \"valentines\", \"pink\" ],\n    char: \"\\ud83d\\udc97\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  sparkling_heart: {\n    keywords: [ \"love\", \"like\", \"affection\", \"valentines\" ],\n    char: \"\\ud83d\\udc96\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  cupid: {\n    keywords: [ \"love\", \"like\", \"heart\", \"affection\", \"valentines\" ],\n    char: \"\\ud83d\\udc98\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  gift_heart: {\n    keywords: [ \"love\", \"valentines\" ],\n    char: \"\\ud83d\\udc9d\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heart_decoration: {\n    keywords: [ \"purple-square\", \"love\", \"like\" ],\n    char: \"\\ud83d\\udc9f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  peace_symbol: {\n    keywords: [ \"hippie\" ],\n    char: \"\\u262e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  latin_cross: {\n    keywords: [ \"christianity\" ],\n    char: \"\\u271d\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  star_and_crescent: {\n    keywords: [ \"islam\" ],\n    char: \"\\u262a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  om: {\n    keywords: [ \"hinduism\", \"buddhism\", \"sikhism\", \"jainism\" ],\n    char: \"\\ud83d\\udd49\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  wheel_of_dharma: {\n    keywords: [ \"hinduism\", \"buddhism\", \"sikhism\", \"jainism\" ],\n    char: \"\\u2638\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  star_of_david: {\n    keywords: [ \"judaism\" ],\n    char: \"\\u2721\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  six_pointed_star: {\n    keywords: [ \"purple-square\", \"religion\", \"jewish\", \"hexagram\" ],\n    char: \"\\ud83d\\udd2f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  menorah: {\n    keywords: [ \"hanukkah\", \"candles\", \"jewish\" ],\n    char: \"\\ud83d\\udd4e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  yin_yang: {\n    keywords: [ \"balance\" ],\n    char: \"\\u262f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  orthodox_cross: {\n    keywords: [ \"suppedaneum\", \"religion\" ],\n    char: \"\\u2626\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  place_of_worship: {\n    keywords: [ \"religion\", \"church\", \"temple\", \"prayer\" ],\n    char: \"\\ud83d\\uded0\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  ophiuchus: {\n    keywords: [ \"sign\", \"purple-square\", \"constellation\", \"astrology\" ],\n    char: \"\\u26ce\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  aries: {\n    keywords: [ \"sign\", \"purple-square\", \"zodiac\", \"astrology\" ],\n    char: \"\\u2648\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  taurus: {\n    keywords: [ \"purple-square\", \"sign\", \"zodiac\", \"astrology\" ],\n    char: \"\\u2649\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  gemini: {\n    keywords: [ \"sign\", \"zodiac\", \"purple-square\", \"astrology\" ],\n    char: \"\\u264a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  cancer: {\n    keywords: [ \"sign\", \"zodiac\", \"purple-square\", \"astrology\" ],\n    char: \"\\u264b\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  leo: {\n    keywords: [ \"sign\", \"purple-square\", \"zodiac\", \"astrology\" ],\n    char: \"\\u264c\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  virgo: {\n    keywords: [ \"sign\", \"zodiac\", \"purple-square\", \"astrology\" ],\n    char: \"\\u264d\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  libra: {\n    keywords: [ \"sign\", \"purple-square\", \"zodiac\", \"astrology\" ],\n    char: \"\\u264e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  scorpius: {\n    keywords: [ \"sign\", \"zodiac\", \"purple-square\", \"astrology\", \"scorpio\" ],\n    char: \"\\u264f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  sagittarius: {\n    keywords: [ \"sign\", \"zodiac\", \"purple-square\", \"astrology\" ],\n    char: \"\\u2650\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  capricorn: {\n    keywords: [ \"sign\", \"zodiac\", \"purple-square\", \"astrology\" ],\n    char: \"\\u2651\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  aquarius: {\n    keywords: [ \"sign\", \"purple-square\", \"zodiac\", \"astrology\" ],\n    char: \"\\u2652\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  pisces: {\n    keywords: [ \"purple-square\", \"sign\", \"zodiac\", \"astrology\" ],\n    char: \"\\u2653\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  id: {\n    keywords: [ \"purple-square\", \"words\" ],\n    char: \"\\ud83c\\udd94\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  atom_symbol: {\n    keywords: [ \"science\", \"physics\", \"chemistry\" ],\n    char: \"\\u269b\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u7a7a: {\n    keywords: [ \"kanji\", \"japanese\", \"chinese\", \"empty\", \"sky\", \"blue-square\" ],\n    char: \"\\ud83c\\ude33\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u5272: {\n    keywords: [ \"cut\", \"divide\", \"chinese\", \"kanji\", \"pink-square\" ],\n    char: \"\\ud83c\\ude39\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  radioactive: {\n    keywords: [ \"nuclear\", \"danger\" ],\n    char: \"\\u2622\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  biohazard: {\n    keywords: [ \"danger\" ],\n    char: \"\\u2623\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  mobile_phone_off: {\n    keywords: [ \"mute\", \"orange-square\", \"silence\", \"quiet\" ],\n    char: \"\\ud83d\\udcf4\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  vibration_mode: {\n    keywords: [ \"orange-square\", \"phone\" ],\n    char: \"\\ud83d\\udcf3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u6709: {\n    keywords: [ \"orange-square\", \"chinese\", \"have\", \"kanji\" ],\n    char: \"\\ud83c\\ude36\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u7121: {\n    keywords: [ \"nothing\", \"chinese\", \"kanji\", \"japanese\", \"orange-square\" ],\n    char: \"\\ud83c\\ude1a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u7533: {\n    keywords: [ \"chinese\", \"japanese\", \"kanji\", \"orange-square\" ],\n    char: \"\\ud83c\\ude38\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u55b6: {\n    keywords: [ \"japanese\", \"opening hours\", \"orange-square\" ],\n    char: \"\\ud83c\\ude3a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u6708: {\n    keywords: [ \"chinese\", \"month\", \"moon\", \"japanese\", \"orange-square\", \"kanji\" ],\n    char: \"\\ud83c\\ude37\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  eight_pointed_black_star: {\n    keywords: [ \"orange-square\", \"shape\", \"polygon\" ],\n    char: \"\\u2734\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  vs: {\n    keywords: [ \"words\", \"orange-square\" ],\n    char: \"\\ud83c\\udd9a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  accept: {\n    keywords: [ \"ok\", \"good\", \"chinese\", \"kanji\", \"agree\", \"yes\", \"orange-circle\" ],\n    char: \"\\ud83c\\ude51\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  white_flower: {\n    keywords: [ \"japanese\", \"spring\" ],\n    char: \"\\ud83d\\udcae\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  ideograph_advantage: {\n    keywords: [ \"chinese\", \"kanji\", \"obtain\", \"get\", \"circle\" ],\n    char: \"\\ud83c\\ude50\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  secret: {\n    keywords: [ \"privacy\", \"chinese\", \"sshh\", \"kanji\", \"red-circle\" ],\n    char: \"\\u3299\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  congratulations: {\n    keywords: [ \"chinese\", \"kanji\", \"japanese\", \"red-circle\" ],\n    char: \"\\u3297\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u5408: {\n    keywords: [ \"japanese\", \"chinese\", \"join\", \"kanji\", \"red-square\" ],\n    char: \"\\ud83c\\ude34\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u6e80: {\n    keywords: [ \"full\", \"chinese\", \"japanese\", \"red-square\", \"kanji\" ],\n    char: \"\\ud83c\\ude35\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u7981: {\n    keywords: [ \"kanji\", \"japanese\", \"chinese\", \"forbidden\", \"limit\", \"restricted\", \"red-square\" ],\n    char: \"\\ud83c\\ude32\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  a: {\n    keywords: [ \"red-square\", \"alphabet\", \"letter\" ],\n    char: \"\\ud83c\\udd70\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  b: {\n    keywords: [ \"red-square\", \"alphabet\", \"letter\" ],\n    char: \"\\ud83c\\udd71\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  ab: {\n    keywords: [ \"red-square\", \"alphabet\" ],\n    char: \"\\ud83c\\udd8e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  cl: {\n    keywords: [ \"alphabet\", \"words\", \"red-square\" ],\n    char: \"\\ud83c\\udd91\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  o2: {\n    keywords: [ \"alphabet\", \"red-square\", \"letter\" ],\n    char: \"\\ud83c\\udd7e\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  sos: {\n    keywords: [ \"help\", \"red-square\", \"words\", \"emergency\", \"911\" ],\n    char: \"\\ud83c\\udd98\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  no_entry: {\n    keywords: [ \"limit\", \"security\", \"privacy\", \"bad\", \"denied\", \"stop\", \"circle\" ],\n    char: \"\\u26d4\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  name_badge: {\n    keywords: [ \"fire\", \"forbid\" ],\n    char: \"\\ud83d\\udcdb\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  no_entry_sign: {\n    keywords: [ \"forbid\", \"stop\", \"limit\", \"denied\", \"disallow\", \"circle\" ],\n    char: \"\\ud83d\\udeab\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  x: {\n    keywords: [ \"no\", \"delete\", \"remove\", \"cancel\", \"red\" ],\n    char: \"\\u274c\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  o: {\n    keywords: [ \"circle\", \"round\" ],\n    char: \"\\u2b55\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  stop_sign: {\n    keywords: [ \"stop\" ],\n    char: \"\\ud83d\\uded1\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  anger: {\n    keywords: [ \"angry\", \"mad\" ],\n    char: \"\\ud83d\\udca2\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  hotsprings: {\n    keywords: [ \"bath\", \"warm\", \"relax\" ],\n    char: \"\\u2668\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  no_pedestrians: {\n    keywords: [ \"rules\", \"crossing\", \"walking\", \"circle\" ],\n    char: \"\\ud83d\\udeb7\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  do_not_litter: {\n    keywords: [ \"trash\", \"bin\", \"garbage\", \"circle\" ],\n    char: \"\\ud83d\\udeaf\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  no_bicycles: {\n    keywords: [ \"cyclist\", \"prohibited\", \"circle\" ],\n    char: \"\\ud83d\\udeb3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  \"non-potable_water\": {\n    keywords: [ \"drink\", \"faucet\", \"tap\", \"circle\" ],\n    char: \"\\ud83d\\udeb1\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  underage: {\n    keywords: [ \"18\", \"drink\", \"pub\", \"night\", \"minor\", \"circle\" ],\n    char: \"\\ud83d\\udd1e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  no_mobile_phones: {\n    keywords: [ \"iphone\", \"mute\", \"circle\" ],\n    char: \"\\ud83d\\udcf5\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  exclamation: {\n    keywords: [ \"heavy_exclamation_mark\", \"danger\", \"surprise\", \"punctuation\", \"wow\", \"warning\" ],\n    char: \"\\u2757\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  grey_exclamation: {\n    keywords: [ \"surprise\", \"punctuation\", \"gray\", \"wow\", \"warning\" ],\n    char: \"\\u2755\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  question: {\n    keywords: [ \"doubt\", \"confused\" ],\n    char: \"\\u2753\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  grey_question: {\n    keywords: [ \"doubts\", \"gray\", \"huh\", \"confused\" ],\n    char: \"\\u2754\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  bangbang: {\n    keywords: [ \"exclamation\", \"surprise\" ],\n    char: \"\\u203c\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  interrobang: {\n    keywords: [ \"wat\", \"punctuation\", \"surprise\" ],\n    char: \"\\u2049\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  100: {\n    keywords: [ \"score\", \"perfect\", \"numbers\", \"century\", \"exam\", \"quiz\", \"test\", \"pass\", \"hundred\" ],\n    char: \"\\ud83d\\udcaf\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  low_brightness: {\n    keywords: [ \"sun\", \"afternoon\", \"warm\", \"summer\" ],\n    char: \"\\ud83d\\udd05\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  high_brightness: {\n    keywords: [ \"sun\", \"light\" ],\n    char: \"\\ud83d\\udd06\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  trident: {\n    keywords: [ \"weapon\", \"spear\" ],\n    char: \"\\ud83d\\udd31\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  fleur_de_lis: {\n    keywords: [ \"decorative\", \"scout\" ],\n    char: \"\\u269c\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  part_alternation_mark: {\n    keywords: [ \"graph\", \"presentation\", \"stats\", \"business\", \"economics\", \"bad\" ],\n    char: \"\\u303d\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  warning: {\n    keywords: [ \"exclamation\", \"wip\", \"alert\", \"error\", \"problem\", \"issue\" ],\n    char: \"\\u26a0\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  children_crossing: {\n    keywords: [ \"school\", \"warning\", \"danger\", \"sign\", \"driving\", \"yellow-diamond\" ],\n    char: \"\\ud83d\\udeb8\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  beginner: {\n    keywords: [ \"badge\", \"shield\" ],\n    char: \"\\ud83d\\udd30\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  recycle: {\n    keywords: [ \"arrow\", \"environment\", \"garbage\", \"trash\" ],\n    char: \"\\u267b\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  u6307: {\n    keywords: [ \"chinese\", \"point\", \"green-square\", \"kanji\" ],\n    char: \"\\ud83c\\ude2f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  chart: {\n    keywords: [ \"green-square\", \"graph\", \"presentation\", \"stats\" ],\n    char: \"\\ud83d\\udcb9\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  sparkle: {\n    keywords: [ \"stars\", \"green-square\", \"awesome\", \"good\", \"fireworks\" ],\n    char: \"\\u2747\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  eight_spoked_asterisk: {\n    keywords: [ \"star\", \"sparkle\", \"green-square\" ],\n    char: \"\\u2733\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  negative_squared_cross_mark: {\n    keywords: [ \"x\", \"green-square\", \"no\", \"deny\" ],\n    char: \"\\u274e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  white_check_mark: {\n    keywords: [ \"green-square\", \"ok\", \"agree\", \"vote\", \"election\", \"answer\", \"tick\" ],\n    char: \"\\u2705\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  diamond_shape_with_a_dot_inside: {\n    keywords: [ \"jewel\", \"blue\", \"gem\", \"crystal\", \"fancy\" ],\n    char: \"\\ud83d\\udca0\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  cyclone: {\n    keywords: [ \"weather\", \"swirl\", \"blue\", \"cloud\", \"vortex\", \"spiral\", \"whirlpool\", \"spin\", \"tornado\", \"hurricane\", \"typhoon\" ],\n    char: \"\\ud83c\\udf00\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  loop: {\n    keywords: [ \"tape\", \"cassette\" ],\n    char: \"\\u27bf\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  globe_with_meridians: {\n    keywords: [ \"earth\", \"international\", \"world\", \"internet\", \"interweb\", \"i18n\" ],\n    char: \"\\ud83c\\udf10\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  m: {\n    keywords: [ \"alphabet\", \"blue-circle\", \"letter\" ],\n    char: \"\\u24c2\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  atm: {\n    keywords: [ \"money\", \"sales\", \"cash\", \"blue-square\", \"payment\", \"bank\" ],\n    char: \"\\ud83c\\udfe7\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  sa: {\n    keywords: [ \"japanese\", \"blue-square\", \"katakana\" ],\n    char: \"\\ud83c\\ude02\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  passport_control: {\n    keywords: [ \"custom\", \"blue-square\" ],\n    char: \"\\ud83d\\udec2\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  customs: {\n    keywords: [ \"passport\", \"border\", \"blue-square\" ],\n    char: \"\\ud83d\\udec3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  baggage_claim: {\n    keywords: [ \"blue-square\", \"airport\", \"transport\" ],\n    char: \"\\ud83d\\udec4\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  left_luggage: {\n    keywords: [ \"blue-square\", \"travel\" ],\n    char: \"\\ud83d\\udec5\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  wheelchair: {\n    keywords: [ \"blue-square\", \"disabled\", \"a11y\", \"accessibility\" ],\n    char: \"\\u267f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  no_smoking: {\n    keywords: [ \"cigarette\", \"blue-square\", \"smell\", \"smoke\" ],\n    char: \"\\ud83d\\udead\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  wc: {\n    keywords: [ \"toilet\", \"restroom\", \"blue-square\" ],\n    char: \"\\ud83d\\udebe\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  parking: {\n    keywords: [ \"cars\", \"blue-square\", \"alphabet\", \"letter\" ],\n    char: \"\\ud83c\\udd7f\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  potable_water: {\n    keywords: [ \"blue-square\", \"liquid\", \"restroom\", \"cleaning\", \"faucet\" ],\n    char: \"\\ud83d\\udeb0\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  mens: {\n    keywords: [ \"toilet\", \"restroom\", \"wc\", \"blue-square\", \"gender\", \"male\" ],\n    char: \"\\ud83d\\udeb9\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  womens: {\n    keywords: [ \"purple-square\", \"woman\", \"female\", \"toilet\", \"loo\", \"restroom\", \"gender\" ],\n    char: \"\\ud83d\\udeba\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  baby_symbol: {\n    keywords: [ \"orange-square\", \"child\" ],\n    char: \"\\ud83d\\udebc\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  restroom: {\n    keywords: [ \"blue-square\", \"toilet\", \"refresh\", \"wc\", \"gender\" ],\n    char: \"\\ud83d\\udebb\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  put_litter_in_its_place: {\n    keywords: [ \"blue-square\", \"sign\", \"human\", \"info\" ],\n    char: \"\\ud83d\\udeae\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  cinema: {\n    keywords: [ \"blue-square\", \"record\", \"film\", \"movie\", \"curtain\", \"stage\", \"theater\" ],\n    char: \"\\ud83c\\udfa6\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  signal_strength: {\n    keywords: [ \"blue-square\", \"reception\", \"phone\", \"internet\", \"connection\", \"wifi\", \"bluetooth\", \"bars\" ],\n    char: \"\\ud83d\\udcf6\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  koko: {\n    keywords: [ \"blue-square\", \"here\", \"katakana\", \"japanese\", \"destination\" ],\n    char: \"\\ud83c\\ude01\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  ng: {\n    keywords: [ \"blue-square\", \"words\", \"shape\", \"icon\" ],\n    char: \"\\ud83c\\udd96\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  ok: {\n    keywords: [ \"good\", \"agree\", \"yes\", \"blue-square\" ],\n    char: \"\\ud83c\\udd97\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  up: {\n    keywords: [ \"blue-square\", \"above\", \"high\" ],\n    char: \"\\ud83c\\udd99\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  cool: {\n    keywords: [ \"words\", \"blue-square\" ],\n    char: \"\\ud83c\\udd92\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  new: {\n    keywords: [ \"blue-square\", \"words\", \"start\" ],\n    char: \"\\ud83c\\udd95\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  free: {\n    keywords: [ \"blue-square\", \"words\" ],\n    char: \"\\ud83c\\udd93\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  zero: {\n    keywords: [ \"0\", \"numbers\", \"blue-square\", \"null\" ],\n    char: \"0\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  one: {\n    keywords: [ \"blue-square\", \"numbers\", \"1\" ],\n    char: \"1\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  two: {\n    keywords: [ \"numbers\", \"2\", \"prime\", \"blue-square\" ],\n    char: \"2\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  three: {\n    keywords: [ \"3\", \"numbers\", \"prime\", \"blue-square\" ],\n    char: \"3\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  four: {\n    keywords: [ \"4\", \"numbers\", \"blue-square\" ],\n    char: \"4\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  five: {\n    keywords: [ \"5\", \"numbers\", \"blue-square\", \"prime\" ],\n    char: \"5\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  six: {\n    keywords: [ \"6\", \"numbers\", \"blue-square\" ],\n    char: \"6\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  seven: {\n    keywords: [ \"7\", \"numbers\", \"blue-square\", \"prime\" ],\n    char: \"7\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  eight: {\n    keywords: [ \"8\", \"blue-square\", \"numbers\" ],\n    char: \"8\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  nine: {\n    keywords: [ \"blue-square\", \"numbers\", \"9\" ],\n    char: \"9\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  keycap_ten: {\n    keywords: [ \"numbers\", \"10\", \"blue-square\" ],\n    char: \"\\ud83d\\udd1f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  asterisk: {\n    keywords: [ \"star\", \"keycap\" ],\n    char: \"*\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  1234: {\n    keywords: [ \"numbers\", \"blue-square\" ],\n    char: \"\\ud83d\\udd22\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  eject_button: {\n    keywords: [ \"blue-square\" ],\n    char: \"\\u23cf\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_forward: {\n    keywords: [ \"blue-square\", \"right\", \"direction\", \"play\" ],\n    char: \"\\u25b6\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  pause_button: {\n    keywords: [ \"pause\", \"blue-square\" ],\n    char: \"\\u23f8\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  next_track_button: {\n    keywords: [ \"forward\", \"next\", \"blue-square\" ],\n    char: \"\\u23ed\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  stop_button: {\n    keywords: [ \"blue-square\" ],\n    char: \"\\u23f9\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  record_button: {\n    keywords: [ \"blue-square\" ],\n    char: \"\\u23fa\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  play_or_pause_button: {\n    keywords: [ \"blue-square\", \"play\", \"pause\" ],\n    char: \"\\u23ef\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  previous_track_button: {\n    keywords: [ \"backward\" ],\n    char: \"\\u23ee\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  fast_forward: {\n    keywords: [ \"blue-square\", \"play\", \"speed\", \"continue\" ],\n    char: \"\\u23e9\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  rewind: {\n    keywords: [ \"play\", \"blue-square\" ],\n    char: \"\\u23ea\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  twisted_rightwards_arrows: {\n    keywords: [ \"blue-square\", \"shuffle\", \"music\", \"random\" ],\n    char: \"\\ud83d\\udd00\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  repeat: {\n    keywords: [ \"loop\", \"record\" ],\n    char: \"\\ud83d\\udd01\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  repeat_one: {\n    keywords: [ \"blue-square\", \"loop\" ],\n    char: \"\\ud83d\\udd02\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_backward: {\n    keywords: [ \"blue-square\", \"left\", \"direction\" ],\n    char: \"\\u25c0\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_up_small: {\n    keywords: [ \"blue-square\", \"triangle\", \"direction\", \"point\", \"forward\", \"top\" ],\n    char: \"\\ud83d\\udd3c\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_down_small: {\n    keywords: [ \"blue-square\", \"direction\", \"bottom\" ],\n    char: \"\\ud83d\\udd3d\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_double_up: {\n    keywords: [ \"blue-square\", \"direction\", \"top\" ],\n    char: \"\\u23eb\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_double_down: {\n    keywords: [ \"blue-square\", \"direction\", \"bottom\" ],\n    char: \"\\u23ec\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_right: {\n    keywords: [ \"blue-square\", \"next\" ],\n    char: \"\\u27a1\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_left: {\n    keywords: [ \"blue-square\", \"previous\", \"back\" ],\n    char: \"\\u2b05\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_up: {\n    keywords: [ \"blue-square\", \"continue\", \"top\", \"direction\" ],\n    char: \"\\u2b06\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_down: {\n    keywords: [ \"blue-square\", \"direction\", \"bottom\" ],\n    char: \"\\u2b07\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_upper_right: {\n    keywords: [ \"blue-square\", \"point\", \"direction\", \"diagonal\", \"northeast\" ],\n    char: \"\\u2197\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_lower_right: {\n    keywords: [ \"blue-square\", \"direction\", \"diagonal\", \"southeast\" ],\n    char: \"\\u2198\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_lower_left: {\n    keywords: [ \"blue-square\", \"direction\", \"diagonal\", \"southwest\" ],\n    char: \"\\u2199\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_upper_left: {\n    keywords: [ \"blue-square\", \"point\", \"direction\", \"diagonal\", \"northwest\" ],\n    char: \"\\u2196\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_up_down: {\n    keywords: [ \"blue-square\", \"direction\", \"way\", \"vertical\" ],\n    char: \"\\u2195\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  left_right_arrow: {\n    keywords: [ \"shape\", \"direction\", \"horizontal\", \"sideways\" ],\n    char: \"\\u2194\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrows_counterclockwise: {\n    keywords: [ \"blue-square\", \"sync\", \"cycle\" ],\n    char: \"\\ud83d\\udd04\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_right_hook: {\n    keywords: [ \"blue-square\", \"return\", \"rotate\", \"direction\" ],\n    char: \"\\u21aa\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  leftwards_arrow_with_hook: {\n    keywords: [ \"back\", \"return\", \"blue-square\", \"undo\", \"enter\" ],\n    char: \"\\u21a9\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_heading_up: {\n    keywords: [ \"blue-square\", \"direction\", \"top\" ],\n    char: \"\\u2934\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrow_heading_down: {\n    keywords: [ \"blue-square\", \"direction\", \"bottom\" ],\n    char: \"\\u2935\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  hash: {\n    keywords: [ \"symbol\", \"blue-square\", \"twitter\" ],\n    char: \"#\\ufe0f\\u20e3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  information_source: {\n    keywords: [ \"blue-square\", \"alphabet\", \"letter\" ],\n    char: \"\\u2139\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  abc: {\n    keywords: [ \"blue-square\", \"alphabet\" ],\n    char: \"\\ud83d\\udd24\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  abcd: {\n    keywords: [ \"blue-square\", \"alphabet\" ],\n    char: \"\\ud83d\\udd21\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  capital_abcd: {\n    keywords: [ \"alphabet\", \"words\", \"blue-square\" ],\n    char: \"\\ud83d\\udd20\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  symbols: {\n    keywords: [ \"blue-square\", \"music\", \"note\", \"ampersand\", \"percent\", \"glyphs\", \"characters\" ],\n    char: \"\\ud83d\\udd23\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  musical_note: {\n    keywords: [ \"score\", \"tone\", \"sound\" ],\n    char: \"\\ud83c\\udfb5\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  notes: {\n    keywords: [ \"music\", \"score\" ],\n    char: \"\\ud83c\\udfb6\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  wavy_dash: {\n    keywords: [ \"draw\", \"line\", \"moustache\", \"mustache\", \"squiggle\", \"scribble\" ],\n    char: \"\\u3030\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  curly_loop: {\n    keywords: [ \"scribble\", \"draw\", \"shape\", \"squiggle\" ],\n    char: \"\\u27b0\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heavy_check_mark: {\n    keywords: [ \"ok\", \"nike\", \"answer\", \"yes\", \"tick\" ],\n    char: \"\\u2714\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  arrows_clockwise: {\n    keywords: [ \"sync\", \"cycle\", \"round\", \"repeat\" ],\n    char: \"\\ud83d\\udd03\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heavy_plus_sign: {\n    keywords: [ \"math\", \"calculation\", \"addition\", \"more\", \"increase\" ],\n    char: \"\\u2795\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heavy_minus_sign: {\n    keywords: [ \"math\", \"calculation\", \"subtract\", \"less\" ],\n    char: \"\\u2796\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heavy_division_sign: {\n    keywords: [ \"divide\", \"math\", \"calculation\" ],\n    char: \"\\u2797\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heavy_multiplication_x: {\n    keywords: [ \"math\", \"calculation\" ],\n    char: \"\\u2716\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  infinity: {\n    keywords: [ \"forever\" ],\n    char: \"\\u267e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  heavy_dollar_sign: {\n    keywords: [ \"money\", \"sales\", \"payment\", \"currency\", \"buck\" ],\n    char: \"\\ud83d\\udcb2\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  currency_exchange: {\n    keywords: [ \"money\", \"sales\", \"dollar\", \"travel\" ],\n    char: \"\\ud83d\\udcb1\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  copyright: {\n    keywords: [ \"ip\", \"license\", \"circle\", \"law\", \"legal\" ],\n    char: \"\\xa9\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  registered: {\n    keywords: [ \"alphabet\", \"circle\" ],\n    char: \"\\xae\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  tm: {\n    keywords: [ \"trademark\", \"brand\", \"law\", \"legal\" ],\n    char: \"\\u2122\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  end: {\n    keywords: [ \"words\", \"arrow\" ],\n    char: \"\\ud83d\\udd1a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  back: {\n    keywords: [ \"arrow\", \"words\", \"return\" ],\n    char: \"\\ud83d\\udd19\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  on: {\n    keywords: [ \"arrow\", \"words\" ],\n    char: \"\\ud83d\\udd1b\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  top: {\n    keywords: [ \"words\", \"blue-square\" ],\n    char: \"\\ud83d\\udd1d\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  soon: {\n    keywords: [ \"arrow\", \"words\" ],\n    char: \"\\ud83d\\udd1c\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  ballot_box_with_check: {\n    keywords: [ \"ok\", \"agree\", \"confirm\", \"black-square\", \"vote\", \"election\", \"yes\", \"tick\" ],\n    char: \"\\u2611\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  radio_button: {\n    keywords: [ \"input\", \"old\", \"music\", \"circle\" ],\n    char: \"\\ud83d\\udd18\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  white_circle: {\n    keywords: [ \"shape\", \"round\" ],\n    char: \"\\u26aa\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  black_circle: {\n    keywords: [ \"shape\", \"button\", \"round\" ],\n    char: \"\\u26ab\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  red_circle: {\n    keywords: [ \"shape\", \"error\", \"danger\" ],\n    char: \"\\ud83d\\udd34\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  large_blue_circle: {\n    keywords: [ \"shape\", \"icon\", \"button\" ],\n    char: \"\\ud83d\\udd35\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  small_orange_diamond: {\n    keywords: [ \"shape\", \"jewel\", \"gem\" ],\n    char: \"\\ud83d\\udd38\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  small_blue_diamond: {\n    keywords: [ \"shape\", \"jewel\", \"gem\" ],\n    char: \"\\ud83d\\udd39\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  large_orange_diamond: {\n    keywords: [ \"shape\", \"jewel\", \"gem\" ],\n    char: \"\\ud83d\\udd36\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  large_blue_diamond: {\n    keywords: [ \"shape\", \"jewel\", \"gem\" ],\n    char: \"\\ud83d\\udd37\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  small_red_triangle: {\n    keywords: [ \"shape\", \"direction\", \"up\", \"top\" ],\n    char: \"\\ud83d\\udd3a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  black_small_square: {\n    keywords: [ \"shape\", \"icon\" ],\n    char: \"\\u25aa\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  white_small_square: {\n    keywords: [ \"shape\", \"icon\" ],\n    char: \"\\u25ab\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  black_large_square: {\n    keywords: [ \"shape\", \"icon\", \"button\" ],\n    char: \"\\u2b1b\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  white_large_square: {\n    keywords: [ \"shape\", \"icon\", \"stone\", \"button\" ],\n    char: \"\\u2b1c\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  small_red_triangle_down: {\n    keywords: [ \"shape\", \"direction\", \"bottom\" ],\n    char: \"\\ud83d\\udd3b\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  black_medium_square: {\n    keywords: [ \"shape\", \"button\", \"icon\" ],\n    char: \"\\u25fc\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  white_medium_square: {\n    keywords: [ \"shape\", \"stone\", \"icon\" ],\n    char: \"\\u25fb\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  black_medium_small_square: {\n    keywords: [ \"icon\", \"shape\", \"button\" ],\n    char: \"\\u25fe\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  white_medium_small_square: {\n    keywords: [ \"shape\", \"stone\", \"icon\", \"button\" ],\n    char: \"\\u25fd\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  black_square_button: {\n    keywords: [ \"shape\", \"input\", \"frame\" ],\n    char: \"\\ud83d\\udd32\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  white_square_button: {\n    keywords: [ \"shape\", \"input\" ],\n    char: \"\\ud83d\\udd33\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  speaker: {\n    keywords: [ \"sound\", \"volume\", \"silence\", \"broadcast\" ],\n    char: \"\\ud83d\\udd08\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  sound: {\n    keywords: [ \"volume\", \"speaker\", \"broadcast\" ],\n    char: \"\\ud83d\\udd09\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  loud_sound: {\n    keywords: [ \"volume\", \"noise\", \"noisy\", \"speaker\", \"broadcast\" ],\n    char: \"\\ud83d\\udd0a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  mute: {\n    keywords: [ \"sound\", \"volume\", \"silence\", \"quiet\" ],\n    char: \"\\ud83d\\udd07\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  mega: {\n    keywords: [ \"sound\", \"speaker\", \"volume\" ],\n    char: \"\\ud83d\\udce3\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  loudspeaker: {\n    keywords: [ \"volume\", \"sound\" ],\n    char: \"\\ud83d\\udce2\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  bell: {\n    keywords: [ \"sound\", \"notification\", \"christmas\", \"xmas\", \"chime\" ],\n    char: \"\\ud83d\\udd14\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  no_bell: {\n    keywords: [ \"sound\", \"volume\", \"mute\", \"quiet\", \"silent\" ],\n    char: \"\\ud83d\\udd15\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  black_joker: {\n    keywords: [ \"poker\", \"cards\", \"game\", \"play\", \"magic\" ],\n    char: \"\\ud83c\\udccf\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  mahjong: {\n    keywords: [ \"game\", \"play\", \"chinese\", \"kanji\" ],\n    char: \"\\ud83c\\udc04\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  spades: {\n    keywords: [ \"poker\", \"cards\", \"suits\", \"magic\" ],\n    char: \"\\u2660\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clubs: {\n    keywords: [ \"poker\", \"cards\", \"magic\", \"suits\" ],\n    char: \"\\u2663\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  hearts: {\n    keywords: [ \"poker\", \"cards\", \"magic\", \"suits\" ],\n    char: \"\\u2665\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  diamonds: {\n    keywords: [ \"poker\", \"cards\", \"magic\", \"suits\" ],\n    char: \"\\u2666\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  flower_playing_cards: {\n    keywords: [ \"game\", \"sunset\", \"red\" ],\n    char: \"\\ud83c\\udfb4\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  thought_balloon: {\n    keywords: [ \"bubble\", \"cloud\", \"speech\", \"thinking\", \"dream\" ],\n    char: \"\\ud83d\\udcad\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  right_anger_bubble: {\n    keywords: [ \"caption\", \"speech\", \"thinking\", \"mad\" ],\n    char: \"\\ud83d\\uddef\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  speech_balloon: {\n    keywords: [ \"bubble\", \"words\", \"message\", \"talk\", \"chatting\" ],\n    char: \"\\ud83d\\udcac\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  left_speech_bubble: {\n    keywords: [ \"words\", \"message\", \"talk\", \"chatting\" ],\n    char: \"\\ud83d\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock1: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd50\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock2: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd51\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock3: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd52\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock4: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd53\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock5: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd54\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock6: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\", \"dawn\", \"dusk\" ],\n    char: \"\\ud83d\\udd55\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock7: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd56\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock8: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd57\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock9: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd58\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock10: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd59\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock11: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd5a\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock12: {\n    keywords: [ \"time\", \"noon\", \"midnight\", \"midday\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd5b\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock130: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd5c\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock230: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd5d\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock330: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd5e\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock430: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd5f\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock530: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd60\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock630: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd61\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock730: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd62\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock830: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd63\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock930: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd64\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock1030: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd65\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock1130: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd66\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  clock1230: {\n    keywords: [ \"time\", \"late\", \"early\", \"schedule\" ],\n    char: \"\\ud83d\\udd67\",\n    fitzpatrick_scale: false,\n    category: \"symbols\"\n  },\n  afghanistan: {\n    keywords: [ \"af\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  aland_islands: {\n    keywords: [ \"\\xc5land\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddfd\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  albania: {\n    keywords: [ \"al\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  algeria: {\n    keywords: [ \"dz\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde9\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  american_samoa: {\n    keywords: [ \"american\", \"ws\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  andorra: {\n    keywords: [ \"ad\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  angola: {\n    keywords: [ \"ao\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  anguilla: {\n    keywords: [ \"ai\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  antarctica: {\n    keywords: [ \"aq\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddf6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  antigua_barbuda: {\n    keywords: [ \"antigua\", \"barbuda\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  argentina: {\n    keywords: [ \"ar\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  armenia: {\n    keywords: [ \"am\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  aruba: {\n    keywords: [ \"aw\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  australia: {\n    keywords: [ \"au\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  austria: {\n    keywords: [ \"at\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  azerbaijan: {\n    keywords: [ \"az\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  bahamas: {\n    keywords: [ \"bs\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  bahrain: {\n    keywords: [ \"bh\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  bangladesh: {\n    keywords: [ \"bd\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  barbados: {\n    keywords: [ \"bb\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\udde7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  belarus: {\n    keywords: [ \"by\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  belgium: {\n    keywords: [ \"be\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  belize: {\n    keywords: [ \"bz\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  benin: {\n    keywords: [ \"bj\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddef\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  bermuda: {\n    keywords: [ \"bm\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  bhutan: {\n    keywords: [ \"bt\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  bolivia: {\n    keywords: [ \"bo\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  caribbean_netherlands: {\n    keywords: [ \"bonaire\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddf6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  bosnia_herzegovina: {\n    keywords: [ \"bosnia\", \"herzegovina\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  botswana: {\n    keywords: [ \"bw\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  brazil: {\n    keywords: [ \"br\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  british_indian_ocean_territory: {\n    keywords: [ \"british\", \"indian\", \"ocean\", \"territory\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  british_virgin_islands: {\n    keywords: [ \"british\", \"virgin\", \"islands\", \"bvi\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfb\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  brunei: {\n    keywords: [ \"bn\", \"darussalam\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  bulgaria: {\n    keywords: [ \"bg\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  burkina_faso: {\n    keywords: [ \"burkina\", \"faso\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  burundi: {\n    keywords: [ \"bi\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cape_verde: {\n    keywords: [ \"cabo\", \"verde\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddfb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cambodia: {\n    keywords: [ \"kh\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cameroon: {\n    keywords: [ \"cm\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  canada: {\n    keywords: [ \"ca\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  canary_islands: {\n    keywords: [ \"canary\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cayman_islands: {\n    keywords: [ \"cayman\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  central_african_republic: {\n    keywords: [ \"central\", \"african\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  chad: {\n    keywords: [ \"td\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  chile: {\n    keywords: [ \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cn: {\n    keywords: [ \"china\", \"chinese\", \"prc\", \"flag\", \"country\", \"nation\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  christmas_island: {\n    keywords: [ \"christmas\", \"island\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddfd\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cocos_islands: {\n    keywords: [ \"cocos\", \"keeling\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  colombia: {\n    keywords: [ \"co\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  comoros: {\n    keywords: [ \"km\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  congo_brazzaville: {\n    keywords: [ \"congo\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  congo_kinshasa: {\n    keywords: [ \"congo\", \"democratic\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cook_islands: {\n    keywords: [ \"cook\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  costa_rica: {\n    keywords: [ \"costa\", \"rica\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  croatia: {\n    keywords: [ \"hr\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udded\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cuba: {\n    keywords: [ \"cu\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  curacao: {\n    keywords: [ \"cura\\xe7ao\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cyprus: {\n    keywords: [ \"cy\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  czech_republic: {\n    keywords: [ \"cz\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  denmark: {\n    keywords: [ \"dk\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde9\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  djibouti: {\n    keywords: [ \"dj\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde9\\ud83c\\uddef\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  dominica: {\n    keywords: [ \"dm\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde9\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  dominican_republic: {\n    keywords: [ \"dominican\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde9\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  ecuador: {\n    keywords: [ \"ec\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddea\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  egypt: {\n    keywords: [ \"eg\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddea\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  el_salvador: {\n    keywords: [ \"el\", \"salvador\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddfb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  equatorial_guinea: {\n    keywords: [ \"equatorial\", \"gn\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddf6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  eritrea: {\n    keywords: [ \"er\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddea\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  estonia: {\n    keywords: [ \"ee\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddea\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  ethiopia: {\n    keywords: [ \"et\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddea\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  eu: {\n    keywords: [ \"european\", \"union\", \"flag\", \"banner\" ],\n    char: \"\\ud83c\\uddea\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  falkland_islands: {\n    keywords: [ \"falkland\", \"islands\", \"malvinas\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddeb\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  faroe_islands: {\n    keywords: [ \"faroe\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddeb\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  fiji: {\n    keywords: [ \"fj\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddeb\\ud83c\\uddef\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  finland: {\n    keywords: [ \"fi\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddeb\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  fr: {\n    keywords: [ \"banner\", \"flag\", \"nation\", \"france\", \"french\", \"country\" ],\n    char: \"\\ud83c\\uddeb\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  french_guiana: {\n    keywords: [ \"french\", \"guiana\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  french_polynesia: {\n    keywords: [ \"french\", \"polynesia\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  french_southern_territories: {\n    keywords: [ \"french\", \"southern\", \"territories\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  gabon: {\n    keywords: [ \"ga\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  gambia: {\n    keywords: [ \"gm\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  georgia: {\n    keywords: [ \"ge\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  de: {\n    keywords: [ \"german\", \"nation\", \"flag\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde9\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  ghana: {\n    keywords: [ \"gh\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  gibraltar: {\n    keywords: [ \"gi\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  greece: {\n    keywords: [ \"gr\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  greenland: {\n    keywords: [ \"gl\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  grenada: {\n    keywords: [ \"gd\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  guadeloupe: {\n    keywords: [ \"gp\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddf5\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  guam: {\n    keywords: [ \"gu\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  guatemala: {\n    keywords: [ \"gt\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  guernsey: {\n    keywords: [ \"gg\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  guinea: {\n    keywords: [ \"gn\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  guinea_bissau: {\n    keywords: [ \"gw\", \"bissau\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  guyana: {\n    keywords: [ \"gy\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  haiti: {\n    keywords: [ \"ht\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udded\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  honduras: {\n    keywords: [ \"hn\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udded\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  hong_kong: {\n    keywords: [ \"hong\", \"kong\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udded\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  hungary: {\n    keywords: [ \"hu\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udded\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  iceland: {\n    keywords: [ \"is\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  india: {\n    keywords: [ \"in\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  indonesia: {\n    keywords: [ \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  iran: {\n    keywords: [ \"iran,\", \"islamic\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  iraq: {\n    keywords: [ \"iq\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddf6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  ireland: {\n    keywords: [ \"ie\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  isle_of_man: {\n    keywords: [ \"isle\", \"man\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  israel: {\n    keywords: [ \"il\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  it: {\n    keywords: [ \"italy\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddee\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  cote_divoire: {\n    keywords: [ \"ivory\", \"coast\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  jamaica: {\n    keywords: [ \"jm\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddef\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  jp: {\n    keywords: [ \"japanese\", \"nation\", \"flag\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddef\\ud83c\\uddf5\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  jersey: {\n    keywords: [ \"je\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddef\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  jordan: {\n    keywords: [ \"jo\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddef\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  kazakhstan: {\n    keywords: [ \"kz\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  kenya: {\n    keywords: [ \"ke\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  kiribati: {\n    keywords: [ \"ki\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  kosovo: {\n    keywords: [ \"xk\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfd\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  kuwait: {\n    keywords: [ \"kw\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  kyrgyzstan: {\n    keywords: [ \"kg\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  laos: {\n    keywords: [ \"lao\", \"democratic\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  latvia: {\n    keywords: [ \"lv\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\uddfb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  lebanon: {\n    keywords: [ \"lb\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\udde7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  lesotho: {\n    keywords: [ \"ls\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  liberia: {\n    keywords: [ \"lr\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  libya: {\n    keywords: [ \"ly\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  liechtenstein: {\n    keywords: [ \"li\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  lithuania: {\n    keywords: [ \"lt\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  luxembourg: {\n    keywords: [ \"lu\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  macau: {\n    keywords: [ \"macao\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  macedonia: {\n    keywords: [ \"macedonia,\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  madagascar: {\n    keywords: [ \"mg\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  malawi: {\n    keywords: [ \"mw\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  malaysia: {\n    keywords: [ \"my\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  maldives: {\n    keywords: [ \"mv\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddfb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  mali: {\n    keywords: [ \"ml\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  malta: {\n    keywords: [ \"mt\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  marshall_islands: {\n    keywords: [ \"marshall\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  martinique: {\n    keywords: [ \"mq\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  mauritania: {\n    keywords: [ \"mr\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  mauritius: {\n    keywords: [ \"mu\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  mayotte: {\n    keywords: [ \"yt\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfe\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  mexico: {\n    keywords: [ \"mx\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddfd\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  micronesia: {\n    keywords: [ \"micronesia,\", \"federated\", \"states\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddeb\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  moldova: {\n    keywords: [ \"moldova,\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  monaco: {\n    keywords: [ \"mc\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  mongolia: {\n    keywords: [ \"mn\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  montenegro: {\n    keywords: [ \"me\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  montserrat: {\n    keywords: [ \"ms\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  morocco: {\n    keywords: [ \"ma\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  mozambique: {\n    keywords: [ \"mz\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  myanmar: {\n    keywords: [ \"mm\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  namibia: {\n    keywords: [ \"na\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  nauru: {\n    keywords: [ \"nr\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  nepal: {\n    keywords: [ \"np\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddf5\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  netherlands: {\n    keywords: [ \"nl\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  new_caledonia: {\n    keywords: [ \"new\", \"caledonia\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  new_zealand: {\n    keywords: [ \"new\", \"zealand\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  nicaragua: {\n    keywords: [ \"ni\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  niger: {\n    keywords: [ \"ne\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  nigeria: {\n    keywords: [ \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  niue: {\n    keywords: [ \"nu\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  norfolk_island: {\n    keywords: [ \"norfolk\", \"island\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  northern_mariana_islands: {\n    keywords: [ \"northern\", \"mariana\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf2\\ud83c\\uddf5\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  north_korea: {\n    keywords: [ \"north\", \"korea\", \"nation\", \"flag\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddf5\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  norway: {\n    keywords: [ \"no\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf3\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  oman: {\n    keywords: [ \"om_symbol\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf4\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  pakistan: {\n    keywords: [ \"pk\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  palau: {\n    keywords: [ \"pw\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  palestinian_territories: {\n    keywords: [ \"palestine\", \"palestinian\", \"territories\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  panama: {\n    keywords: [ \"pa\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  papua_new_guinea: {\n    keywords: [ \"papua\", \"new\", \"guinea\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  paraguay: {\n    keywords: [ \"py\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  peru: {\n    keywords: [ \"pe\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  philippines: {\n    keywords: [ \"ph\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  pitcairn_islands: {\n    keywords: [ \"pitcairn\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  poland: {\n    keywords: [ \"pl\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  portugal: {\n    keywords: [ \"pt\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  puerto_rico: {\n    keywords: [ \"puerto\", \"rico\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  qatar: {\n    keywords: [ \"qa\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf6\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  reunion: {\n    keywords: [ \"r\\xe9union\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf7\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  romania: {\n    keywords: [ \"ro\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf7\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  ru: {\n    keywords: [ \"russian\", \"federation\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf7\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  rwanda: {\n    keywords: [ \"rw\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf7\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  st_barthelemy: {\n    keywords: [ \"saint\", \"barth\\xe9lemy\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde7\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  st_helena: {\n    keywords: [ \"saint\", \"helena\", \"ascension\", \"tristan\", \"cunha\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  st_kitts_nevis: {\n    keywords: [ \"saint\", \"kitts\", \"nevis\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  st_lucia: {\n    keywords: [ \"saint\", \"lucia\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  st_pierre_miquelon: {\n    keywords: [ \"saint\", \"pierre\", \"miquelon\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf5\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  st_vincent_grenadines: {\n    keywords: [ \"saint\", \"vincent\", \"grenadines\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfb\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  samoa: {\n    keywords: [ \"ws\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfc\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  san_marino: {\n    keywords: [ \"san\", \"marino\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  sao_tome_principe: {\n    keywords: [ \"sao\", \"tome\", \"principe\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  saudi_arabia: {\n    keywords: [ \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  senegal: {\n    keywords: [ \"sn\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  serbia: {\n    keywords: [ \"rs\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf7\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  seychelles: {\n    keywords: [ \"sc\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  sierra_leone: {\n    keywords: [ \"sierra\", \"leone\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  singapore: {\n    keywords: [ \"sg\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  sint_maarten: {\n    keywords: [ \"sint\", \"maarten\", \"dutch\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddfd\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  slovakia: {\n    keywords: [ \"sk\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  slovenia: {\n    keywords: [ \"si\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  solomon_islands: {\n    keywords: [ \"solomon\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\udde7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  somalia: {\n    keywords: [ \"so\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  south_africa: {\n    keywords: [ \"south\", \"africa\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddff\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  south_georgia_south_sandwich_islands: {\n    keywords: [ \"south\", \"georgia\", \"sandwich\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  kr: {\n    keywords: [ \"south\", \"korea\", \"nation\", \"flag\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf0\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  south_sudan: {\n    keywords: [ \"south\", \"sd\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  es: {\n    keywords: [ \"spain\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddea\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  sri_lanka: {\n    keywords: [ \"sri\", \"lanka\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf1\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  sudan: {\n    keywords: [ \"sd\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\udde9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  suriname: {\n    keywords: [ \"sr\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  swaziland: {\n    keywords: [ \"sz\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  sweden: {\n    keywords: [ \"se\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  switzerland: {\n    keywords: [ \"ch\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde8\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  syria: {\n    keywords: [ \"syrian\", \"arab\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf8\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  taiwan: {\n    keywords: [ \"tw\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  tajikistan: {\n    keywords: [ \"tj\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddef\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  tanzania: {\n    keywords: [ \"tanzania,\", \"united\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  thailand: {\n    keywords: [ \"th\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  timor_leste: {\n    keywords: [ \"timor\", \"leste\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddf1\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  togo: {\n    keywords: [ \"tg\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  tokelau: {\n    keywords: [ \"tk\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddf0\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  tonga: {\n    keywords: [ \"to\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddf4\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  trinidad_tobago: {\n    keywords: [ \"trinidad\", \"tobago\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddf9\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  tunisia: {\n    keywords: [ \"tn\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  tr: {\n    keywords: [ \"turkey\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddf7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  turkmenistan: {\n    keywords: [ \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  turks_caicos_islands: {\n    keywords: [ \"turks\", \"caicos\", \"islands\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\udde8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  tuvalu: {\n    keywords: [ \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddf9\\ud83c\\uddfb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  uganda: {\n    keywords: [ \"ug\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfa\\ud83c\\uddec\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  ukraine: {\n    keywords: [ \"ua\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfa\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  united_arab_emirates: {\n    keywords: [ \"united\", \"arab\", \"emirates\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\udde6\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  uk: {\n    keywords: [ \"united\", \"kingdom\", \"great\", \"britain\", \"northern\", \"ireland\", \"flag\", \"nation\", \"country\", \"banner\", \"british\", \"UK\", \"english\", \"england\", \"union jack\" ],\n    char: \"\\ud83c\\uddec\\ud83c\\udde7\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  england: {\n    keywords: [ \"flag\", \"english\" ],\n    char: \"\\ud83c\\udff4\\udb40\\udc67\\udb40\\udc62\\udb40\\udc65\\udb40\\udc6e\\udb40\\udc67\\udb40\\udc7f\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  scotland: {\n    keywords: [ \"flag\", \"scottish\" ],\n    char: \"\\ud83c\\udff4\\udb40\\udc67\\udb40\\udc62\\udb40\\udc73\\udb40\\udc63\\udb40\\udc74\\udb40\\udc7f\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  wales: {\n    keywords: [ \"flag\", \"welsh\" ],\n    char: \"\\ud83c\\udff4\\udb40\\udc67\\udb40\\udc62\\udb40\\udc77\\udb40\\udc6c\\udb40\\udc73\\udb40\\udc7f\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  us: {\n    keywords: [ \"united\", \"states\", \"america\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfa\\ud83c\\uddf8\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  us_virgin_islands: {\n    keywords: [ \"virgin\", \"islands\", \"us\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfb\\ud83c\\uddee\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  uruguay: {\n    keywords: [ \"uy\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfa\\ud83c\\uddfe\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  uzbekistan: {\n    keywords: [ \"uz\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfa\\ud83c\\uddff\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  vanuatu: {\n    keywords: [ \"vu\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfb\\ud83c\\uddfa\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  vatican_city: {\n    keywords: [ \"vatican\", \"city\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfb\\ud83c\\udde6\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  venezuela: {\n    keywords: [ \"ve\", \"bolivarian\", \"republic\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfb\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  vietnam: {\n    keywords: [ \"viet\", \"nam\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfb\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  wallis_futuna: {\n    keywords: [ \"wallis\", \"futuna\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfc\\ud83c\\uddeb\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  western_sahara: {\n    keywords: [ \"western\", \"sahara\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddea\\ud83c\\udded\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  yemen: {\n    keywords: [ \"ye\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddfe\\ud83c\\uddea\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  zambia: {\n    keywords: [ \"zm\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddff\\ud83c\\uddf2\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  zimbabwe: {\n    keywords: [ \"zw\", \"flag\", \"nation\", \"country\", \"banner\" ],\n    char: \"\\ud83c\\uddff\\ud83c\\uddfc\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  united_nations: {\n    keywords: [ \"un\", \"flag\", \"banner\" ],\n    char: \"\\ud83c\\uddfa\\ud83c\\uddf3\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  },\n  pirate_flag: {\n    keywords: [ \"skull\", \"crossbones\", \"flag\", \"banner\" ],\n    char: \"\\ud83c\\udff4\\u200d\\u2620\\ufe0f\",\n    fitzpatrick_scale: false,\n    category: \"flags\"\n  }\n});", "export default require(\"./node_modules/tinymce/plugins/emoticons/js/emojis.js\");"], "mappings": ";;;;;AAAA;AAAA;AACA,WAAO,QAAQ,SAAS,IAAI,6BAA6B;AAAA,MACvD,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,SAAS,SAAS,OAAO,MAAM;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,SAAS,SAAS,OAAO;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,OAAO,SAAS,QAAQ,SAAS,cAAc;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,WAAW,SAAS,YAAY,OAAO;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,eAAe;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,SAAS,OAAO,QAAQ,MAAM,MAAM,SAAS;AAAA,QACjE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,SAAS,OAAO,SAAS,QAAQ,SAAS,QAAQ,MAAM;AAAA,QAC5E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,OAAO,SAAS,SAAS,SAAS,SAAS;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,OAAO,OAAO,aAAa,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC9E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,SAAS,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,SAAS,eAAe,UAAU,MAAM,SAAS;AAAA,QACrE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,SAAS,SAAS,WAAW,SAAS,eAAe,OAAO;AAAA,QAChF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,WAAW,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,SAAS,WAAW;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS,OAAO,UAAU,SAAS,QAAQ,SAAS,SAAS,OAAO,aAAa;AAAA,QAC7F,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,WAAW,QAAQ,WAAW;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,QAAQ,QAAQ,aAAa,cAAc,eAAe,SAAS;AAAA,QACvF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gCAAgC;AAAA,QAC9B,UAAU,CAAE,QAAQ,QAAQ,QAAQ,aAAa,cAAc,eAAe,SAAS,UAAU;AAAA,QACjG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,QAAQ,QAAQ,QAAQ,aAAa,cAAc,eAAe;AAAA,QAC9E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,QAAQ,QAAQ,KAAK,cAAc,eAAe;AAAA,QACtE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,QAAQ,aAAa,cAAc,eAAe;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,QAAQ,QAAQ,QAAQ,aAAa,cAAc,eAAe;AAAA,QAC9E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,8BAA8B;AAAA,QAC5B,UAAU,CAAE,QAAQ,SAAS,YAAY,WAAW,eAAe,SAAS,QAAQ;AAAA,QACpF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ,YAAY,cAAc,eAAe,aAAa;AAAA,QAC1E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,UAAU;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,8BAA8B;AAAA,QAC5B,UAAU,CAAE,QAAQ,SAAS,WAAW,eAAe,SAAS;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,SAAS,YAAY,WAAW,eAAe,SAAS;AAAA,QAC5E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,QAAQ,UAAU;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,SAAS,QAAQ;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,QAAQ,SAAS,UAAU,SAAS;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,SAAS,UAAU,QAAQ;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,SAAS,QAAQ,SAAS,QAAQ;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,gBAAgB,OAAO,MAAM;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ,eAAe,OAAO,OAAO;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,gBAAgB,SAAS,iBAAiB,WAAW,WAAW,eAAe,aAAa,WAAW;AAAA,QACnH,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,UAAU,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,QAAQ,YAAY,WAAW,WAAW,aAAa;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ,WAAW,QAAQ;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,SAAS,OAAO;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,OAAO,SAAS,aAAa;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,WAAW,WAAW;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,OAAO,QAAQ,WAAW;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,OAAO,QAAQ;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,OAAO,aAAa;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,gBAAgB,OAAO,SAAS,QAAQ;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,QAAQ,YAAY,gBAAgB,OAAO;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,QAAQ,OAAO,SAAS;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,QAAQ,MAAM,SAAS;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,YAAY,QAAQ,UAAU,QAAQ;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,SAAS,SAAS;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,SAAS,UAAU,OAAO,cAAc;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,OAAO,QAAQ,SAAS;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,YAAY,aAAa,OAAO,QAAQ;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,SAAS,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,UAAU,aAAa,WAAW,QAAQ;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,MAAM;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,SAAS,OAAO,aAAa,SAAS;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,SAAS,QAAQ;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,OAAO,OAAO,SAAS;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,YAAY,QAAQ,OAAO;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,QAAQ,YAAY,UAAU,aAAa;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,OAAO,SAAS,OAAO,SAAS;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,eAAe,OAAO;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,OAAO,aAAa;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,QAAQ,UAAU,UAAU;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ,SAAS,SAAS,SAAS,QAAQ,YAAY;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,QAAQ,cAAc,UAAU,QAAQ;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,QAAQ,OAAO;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,QAAQ,eAAe,eAAe,QAAQ;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,WAAW,UAAU,WAAW;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,SAAS,eAAe,SAAS;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,SAAS,UAAU,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,YAAY,QAAQ,QAAQ;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,WAAW,OAAO,QAAQ,aAAa,SAAS,UAAU,SAAS,SAAS,YAAY;AAAA,QACpG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,OAAO,QAAQ,QAAQ,WAAW,SAAS,UAAU,YAAY;AAAA,QAC7E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,YAAY,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,aAAa,UAAU;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,OAAO,QAAQ,SAAS;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,YAAY,WAAW;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,QAAQ,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,QAAQ,QAAQ,SAAS;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,QAAQ,QAAQ,aAAa,QAAQ,cAAc;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,QAAQ,SAAS,UAAU;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,UAAU,SAAS,QAAQ,OAAO,QAAQ,SAAS;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,WAAW,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW,UAAU,OAAO,eAAe;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,UAAU,YAAY,YAAY;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,WAAW,WAAW,UAAU,YAAY,SAAS,MAAM;AAAA,QAChF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,YAAY,OAAO,WAAW,QAAQ,SAAS,UAAU,QAAQ,QAAQ;AAAA,QACrF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,cAAc,MAAM,WAAW;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,YAAY,QAAQ,OAAO,UAAU;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,WAAW,QAAQ;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,GAAG;AAAA,QACD,UAAU,CAAE,WAAW,UAAU,QAAQ,SAAS,WAAW;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW,SAAS,WAAW,MAAM;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,WAAW,QAAQ,YAAY,QAAQ;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,WAAW,UAAU;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,aAAa,SAAS;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,OAAO,QAAQ,QAAQ,UAAU,UAAU;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,QAAQ,QAAQ,WAAW;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,aAAa;AAAA,QACzB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,WAAW,aAAa;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,QAAQ,aAAa;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,QAAQ,aAAa;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,aAAa,WAAW,QAAQ;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,WAAW,QAAQ,aAAa;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,QAAQ,WAAW,QAAQ,UAAU;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kCAAkC;AAAA,QAChC,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,WAAW,YAAY,iBAAiB;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,QAAQ,WAAW,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,4BAA4B,cAAc,SAAS;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,YAAY,UAAU,WAAW;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,QAAQ,OAAO,SAAS;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,SAAS,SAAS,QAAQ;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,QAAQ,UAAU;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,QAAQ,UAAU,SAAS,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,QAAQ,UAAU,SAAS,QAAQ,OAAO;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,OAAO,QAAQ;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,OAAO,QAAQ,OAAO;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,YAAY,UAAU,OAAO,OAAO,UAAU,OAAO;AAAA,QACjE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,UAAU,QAAQ,UAAU;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,OAAO,QAAQ,OAAO,UAAU,OAAO;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,SAAS,UAAU;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,QAAQ,OAAO,OAAO,SAAS;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,UAAU,SAAS,QAAQ,OAAO,SAAS;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,UAAU,SAAS,YAAY;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,UAAU,UAAU,YAAY,SAAS;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,UAAU,YAAY;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,UAAU,OAAO,SAAS,eAAe,UAAU,OAAO;AAAA,QAC/E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,OAAO,UAAU,OAAO,SAAS,eAAe,UAAU;AAAA,QACtE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,2BAA2B;AAAA,QACzB,UAAU,CAAE,UAAU,SAAS,OAAO,SAAS,gBAAgB,UAAU,SAAS;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,QAAQ,SAAS,OAAO,OAAO,SAAS,gBAAgB,UAAU;AAAA,QAC9E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,MAAM,MAAM,WAAW,UAAU,SAAS;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,MAAM,WAAW,QAAQ,OAAO;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,SAAS,OAAO,aAAa,UAAU;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,OAAO;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,UAAU,SAAS,aAAa,cAAc,SAAS;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,UAAU,SAAS,aAAa,cAAc,OAAO;AAAA,QACjE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW,YAAY,SAAS;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,YAAY,OAAO;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,YAAY,SAAS;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,YAAY,OAAO;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,YAAY,eAAe,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,YAAY,eAAe,OAAO;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,cAAc,aAAa,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,cAAc,aAAa,OAAO;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,YAAY,cAAc,SAAS;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,YAAY,cAAc,OAAO;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,aAAa,YAAY,cAAc,YAAY,SAAS,SAAS,UAAU;AAAA,QACpG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,SAAS,aAAa,YAAY,cAAc,YAAY,OAAO,SAAS,UAAU;AAAA,QAClG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,YAAY,WAAW,SAAS;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,YAAY,WAAW,OAAO;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,SAAS,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW,OAAO,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,aAAa,WAAW,YAAY,aAAa,SAAS;AAAA,QACtE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,aAAa,WAAW,YAAY,aAAa,OAAO;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW,SAAS;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,OAAO;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,WAAW,SAAS;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,WAAW,OAAO;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,WAAW,SAAS,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,WAAW,SAAS,OAAO;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,SAAS,UAAU,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,UAAU,OAAO;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,WAAW,SAAS,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,WAAW,SAAS,OAAO;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,SAAS,UAAU,QAAQ,WAAW;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,OAAO,QAAQ,QAAQ,QAAQ;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,UAAU,QAAQ,OAAO,YAAY,WAAW;AAAA,QACrE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,OAAO,QAAQ,QAAQ,OAAO,YAAY,QAAQ;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,UAAU,QAAQ;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,YAAY,OAAO,QAAQ,QAAQ;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,UAAU,QAAQ;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,OAAO,QAAQ,QAAQ;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,OAAO;AAAA,QACnB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,OAAO,QAAQ;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,UAAU,UAAU;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,OAAO,QAAQ,WAAW,UAAU;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,OAAO;AAAA,QACnB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU,YAAY;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,OAAO,QAAQ;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,OAAO;AAAA,QACnB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,SAAS,UAAU,SAAS,SAAS,SAAS;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,OAAO,OAAO,QAAQ,SAAS,SAAS;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,UAAU,YAAY,WAAW,SAAS;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,YAAY,WAAW;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,WAAW,YAAY,QAAQ,WAAW;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,OAAO,WAAW,YAAY,QAAQ;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,QAAQ,SAAS,SAAS;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,QAAQ,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,OAAO,OAAO;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,SAAS,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,SAAS,OAAO;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,UAAU,SAAS,QAAQ,QAAQ,UAAU,QAAQ,aAAa,cAAc;AAAA,QACpG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,QAAQ,UAAU,QAAQ,QAAQ,YAAY,cAAc,UAAU;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,QAAQ,cAAc,UAAU,QAAQ,QAAQ,UAAU,UAAU;AAAA,QAChF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,OAAO,QAAQ;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,OAAO,QAAQ,OAAO;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS,UAAU,QAAQ;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,SAAS,UAAU,QAAQ,YAAY,eAAe;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,OAAO,QAAQ,OAAO,YAAY,eAAe;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,UAAU,QAAQ,SAAS,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,OAAO,OAAO,SAAS;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,QAAQ,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,OAAO,OAAO;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,QAAQ,UAAU,QAAQ,SAAS;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,OAAO,OAAO,QAAQ,QAAQ,SAAS;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,QAAQ,SAAS,OAAO,aAAa,eAAe;AAAA,QAC1E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,OAAO,OAAO,OAAO,aAAa,eAAe;AAAA,QACrE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,QAAQ,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,OAAO,OAAO;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,UAAU,SAAS,OAAO,aAAa;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,QAAQ,OAAO,OAAO,aAAa;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,6BAA6B;AAAA,QAC3B,UAAU,CAAE,QAAQ,QAAQ,QAAQ,aAAa,SAAS,UAAU,cAAc;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,+BAA+B;AAAA,QAC7B,UAAU,CAAE,QAAQ,QAAQ,QAAQ,aAAa,SAAS,UAAU,cAAc;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,2BAA2B;AAAA,QACzB,UAAU,CAAE,QAAQ,QAAQ,QAAQ,aAAa,SAAS,UAAU,cAAc;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,QAAQ,cAAc,QAAQ,QAAQ,UAAU;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,QAAQ,cAAc,QAAQ,QAAQ,UAAU;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,QAAQ,cAAc,QAAQ,QAAQ,UAAU;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,QAAQ,WAAW,SAAS,OAAO,OAAO,UAAU,UAAU,UAAU;AAAA,QACpF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,2BAA2B;AAAA,QACzB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,0BAA0B;AAAA,QACxB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,4BAA4B;AAAA,QAC1B,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,6BAA6B;AAAA,QAC3B,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,4BAA4B;AAAA,QAC1B,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,8BAA8B;AAAA,QAC5B,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,0BAA0B;AAAA,QACxB,UAAU,CAAE,QAAQ,WAAW,UAAU,SAAS;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,QAAQ,UAAU,UAAU,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,cAAc,aAAa;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,iBAAiB;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,WAAW,SAAS,UAAU,SAAS;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU,UAAU,WAAW,SAAS;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,WAAW;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,YAAY,UAAU,SAAS,QAAQ,WAAW,SAAS;AAAA,QACvE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,WAAW,SAAS,UAAU;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,QAAQ,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,QAAQ,QAAQ,QAAQ,aAAa;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,YAAY,WAAW;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,WAAW,SAAS,UAAU,SAAS;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,WAAW;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,eAAe,WAAW;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,aAAa;AAAA,QACzB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,UAAU;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,eAAe,UAAU,QAAQ;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,aAAa,UAAU;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,OAAO;AAAA,QACnB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,gBAAgB;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,WAAW,UAAU,cAAc,cAAc,OAAO,OAAO,SAAS,SAAS;AAAA,QACvG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,OAAO,UAAU,WAAW;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,aAAa,OAAO;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,OAAO,eAAe;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,eAAe,SAAS,SAAS;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW,aAAa,eAAe;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,YAAY,aAAa,QAAQ,OAAO,SAAS,OAAO;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,eAAe,YAAY,SAAS,QAAQ;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,cAAc;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,WAAW,WAAW,YAAY,cAAc,WAAW,WAAW,WAAW,OAAO;AAAA,QACpG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,WAAW,QAAQ;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,UAAU,UAAU,QAAQ,SAAS,OAAO;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,QAAQ,UAAU,OAAO;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU,gBAAgB;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU,OAAO,UAAU,SAAS;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,OAAO,UAAU,QAAQ,UAAU;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,MAAM,UAAU,UAAU,OAAO;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,UAAU,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,YAAY,SAAS,OAAO,UAAU;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,SAAS,UAAU;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,SAAS,UAAU;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,UAAU,OAAO,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,WAAW,OAAO,QAAQ,QAAQ;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,WAAW,QAAQ;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,UAAU,QAAQ;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,UAAU,QAAQ;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,UAAU,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU,UAAU,OAAO,UAAU;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,UAAU;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,QAAQ,UAAU,QAAQ;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU,YAAY,iBAAiB;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU,YAAY,iBAAiB,gBAAgB,cAAc;AAAA,QAC3F,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,QAAQ,UAAU;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,QAAQ,SAAS,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU,QAAQ,OAAO;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU,QAAQ,OAAO,SAAS,WAAW,QAAQ;AAAA,QAC3E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU,QAAQ,OAAO,SAAS,QAAQ,QAAQ;AAAA,QACxE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU,OAAO;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU,OAAO;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,UAAU,WAAW,UAAU;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU,WAAW;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,UAAU,MAAM;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,UAAU,OAAO;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,MAAM,UAAU,UAAU,OAAO;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,UAAU,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,UAAU,OAAO,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU,OAAO,UAAU;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU,QAAQ,MAAM;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU,QAAQ;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,UAAU,UAAU,QAAQ,OAAO;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,OAAO,UAAU,OAAO,UAAU;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,QAAQ,OAAO;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU,OAAO,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU,aAAa,QAAQ,OAAO;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,UAAU,QAAQ,UAAU;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU,UAAU,SAAS;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,YAAY,cAAc,OAAO,OAAO,OAAO;AAAA,QACrE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,QAAQ,UAAU,WAAW;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,QAAQ,UAAU,WAAW;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,aAAa,SAAS;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,YAAY,YAAY,YAAY,QAAQ;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,aAAa,UAAU,UAAU,SAAS,UAAU;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,UAAU,SAAS,QAAQ;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,aAAa,SAAS,YAAY,QAAQ,SAAS;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,aAAa,SAAS,UAAU,SAAS;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,aAAa,SAAS,UAAU,SAAS;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,UAAU,aAAa,SAAS;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,UAAU,UAAU;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,SAAS,QAAQ,aAAa,SAAS,QAAQ;AAAA,QACrE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,SAAS,aAAa;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,SAAS,aAAa,MAAM;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,aAAa,WAAW;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,WAAW,cAAc,QAAQ;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,SAAS,UAAU,UAAU;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,SAAS,UAAU;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW,UAAU;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,aAAa,SAAS,WAAW,UAAU;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,OAAO;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,UAAU,YAAY;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,SAAS,OAAO;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,SAAS,QAAQ;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACnF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW,SAAS;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,UAAU,SAAS,OAAO,QAAQ,YAAY,UAAU,SAAS,WAAW;AAAA,QACxF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,8BAA8B;AAAA,QAC5B,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,6BAA6B;AAAA,QAC3B,UAAU,CAAE,UAAU,YAAY,UAAU,SAAS,SAAS,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,SAAS,OAAO,WAAW;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,WAAW,WAAW,QAAQ;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,WAAW,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,SAAS,SAAS,QAAQ,WAAW,QAAQ;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,UAAU,cAAc,UAAU,SAAS;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW,UAAU,UAAU,WAAW,QAAQ;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,+BAA+B;AAAA,QAC7B,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,WAAW,WAAW,kBAAkB;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,OAAO,QAAQ;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,WAAW,aAAa,aAAa;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,UAAU,QAAQ,WAAW,aAAa;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,UAAU,QAAQ,WAAW,aAAa,QAAQ,UAAU;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,UAAU,UAAU,QAAQ,WAAW,aAAa,QAAQ;AAAA,QACxE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW,WAAW;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,WAAW;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,QAAQ,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,OAAO,SAAS,QAAQ,UAAU,WAAW;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,OAAO;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,SAAS,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,QAAQ,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU,QAAQ;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,aAAa,UAAU;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,aAAa,UAAU,QAAQ;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,aAAa,QAAQ;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,SAAS,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,SAAS,aAAa;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,aAAa;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,aAAa,SAAS,YAAY,WAAW,QAAQ;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,SAAS,aAAa;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,SAAS,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,aAAa,QAAQ,OAAO;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,OAAO,QAAQ,OAAO,QAAQ,YAAY;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,aAAa,aAAa;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,QAAQ,aAAa,QAAQ,WAAW;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,UAAU,aAAa;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,aAAa,WAAW;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,aAAa,QAAQ,gBAAgB,aAAa;AAAA,QACtE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,QAAQ,aAAa,WAAW;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,QAAQ,WAAW,aAAa;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,YAAY,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,SAAS,OAAO,SAAS,cAAc,QAAQ,SAAS,YAAY,UAAU;AAAA,QAClG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,QAAQ,YAAY;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,YAAY;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,SAAS,OAAO;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,WAAW,SAAS,YAAY,YAAY;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,OAAO,WAAW;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,OAAO,WAAW;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,WAAW,UAAU;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,WAAW,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,SAAS,SAAS;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,QAAQ,SAAS,WAAW;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,iBAAiB,SAAS;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,YAAY,WAAW;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,WAAW,SAAS,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,SAAS,QAAQ,aAAa,SAAS;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,YAAY,SAAS;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,YAAY,SAAS,SAAS,SAAS,OAAO,UAAU,WAAW;AAAA,QACxF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,YAAY,SAAS,SAAS,SAAS,OAAO,UAAU,WAAW;AAAA,QACxF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,YAAY,SAAS,SAAS,WAAW,aAAa,UAAU,QAAQ,aAAa;AAAA,QACjG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,YAAY,SAAS,WAAW;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,YAAY,SAAS,WAAW,UAAU,SAAS,WAAW,UAAU,UAAU,SAAS;AAAA,QAChH,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,SAAS,WAAW,YAAY,SAAS;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,YAAY,YAAY,UAAU,SAAS,WAAW,SAAS;AAAA,QAC3E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,QAAQ,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,SAAS,SAAS,YAAY,YAAY,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS,QAAQ,aAAa,SAAS;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,YAAY,YAAY,SAAS;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,aAAa;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,aAAa;AAAA,QACzB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,WAAW;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,QAAQ,OAAO,QAAQ,SAAS,UAAU;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,aAAa,UAAU,WAAW;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,SAAS,QAAQ,QAAQ;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,YAAY,QAAQ,QAAQ;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,YAAY,SAAS;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,UAAU,QAAQ;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,QAAQ,UAAU;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,SAAS,SAAS,QAAQ,SAAS;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,SAAS,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,SAAS,SAAS,UAAU;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,SAAS,OAAO,QAAQ;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,YAAY,SAAS,WAAW,SAAS,UAAU,SAAS;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,YAAY,SAAS,WAAW,SAAS;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,0BAA0B;AAAA,QACxB,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,SAAS,UAAU,cAAc,QAAQ,YAAY,OAAO;AAAA,QACxE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,OAAO,QAAQ,cAAc,QAAQ,YAAY,OAAO;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,SAAS,OAAO,UAAU,SAAS,SAAS;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,SAAS,OAAO,UAAU;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,UAAU,SAAS,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,UAAU,YAAY,YAAY,SAAS;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,UAAU,YAAY;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,QAAQ,YAAY,WAAW,SAAS;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,QAAQ,YAAY;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,kBAAkB,UAAU,SAAS,QAAQ,QAAQ,SAAS;AAAA,QAC1E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,kBAAkB,UAAU,SAAS,QAAQ;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,WAAW,eAAe,YAAY;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,0BAA0B;AAAA,QACxB,UAAU,CAAE,QAAQ,YAAY,YAAY,SAAS;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,OAAO,SAAS,WAAW,SAAS,OAAO;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,WAAW;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS,WAAW;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,UAAU,SAAS,WAAW;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,cAAc;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,WAAW;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,SAAS,QAAQ;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,YAAY,YAAY;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,WAAW,SAAS;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,WAAW,SAAS;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,SAAS,MAAM,QAAQ;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,SAAS,cAAc;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,cAAc,cAAc;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,cAAc,QAAQ;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,cAAc,aAAa;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,WAAW,OAAO;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,QAAQ,UAAU;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,QAAQ,OAAO,UAAU;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,UAAU,YAAY,QAAQ;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,OAAO,UAAU,SAAS,iBAAiB,QAAQ;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,gBAAgB,UAAU;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,OAAO;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,OAAO,kBAAkB;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,WAAW,QAAQ;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,OAAO,WAAW;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,kBAAkB;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,QAAQ,QAAQ,WAAW;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,QAAQ,kBAAkB,OAAO,SAAS;AAAA,QACjE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,OAAO;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,kBAAkB,QAAQ;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW,OAAO;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,WAAW,QAAQ,kBAAkB;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW,OAAO,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW,QAAQ;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,UAAU;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,WAAW,YAAY;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,WAAW,SAAS;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,aAAa,OAAO,aAAa,SAAS,SAAS,UAAU,OAAO;AAAA,QAC1F,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,WAAW,OAAO,SAAS,eAAe;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,OAAO,WAAW;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,WAAW,QAAQ;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,kBAAkB,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,kBAAkB,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,kBAAkB,WAAW,YAAY,UAAU;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,kBAAkB,WAAW,SAAS,QAAQ,UAAU;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,kBAAkB,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,kBAAkB,eAAe,OAAO,eAAe;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,kBAAkB,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,kBAAkB,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,kBAAkB,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,kBAAkB,OAAO;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,WAAW,kBAAkB,UAAU;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,WAAW,UAAU;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,UAAU;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,UAAU,kBAAkB,SAAS;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,kBAAkB,WAAW;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,UAAU,QAAQ,aAAa,QAAQ,eAAe,eAAe;AAAA,QACjF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,iBAAiB,OAAO,SAAS,eAAe,QAAQ;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,OAAO,YAAY,aAAa,OAAO,UAAU;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,UAAU,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,SAAS,OAAO;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,OAAO,YAAY,WAAW;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,kBAAkB;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,cAAc,QAAQ;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,kBAAkB,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,YAAY;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,YAAY,cAAc,SAAS;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,OAAO,WAAW;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,YAAY,YAAY,aAAa;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,UAAU,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,SAAS,QAAQ;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,UAAU,eAAe,UAAU;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,YAAY,UAAU;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,WAAW,YAAY;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,YAAY;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,WAAW;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,eAAe;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,aAAa,cAAc;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW,QAAQ,YAAY;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,QAAQ,YAAY;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,UAAU,SAAS,QAAQ;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,YAAY;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,gBAAgB;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,WAAW,OAAO;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,cAAc;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,WAAW,QAAQ;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,YAAY,YAAY;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,SAAS,gBAAgB,SAAS,OAAO;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,aAAa;AAAA,QACzB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,YAAY,WAAW;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,SAAS,UAAU,WAAW;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,SAAS,UAAU;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,YAAY,UAAU;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,YAAY,YAAY;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,YAAY,YAAY;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,YAAY,UAAU,WAAW;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,YAAY,SAAS,SAAS,QAAQ,YAAY;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,YAAY,gBAAgB;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,YAAY,YAAY;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,YAAY,WAAW,aAAa,SAAS;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,aAAa;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,QAAQ,aAAa,UAAU,YAAY,SAAS;AAAA,QACxE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,OAAO,WAAW;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,YAAY,YAAY;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,WAAW;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,WAAW,WAAW,UAAU;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,SAAS;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,cAAc,SAAS,WAAW;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,cAAc,UAAU,UAAU,WAAW;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,cAAc,YAAY,QAAQ,SAAS;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,cAAc,aAAa;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,cAAc,UAAU,QAAQ,QAAQ;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,aAAa,cAAc,QAAQ,OAAO;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,cAAc,OAAO,QAAQ,QAAQ;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,MAAM,QAAQ;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,SAAS,aAAa,OAAO;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,QAAQ,UAAU;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,cAAc,iBAAiB;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,cAAc,iBAAiB,QAAQ;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,aAAa;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,iBAAiB;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,cAAc,WAAW,aAAa,QAAQ;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,iBAAiB,SAAS,WAAW;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,QAAQ,aAAa,UAAU;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,YAAY,cAAc;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,aAAa,QAAQ;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,SAAS,aAAa,SAAS,QAAQ,QAAQ;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,iBAAiB,UAAU,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,eAAe;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,WAAW,SAAS;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,OAAO,SAAS,WAAW,WAAW;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,UAAU,SAAS,WAAW;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,SAAS,QAAQ;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS,SAAS,YAAY,UAAU;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,SAAS,UAAU;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,YAAY,SAAS,SAAS,SAAS,MAAM,WAAW;AAAA,QAC/E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,UAAU,WAAW,SAAS;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,SAAS,UAAU,QAAQ,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,QAAQ,WAAW;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,OAAO,YAAY;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,OAAO,OAAO,cAAc;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,OAAO,QAAQ,OAAO;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,YAAY,UAAU,UAAU;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ,WAAW,aAAa;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,YAAY,QAAQ,WAAW,aAAa;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,SAAS,WAAW,WAAW;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,WAAW,aAAa,SAAS;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,UAAU,UAAU,UAAU,SAAS,SAAS,UAAU;AAAA,QACtE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,WAAW,QAAQ,OAAO,SAAS,OAAO,aAAa,YAAY,UAAU,WAAW;AAAA,QACpG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,OAAO,SAAS,OAAO;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,SAAS,SAAS,UAAU;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,cAAc,WAAW,cAAc;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,SAAS,QAAQ,WAAW;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,cAAc,cAAc,UAAU,WAAW;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,YAAY,UAAU,YAAY;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,YAAY,SAAS,SAAS,YAAY,UAAU,UAAU;AAAA,QACpF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,aAAa,YAAY;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU,YAAY;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,YAAY,WAAW,WAAW;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,aAAa,cAAc,OAAO;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,WAAW,eAAe,OAAO;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,YAAY,YAAY;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,YAAY,SAAS;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,YAAY,MAAM,YAAY,YAAY;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,OAAO,WAAW,YAAY;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,aAAa,YAAY;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,OAAO;AAAA,QACnB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,iBAAiB;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,eAAe,YAAY;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,YAAY,aAAa,QAAQ;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,cAAc,QAAQ,QAAQ;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,WAAW,YAAY,aAAa;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,YAAY,SAAS,YAAY;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,mBAAmB,YAAY,SAAS,UAAU;AAAA,QACvE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,YAAY,OAAO;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,QAAQ,UAAU;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,YAAY,UAAU,WAAW;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,SAAS,SAAS,aAAa;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,UAAU,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,iBAAiB;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,QAAQ,aAAa,YAAY;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,iBAAiB;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,QAAQ,aAAa,OAAO;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,aAAa,WAAW,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,aAAa,UAAU;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,YAAY,QAAQ,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,gBAAgB;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,0BAA0B;AAAA,QACxB,UAAU,CAAE,SAAS,gBAAgB,SAAS,YAAY,YAAY,aAAa,SAAS,SAAS,QAAQ;AAAA,QAC7G,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,4BAA4B;AAAA,QAC1B,UAAU,CAAE,SAAS,gBAAgB,SAAS,aAAa,YAAY,aAAa,SAAS,SAAS,OAAO;AAAA,QAC7G,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,aAAa,UAAU,SAAS;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,YAAY,QAAQ;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,YAAY;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,aAAa,YAAY;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,aAAa;AAAA,QACzB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,cAAc,YAAY;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,cAAc,UAAU,SAAS,SAAS;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,WAAW,aAAa,YAAY;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,WAAW,aAAa;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,WAAW,aAAa,SAAS;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,WAAW,aAAa,YAAY;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gCAAgC;AAAA,QAC9B,UAAU,CAAE,aAAa,SAAS,UAAU,SAAS;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,cAAc,WAAW;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,QAAQ,WAAW,aAAa,cAAc,SAAS;AAAA,QAC3E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,aAAa;AAAA,QACzB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,aAAa;AAAA,QACzB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,cAAc,QAAQ,aAAa;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,cAAc,aAAa,UAAU,QAAQ,UAAU,WAAW,aAAa;AAAA,QAC3F,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,cAAc,QAAQ;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,cAAc,YAAY,OAAO;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,QAAQ,aAAa;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,SAAS,QAAQ,aAAa,WAAW;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,WAAW,SAAS,OAAO,QAAQ,QAAQ,SAAS,cAAc,WAAW,YAAY;AAAA,QAC7G,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,YAAY,YAAY;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,cAAc,WAAW;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,cAAc,WAAW;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,OAAO,cAAc,WAAW;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,aAAa,cAAc,UAAU,SAAS,WAAW,SAAS,QAAQ,QAAQ,QAAQ,SAAS;AAAA,QACxH,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,cAAc,SAAS,SAAS,WAAW,UAAU;AAAA,QACjE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,cAAc;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,UAAU,QAAQ,QAAQ;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,UAAU,QAAQ,QAAQ;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,QAAQ,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,QAAQ,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,QAAQ,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,QAAQ,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,QAAQ,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,OAAO,SAAS,SAAS,SAAS;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,QAAQ,aAAa,cAAc;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,QAAQ,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,QAAQ,aAAa,cAAc,QAAQ;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,QAAQ,aAAa,cAAc;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,QAAQ,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,QAAQ,SAAS,aAAa;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,iBAAiB,QAAQ;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,YAAY,YAAY,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,YAAY,YAAY,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,iBAAiB,YAAY,UAAU;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,YAAY,WAAW;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,YAAY,UAAU,UAAU;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,iBAAiB,iBAAiB;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,iBAAiB,UAAU;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,iBAAiB,QAAQ,UAAU;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,UAAU,iBAAiB;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,UAAU,iBAAiB;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,iBAAiB,UAAU;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,UAAU,iBAAiB;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,iBAAiB,UAAU;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,UAAU,iBAAiB,aAAa;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,UAAU,iBAAiB;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,UAAU,iBAAiB;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,iBAAiB,UAAU;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,iBAAiB,QAAQ,UAAU;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,iBAAiB;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,WAAW,WAAW;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,YAAY,WAAW,SAAS,OAAO;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,OAAO,UAAU,WAAW,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,iBAAiB,WAAW;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,iBAAiB;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,iBAAiB,WAAW,QAAQ;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,WAAW,SAAS,YAAY;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,YAAY,SAAS;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,YAAY,iBAAiB;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,SAAS,QAAQ,YAAY,iBAAiB;AAAA,QACrE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,0BAA0B;AAAA,QACxB,UAAU,CAAE,iBAAiB,SAAS;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,WAAW,SAAS,UAAU,OAAO;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,WAAW,WAAW,QAAQ,SAAS;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,WAAW,SAAS,YAAY;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,YAAY,WAAW,QAAQ,SAAS;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,WAAW,YAAY,cAAc;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,YAAY,WAAW,aAAa,SAAS,cAAc;AAAA,QAChF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,GAAG;AAAA,QACD,UAAU,CAAE,cAAc,YAAY;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,GAAG;AAAA,QACD,UAAU,CAAE,cAAc,YAAY;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,YAAY,SAAS;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,YAAY,cAAc;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,QAAQ,cAAc,SAAS,aAAa;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,YAAY,WAAW,OAAO,UAAU,QAAQ;AAAA,QACrE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,QAAQ,SAAS,UAAU,YAAY;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,GAAG;AAAA,QACD,UAAU,CAAE,MAAM,UAAU,UAAU,UAAU;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,GAAG;AAAA,QACD,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,YAAY,WAAW;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,OAAO,WAAW;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,WAAW,cAAc;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,SAAS,UAAU,OAAO;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,SAAS,OAAO,SAAS,SAAS;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,0BAA0B,UAAU,YAAY,eAAe,OAAO;AAAA,QAClF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,YAAY,eAAe,QAAQ,OAAO;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,QAAQ,OAAO;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,OAAO,eAAe;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS,WAAW,WAAW,WAAW,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,QACtF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,OAAO,aAAa,QAAQ;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,OAAO;AAAA,QACnB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,cAAc;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,SAAS,gBAAgB,SAAS,YAAY,aAAa;AAAA,QACvE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,eAAe,OAAO,SAAS,SAAS,WAAW;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,UAAU,WAAW,UAAU,QAAQ,WAAW;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,eAAe,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,WAAW,SAAS,gBAAgB;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,gBAAgB,SAAS,gBAAgB;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,gBAAgB,WAAW,QAAQ;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,QAAQ,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,6BAA6B;AAAA,QAC3B,UAAU,CAAE,KAAK,gBAAgB,MAAM;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,gBAAgB,MAAM,SAAS,QAAQ,YAAY,UAAU;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iCAAiC;AAAA,QAC/B,UAAU,CAAE,SAAS,QAAQ,OAAO,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,WAAW,SAAS,QAAQ,SAAS,UAAU,UAAU,aAAa,QAAQ,WAAW,aAAa;AAAA,QAClH,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,SAAS,iBAAiB,SAAS,YAAY,YAAY;AAAA,QACvE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,GAAG;AAAA,QACD,UAAU,CAAE,YAAY,eAAe;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS,SAAS,QAAQ,eAAe,WAAW;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,YAAY,eAAe;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,YAAY,UAAU;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,eAAe,WAAW;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,eAAe,YAAY,QAAQ;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,aAAa,eAAe,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,UAAU,YAAY;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,eAAe,YAAY;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,eAAe,UAAU,YAAY,YAAY;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,YAAY,MAAM,eAAe,UAAU;AAAA,QACjE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,iBAAiB,SAAS,UAAU,UAAU,OAAO,YAAY;AAAA,QAC7E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,iBAAiB;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,eAAe,UAAU,WAAW,MAAM;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,eAAe,QAAQ,SAAS;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,eAAe,UAAU,QAAQ,SAAS,WAAW,SAAS;AAAA,QAC1E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,eAAe,aAAa,SAAS,YAAY,cAAc,QAAQ,aAAa;AAAA,QAChG,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,eAAe,QAAQ,YAAY,YAAY;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,eAAe,SAAS,SAAS;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,QAAQ,SAAS,OAAO;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,eAAe,SAAS;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,eAAe,SAAS;AAAA,QACpC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,KAAK,WAAW,eAAe;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,eAAe,WAAW;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,WAAW,KAAK,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,KAAK,WAAW,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,KAAK,WAAW;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,KAAK,WAAW,eAAe;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,KAAK,WAAW;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,KAAK,WAAW,eAAe;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,KAAK,eAAe;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,eAAe,WAAW;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,WAAW,MAAM;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,eAAe,SAAS,aAAa;AAAA,QACjD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,WAAW,QAAQ;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,eAAe,QAAQ;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,eAAe,QAAQ,SAAS;AAAA,QAC5C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,2BAA2B;AAAA,QACzB,UAAU,CAAE,eAAe,WAAW,SAAS;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,eAAe,QAAQ;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,eAAe,YAAY,aAAa,SAAS,WAAW;AAAA,QACxE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,eAAe,aAAa;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,eAAe,aAAa;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,eAAe,aAAa;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,eAAe,YAAY;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,eAAe,YAAY,OAAO;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,eAAe,aAAa;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,eAAe,SAAS,aAAa,YAAY;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,eAAe,aAAa,YAAY;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,eAAe,aAAa,YAAY;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,eAAe,SAAS,aAAa,YAAY;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,eAAe,aAAa,OAAO;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,SAAS,aAAa,cAAc;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,eAAe,QAAQ;AAAA,QACnC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,eAAe,UAAU,UAAU;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,2BAA2B;AAAA,QACzB,UAAU,CAAE,QAAQ,UAAU,eAAe,QAAQ;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,eAAe,aAAa;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,eAAe,aAAa;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,UAAU,eAAe;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,eAAe,YAAY;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,YAAY,SAAS;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,eAAe,SAAS,QAAQ,aAAa,WAAW,UAAU;AAAA,QAC9E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,QAAQ,aAAa,YAAY,YAAY;AAAA,QACjE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,YAAY,QAAQ,SAAS;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,MAAM,QAAQ,UAAU,OAAO;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,SAAS,SAAS;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,QAAQ,eAAe,YAAY,QAAQ;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,QAAQ,eAAe,YAAY;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,UAAU,QAAQ;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE;AAAA,QACZ,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS,SAAS,WAAW,YAAY;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS,SAAS,UAAU;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,WAAW,UAAU,OAAO;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,aAAa,SAAS,OAAO;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,KAAK;AAAA,QACH,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,MAAM,SAAS,WAAW,gBAAgB,QAAQ,YAAY,OAAO;AAAA,QACjF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,OAAO,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,aAAa,MAAM;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,QAAQ;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,QAAQ,SAAS;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,SAAS,aAAa;AAAA,QAClC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,SAAS,UAAU;AAAA,QAC/B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,2BAA2B;AAAA,QACzB,UAAU,CAAE,QAAQ,SAAS;AAAA,QAC7B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,2BAA2B;AAAA,QACzB,UAAU,CAAE,SAAS,SAAS,QAAQ;AAAA,QACtC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,SAAS,SAAS;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,qBAAqB;AAAA,QACnB,UAAU,CAAE,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU,WAAW;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,UAAU,SAAS,SAAS,WAAW;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,UAAU,WAAW;AAAA,QAC1C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,WAAW;AAAA,QAChC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU;AAAA,QACtB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,gBAAgB,aAAa,QAAQ;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,SAAS,UAAU,QAAQ,SAAS;AAAA,QAChD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,SAAS,QAAQ,QAAQ;AAAA,QAC9C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,QAAQ,WAAW;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,SAAS,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,SAAS,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,SAAS,SAAS,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,SAAS,SAAS;AAAA,QACvC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,QAAQ,UAAU;AAAA,QAC9B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,UAAU,SAAS,UAAU,YAAY;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,WAAW,UAAU,YAAY;AAAA,QAC7C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,SAAS,WAAW,QAAQ;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,WAAW,QAAQ;AAAA,QACxC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS,YAAY,QAAQ;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,QAAQ,YAAY,UAAU,QAAQ,SAAS;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,QAAQ,SAAS;AAAA,QACrC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,YAAY,WAAW,QAAQ,UAAU,WAAW;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,YAAY,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,WAAW,WAAW,QAAQ,UAAU,WAAW;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,WAAW,QAAQ,UAAU,WAAW;AAAA,QACpD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,UAAU,eAAe,QAAQ,UAAU,WAAW;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gCAAgC;AAAA,QAC9B,UAAU,CAAE,WAAW,UAAU,SAAS,aAAa,QAAQ,UAAU,WAAW;AAAA,QACpF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,wBAAwB;AAAA,QACtB,UAAU,CAAE,WAAW,UAAU,WAAW,OAAO,QAAQ,UAAU,WAAW;AAAA,QAChF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,cAAc,QAAQ,UAAU,WAAW;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW,QAAQ,QAAQ,UAAU,WAAW;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,QAAQ,SAAS,QAAQ,UAAU,WAAW;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,WAAW,QAAQ,UAAU,WAAW;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,UAAU,WAAW,QAAQ,UAAU,WAAW;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,0BAA0B;AAAA,QACxB,UAAU,CAAE,WAAW,WAAW,YAAY,QAAQ,UAAU,WAAW;AAAA,QAC3E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ,UAAU,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,SAAS,WAAW,OAAO,QAAQ,WAAW,UAAU;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,aAAa,UAAU,QAAQ,UAAU,WAAW;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,WAAW,WAAW,QAAQ,UAAU,WAAW;AAAA,QACxE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,SAAS,QAAQ,UAAU,WAAW;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,cAAc,YAAY,QAAQ,UAAU,WAAW;AAAA,QAC5E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,WAAW,QAAQ,UAAU,WAAW;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,SAAS,QAAQ,QAAQ,UAAU,WAAW;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,cAAc,QAAQ,UAAU,WAAW;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,aAAa,YAAY,QAAQ,UAAU,WAAW;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,MAAM,YAAY,QAAQ,UAAU,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,cAAc,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,YAAY,SAAS,QAAQ;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,YAAY,WAAW,YAAY,QAAQ,UAAU,WAAW;AAAA,QAC5E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,WAAW,QAAQ,UAAU,WAAW;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,UAAU,QAAQ,UAAU,UAAU,UAAU;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,UAAU,QAAQ,UAAU,WAAW;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,UAAU,aAAa,QAAQ,UAAU,WAAW;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,6BAA6B;AAAA,QAC3B,UAAU,CAAE,UAAU,YAAY,eAAe,QAAQ,UAAU,WAAW;AAAA,QAC9E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,UAAU,UAAU,QAAQ,WAAW;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,MAAM,UAAU,QAAQ,UAAU,WAAW;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,QAAQ,QAAQ,UAAU,WAAW;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,QAAQ,UAAU,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,SAAS,WAAW,YAAY,QAAQ,UAAU,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,QAAQ,OAAO,QAAQ,UAAU,WAAW;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,SAAS,QAAQ,UAAU,WAAW;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,SAAS,QAAQ,UAAU,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,YAAY,UAAU,QAAQ,WAAW;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,OAAO,cAAc,YAAY,QAAQ,UAAU,WAAW;AAAA,QAC1E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,SAAS,QAAQ,UAAU,WAAW;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,cAAc,QAAQ,UAAU,WAAW;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,YAAY,WAAW,QAAQ,UAAU,WAAW;AAAA,QAChE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,eAAe,aAAa,UAAU,QAAQ,UAAU,WAAW;AAAA,QAC/E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,YAAY,YAAY,QAAQ,UAAU,WAAW;AAAA,QACjE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,OAAO,aAAa,QAAQ,UAAU,WAAW;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,OAAO,WAAW,QAAQ,UAAU,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,UAAU,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,UAAU,QAAQ,UAAU,WAAW;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,0BAA0B;AAAA,QACxB,UAAU,CAAE,YAAY,WAAW,WAAW,QAAQ,UAAU,WAAW;AAAA,QAC3E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,SAAS,UAAU,QAAQ,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,aAAa,QAAQ,UAAU,WAAW;AAAA,QACtD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,yBAAyB;AAAA,QACvB,UAAU,CAAE,aAAa,eAAe,eAAe,QAAQ,UAAU,WAAW;AAAA,QACpF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,SAAS,OAAO,UAAU,QAAQ,UAAU,WAAW;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,UAAU,CAAE,YAAY,QAAQ,UAAU,WAAW;AAAA,QACrD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,UAAU,QAAQ,QAAQ,UAAU,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,cAAc,QAAQ,UAAU,WAAW;AAAA,QACvD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,WAAW,cAAc,QAAQ,UAAU,WAAW;AAAA,QAClE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,SAAS,iBAAiB,QAAQ,UAAU,WAAW;AAAA,QACnE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,SAAS,UAAU,aAAa,WAAW,SAAS,QAAQ,UAAU,WAAW;AAAA,QAC7F,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,SAAS,SAAS,SAAS,QAAQ,UAAU,WAAW;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,SAAS,SAAS,QAAQ,UAAU,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,oBAAoB;AAAA,QAClB,UAAU,CAAE,SAAS,UAAU,YAAY,QAAQ,UAAU,WAAW;AAAA,QACxE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,uBAAuB;AAAA,QACrB,UAAU,CAAE,SAAS,WAAW,cAAc,QAAQ,UAAU,WAAW;AAAA,QAC3E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,OAAO,UAAU,QAAQ,UAAU,WAAW;AAAA,QAC1D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,OAAO,QAAQ,YAAY,QAAQ,UAAU,WAAW;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,UAAU,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,UAAU,SAAS,QAAQ,UAAU,WAAW;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,WAAW,SAAS,QAAQ,UAAU,WAAW;AAAA,QACrE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,WAAW,WAAW,QAAQ,UAAU,WAAW;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,SAAS,UAAU,QAAQ,UAAU,WAAW;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sCAAsC;AAAA,QACpC,UAAU,CAAE,SAAS,WAAW,YAAY,WAAW,QAAQ,UAAU,WAAW;AAAA,QACpF,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,SAAS,SAAS,UAAU,QAAQ,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,MAAM,QAAQ,UAAU,WAAW;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,SAAS,QAAQ,UAAU,WAAW;AAAA,QAClD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,OAAO,SAAS,QAAQ,UAAU,WAAW;AAAA,QACzD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,UAAU,QAAQ,YAAY,QAAQ,UAAU,WAAW;AAAA,QACvE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,aAAa,UAAU,YAAY,QAAQ,UAAU,WAAW;AAAA,QAC5E,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,SAAS,QAAQ,UAAU,WAAW;AAAA,QAC3D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,UAAU,CAAE,YAAY,UAAU,QAAQ,UAAU,WAAW;AAAA,QAC/D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,UAAU,QAAQ,UAAU,WAAW;AAAA,QACnD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,QAAQ,UAAU,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,SAAS,UAAU,WAAW,QAAQ,UAAU,WAAW;AAAA,QACvE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,QAAQ,UAAU,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,sBAAsB;AAAA,QACpB,UAAU,CAAE,UAAU,QAAQ,YAAY,QAAQ,UAAU,WAAW;AAAA,QACvE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,UAAU,WAAW,SAAS,WAAW,YAAY,WAAW,QAAQ,UAAU,WAAW,UAAU,WAAW,MAAM,WAAW,WAAW;AAAA,QAC1J,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,QAAQ;AAAA,QACpB,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,IAAI;AAAA,QACF,UAAU,CAAE,UAAU,UAAU,WAAW,QAAQ,UAAU,WAAW;AAAA,QACxE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,mBAAmB;AAAA,QACjB,UAAU,CAAE,UAAU,WAAW,MAAM,QAAQ,UAAU,WAAW;AAAA,QACpE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,YAAY;AAAA,QACV,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,cAAc;AAAA,QACZ,UAAU,CAAE,WAAW,QAAQ,QAAQ,UAAU,WAAW;AAAA,QAC5D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,UAAU,CAAE,MAAM,cAAc,YAAY,QAAQ,UAAU,WAAW;AAAA,QACzE,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,SAAS;AAAA,QACP,UAAU,CAAE,QAAQ,OAAO,QAAQ,UAAU,WAAW;AAAA,QACxD,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,eAAe;AAAA,QACb,UAAU,CAAE,UAAU,UAAU,QAAQ,UAAU,WAAW;AAAA,QAC7D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,WAAW,UAAU,QAAQ,UAAU,WAAW;AAAA,QAC9D,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,OAAO;AAAA,QACL,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,QAAQ;AAAA,QACN,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,UAAU;AAAA,QACR,UAAU,CAAE,MAAM,QAAQ,UAAU,WAAW;AAAA,QAC/C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,gBAAgB;AAAA,QACd,UAAU,CAAE,MAAM,QAAQ;AAAA,QAC1B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA,MAEZ,aAAa;AAAA,QACX,UAAU,CAAE,SAAS,cAAc,QAAQ;AAAA,QAC3C,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,UAAU;AAAA;AAAA;AAAA;AAAA;;;AC5sSd,IAAO,8CAAQ;", "names": []}