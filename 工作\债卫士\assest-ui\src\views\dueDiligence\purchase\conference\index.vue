<template>
  <div class="app-container">
    <el-form inline label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef">
      <el-form-item prop="caseId" label="项目ID">
        <el-input style="width: 320px" v-model="queryParams.caseId" placeholder="请输入项目ID" />
      </el-form-item>
      <el-form-item prop="caseId" label="项目名称">
        <el-input style="width: 320px" v-model="queryParams.caseId" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="产品类型" prop="name">
        <el-select v-model="queryParams.name" filterable :reserve-keyword="false" placeholder="请输入产品方名称"
          @focus="searchName()" style="width: 320px">
          <el-option v-for="item in options" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="entrustingPartyId" label="资产转让方">
        <el-select v-model="queryParams.entrustingPartyId" collapse-tags collapse-tags-tooltip multiple
          placeholder="请输入或选择资产转让方" clearable filterable :reserve-keyword="false" style="width: 320px">
          <el-option v-for="item in ownerOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="lastContactTime" label="决策时间">
        <el-date-picker v-model="queryParams.lastContactTime" value-format="YYYY-MM-DD" type="daterange" clearable
          unlink-panels range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" style="width: 320px" />
      </el-form-item>
      <el-form-item prop="entrustingPartyId" label="决策文号">
        <el-select v-model="queryParams.entrustingPartyId" collapse-tags collapse-tags-tooltip multiple
          placeholder="请输入或选择决策文号" clearable filterable :reserve-keyword="false" style="width: 320px">
          <el-option v-for="item in ownerOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="entrustingPartyId" label="创建人">
        <el-select v-model="queryParams.entrustingPartyId" collapse-tags collapse-tags-tooltip multiple
          placeholder="请输入或选择创建人" clearable filterable :reserve-keyword="false" style="width: 320px">
          <el-option v-for="item in ownerOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="lastContactTime" label="创建时间">
        <el-date-picker v-model="queryParams.lastContactTime" value-format="YYYY-MM-DD" type="daterange" clearable
          unlink-panels range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" style="width: 320px" />
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <div class="operation-revealing-area mb20">
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-tabs v-model="activeName" @tab-change="antiShake(resetQuery)">
      <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.info" :name="index" />
    </el-tabs>
    <div class="table-box">
      <el-table :data="dataList" :loading="loading">
        <el-table-column label="项目ID" v-if="columns[0].visible" align="center" prop="projectId" />
        <el-table-column label="项目名称" v-if="columns[1].visible" align="center" prop="projectName" />
        <el-table-column label="会议决策" v-if="columns[2].visible" align="center" prop="meetingDecision" />
        <el-table-column label="资产转让方" v-if="columns[3].visible" align="center" prop="assetTransferor" />
        <el-table-column label="产品类型" v-if="columns[4].visible" align="center" prop="productType" />
        <el-table-column label="会议标题" v-if="columns[5].visible" align="center" prop="meetingTitle" />
        <el-table-column label="决策时间" v-if="columns[6].visible" align="center" prop="decisionTime" />
        <el-table-column label="决策文号" v-if="columns[7].visible" align="center" prop="decisionNo" />
        <el-table-column label="附件" v-if="columns[8].visible" align="center">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="决策结果" v-if="columns[9].visible" align="center" prop="decisionResult" />
        <el-table-column label="创建人" v-if="columns[10].visible" align="center" prop="createBy" />
        <el-table-column label="创建时间" v-if="columns[11].visible" align="center" prop="createTime" />
        <el-table-column fixed="right" width="180" label="操作">
          <template #default="{ row }">
            <div>
              <el-button type="text" v-if="row.meetingDecision == '待添加'" @click="check(row)">添加会议决策</el-button>
              <el-button type="text" @click="handleDetails(row)">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
  </div>
</template>

<script setup name="StartNapeList">
import { getDictProductType } from "@/api/assets/assetside";
import { getOwners } from "@/api/assets/casemanage";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const queryParams = ref({
  pageNum: 1, pageSize: 10
});

const options = ref([]);

const ownerOption = ref([]);

const tabList = ref([
  { code: '', info: "待添加" },
  { code: '', info: "决策未通过" },
  { code: '', info: "决策通过" },
  { code: '', info: "全部" },
])


const total = ref(1);
const dataList = ref([
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目',
    meetingDecision: '待添加',
    assetTransferor: '东莞银行',
    productType: '信用贷',
    meetingTitle: '',
    decisionTime: '',
    decisionNo: '',
    decisionResult: '',
    createBy: '',
    createTime: '',
  },
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目',
    meetingDecision: '未通过',
    assetTransferor: '东莞银行',
    productType: '信用贷',
    meetingTitle: '2024年第8次总经...',
    decisionTime: '2024-11-01 12:00:00',
    decisionNo: '【2024】8号',
    decisionResult: '不同意收购',
    createBy: '胡图图',
    createTime: '2024-11-01 12:00:00',
  },
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目',
    meetingDecision: '决策通过',
    assetTransferor: '东莞银行',
    productType: '信用贷',
    meetingTitle: '2024年第8次总经...',
    decisionTime: '2024-11-01 12:00:00',
    decisionNo: '【2024】8号',
    decisionResult: '不同意收购',
    createBy: '胡图图',
    createTime: '2024-11-01 12:00:00',
  },
]);
const loading = ref(false);
const showSearch = ref(false);
const columns = ref([
  { "key": 0, "label": "项目ID", "visible": true },
  { "key": 1, "label": "项目名称", "visible": true },
  { "key": 2, "label": "会议决策", "visible": true },
  { "key": 3, "label": "资产转让方", "visible": true },
  { "key": 4, "label": "产品类型", "visible": true },
  { "key": 5, "label": "会议标题", "visible": true },
  { "key": 6, "label": "决策时间", "visible": true },
  { "key": 7, "label": "决策文号", "visible": true },
  { "key": 8, "label": "附件", "visible": true },
  { "key": 9, "label": "决策结果", "visible": true },
  { "key": 10, "label": "创建人", "visible": true },
  { "key": 11, "label": "创建时间", "visible": true }
]);

// 获取转让方
getOwners().then((res) => {
  ownerOption.value = res.data;
});

function searchName() {
  getDictProductType().then((res) => {
    options.value = res.data;
  });
}

function getList() { }
function resetQuery() {
  queryParams.value = { pageNum: 1, pageSize: 10 };
  getList();
}
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleOpenDailog(refName) {
  proxy.$refs[refName].openDialog();
}
function handleDetails(row) {
  const query = { path: route.path, pageType: 'contract', progressStatus: 2 }
  router.push({ path: `/dueDiligence/projectInfo`, query })
}
</script>

<style lang="scss" scoped></style>