{"version": 3, "sources": ["../vue3-seamless-scroll/dist/vue3-seamless-scroll.es.js", "dep:vue3-seamless-scroll"], "sourcesContent": ["import { defineComponent, ref, computed, watch, onBeforeMount, onMounted, createVNode, Fragment } from 'vue';\n\n/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param  {number}    delay -          A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {boolean}   [noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds while the\n *                                    throttled-function is being called. If noTrailing is false or unspecified, callback will be executed one final time\n *                                    after the last throttled-function call. (After the throttled-function has not been called for `delay` milliseconds,\n *                                    the internal counter is reset).\n * @param  {Function}  callback -       A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                    to `callback` when the throttled-function is executed.\n * @param  {boolean}   [debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is false (at end),\n *                                    schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function}  A new, throttled, function.\n */\nfunction throttle (delay, noTrailing, callback, debounceMode) {\n  /*\n   * After wrapper has stopped being called, this timeout ensures that\n   * `callback` is executed at the proper times in `throttle` and `end`\n   * debounce modes.\n   */\n  var timeoutID;\n  var cancelled = false; // Keep track of the last time `callback` was executed.\n\n  var lastExec = 0; // Function to clear existing timeout\n\n  function clearExistingTimeout() {\n    if (timeoutID) {\n      clearTimeout(timeoutID);\n    }\n  } // Function to cancel next exec\n\n\n  function cancel() {\n    clearExistingTimeout();\n    cancelled = true;\n  } // `noTrailing` defaults to falsy.\n\n\n  if (typeof noTrailing !== 'boolean') {\n    debounceMode = callback;\n    callback = noTrailing;\n    noTrailing = undefined;\n  }\n  /*\n   * The `wrapper` function encapsulates all of the throttling / debouncing\n   * functionality and when executed will limit the rate at which `callback`\n   * is executed.\n   */\n\n\n  function wrapper() {\n    for (var _len = arguments.length, arguments_ = new Array(_len), _key = 0; _key < _len; _key++) {\n      arguments_[_key] = arguments[_key];\n    }\n\n    var self = this;\n    var elapsed = Date.now() - lastExec;\n\n    if (cancelled) {\n      return;\n    } // Execute `callback` and update the `lastExec` timestamp.\n\n\n    function exec() {\n      lastExec = Date.now();\n      callback.apply(self, arguments_);\n    }\n    /*\n     * If `debounceMode` is true (at begin) this is used to clear the flag\n     * to allow future `callback` executions.\n     */\n\n\n    function clear() {\n      timeoutID = undefined;\n    }\n\n    if (debounceMode && !timeoutID) {\n      /*\n       * Since `wrapper` is being called for the first time and\n       * `debounceMode` is true (at begin), execute `callback`.\n       */\n      exec();\n    }\n\n    clearExistingTimeout();\n\n    if (debounceMode === undefined && elapsed > delay) {\n      /*\n       * In throttle mode, if `delay` time has been exceeded, execute\n       * `callback`.\n       */\n      exec();\n    } else if (noTrailing !== true) {\n      /*\n       * In trailing throttle mode, since `delay` time has not been\n       * exceeded, schedule `callback` to execute `delay` ms after most\n       * recent execution.\n       *\n       * If `debounceMode` is true (at begin), schedule `clear` to execute\n       * after `delay` ms.\n       *\n       * If `debounceMode` is false (at end), schedule `callback` to\n       * execute after `delay` ms.\n       */\n      timeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === undefined ? delay - elapsed : delay);\n    }\n  }\n\n  wrapper.cancel = cancel; // Return the wrapper function.\n\n  return wrapper;\n}\n\nconst props = {\n  // 是否开启自动滚动\n  modelValue: {\n    type: Boolean,\n    default: true\n  },\n  // 原始数据列表\n  list: {\n    type: Array,\n    required: true\n  },\n  // 步进速度，step 需是单步大小的约数\n  step: {\n    type: Number,\n    default: 1\n  },\n  // 开启滚动的数据量\n  limitScrollNum: {\n    type: Number,\n    default: 1\n  },\n  // 是否开启鼠标悬停\n  hover: {\n    type: Boolean,\n    default: false\n  },\n  // 控制滚动方向\n  direction: {\n    type: String,\n    default: 'up'\n  },\n  // 单步运动停止的高度\n  singleHeight: {\n    type: Number,\n    default: 0\n  },\n  // 单步运动停止的宽度\n  singleWidth: {\n    type: Number,\n    default: 0\n  },\n  // 单步停止等待时间(默认值 1000ms)\n  singleWaitTime: {\n    type: Number,\n    default: 1000\n  },\n  // 是否开启 rem 度量\n  isRemUnit: {\n    type: Boolean,\n    default: false\n  },\n  // 开启数据更新监听\n  isWatch: {\n    type: Boolean,\n    default: true\n  },\n  // 动画时间\n  delay: {\n    type: Number,\n    default: 0\n  },\n  // 动画方式\n  ease: {\n    type: [String, Object],\n    default: 'ease-in'\n  },\n  // 动画循环次数，-1表示一直动画\n  count: {\n    type: Number,\n    default: -1\n  },\n  // 拷贝几份滚动列表\n  copyNum: {\n    type: Number,\n    default: 1\n  },\n  // 开启鼠标悬停时支持滚轮滚动\n  wheel: {\n    type: Boolean,\n    default: false\n  }\n};\n\nglobalThis.window.cancelAnimationFrame = function () {\n  return globalThis.window.cancelAnimationFrame || // @ts-ignore\n  globalThis.window.webkitCancelAnimationFrame || // @ts-ignore\n  globalThis.window.mozCancelAnimationFrame || // @ts-ignore\n  globalThis.window.oCancelAnimationFrame || // @ts-ignore\n  globalThis.window.msCancelAnimationFrame || function (id) {\n    return globalThis.window.clearTimeout(id);\n  };\n}();\n\nglobalThis.window.requestAnimationFrame = function () {\n  return globalThis.window.requestAnimationFrame || // @ts-ignore\n  globalThis.window.webkitRequestAnimationFrame || // @ts-ignore\n  globalThis.window.mozRequestAnimationFrame || // @ts-ignore\n  globalThis.window.oRequestAnimationFrame || // @ts-ignore\n  globalThis.window.msRequestAnimationFrame || function (callback) {\n    return globalThis.window.setTimeout(callback, 1000 / 60);\n  };\n}();\n\nfunction dataWarm(modelValue) {\n  if (typeof modelValue !== 'boolean' && modelValue.length > 100) {\n    console.warn(`数据达到了${modelValue.length}条有点多哦~,可能会造成部分老旧浏览器卡顿。`);\n  }\n}\n\nconst Vue3SeamlessScroll = defineComponent({\n  name: 'vue3-seamless-scroll',\n  inheritAttrs: false,\n  props,\n  emits: ['stop', 'count'],\n\n  setup(props, {\n    slots,\n    emit,\n    attrs\n  }) {\n    const scrollRef = ref(null);\n    const slotListRef = ref(null);\n    const realBoxRef = ref(null);\n    const reqFrame = ref(null);\n    const singleWaitTimeout = ref(null);\n    const realBoxWidth = ref(0);\n    const realBoxHeight = ref(0);\n    const xPos = ref(0);\n    const yPos = ref(0);\n    const isHover = ref(false);\n\n    const _count = ref(0);\n\n    const isScroll = computed(() => props.list.length >= props.limitScrollNum);\n    const realBoxStyle = computed(() => {\n      return {\n        width: realBoxWidth.value ? `${realBoxWidth.value}px` : 'auto',\n        transform: `translate(${xPos.value}px,${yPos.value}px)`,\n        // @ts-ignore\n        transition: `all ${typeof props.ease === 'string' ? props.ease : 'cubic-bezier(' + props.ease.x1 + ',' + props.ease.y1 + ',' + props.ease.x2 + ',' + props.ease.y2 + ')'} ${props.delay}ms`,\n        overflow: 'hidden'\n      };\n    });\n    const isHorizontal = computed(() => props.direction == 'left' || props.direction == 'right');\n    const floatStyle = computed(() => {\n      return isHorizontal.value ? {\n        float: 'left',\n        overflow: 'hidden'\n      } : {\n        overflow: 'hidden'\n      };\n    });\n    const baseFontSize = computed(() => {\n      return props.isRemUnit ? parseInt(globalThis.window.getComputedStyle(globalThis.document.documentElement, null).fontSize) : 1;\n    });\n    const realSingleStopWidth = computed(() => props.singleWidth * baseFontSize.value);\n    const realSingleStopHeight = computed(() => props.singleHeight * baseFontSize.value);\n    const step = computed(() => {\n      let singleStep;\n      let _step = props.step;\n\n      if (isHorizontal.value) {\n        singleStep = realSingleStopWidth.value;\n      } else {\n        singleStep = realSingleStopHeight.value;\n      }\n\n      if (singleStep > 0 && singleStep % _step > 0) {\n        console.error(\"如果设置了单步滚动,step需是单步大小的约数,否则无法保证单步滚动结束的位置是否准确。~~~~~\");\n      }\n\n      return _step;\n    });\n\n    function cancle() {\n      cancelAnimationFrame(reqFrame.value);\n      reqFrame.value = null;\n    }\n\n    function animation(_direction, _step, isWheel) {\n      reqFrame.value = requestAnimationFrame(function () {\n        const h = realBoxHeight.value / 2;\n        const w = realBoxWidth.value / 2;\n\n        if (_direction === 'up') {\n          if (Math.abs(yPos.value) >= h) {\n            yPos.value = 0;\n            _count.value += 1;\n            emit('count', _count.value);\n          }\n\n          yPos.value -= _step;\n        } else if (_direction === 'down') {\n          if (yPos.value >= 0) {\n            yPos.value = h * -1;\n            _count.value += 1;\n            emit('count', _count.value);\n          }\n\n          yPos.value += _step;\n        } else if (_direction === 'left') {\n          if (Math.abs(xPos.value) >= w) {\n            xPos.value = 0;\n            _count.value += 1;\n            emit('count', _count.value);\n          }\n\n          xPos.value -= _step;\n        } else if (_direction === 'right') {\n          if (xPos.value >= 0) {\n            xPos.value = w * -1;\n            _count.value += 1;\n            emit('count', _count.value);\n          }\n\n          xPos.value += _step;\n        }\n\n        if (isWheel) {\n          return;\n        }\n\n        let {\n          singleWaitTime\n        } = props;\n\n        if (singleWaitTimeout.value) {\n          clearTimeout(singleWaitTimeout.value);\n        }\n\n        if (!!realSingleStopHeight.value) {\n          if (Math.abs(yPos.value) % realSingleStopHeight.value < _step) {\n            singleWaitTimeout.value = setTimeout(() => {\n              move();\n            }, singleWaitTime);\n          } else {\n            move();\n          }\n        } else if (!!realSingleStopWidth.value) {\n          if (Math.abs(xPos.value) % realSingleStopWidth.value < _step) {\n            singleWaitTimeout.value = setTimeout(() => {\n              move();\n            }, singleWaitTime);\n          } else {\n            move();\n          }\n        } else {\n          move();\n        }\n      });\n    }\n\n    function move() {\n      cancle();\n\n      if (isHover.value || !isScroll.value || _count.value === props.count) {\n        emit('stop', _count.value);\n        _count.value = 0;\n        return;\n      }\n\n      animation(props.direction, step.value, false);\n    }\n\n    function initMove() {\n      dataWarm(props.list);\n\n      if (isHorizontal.value) {\n        let slotListWidth = slotListRef.value.offsetWidth;\n        slotListWidth = slotListWidth * 2 + 1;\n        realBoxWidth.value = slotListWidth;\n      }\n\n      if (isScroll.value) {\n        realBoxHeight.value = realBoxRef.value.offsetHeight;\n\n        if (props.modelValue) {\n          move();\n        }\n      } else {\n        cancle();\n        yPos.value = xPos.value = 0;\n      }\n    }\n\n    function startMove() {\n      isHover.value = false;\n      move();\n    }\n\n    function stopMove() {\n      isHover.value = true;\n\n      if (singleWaitTimeout.value) {\n        clearTimeout(singleWaitTimeout.value);\n      }\n\n      cancle();\n    }\n\n    const hoverStop = computed(() => props.hover && props.modelValue && isScroll.value);\n    const throttleFunc = throttle(30, e => {\n      cancle();\n      const singleHeight = !!realSingleStopHeight.value ? realSingleStopHeight.value : 15;\n\n      if (e.deltaY < 0) {\n        animation('down', singleHeight, true);\n      }\n\n      if (e.deltaY > 0) {\n        animation('up', singleHeight, true);\n      }\n    });\n\n    const onWheel = e => {\n      throttleFunc(e);\n    };\n\n    function reset() {\n      cancle();\n      isHover.value = false;\n      initMove();\n    }\n\n    watch(() => props.list, () => {\n      if (props.isWatch) {\n        reset();\n      }\n    }, {\n      deep: true\n    });\n    watch(() => props.modelValue, newValue => {\n      if (newValue) {\n        startMove();\n      } else {\n        stopMove();\n      }\n    });\n    watch(() => props.count, newValue => {\n      if (newValue !== 0) {\n        startMove();\n      }\n    });\n    onBeforeMount(() => {\n      cancle();\n      clearTimeout(singleWaitTimeout.value);\n    });\n    onMounted(() => {\n      initMove();\n    });\n    const {\n      default: $default,\n      html\n    } = slots;\n    const copyNum = new Array(props.copyNum).fill(null);\n\n    const getHtml = () => {\n      return createVNode(Fragment, null, [createVNode(\"div\", {\n        \"ref\": slotListRef,\n        \"style\": floatStyle.value\n      }, [$default()]), isScroll ? copyNum.map(() => {\n        if (html && typeof html === 'function') {\n          return createVNode(\"div\", {\n            \"style\": floatStyle.value\n          }, [html()]);\n        } else {\n          return createVNode(\"div\", {\n            \"style\": floatStyle.value\n          }, [$default()]);\n        }\n      }) : null]);\n    };\n\n    return () => createVNode(\"div\", {\n      \"ref\": scrollRef,\n      \"class\": attrs.class\n    }, [props.wheel && props.hover ? createVNode(\"div\", {\n      \"ref\": realBoxRef,\n      \"style\": realBoxStyle.value,\n      \"onMouseenter\": () => {\n        if (hoverStop.value) {\n          stopMove();\n        }\n      },\n      \"onMouseleave\": () => {\n        if (hoverStop.value) {\n          startMove();\n        }\n      },\n      \"onWheel\": e => {\n        if (hoverStop.value) {\n          onWheel(e);\n        }\n      }\n    }, [getHtml()]) : createVNode(\"div\", {\n      \"ref\": realBoxRef,\n      \"style\": realBoxStyle.value,\n      \"onMouseenter\": () => {\n        if (hoverStop.value) {\n          stopMove();\n        }\n      },\n      \"onMouseleave\": () => {\n        if (hoverStop.value) {\n          startMove();\n        }\n      }\n    }, [getHtml()])]);\n  }\n\n});\n\nconst install = function (app, options = {}) {\n  app.component(options.name || Vue3SeamlessScroll.name, Vue3SeamlessScroll);\n};\n\nfunction index (app) {\n  app.use(install);\n}\n\nexport { Vue3SeamlessScroll, index as default };\n", "import d from \"./node_modules/vue3-seamless-scroll/dist/vue3-seamless-scroll.es.js\";export default d;\nexport * from \"./node_modules/vue3-seamless-scroll/dist/vue3-seamless-scroll.es.js\""], "mappings": ";;;;;;;;;;;;;;;;;AAoBA,kBAAmB,OAAO,YAAY,UAAU,cAAc;AAM5D,MAAI;AACJ,MAAI,YAAY;AAEhB,MAAI,WAAW;AAEf,kCAAgC;AAC9B,QAAI,WAAW;AACb,mBAAa;AAAA;AAAA;AAKjB,oBAAkB;AAChB;AACA,gBAAY;AAAA;AAId,MAAI,OAAO,eAAe,WAAW;AACnC,mBAAe;AACf,eAAW;AACX,iBAAa;AAAA;AASf,qBAAmB;AACjB,aAAS,OAAO,UAAU,QAAQ,aAAa,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC7F,iBAAW,QAAQ,UAAU;AAAA;AAG/B,QAAI,OAAO;AACX,QAAI,UAAU,KAAK,QAAQ;AAE3B,QAAI,WAAW;AACb;AAAA;AAIF,oBAAgB;AACd,iBAAW,KAAK;AAChB,eAAS,MAAM,MAAM;AAAA;AAQvB,qBAAiB;AACf,kBAAY;AAAA;AAGd,QAAI,gBAAgB,CAAC,WAAW;AAK9B;AAAA;AAGF;AAEA,QAAI,iBAAiB,UAAa,UAAU,OAAO;AAKjD;AAAA,eACS,eAAe,MAAM;AAY9B,kBAAY,WAAW,eAAe,QAAQ,MAAM,iBAAiB,SAAY,QAAQ,UAAU;AAAA;AAAA;AAIvG,UAAQ,SAAS;AAEjB,SAAO;AAAA;AAGT,IAAM,QAAQ;AAAA,EAEZ,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA;AAAA,EAGZ,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,MAAM;AAAA,IACJ,MAAM,CAAC,QAAQ;AAAA,IACf,SAAS;AAAA;AAAA,EAGX,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,EAGX,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA;AAAA;AAIb,WAAW,OAAO,uBAAuB,WAAY;AACnD,SAAO,WAAW,OAAO,wBACzB,WAAW,OAAO,8BAClB,WAAW,OAAO,2BAClB,WAAW,OAAO,yBAClB,WAAW,OAAO,0BAA0B,SAAU,IAAI;AACxD,WAAO,WAAW,OAAO,aAAa;AAAA;AAAA;AAI1C,WAAW,OAAO,wBAAwB,WAAY;AACpD,SAAO,WAAW,OAAO,yBACzB,WAAW,OAAO,+BAClB,WAAW,OAAO,4BAClB,WAAW,OAAO,0BAClB,WAAW,OAAO,2BAA2B,SAAU,UAAU;AAC/D,WAAO,WAAW,OAAO,WAAW,UAAU,MAAO;AAAA;AAAA;AAIzD,kBAAkB,YAAY;AAC5B,MAAI,OAAO,eAAe,aAAa,WAAW,SAAS,KAAK;AAC9D,YAAQ,KAAK,iCAAQ,WAAW;AAAA;AAAA;AAIpC,IAAM,qBAAqB,gBAAgB;AAAA,EACzC,MAAM;AAAA,EACN,cAAc;AAAA,EACd;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAEhB,MAAM,QAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,KACC;AACD,UAAM,YAAY,IAAI;AACtB,UAAM,cAAc,IAAI;AACxB,UAAM,aAAa,IAAI;AACvB,UAAM,WAAW,IAAI;AACrB,UAAM,oBAAoB,IAAI;AAC9B,UAAM,eAAe,IAAI;AACzB,UAAM,gBAAgB,IAAI;AAC1B,UAAM,OAAO,IAAI;AACjB,UAAM,OAAO,IAAI;AACjB,UAAM,UAAU,IAAI;AAEpB,UAAM,SAAS,IAAI;AAEnB,UAAM,WAAW,SAAS,MAAM,OAAM,KAAK,UAAU,OAAM;AAC3D,UAAM,eAAe,SAAS,MAAM;AAClC,aAAO;AAAA,QACL,OAAO,aAAa,QAAQ,GAAG,aAAa,YAAY;AAAA,QACxD,WAAW,aAAa,KAAK,WAAW,KAAK;AAAA,QAE7C,YAAY,OAAO,OAAO,OAAM,SAAS,WAAW,OAAM,OAAO,kBAAkB,OAAM,KAAK,KAAK,MAAM,OAAM,KAAK,KAAK,MAAM,OAAM,KAAK,KAAK,MAAM,OAAM,KAAK,KAAK,OAAO,OAAM;AAAA,QAClL,UAAU;AAAA;AAAA;AAGd,UAAM,eAAe,SAAS,MAAM,OAAM,aAAa,UAAU,OAAM,aAAa;AACpF,UAAM,aAAa,SAAS,MAAM;AAChC,aAAO,aAAa,QAAQ;AAAA,QAC1B,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,QACF,UAAU;AAAA;AAAA;AAGd,UAAM,eAAe,SAAS,MAAM;AAClC,aAAO,OAAM,YAAY,SAAS,WAAW,OAAO,iBAAiB,WAAW,SAAS,iBAAiB,MAAM,YAAY;AAAA;AAE9H,UAAM,sBAAsB,SAAS,MAAM,OAAM,cAAc,aAAa;AAC5E,UAAM,uBAAuB,SAAS,MAAM,OAAM,eAAe,aAAa;AAC9E,UAAM,OAAO,SAAS,MAAM;AAC1B,UAAI;AACJ,UAAI,QAAQ,OAAM;AAElB,UAAI,aAAa,OAAO;AACtB,qBAAa,oBAAoB;AAAA,aAC5B;AACL,qBAAa,qBAAqB;AAAA;AAGpC,UAAI,aAAa,KAAK,aAAa,QAAQ,GAAG;AAC5C,gBAAQ,MAAM;AAAA;AAGhB,aAAO;AAAA;AAGT,sBAAkB;AAChB,2BAAqB,SAAS;AAC9B,eAAS,QAAQ;AAAA;AAGnB,uBAAmB,YAAY,OAAO,SAAS;AAC7C,eAAS,QAAQ,sBAAsB,WAAY;AACjD,cAAM,IAAI,cAAc,QAAQ;AAChC,cAAM,IAAI,aAAa,QAAQ;AAE/B,YAAI,eAAe,MAAM;AACvB,cAAI,KAAK,IAAI,KAAK,UAAU,GAAG;AAC7B,iBAAK,QAAQ;AACb,mBAAO,SAAS;AAChB,iBAAK,SAAS,OAAO;AAAA;AAGvB,eAAK,SAAS;AAAA,mBACL,eAAe,QAAQ;AAChC,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,QAAQ,IAAI;AACjB,mBAAO,SAAS;AAChB,iBAAK,SAAS,OAAO;AAAA;AAGvB,eAAK,SAAS;AAAA,mBACL,eAAe,QAAQ;AAChC,cAAI,KAAK,IAAI,KAAK,UAAU,GAAG;AAC7B,iBAAK,QAAQ;AACb,mBAAO,SAAS;AAChB,iBAAK,SAAS,OAAO;AAAA;AAGvB,eAAK,SAAS;AAAA,mBACL,eAAe,SAAS;AACjC,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,QAAQ,IAAI;AACjB,mBAAO,SAAS;AAChB,iBAAK,SAAS,OAAO;AAAA;AAGvB,eAAK,SAAS;AAAA;AAGhB,YAAI,SAAS;AACX;AAAA;AAGF,YAAI;AAAA,UACF;AAAA,YACE;AAEJ,YAAI,kBAAkB,OAAO;AAC3B,uBAAa,kBAAkB;AAAA;AAGjC,YAAI,CAAC,CAAC,qBAAqB,OAAO;AAChC,cAAI,KAAK,IAAI,KAAK,SAAS,qBAAqB,QAAQ,OAAO;AAC7D,8BAAkB,QAAQ,WAAW,MAAM;AACzC;AAAA,eACC;AAAA,iBACE;AACL;AAAA;AAAA,mBAEO,CAAC,CAAC,oBAAoB,OAAO;AACtC,cAAI,KAAK,IAAI,KAAK,SAAS,oBAAoB,QAAQ,OAAO;AAC5D,8BAAkB,QAAQ,WAAW,MAAM;AACzC;AAAA,eACC;AAAA,iBACE;AACL;AAAA;AAAA,eAEG;AACL;AAAA;AAAA;AAAA;AAKN,oBAAgB;AACd;AAEA,UAAI,QAAQ,SAAS,CAAC,SAAS,SAAS,OAAO,UAAU,OAAM,OAAO;AACpE,aAAK,QAAQ,OAAO;AACpB,eAAO,QAAQ;AACf;AAAA;AAGF,gBAAU,OAAM,WAAW,KAAK,OAAO;AAAA;AAGzC,wBAAoB;AAClB,eAAS,OAAM;AAEf,UAAI,aAAa,OAAO;AACtB,YAAI,gBAAgB,YAAY,MAAM;AACtC,wBAAgB,gBAAgB,IAAI;AACpC,qBAAa,QAAQ;AAAA;AAGvB,UAAI,SAAS,OAAO;AAClB,sBAAc,QAAQ,WAAW,MAAM;AAEvC,YAAI,OAAM,YAAY;AACpB;AAAA;AAAA,aAEG;AACL;AACA,aAAK,QAAQ,KAAK,QAAQ;AAAA;AAAA;AAI9B,yBAAqB;AACnB,cAAQ,QAAQ;AAChB;AAAA;AAGF,wBAAoB;AAClB,cAAQ,QAAQ;AAEhB,UAAI,kBAAkB,OAAO;AAC3B,qBAAa,kBAAkB;AAAA;AAGjC;AAAA;AAGF,UAAM,YAAY,SAAS,MAAM,OAAM,SAAS,OAAM,cAAc,SAAS;AAC7E,UAAM,eAAe,SAAS,IAAI,OAAK;AACrC;AACA,YAAM,eAAe,CAAC,CAAC,qBAAqB,QAAQ,qBAAqB,QAAQ;AAEjF,UAAI,EAAE,SAAS,GAAG;AAChB,kBAAU,QAAQ,cAAc;AAAA;AAGlC,UAAI,EAAE,SAAS,GAAG;AAChB,kBAAU,MAAM,cAAc;AAAA;AAAA;AAIlC,UAAM,UAAU,OAAK;AACnB,mBAAa;AAAA;AAGf,qBAAiB;AACf;AACA,cAAQ,QAAQ;AAChB;AAAA;AAGF,UAAM,MAAM,OAAM,MAAM,MAAM;AAC5B,UAAI,OAAM,SAAS;AACjB;AAAA;AAAA,OAED;AAAA,MACD,MAAM;AAAA;AAER,UAAM,MAAM,OAAM,YAAY,cAAY;AACxC,UAAI,UAAU;AACZ;AAAA,aACK;AACL;AAAA;AAAA;AAGJ,UAAM,MAAM,OAAM,OAAO,cAAY;AACnC,UAAI,aAAa,GAAG;AAClB;AAAA;AAAA;AAGJ,kBAAc,MAAM;AAClB;AACA,mBAAa,kBAAkB;AAAA;AAEjC,cAAU,MAAM;AACd;AAAA;AAEF,UAAM;AAAA,MACJ,SAAS;AAAA,MACT;AAAA,QACE;AACJ,UAAM,UAAU,IAAI,MAAM,OAAM,SAAS,KAAK;AAE9C,UAAM,UAAU,MAAM;AACpB,aAAO,YAAY,UAAU,MAAM,CAAC,YAAY,OAAO;AAAA,QACrD,OAAO;AAAA,QACP,SAAS,WAAW;AAAA,SACnB,CAAC,cAAc,WAAW,QAAQ,IAAI,MAAM;AAC7C,YAAI,QAAQ,OAAO,SAAS,YAAY;AACtC,iBAAO,YAAY,OAAO;AAAA,YACxB,SAAS,WAAW;AAAA,aACnB,CAAC;AAAA,eACC;AACL,iBAAO,YAAY,OAAO;AAAA,YACxB,SAAS,WAAW;AAAA,aACnB,CAAC;AAAA;AAAA,WAEH;AAAA;AAGP,WAAO,MAAM,YAAY,OAAO;AAAA,MAC9B,OAAO;AAAA,MACP,SAAS,MAAM;AAAA,OACd,CAAC,OAAM,SAAS,OAAM,QAAQ,YAAY,OAAO;AAAA,MAClD,OAAO;AAAA,MACP,SAAS,aAAa;AAAA,MACtB,gBAAgB,MAAM;AACpB,YAAI,UAAU,OAAO;AACnB;AAAA;AAAA;AAAA,MAGJ,gBAAgB,MAAM;AACpB,YAAI,UAAU,OAAO;AACnB;AAAA;AAAA;AAAA,MAGJ,WAAW,OAAK;AACd,YAAI,UAAU,OAAO;AACnB,kBAAQ;AAAA;AAAA;AAAA,OAGX,CAAC,cAAc,YAAY,OAAO;AAAA,MACnC,OAAO;AAAA,MACP,SAAS,aAAa;AAAA,MACtB,gBAAgB,MAAM;AACpB,YAAI,UAAU,OAAO;AACnB;AAAA;AAAA;AAAA,MAGJ,gBAAgB,MAAM;AACpB,YAAI,UAAU,OAAO;AACnB;AAAA;AAAA;AAAA,OAGH,CAAC;AAAA;AAAA;AAKR,IAAM,UAAU,SAAU,KAAK,UAAU,IAAI;AAC3C,MAAI,UAAU,QAAQ,QAAQ,mBAAmB,MAAM;AAAA;AAGzD,eAAgB,KAAK;AACnB,MAAI,IAAI;AAAA;;;ACzhB0E,IAAO,+BAAQ;", "names": []}