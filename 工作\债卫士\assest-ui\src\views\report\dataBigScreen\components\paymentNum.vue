<template>
    <div class="payment-data">
        <div class="payment-data-title">回款：</div>
        <div class="num-list">
            <div v-for="(v, i) in String(oldNum)" :key="v" :class="`num-item ${isActiveFun(i) && 'activeFlipPage'}`">
                {{ v }}
            </div>
        </div>
    </div>
</template>
<script setup>
const props = defineProps({
    num: { type: [String, Number], default: 0 }
})
const oldNum = ref(String(props.num))
function isActiveFun(index) {
    const strNum = String(props.num)
    const strOldNum = String(oldNum.value)
    const isFlag = strNum[index] != strOldNum[index]
    if (isFlag) {
        setTimeout(() => {
            oldNum.value = strNum
        }, 2500)
    }
    return isFlag
}
</script>

<style lang="scss" scoped>
.payment-data {
    display: flex;
    padding: 20px 20px 0;

    .payment-data-title {
        font-size: 20px;
        color: #38E1FF;
        margin-right: 20px;
        padding: 10px 10px 25px 25px;
        background-image: url(@/assets/images/dataBigScreen/repayment-bg.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: top;
    }

    .num-list {
        display: flex;
        gap: 20px;

        .num-item {
            font-size: 48px;
            color: #fff;
            padding: 15px 29px;
            background-image: url(@/assets/images/dataBigScreen/num-bg.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-position: center;

        }
    }
}

.activeFlipPage {
    /* 应用动画，2.5秒一次，无限循环 */
    animation: flipPage 3s infinite;
}

@keyframes flipPage {
    0% {
        transform: rotate3d(1, 0, 0, 0deg);
    }

    25% {
        transform: rotate3d(1, 0, 0, 90deg);
    }

    50% {
        transform: rotate3d(1, 0, 0, 180deg);
    }

    75% {
        transform: rotate3d(1, 0, 0, 270deg);
    }

    100% {
        transform: rotate3d(1, 0, 0, 360deg);
    }
}
</style>