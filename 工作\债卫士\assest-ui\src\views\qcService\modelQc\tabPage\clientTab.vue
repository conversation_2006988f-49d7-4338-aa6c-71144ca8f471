<template>
  <div>
    <el-form inline label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef">
      <el-form-item prop="modelName" label="模型名称">
        <el-input v-model="queryParams.modelName" style="width:240px" placeholder="请输入模型名称" />
      </el-form-item>
      <el-form-item prop="label" label="标签">
        <el-input v-model="queryParams.label" style="width:240px" placeholder="请输入标签" />
      </el-form-item>
      <el-form-item prop="status" label="状态">
        <el-select v-model="queryParams.status" style="width:240px" placeholder="请选择状态">
          <el-option v-for="(v, i) in switchStatusEnum" :key="i" :label="v" :name="i" :value="i">
            {{ v }}
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <div class="operation-revealing-area mb20">
      <el-button v-if="checkPermi(['modelQc:clientTab:add'])" type="warning" @click="update()">添加</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <div class="table-box">
      <el-table :data="dataList" v-loading="loading" @sort-change="handleSortChange">
        <el-table-column label="模型ID" v-if="columns[0].visible" align="center" width="120" prop="id" sortable="id" />
        <el-table-column label="模型名称" v-if="columns[1].visible" align="center" width="180" prop="modelName"
          sortable="modelName">
          <template #default="{ row }">
            <Tooltip :content="row.modelName" :length="10" :width="600" />
          </template>
        </el-table-column>
        <el-table-column label="标签" v-if="columns[2].visible" align="center" prop="label" sortable="label">
          <template #default="{ row }">
            <Tooltip :content="row.label" :length="30" :width="600" />
          </template>
        </el-table-column>
        <el-table-column v-if="columns[3].visible" align="center" width="240" prop="updateTime" sortable="updateTime"
          label="更新人/时间">
          <template #default="{ row }">
            <div>{{ `${row.updateBy || ''}${row.updateBy ? '/' : ''}${row.updateTime || ''}` }}</div>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[4].visible" align="center" prop="status" sortable="status" width="120"
          label="状态">
          <template #default="{ row }">
            <el-switch v-model="row.status" :active-value="0" :inactive-value="1" @change="handleChangeSwtich(row)"
              inline-prompt active-text="开" inactive-text="关" />
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="140" label="操作">
          <template #default="{ row }">
            <div>
              <el-button v-if="checkPermi(['modelQc:clientTab:edit'])" type="text" @click="update(row)">编辑</el-button>
              <el-button v-if="checkPermi(['modelQc:clientTab:remove'])" type="text" @click="remove(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <updateInfo ref="updateInfoRef" :getList="getList" />
  </div>
</template>

<script setup>
import { switchStatusEnum } from '@/utils/enum';
import updateInfo from '../dialog/updateInfo';
import { checkPermi } from "@/utils/permission";
import { selectTaskListApi, editTaskApi, removeTaskApi } from '@/api/qcService/modelQc';
const { proxy } = getCurrentInstance()
const queryParams = ref({
  type: 1,
  pageNum: 1,
  pageSize: 10,
})
const dataList = ref([])
const loading = ref(false)
const showSearch = ref(false)
const total = ref(2)
const columns = ref([
  { "key": 0, "label": "模型ID", "visible": true },
  { "key": 1, "label": "模型名称", "visible": true },
  { "key": 2, "label": "标签", "visible": true },
  { "key": 3, "label": "更新人/时间", "visible": true },
  { "key": 4, "label": "状态", "visible": true }
])
getList()
function getList() {
  const reqForm = JSON.parse(JSON.stringify(queryParams.value))
  loading.value = true
  selectTaskListApi(reqForm).then(res => {
    total.value = res.total
    dataList.value = res.rows
  }).finally(() => loading.value = false)
}
function handleSortChange({ prop, order }) {
  const orderObj = {
    id: 1,
    modelName: 3,
    label: 4,
    createTime: 5,
    status: 6,
  }
  delete queryParams.value.orderBy
  delete queryParams.value.sortOrder
  if (order) {
    queryParams.value.orderBy = orderObj[prop]
    queryParams.value.sortOrder = proxy.orderEnum[order]
  }
  handleQuery()
}

function resetQuery() {
  queryParams.value = {
    type: 1,
    pageNum: 1,
    pageSize: 10,
  }
  getList()
}
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}
function update(row) {
  const title = row ? '编辑模型库' : '添加模型库'
  const newRow = row ? JSON.parse(JSON.stringify(row)) : null
  proxy.$refs['updateInfoRef'].openDialog({ title, row: newRow, type: 1 })
}


function handleChangeSwtich(row) {
  const content = `是否确认${row.status == 1 ? '禁用' : '启用'}该模型，是否确认?`
  proxy.$modal.confirm(content).then(() => {
    editTaskApi(row).then(() => {
      proxy.$modal.msgSuccess('操作成功!')
      getList()
    })
  }).catch(() => getList())
}

function remove(row) {
  const content = `是否确认删除该模型，模型删除后不可恢复，是否确认?`
  proxy.$modal.confirm(content).then(() => {
    removeTaskApi(row).then(() => {
      proxy.$modal.msgSuccess('操作成功!')
      getList()
    })
  })
}
</script>

<style lang="scss" scoped></style>