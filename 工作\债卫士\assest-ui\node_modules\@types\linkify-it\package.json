{"name": "@types/linkify-it", "version": "5.0.0", "description": "TypeScript definitions for linkify-it", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/linkify-it", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "p<PERSON><PERSON>s", "url": "https://github.com/praxxis"}, {"name": "<PERSON>", "url": "https://github.com/rapropos/typed-linkify-it"}, {"name": "<PERSON>", "githubUsername": "alexplumb", "url": "https://github.com/alexplumb"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON>us", "url": "https://github.com/ragafus"}], "main": "", "types": "index.d.ts", "exports": {".": {"import": "./index.d.mts", "require": "./build/index.cjs.d.ts"}, "./*": {"import": "./*", "require": "./*"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/linkify-it"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "884f8e9f827ff23c410016d9edc6e2410efbd2555c6e9e6c0ed734edd4145036", "typeScriptVersion": "4.7"}