import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/visualchars/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/visualchars/plugin.js"() {
    (function() {
      "use strict";
      var Cell = function(initial) {
        var value2 = initial;
        var get2 = function() {
          return value2;
        };
        var set2 = function(v) {
          value2 = v;
        };
        return {
          get: get2,
          set: set2
        };
      };
      var global$1 = tinymce.util.Tools.resolve("tinymce.PluginManager");
      var get$2 = function(toggleState) {
        var isEnabled = function() {
          return toggleState.get();
        };
        return { isEnabled };
      };
      var fireVisualChars = function(editor, state) {
        return editor.fire("VisualChars", { state });
      };
      var typeOf = function(x) {
        var t = typeof x;
        if (x === null) {
          return "null";
        } else if (t === "object" && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === "Array")) {
          return "array";
        } else if (t === "object" && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === "String")) {
          return "string";
        } else {
          return t;
        }
      };
      var isType$1 = function(type2) {
        return function(value2) {
          return typeOf(value2) === type2;
        };
      };
      var isSimpleType = function(type2) {
        return function(value2) {
          return typeof value2 === type2;
        };
      };
      var isString = isType$1("string");
      var isBoolean = isSimpleType("boolean");
      var isNumber = isSimpleType("number");
      var noop = function() {
      };
      var constant = function(value2) {
        return function() {
          return value2;
        };
      };
      var identity = function(x) {
        return x;
      };
      var never = constant(false);
      var always = constant(true);
      var none = function() {
        return NONE;
      };
      var NONE = function() {
        var call = function(thunk) {
          return thunk();
        };
        var id = identity;
        var me = {
          fold: function(n, _s) {
            return n();
          },
          isSome: never,
          isNone: always,
          getOr: id,
          getOrThunk: call,
          getOrDie: function(msg) {
            throw new Error(msg || "error: getOrDie called on none.");
          },
          getOrNull: constant(null),
          getOrUndefined: constant(void 0),
          or: id,
          orThunk: call,
          map: none,
          each: noop,
          bind: none,
          exists: never,
          forall: always,
          filter: function() {
            return none();
          },
          toArray: function() {
            return [];
          },
          toString: constant("none()")
        };
        return me;
      }();
      var some = function(a) {
        var constant_a = constant(a);
        var self = function() {
          return me;
        };
        var bind = function(f) {
          return f(a);
        };
        var me = {
          fold: function(n, s) {
            return s(a);
          },
          isSome: always,
          isNone: never,
          getOr: constant_a,
          getOrThunk: constant_a,
          getOrDie: constant_a,
          getOrNull: constant_a,
          getOrUndefined: constant_a,
          or: self,
          orThunk: self,
          map: function(f) {
            return some(f(a));
          },
          each: function(f) {
            f(a);
          },
          bind,
          exists: bind,
          forall: bind,
          filter: function(f) {
            return f(a) ? me : NONE;
          },
          toArray: function() {
            return [a];
          },
          toString: function() {
            return "some(" + a + ")";
          }
        };
        return me;
      };
      var from = function(value2) {
        return value2 === null || value2 === void 0 ? NONE : some(value2);
      };
      var Optional = {
        some,
        none,
        from
      };
      var map = function(xs, f) {
        var len = xs.length;
        var r = new Array(len);
        for (var i = 0; i < len; i++) {
          var x = xs[i];
          r[i] = f(x, i);
        }
        return r;
      };
      var each$1 = function(xs, f) {
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          f(x, i);
        }
      };
      var filter = function(xs, pred) {
        var r = [];
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          if (pred(x, i)) {
            r.push(x);
          }
        }
        return r;
      };
      var keys = Object.keys;
      var each = function(obj, f) {
        var props = keys(obj);
        for (var k = 0, len = props.length; k < len; k++) {
          var i = props[k];
          var x = obj[i];
          f(x, i);
        }
      };
      typeof window !== "undefined" ? window : Function("return this;")();
      var TEXT = 3;
      var type = function(element) {
        return element.dom.nodeType;
      };
      var value = function(element) {
        return element.dom.nodeValue;
      };
      var isType = function(t) {
        return function(element) {
          return type(element) === t;
        };
      };
      var isText = isType(TEXT);
      var rawSet = function(dom, key, value2) {
        if (isString(value2) || isBoolean(value2) || isNumber(value2)) {
          dom.setAttribute(key, value2 + "");
        } else {
          console.error("Invalid call to Attribute.set. Key ", key, ":: Value ", value2, ":: Element ", dom);
          throw new Error("Attribute value was not simple");
        }
      };
      var set = function(element, key, value2) {
        rawSet(element.dom, key, value2);
      };
      var get$1 = function(element, key) {
        var v = element.dom.getAttribute(key);
        return v === null ? void 0 : v;
      };
      var remove$3 = function(element, key) {
        element.dom.removeAttribute(key);
      };
      var read = function(element, attr) {
        var value2 = get$1(element, attr);
        return value2 === void 0 || value2 === "" ? [] : value2.split(" ");
      };
      var add$2 = function(element, attr, id) {
        var old = read(element, attr);
        var nu = old.concat([id]);
        set(element, attr, nu.join(" "));
        return true;
      };
      var remove$2 = function(element, attr, id) {
        var nu = filter(read(element, attr), function(v) {
          return v !== id;
        });
        if (nu.length > 0) {
          set(element, attr, nu.join(" "));
        } else {
          remove$3(element, attr);
        }
        return false;
      };
      var supports = function(element) {
        return element.dom.classList !== void 0;
      };
      var get = function(element) {
        return read(element, "class");
      };
      var add$1 = function(element, clazz) {
        return add$2(element, "class", clazz);
      };
      var remove$1 = function(element, clazz) {
        return remove$2(element, "class", clazz);
      };
      var add = function(element, clazz) {
        if (supports(element)) {
          element.dom.classList.add(clazz);
        } else {
          add$1(element, clazz);
        }
      };
      var cleanClass = function(element) {
        var classList = supports(element) ? element.dom.classList : get(element);
        if (classList.length === 0) {
          remove$3(element, "class");
        }
      };
      var remove = function(element, clazz) {
        if (supports(element)) {
          var classList = element.dom.classList;
          classList.remove(clazz);
        } else {
          remove$1(element, clazz);
        }
        cleanClass(element);
      };
      var fromHtml = function(html, scope) {
        var doc = scope || document;
        var div = doc.createElement("div");
        div.innerHTML = html;
        if (!div.hasChildNodes() || div.childNodes.length > 1) {
          console.error("HTML does not have a single root node", html);
          throw new Error("HTML must have a single root node");
        }
        return fromDom(div.childNodes[0]);
      };
      var fromTag = function(tag, scope) {
        var doc = scope || document;
        var node = doc.createElement(tag);
        return fromDom(node);
      };
      var fromText = function(text, scope) {
        var doc = scope || document;
        var node = doc.createTextNode(text);
        return fromDom(node);
      };
      var fromDom = function(node) {
        if (node === null || node === void 0) {
          throw new Error("Node cannot be null or undefined");
        }
        return { dom: node };
      };
      var fromPoint = function(docElm, x, y) {
        return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);
      };
      var SugarElement = {
        fromHtml,
        fromTag,
        fromText,
        fromDom,
        fromPoint
      };
      var charMap = {
        "\xA0": "nbsp",
        "\xAD": "shy"
      };
      var charMapToRegExp = function(charMap2, global2) {
        var regExp2 = "";
        each(charMap2, function(_value, key) {
          regExp2 += key;
        });
        return new RegExp("[" + regExp2 + "]", global2 ? "g" : "");
      };
      var charMapToSelector = function(charMap2) {
        var selector2 = "";
        each(charMap2, function(value2) {
          if (selector2) {
            selector2 += ",";
          }
          selector2 += "span.mce-" + value2;
        });
        return selector2;
      };
      var regExp = charMapToRegExp(charMap);
      var regExpGlobal = charMapToRegExp(charMap, true);
      var selector = charMapToSelector(charMap);
      var nbspClass = "mce-nbsp";
      var wrapCharWithSpan = function(value2) {
        return '<span data-mce-bogus="1" class="mce-' + charMap[value2] + '">' + value2 + "</span>";
      };
      var isMatch = function(n) {
        var value$1 = value(n);
        return isText(n) && value$1 !== void 0 && regExp.test(value$1);
      };
      var filterDescendants = function(scope, predicate) {
        var result = [];
        var dom = scope.dom;
        var children = map(dom.childNodes, SugarElement.fromDom);
        each$1(children, function(x) {
          if (predicate(x)) {
            result = result.concat([x]);
          }
          result = result.concat(filterDescendants(x, predicate));
        });
        return result;
      };
      var findParentElm = function(elm, rootElm) {
        while (elm.parentNode) {
          if (elm.parentNode === rootElm) {
            return elm;
          }
          elm = elm.parentNode;
        }
      };
      var replaceWithSpans = function(text) {
        return text.replace(regExpGlobal, wrapCharWithSpan);
      };
      var isWrappedNbsp = function(node) {
        return node.nodeName.toLowerCase() === "span" && node.classList.contains("mce-nbsp-wrap");
      };
      var show = function(editor, rootElm) {
        var nodeList = filterDescendants(SugarElement.fromDom(rootElm), isMatch);
        each$1(nodeList, function(n) {
          var parent = n.dom.parentNode;
          if (isWrappedNbsp(parent)) {
            add(SugarElement.fromDom(parent), nbspClass);
          } else {
            var withSpans = replaceWithSpans(editor.dom.encode(value(n)));
            var div = editor.dom.create("div", null, withSpans);
            var node = void 0;
            while (node = div.lastChild) {
              editor.dom.insertAfter(node, n.dom);
            }
            editor.dom.remove(n.dom);
          }
        });
      };
      var hide = function(editor, rootElm) {
        var nodeList = editor.dom.select(selector, rootElm);
        each$1(nodeList, function(node) {
          if (isWrappedNbsp(node)) {
            remove(SugarElement.fromDom(node), nbspClass);
          } else {
            editor.dom.remove(node, true);
          }
        });
      };
      var toggle = function(editor) {
        var body = editor.getBody();
        var bookmark = editor.selection.getBookmark();
        var parentNode = findParentElm(editor.selection.getNode(), body);
        parentNode = parentNode !== void 0 ? parentNode : body;
        hide(editor, parentNode);
        show(editor, parentNode);
        editor.selection.moveToBookmark(bookmark);
      };
      var applyVisualChars = function(editor, toggleState) {
        fireVisualChars(editor, toggleState.get());
        var body = editor.getBody();
        if (toggleState.get() === true) {
          show(editor, body);
        } else {
          hide(editor, body);
        }
      };
      var toggleVisualChars = function(editor, toggleState) {
        toggleState.set(!toggleState.get());
        var bookmark = editor.selection.getBookmark();
        applyVisualChars(editor, toggleState);
        editor.selection.moveToBookmark(bookmark);
      };
      var register$1 = function(editor, toggleState) {
        editor.addCommand("mceVisualChars", function() {
          toggleVisualChars(editor, toggleState);
        });
      };
      var isEnabledByDefault = function(editor) {
        return editor.getParam("visualchars_default_state", false);
      };
      var hasForcedRootBlock = function(editor) {
        return editor.getParam("forced_root_block") !== false;
      };
      var setup$1 = function(editor, toggleState) {
        editor.on("init", function() {
          applyVisualChars(editor, toggleState);
        });
      };
      var global = tinymce.util.Tools.resolve("tinymce.util.Delay");
      var setup = function(editor, toggleState) {
        var debouncedToggle = global.debounce(function() {
          toggle(editor);
        }, 300);
        if (hasForcedRootBlock(editor)) {
          editor.on("keydown", function(e) {
            if (toggleState.get() === true) {
              e.keyCode === 13 ? toggle(editor) : debouncedToggle();
            }
          });
        }
        editor.on("remove", debouncedToggle.stop);
      };
      var toggleActiveState = function(editor, enabledStated) {
        return function(api) {
          api.setActive(enabledStated.get());
          var editorEventCallback = function(e) {
            return api.setActive(e.state);
          };
          editor.on("VisualChars", editorEventCallback);
          return function() {
            return editor.off("VisualChars", editorEventCallback);
          };
        };
      };
      var register = function(editor, toggleState) {
        var onAction = function() {
          return editor.execCommand("mceVisualChars");
        };
        editor.ui.registry.addToggleButton("visualchars", {
          tooltip: "Show invisible characters",
          icon: "visualchars",
          onAction,
          onSetup: toggleActiveState(editor, toggleState)
        });
        editor.ui.registry.addToggleMenuItem("visualchars", {
          text: "Show invisible characters",
          icon: "visualchars",
          onAction,
          onSetup: toggleActiveState(editor, toggleState)
        });
      };
      function Plugin() {
        global$1.add("visualchars", function(editor) {
          var toggleState = Cell(isEnabledByDefault(editor));
          register$1(editor, toggleState);
          register(editor, toggleState);
          setup(editor, toggleState);
          setup$1(editor, toggleState);
          return get$2(toggleState);
        });
      }
      Plugin();
    })();
  }
});

// node_modules/tinymce/plugins/visualchars/index.js
var require_visualchars = __commonJS({
  "node_modules/tinymce/plugins/visualchars/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_visualchars
var tinymce_plugins_visualchars_default = require_visualchars();
export {
  tinymce_plugins_visualchars_default as default
};
//# sourceMappingURL=tinymce_plugins_visualchars.js.map
