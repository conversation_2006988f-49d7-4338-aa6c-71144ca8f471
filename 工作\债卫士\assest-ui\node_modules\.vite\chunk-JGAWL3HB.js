import {
  init_runtime_dom_esm_bundler
} from "./chunk-UOUDGCKJ.js";
import {
  initCustomFormatter,
  warn
} from "./chunk-MZ7ANLUJ.js";

// node_modules/vue/dist/vue.runtime.esm-bundler.js
init_runtime_dom_esm_bundler();
init_runtime_dom_esm_bundler();
function initDev() {
  {
    initCustomFormatter();
  }
}
if (true) {
  initDev();
}
var compile = () => {
  if (true) {
    warn(`Runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".`);
  }
};

export {
  compile
};
//# sourceMappingURL=chunk-JGAWL3HB.js.map
