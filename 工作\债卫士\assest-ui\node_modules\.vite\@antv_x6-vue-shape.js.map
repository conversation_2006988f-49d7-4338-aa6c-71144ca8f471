{"version": 3, "sources": ["../@antv/x6-vue-shape/src/node.ts", "../@antv/x6-vue-shape/src/registry.ts", "../@antv/x6-vue-shape/src/teleport.ts", "../@antv/x6-vue-shape/src/view.ts"], "sourcesContent": [null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEM,6BAEI,KAAgB;;AAgB1B,AAAA,UAAiB,WAAQ;AACvB,qBAAmB,QAAe;AAChC,UAAM,SAA8B;AACpC,UAAM,UAAU,OAAO;AAEvB,QAAI,QAAQ;AACV,aAAO,KACL,GAAG;QACD;UACE,SAAS;UACT,UAAU;;QAEZ;;WAGC;AACL,aAAO,KAAK;;AAGd,WAAO;;AAGT,YAAS,OAAmB;IAC1B,MAAM;IACN,QAAQ;IACR,OAAO;MACL,MAAM;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW;;MAEb,IAAI;QACF,UAAU;QACV,WAAW;;;IAGf,UAAU,UAAoB;AAC5B,UAAI,SAAS,UAAU,MAAM;AAC3B,cAAM,SAAS,SAAS;AACxB,YAAI,QAAQ;AACV,mBAAS,SAAS,UAAU;AAE5B,cAAI,QAAQ;AACZ,kBAAQ;iBACD;AACH,sBAAQ;gBACN,OAAO;gBACP,OAAO;gBACP,MAAM;;AAER;iBACG;AACH,sBAAQ;gBACN,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,OAAO;;AAET;;AAEA;;AAEJ,mBAAS,QAAQ,eAAU,MACzB,IACA;YACE,MAAI,OAAA,OAAA,EACF,UAAU,MACV,WAAW,QACR;aAGP,SAAS,SAAS;;;AAIxB,aAAO;;;AAIX,OAAK,SAAS,SAAS,aAAa,WAAU;GAhF/B,YAAA,YAAQ;;;;;;;;;;;;;;;ACZlB,IAAM,YAKT;AAEE,kBAAmB,QAAsB;AAC7C,QAAM,EAAE,OAAO,WAAW,YAAuB,QAAX,SAAM,OAAK,QAA3C,CAAA,SAAA,aAAA;AACN,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM;;AAElB,YAAU,SAAS;IACjB;;AAGF,QAAM,aACJ,OAAK,OAAA,OAAA,EAEH,SAAS,WAAW,eACjB,SAEL;;;;AC1BJ,IAAI,SAAS;AACb,IAAM,QAAQ,SAAiC;AAEzC,iBACJ,IACA,WACA,WACA,MACA,OAAY;AAEZ,MAAI,QAAQ;AACV,UAAM,MAAM,QACV,gBAAgB;MACd,QAAQ,MAAM,EAAE,UAAU,EAAE,IAAI,aAAoB,CAAC,EAAE,WAAW,EAAE,MAAM;MAC1E,SAAS,MAAO;QACd,SAAS,MAAM;QACf,UAAU,MAAM;;;;;AAOpB,oBAAqB,IAAU;AACnC,MAAI,QAAQ;AACV,WAAO,MAAM;;;AAIX,oBAAkB;AACtB,SAAO;;AAGH,uBAAqB;AACzB,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM;;AAElB,WAAS;AAET,SAAO,gBAAgB;IACrB,QAAK;AACH,aAAO,MACL,EACE,UACA,IACA,OAAO,KAAK,OAAO,IAAI,CAAC,OAAO,EAAE,MAAM;;;;;;AC3C3C,iCAA4B,SAAkB;EAGlD,wBAAqB;AACnB,WAAO,KAAK,aAAc,KAAK,UAAU;;EAG3C,cAAc,MAAY;AACxB,UAAM,MAAM,MAAM,cAAc;AAChC,WAAO,KAAK,aAAa,KAAK,aAAa,QAAQ,MAAK;AACtD,WAAK;;;EAIC,WAAQ;AAChB,WAAO,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK;;EAGnC,qBAAkB;AAC1B,SAAK;AACL,UAAM,OAAO,KAAK;AAClB,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,KAAK;AAEnB,QAAI,MAAM;AACR,YAAM,EAAE,cAAc,UAAU,KAAK;AACrC,UAAI,WAAW;AACb,YAAI,QAAQ;AACV,gBAAM,MAAM;AACZ,eAAK,KAAK,IAAI,IAAI;YAChB,IAAI;YACJ,OAAO,IAAM;AACX,qBAAO,GAAE,WAAW,EAAE,MAAM;;YAE9B,UAAO;AACL,qBAAO;gBACL,SAAS,MAAM;gBACf,UAAU,MAAM;;;;mBAIb,QAAQ;AACjB,cAAI,YAAY;AACd,oBAAQ,KAAK,YAAY,WAAW,MAAM,MAAM;iBAC3C;AACL,iBAAK,KAAK,UAAU;cAClB,SAAM;AACJ,uBAAO,EAAE,WAAW,EAAE,MAAM;;cAE9B,UAAO;AACL,uBAAO;kBACL,SAAS,MAAM;kBACf,UAAU,MAAM;;;;AAItB,iBAAK,GAAG,MAAM;;;;;;EAOd,sBAAmB;AAC3B,UAAM,OAAO,KAAK;AAClB,QAAI,KAAK,IAAI;AACX,gBAAU,KAAK,GAAG;AAClB,gBAAU,KAAK,GAAG;AAClB,WAAK,KAAK;;AAEZ,QAAI,MAAM;AACR,WAAK,YAAY;;AAEnB,WAAO;;EAGT,YAAY,GAAuB,GAAW,GAAS;AACrD,UAAM,SAAS,EAAE;AACjB,UAAM,UAAU,OAAO,QAAQ;AAC/B,QAAI,YAAY,SAAS;AACvB,YAAM,OAAO,OAAO,aAAa;AACjC,UACE,QAAQ,QACR;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,OACX;AACA;;;AAIJ,UAAM,YAAY,GAAG,GAAG;;EAG1B,UAAO;AACL,QAAI,YAAY;AACd,iBAAW,KAAK;;AAElB,SAAK;AACL,UAAM;AACN,WAAO;;;AAIX,AAAA,UAAiB,eAAY;AACd,gBAAA,SAAS;AAEtB,gBAAa,OAAO;IAClB,WAAW,CAAC,cAAA;IACZ,SAAS;MACP,WAAW,cAAA;;;AAIf,WAAS,SAAS,SAAS,kBAAkB,eAAc;GAV5C,gBAAA,gBAAY;", "names": []}