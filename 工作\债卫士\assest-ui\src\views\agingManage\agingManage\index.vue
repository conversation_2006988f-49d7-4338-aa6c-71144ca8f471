1<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      :class="showSearch ? 'h-auto' : 'h-50'"
      inline
      ref="queryRef"
      label-width="100px"
    >
      <el-form-item prop="caseIdsStr" label="案件ID">
        <el-input
          v-model="queryParams.caseIdsStr"
          style="width: 260px"
          placeholder="请输入案件ID"
        />
      </el-form-item>
      <el-form-item prop="clientName" label="借款人">
        <el-input
          v-model="queryParams.clientName"
          style="width: 260px"
          placeholder="请输入借款人"
        />
      </el-form-item>
      <el-form-item prop="entrustingPartyId" label="转让方">
        <el-select
          v-model="queryParams.entrustingPartyId"
          collapse-tags
          collapse-tags-tooltip
          multiple
          placeholder="请输入或选择转让方"
          clearable
          filterable
          :reserve-keyword="false"
          style="width: 260px"
        >
          <el-option
            v-for="item in ownerOption"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="entrustingCaseBatchNum" label="委案批次号">
        <el-select
          v-model="queryParams.entrustingBatchNum"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="请输入或选择委案批次号"
          clearable
          filterable
          :reserve-keyword="false"
          @visible-change="BatchList"
          style="width: 260px"
        >
          <el-option
            v-for="item in committeeBatchOption"
            :key="item.info"
            :label="item.info"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="residueTime" label="剩余时效">
        <div class="input-list" style="width: 260px">
          <NumberInput
            v-model="queryParams.residueTime1"
            :decimals="0"
            placeholder="请输入(天)"
          />
          <!-- <el-input v-model="queryParams.residueTime1" onkeyup="value=value.replace(/[^\d]/g, '')"
                        oninput="value=value.replace(/[^\d]/g, '')" placeholder="请输入(天)" class="input-item" /> -->
          <span class="transverse-line">-</span>
          <NumberInput
            v-model="queryParams.residueTime2"
            :decimals="0"
            placeholder="请输入(天)"
          />
          <!-- <el-input v-model="queryParams.residueTime2" onkeyup="value=value.replace(/[^\d]/g, '')"
                        oninput="value=value.replace(/[^\d]/g, '')" placeholder="请输入(天)" class="input-item" /> -->
        </div>
      </el-form-item>
      <el-form-item prop="remainingDue" label="剩余总额">
        <div class="input-list" style="width: 260px">
          <NumberInput
            v-model="queryParams.remainingDue1"
            :decimals="2"
            placeholder="请输入(金额)"
          />
          <!-- <el-input v-model="queryParams.remainingDue1" @input="(e) => handleInputRemainingDue(e, 1)"
                        placeholder="请输入(金额)" class="input-item" /> -->
          <span class="transverse-line">-</span>
          <NumberInput
            v-model="queryParams.remainingDue2"
            :decimals="2"
            placeholder="请输入(金额)"
          />
          <!-- <el-input v-model="queryParams.remainingDue2" @input="(e) => handleInputRemainingDue(e, 2)"
                        placeholder="请输入(金额)" class="input-item" /> -->
        </div>
      </el-form-item>
      <el-form-item prop="contactInformation" label="跟进方式">
        <el-input
          v-model="queryParams.contactInformation"
          style="width: 260px"
          placeholder="请输入跟进方式"
        />
      </el-form-item>
      <el-form-item prop="lastContactTime" label="跟进时间">
        <el-date-picker
          v-model="queryParams.lastContactTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          clearable
          unlink-panels
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 260px"
        />
      </el-form-item>
      <el-form-item prop="outsourcingTeam" label="机构名称">
        <el-select
          v-model="queryParams.outsourcingTeam"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="请选择机构名称"
          clearable
          filterable
          :reserve-keyword="false"
          style="width: 260px"
        >
          <el-option-group
            v-for="group in teamOptions"
            :key="group.label"
            :label="group.label"
          >
            <el-option
              v-for="item in group.children"
              :key="item.label"
              :label="item.label"
              :value="item.label"
            />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item prop="batchNum" label="资产批次号">
        <el-select
          v-model="queryParams.batchNum"
          placeholder="请输入或选择资产批次号"
          clearable
          filterable
          :reserve-keyword="false"
          style="width: 260px"
        >
          <el-option
            v-for="item in assetBatchOption"
            :key="item.info"
            :label="item.info"
            :value="item.info"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="query-area text-center">
      <el-button
        :loading="loading"
        icon="Refresh"
        @click="antiShake(resetQuery)"
        >重置</el-button
      >
      <el-button
        :loading="loading"
        type="primary"
        icon="Search"
        @click="antiShake(handleQuery)"
        >搜索</el-button
      >
    </div>
    <div class="operation-area">
      <el-button
        class="mb10"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        v-if="checkPermi(['agingManage:index:download'])"
        @click="handleDownload()"
        >导出</el-button
      >
      <el-button
        class="mb10"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        v-if="checkPermi(['agingManage:index:sendMessage'])"
        @click="handleSendMessage()"
        >发短信</el-button
      >
      <el-button
        class="mb10"
        :loading="loading"
        type="primary"
        :disabled="selectedArr.length == 0"
        v-if="checkPermi(['agingManage:index:AIInform'])"
        @click="handleAIInform()"
        >AI语音通知</el-button
      >
      <el-button
        class="mb10"
        :disabled="selectedArr.length == 0"
        :loading="loading"
        type="primary"
        v-if="checkPermi(['agingManage:index:downloadBatch'])"
        @click="handleDownloadBatch()"
        >批量导出时效催记</el-button
      >
      <SelectedAll
        ref="selectedAllRef"
        :dataList="dataList"
        :selectedArr="selectedArr"
        v-model:allQuery="allQuery"
      >
        <template #content>
          <span class="case-info">
            <span
              >剩余债权总额(元)：
              <i class="danger">{{
                proxy.numFilter(dataInfo.totalRemainingClaims || 0)
              }}</i>
            </span>
            <span
              >案件数量(个)：<i class="danger">{{ dataInfo.caseNum }}</i></span
            >
          </span>
        </template>
      </SelectedAll>
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      ></right-toolbar>
    </div>
    <el-tabs v-model="activetab" @tab-change="antiShake(handleQuery)">
      <el-tab-pane
        v-for="item in tabs"
        :key="item.code"
        :label="item.info"
        :name="item.code"
      />
    </el-tabs>
    <div class="table-area">
      <el-table
        :data="dataList"
        ref="multipleTableRef"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        v-loading="loading"
      >
        <el-table-column
          type="selection"
          width="44px"
          :selectable="selectable"
          align="right"
        />
        <el-table-column
          v-if="columns[0].visible"
          align="center"
          prop="caseId"
          sortable
          label="案件ID"
          width="90"
        />
        <el-table-column
          v-if="columns[1].visible"
          align="center"
          prop="residueTime"
          label="剩余时效(天)"
          sortable="residueTime"
          width="130"
        />
        <el-table-column
          v-if="columns[2].visible"
          align="center"
          prop="clientName"
          sortable
          label="借款人"
          width="110"
        />
        <el-table-column
          v-if="columns[3].visible"
          align="center"
          prop="entrustingParty"
          sortable
          label="转让方"
          width="120"
        />
        <el-table-column
          v-if="columns[4].visible"
          align="center"
          prop="batchNum"
          sortable
          label="资产批次号"
          width="200"
        />
        <el-table-column
          v-if="columns[5].visible"
          align="center"
          prop="entrustingCaseBatchNum"
          sortable
          label="委案批次号"
          width="180"
        />
        <el-table-column
          v-if="columns[6].visible"
          align="center"
          prop="remainingDue"
          sortable
          label="剩余应还债权总额"
          width="140"
        >
          <template #default="{ row }">
            <span>{{ proxy.$toThousands(row.remainingDue) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[7].visible"
          align="center"
          prop="outsourcingTeam"
          sortable
          label="机构名称"
          width="110"
        />
        <el-table-column
          v-if="columns[8].visible"
          align="center"
          prop="caseState"
          sortable
          label="案件状态"
          width="90"
          :formatter="stutasFor"
        />
        <el-table-column
          v-if="columns[9].visible"
          align="center"
          prop="lastContactTime"
          sortable
          label="最近一次跟进时间"
          width="160"
        />
        <el-table-column
          v-if="columns[10].visible"
          align="center"
          prop="contactInformation"
          sortable
          label="最近一次跟进方式"
          width="140"
        />
        <el-table-column
          v-if="columns[11].visible"
          align="center"
          prop="workFollowContent"
          sortable
          label="最近一次跟进内容"
          width="140"
        >
          <template #default="{ row }">
            <Tooltip :content="row.workFollowContent" :length="8" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="110px" fixed="right">
          <template #default="{ row }">
            <div>
              <el-button
                type="text"
                v-if="checkPermi(['agingManage:index:check'])"
                @click="check(row)"
                >查看详情</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <AIInform ref="AIInformRef" />
  </div>
</template>
<script setup name="AgingManage">
import AIInform from "./dialog/AIInform";
import { checkPermi } from "@/utils/permission";
import { agingManageList, getCaseCount } from "@/api/agingManage/agingManage";
import {
  getOwners,
  getBatchNums,
  getEntrustingCaseBatchNums,
} from "@/api/assets/casemanage";
import { getTeamTree } from "@/api/team/team";
import { caseStatusEnum } from "@/utils/enum";
import { isArray } from "@/utils/estimate";
const rangFields = ["lastContactTime"]; //范围字段
const { proxy } = getCurrentInstance();
const router = useRouter();
const store = useStore();
const tabs = ref([
  { code: "0", info: "近1年" },
  { code: "1", info: "近2年" },
  { code: "2", info: "近3年" },
  { code: "all", info: "全部" },
]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseIdsStr: undefined,
    clientName: undefined,
    entrustingParty: undefined,
    entrustingCaseBatchNum: undefined,
    residueTime1: undefined,
    residueTime2: undefined,
    remainingDue1: undefined,
    remainingDue2: undefined,
    contactInformation: undefined,
    lastContactTime: undefined,
    outsourcingTeam: undefined,
  },
  dataInfo: {
    caseNum: 0, // 案件数量
    totalRemainingClaims: 0, // 剩余债权总额
  },
  dataList: [],
  selectedArr: [],
  activetab: "all",
  total: 0,
  loading: false,
  allQuery: false,
  showSearch: false,
});
const options = reactive({
  ownerOption: [],
  teamOptions: [],
  assetBatchOption: [],
  committeeBatchOption: [],
});
const columns = ref([
  { key: 0, visible: true, label: "案件ID" },
  { key: 1, visible: true, label: "剩余时效(天)" },
  { key: 2, visible: true, label: "借款人" },
  { key: 3, visible: true, label: "转让方" },
  { key: 4, visible: true, label: "资产批次号" },
  { key: 5, visible: true, label: "委案批次号" },
  { key: 6, visible: true, label: "剩余应还债权总额" },
  { key: 7, visible: true, label: "机构名称" },
  { key: 8, visible: true, label: "案件状态" },
  { key: 9, visible: true, label: "最近一次跟进时间" },
  { key: 10, visible: true, label: "最近一次跟进方式" },
  { key: 11, visible: true, label: "最近一次跟进内容" },
]);
const {
  queryParams,
  dataList,
  total,
  loading,
  selectedArr,
  activetab,
  dataInfo,
  allQuery,
  showSearch,
} = toRefs(data);
const { assetBatchOption, ownerOption, committeeBatchOption, teamOptions } =
  toRefs(options);
getList();
function getList() {
  loading.value = true;
  let reqForm = JSON.parse(JSON.stringify(queryParams.value));
  reqForm = proxy.addFieldsRange(reqForm, rangFields);
  reqForm.recentYears = activetab.value == "all" ? "" : activetab.value;
  reqForm.allQuery = allQuery.value;
  for (const key in reqForm) {
    if (isArray(reqForm[key])) {
      reqForm[key] = reqForm[key].join(",");
    }
  }
  agingManageList(reqForm)
    .then((res) => {
      if (res.code == 200) {
        total.value = res.total;
        dataList.value = res.rows;
      }
    })
    .finally(() => (loading.value = false));
}
function check(row) {
  const path = `/agingManage/agingDetails`;
  const query = { caseId: row.caseId };
  router.push({ path, query });
}

// 案件统计
getCount();
function getCount() {
  return new Promise((resolve, reject) => {
    let reqForm = {};
    if (allQuery.value) {
      reqForm = JSON.parse(JSON.stringify(queryParams.value));
      reqForm.recentYears = activetab.value == "all" ? "" : activetab.value;
      delete reqForm.pageNum;
      delete reqForm.pageSize;
      for (const key in reqForm) {
        if (isArray(reqForm[key])) {
          reqForm[key] = reqForm[key].join(",");
        }
      }
    } else {
      reqForm.strCaseId = selectedArr.value
        .map((item) => item.caseId)
        .join(",");
    }
    reqForm.allQuery = allQuery.value;
    getCaseCount(reqForm)
      .then((res) => {
        if (res.code == 200) {
          dataInfo.value = res.data;
        }
      })
      .finally(() => {
        resolve();
        loading.value = false;
      });
  });
}

// 选择列表数据
function handleSelectionChange(selection) {
  selectedArr.value = selection;
}

// 发短信
function handleSendMessage(row) {
  const reqForm = JSON.parse(JSON.stringify(queryParams.value));
  for (const key in reqForm) {
    if (isArray(reqForm[key])) {
      reqForm[key] = reqForm[key].join(",");
    }
  }
  let req = {
    type: 2,
    relation: "本人",
    count: row ? 1 : dataInfo.value.caseNum,
    queryParams: { ...reqForm, allQuery: allQuery.value },
    caseIds: row ? [row.id] : selectedArr.value.map((item) => item.caseId),
    caseId: row ? [row.id] : selectedArr.value.map((item) => item.caseId),
  };
  if (row) {
    req.queryParams.allQuery = false;
  }
  localStorage.setItem(`/send-note`, JSON.stringify(req));
  store.dispatch("tagsView/delCachedView", { name: "SendNote" });
  router.push(`/note/send-note/operate`);
}
// AI语音通知
function handleAIInform() {
  proxy.$refs["AIInformRef"].openDialog(getQueryParams());
}
// 批量导出时效催记
function handleDownloadBatch() {
  proxy.downloadforjson(
    "/caseManage/time/exportTimeManageAndDetails",
    getQueryParams(),
    `时效催记列表_${+new Date()}.xlsx`
  );
}

// 导出
function handleDownload() {
  proxy.downloadforjson(
    "/caseManage/time/exportTimeManage",
    getQueryParams(),
    `时效管理列表_${+new Date()}.xlsx`
  );
}

function getQueryParams() {
  let reqForm = {};
  if (allQuery.value) {
    reqForm = JSON.parse(JSON.stringify(queryParams.value));
    reqForm.recentYears = activetab.value == "all" ? "" : activetab.value;
    for (const key in reqForm) {
      if (isArray(reqForm[key])) {
        reqForm[key] = reqForm[key].join(",");
      }
    }
    delete reqForm.pageNum;
    delete reqForm.pageSize;
  }
  if (!allQuery.value && selectedArr.value.length > 0) {
    reqForm.caseIdsStr = selectedArr.value.map((item) => item.caseId).join(",");
    reqForm.pageNum = queryParams.value.pageNum;
    reqForm.pageSize = queryParams.value.pageSize;
  }
  reqForm.allQuery = allQuery.value;
  return reqForm;
}
// 重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseIdsStr: undefined,
    clientName: undefined,
    entrustingParty: undefined,
    entrustingCaseBatchNum: undefined,
    residueTime1: undefined,
    residueTime2: undefined,
    remainingDue1: undefined,
    remainingDue2: undefined,
    contactInformation: undefined,
    lastContactTime: undefined,
    outsourcingTeam: undefined,
  };
  getList();
}
// 搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

// 排序
function handleSortChange({ prop, order }) {
  const orderByObj = { residueTime: 1 };
  queryParams.value.orderBy = orderByObj[prop];
  queryParams.value.sortOrder = proxy.orderEnum[order];
  getList();
}

function handleInputRemainingDue(val, filedType) {
  if (val && /[^\d]/g.test(val)) {
    queryParams.value[`remainingDue${filedType}`] = val.replace(
      /[^(\d.)]/g,
      ""
    );
  }
  if (val && val.includes(".")) {
    const valIndex = val.indexOf(".");
    const newVal = val.slice(valIndex, val.length);
    if (newVal.length > 5) {
      queryParams.value[`remainingDue${filedType}`] = val.slice(
        0,
        valIndex + 5
      );
    }
  }
}

// 获取下拉
getOptions();
function getOptions() {
  // 获取转让方
  getOwners().then((res) => {
    ownerOption.value = res.data;
  });
  //获取资产批次号
  getBatchNums().then((res) => {
    assetBatchOption.value = res.data;
  });
  // 获取委案批次号
  getEntrustingCaseBatchNums().then((res) => {
    committeeBatchOption.value = res.data;
  });
  // 获取机构
  getTeamTree().then((res) => {
    teamOptions.value = res.data;
  });
}
function selectable() {
  return !allQuery.value;
}

function stutasFor(row) {
  return caseStatusEnum[row.caseState];
}
watch(
  () => selectedArr.value,
  () => {
    if (!loading.value) {
      loading.value = true;
      nextTick(() => {
        getCount().finally(() => (loading.value = false));
      });
    }
  },
  { deep: true }
);
</script>
<style lang="scss" scoped>
.input-list {
  display: flex;

  .input-item {
    width: 118px;
  }

  .transverse-line {
    margin: 0 10px;
  }
}

.case-info {
  font-size: 14px;

  span {
    margin-left: 10px;
  }
}

.operation-area {
  position: relative;
}

:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}
</style>
<style>
.top-right-btn {
  top: 0;
}
</style>