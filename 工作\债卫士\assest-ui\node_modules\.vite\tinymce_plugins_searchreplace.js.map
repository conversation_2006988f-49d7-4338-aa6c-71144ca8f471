{"version": 3, "sources": ["../tinymce/plugins/searchreplace/plugin.js", "../tinymce/plugins/searchreplace/index.js", "dep:tinymce_plugins_searchreplace"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType$1 = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var isString = isType$1('string');\n    var isArray = isType$1('array');\n    var isBoolean = isSimpleType('boolean');\n    var isNumber = isSimpleType('number');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var punctuationStr = '[!-#%-*,-\\\\/:;?@\\\\[-\\\\]_{}\\xA1\\xAB\\xB7\\xBB\\xBF;\\xB7\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1361-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u3008\\u3009\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30\\u2E31\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uff3f\\uFF5B\\uFF5D\\uFF5F-\\uFF65]';\n\n    var punctuation$1 = constant(punctuationStr);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var punctuation = punctuation$1;\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var nativeSlice = Array.prototype.slice;\n    var nativePush = Array.prototype.push;\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var eachr = function (xs, f) {\n      for (var i = xs.length - 1; i >= 0; i--) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var groupBy = function (xs, f) {\n      if (xs.length === 0) {\n        return [];\n      } else {\n        var wasType = f(xs[0]);\n        var r = [];\n        var group = [];\n        for (var i = 0, len = xs.length; i < len; i++) {\n          var x = xs[i];\n          var type = f(x);\n          if (type !== wasType) {\n            r.push(group);\n            group = [];\n          }\n          wasType = type;\n          group.push(x);\n        }\n        if (group.length !== 0) {\n          r.push(group);\n        }\n        return r;\n      }\n    };\n    var foldl = function (xs, f, acc) {\n      each(xs, function (x, i) {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    var flatten = function (xs) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    var bind = function (xs, f) {\n      return flatten(map(xs, f));\n    };\n    var sort = function (xs, comparator) {\n      var copy = nativeSlice.call(xs, 0);\n      copy.sort(comparator);\n      return copy;\n    };\n\n    var hasOwnProperty = Object.hasOwnProperty;\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var DOCUMENT = 9;\n    var DOCUMENT_FRAGMENT = 11;\n    var ELEMENT = 1;\n    var TEXT = 3;\n\n    var type = function (element) {\n      return element.dom.nodeType;\n    };\n    var isType = function (t) {\n      return function (element) {\n        return type(element) === t;\n      };\n    };\n    var isText$1 = isType(TEXT);\n\n    var rawSet = function (dom, key, value) {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    var set = function (element, key, value) {\n      rawSet(element.dom, key, value);\n    };\n\n    var compareDocumentPosition = function (a, b, match) {\n      return (a.compareDocumentPosition(b) & match) !== 0;\n    };\n    var documentPositionPreceding = function (a, b) {\n      return compareDocumentPosition(a, b, Node.DOCUMENT_POSITION_PRECEDING);\n    };\n\n    var fromHtml = function (html, scope) {\n      var doc = scope || document;\n      var div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        console.error('HTML does not have a single root node', html);\n        throw new Error('HTML must have a single root node');\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    var fromTag = function (tag, scope) {\n      var doc = scope || document;\n      var node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    var fromText = function (text, scope) {\n      var doc = scope || document;\n      var node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    var fromDom = function (node) {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    var fromPoint = function (docElm, x, y) {\n      return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    };\n    var SugarElement = {\n      fromHtml: fromHtml,\n      fromTag: fromTag,\n      fromText: fromText,\n      fromDom: fromDom,\n      fromPoint: fromPoint\n    };\n\n    var bypassSelector = function (dom) {\n      return dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    };\n    var all = function (selector, scope) {\n      var base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n\n    var parent = function (element) {\n      return Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    };\n    var children = function (element) {\n      return map(element.dom.childNodes, SugarElement.fromDom);\n    };\n    var spot = function (element, offset) {\n      return {\n        element: element,\n        offset: offset\n      };\n    };\n    var leaf = function (element, offset) {\n      var cs = children(element);\n      return cs.length > 0 && offset < cs.length ? spot(cs[offset], 0) : spot(element, offset);\n    };\n\n    var before = function (marker, element) {\n      var parent$1 = parent(marker);\n      parent$1.each(function (v) {\n        v.dom.insertBefore(element.dom, marker.dom);\n      });\n    };\n    var append = function (parent, element) {\n      parent.dom.appendChild(element.dom);\n    };\n    var wrap = function (element, wrapper) {\n      before(element, wrapper);\n      append(wrapper, element);\n    };\n\n    var NodeValue = function (is, name) {\n      var get = function (element) {\n        if (!is(element)) {\n          throw new Error('Can only get ' + name + ' value of a ' + name + ' node');\n        }\n        return getOption(element).getOr('');\n      };\n      var getOption = function (element) {\n        return is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();\n      };\n      var set = function (element, value) {\n        if (!is(element)) {\n          throw new Error('Can only set raw ' + name + ' value of a ' + name + ' node');\n        }\n        element.dom.nodeValue = value;\n      };\n      return {\n        get: get,\n        getOption: getOption,\n        set: set\n      };\n    };\n\n    var api = NodeValue(isText$1, 'text');\n    var get$1 = function (element) {\n      return api.get(element);\n    };\n\n    var descendants = function (scope, selector) {\n      return all(selector, scope);\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    var isSimpleBoundary = function (dom, node) {\n      return dom.isBlock(node) || has(dom.schema.getShortEndedElements(), node.nodeName);\n    };\n    var isContentEditableFalse = function (dom, node) {\n      return dom.getContentEditable(node) === 'false';\n    };\n    var isContentEditableTrueInCef = function (dom, node) {\n      return dom.getContentEditable(node) === 'true' && dom.getContentEditableParent(node.parentNode) === 'false';\n    };\n    var isHidden = function (dom, node) {\n      return !dom.isBlock(node) && has(dom.schema.getWhiteSpaceElements(), node.nodeName);\n    };\n    var isBoundary = function (dom, node) {\n      return isSimpleBoundary(dom, node) || isContentEditableFalse(dom, node) || isHidden(dom, node) || isContentEditableTrueInCef(dom, node);\n    };\n    var isText = function (node) {\n      return node.nodeType === 3;\n    };\n    var nuSection = function () {\n      return {\n        sOffset: 0,\n        fOffset: 0,\n        elements: []\n      };\n    };\n    var toLeaf = function (node, offset) {\n      return leaf(SugarElement.fromDom(node), offset);\n    };\n    var walk = function (dom, walkerFn, startNode, callbacks, endNode, skipStart) {\n      if (skipStart === void 0) {\n        skipStart = true;\n      }\n      var next = skipStart ? walkerFn(false) : startNode;\n      while (next) {\n        var isCefNode = isContentEditableFalse(dom, next);\n        if (isCefNode || isHidden(dom, next)) {\n          var stopWalking = isCefNode ? callbacks.cef(next) : callbacks.boundary(next);\n          if (stopWalking) {\n            break;\n          } else {\n            next = walkerFn(true);\n            continue;\n          }\n        } else if (isSimpleBoundary(dom, next)) {\n          if (callbacks.boundary(next)) {\n            break;\n          }\n        } else if (isText(next)) {\n          callbacks.text(next);\n        }\n        if (next === endNode) {\n          break;\n        } else {\n          next = walkerFn(false);\n        }\n      }\n    };\n    var collectTextToBoundary = function (dom, section, node, rootNode, forwards) {\n      if (isBoundary(dom, node)) {\n        return;\n      }\n      var rootBlock = dom.getParent(rootNode, dom.isBlock);\n      var walker = new global(node, rootBlock);\n      var walkerFn = forwards ? walker.next.bind(walker) : walker.prev.bind(walker);\n      walk(dom, walkerFn, node, {\n        boundary: always,\n        cef: always,\n        text: function (next) {\n          if (forwards) {\n            section.fOffset += next.length;\n          } else {\n            section.sOffset += next.length;\n          }\n          section.elements.push(SugarElement.fromDom(next));\n        }\n      });\n    };\n    var collect = function (dom, rootNode, startNode, endNode, callbacks, skipStart) {\n      if (skipStart === void 0) {\n        skipStart = true;\n      }\n      var walker = new global(startNode, rootNode);\n      var sections = [];\n      var current = nuSection();\n      collectTextToBoundary(dom, current, startNode, rootNode, false);\n      var finishSection = function () {\n        if (current.elements.length > 0) {\n          sections.push(current);\n          current = nuSection();\n        }\n        return false;\n      };\n      walk(dom, walker.next.bind(walker), startNode, {\n        boundary: finishSection,\n        cef: function (node) {\n          finishSection();\n          if (callbacks) {\n            sections.push.apply(sections, callbacks.cef(node));\n          }\n          return false;\n        },\n        text: function (next) {\n          current.elements.push(SugarElement.fromDom(next));\n          if (callbacks) {\n            callbacks.text(next, current);\n          }\n        }\n      }, endNode, skipStart);\n      if (endNode) {\n        collectTextToBoundary(dom, current, endNode, rootNode, true);\n      }\n      finishSection();\n      return sections;\n    };\n    var collectRangeSections = function (dom, rng) {\n      var start = toLeaf(rng.startContainer, rng.startOffset);\n      var startNode = start.element.dom;\n      var end = toLeaf(rng.endContainer, rng.endOffset);\n      var endNode = end.element.dom;\n      return collect(dom, rng.commonAncestorContainer, startNode, endNode, {\n        text: function (node, section) {\n          if (node === endNode) {\n            section.fOffset += node.length - end.offset;\n          } else if (node === startNode) {\n            section.sOffset += start.offset;\n          }\n        },\n        cef: function (node) {\n          var sections = bind(descendants(SugarElement.fromDom(node), '*[contenteditable=true]'), function (e) {\n            var ceTrueNode = e.dom;\n            return collect(dom, ceTrueNode, ceTrueNode);\n          });\n          return sort(sections, function (a, b) {\n            return documentPositionPreceding(a.elements[0].dom, b.elements[0].dom) ? 1 : -1;\n          });\n        }\n      }, false);\n    };\n    var fromRng = function (dom, rng) {\n      return rng.collapsed ? [] : collectRangeSections(dom, rng);\n    };\n    var fromNode = function (dom, node) {\n      var rng = dom.createRng();\n      rng.selectNode(node);\n      return fromRng(dom, rng);\n    };\n    var fromNodes = function (dom, nodes) {\n      return bind(nodes, function (node) {\n        return fromNode(dom, node);\n      });\n    };\n\n    var find$2 = function (text, pattern, start, finish) {\n      if (start === void 0) {\n        start = 0;\n      }\n      if (finish === void 0) {\n        finish = text.length;\n      }\n      var regex = pattern.regex;\n      regex.lastIndex = start;\n      var results = [];\n      var match;\n      while (match = regex.exec(text)) {\n        var matchedText = match[pattern.matchIndex];\n        var matchStart = match.index + match[0].indexOf(matchedText);\n        var matchFinish = matchStart + matchedText.length;\n        if (matchFinish > finish) {\n          break;\n        }\n        results.push({\n          start: matchStart,\n          finish: matchFinish\n        });\n        regex.lastIndex = matchFinish;\n      }\n      return results;\n    };\n    var extract = function (elements, matches) {\n      var nodePositions = foldl(elements, function (acc, element) {\n        var content = get$1(element);\n        var start = acc.last;\n        var finish = start + content.length;\n        var positions = bind(matches, function (match, matchIdx) {\n          if (match.start < finish && match.finish > start) {\n            return [{\n                element: element,\n                start: Math.max(start, match.start) - start,\n                finish: Math.min(finish, match.finish) - start,\n                matchId: matchIdx\n              }];\n          } else {\n            return [];\n          }\n        });\n        return {\n          results: acc.results.concat(positions),\n          last: finish\n        };\n      }, {\n        results: [],\n        last: 0\n      }).results;\n      return groupBy(nodePositions, function (position) {\n        return position.matchId;\n      });\n    };\n\n    var find$1 = function (pattern, sections) {\n      return bind(sections, function (section) {\n        var elements = section.elements;\n        var content = map(elements, get$1).join('');\n        var positions = find$2(content, pattern, section.sOffset, content.length - section.fOffset);\n        return extract(elements, positions);\n      });\n    };\n    var mark = function (matches, replacementNode) {\n      eachr(matches, function (match, idx) {\n        eachr(match, function (pos) {\n          var wrapper = SugarElement.fromDom(replacementNode.cloneNode(false));\n          set(wrapper, 'data-mce-index', idx);\n          var textNode = pos.element.dom;\n          if (textNode.length === pos.finish && pos.start === 0) {\n            wrap(pos.element, wrapper);\n          } else {\n            if (textNode.length !== pos.finish) {\n              textNode.splitText(pos.finish);\n            }\n            var matchNode = textNode.splitText(pos.start);\n            wrap(SugarElement.fromDom(matchNode), wrapper);\n          }\n        });\n      });\n    };\n    var findAndMark = function (dom, pattern, node, replacementNode) {\n      var textSections = fromNode(dom, node);\n      var matches = find$1(pattern, textSections);\n      mark(matches, replacementNode);\n      return matches.length;\n    };\n    var findAndMarkInSelection = function (dom, pattern, selection, replacementNode) {\n      var bookmark = selection.getBookmark();\n      var nodes = dom.select('td[data-mce-selected],th[data-mce-selected]');\n      var textSections = nodes.length > 0 ? fromNodes(dom, nodes) : fromRng(dom, selection.getRng());\n      var matches = find$1(pattern, textSections);\n      mark(matches, replacementNode);\n      selection.moveToBookmark(bookmark);\n      return matches.length;\n    };\n\n    var getElmIndex = function (elm) {\n      var value = elm.getAttribute('data-mce-index');\n      if (typeof value === 'number') {\n        return '' + value;\n      }\n      return value;\n    };\n    var markAllMatches = function (editor, currentSearchState, pattern, inSelection) {\n      var marker = editor.dom.create('span', { 'data-mce-bogus': 1 });\n      marker.className = 'mce-match-marker';\n      var node = editor.getBody();\n      done(editor, currentSearchState, false);\n      if (inSelection) {\n        return findAndMarkInSelection(editor.dom, pattern, editor.selection, marker);\n      } else {\n        return findAndMark(editor.dom, pattern, node, marker);\n      }\n    };\n    var unwrap = function (node) {\n      var parentNode = node.parentNode;\n      if (node.firstChild) {\n        parentNode.insertBefore(node.firstChild, node);\n      }\n      node.parentNode.removeChild(node);\n    };\n    var findSpansByIndex = function (editor, index) {\n      var spans = [];\n      var nodes = global$1.toArray(editor.getBody().getElementsByTagName('span'));\n      if (nodes.length) {\n        for (var i = 0; i < nodes.length; i++) {\n          var nodeIndex = getElmIndex(nodes[i]);\n          if (nodeIndex === null || !nodeIndex.length) {\n            continue;\n          }\n          if (nodeIndex === index.toString()) {\n            spans.push(nodes[i]);\n          }\n        }\n      }\n      return spans;\n    };\n    var moveSelection = function (editor, currentSearchState, forward) {\n      var searchState = currentSearchState.get();\n      var testIndex = searchState.index;\n      var dom = editor.dom;\n      forward = forward !== false;\n      if (forward) {\n        if (testIndex + 1 === searchState.count) {\n          testIndex = 0;\n        } else {\n          testIndex++;\n        }\n      } else {\n        if (testIndex - 1 === -1) {\n          testIndex = searchState.count - 1;\n        } else {\n          testIndex--;\n        }\n      }\n      dom.removeClass(findSpansByIndex(editor, searchState.index), 'mce-match-marker-selected');\n      var spans = findSpansByIndex(editor, testIndex);\n      if (spans.length) {\n        dom.addClass(findSpansByIndex(editor, testIndex), 'mce-match-marker-selected');\n        editor.selection.scrollIntoView(spans[0]);\n        return testIndex;\n      }\n      return -1;\n    };\n    var removeNode = function (dom, node) {\n      var parent = node.parentNode;\n      dom.remove(node);\n      if (dom.isEmpty(parent)) {\n        dom.remove(parent);\n      }\n    };\n    var escapeSearchText = function (text, wholeWord) {\n      var escapedText = text.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&').replace(/\\s/g, '[^\\\\S\\\\r\\\\n\\\\uFEFF]');\n      var wordRegex = '(' + escapedText + ')';\n      return wholeWord ? '(?:^|\\\\s|' + punctuation() + ')' + wordRegex + ('(?=$|\\\\s|' + punctuation() + ')') : wordRegex;\n    };\n    var find = function (editor, currentSearchState, text, matchCase, wholeWord, inSelection) {\n      var selection = editor.selection;\n      var escapedText = escapeSearchText(text, wholeWord);\n      var isForwardSelection = selection.isForward();\n      var pattern = {\n        regex: new RegExp(escapedText, matchCase ? 'g' : 'gi'),\n        matchIndex: 1\n      };\n      var count = markAllMatches(editor, currentSearchState, pattern, inSelection);\n      if (global$2.browser.isSafari()) {\n        selection.setRng(selection.getRng(), isForwardSelection);\n      }\n      if (count) {\n        var newIndex = moveSelection(editor, currentSearchState, true);\n        currentSearchState.set({\n          index: newIndex,\n          count: count,\n          text: text,\n          matchCase: matchCase,\n          wholeWord: wholeWord,\n          inSelection: inSelection\n        });\n      }\n      return count;\n    };\n    var next = function (editor, currentSearchState) {\n      var index = moveSelection(editor, currentSearchState, true);\n      currentSearchState.set(__assign(__assign({}, currentSearchState.get()), { index: index }));\n    };\n    var prev = function (editor, currentSearchState) {\n      var index = moveSelection(editor, currentSearchState, false);\n      currentSearchState.set(__assign(__assign({}, currentSearchState.get()), { index: index }));\n    };\n    var isMatchSpan = function (node) {\n      var matchIndex = getElmIndex(node);\n      return matchIndex !== null && matchIndex.length > 0;\n    };\n    var replace = function (editor, currentSearchState, text, forward, all) {\n      var searchState = currentSearchState.get();\n      var currentIndex = searchState.index;\n      var currentMatchIndex, nextIndex = currentIndex;\n      forward = forward !== false;\n      var node = editor.getBody();\n      var nodes = global$1.grep(global$1.toArray(node.getElementsByTagName('span')), isMatchSpan);\n      for (var i = 0; i < nodes.length; i++) {\n        var nodeIndex = getElmIndex(nodes[i]);\n        var matchIndex = currentMatchIndex = parseInt(nodeIndex, 10);\n        if (all || matchIndex === searchState.index) {\n          if (text.length) {\n            nodes[i].firstChild.nodeValue = text;\n            unwrap(nodes[i]);\n          } else {\n            removeNode(editor.dom, nodes[i]);\n          }\n          while (nodes[++i]) {\n            matchIndex = parseInt(getElmIndex(nodes[i]), 10);\n            if (matchIndex === currentMatchIndex) {\n              removeNode(editor.dom, nodes[i]);\n            } else {\n              i--;\n              break;\n            }\n          }\n          if (forward) {\n            nextIndex--;\n          }\n        } else if (currentMatchIndex > currentIndex) {\n          nodes[i].setAttribute('data-mce-index', String(currentMatchIndex - 1));\n        }\n      }\n      currentSearchState.set(__assign(__assign({}, searchState), {\n        count: all ? 0 : searchState.count - 1,\n        index: nextIndex\n      }));\n      if (forward) {\n        next(editor, currentSearchState);\n      } else {\n        prev(editor, currentSearchState);\n      }\n      return !all && currentSearchState.get().count > 0;\n    };\n    var done = function (editor, currentSearchState, keepEditorSelection) {\n      var startContainer, endContainer;\n      var searchState = currentSearchState.get();\n      var nodes = global$1.toArray(editor.getBody().getElementsByTagName('span'));\n      for (var i = 0; i < nodes.length; i++) {\n        var nodeIndex = getElmIndex(nodes[i]);\n        if (nodeIndex !== null && nodeIndex.length) {\n          if (nodeIndex === searchState.index.toString()) {\n            if (!startContainer) {\n              startContainer = nodes[i].firstChild;\n            }\n            endContainer = nodes[i].firstChild;\n          }\n          unwrap(nodes[i]);\n        }\n      }\n      currentSearchState.set(__assign(__assign({}, searchState), {\n        index: -1,\n        count: 0,\n        text: ''\n      }));\n      if (startContainer && endContainer) {\n        var rng = editor.dom.createRng();\n        rng.setStart(startContainer, 0);\n        rng.setEnd(endContainer, endContainer.data.length);\n        if (keepEditorSelection !== false) {\n          editor.selection.setRng(rng);\n        }\n        return rng;\n      }\n    };\n    var hasNext = function (editor, currentSearchState) {\n      return currentSearchState.get().count > 1;\n    };\n    var hasPrev = function (editor, currentSearchState) {\n      return currentSearchState.get().count > 1;\n    };\n\n    var get = function (editor, currentState) {\n      var done$1 = function (keepEditorSelection) {\n        return done(editor, currentState, keepEditorSelection);\n      };\n      var find$1 = function (text, matchCase, wholeWord, inSelection) {\n        if (inSelection === void 0) {\n          inSelection = false;\n        }\n        return find(editor, currentState, text, matchCase, wholeWord, inSelection);\n      };\n      var next$1 = function () {\n        return next(editor, currentState);\n      };\n      var prev$1 = function () {\n        return prev(editor, currentState);\n      };\n      var replace$1 = function (text, forward, all) {\n        return replace(editor, currentState, text, forward, all);\n      };\n      return {\n        done: done$1,\n        find: find$1,\n        next: next$1,\n        prev: prev$1,\n        replace: replace$1\n      };\n    };\n\n    var singleton = function (doRevoke) {\n      var subject = Cell(Optional.none());\n      var revoke = function () {\n        return subject.get().each(doRevoke);\n      };\n      var clear = function () {\n        revoke();\n        subject.set(Optional.none());\n      };\n      var isSet = function () {\n        return subject.get().isSome();\n      };\n      var get = function () {\n        return subject.get();\n      };\n      var set = function (s) {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear: clear,\n        isSet: isSet,\n        get: get,\n        set: set\n      };\n    };\n    var value = function () {\n      var subject = singleton(noop);\n      var on = function (f) {\n        return subject.get().each(f);\n      };\n      return __assign(__assign({}, subject), { on: on });\n    };\n\n    var open = function (editor, currentSearchState) {\n      var dialogApi = value();\n      editor.undoManager.add();\n      var selectedText = global$1.trim(editor.selection.getContent({ format: 'text' }));\n      var updateButtonStates = function (api) {\n        var updateNext = hasNext(editor, currentSearchState) ? api.enable : api.disable;\n        updateNext('next');\n        var updatePrev = hasPrev(editor, currentSearchState) ? api.enable : api.disable;\n        updatePrev('prev');\n      };\n      var updateSearchState = function (api) {\n        var data = api.getData();\n        var current = currentSearchState.get();\n        currentSearchState.set(__assign(__assign({}, current), {\n          matchCase: data.matchcase,\n          wholeWord: data.wholewords,\n          inSelection: data.inselection\n        }));\n      };\n      var disableAll = function (api, disable) {\n        var buttons = [\n          'replace',\n          'replaceall',\n          'prev',\n          'next'\n        ];\n        var toggle = disable ? api.disable : api.enable;\n        each(buttons, toggle);\n      };\n      var notFoundAlert = function (api) {\n        editor.windowManager.alert('Could not find the specified string.', function () {\n          api.focus('findtext');\n        });\n      };\n      var focusButtonIfRequired = function (api, name) {\n        if (global$2.browser.isSafari() && global$2.deviceType.isTouch() && (name === 'find' || name === 'replace' || name === 'replaceall')) {\n          api.focus(name);\n        }\n      };\n      var reset = function (api) {\n        done(editor, currentSearchState, false);\n        disableAll(api, true);\n        updateButtonStates(api);\n      };\n      var doFind = function (api) {\n        var data = api.getData();\n        var last = currentSearchState.get();\n        if (!data.findtext.length) {\n          reset(api);\n          return;\n        }\n        if (last.text === data.findtext && last.matchCase === data.matchcase && last.wholeWord === data.wholewords) {\n          next(editor, currentSearchState);\n        } else {\n          var count = find(editor, currentSearchState, data.findtext, data.matchcase, data.wholewords, data.inselection);\n          if (count <= 0) {\n            notFoundAlert(api);\n          }\n          disableAll(api, count === 0);\n        }\n        updateButtonStates(api);\n      };\n      var initialState = currentSearchState.get();\n      var initialData = {\n        findtext: selectedText,\n        replacetext: '',\n        wholewords: initialState.wholeWord,\n        matchcase: initialState.matchCase,\n        inselection: initialState.inSelection\n      };\n      var spec = {\n        title: 'Find and Replace',\n        size: 'normal',\n        body: {\n          type: 'panel',\n          items: [\n            {\n              type: 'bar',\n              items: [\n                {\n                  type: 'input',\n                  name: 'findtext',\n                  placeholder: 'Find',\n                  maximized: true,\n                  inputMode: 'search'\n                },\n                {\n                  type: 'button',\n                  name: 'prev',\n                  text: 'Previous',\n                  icon: 'action-prev',\n                  disabled: true,\n                  borderless: true\n                },\n                {\n                  type: 'button',\n                  name: 'next',\n                  text: 'Next',\n                  icon: 'action-next',\n                  disabled: true,\n                  borderless: true\n                }\n              ]\n            },\n            {\n              type: 'input',\n              name: 'replacetext',\n              placeholder: 'Replace with',\n              inputMode: 'search'\n            }\n          ]\n        },\n        buttons: [\n          {\n            type: 'menu',\n            name: 'options',\n            icon: 'preferences',\n            tooltip: 'Preferences',\n            align: 'start',\n            items: [\n              {\n                type: 'togglemenuitem',\n                name: 'matchcase',\n                text: 'Match case'\n              },\n              {\n                type: 'togglemenuitem',\n                name: 'wholewords',\n                text: 'Find whole words only'\n              },\n              {\n                type: 'togglemenuitem',\n                name: 'inselection',\n                text: 'Find in selection'\n              }\n            ]\n          },\n          {\n            type: 'custom',\n            name: 'find',\n            text: 'Find',\n            primary: true\n          },\n          {\n            type: 'custom',\n            name: 'replace',\n            text: 'Replace',\n            disabled: true\n          },\n          {\n            type: 'custom',\n            name: 'replaceall',\n            text: 'Replace all',\n            disabled: true\n          }\n        ],\n        initialData: initialData,\n        onChange: function (api, details) {\n          if (details.name === 'findtext' && currentSearchState.get().count > 0) {\n            reset(api);\n          }\n        },\n        onAction: function (api, details) {\n          var data = api.getData();\n          switch (details.name) {\n          case 'find':\n            doFind(api);\n            break;\n          case 'replace':\n            if (!replace(editor, currentSearchState, data.replacetext)) {\n              reset(api);\n            } else {\n              updateButtonStates(api);\n            }\n            break;\n          case 'replaceall':\n            replace(editor, currentSearchState, data.replacetext, true, true);\n            reset(api);\n            break;\n          case 'prev':\n            prev(editor, currentSearchState);\n            updateButtonStates(api);\n            break;\n          case 'next':\n            next(editor, currentSearchState);\n            updateButtonStates(api);\n            break;\n          case 'matchcase':\n          case 'wholewords':\n          case 'inselection':\n            updateSearchState(api);\n            reset(api);\n            break;\n          }\n          focusButtonIfRequired(api, details.name);\n        },\n        onSubmit: function (api) {\n          doFind(api);\n          focusButtonIfRequired(api, 'find');\n        },\n        onClose: function () {\n          editor.focus();\n          done(editor, currentSearchState);\n          editor.undoManager.add();\n        }\n      };\n      dialogApi.set(editor.windowManager.open(spec, { inline: 'toolbar' }));\n    };\n\n    var register$1 = function (editor, currentSearchState) {\n      editor.addCommand('SearchReplace', function () {\n        open(editor, currentSearchState);\n      });\n    };\n\n    var showDialog = function (editor, currentSearchState) {\n      return function () {\n        open(editor, currentSearchState);\n      };\n    };\n    var register = function (editor, currentSearchState) {\n      editor.ui.registry.addMenuItem('searchreplace', {\n        text: 'Find and replace...',\n        shortcut: 'Meta+F',\n        onAction: showDialog(editor, currentSearchState),\n        icon: 'search'\n      });\n      editor.ui.registry.addButton('searchreplace', {\n        tooltip: 'Find and replace',\n        onAction: showDialog(editor, currentSearchState),\n        icon: 'search'\n      });\n      editor.shortcuts.add('Meta+F', '', showDialog(editor, currentSearchState));\n    };\n\n    function Plugin () {\n      global$3.add('searchreplace', function (editor) {\n        var currentSearchState = Cell({\n          index: -1,\n          count: 0,\n          text: '',\n          matchCase: false,\n          wholeWord: false,\n          inSelection: false\n        });\n        register$1(editor, currentSearchState);\n        register(editor, currentSearchState);\n        return get(editor, currentSearchState);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"searchreplace\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/searchreplace')\n//   ES2015:\n//     import 'tinymce/plugins/searchreplace'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/searchreplace/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,SAAQ;AACZ,YAAI,OAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,OAAM,SAAU,GAAG;AACrB,mBAAQ;AAAA;AAEV,eAAO;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA;AAAA;AAIT,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,OAAM;AAC7B,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,YAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,OAAM;AACjC,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,WAAU;AAAA;AAAA;AAG5B,UAAI,WAAW,SAAS;AACxB,UAAI,UAAU,SAAS;AACvB,UAAI,YAAY,aAAa;AAC7B,UAAI,WAAW,aAAa;AAE5B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,QAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,iBAAiB;AAErB,UAAI,gBAAgB,SAAS;AAE7B,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,QAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,QAAO;AAC1B,eAAO,WAAU,QAAQ,WAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,cAAc;AAElB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,cAAc,MAAM,UAAU;AAClC,UAAI,aAAa,MAAM,UAAU;AACjC,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,QAAQ,SAAU,IAAI,GAAG;AAC3B,iBAAS,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,UAAU,SAAU,IAAI,GAAG;AAC7B,YAAI,GAAG,WAAW,GAAG;AACnB,iBAAO;AAAA,eACF;AACL,cAAI,UAAU,EAAE,GAAG;AACnB,cAAI,IAAI;AACR,cAAI,QAAQ;AACZ,mBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAI,IAAI,GAAG;AACX,gBAAI,QAAO,EAAE;AACb,gBAAI,UAAS,SAAS;AACpB,gBAAE,KAAK;AACP,sBAAQ;AAAA;AAEV,sBAAU;AACV,kBAAM,KAAK;AAAA;AAEb,cAAI,MAAM,WAAW,GAAG;AACtB,cAAE,KAAK;AAAA;AAET,iBAAO;AAAA;AAAA;AAGX,UAAI,QAAQ,SAAU,IAAI,GAAG,KAAK;AAChC,aAAK,IAAI,SAAU,GAAG,GAAG;AACvB,gBAAM,EAAE,KAAK,GAAG;AAAA;AAElB,eAAO;AAAA;AAET,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,KAAK;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;AAAA;AAE3E,qBAAW,MAAM,GAAG,GAAG;AAAA;AAEzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,eAAO,QAAQ,IAAI,IAAI;AAAA;AAEzB,UAAI,OAAO,SAAU,IAAI,YAAY;AACnC,YAAI,OAAO,YAAY,KAAK,IAAI;AAChC,aAAK,KAAK;AACV,eAAO;AAAA;AAGT,UAAI,iBAAiB,OAAO;AAC5B,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAGlC,aAAO,WAAW,cAAc,SAAS,SAAS;AAElD,UAAI,WAAW;AACf,UAAI,oBAAoB;AACxB,UAAI,UAAU;AACd,UAAI,OAAO;AAEX,UAAI,OAAO,SAAU,SAAS;AAC5B,eAAO,QAAQ,IAAI;AAAA;AAErB,UAAI,SAAS,SAAU,GAAG;AACxB,eAAO,SAAU,SAAS;AACxB,iBAAO,KAAK,aAAa;AAAA;AAAA;AAG7B,UAAI,WAAW,OAAO;AAEtB,UAAI,SAAS,SAAU,KAAK,KAAK,QAAO;AACtC,YAAI,SAAS,WAAU,UAAU,WAAU,SAAS,SAAQ;AAC1D,cAAI,aAAa,KAAK,SAAQ;AAAA,eACzB;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAa,QAAO,eAAe;AAC7F,gBAAM,IAAI,MAAM;AAAA;AAAA;AAGpB,UAAI,MAAM,SAAU,SAAS,KAAK,QAAO;AACvC,eAAO,QAAQ,KAAK,KAAK;AAAA;AAG3B,UAAI,0BAA0B,SAAU,GAAG,GAAG,OAAO;AACnD,eAAQ,GAAE,wBAAwB,KAAK,WAAW;AAAA;AAEpD,UAAI,4BAA4B,SAAU,GAAG,GAAG;AAC9C,eAAO,wBAAwB,GAAG,GAAG,KAAK;AAAA;AAG5C,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,IAAI,cAAc;AAC5B,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,mBAAmB,IAAI,WAAW,SAAS,GAAG;AACrD,kBAAQ,MAAM,yCAAyC;AACvD,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,QAAQ,IAAI,WAAW;AAAA;AAEhC,UAAI,UAAU,SAAU,KAAK,OAAO;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,cAAc;AAC7B,eAAO,QAAQ;AAAA;AAEjB,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,eAAe;AAC9B,eAAO,QAAQ;AAAA;AAEjB,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,EAAE,KAAK;AAAA;AAEhB,UAAI,YAAY,SAAU,QAAQ,GAAG,GAAG;AACtC,eAAO,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,IAAI,IAAI;AAAA;AAE9D,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,iBAAiB,SAAU,KAAK;AAClC,eAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AAAA;AAElI,UAAI,MAAM,SAAU,UAAU,OAAO;AACnC,YAAI,OAAO,UAAU,SAAY,WAAW,MAAM;AAClD,eAAO,eAAe,QAAQ,KAAK,IAAI,KAAK,iBAAiB,WAAW,aAAa;AAAA;AAGvF,UAAI,SAAS,SAAU,SAAS;AAC9B,eAAO,SAAS,KAAK,QAAQ,IAAI,YAAY,IAAI,aAAa;AAAA;AAEhE,UAAI,WAAW,SAAU,SAAS;AAChC,eAAO,IAAI,QAAQ,IAAI,YAAY,aAAa;AAAA;AAElD,UAAI,OAAO,SAAU,SAAS,QAAQ;AACpC,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,OAAO,SAAU,SAAS,QAAQ;AACpC,YAAI,KAAK,SAAS;AAClB,eAAO,GAAG,SAAS,KAAK,SAAS,GAAG,SAAS,KAAK,GAAG,SAAS,KAAK,KAAK,SAAS;AAAA;AAGnF,UAAI,SAAS,SAAU,QAAQ,SAAS;AACtC,YAAI,WAAW,OAAO;AACtB,iBAAS,KAAK,SAAU,GAAG;AACzB,YAAE,IAAI,aAAa,QAAQ,KAAK,OAAO;AAAA;AAAA;AAG3C,UAAI,SAAS,SAAU,SAAQ,SAAS;AACtC,gBAAO,IAAI,YAAY,QAAQ;AAAA;AAEjC,UAAI,OAAO,SAAU,SAAS,SAAS;AACrC,eAAO,SAAS;AAChB,eAAO,SAAS;AAAA;AAGlB,UAAI,YAAY,SAAU,IAAI,MAAM;AAClC,YAAI,OAAM,SAAU,SAAS;AAC3B,cAAI,CAAC,GAAG,UAAU;AAChB,kBAAM,IAAI,MAAM,kBAAkB,OAAO,iBAAiB,OAAO;AAAA;AAEnE,iBAAO,UAAU,SAAS,MAAM;AAAA;AAElC,YAAI,YAAY,SAAU,SAAS;AACjC,iBAAO,GAAG,WAAW,SAAS,KAAK,QAAQ,IAAI,aAAa,SAAS;AAAA;AAEvE,YAAI,OAAM,SAAU,SAAS,QAAO;AAClC,cAAI,CAAC,GAAG,UAAU;AAChB,kBAAM,IAAI,MAAM,sBAAsB,OAAO,iBAAiB,OAAO;AAAA;AAEvE,kBAAQ,IAAI,YAAY;AAAA;AAE1B,eAAO;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA;AAAA;AAIT,UAAI,MAAM,UAAU,UAAU;AAC9B,UAAI,QAAQ,SAAU,SAAS;AAC7B,eAAO,IAAI,IAAI;AAAA;AAGjB,UAAI,cAAc,SAAU,OAAO,UAAU;AAC3C,eAAO,IAAI,UAAU;AAAA;AAGvB,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,mBAAmB,SAAU,KAAK,MAAM;AAC1C,eAAO,IAAI,QAAQ,SAAS,IAAI,IAAI,OAAO,yBAAyB,KAAK;AAAA;AAE3E,UAAI,yBAAyB,SAAU,KAAK,MAAM;AAChD,eAAO,IAAI,mBAAmB,UAAU;AAAA;AAE1C,UAAI,6BAA6B,SAAU,KAAK,MAAM;AACpD,eAAO,IAAI,mBAAmB,UAAU,UAAU,IAAI,yBAAyB,KAAK,gBAAgB;AAAA;AAEtG,UAAI,WAAW,SAAU,KAAK,MAAM;AAClC,eAAO,CAAC,IAAI,QAAQ,SAAS,IAAI,IAAI,OAAO,yBAAyB,KAAK;AAAA;AAE5E,UAAI,aAAa,SAAU,KAAK,MAAM;AACpC,eAAO,iBAAiB,KAAK,SAAS,uBAAuB,KAAK,SAAS,SAAS,KAAK,SAAS,2BAA2B,KAAK;AAAA;AAEpI,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,KAAK,aAAa;AAAA;AAE3B,UAAI,YAAY,WAAY;AAC1B,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA;AAAA;AAGd,UAAI,SAAS,SAAU,MAAM,QAAQ;AACnC,eAAO,KAAK,aAAa,QAAQ,OAAO;AAAA;AAE1C,UAAI,OAAO,SAAU,KAAK,UAAU,WAAW,WAAW,SAAS,WAAW;AAC5E,YAAI,cAAc,QAAQ;AACxB,sBAAY;AAAA;AAEd,YAAI,QAAO,YAAY,SAAS,SAAS;AACzC,eAAO,OAAM;AACX,cAAI,YAAY,uBAAuB,KAAK;AAC5C,cAAI,aAAa,SAAS,KAAK,QAAO;AACpC,gBAAI,cAAc,YAAY,UAAU,IAAI,SAAQ,UAAU,SAAS;AACvE,gBAAI,aAAa;AACf;AAAA,mBACK;AACL,sBAAO,SAAS;AAChB;AAAA;AAAA,qBAEO,iBAAiB,KAAK,QAAO;AACtC,gBAAI,UAAU,SAAS,QAAO;AAC5B;AAAA;AAAA,qBAEO,OAAO,QAAO;AACvB,sBAAU,KAAK;AAAA;AAEjB,cAAI,UAAS,SAAS;AACpB;AAAA,iBACK;AACL,oBAAO,SAAS;AAAA;AAAA;AAAA;AAItB,UAAI,wBAAwB,SAAU,KAAK,SAAS,MAAM,UAAU,UAAU;AAC5E,YAAI,WAAW,KAAK,OAAO;AACzB;AAAA;AAEF,YAAI,YAAY,IAAI,UAAU,UAAU,IAAI;AAC5C,YAAI,SAAS,IAAI,OAAO,MAAM;AAC9B,YAAI,WAAW,WAAW,OAAO,KAAK,KAAK,UAAU,OAAO,KAAK,KAAK;AACtE,aAAK,KAAK,UAAU,MAAM;AAAA,UACxB,UAAU;AAAA,UACV,KAAK;AAAA,UACL,MAAM,SAAU,OAAM;AACpB,gBAAI,UAAU;AACZ,sBAAQ,WAAW,MAAK;AAAA,mBACnB;AACL,sBAAQ,WAAW,MAAK;AAAA;AAE1B,oBAAQ,SAAS,KAAK,aAAa,QAAQ;AAAA;AAAA;AAAA;AAIjD,UAAI,UAAU,SAAU,KAAK,UAAU,WAAW,SAAS,WAAW,WAAW;AAC/E,YAAI,cAAc,QAAQ;AACxB,sBAAY;AAAA;AAEd,YAAI,SAAS,IAAI,OAAO,WAAW;AACnC,YAAI,WAAW;AACf,YAAI,UAAU;AACd,8BAAsB,KAAK,SAAS,WAAW,UAAU;AACzD,YAAI,gBAAgB,WAAY;AAC9B,cAAI,QAAQ,SAAS,SAAS,GAAG;AAC/B,qBAAS,KAAK;AACd,sBAAU;AAAA;AAEZ,iBAAO;AAAA;AAET,aAAK,KAAK,OAAO,KAAK,KAAK,SAAS,WAAW;AAAA,UAC7C,UAAU;AAAA,UACV,KAAK,SAAU,MAAM;AACnB;AACA,gBAAI,WAAW;AACb,uBAAS,KAAK,MAAM,UAAU,UAAU,IAAI;AAAA;AAE9C,mBAAO;AAAA;AAAA,UAET,MAAM,SAAU,OAAM;AACpB,oBAAQ,SAAS,KAAK,aAAa,QAAQ;AAC3C,gBAAI,WAAW;AACb,wBAAU,KAAK,OAAM;AAAA;AAAA;AAAA,WAGxB,SAAS;AACZ,YAAI,SAAS;AACX,gCAAsB,KAAK,SAAS,SAAS,UAAU;AAAA;AAEzD;AACA,eAAO;AAAA;AAET,UAAI,uBAAuB,SAAU,KAAK,KAAK;AAC7C,YAAI,QAAQ,OAAO,IAAI,gBAAgB,IAAI;AAC3C,YAAI,YAAY,MAAM,QAAQ;AAC9B,YAAI,MAAM,OAAO,IAAI,cAAc,IAAI;AACvC,YAAI,UAAU,IAAI,QAAQ;AAC1B,eAAO,QAAQ,KAAK,IAAI,yBAAyB,WAAW,SAAS;AAAA,UACnE,MAAM,SAAU,MAAM,SAAS;AAC7B,gBAAI,SAAS,SAAS;AACpB,sBAAQ,WAAW,KAAK,SAAS,IAAI;AAAA,uBAC5B,SAAS,WAAW;AAC7B,sBAAQ,WAAW,MAAM;AAAA;AAAA;AAAA,UAG7B,KAAK,SAAU,MAAM;AACnB,gBAAI,WAAW,KAAK,YAAY,aAAa,QAAQ,OAAO,4BAA4B,SAAU,GAAG;AACnG,kBAAI,aAAa,EAAE;AACnB,qBAAO,QAAQ,KAAK,YAAY;AAAA;AAElC,mBAAO,KAAK,UAAU,SAAU,GAAG,GAAG;AACpC,qBAAO,0BAA0B,EAAE,SAAS,GAAG,KAAK,EAAE,SAAS,GAAG,OAAO,IAAI;AAAA;AAAA;AAAA,WAGhF;AAAA;AAEL,UAAI,UAAU,SAAU,KAAK,KAAK;AAChC,eAAO,IAAI,YAAY,KAAK,qBAAqB,KAAK;AAAA;AAExD,UAAI,WAAW,SAAU,KAAK,MAAM;AAClC,YAAI,MAAM,IAAI;AACd,YAAI,WAAW;AACf,eAAO,QAAQ,KAAK;AAAA;AAEtB,UAAI,YAAY,SAAU,KAAK,OAAO;AACpC,eAAO,KAAK,OAAO,SAAU,MAAM;AACjC,iBAAO,SAAS,KAAK;AAAA;AAAA;AAIzB,UAAI,SAAS,SAAU,MAAM,SAAS,OAAO,QAAQ;AACnD,YAAI,UAAU,QAAQ;AACpB,kBAAQ;AAAA;AAEV,YAAI,WAAW,QAAQ;AACrB,mBAAS,KAAK;AAAA;AAEhB,YAAI,QAAQ,QAAQ;AACpB,cAAM,YAAY;AAClB,YAAI,UAAU;AACd,YAAI;AACJ,eAAO,QAAQ,MAAM,KAAK,OAAO;AAC/B,cAAI,cAAc,MAAM,QAAQ;AAChC,cAAI,aAAa,MAAM,QAAQ,MAAM,GAAG,QAAQ;AAChD,cAAI,cAAc,aAAa,YAAY;AAC3C,cAAI,cAAc,QAAQ;AACxB;AAAA;AAEF,kBAAQ,KAAK;AAAA,YACX,OAAO;AAAA,YACP,QAAQ;AAAA;AAEV,gBAAM,YAAY;AAAA;AAEpB,eAAO;AAAA;AAET,UAAI,UAAU,SAAU,UAAU,SAAS;AACzC,YAAI,gBAAgB,MAAM,UAAU,SAAU,KAAK,SAAS;AAC1D,cAAI,UAAU,MAAM;AACpB,cAAI,QAAQ,IAAI;AAChB,cAAI,SAAS,QAAQ,QAAQ;AAC7B,cAAI,YAAY,KAAK,SAAS,SAAU,OAAO,UAAU;AACvD,gBAAI,MAAM,QAAQ,UAAU,MAAM,SAAS,OAAO;AAChD,qBAAO,CAAC;AAAA,gBACJ;AAAA,gBACA,OAAO,KAAK,IAAI,OAAO,MAAM,SAAS;AAAA,gBACtC,QAAQ,KAAK,IAAI,QAAQ,MAAM,UAAU;AAAA,gBACzC,SAAS;AAAA;AAAA,mBAER;AACL,qBAAO;AAAA;AAAA;AAGX,iBAAO;AAAA,YACL,SAAS,IAAI,QAAQ,OAAO;AAAA,YAC5B,MAAM;AAAA;AAAA,WAEP;AAAA,UACD,SAAS;AAAA,UACT,MAAM;AAAA,WACL;AACH,eAAO,QAAQ,eAAe,SAAU,UAAU;AAChD,iBAAO,SAAS;AAAA;AAAA;AAIpB,UAAI,SAAS,SAAU,SAAS,UAAU;AACxC,eAAO,KAAK,UAAU,SAAU,SAAS;AACvC,cAAI,WAAW,QAAQ;AACvB,cAAI,UAAU,IAAI,UAAU,OAAO,KAAK;AACxC,cAAI,YAAY,OAAO,SAAS,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ;AACnF,iBAAO,QAAQ,UAAU;AAAA;AAAA;AAG7B,UAAI,OAAO,SAAU,SAAS,iBAAiB;AAC7C,cAAM,SAAS,SAAU,OAAO,KAAK;AACnC,gBAAM,OAAO,SAAU,KAAK;AAC1B,gBAAI,UAAU,aAAa,QAAQ,gBAAgB,UAAU;AAC7D,gBAAI,SAAS,kBAAkB;AAC/B,gBAAI,WAAW,IAAI,QAAQ;AAC3B,gBAAI,SAAS,WAAW,IAAI,UAAU,IAAI,UAAU,GAAG;AACrD,mBAAK,IAAI,SAAS;AAAA,mBACb;AACL,kBAAI,SAAS,WAAW,IAAI,QAAQ;AAClC,yBAAS,UAAU,IAAI;AAAA;AAEzB,kBAAI,YAAY,SAAS,UAAU,IAAI;AACvC,mBAAK,aAAa,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAK9C,UAAI,cAAc,SAAU,KAAK,SAAS,MAAM,iBAAiB;AAC/D,YAAI,eAAe,SAAS,KAAK;AACjC,YAAI,UAAU,OAAO,SAAS;AAC9B,aAAK,SAAS;AACd,eAAO,QAAQ;AAAA;AAEjB,UAAI,yBAAyB,SAAU,KAAK,SAAS,WAAW,iBAAiB;AAC/E,YAAI,WAAW,UAAU;AACzB,YAAI,QAAQ,IAAI,OAAO;AACvB,YAAI,eAAe,MAAM,SAAS,IAAI,UAAU,KAAK,SAAS,QAAQ,KAAK,UAAU;AACrF,YAAI,UAAU,OAAO,SAAS;AAC9B,aAAK,SAAS;AACd,kBAAU,eAAe;AACzB,eAAO,QAAQ;AAAA;AAGjB,UAAI,cAAc,SAAU,KAAK;AAC/B,YAAI,SAAQ,IAAI,aAAa;AAC7B,YAAI,OAAO,WAAU,UAAU;AAC7B,iBAAO,KAAK;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,iBAAiB,SAAU,QAAQ,oBAAoB,SAAS,aAAa;AAC/E,YAAI,SAAS,OAAO,IAAI,OAAO,QAAQ,EAAE,kBAAkB;AAC3D,eAAO,YAAY;AACnB,YAAI,OAAO,OAAO;AAClB,aAAK,QAAQ,oBAAoB;AACjC,YAAI,aAAa;AACf,iBAAO,uBAAuB,OAAO,KAAK,SAAS,OAAO,WAAW;AAAA,eAChE;AACL,iBAAO,YAAY,OAAO,KAAK,SAAS,MAAM;AAAA;AAAA;AAGlD,UAAI,SAAS,SAAU,MAAM;AAC3B,YAAI,aAAa,KAAK;AACtB,YAAI,KAAK,YAAY;AACnB,qBAAW,aAAa,KAAK,YAAY;AAAA;AAE3C,aAAK,WAAW,YAAY;AAAA;AAE9B,UAAI,mBAAmB,SAAU,QAAQ,OAAO;AAC9C,YAAI,QAAQ;AACZ,YAAI,QAAQ,SAAS,QAAQ,OAAO,UAAU,qBAAqB;AACnE,YAAI,MAAM,QAAQ;AAChB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,YAAY,YAAY,MAAM;AAClC,gBAAI,cAAc,QAAQ,CAAC,UAAU,QAAQ;AAC3C;AAAA;AAEF,gBAAI,cAAc,MAAM,YAAY;AAClC,oBAAM,KAAK,MAAM;AAAA;AAAA;AAAA;AAIvB,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,QAAQ,oBAAoB,SAAS;AACjE,YAAI,cAAc,mBAAmB;AACrC,YAAI,YAAY,YAAY;AAC5B,YAAI,MAAM,OAAO;AACjB,kBAAU,YAAY;AACtB,YAAI,SAAS;AACX,cAAI,YAAY,MAAM,YAAY,OAAO;AACvC,wBAAY;AAAA,iBACP;AACL;AAAA;AAAA,eAEG;AACL,cAAI,YAAY,MAAM,IAAI;AACxB,wBAAY,YAAY,QAAQ;AAAA,iBAC3B;AACL;AAAA;AAAA;AAGJ,YAAI,YAAY,iBAAiB,QAAQ,YAAY,QAAQ;AAC7D,YAAI,QAAQ,iBAAiB,QAAQ;AACrC,YAAI,MAAM,QAAQ;AAChB,cAAI,SAAS,iBAAiB,QAAQ,YAAY;AAClD,iBAAO,UAAU,eAAe,MAAM;AACtC,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,aAAa,SAAU,KAAK,MAAM;AACpC,YAAI,UAAS,KAAK;AAClB,YAAI,OAAO;AACX,YAAI,IAAI,QAAQ,UAAS;AACvB,cAAI,OAAO;AAAA;AAAA;AAGf,UAAI,mBAAmB,SAAU,MAAM,WAAW;AAChD,YAAI,cAAc,KAAK,QAAQ,uCAAuC,QAAQ,QAAQ,OAAO;AAC7F,YAAI,YAAY,MAAM,cAAc;AACpC,eAAO,YAAY,cAAc,gBAAgB,MAAM,YAAa,eAAc,gBAAgB,OAAO;AAAA;AAE3G,UAAI,OAAO,SAAU,QAAQ,oBAAoB,MAAM,WAAW,WAAW,aAAa;AACxF,YAAI,YAAY,OAAO;AACvB,YAAI,cAAc,iBAAiB,MAAM;AACzC,YAAI,qBAAqB,UAAU;AACnC,YAAI,UAAU;AAAA,UACZ,OAAO,IAAI,OAAO,aAAa,YAAY,MAAM;AAAA,UACjD,YAAY;AAAA;AAEd,YAAI,QAAQ,eAAe,QAAQ,oBAAoB,SAAS;AAChE,YAAI,SAAS,QAAQ,YAAY;AAC/B,oBAAU,OAAO,UAAU,UAAU;AAAA;AAEvC,YAAI,OAAO;AACT,cAAI,WAAW,cAAc,QAAQ,oBAAoB;AACzD,6BAAmB,IAAI;AAAA,YACrB,OAAO;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAAA;AAGJ,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,QAAQ,oBAAoB;AAC/C,YAAI,QAAQ,cAAc,QAAQ,oBAAoB;AACtD,2BAAmB,IAAI,SAAS,SAAS,IAAI,mBAAmB,QAAQ,EAAE;AAAA;AAE5E,UAAI,OAAO,SAAU,QAAQ,oBAAoB;AAC/C,YAAI,QAAQ,cAAc,QAAQ,oBAAoB;AACtD,2BAAmB,IAAI,SAAS,SAAS,IAAI,mBAAmB,QAAQ,EAAE;AAAA;AAE5E,UAAI,cAAc,SAAU,MAAM;AAChC,YAAI,aAAa,YAAY;AAC7B,eAAO,eAAe,QAAQ,WAAW,SAAS;AAAA;AAEpD,UAAI,UAAU,SAAU,QAAQ,oBAAoB,MAAM,SAAS,MAAK;AACtE,YAAI,cAAc,mBAAmB;AACrC,YAAI,eAAe,YAAY;AAC/B,YAAI,mBAAmB,YAAY;AACnC,kBAAU,YAAY;AACtB,YAAI,OAAO,OAAO;AAClB,YAAI,QAAQ,SAAS,KAAK,SAAS,QAAQ,KAAK,qBAAqB,UAAU;AAC/E,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,YAAY,YAAY,MAAM;AAClC,cAAI,aAAa,oBAAoB,SAAS,WAAW;AACzD,cAAI,QAAO,eAAe,YAAY,OAAO;AAC3C,gBAAI,KAAK,QAAQ;AACf,oBAAM,GAAG,WAAW,YAAY;AAChC,qBAAO,MAAM;AAAA,mBACR;AACL,yBAAW,OAAO,KAAK,MAAM;AAAA;AAE/B,mBAAO,MAAM,EAAE,IAAI;AACjB,2BAAa,SAAS,YAAY,MAAM,KAAK;AAC7C,kBAAI,eAAe,mBAAmB;AACpC,2BAAW,OAAO,KAAK,MAAM;AAAA,qBACxB;AACL;AACA;AAAA;AAAA;AAGJ,gBAAI,SAAS;AACX;AAAA;AAAA,qBAEO,oBAAoB,cAAc;AAC3C,kBAAM,GAAG,aAAa,kBAAkB,OAAO,oBAAoB;AAAA;AAAA;AAGvE,2BAAmB,IAAI,SAAS,SAAS,IAAI,cAAc;AAAA,UACzD,OAAO,OAAM,IAAI,YAAY,QAAQ;AAAA,UACrC,OAAO;AAAA;AAET,YAAI,SAAS;AACX,eAAK,QAAQ;AAAA,eACR;AACL,eAAK,QAAQ;AAAA;AAEf,eAAO,CAAC,QAAO,mBAAmB,MAAM,QAAQ;AAAA;AAElD,UAAI,OAAO,SAAU,QAAQ,oBAAoB,qBAAqB;AACpE,YAAI,gBAAgB;AACpB,YAAI,cAAc,mBAAmB;AACrC,YAAI,QAAQ,SAAS,QAAQ,OAAO,UAAU,qBAAqB;AACnE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,YAAY,YAAY,MAAM;AAClC,cAAI,cAAc,QAAQ,UAAU,QAAQ;AAC1C,gBAAI,cAAc,YAAY,MAAM,YAAY;AAC9C,kBAAI,CAAC,gBAAgB;AACnB,iCAAiB,MAAM,GAAG;AAAA;AAE5B,6BAAe,MAAM,GAAG;AAAA;AAE1B,mBAAO,MAAM;AAAA;AAAA;AAGjB,2BAAmB,IAAI,SAAS,SAAS,IAAI,cAAc;AAAA,UACzD,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA;AAER,YAAI,kBAAkB,cAAc;AAClC,cAAI,MAAM,OAAO,IAAI;AACrB,cAAI,SAAS,gBAAgB;AAC7B,cAAI,OAAO,cAAc,aAAa,KAAK;AAC3C,cAAI,wBAAwB,OAAO;AACjC,mBAAO,UAAU,OAAO;AAAA;AAE1B,iBAAO;AAAA;AAAA;AAGX,UAAI,UAAU,SAAU,QAAQ,oBAAoB;AAClD,eAAO,mBAAmB,MAAM,QAAQ;AAAA;AAE1C,UAAI,UAAU,SAAU,QAAQ,oBAAoB;AAClD,eAAO,mBAAmB,MAAM,QAAQ;AAAA;AAG1C,UAAI,MAAM,SAAU,QAAQ,cAAc;AACxC,YAAI,SAAS,SAAU,qBAAqB;AAC1C,iBAAO,KAAK,QAAQ,cAAc;AAAA;AAEpC,YAAI,UAAS,SAAU,MAAM,WAAW,WAAW,aAAa;AAC9D,cAAI,gBAAgB,QAAQ;AAC1B,0BAAc;AAAA;AAEhB,iBAAO,KAAK,QAAQ,cAAc,MAAM,WAAW,WAAW;AAAA;AAEhE,YAAI,SAAS,WAAY;AACvB,iBAAO,KAAK,QAAQ;AAAA;AAEtB,YAAI,SAAS,WAAY;AACvB,iBAAO,KAAK,QAAQ;AAAA;AAEtB,YAAI,YAAY,SAAU,MAAM,SAAS,MAAK;AAC5C,iBAAO,QAAQ,QAAQ,cAAc,MAAM,SAAS;AAAA;AAEtD,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA;AAAA;AAIb,UAAI,YAAY,SAAU,UAAU;AAClC,YAAI,UAAU,KAAK,SAAS;AAC5B,YAAI,SAAS,WAAY;AACvB,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,YAAI,QAAQ,WAAY;AACtB;AACA,kBAAQ,IAAI,SAAS;AAAA;AAEvB,YAAI,QAAQ,WAAY;AACtB,iBAAO,QAAQ,MAAM;AAAA;AAEvB,YAAI,OAAM,WAAY;AACpB,iBAAO,QAAQ;AAAA;AAEjB,YAAI,OAAM,SAAU,GAAG;AACrB;AACA,kBAAQ,IAAI,SAAS,KAAK;AAAA;AAE5B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA;AAAA;AAGT,UAAI,QAAQ,WAAY;AACtB,YAAI,UAAU,UAAU;AACxB,YAAI,KAAK,SAAU,GAAG;AACpB,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,eAAO,SAAS,SAAS,IAAI,UAAU,EAAE;AAAA;AAG3C,UAAI,OAAO,SAAU,QAAQ,oBAAoB;AAC/C,YAAI,YAAY;AAChB,eAAO,YAAY;AACnB,YAAI,eAAe,SAAS,KAAK,OAAO,UAAU,WAAW,EAAE,QAAQ;AACvE,YAAI,qBAAqB,SAAU,MAAK;AACtC,cAAI,aAAa,QAAQ,QAAQ,sBAAsB,KAAI,SAAS,KAAI;AACxE,qBAAW;AACX,cAAI,aAAa,QAAQ,QAAQ,sBAAsB,KAAI,SAAS,KAAI;AACxE,qBAAW;AAAA;AAEb,YAAI,oBAAoB,SAAU,MAAK;AACrC,cAAI,OAAO,KAAI;AACf,cAAI,UAAU,mBAAmB;AACjC,6BAAmB,IAAI,SAAS,SAAS,IAAI,UAAU;AAAA,YACrD,WAAW,KAAK;AAAA,YAChB,WAAW,KAAK;AAAA,YAChB,aAAa,KAAK;AAAA;AAAA;AAGtB,YAAI,aAAa,SAAU,MAAK,SAAS;AACvC,cAAI,UAAU;AAAA,YACZ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAEF,cAAI,SAAS,UAAU,KAAI,UAAU,KAAI;AACzC,eAAK,SAAS;AAAA;AAEhB,YAAI,gBAAgB,SAAU,MAAK;AACjC,iBAAO,cAAc,MAAM,wCAAwC,WAAY;AAC7E,iBAAI,MAAM;AAAA;AAAA;AAGd,YAAI,wBAAwB,SAAU,MAAK,MAAM;AAC/C,cAAI,SAAS,QAAQ,cAAc,SAAS,WAAW,aAAc,UAAS,UAAU,SAAS,aAAa,SAAS,eAAe;AACpI,iBAAI,MAAM;AAAA;AAAA;AAGd,YAAI,QAAQ,SAAU,MAAK;AACzB,eAAK,QAAQ,oBAAoB;AACjC,qBAAW,MAAK;AAChB,6BAAmB;AAAA;AAErB,YAAI,SAAS,SAAU,MAAK;AAC1B,cAAI,OAAO,KAAI;AACf,cAAI,OAAO,mBAAmB;AAC9B,cAAI,CAAC,KAAK,SAAS,QAAQ;AACzB,kBAAM;AACN;AAAA;AAEF,cAAI,KAAK,SAAS,KAAK,YAAY,KAAK,cAAc,KAAK,aAAa,KAAK,cAAc,KAAK,YAAY;AAC1G,iBAAK,QAAQ;AAAA,iBACR;AACL,gBAAI,QAAQ,KAAK,QAAQ,oBAAoB,KAAK,UAAU,KAAK,WAAW,KAAK,YAAY,KAAK;AAClG,gBAAI,SAAS,GAAG;AACd,4BAAc;AAAA;AAEhB,uBAAW,MAAK,UAAU;AAAA;AAE5B,6BAAmB;AAAA;AAErB,YAAI,eAAe,mBAAmB;AACtC,YAAI,cAAc;AAAA,UAChB,UAAU;AAAA,UACV,aAAa;AAAA,UACb,YAAY,aAAa;AAAA,UACzB,WAAW,aAAa;AAAA,UACxB,aAAa,aAAa;AAAA;AAE5B,YAAI,OAAO;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,cACL;AAAA,gBACE,MAAM;AAAA,gBACN,OAAO;AAAA,kBACL;AAAA,oBACE,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,WAAW;AAAA,oBACX,WAAW;AAAA;AAAA,kBAEb;AAAA,oBACE,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,UAAU;AAAA,oBACV,YAAY;AAAA;AAAA,kBAEd;AAAA,oBACE,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,UAAU;AAAA,oBACV,YAAY;AAAA;AAAA;AAAA;AAAA,cAIlB;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,WAAW;AAAA;AAAA;AAAA;AAAA,UAIjB,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,OAAO;AAAA,cACP,OAAO;AAAA,gBACL;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA;AAAA,gBAER;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA;AAAA,gBAER;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA;AAAA;AAAA;AAAA,YAIZ;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA,YAEX;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA;AAAA,YAEZ;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA;AAAA;AAAA,UAGd;AAAA,UACA,UAAU,SAAU,MAAK,SAAS;AAChC,gBAAI,QAAQ,SAAS,cAAc,mBAAmB,MAAM,QAAQ,GAAG;AACrE,oBAAM;AAAA;AAAA;AAAA,UAGV,UAAU,SAAU,MAAK,SAAS;AAChC,gBAAI,OAAO,KAAI;AACf,oBAAQ,QAAQ;AAAA,mBACX;AACH,uBAAO;AACP;AAAA,mBACG;AACH,oBAAI,CAAC,QAAQ,QAAQ,oBAAoB,KAAK,cAAc;AAC1D,wBAAM;AAAA,uBACD;AACL,qCAAmB;AAAA;AAErB;AAAA,mBACG;AACH,wBAAQ,QAAQ,oBAAoB,KAAK,aAAa,MAAM;AAC5D,sBAAM;AACN;AAAA,mBACG;AACH,qBAAK,QAAQ;AACb,mCAAmB;AACnB;AAAA,mBACG;AACH,qBAAK,QAAQ;AACb,mCAAmB;AACnB;AAAA,mBACG;AAAA,mBACA;AAAA,mBACA;AACH,kCAAkB;AAClB,sBAAM;AACN;AAAA;AAEF,kCAAsB,MAAK,QAAQ;AAAA;AAAA,UAErC,UAAU,SAAU,MAAK;AACvB,mBAAO;AACP,kCAAsB,MAAK;AAAA;AAAA,UAE7B,SAAS,WAAY;AACnB,mBAAO;AACP,iBAAK,QAAQ;AACb,mBAAO,YAAY;AAAA;AAAA;AAGvB,kBAAU,IAAI,OAAO,cAAc,KAAK,MAAM,EAAE,QAAQ;AAAA;AAG1D,UAAI,aAAa,SAAU,QAAQ,oBAAoB;AACrD,eAAO,WAAW,iBAAiB,WAAY;AAC7C,eAAK,QAAQ;AAAA;AAAA;AAIjB,UAAI,aAAa,SAAU,QAAQ,oBAAoB;AACrD,eAAO,WAAY;AACjB,eAAK,QAAQ;AAAA;AAAA;AAGjB,UAAI,WAAW,SAAU,QAAQ,oBAAoB;AACnD,eAAO,GAAG,SAAS,YAAY,iBAAiB;AAAA,UAC9C,MAAM;AAAA,UACN,UAAU;AAAA,UACV,UAAU,WAAW,QAAQ;AAAA,UAC7B,MAAM;AAAA;AAER,eAAO,GAAG,SAAS,UAAU,iBAAiB;AAAA,UAC5C,SAAS;AAAA,UACT,UAAU,WAAW,QAAQ;AAAA,UAC7B,MAAM;AAAA;AAER,eAAO,UAAU,IAAI,UAAU,IAAI,WAAW,QAAQ;AAAA;AAGxD,wBAAmB;AACjB,iBAAS,IAAI,iBAAiB,SAAU,QAAQ;AAC9C,cAAI,qBAAqB,KAAK;AAAA,YAC5B,OAAO;AAAA,YACP,OAAO;AAAA,YACP,MAAM;AAAA,YACN,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa;AAAA;AAEf,qBAAW,QAAQ;AACnB,mBAAS,QAAQ;AACjB,iBAAO,IAAI,QAAQ;AAAA;AAAA;AAIvB;AAAA;AAAA;AAAA;;;AC3oCJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,wCAAQ;", "names": []}