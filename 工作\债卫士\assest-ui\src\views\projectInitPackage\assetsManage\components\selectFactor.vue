<template>
    <div class="box-main">
        <div class="left" :xl="4">
            <div :class="`tab-item ${activeTab == v.code ? 'active' : ''}`" v-for="v in tabList" :key="v.code"
                @click="toHandleUrage(v)">
                {{ v.info }}
            </div>
        </div>
        <div class="right" :xl="20">
            <header class="pb20">
                <div class="title mb10">参数设置</div>
                <div class="remarks pl10">
                    注：如需要设置下限，请填写“0”，如设置小于24的数据，则填写 （0 ≤ 资产规模 &lt; 24）；设置上限，请填写“+”符号， 如设置大于等于24的数据，则填写（24 ≤ 资产规模
                    &lt;
                    +）即可
                </div>
            </header>
            <section class="asset-analysis">
                <principalBox v-model:dataList="form.principalList" title="剩余本金区间" />
                <ageBox v-model:dataList="form.ageList" title="年龄区间" />
                <loansBox v-model:dataList="form.loanAmountList" title="放款金额区间" />
                <overDaysBox v-model:dataList="form.overdueDaysList" title="逾期天数区间" />
                <selectSet id="marriage-box" fileName="maritalName" v-model:optionList="marriageList"
                    v-model:dataList="form.maritalList" title="婚姻情况" />
                <selectSet id="education-box" fileName="educationName" v-model:optionList="educationList"
                    v-model:dataList="form.educationList" title="学历" />
                <paymentDays id="payment-days" v-model:dataList="form.billingRangeList" title="账期区间" />
            </section>
        </div>
    </div>
</template>

<script setup name="ParamSet">
import principalBox from '@/views/assessment/assessment/components/params/principalBox';
import overDaysBox from '@/views/assessment/assessment/components/params/overDaysBox';
import loansBox from '@/views/assessment/assessment/components/params/loansBox';
import ageBox from '@/views/assessment/assessment/components/params/ageBox';
import paymentDays from '@/views/assessment/assessment/components/params/paymentDays';
import selectSet from '@/views/assessment/assessment/components/params/selectSet';
import { updateParameterSetting, queryParameterSetting } from '@/api/assetAnalysis/paramSet';
const props = defineProps({
    updateStep: { type: Function, default: () => { } }
})
const { proxy } = getCurrentInstance()
const loading = ref(false)
const route = useRoute()
const activeTab = ref('0')
const form = ref({
    ageList: [],  // 年龄区间
    overdueDaysList: [], // 逾期天数区间
    assetScaleList: [], // 资产规模区间
    loanAmountList: [], //放款金额区间
    assetAgingList: [], // 资产账龄区间
    lendingTimeList: [],  // 放贷时间区间
    periodsNumberList: [],  // 贷款期数
    principalList: [],  // 本金区间
    creditCardList: [], // 信用卡额度区间
    maritalList: [],  // 婚姻情况
    educationList: [],  // 学历区间
    billingRangeList: [], // 账期区间设置
})
const tabList = ref([
    { id: 'principal-box', code: 0, info: '本金区间设置' },
    { id: 'age-box', code: 1, info: '年龄区间设置' },
    { id: 'loans-box', code: 2, info: '放款金额区间设置' },
    { id: 'over-day-box', code: 3, info: '逾期天数区间设置' },
    { id: 'marriage-box', code: 4, info: '婚姻情况设置' },
    { id: 'education-box', code: 5, info: '学历设置' },
    { id: 'payment-days', code: 6, info: '账期区间设置' },
])
const educationList = ref([
    { code: '0', info: '小学' },
    { code: '1', info: '初中' },
    { code: '2', info: '高中' },
    { code: '3', info: '本科' },
    { code: '4', info: '研究生' },
    { code: '5', info: '其他' },
])
const marriageList = ref([
    { code: '0', info: '未婚' },
    { code: '1', info: '已婚' },
    { code: '2', info: '离婚' },
    { code: '3', info: '丧偶' },
    { code: '5', info: '其他' },
])
const keyObj = {
    principalList: '剩余本金区间',
    ageList: '年龄区间',
    loanAmountList: '放款金额区间',
    overdueDaysList: '逾期天数区间',
    marriageList: '婚姻情况',
    educationList: '学历',
    billingRangeList: '账期区间',
}

getList()
function getList() {
    loading.value = true
    queryParameterSetting().then(res => {
        for (const key in res.data) {
            if (Object.prototype.hasOwnProperty.call(res.data, key) && !res.data[key]) {
                res.data[key] = []
            }
        }
        delete res.data.relationId
        Object.keys(res.data).forEach(key => {
            res.data[key].forEach(item => {
                if (item.endAmount == -1 && item.hasOwnProperty('endAmount')) {
                    item.endAmount = "+"
                }
                if (item.endNumeric == -1 && item.hasOwnProperty('endNumeric')) {
                    item.endNumeric = "+"
                }
            })
        })
        form.value = res.data
    }).finally(() => loading.value = false)
}

function handleSubmit() {
    const reqForm = JSON.parse(JSON.stringify(form.value))
    delete reqForm.relationId
    //添加排序
    Object.keys(reqForm).forEach((key) => {
        if (key != 'id' && key != 'recordId') {
            reqForm[key].forEach((item, index) => {
                if (item.endNumeric == 0) {
                    item.endNumeric = String(item.endNumeric)
                }
                if (item.endAmount == 0) {
                    item.endAmount = String(item.endAmount)
                }
                if (item.startAmount == 0) {
                    item.startAmount = String(item.startAmount)
                }
                if (item.startNumeric == 0) {
                    item.startNumeric = String(item.startNumeric)
                }
                if (index < reqForm[key].length - 1) {
                    if ((!item.endNumeric && item.hasOwnProperty('endNumeric')) || (!item.endAmount && item.hasOwnProperty('endAmount'))) {
                        return proxy.$modal.msgWarning(`【${keyObj[key]}】后区间参数没有设置`)
                    }
                    if ((!item.startNumeric && item.hasOwnProperty('startNumeric')) || (!item.startAmount && item.hasOwnProperty('startAmount'))) {
                        return proxy.$modal.msgWarning(`【${keyObj[key]}】前区间参数没有设置`)
                    }
                }
                if (item.endNumeric && item.endNumeric.toString().indexOf("+") > -1 && item.endNumeric?.length > 1) {
                    return proxy.$modal.msgWarning(`【${keyObj[key]}】后区间参数【${item.endNumeric}】设置错误`)
                }
                if (item.endAmount && item.endAmount?.toString().indexOf("+") > -1 && item.endAmount.length > 1) {
                    return proxy.$modal.msgWarning(`【${keyObj[key]}】后区间参数【${item.endAmount}】设置错误`)
                }
                if (item.endAmount == '+' && item.hasOwnProperty('endAmount')) {
                    item.endAmount = -1
                }
                if (item.endNumeric == '+' && item.hasOwnProperty('endNumeric')) {
                    item.endAmount = -1
                }
            })
            reqForm[key].forEach((item, index) => {
                item.sort = index + 1;
                if (item.endNumeric && item.endNumeric == "+") {
                    item.endNumeric = -1;
                }
                if (item.endAmount && item.endAmount == "+") {
                    item.endAmount = -1;
                }
            })
        }
    });
    updateParameterSetting(reqForm).then(res => {
        if (res.code == 200) {
            proxy.$modal.msgSuccess('参数设置成功！')
        }
    })
}

function handleCancel() {
    getList()
}

//跳转锚点
function toHandleUrage(row) {
    activeTab.value = row.code;
    const dom = document.getElementById(row.id)
    dom.scrollIntoView({ behavior: "auto", block: "start", inline: "nearest" });
    document.body.scrollTop = document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss" scoped>
.box-main {
    display: flex;
    background-color: #F6F8FD;

    .left {
        width: 240px;
        padding-top: 20px;
        height: calc(100vh - 85px);
        background-color: #fff;
        border-right: 1px solid #EFF0F1;
    }

    .right {
        width: 100%;
    }
}


header {
    padding: 20px;
    background: #FFFFFF;
}

.tab-item {
    font-size: 14px;
    cursor: pointer;
    line-height: 44px;
    text-align: center;
}

.title {
    position: relative;
    font-size: 18px;
    padding-left: 10px;
    margin-bottom: 20px;
}

.remarks {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #9f9f9f;
}

section {
    overflow: auto;
    margin-top: 20px;
    padding: 0 20px 20px 0;
    height: calc(100vh - 315px);
}

.active {
    transition: all .4s;
    color: #1890FF;
    background-color: #E3F2FF;
}

.asset-analysis {
    padding-left: 20px;
}
</style>
<style>
.asset-analysis .el-form {
    width: 700px;
}
</style>