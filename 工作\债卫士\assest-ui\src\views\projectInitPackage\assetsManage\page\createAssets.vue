<template>
    <div class="content-container">
        <ProgressInfo :active="activeStep" :progressList="progressList" title="组建资产包流程进度" />
        <div class="content-area">
            <header class="df-jc-sb mb20">
                <div class="box-title">{{ progressList[activeStep - 1].label }}</div>
                <span>
                    <el-button v-if="activeStep == 1" @click="toBack">返回</el-button>
                    <el-button v-if="activeStep != 1" type="primary" @click="preStep">上一步</el-button>
                    <el-button v-if="activeStep != progressList.length" type="primary" @click="nextStep">下一步</el-button>
                    <el-button v-if="activeStep == progressList.length" type="primary" @click="toBack">提交数据</el-button>
                </span>
            </header>
            <SelectFactor v-show="activeStep == 1" />
            <SelectProduct v-show="activeStep == 2" />
            <AssetsInfo v-show="activeStep == 3" />
            <PreviewData v-show="[4, 5].includes(activeStep)" :activeStep="activeStep" />
        </div>
    </div>
</template>

<script setup>
import AssetsInfo from '../components/assetsInfo.vue';
import PreviewData from '../components/previewData.vue';
import SelectFactor from '../components/selectFactor.vue';
import SelectProduct from '../components/selectProduct.vue';
import ProgressInfo from '@/views/transferManage/transferManagement/components/progressInfo.vue';
const { proxy } = getCurrentInstance()
const route = useRoute()
const progressList = ref([
    { label: '选择维度因子', value: '' },
    { label: '选择贷款产品', value: '' },
    { label: '填写资产包信息', value: '' },
    { label: '预览数据', value: '' },
    { label: '提交数据', value: '' },
])
const activeStep = ref(1)
function preStep() {
    activeStep.value--
}
function nextStep() {
    activeStep.value++
}

// 返回
function toBack() {
    proxy.$tab.closeOpenPage({ path: route.query.path });
}
</script>

<style lang="scss" scoped>
header {
    padding: 10px 20px;
    border-bottom: 1px solid #EFF0F1;
}

.content-container {
    padding: 20px;
    min-height: calc(100vh - 88px);
    background-color: #F2F6FC;

    .content-area {
        padding: 20px;
        padding-top: 0;
        background-color: #fff;
    }
}
</style>