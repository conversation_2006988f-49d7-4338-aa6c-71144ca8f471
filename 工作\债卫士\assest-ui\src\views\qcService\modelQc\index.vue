<template>
    <div class="app-container">
        <el-tabs v-model="activeName" type="card" class="demo-tabs">
            <el-tab-pane v-if="checkPermi(['qcService:modelQc:seatTab'])" label="坐席模型" name="坐席模型" />
            <el-tab-pane v-if="checkPermi(['qcService:modelQc:clientTab'])" label="客户模型" name="客户模型" />
        </el-tabs>
        <seatTab v-if="activeName == '坐席模型' && checkPermi(['qcService:modelQc:seatTab'])" />
        <clientTab v-if="activeName == '客户模型' && checkPermi(['qcService:modelQc:clientTab'])" />
    </div>
</template>

<script setup>
import seatTab from './tabPage/seatTab';
import clientTab from './tabPage/clientTab';
import { checkPermi } from "@/utils/permission";
const activeName = ref('坐席模型')
</script>

<style lang="scss" scoped></style>