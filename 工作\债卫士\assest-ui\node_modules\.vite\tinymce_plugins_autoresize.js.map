{"version": 3, "sources": ["../tinymce/plugins/autoresize/plugin.js", "../tinymce/plugins/autoresize/index.js", "dep:tinymce_plugins_autoresize"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var hasOwnProperty = Object.hasOwnProperty;\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var fireResizeEditor = function (editor) {\n      return editor.fire('ResizeEditor');\n    };\n\n    var getAutoResizeMinHeight = function (editor) {\n      return editor.getParam('min_height', editor.getElement().offsetHeight, 'number');\n    };\n    var getAutoResizeMaxHeight = function (editor) {\n      return editor.getParam('max_height', 0, 'number');\n    };\n    var getAutoResizeOverflowPadding = function (editor) {\n      return editor.getParam('autoresize_overflow_padding', 1, 'number');\n    };\n    var getAutoResizeBottomMargin = function (editor) {\n      return editor.getParam('autoresize_bottom_margin', 50, 'number');\n    };\n    var shouldAutoResizeOnInit = function (editor) {\n      return editor.getParam('autoresize_on_init', true, 'boolean');\n    };\n\n    var isFullscreen = function (editor) {\n      return editor.plugins.fullscreen && editor.plugins.fullscreen.isFullscreen();\n    };\n    var wait = function (editor, oldSize, times, interval, callback) {\n      global.setEditorTimeout(editor, function () {\n        resize(editor, oldSize);\n        if (times--) {\n          wait(editor, oldSize, times, interval, callback);\n        } else if (callback) {\n          callback();\n        }\n      }, interval);\n    };\n    var toggleScrolling = function (editor, state) {\n      var body = editor.getBody();\n      if (body) {\n        body.style.overflowY = state ? '' : 'hidden';\n        if (!state) {\n          body.scrollTop = 0;\n        }\n      }\n    };\n    var parseCssValueToInt = function (dom, elm, name, computed) {\n      var value = parseInt(dom.getStyle(elm, name, computed), 10);\n      return isNaN(value) ? 0 : value;\n    };\n    var shouldScrollIntoView = function (trigger) {\n      if ((trigger === null || trigger === void 0 ? void 0 : trigger.type.toLowerCase()) === 'setcontent') {\n        var setContentEvent = trigger;\n        return setContentEvent.selection === true || setContentEvent.paste === true;\n      } else {\n        return false;\n      }\n    };\n    var resize = function (editor, oldSize, trigger) {\n      var dom = editor.dom;\n      var doc = editor.getDoc();\n      if (!doc) {\n        return;\n      }\n      if (isFullscreen(editor)) {\n        toggleScrolling(editor, true);\n        return;\n      }\n      var docEle = doc.documentElement;\n      var resizeBottomMargin = getAutoResizeBottomMargin(editor);\n      var resizeHeight = getAutoResizeMinHeight(editor);\n      var marginTop = parseCssValueToInt(dom, docEle, 'margin-top', true);\n      var marginBottom = parseCssValueToInt(dom, docEle, 'margin-bottom', true);\n      var contentHeight = docEle.offsetHeight + marginTop + marginBottom + resizeBottomMargin;\n      if (contentHeight < 0) {\n        contentHeight = 0;\n      }\n      var containerHeight = editor.getContainer().offsetHeight;\n      var contentAreaHeight = editor.getContentAreaContainer().offsetHeight;\n      var chromeHeight = containerHeight - contentAreaHeight;\n      if (contentHeight + chromeHeight > getAutoResizeMinHeight(editor)) {\n        resizeHeight = contentHeight + chromeHeight;\n      }\n      var maxHeight = getAutoResizeMaxHeight(editor);\n      if (maxHeight && resizeHeight > maxHeight) {\n        resizeHeight = maxHeight;\n        toggleScrolling(editor, true);\n      } else {\n        toggleScrolling(editor, false);\n      }\n      if (resizeHeight !== oldSize.get()) {\n        var deltaSize = resizeHeight - oldSize.get();\n        dom.setStyle(editor.getContainer(), 'height', resizeHeight + 'px');\n        oldSize.set(resizeHeight);\n        fireResizeEditor(editor);\n        if (global$1.browser.isSafari() && global$1.mac) {\n          var win = editor.getWin();\n          win.scrollTo(win.pageXOffset, win.pageYOffset);\n        }\n        if (editor.hasFocus() && shouldScrollIntoView(trigger)) {\n          editor.selection.scrollIntoView();\n        }\n        if (global$1.webkit && deltaSize < 0) {\n          resize(editor, oldSize, trigger);\n        }\n      }\n    };\n    var setup = function (editor, oldSize) {\n      editor.on('init', function () {\n        var overflowPadding = getAutoResizeOverflowPadding(editor);\n        var dom = editor.dom;\n        dom.setStyles(editor.getDoc().documentElement, { height: 'auto' });\n        dom.setStyles(editor.getBody(), {\n          'paddingLeft': overflowPadding,\n          'paddingRight': overflowPadding,\n          'min-height': 0\n        });\n      });\n      editor.on('NodeChange SetContent keyup FullscreenStateChanged ResizeContent', function (e) {\n        resize(editor, oldSize, e);\n      });\n      if (shouldAutoResizeOnInit(editor)) {\n        editor.on('init', function () {\n          wait(editor, oldSize, 20, 100, function () {\n            wait(editor, oldSize, 5, 1000);\n          });\n        });\n      }\n    };\n\n    var register = function (editor, oldSize) {\n      editor.addCommand('mceAutoResize', function () {\n        resize(editor, oldSize);\n      });\n    };\n\n    function Plugin () {\n      global$2.add('autoresize', function (editor) {\n        if (!has(editor.settings, 'resize')) {\n          editor.settings.resize = false;\n        }\n        if (!editor.inline) {\n          var oldSize = Cell(0);\n          register(editor, oldSize);\n          setup(editor, oldSize);\n        }\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"autoresize\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/autoresize')\n//   ES2015:\n//     import 'tinymce/plugins/autoresize'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/autoresize/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,QAAQ;AACZ,YAAI,MAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,kBAAQ;AAAA;AAEV,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,iBAAiB,OAAO;AAC5B,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAGlC,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,KAAK;AAAA;AAGrB,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS,cAAc,OAAO,aAAa,cAAc;AAAA;AAEzE,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS,cAAc,GAAG;AAAA;AAE1C,UAAI,+BAA+B,SAAU,QAAQ;AACnD,eAAO,OAAO,SAAS,+BAA+B,GAAG;AAAA;AAE3D,UAAI,4BAA4B,SAAU,QAAQ;AAChD,eAAO,OAAO,SAAS,4BAA4B,IAAI;AAAA;AAEzD,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS,sBAAsB,MAAM;AAAA;AAGrD,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,QAAQ,cAAc,OAAO,QAAQ,WAAW;AAAA;AAEhE,UAAI,OAAO,SAAU,QAAQ,SAAS,OAAO,UAAU,UAAU;AAC/D,eAAO,iBAAiB,QAAQ,WAAY;AAC1C,iBAAO,QAAQ;AACf,cAAI,SAAS;AACX,iBAAK,QAAQ,SAAS,OAAO,UAAU;AAAA,qBAC9B,UAAU;AACnB;AAAA;AAAA,WAED;AAAA;AAEL,UAAI,kBAAkB,SAAU,QAAQ,OAAO;AAC7C,YAAI,OAAO,OAAO;AAClB,YAAI,MAAM;AACR,eAAK,MAAM,YAAY,QAAQ,KAAK;AACpC,cAAI,CAAC,OAAO;AACV,iBAAK,YAAY;AAAA;AAAA;AAAA;AAIvB,UAAI,qBAAqB,SAAU,KAAK,KAAK,MAAM,UAAU;AAC3D,YAAI,QAAQ,SAAS,IAAI,SAAS,KAAK,MAAM,WAAW;AACxD,eAAO,MAAM,SAAS,IAAI;AAAA;AAE5B,UAAI,uBAAuB,SAAU,SAAS;AAC5C,YAAK,aAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,mBAAmB,cAAc;AACnG,cAAI,kBAAkB;AACtB,iBAAO,gBAAgB,cAAc,QAAQ,gBAAgB,UAAU;AAAA,eAClE;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,QAAQ,SAAS,SAAS;AAC/C,YAAI,MAAM,OAAO;AACjB,YAAI,MAAM,OAAO;AACjB,YAAI,CAAC,KAAK;AACR;AAAA;AAEF,YAAI,aAAa,SAAS;AACxB,0BAAgB,QAAQ;AACxB;AAAA;AAEF,YAAI,SAAS,IAAI;AACjB,YAAI,qBAAqB,0BAA0B;AACnD,YAAI,eAAe,uBAAuB;AAC1C,YAAI,YAAY,mBAAmB,KAAK,QAAQ,cAAc;AAC9D,YAAI,eAAe,mBAAmB,KAAK,QAAQ,iBAAiB;AACpE,YAAI,gBAAgB,OAAO,eAAe,YAAY,eAAe;AACrE,YAAI,gBAAgB,GAAG;AACrB,0BAAgB;AAAA;AAElB,YAAI,kBAAkB,OAAO,eAAe;AAC5C,YAAI,oBAAoB,OAAO,0BAA0B;AACzD,YAAI,eAAe,kBAAkB;AACrC,YAAI,gBAAgB,eAAe,uBAAuB,SAAS;AACjE,yBAAe,gBAAgB;AAAA;AAEjC,YAAI,YAAY,uBAAuB;AACvC,YAAI,aAAa,eAAe,WAAW;AACzC,yBAAe;AACf,0BAAgB,QAAQ;AAAA,eACnB;AACL,0BAAgB,QAAQ;AAAA;AAE1B,YAAI,iBAAiB,QAAQ,OAAO;AAClC,cAAI,YAAY,eAAe,QAAQ;AACvC,cAAI,SAAS,OAAO,gBAAgB,UAAU,eAAe;AAC7D,kBAAQ,IAAI;AACZ,2BAAiB;AACjB,cAAI,SAAS,QAAQ,cAAc,SAAS,KAAK;AAC/C,gBAAI,MAAM,OAAO;AACjB,gBAAI,SAAS,IAAI,aAAa,IAAI;AAAA;AAEpC,cAAI,OAAO,cAAc,qBAAqB,UAAU;AACtD,mBAAO,UAAU;AAAA;AAEnB,cAAI,SAAS,UAAU,YAAY,GAAG;AACpC,mBAAO,QAAQ,SAAS;AAAA;AAAA;AAAA;AAI9B,UAAI,QAAQ,SAAU,QAAQ,SAAS;AACrC,eAAO,GAAG,QAAQ,WAAY;AAC5B,cAAI,kBAAkB,6BAA6B;AACnD,cAAI,MAAM,OAAO;AACjB,cAAI,UAAU,OAAO,SAAS,iBAAiB,EAAE,QAAQ;AACzD,cAAI,UAAU,OAAO,WAAW;AAAA,YAC9B,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB,cAAc;AAAA;AAAA;AAGlB,eAAO,GAAG,oEAAoE,SAAU,GAAG;AACzF,iBAAO,QAAQ,SAAS;AAAA;AAE1B,YAAI,uBAAuB,SAAS;AAClC,iBAAO,GAAG,QAAQ,WAAY;AAC5B,iBAAK,QAAQ,SAAS,IAAI,KAAK,WAAY;AACzC,mBAAK,QAAQ,SAAS,GAAG;AAAA;AAAA;AAAA;AAAA;AAMjC,UAAI,WAAW,SAAU,QAAQ,SAAS;AACxC,eAAO,WAAW,iBAAiB,WAAY;AAC7C,iBAAO,QAAQ;AAAA;AAAA;AAInB,wBAAmB;AACjB,iBAAS,IAAI,cAAc,SAAU,QAAQ;AAC3C,cAAI,CAAC,IAAI,OAAO,UAAU,WAAW;AACnC,mBAAO,SAAS,SAAS;AAAA;AAE3B,cAAI,CAAC,OAAO,QAAQ;AAClB,gBAAI,UAAU,KAAK;AACnB,qBAAS,QAAQ;AACjB,kBAAM,QAAQ;AAAA;AAAA;AAAA;AAKpB;AAAA;AAAA;AAAA;;;ACrLJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,qCAAQ;", "names": []}