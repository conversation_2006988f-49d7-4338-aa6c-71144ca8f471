<template>
  <div class="app-container">
    <el-form inline label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef">
      <el-form-item prop="caseId" label="项目ID">
        <el-input style="width: 320px" v-model="queryParams.caseId" placeholder="请输入项目ID" />
      </el-form-item>
      <el-form-item prop="caseId" label="项目名称">
        <el-input style="width: 320px" v-model="queryParams.caseId" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item prop="entrustingPartyId" label="资产转让方">
        <el-select v-model="queryParams.entrustingPartyId" collapse-tags collapse-tags-tooltip multiple
          placeholder="请输入或选择资产转让方" clearable filterable :reserve-keyword="false" style="width: 320px">
          <el-option v-for="item in ownerOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="产品类型" prop="name">
        <el-select v-model="queryParams.name" filterable :reserve-keyword="false" placeholder="请输入产品类型"
          @focus="searchName()" style="width: 320px">
          <el-option v-for="item in options" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="合同状态" prop="name">
        <el-select v-model="queryParams.name" filterable :reserve-keyword="false" placeholder="请输入合同状态"
          @focus="searchName()" style="width: 320px">
          <el-option v-for="item in options" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="合同名称" prop="name">
        <el-select v-model="queryParams.name" filterable :reserve-keyword="false" placeholder="请输入合同名称"
          @focus="searchName()" style="width: 320px">
          <el-option v-for="item in options" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="合同编号" prop="name">
        <el-select v-model="queryParams.name" filterable :reserve-keyword="false" placeholder="请输入合同编号"
          @focus="searchName()" style="width: 320px">
          <el-option v-for="item in options" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="合同类型" prop="name">
        <el-select v-model="queryParams.name" filterable :reserve-keyword="false" placeholder="请输入合同类型"
          @focus="searchName()" style="width: 320px">
          <el-option v-for="item in options" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="entrustingPartyId" label="申请人">
        <el-select v-model="queryParams.entrustingPartyId" collapse-tags collapse-tags-tooltip multiple
          placeholder="请输入或选择申请人" clearable filterable :reserve-keyword="false" style="width: 320px">
          <el-option v-for="item in ownerOption" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item prop="lastContactTime" label="申请时间">
        <el-date-picker v-model="queryParams.lastContactTime" value-format="YYYY-MM-DD" type="daterange" clearable
          unlink-panels range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" style="width: 320px" />
      </el-form-item>
      <el-form-item label="发起方式" prop="name">
        <el-select v-model="queryParams.name" filterable :reserve-keyword="false" placeholder="请输入发起方式"
          @focus="searchName()" style="width: 320px">
          <el-option v-for="item in options" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="印章类型" prop="name">
        <el-select v-model="queryParams.name" filterable :reserve-keyword="false" placeholder="请输入印章类型"
          @focus="searchName()" style="width: 320px">
          <el-option v-for="item in options" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <div class="operation-revealing-area mb20">
      <el-button type="primary">撤销</el-button>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-tabs v-model="activeName" @tab-change="antiShake(resetQuery)">
      <el-tab-pane v-for="(item, index) in erectNapeEnum" :key="index" :label="item.info" :name="index" />
    </el-tabs>
    <div class="table-box">
      <el-table :data="dataList" :loading="loading">
        <el-table-column label="项目ID" v-if="columns[0].visible" align="center" prop="projectId" />
        <el-table-column label="项目名称" v-if="columns[1].visible" align="center" prop="projectName" />
        <el-table-column label="资产转让方" v-if="columns[2].visible" align="center" prop="assetTransferor" />
        <el-table-column label="产品类型" v-if="columns[3].visible" align="center" prop="productType" />
        <el-table-column label="合同状态" v-if="columns[4].visible" align="center" prop="contractStatus" />
        <el-table-column label="合同名称" v-if="columns[5].visible" align="center" prop="contractName" />
        <el-table-column label="合同编号" v-if="columns[6].visible" align="center" prop="contractNo" />
        <el-table-column label="合同类型" v-if="columns[7].visible" align="center" prop="contractTyoe" />
        <el-table-column label="发起方式" v-if="columns[8].visible" align="center" prop="initiationMethod" />
        <el-table-column label="签署份数" v-if="columns[9].visible" align="center" prop="signNum" />
        <el-table-column label="印章类型" v-if="columns[10].visible" align="center" prop="sealType" />
        <el-table-column label="申请人" v-if="columns[11].visible" align="center" prop="createBy" />
        <el-table-column label="申请时间" v-if="columns[12].visible" align="center" prop="createTime" />
        <el-table-column fixed="right" width="180" label="操作">
          <template #default="{ row }">
            <div>
              <el-button type="text" v-if="['待发起合同', '合同发起失败', '已撤销'].includes(row.contractStatus)"
                @click="check(row)">新增合同</el-button>
              <el-button type="text" v-if="['合同待审批'].includes(row.contractStatus)" @click="check(row)">撤销</el-button>
              <el-button v-if="!['待发起合同'].includes(row.contractStatus)" type="text" @click="handleDetails(row)">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
  </div>
</template>

<script setup name="StartNapeList">
import { getDictProductType } from "@/api/assets/assetside";
import { getOwners } from "@/api/assets/casemanage";
// import { erectNapeEnum } from "@/utils/enum";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const data = reactive({
  queryParams: { pageNum: 1, pageSize: 10 },
});

const options = ref([]);

const ownerOption = ref([]);

const erectNapeEnum = ref([
  { code: '', info: "待添加" },
  { code: '', info: "决策未通过" },
  { code: '', info: "决策通过" },
  { code: '', info: "全部" },
])


const total = ref(1);
const dataList = ref([
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目',
    assetTransferor: '',
    productType: '',
    contractStatus: '待发起合同',
    contractName: '',
    contractNo: '',
    contractTyoe: '',
    initiationMethod: '',
    signNum: '',
    sealType: '',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目',
    assetTransferor: '',
    productType: '',
    contractStatus: '合同待审批',
    contractName: '',
    contractNo: '',
    contractTyoe: '',
    initiationMethod: '',
    signNum: '',
    sealType: '',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目',
    assetTransferor: '',
    productType: '',
    contractStatus: '合同审批中',
    contractName: '2023城投地产合同申请',
    contractNo: '',
    contractTyoe: '',
    initiationMethod: '线下',
    signNum: '',
    sealType: '私人章',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220003',
    projectName: '2024不良资产收购项目11',
    assetTransferor: '',
    productType: '',
    contractStatus: '合同发起失败',
    contractName: '2024城投地产合同申请',
    contractNo: '',
    contractTyoe: '',
    initiationMethod: '线上',
    signNum: '',
    sealType: '公章',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220004',
    projectName: '2024不良资产收购项目33',
    assetTransferor: '',
    productType: '',
    contractStatus: '合同发起成功',
    contractName: '',
    contractNo: '',
    contractTyoe: '',
    initiationMethod: '',
    signNum: '',
    sealType: '',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
  {
    projectId: 'zcb202411220001',
    projectName: '2024不良资产收购项目',
    assetTransferor: '',
    productType: '',
    contractStatus: '已撤销',
    contractName: '',
    contractNo: '',
    contractTyoe: '',
    initiationMethod: '',
    signNum: '',
    sealType: '',
    createBy: '胡图图',
    createTime: '2024-11-10 12:00:00',
  },
]);
const loading = ref(false);
const showSearch = ref(false);
const { queryParams } = toRefs(data);
const columns = ref([
  { "key": 0, "label": "项目ID", "visible": true },
  { "key": 1, "label": "项目名称", "visible": true },
  { "key": 2, "label": "资产转让方", "visible": true },
  { "key": 3, "label": "产品类型", "visible": true },
  { "key": 4, "label": "合同状态", "visible": true },
  { "key": 5, "label": "合同名称", "visible": true },
  { "key": 6, "label": "合同编号", "visible": true },
  { "key": 7, "label": "合同类型", "visible": true },
  { "key": 8, "label": "发起方式", "visible": true },
  { "key": 9, "label": "签署份数", "visible": true },
  { "key": 10, "label": "印章类型", "visible": true },
  { "key": 11, "label": "申请人", "visible": true },
  { "key": 12, "label": "申请时间", "visible": true }
]);

// 获取转让方
getOwners().then((res) => {
  ownerOption.value = res.data;
});

function searchName() {
  getDictProductType().then((res) => {
    options.value = res.data;
  });
}

function getList() { }
function resetQuery() {
  queryParams.value = { pageNum: 1, pageSize: 10 };
  getList();
}
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleDetails(row) {
  const query = { path: route.path, pageType: 'contract' , progressStatus: 2}
  router.push({ path: `/dueDiligence/projectInfo`, query })
}
function handleOpenDailog(refName) {
  proxy.$refs[refName].openDialog();
}
</script>

<style lang="scss" scoped></style>