<template>
    <div class="app-container">
        <el-form inline label-width="100px" :class="{ 'form-h50': !showSearch }" ref="queryRef">
            <el-form-item prop="caseId" label="案件ID">
                <el-input style="width:240px;" v-model="queryParams.caseId" placeholder="请输入案件ID" />
            </el-form-item>
            <el-form-item prop="callFrom" label="呼叫时间">
                <el-input style="width:240px;" v-model="queryParams.callFrom" placeholder="请输入呼叫时间" />
            </el-form-item>
            <el-form-item prop="caseId" label="主叫号码">
                <el-input style="width:240px;" v-model="queryParams.caseId" placeholder="请输入主叫号码" />
            </el-form-item>
            <el-form-item prop="callTo" label="被叫号码">
                <el-input style="width:240px;" v-model="queryParams.callTo" placeholder="请输入被叫号码" />
            </el-form-item>
            <el-form-item prop="cname" label="机构名称">
                <el-select v-model="queryParams.cname" placeholder="请选择机构名称" clearable multiple collapse-tags
                    collapse-tags-tooltip filterable :reserve-keyword="false" style="width: 240px">
                    <el-option-group v-for="group in teamOption" :key="group.label" :label="group.label">
                        <el-option v-for="item in group.children" :key="item.id" :label="item.label"
                            :value="item.label" />
                    </el-option-group>
                </el-select>
            </el-form-item>
            <el-form-item prop="agentModel" label="坐席模型">
                <el-select v-model="queryParams.agentModel" style="width:240px" multiple collapse-tags
                    collapse-tags-tooltip clearable filterable placeholder="请选择坐席模型">
                    <el-option v-for="item in agentOption" :key="item.modelName" :value="item.modelName"
                        :label="item.modleName" />
                </el-select>
            </el-form-item>
            <el-form-item prop="customerModel" label="客户模型">
                <el-select v-model="queryParams.customerModel" style="width:240px" multiple collapse-tags clearable
                    filterable collapse-tags-tooltip placeholder="请选择客户模型">
                    <el-option v-for="item in customerOption" :key="item.modelName" :value="item.modelName"
                        :label="item.modleName" />
                </el-select>
            </el-form-item>
            <el-form-item prop="isArtificial" label="是否复检">
                <el-select v-model="queryParams.isArtificial" style="width:240px;" placeholder="请选择是否复检">
                    <el-option v-for="(v, i) in isNoEnum" :key="v" :value="i" :label="v" />
                </el-select>
            </el-form-item>
            <el-form-item prop="artificialResult" label="复检结果">
                <el-select v-model="queryParams.artificialResult" style="width:240px;" placeholder="请选择复检结果">
                    <el-option v-for="(v, i) in recheckEnum" :key="v" :value="i" :label="v" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button @click="antiShake(resetQuery)">重置</el-button>
            <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
        </div>
        <div class="operation-revealing-area mb20">
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
        </div>
        <div class="table-box">
            <el-table :data="dataList" v-loading="loading" @sort-change="handleSortChange">
                <el-table-column label="ID" v-if="columns[0].visible" align="center" sortable="id" prop="id"
                    width="100" />
                <el-table-column label="呼叫时间" v-if="columns[1].visible" align="center" prop="callTime" width="160" />
                <el-table-column label="案件ID" v-if="columns[2].visible" align="center" sortable="caseId" prop="caseId"
                    width="100" />
                <el-table-column label="主叫号码" v-if="columns[3].visible" align="center" prop="callFrom" width="120" />
                <el-table-column label="被叫号码" v-if="columns[4].visible" align="center" prop="callTo" width="120" />
                <el-table-column label="机构名称" show-overflow-tooltip v-if="columns[5].visible" align="center"
                    prop="cname" min-width="180" />
                <el-table-column label="通话时长" v-if="columns[6].visible" align="center" prop="agentDuration"
                    width="100" />
                <el-table-column label="坐席模型" v-if="columns[7].visible" align="center" prop="agentModel"
                    min-width="180">
                    <template #default="{ row }">
                        <Tooltip :content="row.agentModel" :length="10" />
                    </template>
                </el-table-column>
                <el-table-column label="坐席标签" v-if="columns[8].visible" align="center" prop="agentLabel"
                    min-width="180">
                    <template #default="{ row }">
                        <Tooltip :content="row.agentLabel" :length="10" />
                    </template>
                </el-table-column>
                <el-table-column label="客户模型" v-if="columns[9].visible" align="center" prop="customerModel"
                    min-width="180">
                    <template #default="{ row }">
                        <Tooltip :content="row.customerModel" :length="10" />
                    </template>
                </el-table-column>
                <el-table-column label="客户标签" v-if="columns[10].visible" align="center" prop="customerLabel"
                    min-width="180">
                    <template #default="{ row }">
                        <Tooltip :content="row.customerLabel" :length="10" />
                    </template>
                </el-table-column>
                <el-table-column label="复检人" v-if="columns[11].visible" align="center" prop="artificialName"
                    width="120" />
                <el-table-column label="复检时间" v-if="columns[12].visible" align="center" prop="artificialTime"
                    width="160" />
                <el-table-column label="复检结果" v-if="columns[13].visible" align="center"
                    :formatter="row => recheckEnum[row.artificialResult]" prop="result" width="100" />
                <el-table-column fixed="right" label="操作">
                    <template #default="{ row }">
                        <div>
                            <el-button type="text" @click="handleCheck(row)">查看详情</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
    </div>
</template>

<script setup>
import { getTeamTree } from "@/api/team/team";
import { qcListApi } from '@/api/qcService/listQc'
import { getAgentOptionApi, getCustomerOptionApi } from "@/api/options";
import { recheckEnum, isNoEnum } from '@/utils/enum';
const { proxy } = getCurrentInstance()

const route = useRoute()
const router = useRouter()
const queryParams = ref({
    pageNum: 1, pageSize: 10
})
const total = ref(1)
const dataList = ref([])
const teamOption = ref([]);
const agentOption = ref([])
const customerOption = ref([])
const loading = ref(false)
const showSearch = ref(false)
const columns = ref([
    { "key": 0, "label": "ID", "visible": true },
    { "key": 1, "label": "呼叫时间", "visible": true },
    { "key": 2, "label": "案件ID", "visible": true },
    { "key": 3, "label": "主叫号码", "visible": true },
    { "key": 4, "label": "被叫号码", "visible": true },
    { "key": 5, "label": "机构名称", "visible": true },
    { "key": 6, "label": "通话时长", "visible": true },
    { "key": 7, "label": "坐席模型", "visible": true },
    { "key": 8, "label": "坐席标签", "visible": true },
    { "key": 9, "label": "客户模型", "visible": true },
    { "key": 10, "label": "客户标签", "visible": true },
    { "key": 11, "label": "复检人", "visible": true },
    { "key": 12, "label": "复检时间", "visible": true },
    { "key": 13, "label": "复检结果", "visible": true }
])
getList()
function getList() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    loading.value = true
    for (const key in reqForm) {
        if (Array.isArray(reqForm[key])) {
            reqForm[key] = String(reqForm[key])
        }
    }
    qcListApi(reqForm).then(res => {
        total.value = res.total
        dataList.value = res.rows
    }).finally(() => loading.value = false)
}
function resetQuery() {
    queryParams.value = { pageNum: 1, pageSize: 10 }
    getList()
}
function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
}
function handleSortChange({ prop, order }) {
    const orderObj = { id: 1, caseId: 2 }
    delete queryParams.value.orderBy
    delete queryParams.value.sortOrder
    if (order) {
        queryParams.value.orderBy = orderObj[prop]
        queryParams.value.sortOrder = proxy.orderEnum[order]
    }
    handleQuery()
}
function handleCheck(row) {
    const query = { callRecordId: row.callRecordId, path: route.path }
    const path = `/risk/calllog-details/textPlay`
    router.push({ path, query })
}

getAgentOptionFun()
function getAgentOptionFun() {
    getAgentOptionApi().then(res => {
        agentOption.value = res.data
    })

}
getCustomerOptionFun()
function getCustomerOptionFun() {
    getCustomerOptionApi().then(res => {
        customerOption.value = res.data
    })
}
// 获取机构
getTeamTreeFun()
function getTeamTreeFun() {
    getTeamTree().then((res) => {
        teamOption.value = res.data;
    })
}

</script>

<style lang="scss" scoped></style>