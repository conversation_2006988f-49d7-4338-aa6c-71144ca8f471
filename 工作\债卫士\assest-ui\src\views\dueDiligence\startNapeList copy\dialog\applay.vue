<template>
    <el-dialog title="立项申请" v-model="open" width="550px" append-to-body :before-close="cancel">
        <el-form ref="formRef" :model="form" :key="form" :rules="rules" label-width="100px">
            <el-form-item prop="type" label="类型">
                <el-input v-model="form.type" placeholder="请输入类型" style="width:450px" />
            </el-form-item>
            <el-form-item prop="assetName" label="资产名称">
                <el-input v-model="form.assetName" placeholder="请输入资产名称" style="width:450px" />
            </el-form-item>
            <el-form-item prop="projectName" label="项目名称">
                <el-input v-model="form.projectName" placeholder="请输入项目名称" style="width:450px" />
            </el-form-item>
            <el-form-item prop="reason" label="申请原因">
                <el-input v-model="form.reason" type="textarea" maxlength="200" style="width:450px" :rows="3"
                    show-word-limit placeholder="请输入申请原因" />
            </el-form-item>
            <el-form-item prop="report" label="立项报告">
                <FileUpload ref="fileUploadRef" v-model:fileList="fileList" uploadFileUrl="/file/upload"
                    :fileType="fileType" />
                <span class="report-tig">上传的凭证格式为：.jpg，.png，.xls，.docx，.doc，pdf；文件数量限制1个</span>
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button type="primary" @click="sumbit">确认</el-button>
                <el-button @click="cancel">取消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import FileUpload from '@/components/FileUpload';
import { getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
const props = defineProps({ getList: { type: Function } })
const open = ref(false)
const typeOption = ref([])
const assetOption = ref([])
const projectOption = ref([])
// 文件上传
const fileList = ref([])
const fileType = ref(['jpg', 'png', 'xls', 'docx', 'doc', 'pdf'])
const data = reactive({
    form: {},
    rules: {
        type: [{ required: true, message: '请输入类型', trigger: 'blur' }],
        assetName: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
        projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        report: [{ required: true, message: '请上传立项报告', trigger: 'blur' }],
    }
})
const { form, rules } = toRefs(data)
function sumbit() {
    form.value.report = fileList.value
    nextTick(() => {
        proxy.$refs['formRef'].validate(valid => {
            if (valid) {
                props.getList && props.getList()
                cancel()
            }
        })
    })

}
function openDialog(data) {
    open.value = true
}
function cancel() {
    open.value = false
    form.value = {}
}

defineExpose({ openDialog })
</script>

<style lang="scss" scoped>
.report-tig {
    font-size: 12px;
}
</style>