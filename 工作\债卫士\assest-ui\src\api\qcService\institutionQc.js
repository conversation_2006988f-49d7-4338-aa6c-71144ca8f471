import request from '@/utils/request'
//机构质检列表--列表
export function teamQcListApi(query) {
    return request({
        url: '/quality/team/list',
        method: 'get',
        params: query
    })
}
//机构质检列表--编辑
export function editTeamQcApi(data) {
    return request({
        url: '/quality/team/edit',
        method: 'post',
        data
    })
}
//机构质检列表--获取质检配置
export function getConfigApi(data) {
    return request({
        url: '/quality/team/getConfig',
        method: 'post',
        data
    })
}
//机构质检列表--新增质检配置
export function insertConfigApi(data) {
    return request({
        url: '/quality/team/insertConfig',
        method: 'post',
        data
    })
}
