{"version": 3, "sources": ["../tinymce/plugins/help/plugin.js", "../tinymce/plugins/help/index.js", "dep:tinymce_plugins_help"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var get$1 = function (customTabs) {\n      var addTab = function (spec) {\n        var currentCustomTabs = customTabs.get();\n        currentCustomTabs[spec.name] = spec;\n        customTabs.set(currentCustomTabs);\n      };\n      return { addTab: addTab };\n    };\n\n    var register$1 = function (editor, dialogOpener) {\n      editor.addCommand('mceHelp', dialogOpener);\n    };\n\n    var register = function (editor, dialogOpener) {\n      editor.ui.registry.addButton('help', {\n        icon: 'help',\n        tooltip: 'Help',\n        onAction: dialogOpener\n      });\n      editor.ui.registry.addMenuItem('help', {\n        text: 'Help',\n        icon: 'help',\n        shortcut: 'Alt+0',\n        onAction: dialogOpener\n      });\n    };\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var nativeIndexOf = Array.prototype.indexOf;\n    var rawIndexOf = function (ts, t) {\n      return nativeIndexOf.call(ts, t);\n    };\n    var contains = function (xs, x) {\n      return rawIndexOf(xs, x) > -1;\n    };\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var filter = function (xs, pred) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    var findUntil = function (xs, pred, until) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    var find = function (xs, pred) {\n      return findUntil(xs, pred, never);\n    };\n\n    var keys = Object.keys;\n    var hasOwnProperty = Object.hasOwnProperty;\n    var get = function (obj, key) {\n      return has(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n\n    var cat = function (arr) {\n      var r = [];\n      var push = function (x) {\n        r.push(x);\n      };\n      for (var i = 0; i < arr.length; i++) {\n        arr[i].each(push);\n      }\n      return r;\n    };\n\n    var getHelpTabs = function (editor) {\n      return Optional.from(editor.getParam('help_tabs'));\n    };\n    var getForcedPlugins = function (editor) {\n      return editor.getParam('forced_plugins');\n    };\n\n    var description = '<h1>Editor UI keyboard navigation</h1>\\n\\n<h2>Activating keyboard navigation</h2>\\n\\n<p>The sections of the outer UI of the editor - the menubar, toolbar, sidebar and footer - are all keyboard navigable. As such, there are multiple ways to activate keyboard navigation:</p>\\n<ul>\\n  <li>Focus the menubar: Alt + F9 (Windows) or &#x2325;F9 (MacOS)</li>\\n  <li>Focus the toolbar: Alt + F10 (Windows) or &#x2325;F10 (MacOS)</li>\\n  <li>Focus the footer: Alt + F11 (Windows) or &#x2325;F11 (MacOS)</li>\\n</ul>\\n\\n<p>Focusing the menubar or toolbar will start keyboard navigation at the first item in the menubar or toolbar, which will be highlighted with a gray background. Focusing the footer will start keyboard navigation at the first item in the element path, which will be highlighted with an underline. </p>\\n\\n<h2>Moving between UI sections</h2>\\n\\n<p>When keyboard navigation is active, pressing tab will move the focus to the next major section of the UI, where applicable. These sections are:</p>\\n<ul>\\n  <li>the menubar</li>\\n  <li>each group of the toolbar </li>\\n  <li>the sidebar</li>\\n  <li>the element path in the footer </li>\\n  <li>the wordcount toggle button in the footer </li>\\n  <li>the branding link in the footer </li>\\n  <li>the editor resize handle in the footer</li>\\n</ul>\\n\\n<p>Pressing shift + tab will move backwards through the same sections, except when moving from the footer to the toolbar. Focusing the element path then pressing shift + tab will move focus to the first toolbar group, not the last.</p>\\n\\n<h2>Moving within UI sections</h2>\\n\\n<p>Keyboard navigation within UI sections can usually be achieved using the left and right arrow keys. This includes:</p>\\n<ul>\\n  <li>moving between menus in the menubar</li>\\n  <li>moving between buttons in a toolbar group</li>\\n  <li>moving between items in the element path</li>\\n</ul>\\n\\n<p>In all these UI sections, keyboard navigation will cycle within the section. For example, focusing the last button in a toolbar group then pressing right arrow will move focus to the first item in the same toolbar group. </p>\\n\\n<h1>Executing buttons</h1>\\n\\n<p>To execute a button, navigate the selection to the desired button and hit space or enter.</p>\\n\\n<h1>Opening, navigating and closing menus</h1>\\n\\n<p>When focusing a menubar button or a toolbar button with a menu, pressing space, enter or down arrow will open the menu. When the menu opens the first item will be selected. To move up or down the menu, press the up or down arrow key respectively. This is the same for submenus, which can also be opened and closed using the left and right arrow keys.</p>\\n\\n<p>To close any active menu, hit the escape key. When a menu is closed the selection will be restored to its previous selection. This also works for closing submenus.</p>\\n\\n<h1>Context toolbars and menus</h1>\\n\\n<p>To focus an open context toolbar such as the table context toolbar, press Ctrl + F9 (Windows) or &#x2303;F9 (MacOS).</p>\\n\\n<p>Context toolbar navigation is the same as toolbar navigation, and context menu navigation is the same as standard menu navigation.</p>\\n\\n<h1>Dialog navigation</h1>\\n\\n<p>There are two types of dialog UIs in TinyMCE: tabbed dialogs and non-tabbed dialogs.</p>\\n\\n<p>When a non-tabbed dialog is opened, the first interactive component in the dialog will be focused. Users can navigate between interactive components by pressing tab. This includes any footer buttons. Navigation will cycle back to the first dialog component if tab is pressed while focusing the last component in the dialog. Pressing shift + tab will navigate backwards.</p>\\n\\n<p>When a tabbed dialog is opened, the first button in the tab menu is focused. Pressing tab will navigate to the first interactive component in that tab, and will cycle through the tab\\u2019s components, the footer buttons, then back to the tab button. To switch to another tab, focus the tab button for the current tab, then use the arrow keys to cycle through the tab buttons.</p>';\n    var tab$3 = function () {\n      var body = {\n        type: 'htmlpanel',\n        presets: 'document',\n        html: description\n      };\n      return {\n        name: 'keyboardnav',\n        title: 'Keyboard Navigation',\n        items: [body]\n      };\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var convertText = function (source) {\n      var mac = {\n        alt: '&#x2325;',\n        ctrl: '&#x2303;',\n        shift: '&#x21E7;',\n        meta: '&#x2318;',\n        access: '&#x2303;&#x2325;'\n      };\n      var other = {\n        meta: 'Ctrl ',\n        access: 'Shift + Alt '\n      };\n      var replace = global$2.mac ? mac : other;\n      var shortcut = source.split('+');\n      var updated = map(shortcut, function (segment) {\n        var search = segment.toLowerCase().trim();\n        return has(replace, search) ? replace[search] : segment;\n      });\n      return global$2.mac ? updated.join('').replace(/\\s/, '') : updated.join('+');\n    };\n\n    var shortcuts = [\n      {\n        shortcuts: ['Meta + B'],\n        action: 'Bold'\n      },\n      {\n        shortcuts: ['Meta + I'],\n        action: 'Italic'\n      },\n      {\n        shortcuts: ['Meta + U'],\n        action: 'Underline'\n      },\n      {\n        shortcuts: ['Meta + A'],\n        action: 'Select all'\n      },\n      {\n        shortcuts: [\n          'Meta + Y',\n          'Meta + Shift + Z'\n        ],\n        action: 'Redo'\n      },\n      {\n        shortcuts: ['Meta + Z'],\n        action: 'Undo'\n      },\n      {\n        shortcuts: ['Access + 1'],\n        action: 'Heading 1'\n      },\n      {\n        shortcuts: ['Access + 2'],\n        action: 'Heading 2'\n      },\n      {\n        shortcuts: ['Access + 3'],\n        action: 'Heading 3'\n      },\n      {\n        shortcuts: ['Access + 4'],\n        action: 'Heading 4'\n      },\n      {\n        shortcuts: ['Access + 5'],\n        action: 'Heading 5'\n      },\n      {\n        shortcuts: ['Access + 6'],\n        action: 'Heading 6'\n      },\n      {\n        shortcuts: ['Access + 7'],\n        action: 'Paragraph'\n      },\n      {\n        shortcuts: ['Access + 8'],\n        action: 'Div'\n      },\n      {\n        shortcuts: ['Access + 9'],\n        action: 'Address'\n      },\n      {\n        shortcuts: ['Alt + 0'],\n        action: 'Open help dialog'\n      },\n      {\n        shortcuts: ['Alt + F9'],\n        action: 'Focus to menubar'\n      },\n      {\n        shortcuts: ['Alt + F10'],\n        action: 'Focus to toolbar'\n      },\n      {\n        shortcuts: ['Alt + F11'],\n        action: 'Focus to element path'\n      },\n      {\n        shortcuts: ['Ctrl + F9'],\n        action: 'Focus to contextual toolbar'\n      },\n      {\n        shortcuts: ['Shift + Enter'],\n        action: 'Open popup menu for split buttons'\n      },\n      {\n        shortcuts: ['Meta + K'],\n        action: 'Insert link (if link plugin activated)'\n      },\n      {\n        shortcuts: ['Meta + S'],\n        action: 'Save (if save plugin activated)'\n      },\n      {\n        shortcuts: ['Meta + F'],\n        action: 'Find (if searchreplace plugin activated)'\n      },\n      {\n        shortcuts: ['Meta + Shift + F'],\n        action: 'Switch to or from fullscreen mode'\n      }\n    ];\n\n    var tab$2 = function () {\n      var shortcutList = map(shortcuts, function (shortcut) {\n        var shortcutText = map(shortcut.shortcuts, convertText).join(' or ');\n        return [\n          shortcut.action,\n          shortcutText\n        ];\n      });\n      var tablePanel = {\n        type: 'table',\n        header: [\n          'Action',\n          'Shortcut'\n        ],\n        cells: shortcutList\n      };\n      return {\n        name: 'shortcuts',\n        title: 'Handy Shortcuts',\n        items: [tablePanel]\n      };\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.I18n');\n\n    var urls = map([\n      {\n        key: 'advlist',\n        name: 'Advanced List'\n      },\n      {\n        key: 'anchor',\n        name: 'Anchor'\n      },\n      {\n        key: 'autolink',\n        name: 'Autolink'\n      },\n      {\n        key: 'autoresize',\n        name: 'Autoresize'\n      },\n      {\n        key: 'autosave',\n        name: 'Autosave'\n      },\n      {\n        key: 'bbcode',\n        name: 'BBCode'\n      },\n      {\n        key: 'charmap',\n        name: 'Character Map'\n      },\n      {\n        key: 'code',\n        name: 'Code'\n      },\n      {\n        key: 'codesample',\n        name: 'Code Sample'\n      },\n      {\n        key: 'colorpicker',\n        name: 'Color Picker'\n      },\n      {\n        key: 'directionality',\n        name: 'Directionality'\n      },\n      {\n        key: 'emoticons',\n        name: 'Emoticons'\n      },\n      {\n        key: 'fullpage',\n        name: 'Full Page'\n      },\n      {\n        key: 'fullscreen',\n        name: 'Full Screen'\n      },\n      {\n        key: 'help',\n        name: 'Help'\n      },\n      {\n        key: 'hr',\n        name: 'Horizontal Rule'\n      },\n      {\n        key: 'image',\n        name: 'Image'\n      },\n      {\n        key: 'imagetools',\n        name: 'Image Tools'\n      },\n      {\n        key: 'importcss',\n        name: 'Import CSS'\n      },\n      {\n        key: 'insertdatetime',\n        name: 'Insert Date/Time'\n      },\n      {\n        key: 'legacyoutput',\n        name: 'Legacy Output'\n      },\n      {\n        key: 'link',\n        name: 'Link'\n      },\n      {\n        key: 'lists',\n        name: 'Lists'\n      },\n      {\n        key: 'media',\n        name: 'Media'\n      },\n      {\n        key: 'nonbreaking',\n        name: 'Nonbreaking'\n      },\n      {\n        key: 'noneditable',\n        name: 'Noneditable'\n      },\n      {\n        key: 'pagebreak',\n        name: 'Page Break'\n      },\n      {\n        key: 'paste',\n        name: 'Paste'\n      },\n      {\n        key: 'preview',\n        name: 'Preview'\n      },\n      {\n        key: 'print',\n        name: 'Print'\n      },\n      {\n        key: 'quickbars',\n        name: 'Quick Toolbars'\n      },\n      {\n        key: 'save',\n        name: 'Save'\n      },\n      {\n        key: 'searchreplace',\n        name: 'Search and Replace'\n      },\n      {\n        key: 'spellchecker',\n        name: 'Spell Checker'\n      },\n      {\n        key: 'tabfocus',\n        name: 'Tab Focus'\n      },\n      {\n        key: 'table',\n        name: 'Table'\n      },\n      {\n        key: 'template',\n        name: 'Template'\n      },\n      {\n        key: 'textcolor',\n        name: 'Text Color'\n      },\n      {\n        key: 'textpattern',\n        name: 'Text Pattern'\n      },\n      {\n        key: 'toc',\n        name: 'Table of Contents'\n      },\n      {\n        key: 'visualblocks',\n        name: 'Visual Blocks'\n      },\n      {\n        key: 'visualchars',\n        name: 'Visual Characters'\n      },\n      {\n        key: 'wordcount',\n        name: 'Word Count'\n      },\n      {\n        key: 'a11ychecker',\n        name: 'Accessibility Checker',\n        type: 'premium'\n      },\n      {\n        key: 'advcode',\n        name: 'Advanced Code Editor',\n        type: 'premium'\n      },\n      {\n        key: 'advtable',\n        name: 'Advanced Tables',\n        type: 'premium'\n      },\n      {\n        key: 'autocorrect',\n        name: 'Autocorrect',\n        type: 'premium'\n      },\n      {\n        key: 'casechange',\n        name: 'Case Change',\n        type: 'premium'\n      },\n      {\n        key: 'checklist',\n        name: 'Checklist',\n        type: 'premium'\n      },\n      {\n        key: 'export',\n        name: 'Export',\n        type: 'premium'\n      },\n      {\n        key: 'mediaembed',\n        name: 'Enhanced Media Embed',\n        type: 'premium'\n      },\n      {\n        key: 'formatpainter',\n        name: 'Format Painter',\n        type: 'premium'\n      },\n      {\n        key: 'linkchecker',\n        name: 'Link Checker',\n        type: 'premium'\n      },\n      {\n        key: 'mentions',\n        name: 'Mentions',\n        type: 'premium'\n      },\n      {\n        key: 'pageembed',\n        name: 'Page Embed',\n        type: 'premium'\n      },\n      {\n        key: 'permanentpen',\n        name: 'Permanent Pen',\n        type: 'premium'\n      },\n      {\n        key: 'powerpaste',\n        name: 'PowerPaste',\n        type: 'premium'\n      },\n      {\n        key: 'rtc',\n        name: 'Real-Time Collaboration',\n        type: 'premium'\n      },\n      {\n        key: 'tinymcespellchecker',\n        name: 'Spell Checker Pro',\n        type: 'premium'\n      },\n      {\n        key: 'tinycomments',\n        name: 'Tiny Comments',\n        type: 'premium',\n        slug: 'comments'\n      },\n      {\n        key: 'tinydrive',\n        name: 'Tiny Drive',\n        type: 'premium'\n      }\n    ], function (item) {\n      return __assign(__assign({}, item), {\n        type: item.type || 'opensource',\n        slug: item.slug || item.key\n      });\n    });\n\n    var tab$1 = function (editor) {\n      var availablePlugins = function () {\n        var premiumPlugins = filter(urls, function (_a) {\n          var key = _a.key, type = _a.type;\n          return key !== 'autocorrect' && type === 'premium';\n        });\n        var premiumPluginList = map(premiumPlugins, function (plugin) {\n          return '<li>' + global$1.translate(plugin.name) + '</li>';\n        }).join('');\n        return '<div data-mce-tabstop=\"1\" tabindex=\"-1\">' + '<p><b>' + global$1.translate('Premium plugins:') + '</b></p>' + '<ul>' + premiumPluginList + '<li class=\"tox-help__more-link\" \"><a href=\"https://www.tiny.cloud/pricing/?utm_campaign=editor_referral&utm_medium=help_dialog&utm_source=tinymce\" target=\"_blank\">' + global$1.translate('Learn more...') + '</a></li>' + '</ul>' + '</div>';\n      };\n      var makeLink = function (p) {\n        return '<a href=\"' + p.url + '\" target=\"_blank\" rel=\"noopener\">' + p.name + '</a>';\n      };\n      var maybeUrlize = function (editor, key) {\n        return find(urls, function (x) {\n          return x.key === key;\n        }).fold(function () {\n          var getMetadata = editor.plugins[key].getMetadata;\n          return typeof getMetadata === 'function' ? makeLink(getMetadata()) : key;\n        }, function (x) {\n          var name = x.type === 'premium' ? x.name + '*' : x.name;\n          return makeLink({\n            name: name,\n            url: 'https://www.tiny.cloud/docs/plugins/' + x.type + '/' + x.slug\n          });\n        });\n      };\n      var getPluginKeys = function (editor) {\n        var keys$1 = keys(editor.plugins);\n        var forced_plugins = getForcedPlugins(editor);\n        return forced_plugins === undefined ? keys$1 : filter(keys$1, function (k) {\n          return !contains(forced_plugins, k);\n        });\n      };\n      var pluginLister = function (editor) {\n        var pluginKeys = getPluginKeys(editor);\n        var pluginLis = map(pluginKeys, function (key) {\n          return '<li>' + maybeUrlize(editor, key) + '</li>';\n        });\n        var count = pluginLis.length;\n        var pluginsString = pluginLis.join('');\n        var html = '<p><b>' + global$1.translate([\n          'Plugins installed ({0}):',\n          count\n        ]) + '</b></p>' + '<ul>' + pluginsString + '</ul>';\n        return html;\n      };\n      var installedPlugins = function (editor) {\n        if (editor == null) {\n          return '';\n        }\n        return '<div data-mce-tabstop=\"1\" tabindex=\"-1\">' + pluginLister(editor) + '</div>';\n      };\n      var htmlPanel = {\n        type: 'htmlpanel',\n        presets: 'document',\n        html: [\n          installedPlugins(editor),\n          availablePlugins()\n        ].join('')\n      };\n      return {\n        name: 'plugins',\n        title: 'Plugins',\n        items: [htmlPanel]\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.EditorManager');\n\n    var tab = function () {\n      var getVersion = function (major, minor) {\n        return major.indexOf('@') === 0 ? 'X.X.X' : major + '.' + minor;\n      };\n      var version = getVersion(global.majorVersion, global.minorVersion);\n      var changeLogLink = '<a href=\"https://www.tiny.cloud/docs/changelog/?utm_campaign=editor_referral&utm_medium=help_dialog&utm_source=tinymce\" target=\"_blank\">TinyMCE ' + version + '</a>';\n      var htmlPanel = {\n        type: 'htmlpanel',\n        html: '<p>' + global$1.translate([\n          'You are using {0}',\n          changeLogLink\n        ]) + '</p>',\n        presets: 'document'\n      };\n      return {\n        name: 'versions',\n        title: 'Version',\n        items: [htmlPanel]\n      };\n    };\n\n    var parseHelpTabsSetting = function (tabsFromSettings, tabs) {\n      var newTabs = {};\n      var names = map(tabsFromSettings, function (t) {\n        if (typeof t === 'string') {\n          if (has(tabs, t)) {\n            newTabs[t] = tabs[t];\n          }\n          return t;\n        } else {\n          newTabs[t.name] = t;\n          return t.name;\n        }\n      });\n      return {\n        tabs: newTabs,\n        names: names\n      };\n    };\n    var getNamesFromTabs = function (tabs) {\n      var names = keys(tabs);\n      var idx = names.indexOf('versions');\n      if (idx !== -1) {\n        names.splice(idx, 1);\n        names.push('versions');\n      }\n      return {\n        tabs: tabs,\n        names: names\n      };\n    };\n    var parseCustomTabs = function (editor, customTabs) {\n      var _a;\n      var shortcuts = tab$2();\n      var nav = tab$3();\n      var plugins = tab$1(editor);\n      var versions = tab();\n      var tabs = __assign((_a = {}, _a[shortcuts.name] = shortcuts, _a[nav.name] = nav, _a[plugins.name] = plugins, _a[versions.name] = versions, _a), customTabs.get());\n      return getHelpTabs(editor).fold(function () {\n        return getNamesFromTabs(tabs);\n      }, function (tabsFromSettings) {\n        return parseHelpTabsSetting(tabsFromSettings, tabs);\n      });\n    };\n    var init = function (editor, customTabs) {\n      return function () {\n        var _a = parseCustomTabs(editor, customTabs), tabs = _a.tabs, names = _a.names;\n        var foundTabs = map(names, function (name) {\n          return get(tabs, name);\n        });\n        var dialogTabs = cat(foundTabs);\n        var body = {\n          type: 'tabpanel',\n          tabs: dialogTabs\n        };\n        editor.windowManager.open({\n          title: 'Help',\n          size: 'medium',\n          body: body,\n          buttons: [{\n              type: 'cancel',\n              name: 'close',\n              text: 'Close',\n              primary: true\n            }],\n          initialData: {}\n        });\n      };\n    };\n\n    function Plugin () {\n      global$3.add('help', function (editor) {\n        var customTabs = Cell({});\n        var api = get$1(customTabs);\n        var dialogOpener = init(editor, customTabs);\n        register(editor, dialogOpener);\n        register$1(editor, dialogOpener);\n        editor.shortcuts.add('Alt+0', 'Open help dialog', 'mceHelp');\n        return api;\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"help\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/help')\n//   ES2015:\n//     import 'tinymce/plugins/help'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/help/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,QAAQ;AACZ,YAAI,OAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,kBAAQ;AAAA;AAEV,eAAO;AAAA,UACL,KAAK;AAAA,UACL;AAAA;AAAA;AAIJ,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,QAAQ,SAAU,YAAY;AAChC,YAAI,SAAS,SAAU,MAAM;AAC3B,cAAI,oBAAoB,WAAW;AACnC,4BAAkB,KAAK,QAAQ;AAC/B,qBAAW,IAAI;AAAA;AAEjB,eAAO,EAAE;AAAA;AAGX,UAAI,aAAa,SAAU,QAAQ,cAAc;AAC/C,eAAO,WAAW,WAAW;AAAA;AAG/B,UAAI,WAAW,SAAU,QAAQ,cAAc;AAC7C,eAAO,GAAG,SAAS,UAAU,QAAQ;AAAA,UACnC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA;AAEZ,eAAO,GAAG,SAAS,YAAY,QAAQ;AAAA,UACrC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,UAAU;AAAA;AAAA;AAId,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,gBAAgB,MAAM,UAAU;AACpC,UAAI,aAAa,SAAU,IAAI,GAAG;AAChC,eAAO,cAAc,KAAK,IAAI;AAAA;AAEhC,UAAI,WAAW,SAAU,IAAI,GAAG;AAC9B,eAAO,WAAW,IAAI,KAAK;AAAA;AAE7B,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,cAAE,KAAK;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,YAAY,SAAU,IAAI,MAAM,OAAO;AACzC,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,mBAAO,SAAS,KAAK;AAAA,qBACZ,MAAM,GAAG,IAAI;AACtB;AAAA;AAAA;AAGJ,eAAO,SAAS;AAAA;AAElB,UAAI,OAAO,SAAU,IAAI,MAAM;AAC7B,eAAO,UAAU,IAAI,MAAM;AAAA;AAG7B,UAAI,OAAO,OAAO;AAClB,UAAI,iBAAiB,OAAO;AAC5B,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,IAAI,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,SAAS;AAAA;AAE5D,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAGlC,UAAI,MAAM,SAAU,KAAK;AACvB,YAAI,IAAI;AACR,YAAI,OAAO,SAAU,GAAG;AACtB,YAAE,KAAK;AAAA;AAET,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,GAAG,KAAK;AAAA;AAEd,eAAO;AAAA;AAGT,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,SAAS,KAAK,OAAO,SAAS;AAAA;AAEvC,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,SAAS;AAAA;AAGzB,UAAI,cAAc;AAClB,UAAI,QAAQ,WAAY;AACtB,YAAI,OAAO;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA;AAER,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO,CAAC;AAAA;AAAA;AAIZ,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,cAAc,SAAU,QAAQ;AAClC,YAAI,MAAM;AAAA,UACR,KAAK;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA;AAEV,YAAI,QAAQ;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA;AAEV,YAAI,UAAU,SAAS,MAAM,MAAM;AACnC,YAAI,WAAW,OAAO,MAAM;AAC5B,YAAI,UAAU,IAAI,UAAU,SAAU,SAAS;AAC7C,cAAI,SAAS,QAAQ,cAAc;AACnC,iBAAO,IAAI,SAAS,UAAU,QAAQ,UAAU;AAAA;AAElD,eAAO,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ,MAAM,MAAM,QAAQ,KAAK;AAAA;AAG1E,UAAI,YAAY;AAAA,QACd;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW;AAAA,YACT;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA;AAAA;AAIZ,UAAI,QAAQ,WAAY;AACtB,YAAI,eAAe,IAAI,WAAW,SAAU,UAAU;AACpD,cAAI,eAAe,IAAI,SAAS,WAAW,aAAa,KAAK;AAC7D,iBAAO;AAAA,YACL,SAAS;AAAA,YACT;AAAA;AAAA;AAGJ,YAAI,aAAa;AAAA,UACf,MAAM;AAAA,UACN,QAAQ;AAAA,YACN;AAAA,YACA;AAAA;AAAA,UAEF,OAAO;AAAA;AAET,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO,CAAC;AAAA;AAAA;AAIZ,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,OAAO,IAAI;AAAA,QACb;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,QAER;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA;AAAA,SAEP,SAAU,MAAM;AACjB,eAAO,SAAS,SAAS,IAAI,OAAO;AAAA,UAClC,MAAM,KAAK,QAAQ;AAAA,UACnB,MAAM,KAAK,QAAQ,KAAK;AAAA;AAAA;AAI5B,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI,mBAAmB,WAAY;AACjC,cAAI,iBAAiB,OAAO,MAAM,SAAU,IAAI;AAC9C,gBAAI,MAAM,GAAG,KAAK,OAAO,GAAG;AAC5B,mBAAO,QAAQ,iBAAiB,SAAS;AAAA;AAE3C,cAAI,oBAAoB,IAAI,gBAAgB,SAAU,QAAQ;AAC5D,mBAAO,SAAS,SAAS,UAAU,OAAO,QAAQ;AAAA,aACjD,KAAK;AACR,iBAAO,mDAAwD,SAAS,UAAU,sBAAsB,iBAAsB,oBAAoB,wKAAwK,SAAS,UAAU,mBAAmB;AAAA;AAElW,YAAI,WAAW,SAAU,GAAG;AAC1B,iBAAO,cAAc,EAAE,MAAM,sCAAsC,EAAE,OAAO;AAAA;AAE9E,YAAI,cAAc,SAAU,SAAQ,KAAK;AACvC,iBAAO,KAAK,MAAM,SAAU,GAAG;AAC7B,mBAAO,EAAE,QAAQ;AAAA,aAChB,KAAK,WAAY;AAClB,gBAAI,cAAc,QAAO,QAAQ,KAAK;AACtC,mBAAO,OAAO,gBAAgB,aAAa,SAAS,iBAAiB;AAAA,aACpE,SAAU,GAAG;AACd,gBAAI,OAAO,EAAE,SAAS,YAAY,EAAE,OAAO,MAAM,EAAE;AACnD,mBAAO,SAAS;AAAA,cACd;AAAA,cACA,KAAK,yCAAyC,EAAE,OAAO,MAAM,EAAE;AAAA;AAAA;AAAA;AAIrE,YAAI,gBAAgB,SAAU,SAAQ;AACpC,cAAI,SAAS,KAAK,QAAO;AACzB,cAAI,iBAAiB,iBAAiB;AACtC,iBAAO,mBAAmB,SAAY,SAAS,OAAO,QAAQ,SAAU,GAAG;AACzE,mBAAO,CAAC,SAAS,gBAAgB;AAAA;AAAA;AAGrC,YAAI,eAAe,SAAU,SAAQ;AACnC,cAAI,aAAa,cAAc;AAC/B,cAAI,YAAY,IAAI,YAAY,SAAU,KAAK;AAC7C,mBAAO,SAAS,YAAY,SAAQ,OAAO;AAAA;AAE7C,cAAI,QAAQ,UAAU;AACtB,cAAI,gBAAgB,UAAU,KAAK;AACnC,cAAI,OAAO,WAAW,SAAS,UAAU;AAAA,YACvC;AAAA,YACA;AAAA,eACG,iBAAsB,gBAAgB;AAC3C,iBAAO;AAAA;AAET,YAAI,mBAAmB,SAAU,SAAQ;AACvC,cAAI,WAAU,MAAM;AAClB,mBAAO;AAAA;AAET,iBAAO,6CAA6C,aAAa,WAAU;AAAA;AAE7E,YAAI,YAAY;AAAA,UACd,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,iBAAiB;AAAA,YACjB;AAAA,YACA,KAAK;AAAA;AAET,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO,CAAC;AAAA;AAAA;AAIZ,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,MAAM,WAAY;AACpB,YAAI,aAAa,SAAU,OAAO,OAAO;AACvC,iBAAO,MAAM,QAAQ,SAAS,IAAI,UAAU,QAAQ,MAAM;AAAA;AAE5D,YAAI,UAAU,WAAW,OAAO,cAAc,OAAO;AACrD,YAAI,gBAAgB,qJAAqJ,UAAU;AACnL,YAAI,YAAY;AAAA,UACd,MAAM;AAAA,UACN,MAAM,QAAQ,SAAS,UAAU;AAAA,YAC/B;AAAA,YACA;AAAA,eACG;AAAA,UACL,SAAS;AAAA;AAEX,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO,CAAC;AAAA;AAAA;AAIZ,UAAI,uBAAuB,SAAU,kBAAkB,MAAM;AAC3D,YAAI,UAAU;AACd,YAAI,QAAQ,IAAI,kBAAkB,SAAU,GAAG;AAC7C,cAAI,OAAO,MAAM,UAAU;AACzB,gBAAI,IAAI,MAAM,IAAI;AAChB,sBAAQ,KAAK,KAAK;AAAA;AAEpB,mBAAO;AAAA,iBACF;AACL,oBAAQ,EAAE,QAAQ;AAClB,mBAAO,EAAE;AAAA;AAAA;AAGb,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA;AAAA;AAGJ,UAAI,mBAAmB,SAAU,MAAM;AACrC,YAAI,QAAQ,KAAK;AACjB,YAAI,MAAM,MAAM,QAAQ;AACxB,YAAI,QAAQ,IAAI;AACd,gBAAM,OAAO,KAAK;AAClB,gBAAM,KAAK;AAAA;AAEb,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,kBAAkB,SAAU,QAAQ,YAAY;AAClD,YAAI;AACJ,YAAI,aAAY;AAChB,YAAI,MAAM;AACV,YAAI,UAAU,MAAM;AACpB,YAAI,WAAW;AACf,YAAI,OAAO,SAAU,MAAK,IAAI,GAAG,WAAU,QAAQ,YAAW,GAAG,IAAI,QAAQ,KAAK,GAAG,QAAQ,QAAQ,SAAS,GAAG,SAAS,QAAQ,UAAU,KAAK,WAAW;AAC5J,eAAO,YAAY,QAAQ,KAAK,WAAY;AAC1C,iBAAO,iBAAiB;AAAA,WACvB,SAAU,kBAAkB;AAC7B,iBAAO,qBAAqB,kBAAkB;AAAA;AAAA;AAGlD,UAAI,OAAO,SAAU,QAAQ,YAAY;AACvC,eAAO,WAAY;AACjB,cAAI,KAAK,gBAAgB,QAAQ,aAAa,OAAO,GAAG,MAAM,QAAQ,GAAG;AACzE,cAAI,YAAY,IAAI,OAAO,SAAU,MAAM;AACzC,mBAAO,IAAI,MAAM;AAAA;AAEnB,cAAI,aAAa,IAAI;AACrB,cAAI,OAAO;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA;AAER,iBAAO,cAAc,KAAK;AAAA,YACxB,OAAO;AAAA,YACP,MAAM;AAAA,YACN;AAAA,YACA,SAAS,CAAC;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA,YAEb,aAAa;AAAA;AAAA;AAAA;AAKnB,wBAAmB;AACjB,iBAAS,IAAI,QAAQ,SAAU,QAAQ;AACrC,cAAI,aAAa,KAAK;AACtB,cAAI,MAAM,MAAM;AAChB,cAAI,eAAe,KAAK,QAAQ;AAChC,mBAAS,QAAQ;AACjB,qBAAW,QAAQ;AACnB,iBAAO,UAAU,IAAI,SAAS,oBAAoB;AAClD,iBAAO;AAAA;AAAA;AAIX;AAAA;AAAA;AAAA;;;ACj1BJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,+BAAQ;", "names": []}