<template>
    <div>
        <el-form :class="{ 'h-50': !showSearch }" :model="queryParams" ref="queryRef" inline label-width="100px">
            <el-form-item label="案件ID" prop="caseId">
                <el-input v-model="queryParams.caseId" placeholder="请输入案件ID" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="债务人姓名" prop="clientName">
                <el-input v-model="queryParams.clientName" placeholder="请输入债务人姓名" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码" prop="clientIdNum">
                <el-input v-model="queryParams.clientIdNum" placeholder="请输入证件号码" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="合同号" prop="contractNo">
                <el-input v-model="queryParams.contractNo" placeholder="请输入合同号" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="上传批次">
                <el-select @keyup.enter="handleQuery" multiple v-model="importBatchNums" placeholder="请选择上传批次" clearable
                    style="width: 240px">
                    <el-option v-for="v in infoBatchOption" :label="v" :value="v" :key="v" />
                </el-select>
            </el-form-item>
            <el-form-item label="上传时间" prop="createTime">
                <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD" type="daterange"
                    style="width: 240px" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item label="外显号码" prop="number">
                <el-input v-model="queryParams.number" placeholder="请输入外显号码" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="被叫号码" prop="callTo">
                <el-input v-model="queryParams.callTo" placeholder="请输入被叫号码" clearable style="width: 240px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="质检状态" prop="qualityStatus">
                <el-select v-model="queryParams.qualityStatus" placeholder="请选择质检状态" clearable style="width: 240px">
                    <el-option v-for="(v, i) in qcStatusOptions" :label="v.info" :value="v.code" :key="i" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
            <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </div>

        <div class="mb8">
            <div v-if="activeTab != 'recycle'">
                <el-button v-hasPermi="['callLog:uploadRecord:upload']" type="primary"
                    @click="handleOpenDialog('UploadRecordInfoRef')">上传录音</el-button>
                <span v-if="checkPermi(['callLog:uploadRecord:recordQc'])" class="ml20 mr20">
                    <el-button plain :disabled="single" type="warning" @click="handleRecordQc()">录音质检 </el-button>
                    <el-tooltip effect="dark" content="所选录音按优先顺序进行质检，只能做Mp3和wav的双通道质检。" placement="top">
                        <el-icon class="icon-question" color="#f59a24">
                            <QuestionFilled />
                        </el-icon>
                    </el-tooltip>
                </span>
                <el-button v-hasPermi="['callLog:uploadRecord:remove']" plain :disabled="single" type="info"
                    @click="remove()">删除</el-button>
                <el-button v-hasPermi="['callLog:uploadRecord:download']" plain :disabled="single" type="success"
                    @click="downloadRecord()">下载</el-button>
            </div>
            <el-button @click="recoverReocrd()" v-if="activeTab == 'recycle'" type="primary"
                v-hasPermi="['callLog:uploadRecord:recover']">恢复</el-button>
        </div>

        <el-row class="hint">
            <el-checkbox-group v-model="checkedType" @change="checkedTypeChange">
                <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
                    :indeterminate="item.indeterminate" :disabled="dataList.length === 0" />
            </el-checkbox-group>

            <el-tooltip effect="light" placement="bottom-start">
                <div>
                    <svg-icon class="hint-item text-warning" icon-class="question" />
                </div>
                <template #content>
                    <div class="info-tip">
                        <el-icon class="info-tip-icon" :size="16">
                            <warning-filled />
                        </el-icon>
                        <div>
                            <p>注：</p>
                            <p>
                                1、默认只展示当月的通话记录,请于晚22:00前创建任务，否则无法下载；录音下载任务创建后，将于次日（24小时后）进行批量下载；
                            </p>
                            <p>
                                2、录音按照双方合同约束默认保留储存期，服务器将自动彻底删除录音，请系统使用方即时保留录音到本地，避免造成录音丢失。
                            </p>
                        </div>
                    </div>
                </template>
            </el-tooltip>
            <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList"
                :types="[1, 2, 3]" />
        </el-row>

        <el-tabs class="mb8" v-model="activeTab" @tab-click="tabChange">
            <el-tab-pane v-for="item in answerOptions" :key="item.code" :label="item.info" :name="item.code" />
        </el-tabs>

        <el-table v-loading="loading" max-height="55vh" ref="multipleTableRef" :data="dataList"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" fixed="left" :selectable="checkSelectable" align="right" width="30px" />
            <el-table-column label="案件ID" fixed="left" v-if="columns[0].visible" align="center" :width="120">
                <template #default="{ row }">
                    <router-link v-if="row.caseId" class="text-primary"
                        :to="`/risk/calllog-details/caseDetails/${row.caseId}`">
                        {{ row.caseId }}</router-link>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column label="债务人姓名" v-if="columns[1].visible" prop="clientName" align="center"
                show-overflow-tooltip min-width="120" />
            <el-table-column label="身份证号" v-if="columns[2].visible" prop="clientIdNum" align="center" width="180" />
            <el-table-column label="合同号" v-if="columns[3].visible" prop="contractNo" align="center"
                show-overflow-tooltip width="160" />
            <el-table-column label="当前委托机构" v-if="columns[4].visible" prop="teamName" align="center"
                show-overflow-tooltip min-width="120" />
            <el-table-column label="外显号码" v-if="columns[5].visible" prop="number" align="center" show-overflow-tooltip
                width="120" />
            <el-table-column label="被叫号码" v-if="columns[6].visible" prop="callTo" align="center" show-overflow-tooltip
                width="120" />
            <el-table-column label="录音时间" v-if="columns[7].visible" prop="recordingTime" align="center" width="160" />
            <el-table-column label="上传时间" v-if="columns[8].visible" prop="createTime" align="center" width="160" />
            <el-table-column label="录音类型" v-if="columns[9].visible" prop="recordingType" align="center"
                :formatter="row => recordingTypeEnum[row.recordingType]" width="100" />
            <el-table-column label="备注" v-if="columns[10].visible" prop="remark" align="center" show-overflow-tooltip
                min-width="160" />
            <el-table-column width="450" label="操作" fixed="right">
                <template #default="scope">
                    <div class="play-button"
                        :key="`audioRef${queryParams.pageNum + queryParams.pageSize + scope.$index}`">
                        <musicPlayer v-if="checkPermi(['callLog:uploadRecord:play']) && scope.row.recordUrl"
                            :ref="`audioRef${scope.$index}`" @stopCheck="stopCheck(scope.$index)"
                            :audioSrc="scope.row.recordUrl" v-model:audioObj="scope.row" />
                    </div>
                    <span v-if="activeTab != 'recycle'">
                        <el-button v-if="[1, 2].includes(scope.row.qualityStatus)" type="text"
                            @click="handleTextPlay(scope.row)"
                            v-hasPermi="['callLog:uploadRecord:textPlay']">文本播放</el-button>
                        <el-button v-show="scope.row.recordUrl" type="text" v-hasPermi="['callLog:uploadRecord:edit']"
                            @click="handleOpenDialog('UpdateRecordInfoRef', scope.row)">修改</el-button>
                        <el-button v-show="scope.row.recordUrl" type="text" v-hasPermi="['callLog:uploadRecord:remove']"
                            @click="remove(scope.row)">删除</el-button>
                        <el-button v-show="scope.row.recordUrl" type="text"
                            v-hasPermi="['callLog:uploadRecord:download']"
                            @click="downVideo(scope.row.recordUrl)">下载</el-button>
                    </span>
                    <el-button v-if="activeTab == 'recycle'" type="text" v-hasPermi="['callLog:uploadRecord:recover']"
                        @click="recoverReocrd(scope.row)">恢复</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        <UploadRecordInfo ref="UploadRecordInfoRef" :getList="getList" />
        <UpdateRecordInfo ref="UpdateRecordInfoRef" :getList="getList" />
    </div>
</template>

<script setup name="Calllog">
import UploadRecordInfo from '../dialog/uploadRecordInfo';
import UpdateRecordInfo from '../dialog/updateRecordInfo';
import musicPlayer from "@/components/MusicPlay/musicPlayer";
import { calllogs, deleteCallRecordApi, recoverCallRecordApi, qualityOrderApi } from "@/api/seat/calllog";
import { checkPermi } from "@/utils/permission";
import { recordingTypeEnum } from "@/utils/enum";
import { getQualityStatusApi, getInfoBatchBumOptionApi } from "@/api/options";
const router = useRouter()
const route = useRoute()
const { proxy } = getCurrentInstance();
const single = ref(true); //能否操作
const selectedArr = ref([]); //表格选中数据
const ids = ref([]);
const infoBatchOption = ref([]);
const answerOptions = ref([
    { code: 'all', info: '全部' },
    { code: '0', info: '沟通' },
    { code: '1', info: '客服' },
    { code: 'recycle', info: '回收站' },
]); //tab数据
const activeTab = ref('all');
const showSearch = ref(false);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const qcStatusOptions = ref([]);
const multipleTableRef = ref();
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    callType: 1,
    allQuery: false, //是否搜索结果全选
});
const importBatchNums = ref([])
const handledForm = ref({}); //处理后的搜索条件
const rangFields = ["callTime", 'createTime'];
const columns = ref([
    { "key": 0, "label": "案件ID", "visible": true },
    { "key": 1, "label": "债务人姓名", "visible": true },
    { "key": 2, "label": "身份证号", "visible": true },
    { "key": 3, "label": "合同号", "visible": true },
    { "key": 4, "label": "当前委托机构", "visible": true },
    { "key": 5, "label": "外显号码", "visible": true },
    { "key": 6, "label": "被叫号码", "visible": true },
    { "key": 7, "label": "录音时间", "visible": true },
    { "key": 8, "label": "上传时间", "visible": true },
    { "key": 9, "label": "录音类型", "visible": true },
    { "key": 10, "label": "备注", "visible": true }
]);

const checkedType = ref([]);
const checkStatus = ref([
    { label: "本页选中", is_settle: "1", indeterminate: false },
    { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);

//获取列表数据
getList()
function getList() {
    if (importBatchNums.value.length) {
        queryParams.value.importBatchNum = String(importBatchNums.value)
    } else {
        delete queryParams.value.importBatchNum
    }
    handledForm.value = proxy.addFieldsRange(queryParams.value, rangFields);
    if (activeTab.value != "all" && activeTab.value != "recycle") {
        handledForm.value.recordingType = activeTab.value
    }
    handledForm.value.recycle = activeTab.value == "recycle"
    loading.value = true;
    calllogs(handledForm.value).then((res) => {
        dataList.value = res.rows;
        total.value = res.total;
    }).finally(() => loading.value = false);
}

// 录音质检
function handleRecordQc() {
    const title = '提交优先质检'
    const content = `该操作将会对所选录音进行优先质检，确认进行操作吗?`
    proxy.$modal.confirm(content, title).then(() => {
        let reqForm = JSON.parse(JSON.stringify(handledForm.value));
        if (!queryParams.value.allQuery) {
            reqForm.ids = ids.value;
            reqForm.allQuery = false;
        }
        qualityOrderApi(reqForm).then((res) => {
            proxy.$modal.confirm(res.msg, "操作成功")
        });

    })
}

// 恢复录音
function recoverReocrd(row) {
    const title = '恢复删除录音'
    const content = `该操作将会对所选录音进行恢复，确认进行操作吗?`
    proxy.$modal.confirm(content, title).then(() => {
        loading.value = true
        let reqForm = JSON.parse(JSON.stringify(handledForm.value))
        if (row || !reqForm.allQuery) {
            reqForm = { ids: row ? [row.id] : ids.value, allQuery: false }
        }
        recoverCallRecordApi(reqForm).then(() => {
            getList()
            proxy.$modal.msgSuccess("操作成功！")
        }).finally(() => loading.value = false)
    })
}

//搜索
function handleQuery() {
    ids.value = [];
    selectedArr.value = [];
    queryParams.value.pageNum = 1;
    getList();
}

//重置
function resetQuery() {
    proxy.resetForm("queryRef");
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        callType: 1,
        allQuery: false, //是否搜索结果全选
    };
    ids.value = [];
    selectedArr.value = [];
    importBatchNums.value = [];
    getList();
}
function handleOpenDialog(refName, row) {
    proxy.$refs[refName].openDialog(row)
}
//当有录音播放的时候停止其他录音
function stopCheck(count) {
    dataList.value.forEach((item, index) => {
        if (index !== count) {
            proxy.$refs[`audioRef${index}`].stopAudio();
        }
    });
}

//删除
function remove(row) {
    const content = `此操作将删除选中的录音，删除的录音将会在回收站保存七天，是否继续？?`
    proxy.$modal.confirm(content, '删除提示').then(() => {
        let reqForm = JSON.parse(JSON.stringify(handledForm.value))
        if (row || !reqForm.allQuery) {
            reqForm = { ids: row ? [row.id] : ids.value, allQuery: false }
        }
        loading.value = true
        deleteCallRecordApi(reqForm).then(() => {
            getList()
            proxy.$modal.msgSuccess('操作成功！')
        }).finally(() => loading.value = false)
    })
}

//下载录音
function downloadRecord() {
    let reqForm = handledForm.value;
    if (!queryParams.value.allQuery) {
        reqForm = { ids: ids.value, allQuery: false }
    }
    const apiPath = "caseManage/call/record/fileDownList"
    proxy.downloadforjson(apiPath, reqForm, `上传录音_${new Date().getTime()}.zip`);
}
getQualityStatusFun()
function getQualityStatusFun() {
    getQualityStatusApi().then((res) => {
        qcStatusOptions.value = res.data
    })
}

function handleTextPlay(row) {
    const query = { callRecordId: row.id, path: route.path }
    const path = `/risk/calllog-details/textPlay`
    router.push({ path, query })
}
getInfoBatchBumOptionFun()
function getInfoBatchBumOptionFun() {
    getInfoBatchBumOptionApi().then(res => {
        infoBatchOption.value = res.data
    })
}

//tab选择
function tabChange() {
    selectedArr.value = [];
    ids.value = [];
    nextTick(() => {
        getList();
    });
}

//表格选中
function selectTable() {
    return new Promise((reslove, reject) => {
        try {
            dataList.value.map((item, index) => {
                multipleTableRef.value.toggleRowSelection(item, true);
            });
            reslove(true);
        } catch (error) {
            reject(error);
        }
    });
}

//全选类型
function checkedTypeChange(val) {
    checkedType.value.length > 1 && checkedType.value.shift(); //单选
    if (checkedType.value.length === 0) {
        //全不选
        multipleTableRef.value.clearSelection();
        checkStatus.value[0].indeterminate = false;
    } else {
        dataList.value.length > 0 &&
            dataList.value.map((item) => {
                multipleTableRef.value.toggleRowSelection(item, true);
            });
    }
    if (checkedType.value[0] == "搜索结果全选") {
        checkStatus.value[0].indeterminate = false;
        queryParams.value.allQuery = true;
        handledForm.value.allQuery = true;
    } else {
        queryParams.value.allQuery = false;
        handledForm.value.allQuery = false;
    }
}

//选择列表
function handleSelectionChange(selection) {
    selectedArr.value = selection;
    if (checkedType.value[0] != "搜索结果全选") {
        if (selectedArr.value.length == dataList.value?.length) {
            checkedType.value[0] = "本页选中";
        } else {
            checkedType.value = [];
        }
    }
}

//表格行能否选择
function checkSelectable(row, index) {
    return !queryParams.value.allQuery;
}

watch(dataList, (newval, preval) => {
    if (newval.length > 0) {
        //处理禁用表格复选框时无法选中的情况
        if (queryParams.value.allQuery) {
            queryParams.value.allQuery = false;
            handledForm.value.allQuery = false;
            nextTick(() => {
                selectTable().finally(() => {
                    queryParams.value.allQuery = true;
                    handledForm.value.allQuery = true;
                });
            });
        } else {
            dataList.value?.forEach((item, index) => {
                if (ids.value?.includes(item.id)) {
                    proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
                }
            });
        }
    }
});

watch(selectedArr, (newval) => {
    nextTick(() => {
        let select = newval;
        let pageIds = select.map((item) => item.id).filter((item) => item);
        //添加选择的数据
        let caseTableIds = dataList.value.map((item) => item.id);
        ids.value = ids.value.filter((item) => !caseTableIds.includes(item));
        ids.value = [...new Set([...ids.value, ...pageIds])];
        single.value = !(ids.value?.length > 0);
    });
});


//下载录音
function downVideo(url) {
    window.open(url);
}
</script>

<style lang="scss" scoped>
.h-50 {
    height: 50px;
    overflow: hidden;
}

.icon-question {
    cursor: pointer;
}

:deep(.hint .el-tooltip__trigger) {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
}

.hint-item {
    font-size: 18px;
    // color: #5a5e66;
    cursor: pointer;
}

.info-tip {
    border: unset;
}

:deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
}

.play-button {
    display: inline-block;
    width: 50%;
    vertical-align: middle;
}

.download-button {
    display: inline-block;
    width: 30%;
    vertical-align: middle;
    margin-top: 5px;
}
</style>