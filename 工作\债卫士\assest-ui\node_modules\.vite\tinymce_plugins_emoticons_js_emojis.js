import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/emoticons/js/emojis.js
var require_emojis = __commonJS({
  "node_modules/tinymce/plugins/emoticons/js/emojis.js"() {
    window.tinymce.Resource.add("tinymce.plugins.emoticons", {
      grinning: {
        keywords: ["face", "smile", "happy", "joy", ":D", "grin"],
        char: "\u{1F600}",
        fitzpatrick_scale: false,
        category: "people"
      },
      grimacing: {
        keywords: ["face", "grimace", "teeth"],
        char: "\u{1F62C}",
        fitzpatrick_scale: false,
        category: "people"
      },
      grin: {
        keywords: ["face", "happy", "smile", "joy", "kawaii"],
        char: "\u{1F601}",
        fitzpatrick_scale: false,
        category: "people"
      },
      joy: {
        keywords: ["face", "cry", "tears", "weep", "happy", "happytears", "haha"],
        char: "\u{1F602}",
        fitzpatrick_scale: false,
        category: "people"
      },
      rofl: {
        keywords: ["face", "rolling", "floor", "laughing", "lol", "haha"],
        char: "\u{1F923}",
        fitzpatrick_scale: false,
        category: "people"
      },
      partying: {
        keywords: ["face", "celebration", "woohoo"],
        char: "\u{1F973}",
        fitzpatrick_scale: false,
        category: "people"
      },
      smiley: {
        keywords: ["face", "happy", "joy", "haha", ":D", ":)", "smile", "funny"],
        char: "\u{1F603}",
        fitzpatrick_scale: false,
        category: "people"
      },
      smile: {
        keywords: ["face", "happy", "joy", "funny", "haha", "laugh", "like", ":D", ":)"],
        char: "\u{1F604}",
        fitzpatrick_scale: false,
        category: "people"
      },
      sweat_smile: {
        keywords: ["face", "hot", "happy", "laugh", "sweat", "smile", "relief"],
        char: "\u{1F605}",
        fitzpatrick_scale: false,
        category: "people"
      },
      laughing: {
        keywords: ["happy", "joy", "lol", "satisfied", "haha", "face", "glad", "XD", "laugh"],
        char: "\u{1F606}",
        fitzpatrick_scale: false,
        category: "people"
      },
      innocent: {
        keywords: ["face", "angel", "heaven", "halo"],
        char: "\u{1F607}",
        fitzpatrick_scale: false,
        category: "people"
      },
      wink: {
        keywords: ["face", "happy", "mischievous", "secret", ";)", "smile", "eye"],
        char: "\u{1F609}",
        fitzpatrick_scale: false,
        category: "people"
      },
      blush: {
        keywords: ["face", "smile", "happy", "flushed", "crush", "embarrassed", "shy", "joy"],
        char: "\u{1F60A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      slightly_smiling_face: {
        keywords: ["face", "smile"],
        char: "\u{1F642}",
        fitzpatrick_scale: false,
        category: "people"
      },
      upside_down_face: {
        keywords: ["face", "flipped", "silly", "smile"],
        char: "\u{1F643}",
        fitzpatrick_scale: false,
        category: "people"
      },
      relaxed: {
        keywords: ["face", "blush", "massage", "happiness"],
        char: "\u263A\uFE0F",
        fitzpatrick_scale: false,
        category: "people"
      },
      yum: {
        keywords: ["happy", "joy", "tongue", "smile", "face", "silly", "yummy", "nom", "delicious", "savouring"],
        char: "\u{1F60B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      relieved: {
        keywords: ["face", "relaxed", "phew", "massage", "happiness"],
        char: "\u{1F60C}",
        fitzpatrick_scale: false,
        category: "people"
      },
      heart_eyes: {
        keywords: ["face", "love", "like", "affection", "valentines", "infatuation", "crush", "heart"],
        char: "\u{1F60D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      smiling_face_with_three_hearts: {
        keywords: ["face", "love", "like", "affection", "valentines", "infatuation", "crush", "hearts", "adore"],
        char: "\u{1F970}",
        fitzpatrick_scale: false,
        category: "people"
      },
      kissing_heart: {
        keywords: ["face", "love", "like", "affection", "valentines", "infatuation", "kiss"],
        char: "\u{1F618}",
        fitzpatrick_scale: false,
        category: "people"
      },
      kissing: {
        keywords: ["love", "like", "face", "3", "valentines", "infatuation", "kiss"],
        char: "\u{1F617}",
        fitzpatrick_scale: false,
        category: "people"
      },
      kissing_smiling_eyes: {
        keywords: ["face", "affection", "valentines", "infatuation", "kiss"],
        char: "\u{1F619}",
        fitzpatrick_scale: false,
        category: "people"
      },
      kissing_closed_eyes: {
        keywords: ["face", "love", "like", "affection", "valentines", "infatuation", "kiss"],
        char: "\u{1F61A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      stuck_out_tongue_winking_eye: {
        keywords: ["face", "prank", "childish", "playful", "mischievous", "smile", "wink", "tongue"],
        char: "\u{1F61C}",
        fitzpatrick_scale: false,
        category: "people"
      },
      zany: {
        keywords: ["face", "goofy", "crazy"],
        char: "\u{1F92A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      raised_eyebrow: {
        keywords: ["face", "distrust", "scepticism", "disapproval", "disbelief", "surprise"],
        char: "\u{1F928}",
        fitzpatrick_scale: false,
        category: "people"
      },
      monocle: {
        keywords: ["face", "stuffy", "wealthy"],
        char: "\u{1F9D0}",
        fitzpatrick_scale: false,
        category: "people"
      },
      stuck_out_tongue_closed_eyes: {
        keywords: ["face", "prank", "playful", "mischievous", "smile", "tongue"],
        char: "\u{1F61D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      stuck_out_tongue: {
        keywords: ["face", "prank", "childish", "playful", "mischievous", "smile", "tongue"],
        char: "\u{1F61B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      money_mouth_face: {
        keywords: ["face", "rich", "dollar", "money"],
        char: "\u{1F911}",
        fitzpatrick_scale: false,
        category: "people"
      },
      nerd_face: {
        keywords: ["face", "nerdy", "geek", "dork"],
        char: "\u{1F913}",
        fitzpatrick_scale: false,
        category: "people"
      },
      sunglasses: {
        keywords: ["face", "cool", "smile", "summer", "beach", "sunglass"],
        char: "\u{1F60E}",
        fitzpatrick_scale: false,
        category: "people"
      },
      star_struck: {
        keywords: ["face", "smile", "starry", "eyes", "grinning"],
        char: "\u{1F929}",
        fitzpatrick_scale: false,
        category: "people"
      },
      clown_face: {
        keywords: ["face"],
        char: "\u{1F921}",
        fitzpatrick_scale: false,
        category: "people"
      },
      cowboy_hat_face: {
        keywords: ["face", "cowgirl", "hat"],
        char: "\u{1F920}",
        fitzpatrick_scale: false,
        category: "people"
      },
      hugs: {
        keywords: ["face", "smile", "hug"],
        char: "\u{1F917}",
        fitzpatrick_scale: false,
        category: "people"
      },
      smirk: {
        keywords: ["face", "smile", "mean", "prank", "smug", "sarcasm"],
        char: "\u{1F60F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      no_mouth: {
        keywords: ["face", "hellokitty"],
        char: "\u{1F636}",
        fitzpatrick_scale: false,
        category: "people"
      },
      neutral_face: {
        keywords: ["indifference", "meh", ":|", "neutral"],
        char: "\u{1F610}",
        fitzpatrick_scale: false,
        category: "people"
      },
      expressionless: {
        keywords: ["face", "indifferent", "-_-", "meh", "deadpan"],
        char: "\u{1F611}",
        fitzpatrick_scale: false,
        category: "people"
      },
      unamused: {
        keywords: ["indifference", "bored", "straight face", "serious", "sarcasm", "unimpressed", "skeptical", "dubious", "side_eye"],
        char: "\u{1F612}",
        fitzpatrick_scale: false,
        category: "people"
      },
      roll_eyes: {
        keywords: ["face", "eyeroll", "frustrated"],
        char: "\u{1F644}",
        fitzpatrick_scale: false,
        category: "people"
      },
      thinking: {
        keywords: ["face", "hmmm", "think", "consider"],
        char: "\u{1F914}",
        fitzpatrick_scale: false,
        category: "people"
      },
      lying_face: {
        keywords: ["face", "lie", "pinocchio"],
        char: "\u{1F925}",
        fitzpatrick_scale: false,
        category: "people"
      },
      hand_over_mouth: {
        keywords: ["face", "whoops", "shock", "surprise"],
        char: "\u{1F92D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      shushing: {
        keywords: ["face", "quiet", "shhh"],
        char: "\u{1F92B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      symbols_over_mouth: {
        keywords: ["face", "swearing", "cursing", "cussing", "profanity", "expletive"],
        char: "\u{1F92C}",
        fitzpatrick_scale: false,
        category: "people"
      },
      exploding_head: {
        keywords: ["face", "shocked", "mind", "blown"],
        char: "\u{1F92F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      flushed: {
        keywords: ["face", "blush", "shy", "flattered"],
        char: "\u{1F633}",
        fitzpatrick_scale: false,
        category: "people"
      },
      disappointed: {
        keywords: ["face", "sad", "upset", "depressed", ":("],
        char: "\u{1F61E}",
        fitzpatrick_scale: false,
        category: "people"
      },
      worried: {
        keywords: ["face", "concern", "nervous", ":("],
        char: "\u{1F61F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      angry: {
        keywords: ["mad", "face", "annoyed", "frustrated"],
        char: "\u{1F620}",
        fitzpatrick_scale: false,
        category: "people"
      },
      rage: {
        keywords: ["angry", "mad", "hate", "despise"],
        char: "\u{1F621}",
        fitzpatrick_scale: false,
        category: "people"
      },
      pensive: {
        keywords: ["face", "sad", "depressed", "upset"],
        char: "\u{1F614}",
        fitzpatrick_scale: false,
        category: "people"
      },
      confused: {
        keywords: ["face", "indifference", "huh", "weird", "hmmm", ":/"],
        char: "\u{1F615}",
        fitzpatrick_scale: false,
        category: "people"
      },
      slightly_frowning_face: {
        keywords: ["face", "frowning", "disappointed", "sad", "upset"],
        char: "\u{1F641}",
        fitzpatrick_scale: false,
        category: "people"
      },
      frowning_face: {
        keywords: ["face", "sad", "upset", "frown"],
        char: "\u2639",
        fitzpatrick_scale: false,
        category: "people"
      },
      persevere: {
        keywords: ["face", "sick", "no", "upset", "oops"],
        char: "\u{1F623}",
        fitzpatrick_scale: false,
        category: "people"
      },
      confounded: {
        keywords: ["face", "confused", "sick", "unwell", "oops", ":S"],
        char: "\u{1F616}",
        fitzpatrick_scale: false,
        category: "people"
      },
      tired_face: {
        keywords: ["sick", "whine", "upset", "frustrated"],
        char: "\u{1F62B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      weary: {
        keywords: ["face", "tired", "sleepy", "sad", "frustrated", "upset"],
        char: "\u{1F629}",
        fitzpatrick_scale: false,
        category: "people"
      },
      pleading: {
        keywords: ["face", "begging", "mercy"],
        char: "\u{1F97A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      triumph: {
        keywords: ["face", "gas", "phew", "proud", "pride"],
        char: "\u{1F624}",
        fitzpatrick_scale: false,
        category: "people"
      },
      open_mouth: {
        keywords: ["face", "surprise", "impressed", "wow", "whoa", ":O"],
        char: "\u{1F62E}",
        fitzpatrick_scale: false,
        category: "people"
      },
      scream: {
        keywords: ["face", "munch", "scared", "omg"],
        char: "\u{1F631}",
        fitzpatrick_scale: false,
        category: "people"
      },
      fearful: {
        keywords: ["face", "scared", "terrified", "nervous", "oops", "huh"],
        char: "\u{1F628}",
        fitzpatrick_scale: false,
        category: "people"
      },
      cold_sweat: {
        keywords: ["face", "nervous", "sweat"],
        char: "\u{1F630}",
        fitzpatrick_scale: false,
        category: "people"
      },
      hushed: {
        keywords: ["face", "woo", "shh"],
        char: "\u{1F62F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      frowning: {
        keywords: ["face", "aw", "what"],
        char: "\u{1F626}",
        fitzpatrick_scale: false,
        category: "people"
      },
      anguished: {
        keywords: ["face", "stunned", "nervous"],
        char: "\u{1F627}",
        fitzpatrick_scale: false,
        category: "people"
      },
      cry: {
        keywords: ["face", "tears", "sad", "depressed", "upset", ":'("],
        char: "\u{1F622}",
        fitzpatrick_scale: false,
        category: "people"
      },
      disappointed_relieved: {
        keywords: ["face", "phew", "sweat", "nervous"],
        char: "\u{1F625}",
        fitzpatrick_scale: false,
        category: "people"
      },
      drooling_face: {
        keywords: ["face"],
        char: "\u{1F924}",
        fitzpatrick_scale: false,
        category: "people"
      },
      sleepy: {
        keywords: ["face", "tired", "rest", "nap"],
        char: "\u{1F62A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      sweat: {
        keywords: ["face", "hot", "sad", "tired", "exercise"],
        char: "\u{1F613}",
        fitzpatrick_scale: false,
        category: "people"
      },
      hot: {
        keywords: ["face", "feverish", "heat", "red", "sweating"],
        char: "\u{1F975}",
        fitzpatrick_scale: false,
        category: "people"
      },
      cold: {
        keywords: ["face", "blue", "freezing", "frozen", "frostbite", "icicles"],
        char: "\u{1F976}",
        fitzpatrick_scale: false,
        category: "people"
      },
      sob: {
        keywords: ["face", "cry", "tears", "sad", "upset", "depressed"],
        char: "\u{1F62D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      dizzy_face: {
        keywords: ["spent", "unconscious", "xox", "dizzy"],
        char: "\u{1F635}",
        fitzpatrick_scale: false,
        category: "people"
      },
      astonished: {
        keywords: ["face", "xox", "surprised", "poisoned"],
        char: "\u{1F632}",
        fitzpatrick_scale: false,
        category: "people"
      },
      zipper_mouth_face: {
        keywords: ["face", "sealed", "zipper", "secret"],
        char: "\u{1F910}",
        fitzpatrick_scale: false,
        category: "people"
      },
      nauseated_face: {
        keywords: ["face", "vomit", "gross", "green", "sick", "throw up", "ill"],
        char: "\u{1F922}",
        fitzpatrick_scale: false,
        category: "people"
      },
      sneezing_face: {
        keywords: ["face", "gesundheit", "sneeze", "sick", "allergy"],
        char: "\u{1F927}",
        fitzpatrick_scale: false,
        category: "people"
      },
      vomiting: {
        keywords: ["face", "sick"],
        char: "\u{1F92E}",
        fitzpatrick_scale: false,
        category: "people"
      },
      mask: {
        keywords: ["face", "sick", "ill", "disease"],
        char: "\u{1F637}",
        fitzpatrick_scale: false,
        category: "people"
      },
      face_with_thermometer: {
        keywords: ["sick", "temperature", "thermometer", "cold", "fever"],
        char: "\u{1F912}",
        fitzpatrick_scale: false,
        category: "people"
      },
      face_with_head_bandage: {
        keywords: ["injured", "clumsy", "bandage", "hurt"],
        char: "\u{1F915}",
        fitzpatrick_scale: false,
        category: "people"
      },
      woozy: {
        keywords: ["face", "dizzy", "intoxicated", "tipsy", "wavy"],
        char: "\u{1F974}",
        fitzpatrick_scale: false,
        category: "people"
      },
      sleeping: {
        keywords: ["face", "tired", "sleepy", "night", "zzz"],
        char: "\u{1F634}",
        fitzpatrick_scale: false,
        category: "people"
      },
      zzz: {
        keywords: ["sleepy", "tired", "dream"],
        char: "\u{1F4A4}",
        fitzpatrick_scale: false,
        category: "people"
      },
      poop: {
        keywords: ["hankey", "shitface", "fail", "turd", "shit"],
        char: "\u{1F4A9}",
        fitzpatrick_scale: false,
        category: "people"
      },
      smiling_imp: {
        keywords: ["devil", "horns"],
        char: "\u{1F608}",
        fitzpatrick_scale: false,
        category: "people"
      },
      imp: {
        keywords: ["devil", "angry", "horns"],
        char: "\u{1F47F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      japanese_ogre: {
        keywords: ["monster", "red", "mask", "halloween", "scary", "creepy", "devil", "demon", "japanese", "ogre"],
        char: "\u{1F479}",
        fitzpatrick_scale: false,
        category: "people"
      },
      japanese_goblin: {
        keywords: ["red", "evil", "mask", "monster", "scary", "creepy", "japanese", "goblin"],
        char: "\u{1F47A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      skull: {
        keywords: ["dead", "skeleton", "creepy", "death"],
        char: "\u{1F480}",
        fitzpatrick_scale: false,
        category: "people"
      },
      ghost: {
        keywords: ["halloween", "spooky", "scary"],
        char: "\u{1F47B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      alien: {
        keywords: ["UFO", "paul", "weird", "outer_space"],
        char: "\u{1F47D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      robot: {
        keywords: ["computer", "machine", "bot"],
        char: "\u{1F916}",
        fitzpatrick_scale: false,
        category: "people"
      },
      smiley_cat: {
        keywords: ["animal", "cats", "happy", "smile"],
        char: "\u{1F63A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      smile_cat: {
        keywords: ["animal", "cats", "smile"],
        char: "\u{1F638}",
        fitzpatrick_scale: false,
        category: "people"
      },
      joy_cat: {
        keywords: ["animal", "cats", "haha", "happy", "tears"],
        char: "\u{1F639}",
        fitzpatrick_scale: false,
        category: "people"
      },
      heart_eyes_cat: {
        keywords: ["animal", "love", "like", "affection", "cats", "valentines", "heart"],
        char: "\u{1F63B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      smirk_cat: {
        keywords: ["animal", "cats", "smirk"],
        char: "\u{1F63C}",
        fitzpatrick_scale: false,
        category: "people"
      },
      kissing_cat: {
        keywords: ["animal", "cats", "kiss"],
        char: "\u{1F63D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      scream_cat: {
        keywords: ["animal", "cats", "munch", "scared", "scream"],
        char: "\u{1F640}",
        fitzpatrick_scale: false,
        category: "people"
      },
      crying_cat_face: {
        keywords: ["animal", "tears", "weep", "sad", "cats", "upset", "cry"],
        char: "\u{1F63F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      pouting_cat: {
        keywords: ["animal", "cats"],
        char: "\u{1F63E}",
        fitzpatrick_scale: false,
        category: "people"
      },
      palms_up: {
        keywords: ["hands", "gesture", "cupped", "prayer"],
        char: "\u{1F932}",
        fitzpatrick_scale: true,
        category: "people"
      },
      raised_hands: {
        keywords: ["gesture", "hooray", "yea", "celebration", "hands"],
        char: "\u{1F64C}",
        fitzpatrick_scale: true,
        category: "people"
      },
      clap: {
        keywords: ["hands", "praise", "applause", "congrats", "yay"],
        char: "\u{1F44F}",
        fitzpatrick_scale: true,
        category: "people"
      },
      wave: {
        keywords: ["hands", "gesture", "goodbye", "solong", "farewell", "hello", "hi", "palm"],
        char: "\u{1F44B}",
        fitzpatrick_scale: true,
        category: "people"
      },
      call_me_hand: {
        keywords: ["hands", "gesture"],
        char: "\u{1F919}",
        fitzpatrick_scale: true,
        category: "people"
      },
      "+1": {
        keywords: ["thumbsup", "yes", "awesome", "good", "agree", "accept", "cool", "hand", "like"],
        char: "\u{1F44D}",
        fitzpatrick_scale: true,
        category: "people"
      },
      "-1": {
        keywords: ["thumbsdown", "no", "dislike", "hand"],
        char: "\u{1F44E}",
        fitzpatrick_scale: true,
        category: "people"
      },
      facepunch: {
        keywords: ["angry", "violence", "fist", "hit", "attack", "hand"],
        char: "\u{1F44A}",
        fitzpatrick_scale: true,
        category: "people"
      },
      fist: {
        keywords: ["fingers", "hand", "grasp"],
        char: "\u270A",
        fitzpatrick_scale: true,
        category: "people"
      },
      fist_left: {
        keywords: ["hand", "fistbump"],
        char: "\u{1F91B}",
        fitzpatrick_scale: true,
        category: "people"
      },
      fist_right: {
        keywords: ["hand", "fistbump"],
        char: "\u{1F91C}",
        fitzpatrick_scale: true,
        category: "people"
      },
      v: {
        keywords: ["fingers", "ohyeah", "hand", "peace", "victory", "two"],
        char: "\u270C",
        fitzpatrick_scale: true,
        category: "people"
      },
      ok_hand: {
        keywords: ["fingers", "limbs", "perfect", "ok", "okay"],
        char: "\u{1F44C}",
        fitzpatrick_scale: true,
        category: "people"
      },
      raised_hand: {
        keywords: ["fingers", "stop", "highfive", "palm", "ban"],
        char: "\u270B",
        fitzpatrick_scale: true,
        category: "people"
      },
      raised_back_of_hand: {
        keywords: ["fingers", "raised", "backhand"],
        char: "\u{1F91A}",
        fitzpatrick_scale: true,
        category: "people"
      },
      open_hands: {
        keywords: ["fingers", "butterfly", "hands", "open"],
        char: "\u{1F450}",
        fitzpatrick_scale: true,
        category: "people"
      },
      muscle: {
        keywords: ["arm", "flex", "hand", "summer", "strong", "biceps"],
        char: "\u{1F4AA}",
        fitzpatrick_scale: true,
        category: "people"
      },
      pray: {
        keywords: ["please", "hope", "wish", "namaste", "highfive"],
        char: "\u{1F64F}",
        fitzpatrick_scale: true,
        category: "people"
      },
      foot: {
        keywords: ["kick", "stomp"],
        char: "\u{1F9B6}",
        fitzpatrick_scale: true,
        category: "people"
      },
      leg: {
        keywords: ["kick", "limb"],
        char: "\u{1F9B5}",
        fitzpatrick_scale: true,
        category: "people"
      },
      handshake: {
        keywords: ["agreement", "shake"],
        char: "\u{1F91D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      point_up: {
        keywords: ["hand", "fingers", "direction", "up"],
        char: "\u261D",
        fitzpatrick_scale: true,
        category: "people"
      },
      point_up_2: {
        keywords: ["fingers", "hand", "direction", "up"],
        char: "\u{1F446}",
        fitzpatrick_scale: true,
        category: "people"
      },
      point_down: {
        keywords: ["fingers", "hand", "direction", "down"],
        char: "\u{1F447}",
        fitzpatrick_scale: true,
        category: "people"
      },
      point_left: {
        keywords: ["direction", "fingers", "hand", "left"],
        char: "\u{1F448}",
        fitzpatrick_scale: true,
        category: "people"
      },
      point_right: {
        keywords: ["fingers", "hand", "direction", "right"],
        char: "\u{1F449}",
        fitzpatrick_scale: true,
        category: "people"
      },
      fu: {
        keywords: ["hand", "fingers", "rude", "middle", "flipping"],
        char: "\u{1F595}",
        fitzpatrick_scale: true,
        category: "people"
      },
      raised_hand_with_fingers_splayed: {
        keywords: ["hand", "fingers", "palm"],
        char: "\u{1F590}",
        fitzpatrick_scale: true,
        category: "people"
      },
      love_you: {
        keywords: ["hand", "fingers", "gesture"],
        char: "\u{1F91F}",
        fitzpatrick_scale: true,
        category: "people"
      },
      metal: {
        keywords: ["hand", "fingers", "evil_eye", "sign_of_horns", "rock_on"],
        char: "\u{1F918}",
        fitzpatrick_scale: true,
        category: "people"
      },
      crossed_fingers: {
        keywords: ["good", "lucky"],
        char: "\u{1F91E}",
        fitzpatrick_scale: true,
        category: "people"
      },
      vulcan_salute: {
        keywords: ["hand", "fingers", "spock", "star trek"],
        char: "\u{1F596}",
        fitzpatrick_scale: true,
        category: "people"
      },
      writing_hand: {
        keywords: ["lower_left_ballpoint_pen", "stationery", "write", "compose"],
        char: "\u270D",
        fitzpatrick_scale: true,
        category: "people"
      },
      selfie: {
        keywords: ["camera", "phone"],
        char: "\u{1F933}",
        fitzpatrick_scale: true,
        category: "people"
      },
      nail_care: {
        keywords: ["beauty", "manicure", "finger", "fashion", "nail"],
        char: "\u{1F485}",
        fitzpatrick_scale: true,
        category: "people"
      },
      lips: {
        keywords: ["mouth", "kiss"],
        char: "\u{1F444}",
        fitzpatrick_scale: false,
        category: "people"
      },
      tooth: {
        keywords: ["teeth", "dentist"],
        char: "\u{1F9B7}",
        fitzpatrick_scale: false,
        category: "people"
      },
      tongue: {
        keywords: ["mouth", "playful"],
        char: "\u{1F445}",
        fitzpatrick_scale: false,
        category: "people"
      },
      ear: {
        keywords: ["face", "hear", "sound", "listen"],
        char: "\u{1F442}",
        fitzpatrick_scale: true,
        category: "people"
      },
      nose: {
        keywords: ["smell", "sniff"],
        char: "\u{1F443}",
        fitzpatrick_scale: true,
        category: "people"
      },
      eye: {
        keywords: ["face", "look", "see", "watch", "stare"],
        char: "\u{1F441}",
        fitzpatrick_scale: false,
        category: "people"
      },
      eyes: {
        keywords: ["look", "watch", "stalk", "peek", "see"],
        char: "\u{1F440}",
        fitzpatrick_scale: false,
        category: "people"
      },
      brain: {
        keywords: ["smart", "intelligent"],
        char: "\u{1F9E0}",
        fitzpatrick_scale: false,
        category: "people"
      },
      bust_in_silhouette: {
        keywords: ["user", "person", "human"],
        char: "\u{1F464}",
        fitzpatrick_scale: false,
        category: "people"
      },
      busts_in_silhouette: {
        keywords: ["user", "person", "human", "group", "team"],
        char: "\u{1F465}",
        fitzpatrick_scale: false,
        category: "people"
      },
      speaking_head: {
        keywords: ["user", "person", "human", "sing", "say", "talk"],
        char: "\u{1F5E3}",
        fitzpatrick_scale: false,
        category: "people"
      },
      baby: {
        keywords: ["child", "boy", "girl", "toddler"],
        char: "\u{1F476}",
        fitzpatrick_scale: true,
        category: "people"
      },
      child: {
        keywords: ["gender-neutral", "young"],
        char: "\u{1F9D2}",
        fitzpatrick_scale: true,
        category: "people"
      },
      boy: {
        keywords: ["man", "male", "guy", "teenager"],
        char: "\u{1F466}",
        fitzpatrick_scale: true,
        category: "people"
      },
      girl: {
        keywords: ["female", "woman", "teenager"],
        char: "\u{1F467}",
        fitzpatrick_scale: true,
        category: "people"
      },
      adult: {
        keywords: ["gender-neutral", "person"],
        char: "\u{1F9D1}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man: {
        keywords: ["mustache", "father", "dad", "guy", "classy", "sir", "moustache"],
        char: "\u{1F468}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman: {
        keywords: ["female", "girls", "lady"],
        char: "\u{1F469}",
        fitzpatrick_scale: true,
        category: "people"
      },
      blonde_woman: {
        keywords: ["woman", "female", "girl", "blonde", "person"],
        char: "\u{1F471}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      blonde_man: {
        keywords: ["man", "male", "boy", "blonde", "guy", "person"],
        char: "\u{1F471}",
        fitzpatrick_scale: true,
        category: "people"
      },
      bearded_person: {
        keywords: ["person", "bewhiskered"],
        char: "\u{1F9D4}",
        fitzpatrick_scale: true,
        category: "people"
      },
      older_adult: {
        keywords: ["human", "elder", "senior", "gender-neutral"],
        char: "\u{1F9D3}",
        fitzpatrick_scale: true,
        category: "people"
      },
      older_man: {
        keywords: ["human", "male", "men", "old", "elder", "senior"],
        char: "\u{1F474}",
        fitzpatrick_scale: true,
        category: "people"
      },
      older_woman: {
        keywords: ["human", "female", "women", "lady", "old", "elder", "senior"],
        char: "\u{1F475}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_with_gua_pi_mao: {
        keywords: ["male", "boy", "chinese"],
        char: "\u{1F472}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_with_headscarf: {
        keywords: ["female", "hijab", "mantilla", "tichel"],
        char: "\u{1F9D5}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_with_turban: {
        keywords: ["female", "indian", "hinduism", "arabs", "woman"],
        char: "\u{1F473}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_with_turban: {
        keywords: ["male", "indian", "hinduism", "arabs"],
        char: "\u{1F473}",
        fitzpatrick_scale: true,
        category: "people"
      },
      policewoman: {
        keywords: ["woman", "police", "law", "legal", "enforcement", "arrest", "911", "female"],
        char: "\u{1F46E}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      policeman: {
        keywords: ["man", "police", "law", "legal", "enforcement", "arrest", "911"],
        char: "\u{1F46E}",
        fitzpatrick_scale: true,
        category: "people"
      },
      construction_worker_woman: {
        keywords: ["female", "human", "wip", "build", "construction", "worker", "labor", "woman"],
        char: "\u{1F477}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      construction_worker_man: {
        keywords: ["male", "human", "wip", "guy", "build", "construction", "worker", "labor"],
        char: "\u{1F477}",
        fitzpatrick_scale: true,
        category: "people"
      },
      guardswoman: {
        keywords: ["uk", "gb", "british", "female", "royal", "woman"],
        char: "\u{1F482}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      guardsman: {
        keywords: ["uk", "gb", "british", "male", "guy", "royal"],
        char: "\u{1F482}",
        fitzpatrick_scale: true,
        category: "people"
      },
      female_detective: {
        keywords: ["human", "spy", "detective", "female", "woman"],
        char: "\u{1F575}\uFE0F\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      male_detective: {
        keywords: ["human", "spy", "detective"],
        char: "\u{1F575}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_health_worker: {
        keywords: ["doctor", "nurse", "therapist", "healthcare", "woman", "human"],
        char: "\u{1F469}\u200D\u2695\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_health_worker: {
        keywords: ["doctor", "nurse", "therapist", "healthcare", "man", "human"],
        char: "\u{1F468}\u200D\u2695\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_farmer: {
        keywords: ["rancher", "gardener", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F33E}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_farmer: {
        keywords: ["rancher", "gardener", "man", "human"],
        char: "\u{1F468}\u200D\u{1F33E}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_cook: {
        keywords: ["chef", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F373}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_cook: {
        keywords: ["chef", "man", "human"],
        char: "\u{1F468}\u200D\u{1F373}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_student: {
        keywords: ["graduate", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F393}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_student: {
        keywords: ["graduate", "man", "human"],
        char: "\u{1F468}\u200D\u{1F393}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_singer: {
        keywords: ["rockstar", "entertainer", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F3A4}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_singer: {
        keywords: ["rockstar", "entertainer", "man", "human"],
        char: "\u{1F468}\u200D\u{1F3A4}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_teacher: {
        keywords: ["instructor", "professor", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F3EB}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_teacher: {
        keywords: ["instructor", "professor", "man", "human"],
        char: "\u{1F468}\u200D\u{1F3EB}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_factory_worker: {
        keywords: ["assembly", "industrial", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F3ED}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_factory_worker: {
        keywords: ["assembly", "industrial", "man", "human"],
        char: "\u{1F468}\u200D\u{1F3ED}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_technologist: {
        keywords: ["coder", "developer", "engineer", "programmer", "software", "woman", "human", "laptop", "computer"],
        char: "\u{1F469}\u200D\u{1F4BB}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_technologist: {
        keywords: ["coder", "developer", "engineer", "programmer", "software", "man", "human", "laptop", "computer"],
        char: "\u{1F468}\u200D\u{1F4BB}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_office_worker: {
        keywords: ["business", "manager", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F4BC}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_office_worker: {
        keywords: ["business", "manager", "man", "human"],
        char: "\u{1F468}\u200D\u{1F4BC}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_mechanic: {
        keywords: ["plumber", "woman", "human", "wrench"],
        char: "\u{1F469}\u200D\u{1F527}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_mechanic: {
        keywords: ["plumber", "man", "human", "wrench"],
        char: "\u{1F468}\u200D\u{1F527}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_scientist: {
        keywords: ["biologist", "chemist", "engineer", "physicist", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F52C}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_scientist: {
        keywords: ["biologist", "chemist", "engineer", "physicist", "man", "human"],
        char: "\u{1F468}\u200D\u{1F52C}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_artist: {
        keywords: ["painter", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F3A8}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_artist: {
        keywords: ["painter", "man", "human"],
        char: "\u{1F468}\u200D\u{1F3A8}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_firefighter: {
        keywords: ["fireman", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F692}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_firefighter: {
        keywords: ["fireman", "man", "human"],
        char: "\u{1F468}\u200D\u{1F692}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_pilot: {
        keywords: ["aviator", "plane", "woman", "human"],
        char: "\u{1F469}\u200D\u2708\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_pilot: {
        keywords: ["aviator", "plane", "man", "human"],
        char: "\u{1F468}\u200D\u2708\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_astronaut: {
        keywords: ["space", "rocket", "woman", "human"],
        char: "\u{1F469}\u200D\u{1F680}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_astronaut: {
        keywords: ["space", "rocket", "man", "human"],
        char: "\u{1F468}\u200D\u{1F680}",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_judge: {
        keywords: ["justice", "court", "woman", "human"],
        char: "\u{1F469}\u200D\u2696\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_judge: {
        keywords: ["justice", "court", "man", "human"],
        char: "\u{1F468}\u200D\u2696\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_superhero: {
        keywords: ["woman", "female", "good", "heroine", "superpowers"],
        char: "\u{1F9B8}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_superhero: {
        keywords: ["man", "male", "good", "hero", "superpowers"],
        char: "\u{1F9B8}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_supervillain: {
        keywords: ["woman", "female", "evil", "bad", "criminal", "heroine", "superpowers"],
        char: "\u{1F9B9}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_supervillain: {
        keywords: ["man", "male", "evil", "bad", "criminal", "hero", "superpowers"],
        char: "\u{1F9B9}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      mrs_claus: {
        keywords: ["woman", "female", "xmas", "mother christmas"],
        char: "\u{1F936}",
        fitzpatrick_scale: true,
        category: "people"
      },
      santa: {
        keywords: ["festival", "man", "male", "xmas", "father christmas"],
        char: "\u{1F385}",
        fitzpatrick_scale: true,
        category: "people"
      },
      sorceress: {
        keywords: ["woman", "female", "mage", "witch"],
        char: "\u{1F9D9}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      wizard: {
        keywords: ["man", "male", "mage", "sorcerer"],
        char: "\u{1F9D9}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_elf: {
        keywords: ["woman", "female"],
        char: "\u{1F9DD}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_elf: {
        keywords: ["man", "male"],
        char: "\u{1F9DD}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_vampire: {
        keywords: ["woman", "female"],
        char: "\u{1F9DB}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_vampire: {
        keywords: ["man", "male", "dracula"],
        char: "\u{1F9DB}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_zombie: {
        keywords: ["woman", "female", "undead", "walking dead"],
        char: "\u{1F9DF}\u200D\u2640\uFE0F",
        fitzpatrick_scale: false,
        category: "people"
      },
      man_zombie: {
        keywords: ["man", "male", "dracula", "undead", "walking dead"],
        char: "\u{1F9DF}\u200D\u2642\uFE0F",
        fitzpatrick_scale: false,
        category: "people"
      },
      woman_genie: {
        keywords: ["woman", "female"],
        char: "\u{1F9DE}\u200D\u2640\uFE0F",
        fitzpatrick_scale: false,
        category: "people"
      },
      man_genie: {
        keywords: ["man", "male"],
        char: "\u{1F9DE}\u200D\u2642\uFE0F",
        fitzpatrick_scale: false,
        category: "people"
      },
      mermaid: {
        keywords: ["woman", "female", "merwoman", "ariel"],
        char: "\u{1F9DC}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      merman: {
        keywords: ["man", "male", "triton"],
        char: "\u{1F9DC}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_fairy: {
        keywords: ["woman", "female"],
        char: "\u{1F9DA}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_fairy: {
        keywords: ["man", "male"],
        char: "\u{1F9DA}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      angel: {
        keywords: ["heaven", "wings", "halo"],
        char: "\u{1F47C}",
        fitzpatrick_scale: true,
        category: "people"
      },
      pregnant_woman: {
        keywords: ["baby"],
        char: "\u{1F930}",
        fitzpatrick_scale: true,
        category: "people"
      },
      breastfeeding: {
        keywords: ["nursing", "baby"],
        char: "\u{1F931}",
        fitzpatrick_scale: true,
        category: "people"
      },
      princess: {
        keywords: ["girl", "woman", "female", "blond", "crown", "royal", "queen"],
        char: "\u{1F478}",
        fitzpatrick_scale: true,
        category: "people"
      },
      prince: {
        keywords: ["boy", "man", "male", "crown", "royal", "king"],
        char: "\u{1F934}",
        fitzpatrick_scale: true,
        category: "people"
      },
      bride_with_veil: {
        keywords: ["couple", "marriage", "wedding", "woman", "bride"],
        char: "\u{1F470}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_in_tuxedo: {
        keywords: ["couple", "marriage", "wedding", "groom"],
        char: "\u{1F935}",
        fitzpatrick_scale: true,
        category: "people"
      },
      running_woman: {
        keywords: ["woman", "walking", "exercise", "race", "running", "female"],
        char: "\u{1F3C3}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      running_man: {
        keywords: ["man", "walking", "exercise", "race", "running"],
        char: "\u{1F3C3}",
        fitzpatrick_scale: true,
        category: "people"
      },
      walking_woman: {
        keywords: ["human", "feet", "steps", "woman", "female"],
        char: "\u{1F6B6}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      walking_man: {
        keywords: ["human", "feet", "steps"],
        char: "\u{1F6B6}",
        fitzpatrick_scale: true,
        category: "people"
      },
      dancer: {
        keywords: ["female", "girl", "woman", "fun"],
        char: "\u{1F483}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_dancing: {
        keywords: ["male", "boy", "fun", "dancer"],
        char: "\u{1F57A}",
        fitzpatrick_scale: true,
        category: "people"
      },
      dancing_women: {
        keywords: ["female", "bunny", "women", "girls"],
        char: "\u{1F46F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      dancing_men: {
        keywords: ["male", "bunny", "men", "boys"],
        char: "\u{1F46F}\u200D\u2642\uFE0F",
        fitzpatrick_scale: false,
        category: "people"
      },
      couple: {
        keywords: ["pair", "people", "human", "love", "date", "dating", "like", "affection", "valentines", "marriage"],
        char: "\u{1F46B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      two_men_holding_hands: {
        keywords: ["pair", "couple", "love", "like", "bromance", "friendship", "people", "human"],
        char: "\u{1F46C}",
        fitzpatrick_scale: false,
        category: "people"
      },
      two_women_holding_hands: {
        keywords: ["pair", "friendship", "couple", "love", "like", "female", "people", "human"],
        char: "\u{1F46D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      bowing_woman: {
        keywords: ["woman", "female", "girl"],
        char: "\u{1F647}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      bowing_man: {
        keywords: ["man", "male", "boy"],
        char: "\u{1F647}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_facepalming: {
        keywords: ["man", "male", "boy", "disbelief"],
        char: "\u{1F926}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_facepalming: {
        keywords: ["woman", "female", "girl", "disbelief"],
        char: "\u{1F926}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_shrugging: {
        keywords: ["woman", "female", "girl", "confused", "indifferent", "doubt"],
        char: "\u{1F937}",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_shrugging: {
        keywords: ["man", "male", "boy", "confused", "indifferent", "doubt"],
        char: "\u{1F937}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      tipping_hand_woman: {
        keywords: ["female", "girl", "woman", "human", "information"],
        char: "\u{1F481}",
        fitzpatrick_scale: true,
        category: "people"
      },
      tipping_hand_man: {
        keywords: ["male", "boy", "man", "human", "information"],
        char: "\u{1F481}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      no_good_woman: {
        keywords: ["female", "girl", "woman", "nope"],
        char: "\u{1F645}",
        fitzpatrick_scale: true,
        category: "people"
      },
      no_good_man: {
        keywords: ["male", "boy", "man", "nope"],
        char: "\u{1F645}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      ok_woman: {
        keywords: ["women", "girl", "female", "pink", "human", "woman"],
        char: "\u{1F646}",
        fitzpatrick_scale: true,
        category: "people"
      },
      ok_man: {
        keywords: ["men", "boy", "male", "blue", "human", "man"],
        char: "\u{1F646}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      raising_hand_woman: {
        keywords: ["female", "girl", "woman"],
        char: "\u{1F64B}",
        fitzpatrick_scale: true,
        category: "people"
      },
      raising_hand_man: {
        keywords: ["male", "boy", "man"],
        char: "\u{1F64B}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      pouting_woman: {
        keywords: ["female", "girl", "woman"],
        char: "\u{1F64E}",
        fitzpatrick_scale: true,
        category: "people"
      },
      pouting_man: {
        keywords: ["male", "boy", "man"],
        char: "\u{1F64E}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      frowning_woman: {
        keywords: ["female", "girl", "woman", "sad", "depressed", "discouraged", "unhappy"],
        char: "\u{1F64D}",
        fitzpatrick_scale: true,
        category: "people"
      },
      frowning_man: {
        keywords: ["male", "boy", "man", "sad", "depressed", "discouraged", "unhappy"],
        char: "\u{1F64D}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      haircut_woman: {
        keywords: ["female", "girl", "woman"],
        char: "\u{1F487}",
        fitzpatrick_scale: true,
        category: "people"
      },
      haircut_man: {
        keywords: ["male", "boy", "man"],
        char: "\u{1F487}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      massage_woman: {
        keywords: ["female", "girl", "woman", "head"],
        char: "\u{1F486}",
        fitzpatrick_scale: true,
        category: "people"
      },
      massage_man: {
        keywords: ["male", "boy", "man", "head"],
        char: "\u{1F486}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      woman_in_steamy_room: {
        keywords: ["female", "woman", "spa", "steamroom", "sauna"],
        char: "\u{1F9D6}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      man_in_steamy_room: {
        keywords: ["male", "man", "spa", "steamroom", "sauna"],
        char: "\u{1F9D6}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "people"
      },
      couple_with_heart_woman_man: {
        keywords: ["pair", "love", "like", "affection", "human", "dating", "valentines", "marriage"],
        char: "\u{1F491}",
        fitzpatrick_scale: false,
        category: "people"
      },
      couple_with_heart_woman_woman: {
        keywords: ["pair", "love", "like", "affection", "human", "dating", "valentines", "marriage"],
        char: "\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F469}",
        fitzpatrick_scale: false,
        category: "people"
      },
      couple_with_heart_man_man: {
        keywords: ["pair", "love", "like", "affection", "human", "dating", "valentines", "marriage"],
        char: "\u{1F468}\u200D\u2764\uFE0F\u200D\u{1F468}",
        fitzpatrick_scale: false,
        category: "people"
      },
      couplekiss_man_woman: {
        keywords: ["pair", "valentines", "love", "like", "dating", "marriage"],
        char: "\u{1F48F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      couplekiss_woman_woman: {
        keywords: ["pair", "valentines", "love", "like", "dating", "marriage"],
        char: "\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F48B}\u200D\u{1F469}",
        fitzpatrick_scale: false,
        category: "people"
      },
      couplekiss_man_man: {
        keywords: ["pair", "valentines", "love", "like", "dating", "marriage"],
        char: "\u{1F468}\u200D\u2764\uFE0F\u200D\u{1F48B}\u200D\u{1F468}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_woman_boy: {
        keywords: ["home", "parents", "child", "mom", "dad", "father", "mother", "people", "human"],
        char: "\u{1F46A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_woman_girl: {
        keywords: ["home", "parents", "people", "human", "child"],
        char: "\u{1F468}\u200D\u{1F469}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_woman_girl_boy: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_woman_boy_boy: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_woman_girl_girl: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_woman_boy: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F469}\u200D\u{1F469}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_woman_girl: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F469}\u200D\u{1F469}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_woman_girl_boy: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F469}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_woman_boy_boy: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F469}\u200D\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_woman_girl_girl: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F469}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_man_boy: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F468}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_man_girl: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F468}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_man_girl_boy: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F468}\u200D\u{1F467}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_man_boy_boy: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F468}\u200D\u{1F466}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_man_girl_girl: {
        keywords: ["home", "parents", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F468}\u200D\u{1F467}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_boy: {
        keywords: ["home", "parent", "people", "human", "child"],
        char: "\u{1F469}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_girl: {
        keywords: ["home", "parent", "people", "human", "child"],
        char: "\u{1F469}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_girl_boy: {
        keywords: ["home", "parent", "people", "human", "children"],
        char: "\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_boy_boy: {
        keywords: ["home", "parent", "people", "human", "children"],
        char: "\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_woman_girl_girl: {
        keywords: ["home", "parent", "people", "human", "children"],
        char: "\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_boy: {
        keywords: ["home", "parent", "people", "human", "child"],
        char: "\u{1F468}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_girl: {
        keywords: ["home", "parent", "people", "human", "child"],
        char: "\u{1F468}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_girl_boy: {
        keywords: ["home", "parent", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F467}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_boy_boy: {
        keywords: ["home", "parent", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F466}\u200D\u{1F466}",
        fitzpatrick_scale: false,
        category: "people"
      },
      family_man_girl_girl: {
        keywords: ["home", "parent", "people", "human", "children"],
        char: "\u{1F468}\u200D\u{1F467}\u200D\u{1F467}",
        fitzpatrick_scale: false,
        category: "people"
      },
      yarn: {
        keywords: ["ball", "crochet", "knit"],
        char: "\u{1F9F6}",
        fitzpatrick_scale: false,
        category: "people"
      },
      thread: {
        keywords: ["needle", "sewing", "spool", "string"],
        char: "\u{1F9F5}",
        fitzpatrick_scale: false,
        category: "people"
      },
      coat: {
        keywords: ["jacket"],
        char: "\u{1F9E5}",
        fitzpatrick_scale: false,
        category: "people"
      },
      labcoat: {
        keywords: ["doctor", "experiment", "scientist", "chemist"],
        char: "\u{1F97C}",
        fitzpatrick_scale: false,
        category: "people"
      },
      womans_clothes: {
        keywords: ["fashion", "shopping_bags", "female"],
        char: "\u{1F45A}",
        fitzpatrick_scale: false,
        category: "people"
      },
      tshirt: {
        keywords: ["fashion", "cloth", "casual", "shirt", "tee"],
        char: "\u{1F455}",
        fitzpatrick_scale: false,
        category: "people"
      },
      jeans: {
        keywords: ["fashion", "shopping"],
        char: "\u{1F456}",
        fitzpatrick_scale: false,
        category: "people"
      },
      necktie: {
        keywords: ["shirt", "suitup", "formal", "fashion", "cloth", "business"],
        char: "\u{1F454}",
        fitzpatrick_scale: false,
        category: "people"
      },
      dress: {
        keywords: ["clothes", "fashion", "shopping"],
        char: "\u{1F457}",
        fitzpatrick_scale: false,
        category: "people"
      },
      bikini: {
        keywords: ["swimming", "female", "woman", "girl", "fashion", "beach", "summer"],
        char: "\u{1F459}",
        fitzpatrick_scale: false,
        category: "people"
      },
      kimono: {
        keywords: ["dress", "fashion", "women", "female", "japanese"],
        char: "\u{1F458}",
        fitzpatrick_scale: false,
        category: "people"
      },
      lipstick: {
        keywords: ["female", "girl", "fashion", "woman"],
        char: "\u{1F484}",
        fitzpatrick_scale: false,
        category: "people"
      },
      kiss: {
        keywords: ["face", "lips", "love", "like", "affection", "valentines"],
        char: "\u{1F48B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      footprints: {
        keywords: ["feet", "tracking", "walking", "beach"],
        char: "\u{1F463}",
        fitzpatrick_scale: false,
        category: "people"
      },
      flat_shoe: {
        keywords: ["ballet", "slip-on", "slipper"],
        char: "\u{1F97F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      high_heel: {
        keywords: ["fashion", "shoes", "female", "pumps", "stiletto"],
        char: "\u{1F460}",
        fitzpatrick_scale: false,
        category: "people"
      },
      sandal: {
        keywords: ["shoes", "fashion", "flip flops"],
        char: "\u{1F461}",
        fitzpatrick_scale: false,
        category: "people"
      },
      boot: {
        keywords: ["shoes", "fashion"],
        char: "\u{1F462}",
        fitzpatrick_scale: false,
        category: "people"
      },
      mans_shoe: {
        keywords: ["fashion", "male"],
        char: "\u{1F45E}",
        fitzpatrick_scale: false,
        category: "people"
      },
      athletic_shoe: {
        keywords: ["shoes", "sports", "sneakers"],
        char: "\u{1F45F}",
        fitzpatrick_scale: false,
        category: "people"
      },
      hiking_boot: {
        keywords: ["backpacking", "camping", "hiking"],
        char: "\u{1F97E}",
        fitzpatrick_scale: false,
        category: "people"
      },
      socks: {
        keywords: ["stockings", "clothes"],
        char: "\u{1F9E6}",
        fitzpatrick_scale: false,
        category: "people"
      },
      gloves: {
        keywords: ["hands", "winter", "clothes"],
        char: "\u{1F9E4}",
        fitzpatrick_scale: false,
        category: "people"
      },
      scarf: {
        keywords: ["neck", "winter", "clothes"],
        char: "\u{1F9E3}",
        fitzpatrick_scale: false,
        category: "people"
      },
      womans_hat: {
        keywords: ["fashion", "accessories", "female", "lady", "spring"],
        char: "\u{1F452}",
        fitzpatrick_scale: false,
        category: "people"
      },
      tophat: {
        keywords: ["magic", "gentleman", "classy", "circus"],
        char: "\u{1F3A9}",
        fitzpatrick_scale: false,
        category: "people"
      },
      billed_hat: {
        keywords: ["cap", "baseball"],
        char: "\u{1F9E2}",
        fitzpatrick_scale: false,
        category: "people"
      },
      rescue_worker_helmet: {
        keywords: ["construction", "build"],
        char: "\u26D1",
        fitzpatrick_scale: false,
        category: "people"
      },
      mortar_board: {
        keywords: ["school", "college", "degree", "university", "graduation", "cap", "hat", "legal", "learn", "education"],
        char: "\u{1F393}",
        fitzpatrick_scale: false,
        category: "people"
      },
      crown: {
        keywords: ["king", "kod", "leader", "royalty", "lord"],
        char: "\u{1F451}",
        fitzpatrick_scale: false,
        category: "people"
      },
      school_satchel: {
        keywords: ["student", "education", "bag", "backpack"],
        char: "\u{1F392}",
        fitzpatrick_scale: false,
        category: "people"
      },
      luggage: {
        keywords: ["packing", "travel"],
        char: "\u{1F9F3}",
        fitzpatrick_scale: false,
        category: "people"
      },
      pouch: {
        keywords: ["bag", "accessories", "shopping"],
        char: "\u{1F45D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      purse: {
        keywords: ["fashion", "accessories", "money", "sales", "shopping"],
        char: "\u{1F45B}",
        fitzpatrick_scale: false,
        category: "people"
      },
      handbag: {
        keywords: ["fashion", "accessory", "accessories", "shopping"],
        char: "\u{1F45C}",
        fitzpatrick_scale: false,
        category: "people"
      },
      briefcase: {
        keywords: ["business", "documents", "work", "law", "legal", "job", "career"],
        char: "\u{1F4BC}",
        fitzpatrick_scale: false,
        category: "people"
      },
      eyeglasses: {
        keywords: ["fashion", "accessories", "eyesight", "nerdy", "dork", "geek"],
        char: "\u{1F453}",
        fitzpatrick_scale: false,
        category: "people"
      },
      dark_sunglasses: {
        keywords: ["face", "cool", "accessories"],
        char: "\u{1F576}",
        fitzpatrick_scale: false,
        category: "people"
      },
      goggles: {
        keywords: ["eyes", "protection", "safety"],
        char: "\u{1F97D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      ring: {
        keywords: ["wedding", "propose", "marriage", "valentines", "diamond", "fashion", "jewelry", "gem", "engagement"],
        char: "\u{1F48D}",
        fitzpatrick_scale: false,
        category: "people"
      },
      closed_umbrella: {
        keywords: ["weather", "rain", "drizzle"],
        char: "\u{1F302}",
        fitzpatrick_scale: false,
        category: "people"
      },
      dog: {
        keywords: ["animal", "friend", "nature", "woof", "puppy", "pet", "faithful"],
        char: "\u{1F436}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cat: {
        keywords: ["animal", "meow", "nature", "pet", "kitten"],
        char: "\u{1F431}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      mouse: {
        keywords: ["animal", "nature", "cheese_wedge", "rodent"],
        char: "\u{1F42D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      hamster: {
        keywords: ["animal", "nature"],
        char: "\u{1F439}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      rabbit: {
        keywords: ["animal", "nature", "pet", "spring", "magic", "bunny"],
        char: "\u{1F430}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      fox_face: {
        keywords: ["animal", "nature", "face"],
        char: "\u{1F98A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      bear: {
        keywords: ["animal", "nature", "wild"],
        char: "\u{1F43B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      panda_face: {
        keywords: ["animal", "nature", "panda"],
        char: "\u{1F43C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      koala: {
        keywords: ["animal", "nature"],
        char: "\u{1F428}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      tiger: {
        keywords: ["animal", "cat", "danger", "wild", "nature", "roar"],
        char: "\u{1F42F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      lion: {
        keywords: ["animal", "nature"],
        char: "\u{1F981}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cow: {
        keywords: ["beef", "ox", "animal", "nature", "moo", "milk"],
        char: "\u{1F42E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      pig: {
        keywords: ["animal", "oink", "nature"],
        char: "\u{1F437}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      pig_nose: {
        keywords: ["animal", "oink"],
        char: "\u{1F43D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      frog: {
        keywords: ["animal", "nature", "croak", "toad"],
        char: "\u{1F438}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      squid: {
        keywords: ["animal", "nature", "ocean", "sea"],
        char: "\u{1F991}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      octopus: {
        keywords: ["animal", "creature", "ocean", "sea", "nature", "beach"],
        char: "\u{1F419}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      shrimp: {
        keywords: ["animal", "ocean", "nature", "seafood"],
        char: "\u{1F990}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      monkey_face: {
        keywords: ["animal", "nature", "circus"],
        char: "\u{1F435}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      gorilla: {
        keywords: ["animal", "nature", "circus"],
        char: "\u{1F98D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      see_no_evil: {
        keywords: ["monkey", "animal", "nature", "haha"],
        char: "\u{1F648}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      hear_no_evil: {
        keywords: ["animal", "monkey", "nature"],
        char: "\u{1F649}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      speak_no_evil: {
        keywords: ["monkey", "animal", "nature", "omg"],
        char: "\u{1F64A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      monkey: {
        keywords: ["animal", "nature", "banana", "circus"],
        char: "\u{1F412}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      chicken: {
        keywords: ["animal", "cluck", "nature", "bird"],
        char: "\u{1F414}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      penguin: {
        keywords: ["animal", "nature"],
        char: "\u{1F427}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      bird: {
        keywords: ["animal", "nature", "fly", "tweet", "spring"],
        char: "\u{1F426}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      baby_chick: {
        keywords: ["animal", "chicken", "bird"],
        char: "\u{1F424}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      hatching_chick: {
        keywords: ["animal", "chicken", "egg", "born", "baby", "bird"],
        char: "\u{1F423}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      hatched_chick: {
        keywords: ["animal", "chicken", "baby", "bird"],
        char: "\u{1F425}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      duck: {
        keywords: ["animal", "nature", "bird", "mallard"],
        char: "\u{1F986}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      eagle: {
        keywords: ["animal", "nature", "bird"],
        char: "\u{1F985}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      owl: {
        keywords: ["animal", "nature", "bird", "hoot"],
        char: "\u{1F989}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      bat: {
        keywords: ["animal", "nature", "blind", "vampire"],
        char: "\u{1F987}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      wolf: {
        keywords: ["animal", "nature", "wild"],
        char: "\u{1F43A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      boar: {
        keywords: ["animal", "nature"],
        char: "\u{1F417}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      horse: {
        keywords: ["animal", "brown", "nature"],
        char: "\u{1F434}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      unicorn: {
        keywords: ["animal", "nature", "mystical"],
        char: "\u{1F984}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      honeybee: {
        keywords: ["animal", "insect", "nature", "bug", "spring", "honey"],
        char: "\u{1F41D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      bug: {
        keywords: ["animal", "insect", "nature", "worm"],
        char: "\u{1F41B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      butterfly: {
        keywords: ["animal", "insect", "nature", "caterpillar"],
        char: "\u{1F98B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      snail: {
        keywords: ["slow", "animal", "shell"],
        char: "\u{1F40C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      beetle: {
        keywords: ["animal", "insect", "nature", "ladybug"],
        char: "\u{1F41E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      ant: {
        keywords: ["animal", "insect", "nature", "bug"],
        char: "\u{1F41C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      grasshopper: {
        keywords: ["animal", "cricket", "chirp"],
        char: "\u{1F997}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      spider: {
        keywords: ["animal", "arachnid"],
        char: "\u{1F577}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      scorpion: {
        keywords: ["animal", "arachnid"],
        char: "\u{1F982}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      crab: {
        keywords: ["animal", "crustacean"],
        char: "\u{1F980}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      snake: {
        keywords: ["animal", "evil", "nature", "hiss", "python"],
        char: "\u{1F40D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      lizard: {
        keywords: ["animal", "nature", "reptile"],
        char: "\u{1F98E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      "t-rex": {
        keywords: ["animal", "nature", "dinosaur", "tyrannosaurus", "extinct"],
        char: "\u{1F996}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sauropod: {
        keywords: ["animal", "nature", "dinosaur", "brachiosaurus", "brontosaurus", "diplodocus", "extinct"],
        char: "\u{1F995}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      turtle: {
        keywords: ["animal", "slow", "nature", "tortoise"],
        char: "\u{1F422}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      tropical_fish: {
        keywords: ["animal", "swim", "ocean", "beach", "nemo"],
        char: "\u{1F420}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      fish: {
        keywords: ["animal", "food", "nature"],
        char: "\u{1F41F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      blowfish: {
        keywords: ["animal", "nature", "food", "sea", "ocean"],
        char: "\u{1F421}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      dolphin: {
        keywords: ["animal", "nature", "fish", "sea", "ocean", "flipper", "fins", "beach"],
        char: "\u{1F42C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      shark: {
        keywords: ["animal", "nature", "fish", "sea", "ocean", "jaws", "fins", "beach"],
        char: "\u{1F988}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      whale: {
        keywords: ["animal", "nature", "sea", "ocean"],
        char: "\u{1F433}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      whale2: {
        keywords: ["animal", "nature", "sea", "ocean"],
        char: "\u{1F40B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      crocodile: {
        keywords: ["animal", "nature", "reptile", "lizard", "alligator"],
        char: "\u{1F40A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      leopard: {
        keywords: ["animal", "nature"],
        char: "\u{1F406}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      zebra: {
        keywords: ["animal", "nature", "stripes", "safari"],
        char: "\u{1F993}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      tiger2: {
        keywords: ["animal", "nature", "roar"],
        char: "\u{1F405}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      water_buffalo: {
        keywords: ["animal", "nature", "ox", "cow"],
        char: "\u{1F403}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      ox: {
        keywords: ["animal", "cow", "beef"],
        char: "\u{1F402}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cow2: {
        keywords: ["beef", "ox", "animal", "nature", "moo", "milk"],
        char: "\u{1F404}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      deer: {
        keywords: ["animal", "nature", "horns", "venison"],
        char: "\u{1F98C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      dromedary_camel: {
        keywords: ["animal", "hot", "desert", "hump"],
        char: "\u{1F42A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      camel: {
        keywords: ["animal", "nature", "hot", "desert", "hump"],
        char: "\u{1F42B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      giraffe: {
        keywords: ["animal", "nature", "spots", "safari"],
        char: "\u{1F992}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      elephant: {
        keywords: ["animal", "nature", "nose", "th", "circus"],
        char: "\u{1F418}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      rhinoceros: {
        keywords: ["animal", "nature", "horn"],
        char: "\u{1F98F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      goat: {
        keywords: ["animal", "nature"],
        char: "\u{1F410}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      ram: {
        keywords: ["animal", "sheep", "nature"],
        char: "\u{1F40F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sheep: {
        keywords: ["animal", "nature", "wool", "shipit"],
        char: "\u{1F411}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      racehorse: {
        keywords: ["animal", "gamble", "luck"],
        char: "\u{1F40E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      pig2: {
        keywords: ["animal", "nature"],
        char: "\u{1F416}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      rat: {
        keywords: ["animal", "mouse", "rodent"],
        char: "\u{1F400}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      mouse2: {
        keywords: ["animal", "nature", "rodent"],
        char: "\u{1F401}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      rooster: {
        keywords: ["animal", "nature", "chicken"],
        char: "\u{1F413}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      turkey: {
        keywords: ["animal", "bird"],
        char: "\u{1F983}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      dove: {
        keywords: ["animal", "bird"],
        char: "\u{1F54A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      dog2: {
        keywords: ["animal", "nature", "friend", "doge", "pet", "faithful"],
        char: "\u{1F415}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      poodle: {
        keywords: ["dog", "animal", "101", "nature", "pet"],
        char: "\u{1F429}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cat2: {
        keywords: ["animal", "meow", "pet", "cats"],
        char: "\u{1F408}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      rabbit2: {
        keywords: ["animal", "nature", "pet", "magic", "spring"],
        char: "\u{1F407}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      chipmunk: {
        keywords: ["animal", "nature", "rodent", "squirrel"],
        char: "\u{1F43F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      hedgehog: {
        keywords: ["animal", "nature", "spiny"],
        char: "\u{1F994}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      raccoon: {
        keywords: ["animal", "nature"],
        char: "\u{1F99D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      llama: {
        keywords: ["animal", "nature", "alpaca"],
        char: "\u{1F999}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      hippopotamus: {
        keywords: ["animal", "nature"],
        char: "\u{1F99B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      kangaroo: {
        keywords: ["animal", "nature", "australia", "joey", "hop", "marsupial"],
        char: "\u{1F998}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      badger: {
        keywords: ["animal", "nature", "honey"],
        char: "\u{1F9A1}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      swan: {
        keywords: ["animal", "nature", "bird"],
        char: "\u{1F9A2}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      peacock: {
        keywords: ["animal", "nature", "peahen", "bird"],
        char: "\u{1F99A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      parrot: {
        keywords: ["animal", "nature", "bird", "pirate", "talk"],
        char: "\u{1F99C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      lobster: {
        keywords: ["animal", "nature", "bisque", "claws", "seafood"],
        char: "\u{1F99E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      mosquito: {
        keywords: ["animal", "nature", "insect", "malaria"],
        char: "\u{1F99F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      paw_prints: {
        keywords: ["animal", "tracking", "footprints", "dog", "cat", "pet", "feet"],
        char: "\u{1F43E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      dragon: {
        keywords: ["animal", "myth", "nature", "chinese", "green"],
        char: "\u{1F409}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      dragon_face: {
        keywords: ["animal", "myth", "nature", "chinese", "green"],
        char: "\u{1F432}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cactus: {
        keywords: ["vegetable", "plant", "nature"],
        char: "\u{1F335}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      christmas_tree: {
        keywords: ["festival", "vacation", "december", "xmas", "celebration"],
        char: "\u{1F384}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      evergreen_tree: {
        keywords: ["plant", "nature"],
        char: "\u{1F332}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      deciduous_tree: {
        keywords: ["plant", "nature"],
        char: "\u{1F333}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      palm_tree: {
        keywords: ["plant", "vegetable", "nature", "summer", "beach", "mojito", "tropical"],
        char: "\u{1F334}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      seedling: {
        keywords: ["plant", "nature", "grass", "lawn", "spring"],
        char: "\u{1F331}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      herb: {
        keywords: ["vegetable", "plant", "medicine", "weed", "grass", "lawn"],
        char: "\u{1F33F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      shamrock: {
        keywords: ["vegetable", "plant", "nature", "irish", "clover"],
        char: "\u2618",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      four_leaf_clover: {
        keywords: ["vegetable", "plant", "nature", "lucky", "irish"],
        char: "\u{1F340}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      bamboo: {
        keywords: ["plant", "nature", "vegetable", "panda", "pine_decoration"],
        char: "\u{1F38D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      tanabata_tree: {
        keywords: ["plant", "nature", "branch", "summer"],
        char: "\u{1F38B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      leaves: {
        keywords: ["nature", "plant", "tree", "vegetable", "grass", "lawn", "spring"],
        char: "\u{1F343}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      fallen_leaf: {
        keywords: ["nature", "plant", "vegetable", "leaves"],
        char: "\u{1F342}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      maple_leaf: {
        keywords: ["nature", "plant", "vegetable", "ca", "fall"],
        char: "\u{1F341}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      ear_of_rice: {
        keywords: ["nature", "plant"],
        char: "\u{1F33E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      hibiscus: {
        keywords: ["plant", "vegetable", "flowers", "beach"],
        char: "\u{1F33A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sunflower: {
        keywords: ["nature", "plant", "fall"],
        char: "\u{1F33B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      rose: {
        keywords: ["flowers", "valentines", "love", "spring"],
        char: "\u{1F339}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      wilted_flower: {
        keywords: ["plant", "nature", "flower"],
        char: "\u{1F940}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      tulip: {
        keywords: ["flowers", "plant", "nature", "summer", "spring"],
        char: "\u{1F337}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      blossom: {
        keywords: ["nature", "flowers", "yellow"],
        char: "\u{1F33C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cherry_blossom: {
        keywords: ["nature", "plant", "spring", "flower"],
        char: "\u{1F338}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      bouquet: {
        keywords: ["flowers", "nature", "spring"],
        char: "\u{1F490}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      mushroom: {
        keywords: ["plant", "vegetable"],
        char: "\u{1F344}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      chestnut: {
        keywords: ["food", "squirrel"],
        char: "\u{1F330}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      jack_o_lantern: {
        keywords: ["halloween", "light", "pumpkin", "creepy", "fall"],
        char: "\u{1F383}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      shell: {
        keywords: ["nature", "sea", "beach"],
        char: "\u{1F41A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      spider_web: {
        keywords: ["animal", "insect", "arachnid", "silk"],
        char: "\u{1F578}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      earth_americas: {
        keywords: ["globe", "world", "USA", "international"],
        char: "\u{1F30E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      earth_africa: {
        keywords: ["globe", "world", "international"],
        char: "\u{1F30D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      earth_asia: {
        keywords: ["globe", "world", "east", "international"],
        char: "\u{1F30F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      full_moon: {
        keywords: ["nature", "yellow", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F315}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      waning_gibbous_moon: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep", "waxing_gibbous_moon"],
        char: "\u{1F316}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      last_quarter_moon: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F317}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      waning_crescent_moon: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F318}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      new_moon: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F311}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      waxing_crescent_moon: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F312}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      first_quarter_moon: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F313}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      waxing_gibbous_moon: {
        keywords: ["nature", "night", "sky", "gray", "twilight", "planet", "space", "evening", "sleep"],
        char: "\u{1F314}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      new_moon_with_face: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F31A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      full_moon_with_face: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F31D}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      first_quarter_moon_with_face: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F31B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      last_quarter_moon_with_face: {
        keywords: ["nature", "twilight", "planet", "space", "night", "evening", "sleep"],
        char: "\u{1F31C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sun_with_face: {
        keywords: ["nature", "morning", "sky"],
        char: "\u{1F31E}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      crescent_moon: {
        keywords: ["night", "sleep", "sky", "evening", "magic"],
        char: "\u{1F319}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      star: {
        keywords: ["night", "yellow"],
        char: "\u2B50",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      star2: {
        keywords: ["night", "sparkle", "awesome", "good", "magic"],
        char: "\u{1F31F}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      dizzy: {
        keywords: ["star", "sparkle", "shoot", "magic"],
        char: "\u{1F4AB}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sparkles: {
        keywords: ["stars", "shine", "shiny", "cool", "awesome", "good", "magic"],
        char: "\u2728",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      comet: {
        keywords: ["space"],
        char: "\u2604",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sunny: {
        keywords: ["weather", "nature", "brightness", "summer", "beach", "spring"],
        char: "\u2600\uFE0F",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sun_behind_small_cloud: {
        keywords: ["weather"],
        char: "\u{1F324}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      partly_sunny: {
        keywords: ["weather", "nature", "cloudy", "morning", "fall", "spring"],
        char: "\u26C5",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sun_behind_large_cloud: {
        keywords: ["weather"],
        char: "\u{1F325}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sun_behind_rain_cloud: {
        keywords: ["weather"],
        char: "\u{1F326}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cloud: {
        keywords: ["weather", "sky"],
        char: "\u2601\uFE0F",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cloud_with_rain: {
        keywords: ["weather"],
        char: "\u{1F327}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cloud_with_lightning_and_rain: {
        keywords: ["weather", "lightning"],
        char: "\u26C8",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cloud_with_lightning: {
        keywords: ["weather", "thunder"],
        char: "\u{1F329}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      zap: {
        keywords: ["thunder", "weather", "lightning bolt", "fast"],
        char: "\u26A1",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      fire: {
        keywords: ["hot", "cook", "flame"],
        char: "\u{1F525}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      boom: {
        keywords: ["bomb", "explode", "explosion", "collision", "blown"],
        char: "\u{1F4A5}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      snowflake: {
        keywords: ["winter", "season", "cold", "weather", "christmas", "xmas"],
        char: "\u2744\uFE0F",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      cloud_with_snow: {
        keywords: ["weather"],
        char: "\u{1F328}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      snowman: {
        keywords: ["winter", "season", "cold", "weather", "christmas", "xmas", "frozen", "without_snow"],
        char: "\u26C4",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      snowman_with_snow: {
        keywords: ["winter", "season", "cold", "weather", "christmas", "xmas", "frozen"],
        char: "\u2603",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      wind_face: {
        keywords: ["gust", "air"],
        char: "\u{1F32C}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      dash: {
        keywords: ["wind", "air", "fast", "shoo", "fart", "smoke", "puff"],
        char: "\u{1F4A8}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      tornado: {
        keywords: ["weather", "cyclone", "twister"],
        char: "\u{1F32A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      fog: {
        keywords: ["weather"],
        char: "\u{1F32B}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      open_umbrella: {
        keywords: ["weather", "spring"],
        char: "\u2602",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      umbrella: {
        keywords: ["rainy", "weather", "spring"],
        char: "\u2614",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      droplet: {
        keywords: ["water", "drip", "faucet", "spring"],
        char: "\u{1F4A7}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      sweat_drops: {
        keywords: ["water", "drip", "oops"],
        char: "\u{1F4A6}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      ocean: {
        keywords: ["sea", "water", "wave", "nature", "tsunami", "disaster"],
        char: "\u{1F30A}",
        fitzpatrick_scale: false,
        category: "animals_and_nature"
      },
      green_apple: {
        keywords: ["fruit", "nature"],
        char: "\u{1F34F}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      apple: {
        keywords: ["fruit", "mac", "school"],
        char: "\u{1F34E}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      pear: {
        keywords: ["fruit", "nature", "food"],
        char: "\u{1F350}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      tangerine: {
        keywords: ["food", "fruit", "nature", "orange"],
        char: "\u{1F34A}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      lemon: {
        keywords: ["fruit", "nature"],
        char: "\u{1F34B}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      banana: {
        keywords: ["fruit", "food", "monkey"],
        char: "\u{1F34C}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      watermelon: {
        keywords: ["fruit", "food", "picnic", "summer"],
        char: "\u{1F349}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      grapes: {
        keywords: ["fruit", "food", "wine"],
        char: "\u{1F347}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      strawberry: {
        keywords: ["fruit", "food", "nature"],
        char: "\u{1F353}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      melon: {
        keywords: ["fruit", "nature", "food"],
        char: "\u{1F348}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      cherries: {
        keywords: ["food", "fruit"],
        char: "\u{1F352}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      peach: {
        keywords: ["fruit", "nature", "food"],
        char: "\u{1F351}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      pineapple: {
        keywords: ["fruit", "nature", "food"],
        char: "\u{1F34D}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      coconut: {
        keywords: ["fruit", "nature", "food", "palm"],
        char: "\u{1F965}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      kiwi_fruit: {
        keywords: ["fruit", "food"],
        char: "\u{1F95D}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      mango: {
        keywords: ["fruit", "food", "tropical"],
        char: "\u{1F96D}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      avocado: {
        keywords: ["fruit", "food"],
        char: "\u{1F951}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      broccoli: {
        keywords: ["fruit", "food", "vegetable"],
        char: "\u{1F966}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      tomato: {
        keywords: ["fruit", "vegetable", "nature", "food"],
        char: "\u{1F345}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      eggplant: {
        keywords: ["vegetable", "nature", "food", "aubergine"],
        char: "\u{1F346}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      cucumber: {
        keywords: ["fruit", "food", "pickle"],
        char: "\u{1F952}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      carrot: {
        keywords: ["vegetable", "food", "orange"],
        char: "\u{1F955}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      hot_pepper: {
        keywords: ["food", "spicy", "chilli", "chili"],
        char: "\u{1F336}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      potato: {
        keywords: ["food", "tuber", "vegatable", "starch"],
        char: "\u{1F954}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      corn: {
        keywords: ["food", "vegetable", "plant"],
        char: "\u{1F33D}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      leafy_greens: {
        keywords: ["food", "vegetable", "plant", "bok choy", "cabbage", "kale", "lettuce"],
        char: "\u{1F96C}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      sweet_potato: {
        keywords: ["food", "nature"],
        char: "\u{1F360}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      peanuts: {
        keywords: ["food", "nut"],
        char: "\u{1F95C}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      honey_pot: {
        keywords: ["bees", "sweet", "kitchen"],
        char: "\u{1F36F}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      croissant: {
        keywords: ["food", "bread", "french"],
        char: "\u{1F950}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      bread: {
        keywords: ["food", "wheat", "breakfast", "toast"],
        char: "\u{1F35E}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      baguette_bread: {
        keywords: ["food", "bread", "french"],
        char: "\u{1F956}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      bagel: {
        keywords: ["food", "bread", "bakery", "schmear"],
        char: "\u{1F96F}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      pretzel: {
        keywords: ["food", "bread", "twisted"],
        char: "\u{1F968}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      cheese: {
        keywords: ["food", "chadder"],
        char: "\u{1F9C0}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      egg: {
        keywords: ["food", "chicken", "breakfast"],
        char: "\u{1F95A}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      bacon: {
        keywords: ["food", "breakfast", "pork", "pig", "meat"],
        char: "\u{1F953}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      steak: {
        keywords: ["food", "cow", "meat", "cut", "chop", "lambchop", "porkchop"],
        char: "\u{1F969}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      pancakes: {
        keywords: ["food", "breakfast", "flapjacks", "hotcakes"],
        char: "\u{1F95E}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      poultry_leg: {
        keywords: ["food", "meat", "drumstick", "bird", "chicken", "turkey"],
        char: "\u{1F357}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      meat_on_bone: {
        keywords: ["good", "food", "drumstick"],
        char: "\u{1F356}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      bone: {
        keywords: ["skeleton"],
        char: "\u{1F9B4}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      fried_shrimp: {
        keywords: ["food", "animal", "appetizer", "summer"],
        char: "\u{1F364}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      fried_egg: {
        keywords: ["food", "breakfast", "kitchen", "egg"],
        char: "\u{1F373}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      hamburger: {
        keywords: ["meat", "fast food", "beef", "cheeseburger", "mcdonalds", "burger king"],
        char: "\u{1F354}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      fries: {
        keywords: ["chips", "snack", "fast food"],
        char: "\u{1F35F}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      stuffed_flatbread: {
        keywords: ["food", "flatbread", "stuffed", "gyro"],
        char: "\u{1F959}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      hotdog: {
        keywords: ["food", "frankfurter"],
        char: "\u{1F32D}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      pizza: {
        keywords: ["food", "party"],
        char: "\u{1F355}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      sandwich: {
        keywords: ["food", "lunch", "bread"],
        char: "\u{1F96A}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      canned_food: {
        keywords: ["food", "soup"],
        char: "\u{1F96B}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      spaghetti: {
        keywords: ["food", "italian", "noodle"],
        char: "\u{1F35D}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      taco: {
        keywords: ["food", "mexican"],
        char: "\u{1F32E}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      burrito: {
        keywords: ["food", "mexican"],
        char: "\u{1F32F}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      green_salad: {
        keywords: ["food", "healthy", "lettuce"],
        char: "\u{1F957}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      shallow_pan_of_food: {
        keywords: ["food", "cooking", "casserole", "paella"],
        char: "\u{1F958}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      ramen: {
        keywords: ["food", "japanese", "noodle", "chopsticks"],
        char: "\u{1F35C}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      stew: {
        keywords: ["food", "meat", "soup"],
        char: "\u{1F372}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      fish_cake: {
        keywords: ["food", "japan", "sea", "beach", "narutomaki", "pink", "swirl", "kamaboko", "surimi", "ramen"],
        char: "\u{1F365}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      fortune_cookie: {
        keywords: ["food", "prophecy"],
        char: "\u{1F960}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      sushi: {
        keywords: ["food", "fish", "japanese", "rice"],
        char: "\u{1F363}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      bento: {
        keywords: ["food", "japanese", "box"],
        char: "\u{1F371}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      curry: {
        keywords: ["food", "spicy", "hot", "indian"],
        char: "\u{1F35B}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      rice_ball: {
        keywords: ["food", "japanese"],
        char: "\u{1F359}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      rice: {
        keywords: ["food", "china", "asian"],
        char: "\u{1F35A}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      rice_cracker: {
        keywords: ["food", "japanese"],
        char: "\u{1F358}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      oden: {
        keywords: ["food", "japanese"],
        char: "\u{1F362}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      dango: {
        keywords: ["food", "dessert", "sweet", "japanese", "barbecue", "meat"],
        char: "\u{1F361}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      shaved_ice: {
        keywords: ["hot", "dessert", "summer"],
        char: "\u{1F367}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      ice_cream: {
        keywords: ["food", "hot", "dessert"],
        char: "\u{1F368}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      icecream: {
        keywords: ["food", "hot", "dessert", "summer"],
        char: "\u{1F366}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      pie: {
        keywords: ["food", "dessert", "pastry"],
        char: "\u{1F967}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      cake: {
        keywords: ["food", "dessert"],
        char: "\u{1F370}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      cupcake: {
        keywords: ["food", "dessert", "bakery", "sweet"],
        char: "\u{1F9C1}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      moon_cake: {
        keywords: ["food", "autumn"],
        char: "\u{1F96E}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      birthday: {
        keywords: ["food", "dessert", "cake"],
        char: "\u{1F382}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      custard: {
        keywords: ["dessert", "food"],
        char: "\u{1F36E}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      candy: {
        keywords: ["snack", "dessert", "sweet", "lolly"],
        char: "\u{1F36C}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      lollipop: {
        keywords: ["food", "snack", "candy", "sweet"],
        char: "\u{1F36D}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      chocolate_bar: {
        keywords: ["food", "snack", "dessert", "sweet"],
        char: "\u{1F36B}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      popcorn: {
        keywords: ["food", "movie theater", "films", "snack"],
        char: "\u{1F37F}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      dumpling: {
        keywords: ["food", "empanada", "pierogi", "potsticker"],
        char: "\u{1F95F}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      doughnut: {
        keywords: ["food", "dessert", "snack", "sweet", "donut"],
        char: "\u{1F369}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      cookie: {
        keywords: ["food", "snack", "oreo", "chocolate", "sweet", "dessert"],
        char: "\u{1F36A}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      milk_glass: {
        keywords: ["beverage", "drink", "cow"],
        char: "\u{1F95B}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      beer: {
        keywords: ["relax", "beverage", "drink", "drunk", "party", "pub", "summer", "alcohol", "booze"],
        char: "\u{1F37A}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      beers: {
        keywords: ["relax", "beverage", "drink", "drunk", "party", "pub", "summer", "alcohol", "booze"],
        char: "\u{1F37B}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      clinking_glasses: {
        keywords: ["beverage", "drink", "party", "alcohol", "celebrate", "cheers", "wine", "champagne", "toast"],
        char: "\u{1F942}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      wine_glass: {
        keywords: ["drink", "beverage", "drunk", "alcohol", "booze"],
        char: "\u{1F377}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      tumbler_glass: {
        keywords: ["drink", "beverage", "drunk", "alcohol", "liquor", "booze", "bourbon", "scotch", "whisky", "glass", "shot"],
        char: "\u{1F943}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      cocktail: {
        keywords: ["drink", "drunk", "alcohol", "beverage", "booze", "mojito"],
        char: "\u{1F378}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      tropical_drink: {
        keywords: ["beverage", "cocktail", "summer", "beach", "alcohol", "booze", "mojito"],
        char: "\u{1F379}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      champagne: {
        keywords: ["drink", "wine", "bottle", "celebration"],
        char: "\u{1F37E}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      sake: {
        keywords: ["wine", "drink", "drunk", "beverage", "japanese", "alcohol", "booze"],
        char: "\u{1F376}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      tea: {
        keywords: ["drink", "bowl", "breakfast", "green", "british"],
        char: "\u{1F375}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      cup_with_straw: {
        keywords: ["drink", "soda"],
        char: "\u{1F964}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      coffee: {
        keywords: ["beverage", "caffeine", "latte", "espresso"],
        char: "\u2615",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      baby_bottle: {
        keywords: ["food", "container", "milk"],
        char: "\u{1F37C}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      salt: {
        keywords: ["condiment", "shaker"],
        char: "\u{1F9C2}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      spoon: {
        keywords: ["cutlery", "kitchen", "tableware"],
        char: "\u{1F944}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      fork_and_knife: {
        keywords: ["cutlery", "kitchen"],
        char: "\u{1F374}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      plate_with_cutlery: {
        keywords: ["food", "eat", "meal", "lunch", "dinner", "restaurant"],
        char: "\u{1F37D}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      bowl_with_spoon: {
        keywords: ["food", "breakfast", "cereal", "oatmeal", "porridge"],
        char: "\u{1F963}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      takeout_box: {
        keywords: ["food", "leftovers"],
        char: "\u{1F961}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      chopsticks: {
        keywords: ["food"],
        char: "\u{1F962}",
        fitzpatrick_scale: false,
        category: "food_and_drink"
      },
      soccer: {
        keywords: ["sports", "football"],
        char: "\u26BD",
        fitzpatrick_scale: false,
        category: "activity"
      },
      basketball: {
        keywords: ["sports", "balls", "NBA"],
        char: "\u{1F3C0}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      football: {
        keywords: ["sports", "balls", "NFL"],
        char: "\u{1F3C8}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      baseball: {
        keywords: ["sports", "balls"],
        char: "\u26BE",
        fitzpatrick_scale: false,
        category: "activity"
      },
      softball: {
        keywords: ["sports", "balls"],
        char: "\u{1F94E}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      tennis: {
        keywords: ["sports", "balls", "green"],
        char: "\u{1F3BE}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      volleyball: {
        keywords: ["sports", "balls"],
        char: "\u{1F3D0}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      rugby_football: {
        keywords: ["sports", "team"],
        char: "\u{1F3C9}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      flying_disc: {
        keywords: ["sports", "frisbee", "ultimate"],
        char: "\u{1F94F}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      "8ball": {
        keywords: ["pool", "hobby", "game", "luck", "magic"],
        char: "\u{1F3B1}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      golf: {
        keywords: ["sports", "business", "flag", "hole", "summer"],
        char: "\u26F3",
        fitzpatrick_scale: false,
        category: "activity"
      },
      golfing_woman: {
        keywords: ["sports", "business", "woman", "female"],
        char: "\u{1F3CC}\uFE0F\u200D\u2640\uFE0F",
        fitzpatrick_scale: false,
        category: "activity"
      },
      golfing_man: {
        keywords: ["sports", "business"],
        char: "\u{1F3CC}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      ping_pong: {
        keywords: ["sports", "pingpong"],
        char: "\u{1F3D3}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      badminton: {
        keywords: ["sports"],
        char: "\u{1F3F8}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      goal_net: {
        keywords: ["sports"],
        char: "\u{1F945}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      ice_hockey: {
        keywords: ["sports"],
        char: "\u{1F3D2}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      field_hockey: {
        keywords: ["sports"],
        char: "\u{1F3D1}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      lacrosse: {
        keywords: ["sports", "ball", "stick"],
        char: "\u{1F94D}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      cricket: {
        keywords: ["sports"],
        char: "\u{1F3CF}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      ski: {
        keywords: ["sports", "winter", "cold", "snow"],
        char: "\u{1F3BF}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      skier: {
        keywords: ["sports", "winter", "snow"],
        char: "\u26F7",
        fitzpatrick_scale: false,
        category: "activity"
      },
      snowboarder: {
        keywords: ["sports", "winter"],
        char: "\u{1F3C2}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      person_fencing: {
        keywords: ["sports", "fencing", "sword"],
        char: "\u{1F93A}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      women_wrestling: {
        keywords: ["sports", "wrestlers"],
        char: "\u{1F93C}\u200D\u2640\uFE0F",
        fitzpatrick_scale: false,
        category: "activity"
      },
      men_wrestling: {
        keywords: ["sports", "wrestlers"],
        char: "\u{1F93C}\u200D\u2642\uFE0F",
        fitzpatrick_scale: false,
        category: "activity"
      },
      woman_cartwheeling: {
        keywords: ["gymnastics"],
        char: "\u{1F938}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      man_cartwheeling: {
        keywords: ["gymnastics"],
        char: "\u{1F938}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      woman_playing_handball: {
        keywords: ["sports"],
        char: "\u{1F93E}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      man_playing_handball: {
        keywords: ["sports"],
        char: "\u{1F93E}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      ice_skate: {
        keywords: ["sports"],
        char: "\u26F8",
        fitzpatrick_scale: false,
        category: "activity"
      },
      curling_stone: {
        keywords: ["sports"],
        char: "\u{1F94C}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      skateboard: {
        keywords: ["board"],
        char: "\u{1F6F9}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      sled: {
        keywords: ["sleigh", "luge", "toboggan"],
        char: "\u{1F6F7}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      bow_and_arrow: {
        keywords: ["sports"],
        char: "\u{1F3F9}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      fishing_pole_and_fish: {
        keywords: ["food", "hobby", "summer"],
        char: "\u{1F3A3}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      boxing_glove: {
        keywords: ["sports", "fighting"],
        char: "\u{1F94A}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      martial_arts_uniform: {
        keywords: ["judo", "karate", "taekwondo"],
        char: "\u{1F94B}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      rowing_woman: {
        keywords: ["sports", "hobby", "water", "ship", "woman", "female"],
        char: "\u{1F6A3}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      rowing_man: {
        keywords: ["sports", "hobby", "water", "ship"],
        char: "\u{1F6A3}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      climbing_woman: {
        keywords: ["sports", "hobby", "woman", "female", "rock"],
        char: "\u{1F9D7}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      climbing_man: {
        keywords: ["sports", "hobby", "man", "male", "rock"],
        char: "\u{1F9D7}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      swimming_woman: {
        keywords: ["sports", "exercise", "human", "athlete", "water", "summer", "woman", "female"],
        char: "\u{1F3CA}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      swimming_man: {
        keywords: ["sports", "exercise", "human", "athlete", "water", "summer"],
        char: "\u{1F3CA}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      woman_playing_water_polo: {
        keywords: ["sports", "pool"],
        char: "\u{1F93D}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      man_playing_water_polo: {
        keywords: ["sports", "pool"],
        char: "\u{1F93D}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      woman_in_lotus_position: {
        keywords: ["woman", "female", "meditation", "yoga", "serenity", "zen", "mindfulness"],
        char: "\u{1F9D8}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      man_in_lotus_position: {
        keywords: ["man", "male", "meditation", "yoga", "serenity", "zen", "mindfulness"],
        char: "\u{1F9D8}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      surfing_woman: {
        keywords: ["sports", "ocean", "sea", "summer", "beach", "woman", "female"],
        char: "\u{1F3C4}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      surfing_man: {
        keywords: ["sports", "ocean", "sea", "summer", "beach"],
        char: "\u{1F3C4}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      bath: {
        keywords: ["clean", "shower", "bathroom"],
        char: "\u{1F6C0}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      basketball_woman: {
        keywords: ["sports", "human", "woman", "female"],
        char: "\u26F9\uFE0F\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      basketball_man: {
        keywords: ["sports", "human"],
        char: "\u26F9",
        fitzpatrick_scale: true,
        category: "activity"
      },
      weight_lifting_woman: {
        keywords: ["sports", "training", "exercise", "woman", "female"],
        char: "\u{1F3CB}\uFE0F\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      weight_lifting_man: {
        keywords: ["sports", "training", "exercise"],
        char: "\u{1F3CB}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      biking_woman: {
        keywords: ["sports", "bike", "exercise", "hipster", "woman", "female"],
        char: "\u{1F6B4}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      biking_man: {
        keywords: ["sports", "bike", "exercise", "hipster"],
        char: "\u{1F6B4}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      mountain_biking_woman: {
        keywords: ["transportation", "sports", "human", "race", "bike", "woman", "female"],
        char: "\u{1F6B5}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      mountain_biking_man: {
        keywords: ["transportation", "sports", "human", "race", "bike"],
        char: "\u{1F6B5}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      horse_racing: {
        keywords: ["animal", "betting", "competition", "gambling", "luck"],
        char: "\u{1F3C7}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      business_suit_levitating: {
        keywords: ["suit", "business", "levitate", "hover", "jump"],
        char: "\u{1F574}",
        fitzpatrick_scale: true,
        category: "activity"
      },
      trophy: {
        keywords: ["win", "award", "contest", "place", "ftw", "ceremony"],
        char: "\u{1F3C6}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      running_shirt_with_sash: {
        keywords: ["play", "pageant"],
        char: "\u{1F3BD}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      medal_sports: {
        keywords: ["award", "winning"],
        char: "\u{1F3C5}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      medal_military: {
        keywords: ["award", "winning", "army"],
        char: "\u{1F396}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      "1st_place_medal": {
        keywords: ["award", "winning", "first"],
        char: "\u{1F947}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      "2nd_place_medal": {
        keywords: ["award", "second"],
        char: "\u{1F948}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      "3rd_place_medal": {
        keywords: ["award", "third"],
        char: "\u{1F949}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      reminder_ribbon: {
        keywords: ["sports", "cause", "support", "awareness"],
        char: "\u{1F397}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      rosette: {
        keywords: ["flower", "decoration", "military"],
        char: "\u{1F3F5}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      ticket: {
        keywords: ["event", "concert", "pass"],
        char: "\u{1F3AB}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      tickets: {
        keywords: ["sports", "concert", "entrance"],
        char: "\u{1F39F}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      performing_arts: {
        keywords: ["acting", "theater", "drama"],
        char: "\u{1F3AD}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      art: {
        keywords: ["design", "paint", "draw", "colors"],
        char: "\u{1F3A8}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      circus_tent: {
        keywords: ["festival", "carnival", "party"],
        char: "\u{1F3AA}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      woman_juggling: {
        keywords: ["juggle", "balance", "skill", "multitask"],
        char: "\u{1F939}\u200D\u2640\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      man_juggling: {
        keywords: ["juggle", "balance", "skill", "multitask"],
        char: "\u{1F939}\u200D\u2642\uFE0F",
        fitzpatrick_scale: true,
        category: "activity"
      },
      microphone: {
        keywords: ["sound", "music", "PA", "sing", "talkshow"],
        char: "\u{1F3A4}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      headphones: {
        keywords: ["music", "score", "gadgets"],
        char: "\u{1F3A7}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      musical_score: {
        keywords: ["treble", "clef", "compose"],
        char: "\u{1F3BC}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      musical_keyboard: {
        keywords: ["piano", "instrument", "compose"],
        char: "\u{1F3B9}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      drum: {
        keywords: ["music", "instrument", "drumsticks", "snare"],
        char: "\u{1F941}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      saxophone: {
        keywords: ["music", "instrument", "jazz", "blues"],
        char: "\u{1F3B7}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      trumpet: {
        keywords: ["music", "brass"],
        char: "\u{1F3BA}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      guitar: {
        keywords: ["music", "instrument"],
        char: "\u{1F3B8}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      violin: {
        keywords: ["music", "instrument", "orchestra", "symphony"],
        char: "\u{1F3BB}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      clapper: {
        keywords: ["movie", "film", "record"],
        char: "\u{1F3AC}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      video_game: {
        keywords: ["play", "console", "PS4", "controller"],
        char: "\u{1F3AE}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      space_invader: {
        keywords: ["game", "arcade", "play"],
        char: "\u{1F47E}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      dart: {
        keywords: ["game", "play", "bar", "target", "bullseye"],
        char: "\u{1F3AF}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      game_die: {
        keywords: ["dice", "random", "tabletop", "play", "luck"],
        char: "\u{1F3B2}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      chess_pawn: {
        keywords: ["expendable"],
        char: "\u265F",
        fitzpatrick_scale: false,
        category: "activity"
      },
      slot_machine: {
        keywords: ["bet", "gamble", "vegas", "fruit machine", "luck", "casino"],
        char: "\u{1F3B0}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      jigsaw: {
        keywords: ["interlocking", "puzzle", "piece"],
        char: "\u{1F9E9}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      bowling: {
        keywords: ["sports", "fun", "play"],
        char: "\u{1F3B3}",
        fitzpatrick_scale: false,
        category: "activity"
      },
      red_car: {
        keywords: ["red", "transportation", "vehicle"],
        char: "\u{1F697}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      taxi: {
        keywords: ["uber", "vehicle", "cars", "transportation"],
        char: "\u{1F695}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      blue_car: {
        keywords: ["transportation", "vehicle"],
        char: "\u{1F699}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      bus: {
        keywords: ["car", "vehicle", "transportation"],
        char: "\u{1F68C}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      trolleybus: {
        keywords: ["bart", "transportation", "vehicle"],
        char: "\u{1F68E}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      racing_car: {
        keywords: ["sports", "race", "fast", "formula", "f1"],
        char: "\u{1F3CE}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      police_car: {
        keywords: ["vehicle", "cars", "transportation", "law", "legal", "enforcement"],
        char: "\u{1F693}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      ambulance: {
        keywords: ["health", "911", "hospital"],
        char: "\u{1F691}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      fire_engine: {
        keywords: ["transportation", "cars", "vehicle"],
        char: "\u{1F692}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      minibus: {
        keywords: ["vehicle", "car", "transportation"],
        char: "\u{1F690}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      truck: {
        keywords: ["cars", "transportation"],
        char: "\u{1F69A}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      articulated_lorry: {
        keywords: ["vehicle", "cars", "transportation", "express"],
        char: "\u{1F69B}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      tractor: {
        keywords: ["vehicle", "car", "farming", "agriculture"],
        char: "\u{1F69C}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      kick_scooter: {
        keywords: ["vehicle", "kick", "razor"],
        char: "\u{1F6F4}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      motorcycle: {
        keywords: ["race", "sports", "fast"],
        char: "\u{1F3CD}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      bike: {
        keywords: ["sports", "bicycle", "exercise", "hipster"],
        char: "\u{1F6B2}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      motor_scooter: {
        keywords: ["vehicle", "vespa", "sasha"],
        char: "\u{1F6F5}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      rotating_light: {
        keywords: ["police", "ambulance", "911", "emergency", "alert", "error", "pinged", "law", "legal"],
        char: "\u{1F6A8}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      oncoming_police_car: {
        keywords: ["vehicle", "law", "legal", "enforcement", "911"],
        char: "\u{1F694}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      oncoming_bus: {
        keywords: ["vehicle", "transportation"],
        char: "\u{1F68D}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      oncoming_automobile: {
        keywords: ["car", "vehicle", "transportation"],
        char: "\u{1F698}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      oncoming_taxi: {
        keywords: ["vehicle", "cars", "uber"],
        char: "\u{1F696}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      aerial_tramway: {
        keywords: ["transportation", "vehicle", "ski"],
        char: "\u{1F6A1}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      mountain_cableway: {
        keywords: ["transportation", "vehicle", "ski"],
        char: "\u{1F6A0}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      suspension_railway: {
        keywords: ["vehicle", "transportation"],
        char: "\u{1F69F}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      railway_car: {
        keywords: ["transportation", "vehicle"],
        char: "\u{1F683}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      train: {
        keywords: ["transportation", "vehicle", "carriage", "public", "travel"],
        char: "\u{1F68B}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      monorail: {
        keywords: ["transportation", "vehicle"],
        char: "\u{1F69D}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      bullettrain_side: {
        keywords: ["transportation", "vehicle"],
        char: "\u{1F684}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      bullettrain_front: {
        keywords: ["transportation", "vehicle", "speed", "fast", "public", "travel"],
        char: "\u{1F685}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      light_rail: {
        keywords: ["transportation", "vehicle"],
        char: "\u{1F688}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      mountain_railway: {
        keywords: ["transportation", "vehicle"],
        char: "\u{1F69E}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      steam_locomotive: {
        keywords: ["transportation", "vehicle", "train"],
        char: "\u{1F682}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      train2: {
        keywords: ["transportation", "vehicle"],
        char: "\u{1F686}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      metro: {
        keywords: ["transportation", "blue-square", "mrt", "underground", "tube"],
        char: "\u{1F687}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      tram: {
        keywords: ["transportation", "vehicle"],
        char: "\u{1F68A}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      station: {
        keywords: ["transportation", "vehicle", "public"],
        char: "\u{1F689}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      flying_saucer: {
        keywords: ["transportation", "vehicle", "ufo"],
        char: "\u{1F6F8}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      helicopter: {
        keywords: ["transportation", "vehicle", "fly"],
        char: "\u{1F681}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      small_airplane: {
        keywords: ["flight", "transportation", "fly", "vehicle"],
        char: "\u{1F6E9}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      airplane: {
        keywords: ["vehicle", "transportation", "flight", "fly"],
        char: "\u2708\uFE0F",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      flight_departure: {
        keywords: ["airport", "flight", "landing"],
        char: "\u{1F6EB}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      flight_arrival: {
        keywords: ["airport", "flight", "boarding"],
        char: "\u{1F6EC}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      sailboat: {
        keywords: ["ship", "summer", "transportation", "water", "sailing"],
        char: "\u26F5",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      motor_boat: {
        keywords: ["ship"],
        char: "\u{1F6E5}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      speedboat: {
        keywords: ["ship", "transportation", "vehicle", "summer"],
        char: "\u{1F6A4}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      ferry: {
        keywords: ["boat", "ship", "yacht"],
        char: "\u26F4",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      passenger_ship: {
        keywords: ["yacht", "cruise", "ferry"],
        char: "\u{1F6F3}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      rocket: {
        keywords: ["launch", "ship", "staffmode", "NASA", "outer space", "outer_space", "fly"],
        char: "\u{1F680}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      artificial_satellite: {
        keywords: ["communication", "gps", "orbit", "spaceflight", "NASA", "ISS"],
        char: "\u{1F6F0}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      seat: {
        keywords: ["sit", "airplane", "transport", "bus", "flight", "fly"],
        char: "\u{1F4BA}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      canoe: {
        keywords: ["boat", "paddle", "water", "ship"],
        char: "\u{1F6F6}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      anchor: {
        keywords: ["ship", "ferry", "sea", "boat"],
        char: "\u2693",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      construction: {
        keywords: ["wip", "progress", "caution", "warning"],
        char: "\u{1F6A7}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      fuelpump: {
        keywords: ["gas station", "petroleum"],
        char: "\u26FD",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      busstop: {
        keywords: ["transportation", "wait"],
        char: "\u{1F68F}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      vertical_traffic_light: {
        keywords: ["transportation", "driving"],
        char: "\u{1F6A6}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      traffic_light: {
        keywords: ["transportation", "signal"],
        char: "\u{1F6A5}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      checkered_flag: {
        keywords: ["contest", "finishline", "race", "gokart"],
        char: "\u{1F3C1}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      ship: {
        keywords: ["transportation", "titanic", "deploy"],
        char: "\u{1F6A2}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      ferris_wheel: {
        keywords: ["photo", "carnival", "londoneye"],
        char: "\u{1F3A1}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      roller_coaster: {
        keywords: ["carnival", "playground", "photo", "fun"],
        char: "\u{1F3A2}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      carousel_horse: {
        keywords: ["photo", "carnival"],
        char: "\u{1F3A0}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      building_construction: {
        keywords: ["wip", "working", "progress"],
        char: "\u{1F3D7}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      foggy: {
        keywords: ["photo", "mountain"],
        char: "\u{1F301}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      tokyo_tower: {
        keywords: ["photo", "japanese"],
        char: "\u{1F5FC}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      factory: {
        keywords: ["building", "industry", "pollution", "smoke"],
        char: "\u{1F3ED}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      fountain: {
        keywords: ["photo", "summer", "water", "fresh"],
        char: "\u26F2",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      rice_scene: {
        keywords: ["photo", "japan", "asia", "tsukimi"],
        char: "\u{1F391}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      mountain: {
        keywords: ["photo", "nature", "environment"],
        char: "\u26F0",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      mountain_snow: {
        keywords: ["photo", "nature", "environment", "winter", "cold"],
        char: "\u{1F3D4}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      mount_fuji: {
        keywords: ["photo", "mountain", "nature", "japanese"],
        char: "\u{1F5FB}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      volcano: {
        keywords: ["photo", "nature", "disaster"],
        char: "\u{1F30B}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      japan: {
        keywords: ["nation", "country", "japanese", "asia"],
        char: "\u{1F5FE}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      camping: {
        keywords: ["photo", "outdoors", "tent"],
        char: "\u{1F3D5}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      tent: {
        keywords: ["photo", "camping", "outdoors"],
        char: "\u26FA",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      national_park: {
        keywords: ["photo", "environment", "nature"],
        char: "\u{1F3DE}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      motorway: {
        keywords: ["road", "cupertino", "interstate", "highway"],
        char: "\u{1F6E3}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      railway_track: {
        keywords: ["train", "transportation"],
        char: "\u{1F6E4}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      sunrise: {
        keywords: ["morning", "view", "vacation", "photo"],
        char: "\u{1F305}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      sunrise_over_mountains: {
        keywords: ["view", "vacation", "photo"],
        char: "\u{1F304}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      desert: {
        keywords: ["photo", "warm", "saharah"],
        char: "\u{1F3DC}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      beach_umbrella: {
        keywords: ["weather", "summer", "sunny", "sand", "mojito"],
        char: "\u{1F3D6}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      desert_island: {
        keywords: ["photo", "tropical", "mojito"],
        char: "\u{1F3DD}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      city_sunrise: {
        keywords: ["photo", "good morning", "dawn"],
        char: "\u{1F307}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      city_sunset: {
        keywords: ["photo", "evening", "sky", "buildings"],
        char: "\u{1F306}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      cityscape: {
        keywords: ["photo", "night life", "urban"],
        char: "\u{1F3D9}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      night_with_stars: {
        keywords: ["evening", "city", "downtown"],
        char: "\u{1F303}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      bridge_at_night: {
        keywords: ["photo", "sanfrancisco"],
        char: "\u{1F309}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      milky_way: {
        keywords: ["photo", "space", "stars"],
        char: "\u{1F30C}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      stars: {
        keywords: ["night", "photo"],
        char: "\u{1F320}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      sparkler: {
        keywords: ["stars", "night", "shine"],
        char: "\u{1F387}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      fireworks: {
        keywords: ["photo", "festival", "carnival", "congratulations"],
        char: "\u{1F386}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      rainbow: {
        keywords: ["nature", "happy", "unicorn_face", "photo", "sky", "spring"],
        char: "\u{1F308}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      houses: {
        keywords: ["buildings", "photo"],
        char: "\u{1F3D8}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      european_castle: {
        keywords: ["building", "royalty", "history"],
        char: "\u{1F3F0}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      japanese_castle: {
        keywords: ["photo", "building"],
        char: "\u{1F3EF}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      stadium: {
        keywords: ["photo", "place", "sports", "concert", "venue"],
        char: "\u{1F3DF}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      statue_of_liberty: {
        keywords: ["american", "newyork"],
        char: "\u{1F5FD}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      house: {
        keywords: ["building", "home"],
        char: "\u{1F3E0}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      house_with_garden: {
        keywords: ["home", "plant", "nature"],
        char: "\u{1F3E1}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      derelict_house: {
        keywords: ["abandon", "evict", "broken", "building"],
        char: "\u{1F3DA}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      office: {
        keywords: ["building", "bureau", "work"],
        char: "\u{1F3E2}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      department_store: {
        keywords: ["building", "shopping", "mall"],
        char: "\u{1F3EC}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      post_office: {
        keywords: ["building", "envelope", "communication"],
        char: "\u{1F3E3}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      european_post_office: {
        keywords: ["building", "email"],
        char: "\u{1F3E4}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      hospital: {
        keywords: ["building", "health", "surgery", "doctor"],
        char: "\u{1F3E5}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      bank: {
        keywords: ["building", "money", "sales", "cash", "business", "enterprise"],
        char: "\u{1F3E6}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      hotel: {
        keywords: ["building", "accomodation", "checkin"],
        char: "\u{1F3E8}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      convenience_store: {
        keywords: ["building", "shopping", "groceries"],
        char: "\u{1F3EA}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      school: {
        keywords: ["building", "student", "education", "learn", "teach"],
        char: "\u{1F3EB}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      love_hotel: {
        keywords: ["like", "affection", "dating"],
        char: "\u{1F3E9}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      wedding: {
        keywords: ["love", "like", "affection", "couple", "marriage", "bride", "groom"],
        char: "\u{1F492}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      classical_building: {
        keywords: ["art", "culture", "history"],
        char: "\u{1F3DB}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      church: {
        keywords: ["building", "religion", "christ"],
        char: "\u26EA",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      mosque: {
        keywords: ["islam", "worship", "minaret"],
        char: "\u{1F54C}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      synagogue: {
        keywords: ["judaism", "worship", "temple", "jewish"],
        char: "\u{1F54D}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      kaaba: {
        keywords: ["mecca", "mosque", "islam"],
        char: "\u{1F54B}",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      shinto_shrine: {
        keywords: ["temple", "japan", "kyoto"],
        char: "\u26E9",
        fitzpatrick_scale: false,
        category: "travel_and_places"
      },
      watch: {
        keywords: ["time", "accessories"],
        char: "\u231A",
        fitzpatrick_scale: false,
        category: "objects"
      },
      iphone: {
        keywords: ["technology", "apple", "gadgets", "dial"],
        char: "\u{1F4F1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      calling: {
        keywords: ["iphone", "incoming"],
        char: "\u{1F4F2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      computer: {
        keywords: ["technology", "laptop", "screen", "display", "monitor"],
        char: "\u{1F4BB}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      keyboard: {
        keywords: ["technology", "computer", "type", "input", "text"],
        char: "\u2328",
        fitzpatrick_scale: false,
        category: "objects"
      },
      desktop_computer: {
        keywords: ["technology", "computing", "screen"],
        char: "\u{1F5A5}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      printer: {
        keywords: ["paper", "ink"],
        char: "\u{1F5A8}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      computer_mouse: {
        keywords: ["click"],
        char: "\u{1F5B1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      trackball: {
        keywords: ["technology", "trackpad"],
        char: "\u{1F5B2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      joystick: {
        keywords: ["game", "play"],
        char: "\u{1F579}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      clamp: {
        keywords: ["tool"],
        char: "\u{1F5DC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      minidisc: {
        keywords: ["technology", "record", "data", "disk", "90s"],
        char: "\u{1F4BD}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      floppy_disk: {
        keywords: ["oldschool", "technology", "save", "90s", "80s"],
        char: "\u{1F4BE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      cd: {
        keywords: ["technology", "dvd", "disk", "disc", "90s"],
        char: "\u{1F4BF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      dvd: {
        keywords: ["cd", "disk", "disc"],
        char: "\u{1F4C0}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      vhs: {
        keywords: ["record", "video", "oldschool", "90s", "80s"],
        char: "\u{1F4FC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      camera: {
        keywords: ["gadgets", "photography"],
        char: "\u{1F4F7}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      camera_flash: {
        keywords: ["photography", "gadgets"],
        char: "\u{1F4F8}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      video_camera: {
        keywords: ["film", "record"],
        char: "\u{1F4F9}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      movie_camera: {
        keywords: ["film", "record"],
        char: "\u{1F3A5}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      film_projector: {
        keywords: ["video", "tape", "record", "movie"],
        char: "\u{1F4FD}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      film_strip: {
        keywords: ["movie"],
        char: "\u{1F39E}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      telephone_receiver: {
        keywords: ["technology", "communication", "dial"],
        char: "\u{1F4DE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      phone: {
        keywords: ["technology", "communication", "dial", "telephone"],
        char: "\u260E\uFE0F",
        fitzpatrick_scale: false,
        category: "objects"
      },
      pager: {
        keywords: ["bbcall", "oldschool", "90s"],
        char: "\u{1F4DF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      fax: {
        keywords: ["communication", "technology"],
        char: "\u{1F4E0}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      tv: {
        keywords: ["technology", "program", "oldschool", "show", "television"],
        char: "\u{1F4FA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      radio: {
        keywords: ["communication", "music", "podcast", "program"],
        char: "\u{1F4FB}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      studio_microphone: {
        keywords: ["sing", "recording", "artist", "talkshow"],
        char: "\u{1F399}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      level_slider: {
        keywords: ["scale"],
        char: "\u{1F39A}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      control_knobs: {
        keywords: ["dial"],
        char: "\u{1F39B}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      compass: {
        keywords: ["magnetic", "navigation", "orienteering"],
        char: "\u{1F9ED}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      stopwatch: {
        keywords: ["time", "deadline"],
        char: "\u23F1",
        fitzpatrick_scale: false,
        category: "objects"
      },
      timer_clock: {
        keywords: ["alarm"],
        char: "\u23F2",
        fitzpatrick_scale: false,
        category: "objects"
      },
      alarm_clock: {
        keywords: ["time", "wake"],
        char: "\u23F0",
        fitzpatrick_scale: false,
        category: "objects"
      },
      mantelpiece_clock: {
        keywords: ["time"],
        char: "\u{1F570}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      hourglass_flowing_sand: {
        keywords: ["oldschool", "time", "countdown"],
        char: "\u23F3",
        fitzpatrick_scale: false,
        category: "objects"
      },
      hourglass: {
        keywords: ["time", "clock", "oldschool", "limit", "exam", "quiz", "test"],
        char: "\u231B",
        fitzpatrick_scale: false,
        category: "objects"
      },
      satellite: {
        keywords: ["communication", "future", "radio", "space"],
        char: "\u{1F4E1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      battery: {
        keywords: ["power", "energy", "sustain"],
        char: "\u{1F50B}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      electric_plug: {
        keywords: ["charger", "power"],
        char: "\u{1F50C}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      bulb: {
        keywords: ["light", "electricity", "idea"],
        char: "\u{1F4A1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      flashlight: {
        keywords: ["dark", "camping", "sight", "night"],
        char: "\u{1F526}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      candle: {
        keywords: ["fire", "wax"],
        char: "\u{1F56F}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      fire_extinguisher: {
        keywords: ["quench"],
        char: "\u{1F9EF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      wastebasket: {
        keywords: ["bin", "trash", "rubbish", "garbage", "toss"],
        char: "\u{1F5D1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      oil_drum: {
        keywords: ["barrell"],
        char: "\u{1F6E2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      money_with_wings: {
        keywords: ["dollar", "bills", "payment", "sale"],
        char: "\u{1F4B8}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      dollar: {
        keywords: ["money", "sales", "bill", "currency"],
        char: "\u{1F4B5}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      yen: {
        keywords: ["money", "sales", "japanese", "dollar", "currency"],
        char: "\u{1F4B4}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      euro: {
        keywords: ["money", "sales", "dollar", "currency"],
        char: "\u{1F4B6}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      pound: {
        keywords: ["british", "sterling", "money", "sales", "bills", "uk", "england", "currency"],
        char: "\u{1F4B7}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      moneybag: {
        keywords: ["dollar", "payment", "coins", "sale"],
        char: "\u{1F4B0}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      credit_card: {
        keywords: ["money", "sales", "dollar", "bill", "payment", "shopping"],
        char: "\u{1F4B3}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      gem: {
        keywords: ["blue", "ruby", "diamond", "jewelry"],
        char: "\u{1F48E}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      balance_scale: {
        keywords: ["law", "fairness", "weight"],
        char: "\u2696",
        fitzpatrick_scale: false,
        category: "objects"
      },
      toolbox: {
        keywords: ["tools", "diy", "fix", "maintainer", "mechanic"],
        char: "\u{1F9F0}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      wrench: {
        keywords: ["tools", "diy", "ikea", "fix", "maintainer"],
        char: "\u{1F527}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      hammer: {
        keywords: ["tools", "build", "create"],
        char: "\u{1F528}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      hammer_and_pick: {
        keywords: ["tools", "build", "create"],
        char: "\u2692",
        fitzpatrick_scale: false,
        category: "objects"
      },
      hammer_and_wrench: {
        keywords: ["tools", "build", "create"],
        char: "\u{1F6E0}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      pick: {
        keywords: ["tools", "dig"],
        char: "\u26CF",
        fitzpatrick_scale: false,
        category: "objects"
      },
      nut_and_bolt: {
        keywords: ["handy", "tools", "fix"],
        char: "\u{1F529}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      gear: {
        keywords: ["cog"],
        char: "\u2699",
        fitzpatrick_scale: false,
        category: "objects"
      },
      brick: {
        keywords: ["bricks"],
        char: "\u{1F9F1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      chains: {
        keywords: ["lock", "arrest"],
        char: "\u26D3",
        fitzpatrick_scale: false,
        category: "objects"
      },
      magnet: {
        keywords: ["attraction", "magnetic"],
        char: "\u{1F9F2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      gun: {
        keywords: ["violence", "weapon", "pistol", "revolver"],
        char: "\u{1F52B}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      bomb: {
        keywords: ["boom", "explode", "explosion", "terrorism"],
        char: "\u{1F4A3}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      firecracker: {
        keywords: ["dynamite", "boom", "explode", "explosion", "explosive"],
        char: "\u{1F9E8}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      hocho: {
        keywords: ["knife", "blade", "cutlery", "kitchen", "weapon"],
        char: "\u{1F52A}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      dagger: {
        keywords: ["weapon"],
        char: "\u{1F5E1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      crossed_swords: {
        keywords: ["weapon"],
        char: "\u2694",
        fitzpatrick_scale: false,
        category: "objects"
      },
      shield: {
        keywords: ["protection", "security"],
        char: "\u{1F6E1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      smoking: {
        keywords: ["kills", "tobacco", "cigarette", "joint", "smoke"],
        char: "\u{1F6AC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      skull_and_crossbones: {
        keywords: ["poison", "danger", "deadly", "scary", "death", "pirate", "evil"],
        char: "\u2620",
        fitzpatrick_scale: false,
        category: "objects"
      },
      coffin: {
        keywords: ["vampire", "dead", "die", "death", "rip", "graveyard", "cemetery", "casket", "funeral", "box"],
        char: "\u26B0",
        fitzpatrick_scale: false,
        category: "objects"
      },
      funeral_urn: {
        keywords: ["dead", "die", "death", "rip", "ashes"],
        char: "\u26B1",
        fitzpatrick_scale: false,
        category: "objects"
      },
      amphora: {
        keywords: ["vase", "jar"],
        char: "\u{1F3FA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      crystal_ball: {
        keywords: ["disco", "party", "magic", "circus", "fortune_teller"],
        char: "\u{1F52E}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      prayer_beads: {
        keywords: ["dhikr", "religious"],
        char: "\u{1F4FF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      nazar_amulet: {
        keywords: ["bead", "charm"],
        char: "\u{1F9FF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      barber: {
        keywords: ["hair", "salon", "style"],
        char: "\u{1F488}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      alembic: {
        keywords: ["distilling", "science", "experiment", "chemistry"],
        char: "\u2697",
        fitzpatrick_scale: false,
        category: "objects"
      },
      telescope: {
        keywords: ["stars", "space", "zoom", "science", "astronomy"],
        char: "\u{1F52D}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      microscope: {
        keywords: ["laboratory", "experiment", "zoomin", "science", "study"],
        char: "\u{1F52C}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      hole: {
        keywords: ["embarrassing"],
        char: "\u{1F573}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      pill: {
        keywords: ["health", "medicine", "doctor", "pharmacy", "drug"],
        char: "\u{1F48A}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      syringe: {
        keywords: ["health", "hospital", "drugs", "blood", "medicine", "needle", "doctor", "nurse"],
        char: "\u{1F489}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      dna: {
        keywords: ["biologist", "genetics", "life"],
        char: "\u{1F9EC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      microbe: {
        keywords: ["amoeba", "bacteria", "germs"],
        char: "\u{1F9A0}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      petri_dish: {
        keywords: ["bacteria", "biology", "culture", "lab"],
        char: "\u{1F9EB}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      test_tube: {
        keywords: ["chemistry", "experiment", "lab", "science"],
        char: "\u{1F9EA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      thermometer: {
        keywords: ["weather", "temperature", "hot", "cold"],
        char: "\u{1F321}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      broom: {
        keywords: ["cleaning", "sweeping", "witch"],
        char: "\u{1F9F9}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      basket: {
        keywords: ["laundry"],
        char: "\u{1F9FA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      toilet_paper: {
        keywords: ["roll"],
        char: "\u{1F9FB}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      label: {
        keywords: ["sale", "tag"],
        char: "\u{1F3F7}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      bookmark: {
        keywords: ["favorite", "label", "save"],
        char: "\u{1F516}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      toilet: {
        keywords: ["restroom", "wc", "washroom", "bathroom", "potty"],
        char: "\u{1F6BD}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      shower: {
        keywords: ["clean", "water", "bathroom"],
        char: "\u{1F6BF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      bathtub: {
        keywords: ["clean", "shower", "bathroom"],
        char: "\u{1F6C1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      soap: {
        keywords: ["bar", "bathing", "cleaning", "lather"],
        char: "\u{1F9FC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      sponge: {
        keywords: ["absorbing", "cleaning", "porous"],
        char: "\u{1F9FD}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      lotion_bottle: {
        keywords: ["moisturizer", "sunscreen"],
        char: "\u{1F9F4}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      key: {
        keywords: ["lock", "door", "password"],
        char: "\u{1F511}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      old_key: {
        keywords: ["lock", "door", "password"],
        char: "\u{1F5DD}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      couch_and_lamp: {
        keywords: ["read", "chill"],
        char: "\u{1F6CB}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      sleeping_bed: {
        keywords: ["bed", "rest"],
        char: "\u{1F6CC}",
        fitzpatrick_scale: true,
        category: "objects"
      },
      bed: {
        keywords: ["sleep", "rest"],
        char: "\u{1F6CF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      door: {
        keywords: ["house", "entry", "exit"],
        char: "\u{1F6AA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      bellhop_bell: {
        keywords: ["service"],
        char: "\u{1F6CE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      teddy_bear: {
        keywords: ["plush", "stuffed"],
        char: "\u{1F9F8}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      framed_picture: {
        keywords: ["photography"],
        char: "\u{1F5BC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      world_map: {
        keywords: ["location", "direction"],
        char: "\u{1F5FA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      parasol_on_ground: {
        keywords: ["weather", "summer"],
        char: "\u26F1",
        fitzpatrick_scale: false,
        category: "objects"
      },
      moyai: {
        keywords: ["rock", "easter island", "moai"],
        char: "\u{1F5FF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      shopping: {
        keywords: ["mall", "buy", "purchase"],
        char: "\u{1F6CD}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      shopping_cart: {
        keywords: ["trolley"],
        char: "\u{1F6D2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      balloon: {
        keywords: ["party", "celebration", "birthday", "circus"],
        char: "\u{1F388}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      flags: {
        keywords: ["fish", "japanese", "koinobori", "carp", "banner"],
        char: "\u{1F38F}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      ribbon: {
        keywords: ["decoration", "pink", "girl", "bowtie"],
        char: "\u{1F380}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      gift: {
        keywords: ["present", "birthday", "christmas", "xmas"],
        char: "\u{1F381}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      confetti_ball: {
        keywords: ["festival", "party", "birthday", "circus"],
        char: "\u{1F38A}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      tada: {
        keywords: ["party", "congratulations", "birthday", "magic", "circus", "celebration"],
        char: "\u{1F389}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      dolls: {
        keywords: ["japanese", "toy", "kimono"],
        char: "\u{1F38E}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      wind_chime: {
        keywords: ["nature", "ding", "spring", "bell"],
        char: "\u{1F390}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      crossed_flags: {
        keywords: ["japanese", "nation", "country", "border"],
        char: "\u{1F38C}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      izakaya_lantern: {
        keywords: ["light", "paper", "halloween", "spooky"],
        char: "\u{1F3EE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      red_envelope: {
        keywords: ["gift"],
        char: "\u{1F9E7}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      email: {
        keywords: ["letter", "postal", "inbox", "communication"],
        char: "\u2709\uFE0F",
        fitzpatrick_scale: false,
        category: "objects"
      },
      envelope_with_arrow: {
        keywords: ["email", "communication"],
        char: "\u{1F4E9}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      incoming_envelope: {
        keywords: ["email", "inbox"],
        char: "\u{1F4E8}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      "e-mail": {
        keywords: ["communication", "inbox"],
        char: "\u{1F4E7}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      love_letter: {
        keywords: ["email", "like", "affection", "envelope", "valentines"],
        char: "\u{1F48C}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      postbox: {
        keywords: ["email", "letter", "envelope"],
        char: "\u{1F4EE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      mailbox_closed: {
        keywords: ["email", "communication", "inbox"],
        char: "\u{1F4EA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      mailbox: {
        keywords: ["email", "inbox", "communication"],
        char: "\u{1F4EB}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      mailbox_with_mail: {
        keywords: ["email", "inbox", "communication"],
        char: "\u{1F4EC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      mailbox_with_no_mail: {
        keywords: ["email", "inbox"],
        char: "\u{1F4ED}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      package: {
        keywords: ["mail", "gift", "cardboard", "box", "moving"],
        char: "\u{1F4E6}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      postal_horn: {
        keywords: ["instrument", "music"],
        char: "\u{1F4EF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      inbox_tray: {
        keywords: ["email", "documents"],
        char: "\u{1F4E5}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      outbox_tray: {
        keywords: ["inbox", "email"],
        char: "\u{1F4E4}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      scroll: {
        keywords: ["documents", "ancient", "history", "paper"],
        char: "\u{1F4DC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      page_with_curl: {
        keywords: ["documents", "office", "paper"],
        char: "\u{1F4C3}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      bookmark_tabs: {
        keywords: ["favorite", "save", "order", "tidy"],
        char: "\u{1F4D1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      receipt: {
        keywords: ["accounting", "expenses"],
        char: "\u{1F9FE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      bar_chart: {
        keywords: ["graph", "presentation", "stats"],
        char: "\u{1F4CA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      chart_with_upwards_trend: {
        keywords: ["graph", "presentation", "stats", "recovery", "business", "economics", "money", "sales", "good", "success"],
        char: "\u{1F4C8}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      chart_with_downwards_trend: {
        keywords: ["graph", "presentation", "stats", "recession", "business", "economics", "money", "sales", "bad", "failure"],
        char: "\u{1F4C9}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      page_facing_up: {
        keywords: ["documents", "office", "paper", "information"],
        char: "\u{1F4C4}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      date: {
        keywords: ["calendar", "schedule"],
        char: "\u{1F4C5}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      calendar: {
        keywords: ["schedule", "date", "planning"],
        char: "\u{1F4C6}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      spiral_calendar: {
        keywords: ["date", "schedule", "planning"],
        char: "\u{1F5D3}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      card_index: {
        keywords: ["business", "stationery"],
        char: "\u{1F4C7}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      card_file_box: {
        keywords: ["business", "stationery"],
        char: "\u{1F5C3}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      ballot_box: {
        keywords: ["election", "vote"],
        char: "\u{1F5F3}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      file_cabinet: {
        keywords: ["filing", "organizing"],
        char: "\u{1F5C4}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      clipboard: {
        keywords: ["stationery", "documents"],
        char: "\u{1F4CB}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      spiral_notepad: {
        keywords: ["memo", "stationery"],
        char: "\u{1F5D2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      file_folder: {
        keywords: ["documents", "business", "office"],
        char: "\u{1F4C1}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      open_file_folder: {
        keywords: ["documents", "load"],
        char: "\u{1F4C2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      card_index_dividers: {
        keywords: ["organizing", "business", "stationery"],
        char: "\u{1F5C2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      newspaper_roll: {
        keywords: ["press", "headline"],
        char: "\u{1F5DE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      newspaper: {
        keywords: ["press", "headline"],
        char: "\u{1F4F0}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      notebook: {
        keywords: ["stationery", "record", "notes", "paper", "study"],
        char: "\u{1F4D3}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      closed_book: {
        keywords: ["read", "library", "knowledge", "textbook", "learn"],
        char: "\u{1F4D5}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      green_book: {
        keywords: ["read", "library", "knowledge", "study"],
        char: "\u{1F4D7}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      blue_book: {
        keywords: ["read", "library", "knowledge", "learn", "study"],
        char: "\u{1F4D8}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      orange_book: {
        keywords: ["read", "library", "knowledge", "textbook", "study"],
        char: "\u{1F4D9}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      notebook_with_decorative_cover: {
        keywords: ["classroom", "notes", "record", "paper", "study"],
        char: "\u{1F4D4}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      ledger: {
        keywords: ["notes", "paper"],
        char: "\u{1F4D2}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      books: {
        keywords: ["literature", "library", "study"],
        char: "\u{1F4DA}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      open_book: {
        keywords: ["book", "read", "library", "knowledge", "literature", "learn", "study"],
        char: "\u{1F4D6}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      safety_pin: {
        keywords: ["diaper"],
        char: "\u{1F9F7}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      link: {
        keywords: ["rings", "url"],
        char: "\u{1F517}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      paperclip: {
        keywords: ["documents", "stationery"],
        char: "\u{1F4CE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      paperclips: {
        keywords: ["documents", "stationery"],
        char: "\u{1F587}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      scissors: {
        keywords: ["stationery", "cut"],
        char: "\u2702\uFE0F",
        fitzpatrick_scale: false,
        category: "objects"
      },
      triangular_ruler: {
        keywords: ["stationery", "math", "architect", "sketch"],
        char: "\u{1F4D0}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      straight_ruler: {
        keywords: ["stationery", "calculate", "length", "math", "school", "drawing", "architect", "sketch"],
        char: "\u{1F4CF}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      abacus: {
        keywords: ["calculation"],
        char: "\u{1F9EE}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      pushpin: {
        keywords: ["stationery", "mark", "here"],
        char: "\u{1F4CC}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      round_pushpin: {
        keywords: ["stationery", "location", "map", "here"],
        char: "\u{1F4CD}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      triangular_flag_on_post: {
        keywords: ["mark", "milestone", "place"],
        char: "\u{1F6A9}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      white_flag: {
        keywords: ["losing", "loser", "lost", "surrender", "give up", "fail"],
        char: "\u{1F3F3}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      black_flag: {
        keywords: ["pirate"],
        char: "\u{1F3F4}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      rainbow_flag: {
        keywords: ["flag", "rainbow", "pride", "gay", "lgbt", "glbt", "queer", "homosexual", "lesbian", "bisexual", "transgender"],
        char: "\u{1F3F3}\uFE0F\u200D\u{1F308}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      closed_lock_with_key: {
        keywords: ["security", "privacy"],
        char: "\u{1F510}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      lock: {
        keywords: ["security", "password", "padlock"],
        char: "\u{1F512}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      unlock: {
        keywords: ["privacy", "security"],
        char: "\u{1F513}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      lock_with_ink_pen: {
        keywords: ["security", "secret"],
        char: "\u{1F50F}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      pen: {
        keywords: ["stationery", "writing", "write"],
        char: "\u{1F58A}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      fountain_pen: {
        keywords: ["stationery", "writing", "write"],
        char: "\u{1F58B}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      black_nib: {
        keywords: ["pen", "stationery", "writing", "write"],
        char: "\u2712\uFE0F",
        fitzpatrick_scale: false,
        category: "objects"
      },
      memo: {
        keywords: ["write", "documents", "stationery", "pencil", "paper", "writing", "legal", "exam", "quiz", "test", "study", "compose"],
        char: "\u{1F4DD}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      pencil2: {
        keywords: ["stationery", "write", "paper", "writing", "school", "study"],
        char: "\u270F\uFE0F",
        fitzpatrick_scale: false,
        category: "objects"
      },
      crayon: {
        keywords: ["drawing", "creativity"],
        char: "\u{1F58D}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      paintbrush: {
        keywords: ["drawing", "creativity", "art"],
        char: "\u{1F58C}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      mag: {
        keywords: ["search", "zoom", "find", "detective"],
        char: "\u{1F50D}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      mag_right: {
        keywords: ["search", "zoom", "find", "detective"],
        char: "\u{1F50E}",
        fitzpatrick_scale: false,
        category: "objects"
      },
      heart: {
        keywords: ["love", "like", "valentines"],
        char: "\u2764\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      orange_heart: {
        keywords: ["love", "like", "affection", "valentines"],
        char: "\u{1F9E1}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      yellow_heart: {
        keywords: ["love", "like", "affection", "valentines"],
        char: "\u{1F49B}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      green_heart: {
        keywords: ["love", "like", "affection", "valentines"],
        char: "\u{1F49A}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      blue_heart: {
        keywords: ["love", "like", "affection", "valentines"],
        char: "\u{1F499}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      purple_heart: {
        keywords: ["love", "like", "affection", "valentines"],
        char: "\u{1F49C}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      black_heart: {
        keywords: ["evil"],
        char: "\u{1F5A4}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      broken_heart: {
        keywords: ["sad", "sorry", "break", "heart", "heartbreak"],
        char: "\u{1F494}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heavy_heart_exclamation: {
        keywords: ["decoration", "love"],
        char: "\u2763",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      two_hearts: {
        keywords: ["love", "like", "affection", "valentines", "heart"],
        char: "\u{1F495}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      revolving_hearts: {
        keywords: ["love", "like", "affection", "valentines"],
        char: "\u{1F49E}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heartbeat: {
        keywords: ["love", "like", "affection", "valentines", "pink", "heart"],
        char: "\u{1F493}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heartpulse: {
        keywords: ["like", "love", "affection", "valentines", "pink"],
        char: "\u{1F497}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      sparkling_heart: {
        keywords: ["love", "like", "affection", "valentines"],
        char: "\u{1F496}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      cupid: {
        keywords: ["love", "like", "heart", "affection", "valentines"],
        char: "\u{1F498}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      gift_heart: {
        keywords: ["love", "valentines"],
        char: "\u{1F49D}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heart_decoration: {
        keywords: ["purple-square", "love", "like"],
        char: "\u{1F49F}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      peace_symbol: {
        keywords: ["hippie"],
        char: "\u262E",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      latin_cross: {
        keywords: ["christianity"],
        char: "\u271D",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      star_and_crescent: {
        keywords: ["islam"],
        char: "\u262A",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      om: {
        keywords: ["hinduism", "buddhism", "sikhism", "jainism"],
        char: "\u{1F549}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      wheel_of_dharma: {
        keywords: ["hinduism", "buddhism", "sikhism", "jainism"],
        char: "\u2638",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      star_of_david: {
        keywords: ["judaism"],
        char: "\u2721",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      six_pointed_star: {
        keywords: ["purple-square", "religion", "jewish", "hexagram"],
        char: "\u{1F52F}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      menorah: {
        keywords: ["hanukkah", "candles", "jewish"],
        char: "\u{1F54E}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      yin_yang: {
        keywords: ["balance"],
        char: "\u262F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      orthodox_cross: {
        keywords: ["suppedaneum", "religion"],
        char: "\u2626",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      place_of_worship: {
        keywords: ["religion", "church", "temple", "prayer"],
        char: "\u{1F6D0}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      ophiuchus: {
        keywords: ["sign", "purple-square", "constellation", "astrology"],
        char: "\u26CE",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      aries: {
        keywords: ["sign", "purple-square", "zodiac", "astrology"],
        char: "\u2648",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      taurus: {
        keywords: ["purple-square", "sign", "zodiac", "astrology"],
        char: "\u2649",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      gemini: {
        keywords: ["sign", "zodiac", "purple-square", "astrology"],
        char: "\u264A",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      cancer: {
        keywords: ["sign", "zodiac", "purple-square", "astrology"],
        char: "\u264B",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      leo: {
        keywords: ["sign", "purple-square", "zodiac", "astrology"],
        char: "\u264C",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      virgo: {
        keywords: ["sign", "zodiac", "purple-square", "astrology"],
        char: "\u264D",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      libra: {
        keywords: ["sign", "purple-square", "zodiac", "astrology"],
        char: "\u264E",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      scorpius: {
        keywords: ["sign", "zodiac", "purple-square", "astrology", "scorpio"],
        char: "\u264F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      sagittarius: {
        keywords: ["sign", "zodiac", "purple-square", "astrology"],
        char: "\u2650",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      capricorn: {
        keywords: ["sign", "zodiac", "purple-square", "astrology"],
        char: "\u2651",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      aquarius: {
        keywords: ["sign", "purple-square", "zodiac", "astrology"],
        char: "\u2652",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      pisces: {
        keywords: ["purple-square", "sign", "zodiac", "astrology"],
        char: "\u2653",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      id: {
        keywords: ["purple-square", "words"],
        char: "\u{1F194}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      atom_symbol: {
        keywords: ["science", "physics", "chemistry"],
        char: "\u269B",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u7a7a: {
        keywords: ["kanji", "japanese", "chinese", "empty", "sky", "blue-square"],
        char: "\u{1F233}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u5272: {
        keywords: ["cut", "divide", "chinese", "kanji", "pink-square"],
        char: "\u{1F239}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      radioactive: {
        keywords: ["nuclear", "danger"],
        char: "\u2622",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      biohazard: {
        keywords: ["danger"],
        char: "\u2623",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      mobile_phone_off: {
        keywords: ["mute", "orange-square", "silence", "quiet"],
        char: "\u{1F4F4}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      vibration_mode: {
        keywords: ["orange-square", "phone"],
        char: "\u{1F4F3}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u6709: {
        keywords: ["orange-square", "chinese", "have", "kanji"],
        char: "\u{1F236}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u7121: {
        keywords: ["nothing", "chinese", "kanji", "japanese", "orange-square"],
        char: "\u{1F21A}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u7533: {
        keywords: ["chinese", "japanese", "kanji", "orange-square"],
        char: "\u{1F238}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u55b6: {
        keywords: ["japanese", "opening hours", "orange-square"],
        char: "\u{1F23A}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u6708: {
        keywords: ["chinese", "month", "moon", "japanese", "orange-square", "kanji"],
        char: "\u{1F237}\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      eight_pointed_black_star: {
        keywords: ["orange-square", "shape", "polygon"],
        char: "\u2734\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      vs: {
        keywords: ["words", "orange-square"],
        char: "\u{1F19A}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      accept: {
        keywords: ["ok", "good", "chinese", "kanji", "agree", "yes", "orange-circle"],
        char: "\u{1F251}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      white_flower: {
        keywords: ["japanese", "spring"],
        char: "\u{1F4AE}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      ideograph_advantage: {
        keywords: ["chinese", "kanji", "obtain", "get", "circle"],
        char: "\u{1F250}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      secret: {
        keywords: ["privacy", "chinese", "sshh", "kanji", "red-circle"],
        char: "\u3299\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      congratulations: {
        keywords: ["chinese", "kanji", "japanese", "red-circle"],
        char: "\u3297\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u5408: {
        keywords: ["japanese", "chinese", "join", "kanji", "red-square"],
        char: "\u{1F234}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u6e80: {
        keywords: ["full", "chinese", "japanese", "red-square", "kanji"],
        char: "\u{1F235}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u7981: {
        keywords: ["kanji", "japanese", "chinese", "forbidden", "limit", "restricted", "red-square"],
        char: "\u{1F232}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      a: {
        keywords: ["red-square", "alphabet", "letter"],
        char: "\u{1F170}\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      b: {
        keywords: ["red-square", "alphabet", "letter"],
        char: "\u{1F171}\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      ab: {
        keywords: ["red-square", "alphabet"],
        char: "\u{1F18E}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      cl: {
        keywords: ["alphabet", "words", "red-square"],
        char: "\u{1F191}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      o2: {
        keywords: ["alphabet", "red-square", "letter"],
        char: "\u{1F17E}\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      sos: {
        keywords: ["help", "red-square", "words", "emergency", "911"],
        char: "\u{1F198}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      no_entry: {
        keywords: ["limit", "security", "privacy", "bad", "denied", "stop", "circle"],
        char: "\u26D4",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      name_badge: {
        keywords: ["fire", "forbid"],
        char: "\u{1F4DB}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      no_entry_sign: {
        keywords: ["forbid", "stop", "limit", "denied", "disallow", "circle"],
        char: "\u{1F6AB}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      x: {
        keywords: ["no", "delete", "remove", "cancel", "red"],
        char: "\u274C",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      o: {
        keywords: ["circle", "round"],
        char: "\u2B55",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      stop_sign: {
        keywords: ["stop"],
        char: "\u{1F6D1}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      anger: {
        keywords: ["angry", "mad"],
        char: "\u{1F4A2}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      hotsprings: {
        keywords: ["bath", "warm", "relax"],
        char: "\u2668\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      no_pedestrians: {
        keywords: ["rules", "crossing", "walking", "circle"],
        char: "\u{1F6B7}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      do_not_litter: {
        keywords: ["trash", "bin", "garbage", "circle"],
        char: "\u{1F6AF}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      no_bicycles: {
        keywords: ["cyclist", "prohibited", "circle"],
        char: "\u{1F6B3}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      "non-potable_water": {
        keywords: ["drink", "faucet", "tap", "circle"],
        char: "\u{1F6B1}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      underage: {
        keywords: ["18", "drink", "pub", "night", "minor", "circle"],
        char: "\u{1F51E}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      no_mobile_phones: {
        keywords: ["iphone", "mute", "circle"],
        char: "\u{1F4F5}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      exclamation: {
        keywords: ["heavy_exclamation_mark", "danger", "surprise", "punctuation", "wow", "warning"],
        char: "\u2757",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      grey_exclamation: {
        keywords: ["surprise", "punctuation", "gray", "wow", "warning"],
        char: "\u2755",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      question: {
        keywords: ["doubt", "confused"],
        char: "\u2753",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      grey_question: {
        keywords: ["doubts", "gray", "huh", "confused"],
        char: "\u2754",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      bangbang: {
        keywords: ["exclamation", "surprise"],
        char: "\u203C\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      interrobang: {
        keywords: ["wat", "punctuation", "surprise"],
        char: "\u2049\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      100: {
        keywords: ["score", "perfect", "numbers", "century", "exam", "quiz", "test", "pass", "hundred"],
        char: "\u{1F4AF}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      low_brightness: {
        keywords: ["sun", "afternoon", "warm", "summer"],
        char: "\u{1F505}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      high_brightness: {
        keywords: ["sun", "light"],
        char: "\u{1F506}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      trident: {
        keywords: ["weapon", "spear"],
        char: "\u{1F531}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      fleur_de_lis: {
        keywords: ["decorative", "scout"],
        char: "\u269C",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      part_alternation_mark: {
        keywords: ["graph", "presentation", "stats", "business", "economics", "bad"],
        char: "\u303D\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      warning: {
        keywords: ["exclamation", "wip", "alert", "error", "problem", "issue"],
        char: "\u26A0\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      children_crossing: {
        keywords: ["school", "warning", "danger", "sign", "driving", "yellow-diamond"],
        char: "\u{1F6B8}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      beginner: {
        keywords: ["badge", "shield"],
        char: "\u{1F530}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      recycle: {
        keywords: ["arrow", "environment", "garbage", "trash"],
        char: "\u267B\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      u6307: {
        keywords: ["chinese", "point", "green-square", "kanji"],
        char: "\u{1F22F}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      chart: {
        keywords: ["green-square", "graph", "presentation", "stats"],
        char: "\u{1F4B9}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      sparkle: {
        keywords: ["stars", "green-square", "awesome", "good", "fireworks"],
        char: "\u2747\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      eight_spoked_asterisk: {
        keywords: ["star", "sparkle", "green-square"],
        char: "\u2733\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      negative_squared_cross_mark: {
        keywords: ["x", "green-square", "no", "deny"],
        char: "\u274E",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      white_check_mark: {
        keywords: ["green-square", "ok", "agree", "vote", "election", "answer", "tick"],
        char: "\u2705",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      diamond_shape_with_a_dot_inside: {
        keywords: ["jewel", "blue", "gem", "crystal", "fancy"],
        char: "\u{1F4A0}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      cyclone: {
        keywords: ["weather", "swirl", "blue", "cloud", "vortex", "spiral", "whirlpool", "spin", "tornado", "hurricane", "typhoon"],
        char: "\u{1F300}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      loop: {
        keywords: ["tape", "cassette"],
        char: "\u27BF",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      globe_with_meridians: {
        keywords: ["earth", "international", "world", "internet", "interweb", "i18n"],
        char: "\u{1F310}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      m: {
        keywords: ["alphabet", "blue-circle", "letter"],
        char: "\u24C2\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      atm: {
        keywords: ["money", "sales", "cash", "blue-square", "payment", "bank"],
        char: "\u{1F3E7}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      sa: {
        keywords: ["japanese", "blue-square", "katakana"],
        char: "\u{1F202}\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      passport_control: {
        keywords: ["custom", "blue-square"],
        char: "\u{1F6C2}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      customs: {
        keywords: ["passport", "border", "blue-square"],
        char: "\u{1F6C3}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      baggage_claim: {
        keywords: ["blue-square", "airport", "transport"],
        char: "\u{1F6C4}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      left_luggage: {
        keywords: ["blue-square", "travel"],
        char: "\u{1F6C5}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      wheelchair: {
        keywords: ["blue-square", "disabled", "a11y", "accessibility"],
        char: "\u267F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      no_smoking: {
        keywords: ["cigarette", "blue-square", "smell", "smoke"],
        char: "\u{1F6AD}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      wc: {
        keywords: ["toilet", "restroom", "blue-square"],
        char: "\u{1F6BE}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      parking: {
        keywords: ["cars", "blue-square", "alphabet", "letter"],
        char: "\u{1F17F}\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      potable_water: {
        keywords: ["blue-square", "liquid", "restroom", "cleaning", "faucet"],
        char: "\u{1F6B0}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      mens: {
        keywords: ["toilet", "restroom", "wc", "blue-square", "gender", "male"],
        char: "\u{1F6B9}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      womens: {
        keywords: ["purple-square", "woman", "female", "toilet", "loo", "restroom", "gender"],
        char: "\u{1F6BA}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      baby_symbol: {
        keywords: ["orange-square", "child"],
        char: "\u{1F6BC}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      restroom: {
        keywords: ["blue-square", "toilet", "refresh", "wc", "gender"],
        char: "\u{1F6BB}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      put_litter_in_its_place: {
        keywords: ["blue-square", "sign", "human", "info"],
        char: "\u{1F6AE}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      cinema: {
        keywords: ["blue-square", "record", "film", "movie", "curtain", "stage", "theater"],
        char: "\u{1F3A6}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      signal_strength: {
        keywords: ["blue-square", "reception", "phone", "internet", "connection", "wifi", "bluetooth", "bars"],
        char: "\u{1F4F6}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      koko: {
        keywords: ["blue-square", "here", "katakana", "japanese", "destination"],
        char: "\u{1F201}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      ng: {
        keywords: ["blue-square", "words", "shape", "icon"],
        char: "\u{1F196}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      ok: {
        keywords: ["good", "agree", "yes", "blue-square"],
        char: "\u{1F197}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      up: {
        keywords: ["blue-square", "above", "high"],
        char: "\u{1F199}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      cool: {
        keywords: ["words", "blue-square"],
        char: "\u{1F192}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      new: {
        keywords: ["blue-square", "words", "start"],
        char: "\u{1F195}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      free: {
        keywords: ["blue-square", "words"],
        char: "\u{1F193}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      zero: {
        keywords: ["0", "numbers", "blue-square", "null"],
        char: "0\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      one: {
        keywords: ["blue-square", "numbers", "1"],
        char: "1\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      two: {
        keywords: ["numbers", "2", "prime", "blue-square"],
        char: "2\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      three: {
        keywords: ["3", "numbers", "prime", "blue-square"],
        char: "3\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      four: {
        keywords: ["4", "numbers", "blue-square"],
        char: "4\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      five: {
        keywords: ["5", "numbers", "blue-square", "prime"],
        char: "5\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      six: {
        keywords: ["6", "numbers", "blue-square"],
        char: "6\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      seven: {
        keywords: ["7", "numbers", "blue-square", "prime"],
        char: "7\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      eight: {
        keywords: ["8", "blue-square", "numbers"],
        char: "8\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      nine: {
        keywords: ["blue-square", "numbers", "9"],
        char: "9\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      keycap_ten: {
        keywords: ["numbers", "10", "blue-square"],
        char: "\u{1F51F}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      asterisk: {
        keywords: ["star", "keycap"],
        char: "*\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      1234: {
        keywords: ["numbers", "blue-square"],
        char: "\u{1F522}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      eject_button: {
        keywords: ["blue-square"],
        char: "\u23CF\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_forward: {
        keywords: ["blue-square", "right", "direction", "play"],
        char: "\u25B6\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      pause_button: {
        keywords: ["pause", "blue-square"],
        char: "\u23F8",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      next_track_button: {
        keywords: ["forward", "next", "blue-square"],
        char: "\u23ED",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      stop_button: {
        keywords: ["blue-square"],
        char: "\u23F9",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      record_button: {
        keywords: ["blue-square"],
        char: "\u23FA",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      play_or_pause_button: {
        keywords: ["blue-square", "play", "pause"],
        char: "\u23EF",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      previous_track_button: {
        keywords: ["backward"],
        char: "\u23EE",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      fast_forward: {
        keywords: ["blue-square", "play", "speed", "continue"],
        char: "\u23E9",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      rewind: {
        keywords: ["play", "blue-square"],
        char: "\u23EA",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      twisted_rightwards_arrows: {
        keywords: ["blue-square", "shuffle", "music", "random"],
        char: "\u{1F500}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      repeat: {
        keywords: ["loop", "record"],
        char: "\u{1F501}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      repeat_one: {
        keywords: ["blue-square", "loop"],
        char: "\u{1F502}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_backward: {
        keywords: ["blue-square", "left", "direction"],
        char: "\u25C0\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_up_small: {
        keywords: ["blue-square", "triangle", "direction", "point", "forward", "top"],
        char: "\u{1F53C}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_down_small: {
        keywords: ["blue-square", "direction", "bottom"],
        char: "\u{1F53D}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_double_up: {
        keywords: ["blue-square", "direction", "top"],
        char: "\u23EB",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_double_down: {
        keywords: ["blue-square", "direction", "bottom"],
        char: "\u23EC",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_right: {
        keywords: ["blue-square", "next"],
        char: "\u27A1\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_left: {
        keywords: ["blue-square", "previous", "back"],
        char: "\u2B05\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_up: {
        keywords: ["blue-square", "continue", "top", "direction"],
        char: "\u2B06\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_down: {
        keywords: ["blue-square", "direction", "bottom"],
        char: "\u2B07\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_upper_right: {
        keywords: ["blue-square", "point", "direction", "diagonal", "northeast"],
        char: "\u2197\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_lower_right: {
        keywords: ["blue-square", "direction", "diagonal", "southeast"],
        char: "\u2198\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_lower_left: {
        keywords: ["blue-square", "direction", "diagonal", "southwest"],
        char: "\u2199\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_upper_left: {
        keywords: ["blue-square", "point", "direction", "diagonal", "northwest"],
        char: "\u2196\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_up_down: {
        keywords: ["blue-square", "direction", "way", "vertical"],
        char: "\u2195\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      left_right_arrow: {
        keywords: ["shape", "direction", "horizontal", "sideways"],
        char: "\u2194\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrows_counterclockwise: {
        keywords: ["blue-square", "sync", "cycle"],
        char: "\u{1F504}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_right_hook: {
        keywords: ["blue-square", "return", "rotate", "direction"],
        char: "\u21AA\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      leftwards_arrow_with_hook: {
        keywords: ["back", "return", "blue-square", "undo", "enter"],
        char: "\u21A9\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_heading_up: {
        keywords: ["blue-square", "direction", "top"],
        char: "\u2934\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrow_heading_down: {
        keywords: ["blue-square", "direction", "bottom"],
        char: "\u2935\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      hash: {
        keywords: ["symbol", "blue-square", "twitter"],
        char: "#\uFE0F\u20E3",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      information_source: {
        keywords: ["blue-square", "alphabet", "letter"],
        char: "\u2139\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      abc: {
        keywords: ["blue-square", "alphabet"],
        char: "\u{1F524}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      abcd: {
        keywords: ["blue-square", "alphabet"],
        char: "\u{1F521}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      capital_abcd: {
        keywords: ["alphabet", "words", "blue-square"],
        char: "\u{1F520}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      symbols: {
        keywords: ["blue-square", "music", "note", "ampersand", "percent", "glyphs", "characters"],
        char: "\u{1F523}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      musical_note: {
        keywords: ["score", "tone", "sound"],
        char: "\u{1F3B5}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      notes: {
        keywords: ["music", "score"],
        char: "\u{1F3B6}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      wavy_dash: {
        keywords: ["draw", "line", "moustache", "mustache", "squiggle", "scribble"],
        char: "\u3030\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      curly_loop: {
        keywords: ["scribble", "draw", "shape", "squiggle"],
        char: "\u27B0",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heavy_check_mark: {
        keywords: ["ok", "nike", "answer", "yes", "tick"],
        char: "\u2714\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      arrows_clockwise: {
        keywords: ["sync", "cycle", "round", "repeat"],
        char: "\u{1F503}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heavy_plus_sign: {
        keywords: ["math", "calculation", "addition", "more", "increase"],
        char: "\u2795",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heavy_minus_sign: {
        keywords: ["math", "calculation", "subtract", "less"],
        char: "\u2796",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heavy_division_sign: {
        keywords: ["divide", "math", "calculation"],
        char: "\u2797",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heavy_multiplication_x: {
        keywords: ["math", "calculation"],
        char: "\u2716\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      infinity: {
        keywords: ["forever"],
        char: "\u267E",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      heavy_dollar_sign: {
        keywords: ["money", "sales", "payment", "currency", "buck"],
        char: "\u{1F4B2}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      currency_exchange: {
        keywords: ["money", "sales", "dollar", "travel"],
        char: "\u{1F4B1}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      copyright: {
        keywords: ["ip", "license", "circle", "law", "legal"],
        char: "\xA9\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      registered: {
        keywords: ["alphabet", "circle"],
        char: "\xAE\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      tm: {
        keywords: ["trademark", "brand", "law", "legal"],
        char: "\u2122\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      end: {
        keywords: ["words", "arrow"],
        char: "\u{1F51A}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      back: {
        keywords: ["arrow", "words", "return"],
        char: "\u{1F519}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      on: {
        keywords: ["arrow", "words"],
        char: "\u{1F51B}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      top: {
        keywords: ["words", "blue-square"],
        char: "\u{1F51D}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      soon: {
        keywords: ["arrow", "words"],
        char: "\u{1F51C}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      ballot_box_with_check: {
        keywords: ["ok", "agree", "confirm", "black-square", "vote", "election", "yes", "tick"],
        char: "\u2611\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      radio_button: {
        keywords: ["input", "old", "music", "circle"],
        char: "\u{1F518}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      white_circle: {
        keywords: ["shape", "round"],
        char: "\u26AA",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      black_circle: {
        keywords: ["shape", "button", "round"],
        char: "\u26AB",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      red_circle: {
        keywords: ["shape", "error", "danger"],
        char: "\u{1F534}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      large_blue_circle: {
        keywords: ["shape", "icon", "button"],
        char: "\u{1F535}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      small_orange_diamond: {
        keywords: ["shape", "jewel", "gem"],
        char: "\u{1F538}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      small_blue_diamond: {
        keywords: ["shape", "jewel", "gem"],
        char: "\u{1F539}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      large_orange_diamond: {
        keywords: ["shape", "jewel", "gem"],
        char: "\u{1F536}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      large_blue_diamond: {
        keywords: ["shape", "jewel", "gem"],
        char: "\u{1F537}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      small_red_triangle: {
        keywords: ["shape", "direction", "up", "top"],
        char: "\u{1F53A}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      black_small_square: {
        keywords: ["shape", "icon"],
        char: "\u25AA\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      white_small_square: {
        keywords: ["shape", "icon"],
        char: "\u25AB\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      black_large_square: {
        keywords: ["shape", "icon", "button"],
        char: "\u2B1B",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      white_large_square: {
        keywords: ["shape", "icon", "stone", "button"],
        char: "\u2B1C",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      small_red_triangle_down: {
        keywords: ["shape", "direction", "bottom"],
        char: "\u{1F53B}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      black_medium_square: {
        keywords: ["shape", "button", "icon"],
        char: "\u25FC\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      white_medium_square: {
        keywords: ["shape", "stone", "icon"],
        char: "\u25FB\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      black_medium_small_square: {
        keywords: ["icon", "shape", "button"],
        char: "\u25FE",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      white_medium_small_square: {
        keywords: ["shape", "stone", "icon", "button"],
        char: "\u25FD",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      black_square_button: {
        keywords: ["shape", "input", "frame"],
        char: "\u{1F532}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      white_square_button: {
        keywords: ["shape", "input"],
        char: "\u{1F533}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      speaker: {
        keywords: ["sound", "volume", "silence", "broadcast"],
        char: "\u{1F508}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      sound: {
        keywords: ["volume", "speaker", "broadcast"],
        char: "\u{1F509}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      loud_sound: {
        keywords: ["volume", "noise", "noisy", "speaker", "broadcast"],
        char: "\u{1F50A}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      mute: {
        keywords: ["sound", "volume", "silence", "quiet"],
        char: "\u{1F507}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      mega: {
        keywords: ["sound", "speaker", "volume"],
        char: "\u{1F4E3}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      loudspeaker: {
        keywords: ["volume", "sound"],
        char: "\u{1F4E2}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      bell: {
        keywords: ["sound", "notification", "christmas", "xmas", "chime"],
        char: "\u{1F514}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      no_bell: {
        keywords: ["sound", "volume", "mute", "quiet", "silent"],
        char: "\u{1F515}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      black_joker: {
        keywords: ["poker", "cards", "game", "play", "magic"],
        char: "\u{1F0CF}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      mahjong: {
        keywords: ["game", "play", "chinese", "kanji"],
        char: "\u{1F004}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      spades: {
        keywords: ["poker", "cards", "suits", "magic"],
        char: "\u2660\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clubs: {
        keywords: ["poker", "cards", "magic", "suits"],
        char: "\u2663\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      hearts: {
        keywords: ["poker", "cards", "magic", "suits"],
        char: "\u2665\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      diamonds: {
        keywords: ["poker", "cards", "magic", "suits"],
        char: "\u2666\uFE0F",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      flower_playing_cards: {
        keywords: ["game", "sunset", "red"],
        char: "\u{1F3B4}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      thought_balloon: {
        keywords: ["bubble", "cloud", "speech", "thinking", "dream"],
        char: "\u{1F4AD}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      right_anger_bubble: {
        keywords: ["caption", "speech", "thinking", "mad"],
        char: "\u{1F5EF}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      speech_balloon: {
        keywords: ["bubble", "words", "message", "talk", "chatting"],
        char: "\u{1F4AC}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      left_speech_bubble: {
        keywords: ["words", "message", "talk", "chatting"],
        char: "\u{1F5E8}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock1: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F550}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock2: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F551}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock3: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F552}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock4: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F553}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock5: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F554}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock6: {
        keywords: ["time", "late", "early", "schedule", "dawn", "dusk"],
        char: "\u{1F555}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock7: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F556}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock8: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F557}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock9: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F558}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock10: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F559}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock11: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F55A}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock12: {
        keywords: ["time", "noon", "midnight", "midday", "late", "early", "schedule"],
        char: "\u{1F55B}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock130: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F55C}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock230: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F55D}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock330: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F55E}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock430: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F55F}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock530: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F560}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock630: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F561}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock730: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F562}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock830: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F563}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock930: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F564}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock1030: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F565}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock1130: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F566}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      clock1230: {
        keywords: ["time", "late", "early", "schedule"],
        char: "\u{1F567}",
        fitzpatrick_scale: false,
        category: "symbols"
      },
      afghanistan: {
        keywords: ["af", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1EB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      aland_islands: {
        keywords: ["\xC5land", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1FD}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      albania: {
        keywords: ["al", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      algeria: {
        keywords: ["dz", "flag", "nation", "country", "banner"],
        char: "\u{1F1E9}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      american_samoa: {
        keywords: ["american", "ws", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      andorra: {
        keywords: ["ad", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1E9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      angola: {
        keywords: ["ao", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      anguilla: {
        keywords: ["ai", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      antarctica: {
        keywords: ["aq", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1F6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      antigua_barbuda: {
        keywords: ["antigua", "barbuda", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      argentina: {
        keywords: ["ar", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      armenia: {
        keywords: ["am", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      aruba: {
        keywords: ["aw", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      australia: {
        keywords: ["au", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      austria: {
        keywords: ["at", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      azerbaijan: {
        keywords: ["az", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      bahamas: {
        keywords: ["bs", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      bahrain: {
        keywords: ["bh", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      bangladesh: {
        keywords: ["bd", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1E9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      barbados: {
        keywords: ["bb", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1E7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      belarus: {
        keywords: ["by", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      belgium: {
        keywords: ["be", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      belize: {
        keywords: ["bz", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      benin: {
        keywords: ["bj", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1EF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      bermuda: {
        keywords: ["bm", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      bhutan: {
        keywords: ["bt", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      bolivia: {
        keywords: ["bo", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      caribbean_netherlands: {
        keywords: ["bonaire", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1F6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      bosnia_herzegovina: {
        keywords: ["bosnia", "herzegovina", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      botswana: {
        keywords: ["bw", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      brazil: {
        keywords: ["br", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      british_indian_ocean_territory: {
        keywords: ["british", "indian", "ocean", "territory", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      british_virgin_islands: {
        keywords: ["british", "virgin", "islands", "bvi", "flag", "nation", "country", "banner"],
        char: "\u{1F1FB}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      brunei: {
        keywords: ["bn", "darussalam", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      bulgaria: {
        keywords: ["bg", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      burkina_faso: {
        keywords: ["burkina", "faso", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1EB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      burundi: {
        keywords: ["bi", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cape_verde: {
        keywords: ["cabo", "verde", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1FB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cambodia: {
        keywords: ["kh", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cameroon: {
        keywords: ["cm", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      canada: {
        keywords: ["ca", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      canary_islands: {
        keywords: ["canary", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cayman_islands: {
        keywords: ["cayman", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      central_african_republic: {
        keywords: ["central", "african", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1EB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      chad: {
        keywords: ["td", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1E9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      chile: {
        keywords: ["flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cn: {
        keywords: ["china", "chinese", "prc", "flag", "country", "nation", "banner"],
        char: "\u{1F1E8}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      christmas_island: {
        keywords: ["christmas", "island", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1FD}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cocos_islands: {
        keywords: ["cocos", "keeling", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      colombia: {
        keywords: ["co", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      comoros: {
        keywords: ["km", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      congo_brazzaville: {
        keywords: ["congo", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      congo_kinshasa: {
        keywords: ["congo", "democratic", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1E9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cook_islands: {
        keywords: ["cook", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      costa_rica: {
        keywords: ["costa", "rica", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      croatia: {
        keywords: ["hr", "flag", "nation", "country", "banner"],
        char: "\u{1F1ED}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cuba: {
        keywords: ["cu", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      curacao: {
        keywords: ["cura\xE7ao", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cyprus: {
        keywords: ["cy", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      czech_republic: {
        keywords: ["cz", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      denmark: {
        keywords: ["dk", "flag", "nation", "country", "banner"],
        char: "\u{1F1E9}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      djibouti: {
        keywords: ["dj", "flag", "nation", "country", "banner"],
        char: "\u{1F1E9}\u{1F1EF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      dominica: {
        keywords: ["dm", "flag", "nation", "country", "banner"],
        char: "\u{1F1E9}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      dominican_republic: {
        keywords: ["dominican", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1E9}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      ecuador: {
        keywords: ["ec", "flag", "nation", "country", "banner"],
        char: "\u{1F1EA}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      egypt: {
        keywords: ["eg", "flag", "nation", "country", "banner"],
        char: "\u{1F1EA}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      el_salvador: {
        keywords: ["el", "salvador", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1FB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      equatorial_guinea: {
        keywords: ["equatorial", "gn", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1F6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      eritrea: {
        keywords: ["er", "flag", "nation", "country", "banner"],
        char: "\u{1F1EA}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      estonia: {
        keywords: ["ee", "flag", "nation", "country", "banner"],
        char: "\u{1F1EA}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      ethiopia: {
        keywords: ["et", "flag", "nation", "country", "banner"],
        char: "\u{1F1EA}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      eu: {
        keywords: ["european", "union", "flag", "banner"],
        char: "\u{1F1EA}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      falkland_islands: {
        keywords: ["falkland", "islands", "malvinas", "flag", "nation", "country", "banner"],
        char: "\u{1F1EB}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      faroe_islands: {
        keywords: ["faroe", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1EB}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      fiji: {
        keywords: ["fj", "flag", "nation", "country", "banner"],
        char: "\u{1F1EB}\u{1F1EF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      finland: {
        keywords: ["fi", "flag", "nation", "country", "banner"],
        char: "\u{1F1EB}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      fr: {
        keywords: ["banner", "flag", "nation", "france", "french", "country"],
        char: "\u{1F1EB}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      french_guiana: {
        keywords: ["french", "guiana", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1EB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      french_polynesia: {
        keywords: ["french", "polynesia", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1EB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      french_southern_territories: {
        keywords: ["french", "southern", "territories", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1EB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      gabon: {
        keywords: ["ga", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      gambia: {
        keywords: ["gm", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      georgia: {
        keywords: ["ge", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      de: {
        keywords: ["german", "nation", "flag", "country", "banner"],
        char: "\u{1F1E9}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      ghana: {
        keywords: ["gh", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      gibraltar: {
        keywords: ["gi", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      greece: {
        keywords: ["gr", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      greenland: {
        keywords: ["gl", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      grenada: {
        keywords: ["gd", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1E9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      guadeloupe: {
        keywords: ["gp", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1F5}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      guam: {
        keywords: ["gu", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      guatemala: {
        keywords: ["gt", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      guernsey: {
        keywords: ["gg", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      guinea: {
        keywords: ["gn", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      guinea_bissau: {
        keywords: ["gw", "bissau", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      guyana: {
        keywords: ["gy", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      haiti: {
        keywords: ["ht", "flag", "nation", "country", "banner"],
        char: "\u{1F1ED}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      honduras: {
        keywords: ["hn", "flag", "nation", "country", "banner"],
        char: "\u{1F1ED}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      hong_kong: {
        keywords: ["hong", "kong", "flag", "nation", "country", "banner"],
        char: "\u{1F1ED}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      hungary: {
        keywords: ["hu", "flag", "nation", "country", "banner"],
        char: "\u{1F1ED}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      iceland: {
        keywords: ["is", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      india: {
        keywords: ["in", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      indonesia: {
        keywords: ["flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1E9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      iran: {
        keywords: ["iran,", "islamic", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      iraq: {
        keywords: ["iq", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1F6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      ireland: {
        keywords: ["ie", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      isle_of_man: {
        keywords: ["isle", "man", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      israel: {
        keywords: ["il", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      it: {
        keywords: ["italy", "flag", "nation", "country", "banner"],
        char: "\u{1F1EE}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      cote_divoire: {
        keywords: ["ivory", "coast", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      jamaica: {
        keywords: ["jm", "flag", "nation", "country", "banner"],
        char: "\u{1F1EF}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      jp: {
        keywords: ["japanese", "nation", "flag", "country", "banner"],
        char: "\u{1F1EF}\u{1F1F5}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      jersey: {
        keywords: ["je", "flag", "nation", "country", "banner"],
        char: "\u{1F1EF}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      jordan: {
        keywords: ["jo", "flag", "nation", "country", "banner"],
        char: "\u{1F1EF}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      kazakhstan: {
        keywords: ["kz", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      kenya: {
        keywords: ["ke", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      kiribati: {
        keywords: ["ki", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      kosovo: {
        keywords: ["xk", "flag", "nation", "country", "banner"],
        char: "\u{1F1FD}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      kuwait: {
        keywords: ["kw", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      kyrgyzstan: {
        keywords: ["kg", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      laos: {
        keywords: ["lao", "democratic", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      latvia: {
        keywords: ["lv", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1FB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      lebanon: {
        keywords: ["lb", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1E7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      lesotho: {
        keywords: ["ls", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      liberia: {
        keywords: ["lr", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      libya: {
        keywords: ["ly", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      liechtenstein: {
        keywords: ["li", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      lithuania: {
        keywords: ["lt", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      luxembourg: {
        keywords: ["lu", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      macau: {
        keywords: ["macao", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      macedonia: {
        keywords: ["macedonia,", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      madagascar: {
        keywords: ["mg", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      malawi: {
        keywords: ["mw", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      malaysia: {
        keywords: ["my", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      maldives: {
        keywords: ["mv", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1FB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      mali: {
        keywords: ["ml", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      malta: {
        keywords: ["mt", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      marshall_islands: {
        keywords: ["marshall", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      martinique: {
        keywords: ["mq", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      mauritania: {
        keywords: ["mr", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      mauritius: {
        keywords: ["mu", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      mayotte: {
        keywords: ["yt", "flag", "nation", "country", "banner"],
        char: "\u{1F1FE}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      mexico: {
        keywords: ["mx", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1FD}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      micronesia: {
        keywords: ["micronesia,", "federated", "states", "flag", "nation", "country", "banner"],
        char: "\u{1F1EB}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      moldova: {
        keywords: ["moldova,", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1E9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      monaco: {
        keywords: ["mc", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      mongolia: {
        keywords: ["mn", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      montenegro: {
        keywords: ["me", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      montserrat: {
        keywords: ["ms", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      morocco: {
        keywords: ["ma", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      mozambique: {
        keywords: ["mz", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      myanmar: {
        keywords: ["mm", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      namibia: {
        keywords: ["na", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      nauru: {
        keywords: ["nr", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      nepal: {
        keywords: ["np", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1F5}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      netherlands: {
        keywords: ["nl", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      new_caledonia: {
        keywords: ["new", "caledonia", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      new_zealand: {
        keywords: ["new", "zealand", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      nicaragua: {
        keywords: ["ni", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      niger: {
        keywords: ["ne", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      nigeria: {
        keywords: ["flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      niue: {
        keywords: ["nu", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      norfolk_island: {
        keywords: ["norfolk", "island", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1EB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      northern_mariana_islands: {
        keywords: ["northern", "mariana", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1F2}\u{1F1F5}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      north_korea: {
        keywords: ["north", "korea", "nation", "flag", "country", "banner"],
        char: "\u{1F1F0}\u{1F1F5}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      norway: {
        keywords: ["no", "flag", "nation", "country", "banner"],
        char: "\u{1F1F3}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      oman: {
        keywords: ["om_symbol", "flag", "nation", "country", "banner"],
        char: "\u{1F1F4}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      pakistan: {
        keywords: ["pk", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      palau: {
        keywords: ["pw", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      palestinian_territories: {
        keywords: ["palestine", "palestinian", "territories", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      panama: {
        keywords: ["pa", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      papua_new_guinea: {
        keywords: ["papua", "new", "guinea", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      paraguay: {
        keywords: ["py", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      peru: {
        keywords: ["pe", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      philippines: {
        keywords: ["ph", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      pitcairn_islands: {
        keywords: ["pitcairn", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      poland: {
        keywords: ["pl", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      portugal: {
        keywords: ["pt", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      puerto_rico: {
        keywords: ["puerto", "rico", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      qatar: {
        keywords: ["qa", "flag", "nation", "country", "banner"],
        char: "\u{1F1F6}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      reunion: {
        keywords: ["r\xE9union", "flag", "nation", "country", "banner"],
        char: "\u{1F1F7}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      romania: {
        keywords: ["ro", "flag", "nation", "country", "banner"],
        char: "\u{1F1F7}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      ru: {
        keywords: ["russian", "federation", "flag", "nation", "country", "banner"],
        char: "\u{1F1F7}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      rwanda: {
        keywords: ["rw", "flag", "nation", "country", "banner"],
        char: "\u{1F1F7}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      st_barthelemy: {
        keywords: ["saint", "barth\xE9lemy", "flag", "nation", "country", "banner"],
        char: "\u{1F1E7}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      st_helena: {
        keywords: ["saint", "helena", "ascension", "tristan", "cunha", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      st_kitts_nevis: {
        keywords: ["saint", "kitts", "nevis", "flag", "nation", "country", "banner"],
        char: "\u{1F1F0}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      st_lucia: {
        keywords: ["saint", "lucia", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      st_pierre_miquelon: {
        keywords: ["saint", "pierre", "miquelon", "flag", "nation", "country", "banner"],
        char: "\u{1F1F5}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      st_vincent_grenadines: {
        keywords: ["saint", "vincent", "grenadines", "flag", "nation", "country", "banner"],
        char: "\u{1F1FB}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      samoa: {
        keywords: ["ws", "flag", "nation", "country", "banner"],
        char: "\u{1F1FC}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      san_marino: {
        keywords: ["san", "marino", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      sao_tome_principe: {
        keywords: ["sao", "tome", "principe", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      saudi_arabia: {
        keywords: ["flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      senegal: {
        keywords: ["sn", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      serbia: {
        keywords: ["rs", "flag", "nation", "country", "banner"],
        char: "\u{1F1F7}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      seychelles: {
        keywords: ["sc", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      sierra_leone: {
        keywords: ["sierra", "leone", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      singapore: {
        keywords: ["sg", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      sint_maarten: {
        keywords: ["sint", "maarten", "dutch", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1FD}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      slovakia: {
        keywords: ["sk", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      slovenia: {
        keywords: ["si", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      solomon_islands: {
        keywords: ["solomon", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1E7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      somalia: {
        keywords: ["so", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      south_africa: {
        keywords: ["south", "africa", "flag", "nation", "country", "banner"],
        char: "\u{1F1FF}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      south_georgia_south_sandwich_islands: {
        keywords: ["south", "georgia", "sandwich", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1EC}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      kr: {
        keywords: ["south", "korea", "nation", "flag", "country", "banner"],
        char: "\u{1F1F0}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      south_sudan: {
        keywords: ["south", "sd", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      es: {
        keywords: ["spain", "flag", "nation", "country", "banner"],
        char: "\u{1F1EA}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      sri_lanka: {
        keywords: ["sri", "lanka", "flag", "nation", "country", "banner"],
        char: "\u{1F1F1}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      sudan: {
        keywords: ["sd", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1E9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      suriname: {
        keywords: ["sr", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      swaziland: {
        keywords: ["sz", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      sweden: {
        keywords: ["se", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      switzerland: {
        keywords: ["ch", "flag", "nation", "country", "banner"],
        char: "\u{1F1E8}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      syria: {
        keywords: ["syrian", "arab", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1F8}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      taiwan: {
        keywords: ["tw", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      tajikistan: {
        keywords: ["tj", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1EF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      tanzania: {
        keywords: ["tanzania,", "united", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      thailand: {
        keywords: ["th", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      timor_leste: {
        keywords: ["timor", "leste", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1F1}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      togo: {
        keywords: ["tg", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      tokelau: {
        keywords: ["tk", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1F0}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      tonga: {
        keywords: ["to", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1F4}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      trinidad_tobago: {
        keywords: ["trinidad", "tobago", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1F9}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      tunisia: {
        keywords: ["tn", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      tr: {
        keywords: ["turkey", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1F7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      turkmenistan: {
        keywords: ["flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      turks_caicos_islands: {
        keywords: ["turks", "caicos", "islands", "flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1E8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      tuvalu: {
        keywords: ["flag", "nation", "country", "banner"],
        char: "\u{1F1F9}\u{1F1FB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      uganda: {
        keywords: ["ug", "flag", "nation", "country", "banner"],
        char: "\u{1F1FA}\u{1F1EC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      ukraine: {
        keywords: ["ua", "flag", "nation", "country", "banner"],
        char: "\u{1F1FA}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      united_arab_emirates: {
        keywords: ["united", "arab", "emirates", "flag", "nation", "country", "banner"],
        char: "\u{1F1E6}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      uk: {
        keywords: ["united", "kingdom", "great", "britain", "northern", "ireland", "flag", "nation", "country", "banner", "british", "UK", "english", "england", "union jack"],
        char: "\u{1F1EC}\u{1F1E7}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      england: {
        keywords: ["flag", "english"],
        char: "\u{1F3F4}\u{E0067}\u{E0062}\u{E0065}\u{E006E}\u{E0067}\u{E007F}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      scotland: {
        keywords: ["flag", "scottish"],
        char: "\u{1F3F4}\u{E0067}\u{E0062}\u{E0073}\u{E0063}\u{E0074}\u{E007F}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      wales: {
        keywords: ["flag", "welsh"],
        char: "\u{1F3F4}\u{E0067}\u{E0062}\u{E0077}\u{E006C}\u{E0073}\u{E007F}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      us: {
        keywords: ["united", "states", "america", "flag", "nation", "country", "banner"],
        char: "\u{1F1FA}\u{1F1F8}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      us_virgin_islands: {
        keywords: ["virgin", "islands", "us", "flag", "nation", "country", "banner"],
        char: "\u{1F1FB}\u{1F1EE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      uruguay: {
        keywords: ["uy", "flag", "nation", "country", "banner"],
        char: "\u{1F1FA}\u{1F1FE}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      uzbekistan: {
        keywords: ["uz", "flag", "nation", "country", "banner"],
        char: "\u{1F1FA}\u{1F1FF}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      vanuatu: {
        keywords: ["vu", "flag", "nation", "country", "banner"],
        char: "\u{1F1FB}\u{1F1FA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      vatican_city: {
        keywords: ["vatican", "city", "flag", "nation", "country", "banner"],
        char: "\u{1F1FB}\u{1F1E6}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      venezuela: {
        keywords: ["ve", "bolivarian", "republic", "flag", "nation", "country", "banner"],
        char: "\u{1F1FB}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      vietnam: {
        keywords: ["viet", "nam", "flag", "nation", "country", "banner"],
        char: "\u{1F1FB}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      wallis_futuna: {
        keywords: ["wallis", "futuna", "flag", "nation", "country", "banner"],
        char: "\u{1F1FC}\u{1F1EB}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      western_sahara: {
        keywords: ["western", "sahara", "flag", "nation", "country", "banner"],
        char: "\u{1F1EA}\u{1F1ED}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      yemen: {
        keywords: ["ye", "flag", "nation", "country", "banner"],
        char: "\u{1F1FE}\u{1F1EA}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      zambia: {
        keywords: ["zm", "flag", "nation", "country", "banner"],
        char: "\u{1F1FF}\u{1F1F2}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      zimbabwe: {
        keywords: ["zw", "flag", "nation", "country", "banner"],
        char: "\u{1F1FF}\u{1F1FC}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      united_nations: {
        keywords: ["un", "flag", "banner"],
        char: "\u{1F1FA}\u{1F1F3}",
        fitzpatrick_scale: false,
        category: "flags"
      },
      pirate_flag: {
        keywords: ["skull", "crossbones", "flag", "banner"],
        char: "\u{1F3F4}\u200D\u2620\uFE0F",
        fitzpatrick_scale: false,
        category: "flags"
      }
    });
  }
});

// dep:tinymce_plugins_emoticons_js_emojis
var tinymce_plugins_emoticons_js_emojis_default = require_emojis();
export {
  tinymce_plugins_emoticons_js_emojis_default as default
};
//# sourceMappingURL=tinymce_plugins_emoticons_js_emojis.js.map
