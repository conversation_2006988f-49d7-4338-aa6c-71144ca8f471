{"version": 3, "sources": ["../tinymce/plugins/visualchars/plugin.js", "../tinymce/plugins/visualchars/index.js", "dep:tinymce_plugins_visualchars"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var get$2 = function (toggleState) {\n      var isEnabled = function () {\n        return toggleState.get();\n      };\n      return { isEnabled: isEnabled };\n    };\n\n    var fireVisualChars = function (editor, state) {\n      return editor.fire('VisualChars', { state: state });\n    };\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType$1 = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var isString = isType$1('string');\n    var isBoolean = isSimpleType('boolean');\n    var isNumber = isSimpleType('number');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each$1 = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var filter = function (xs, pred) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n\n    var keys = Object.keys;\n    var each = function (obj, f) {\n      var props = keys(obj);\n      for (var k = 0, len = props.length; k < len; k++) {\n        var i = props[k];\n        var x = obj[i];\n        f(x, i);\n      }\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var TEXT = 3;\n\n    var type = function (element) {\n      return element.dom.nodeType;\n    };\n    var value = function (element) {\n      return element.dom.nodeValue;\n    };\n    var isType = function (t) {\n      return function (element) {\n        return type(element) === t;\n      };\n    };\n    var isText = isType(TEXT);\n\n    var rawSet = function (dom, key, value) {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    var set = function (element, key, value) {\n      rawSet(element.dom, key, value);\n    };\n    var get$1 = function (element, key) {\n      var v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    var remove$3 = function (element, key) {\n      element.dom.removeAttribute(key);\n    };\n\n    var read = function (element, attr) {\n      var value = get$1(element, attr);\n      return value === undefined || value === '' ? [] : value.split(' ');\n    };\n    var add$2 = function (element, attr, id) {\n      var old = read(element, attr);\n      var nu = old.concat([id]);\n      set(element, attr, nu.join(' '));\n      return true;\n    };\n    var remove$2 = function (element, attr, id) {\n      var nu = filter(read(element, attr), function (v) {\n        return v !== id;\n      });\n      if (nu.length > 0) {\n        set(element, attr, nu.join(' '));\n      } else {\n        remove$3(element, attr);\n      }\n      return false;\n    };\n\n    var supports = function (element) {\n      return element.dom.classList !== undefined;\n    };\n    var get = function (element) {\n      return read(element, 'class');\n    };\n    var add$1 = function (element, clazz) {\n      return add$2(element, 'class', clazz);\n    };\n    var remove$1 = function (element, clazz) {\n      return remove$2(element, 'class', clazz);\n    };\n\n    var add = function (element, clazz) {\n      if (supports(element)) {\n        element.dom.classList.add(clazz);\n      } else {\n        add$1(element, clazz);\n      }\n    };\n    var cleanClass = function (element) {\n      var classList = supports(element) ? element.dom.classList : get(element);\n      if (classList.length === 0) {\n        remove$3(element, 'class');\n      }\n    };\n    var remove = function (element, clazz) {\n      if (supports(element)) {\n        var classList = element.dom.classList;\n        classList.remove(clazz);\n      } else {\n        remove$1(element, clazz);\n      }\n      cleanClass(element);\n    };\n\n    var fromHtml = function (html, scope) {\n      var doc = scope || document;\n      var div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        console.error('HTML does not have a single root node', html);\n        throw new Error('HTML must have a single root node');\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    var fromTag = function (tag, scope) {\n      var doc = scope || document;\n      var node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    var fromText = function (text, scope) {\n      var doc = scope || document;\n      var node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    var fromDom = function (node) {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    var fromPoint = function (docElm, x, y) {\n      return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    };\n    var SugarElement = {\n      fromHtml: fromHtml,\n      fromTag: fromTag,\n      fromText: fromText,\n      fromDom: fromDom,\n      fromPoint: fromPoint\n    };\n\n    var charMap = {\n      '\\xA0': 'nbsp',\n      '\\xAD': 'shy'\n    };\n    var charMapToRegExp = function (charMap, global) {\n      var regExp = '';\n      each(charMap, function (_value, key) {\n        regExp += key;\n      });\n      return new RegExp('[' + regExp + ']', global ? 'g' : '');\n    };\n    var charMapToSelector = function (charMap) {\n      var selector = '';\n      each(charMap, function (value) {\n        if (selector) {\n          selector += ',';\n        }\n        selector += 'span.mce-' + value;\n      });\n      return selector;\n    };\n    var regExp = charMapToRegExp(charMap);\n    var regExpGlobal = charMapToRegExp(charMap, true);\n    var selector = charMapToSelector(charMap);\n    var nbspClass = 'mce-nbsp';\n\n    var wrapCharWithSpan = function (value) {\n      return '<span data-mce-bogus=\"1\" class=\"mce-' + charMap[value] + '\">' + value + '</span>';\n    };\n\n    var isMatch = function (n) {\n      var value$1 = value(n);\n      return isText(n) && value$1 !== undefined && regExp.test(value$1);\n    };\n    var filterDescendants = function (scope, predicate) {\n      var result = [];\n      var dom = scope.dom;\n      var children = map(dom.childNodes, SugarElement.fromDom);\n      each$1(children, function (x) {\n        if (predicate(x)) {\n          result = result.concat([x]);\n        }\n        result = result.concat(filterDescendants(x, predicate));\n      });\n      return result;\n    };\n    var findParentElm = function (elm, rootElm) {\n      while (elm.parentNode) {\n        if (elm.parentNode === rootElm) {\n          return elm;\n        }\n        elm = elm.parentNode;\n      }\n    };\n    var replaceWithSpans = function (text) {\n      return text.replace(regExpGlobal, wrapCharWithSpan);\n    };\n\n    var isWrappedNbsp = function (node) {\n      return node.nodeName.toLowerCase() === 'span' && node.classList.contains('mce-nbsp-wrap');\n    };\n    var show = function (editor, rootElm) {\n      var nodeList = filterDescendants(SugarElement.fromDom(rootElm), isMatch);\n      each$1(nodeList, function (n) {\n        var parent = n.dom.parentNode;\n        if (isWrappedNbsp(parent)) {\n          add(SugarElement.fromDom(parent), nbspClass);\n        } else {\n          var withSpans = replaceWithSpans(editor.dom.encode(value(n)));\n          var div = editor.dom.create('div', null, withSpans);\n          var node = void 0;\n          while (node = div.lastChild) {\n            editor.dom.insertAfter(node, n.dom);\n          }\n          editor.dom.remove(n.dom);\n        }\n      });\n    };\n    var hide = function (editor, rootElm) {\n      var nodeList = editor.dom.select(selector, rootElm);\n      each$1(nodeList, function (node) {\n        if (isWrappedNbsp(node)) {\n          remove(SugarElement.fromDom(node), nbspClass);\n        } else {\n          editor.dom.remove(node, true);\n        }\n      });\n    };\n    var toggle = function (editor) {\n      var body = editor.getBody();\n      var bookmark = editor.selection.getBookmark();\n      var parentNode = findParentElm(editor.selection.getNode(), body);\n      parentNode = parentNode !== undefined ? parentNode : body;\n      hide(editor, parentNode);\n      show(editor, parentNode);\n      editor.selection.moveToBookmark(bookmark);\n    };\n\n    var applyVisualChars = function (editor, toggleState) {\n      fireVisualChars(editor, toggleState.get());\n      var body = editor.getBody();\n      if (toggleState.get() === true) {\n        show(editor, body);\n      } else {\n        hide(editor, body);\n      }\n    };\n    var toggleVisualChars = function (editor, toggleState) {\n      toggleState.set(!toggleState.get());\n      var bookmark = editor.selection.getBookmark();\n      applyVisualChars(editor, toggleState);\n      editor.selection.moveToBookmark(bookmark);\n    };\n\n    var register$1 = function (editor, toggleState) {\n      editor.addCommand('mceVisualChars', function () {\n        toggleVisualChars(editor, toggleState);\n      });\n    };\n\n    var isEnabledByDefault = function (editor) {\n      return editor.getParam('visualchars_default_state', false);\n    };\n    var hasForcedRootBlock = function (editor) {\n      return editor.getParam('forced_root_block') !== false;\n    };\n\n    var setup$1 = function (editor, toggleState) {\n      editor.on('init', function () {\n        applyVisualChars(editor, toggleState);\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var setup = function (editor, toggleState) {\n      var debouncedToggle = global.debounce(function () {\n        toggle(editor);\n      }, 300);\n      if (hasForcedRootBlock(editor)) {\n        editor.on('keydown', function (e) {\n          if (toggleState.get() === true) {\n            e.keyCode === 13 ? toggle(editor) : debouncedToggle();\n          }\n        });\n      }\n      editor.on('remove', debouncedToggle.stop);\n    };\n\n    var toggleActiveState = function (editor, enabledStated) {\n      return function (api) {\n        api.setActive(enabledStated.get());\n        var editorEventCallback = function (e) {\n          return api.setActive(e.state);\n        };\n        editor.on('VisualChars', editorEventCallback);\n        return function () {\n          return editor.off('VisualChars', editorEventCallback);\n        };\n      };\n    };\n    var register = function (editor, toggleState) {\n      var onAction = function () {\n        return editor.execCommand('mceVisualChars');\n      };\n      editor.ui.registry.addToggleButton('visualchars', {\n        tooltip: 'Show invisible characters',\n        icon: 'visualchars',\n        onAction: onAction,\n        onSetup: toggleActiveState(editor, toggleState)\n      });\n      editor.ui.registry.addToggleMenuItem('visualchars', {\n        text: 'Show invisible characters',\n        icon: 'visualchars',\n        onAction: onAction,\n        onSetup: toggleActiveState(editor, toggleState)\n      });\n    };\n\n    function Plugin () {\n      global$1.add('visualchars', function (editor) {\n        var toggleState = Cell(isEnabledByDefault(editor));\n        register$1(editor, toggleState);\n        register(editor, toggleState);\n        setup(editor, toggleState);\n        setup$1(editor, toggleState);\n        return get$2(toggleState);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"visualchars\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/visualchars')\n//   ES2015:\n//     import 'tinymce/plugins/visualchars'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/visualchars/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,SAAQ;AACZ,YAAI,OAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,OAAM,SAAU,GAAG;AACrB,mBAAQ;AAAA;AAEV,eAAO;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA;AAAA;AAIT,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,QAAQ,SAAU,aAAa;AACjC,YAAI,YAAY,WAAY;AAC1B,iBAAO,YAAY;AAAA;AAErB,eAAO,EAAE;AAAA;AAGX,UAAI,kBAAkB,SAAU,QAAQ,OAAO;AAC7C,eAAO,OAAO,KAAK,eAAe,EAAE;AAAA;AAGtC,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,OAAM;AAC7B,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,YAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,OAAM;AACjC,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,WAAU;AAAA;AAAA;AAG5B,UAAI,WAAW,SAAS;AACxB,UAAI,YAAY,aAAa;AAC7B,UAAI,WAAW,aAAa;AAE5B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,QAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,QAAO;AAC1B,eAAO,WAAU,QAAQ,WAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,IAAI,GAAG;AAC5B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,cAAE,KAAK;AAAA;AAAA;AAGX,eAAO;AAAA;AAGT,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO,SAAU,KAAK,GAAG;AAC3B,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,IAAI;AACZ,YAAE,GAAG;AAAA;AAAA;AAIT,aAAO,WAAW,cAAc,SAAS,SAAS;AAElD,UAAI,OAAO;AAEX,UAAI,OAAO,SAAU,SAAS;AAC5B,eAAO,QAAQ,IAAI;AAAA;AAErB,UAAI,QAAQ,SAAU,SAAS;AAC7B,eAAO,QAAQ,IAAI;AAAA;AAErB,UAAI,SAAS,SAAU,GAAG;AACxB,eAAO,SAAU,SAAS;AACxB,iBAAO,KAAK,aAAa;AAAA;AAAA;AAG7B,UAAI,SAAS,OAAO;AAEpB,UAAI,SAAS,SAAU,KAAK,KAAK,QAAO;AACtC,YAAI,SAAS,WAAU,UAAU,WAAU,SAAS,SAAQ;AAC1D,cAAI,aAAa,KAAK,SAAQ;AAAA,eACzB;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAa,QAAO,eAAe;AAC7F,gBAAM,IAAI,MAAM;AAAA;AAAA;AAGpB,UAAI,MAAM,SAAU,SAAS,KAAK,QAAO;AACvC,eAAO,QAAQ,KAAK,KAAK;AAAA;AAE3B,UAAI,QAAQ,SAAU,SAAS,KAAK;AAClC,YAAI,IAAI,QAAQ,IAAI,aAAa;AACjC,eAAO,MAAM,OAAO,SAAY;AAAA;AAElC,UAAI,WAAW,SAAU,SAAS,KAAK;AACrC,gBAAQ,IAAI,gBAAgB;AAAA;AAG9B,UAAI,OAAO,SAAU,SAAS,MAAM;AAClC,YAAI,SAAQ,MAAM,SAAS;AAC3B,eAAO,WAAU,UAAa,WAAU,KAAK,KAAK,OAAM,MAAM;AAAA;AAEhE,UAAI,QAAQ,SAAU,SAAS,MAAM,IAAI;AACvC,YAAI,MAAM,KAAK,SAAS;AACxB,YAAI,KAAK,IAAI,OAAO,CAAC;AACrB,YAAI,SAAS,MAAM,GAAG,KAAK;AAC3B,eAAO;AAAA;AAET,UAAI,WAAW,SAAU,SAAS,MAAM,IAAI;AAC1C,YAAI,KAAK,OAAO,KAAK,SAAS,OAAO,SAAU,GAAG;AAChD,iBAAO,MAAM;AAAA;AAEf,YAAI,GAAG,SAAS,GAAG;AACjB,cAAI,SAAS,MAAM,GAAG,KAAK;AAAA,eACtB;AACL,mBAAS,SAAS;AAAA;AAEpB,eAAO;AAAA;AAGT,UAAI,WAAW,SAAU,SAAS;AAChC,eAAO,QAAQ,IAAI,cAAc;AAAA;AAEnC,UAAI,MAAM,SAAU,SAAS;AAC3B,eAAO,KAAK,SAAS;AAAA;AAEvB,UAAI,QAAQ,SAAU,SAAS,OAAO;AACpC,eAAO,MAAM,SAAS,SAAS;AAAA;AAEjC,UAAI,WAAW,SAAU,SAAS,OAAO;AACvC,eAAO,SAAS,SAAS,SAAS;AAAA;AAGpC,UAAI,MAAM,SAAU,SAAS,OAAO;AAClC,YAAI,SAAS,UAAU;AACrB,kBAAQ,IAAI,UAAU,IAAI;AAAA,eACrB;AACL,gBAAM,SAAS;AAAA;AAAA;AAGnB,UAAI,aAAa,SAAU,SAAS;AAClC,YAAI,YAAY,SAAS,WAAW,QAAQ,IAAI,YAAY,IAAI;AAChE,YAAI,UAAU,WAAW,GAAG;AAC1B,mBAAS,SAAS;AAAA;AAAA;AAGtB,UAAI,SAAS,SAAU,SAAS,OAAO;AACrC,YAAI,SAAS,UAAU;AACrB,cAAI,YAAY,QAAQ,IAAI;AAC5B,oBAAU,OAAO;AAAA,eACZ;AACL,mBAAS,SAAS;AAAA;AAEpB,mBAAW;AAAA;AAGb,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,IAAI,cAAc;AAC5B,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,mBAAmB,IAAI,WAAW,SAAS,GAAG;AACrD,kBAAQ,MAAM,yCAAyC;AACvD,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,QAAQ,IAAI,WAAW;AAAA;AAEhC,UAAI,UAAU,SAAU,KAAK,OAAO;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,cAAc;AAC7B,eAAO,QAAQ;AAAA;AAEjB,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,eAAe;AAC9B,eAAO,QAAQ;AAAA;AAEjB,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,EAAE,KAAK;AAAA;AAEhB,UAAI,YAAY,SAAU,QAAQ,GAAG,GAAG;AACtC,eAAO,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,IAAI,IAAI;AAAA;AAE9D,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,UAAU;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA;AAEV,UAAI,kBAAkB,SAAU,UAAS,SAAQ;AAC/C,YAAI,UAAS;AACb,aAAK,UAAS,SAAU,QAAQ,KAAK;AACnC,qBAAU;AAAA;AAEZ,eAAO,IAAI,OAAO,MAAM,UAAS,KAAK,UAAS,MAAM;AAAA;AAEvD,UAAI,oBAAoB,SAAU,UAAS;AACzC,YAAI,YAAW;AACf,aAAK,UAAS,SAAU,QAAO;AAC7B,cAAI,WAAU;AACZ,yBAAY;AAAA;AAEd,uBAAY,cAAc;AAAA;AAE5B,eAAO;AAAA;AAET,UAAI,SAAS,gBAAgB;AAC7B,UAAI,eAAe,gBAAgB,SAAS;AAC5C,UAAI,WAAW,kBAAkB;AACjC,UAAI,YAAY;AAEhB,UAAI,mBAAmB,SAAU,QAAO;AACtC,eAAO,yCAAyC,QAAQ,UAAS,OAAO,SAAQ;AAAA;AAGlF,UAAI,UAAU,SAAU,GAAG;AACzB,YAAI,UAAU,MAAM;AACpB,eAAO,OAAO,MAAM,YAAY,UAAa,OAAO,KAAK;AAAA;AAE3D,UAAI,oBAAoB,SAAU,OAAO,WAAW;AAClD,YAAI,SAAS;AACb,YAAI,MAAM,MAAM;AAChB,YAAI,WAAW,IAAI,IAAI,YAAY,aAAa;AAChD,eAAO,UAAU,SAAU,GAAG;AAC5B,cAAI,UAAU,IAAI;AAChB,qBAAS,OAAO,OAAO,CAAC;AAAA;AAE1B,mBAAS,OAAO,OAAO,kBAAkB,GAAG;AAAA;AAE9C,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,KAAK,SAAS;AAC1C,eAAO,IAAI,YAAY;AACrB,cAAI,IAAI,eAAe,SAAS;AAC9B,mBAAO;AAAA;AAET,gBAAM,IAAI;AAAA;AAAA;AAGd,UAAI,mBAAmB,SAAU,MAAM;AACrC,eAAO,KAAK,QAAQ,cAAc;AAAA;AAGpC,UAAI,gBAAgB,SAAU,MAAM;AAClC,eAAO,KAAK,SAAS,kBAAkB,UAAU,KAAK,UAAU,SAAS;AAAA;AAE3E,UAAI,OAAO,SAAU,QAAQ,SAAS;AACpC,YAAI,WAAW,kBAAkB,aAAa,QAAQ,UAAU;AAChE,eAAO,UAAU,SAAU,GAAG;AAC5B,cAAI,SAAS,EAAE,IAAI;AACnB,cAAI,cAAc,SAAS;AACzB,gBAAI,aAAa,QAAQ,SAAS;AAAA,iBAC7B;AACL,gBAAI,YAAY,iBAAiB,OAAO,IAAI,OAAO,MAAM;AACzD,gBAAI,MAAM,OAAO,IAAI,OAAO,OAAO,MAAM;AACzC,gBAAI,OAAO;AACX,mBAAO,OAAO,IAAI,WAAW;AAC3B,qBAAO,IAAI,YAAY,MAAM,EAAE;AAAA;AAEjC,mBAAO,IAAI,OAAO,EAAE;AAAA;AAAA;AAAA;AAI1B,UAAI,OAAO,SAAU,QAAQ,SAAS;AACpC,YAAI,WAAW,OAAO,IAAI,OAAO,UAAU;AAC3C,eAAO,UAAU,SAAU,MAAM;AAC/B,cAAI,cAAc,OAAO;AACvB,mBAAO,aAAa,QAAQ,OAAO;AAAA,iBAC9B;AACL,mBAAO,IAAI,OAAO,MAAM;AAAA;AAAA;AAAA;AAI9B,UAAI,SAAS,SAAU,QAAQ;AAC7B,YAAI,OAAO,OAAO;AAClB,YAAI,WAAW,OAAO,UAAU;AAChC,YAAI,aAAa,cAAc,OAAO,UAAU,WAAW;AAC3D,qBAAa,eAAe,SAAY,aAAa;AACrD,aAAK,QAAQ;AACb,aAAK,QAAQ;AACb,eAAO,UAAU,eAAe;AAAA;AAGlC,UAAI,mBAAmB,SAAU,QAAQ,aAAa;AACpD,wBAAgB,QAAQ,YAAY;AACpC,YAAI,OAAO,OAAO;AAClB,YAAI,YAAY,UAAU,MAAM;AAC9B,eAAK,QAAQ;AAAA,eACR;AACL,eAAK,QAAQ;AAAA;AAAA;AAGjB,UAAI,oBAAoB,SAAU,QAAQ,aAAa;AACrD,oBAAY,IAAI,CAAC,YAAY;AAC7B,YAAI,WAAW,OAAO,UAAU;AAChC,yBAAiB,QAAQ;AACzB,eAAO,UAAU,eAAe;AAAA;AAGlC,UAAI,aAAa,SAAU,QAAQ,aAAa;AAC9C,eAAO,WAAW,kBAAkB,WAAY;AAC9C,4BAAkB,QAAQ;AAAA;AAAA;AAI9B,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS,6BAA6B;AAAA;AAEtD,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,OAAO,SAAS,yBAAyB;AAAA;AAGlD,UAAI,UAAU,SAAU,QAAQ,aAAa;AAC3C,eAAO,GAAG,QAAQ,WAAY;AAC5B,2BAAiB,QAAQ;AAAA;AAAA;AAI7B,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,QAAQ,SAAU,QAAQ,aAAa;AACzC,YAAI,kBAAkB,OAAO,SAAS,WAAY;AAChD,iBAAO;AAAA,WACN;AACH,YAAI,mBAAmB,SAAS;AAC9B,iBAAO,GAAG,WAAW,SAAU,GAAG;AAChC,gBAAI,YAAY,UAAU,MAAM;AAC9B,gBAAE,YAAY,KAAK,OAAO,UAAU;AAAA;AAAA;AAAA;AAI1C,eAAO,GAAG,UAAU,gBAAgB;AAAA;AAGtC,UAAI,oBAAoB,SAAU,QAAQ,eAAe;AACvD,eAAO,SAAU,KAAK;AACpB,cAAI,UAAU,cAAc;AAC5B,cAAI,sBAAsB,SAAU,GAAG;AACrC,mBAAO,IAAI,UAAU,EAAE;AAAA;AAEzB,iBAAO,GAAG,eAAe;AACzB,iBAAO,WAAY;AACjB,mBAAO,OAAO,IAAI,eAAe;AAAA;AAAA;AAAA;AAIvC,UAAI,WAAW,SAAU,QAAQ,aAAa;AAC5C,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,gBAAgB,eAAe;AAAA,UAChD,SAAS;AAAA,UACT,MAAM;AAAA,UACN;AAAA,UACA,SAAS,kBAAkB,QAAQ;AAAA;AAErC,eAAO,GAAG,SAAS,kBAAkB,eAAe;AAAA,UAClD,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,SAAS,kBAAkB,QAAQ;AAAA;AAAA;AAIvC,wBAAmB;AACjB,iBAAS,IAAI,eAAe,SAAU,QAAQ;AAC5C,cAAI,cAAc,KAAK,mBAAmB;AAC1C,qBAAW,QAAQ;AACnB,mBAAS,QAAQ;AACjB,gBAAM,QAAQ;AACd,kBAAQ,QAAQ;AAChB,iBAAO,MAAM;AAAA;AAAA;AAIjB;AAAA;AAAA;AAAA;;;AC3gBJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,sCAAQ;", "names": []}