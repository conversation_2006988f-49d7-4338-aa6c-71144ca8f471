import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/table/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/table/plugin.js"() {
    (function() {
      "use strict";
      var typeOf = function(x) {
        var t = typeof x;
        if (x === null) {
          return "null";
        } else if (t === "object" && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === "Array")) {
          return "array";
        } else if (t === "object" && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === "String")) {
          return "string";
        } else {
          return t;
        }
      };
      var isType$1 = function(type2) {
        return function(value2) {
          return typeOf(value2) === type2;
        };
      };
      var isSimpleType = function(type2) {
        return function(value2) {
          return typeof value2 === type2;
        };
      };
      var eq$2 = function(t) {
        return function(a) {
          return t === a;
        };
      };
      var isString = isType$1("string");
      var isObject = isType$1("object");
      var isArray = isType$1("array");
      var isNull = eq$2(null);
      var isBoolean = isSimpleType("boolean");
      var isUndefined = eq$2(void 0);
      var isNullable = function(a) {
        return a === null || a === void 0;
      };
      var isNonNullable = function(a) {
        return !isNullable(a);
      };
      var isFunction = isSimpleType("function");
      var isNumber = isSimpleType("number");
      var noop = function() {
      };
      var compose = function(fa, fb) {
        return function() {
          var args = [];
          for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
          }
          return fa(fb.apply(null, args));
        };
      };
      var compose1 = function(fbc, fab) {
        return function(a) {
          return fbc(fab(a));
        };
      };
      var constant = function(value2) {
        return function() {
          return value2;
        };
      };
      var identity = function(x) {
        return x;
      };
      var tripleEquals = function(a, b) {
        return a === b;
      };
      function curry(fn) {
        var initialArgs = [];
        for (var _i = 1; _i < arguments.length; _i++) {
          initialArgs[_i - 1] = arguments[_i];
        }
        return function() {
          var restArgs = [];
          for (var _i2 = 0; _i2 < arguments.length; _i2++) {
            restArgs[_i2] = arguments[_i2];
          }
          var all2 = initialArgs.concat(restArgs);
          return fn.apply(null, all2);
        };
      }
      var not = function(f) {
        return function(t) {
          return !f(t);
        };
      };
      var die = function(msg) {
        return function() {
          throw new Error(msg);
        };
      };
      var never = constant(false);
      var always = constant(true);
      var none$2 = function() {
        return NONE;
      };
      var NONE = function() {
        var call = function(thunk) {
          return thunk();
        };
        var id = identity;
        var me = {
          fold: function(n, _s) {
            return n();
          },
          isSome: never,
          isNone: always,
          getOr: id,
          getOrThunk: call,
          getOrDie: function(msg) {
            throw new Error(msg || "error: getOrDie called on none.");
          },
          getOrNull: constant(null),
          getOrUndefined: constant(void 0),
          or: id,
          orThunk: call,
          map: none$2,
          each: noop,
          bind: none$2,
          exists: never,
          forall: always,
          filter: function() {
            return none$2();
          },
          toArray: function() {
            return [];
          },
          toString: constant("none()")
        };
        return me;
      }();
      var some = function(a) {
        var constant_a = constant(a);
        var self = function() {
          return me;
        };
        var bind2 = function(f) {
          return f(a);
        };
        var me = {
          fold: function(n, s) {
            return s(a);
          },
          isSome: always,
          isNone: never,
          getOr: constant_a,
          getOrThunk: constant_a,
          getOrDie: constant_a,
          getOrNull: constant_a,
          getOrUndefined: constant_a,
          or: self,
          orThunk: self,
          map: function(f) {
            return some(f(a));
          },
          each: function(f) {
            f(a);
          },
          bind: bind2,
          exists: bind2,
          forall: bind2,
          filter: function(f) {
            return f(a) ? me : NONE;
          },
          toArray: function() {
            return [a];
          },
          toString: function() {
            return "some(" + a + ")";
          }
        };
        return me;
      };
      var from$1 = function(value2) {
        return value2 === null || value2 === void 0 ? NONE : some(value2);
      };
      var Optional = {
        some,
        none: none$2,
        from: from$1
      };
      var nativeSlice = Array.prototype.slice;
      var nativeIndexOf = Array.prototype.indexOf;
      var nativePush = Array.prototype.push;
      var rawIndexOf = function(ts, t) {
        return nativeIndexOf.call(ts, t);
      };
      var contains$2 = function(xs, x) {
        return rawIndexOf(xs, x) > -1;
      };
      var exists = function(xs, pred) {
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          if (pred(x, i)) {
            return true;
          }
        }
        return false;
      };
      var range$1 = function(num, f) {
        var r2 = [];
        for (var i = 0; i < num; i++) {
          r2.push(f(i));
        }
        return r2;
      };
      var map$1 = function(xs, f) {
        var len = xs.length;
        var r2 = new Array(len);
        for (var i = 0; i < len; i++) {
          var x = xs[i];
          r2[i] = f(x, i);
        }
        return r2;
      };
      var each$2 = function(xs, f) {
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          f(x, i);
        }
      };
      var eachr = function(xs, f) {
        for (var i = xs.length - 1; i >= 0; i--) {
          var x = xs[i];
          f(x, i);
        }
      };
      var partition = function(xs, pred) {
        var pass = [];
        var fail = [];
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          var arr = pred(x, i) ? pass : fail;
          arr.push(x);
        }
        return {
          pass,
          fail
        };
      };
      var filter$2 = function(xs, pred) {
        var r2 = [];
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          if (pred(x, i)) {
            r2.push(x);
          }
        }
        return r2;
      };
      var foldr = function(xs, f, acc) {
        eachr(xs, function(x, i) {
          acc = f(acc, x, i);
        });
        return acc;
      };
      var foldl = function(xs, f, acc) {
        each$2(xs, function(x, i) {
          acc = f(acc, x, i);
        });
        return acc;
      };
      var findUntil = function(xs, pred, until) {
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          if (pred(x, i)) {
            return Optional.some(x);
          } else if (until(x, i)) {
            break;
          }
        }
        return Optional.none();
      };
      var find$1 = function(xs, pred) {
        return findUntil(xs, pred, never);
      };
      var findIndex = function(xs, pred) {
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          if (pred(x, i)) {
            return Optional.some(i);
          }
        }
        return Optional.none();
      };
      var flatten$1 = function(xs) {
        var r2 = [];
        for (var i = 0, len = xs.length; i < len; ++i) {
          if (!isArray(xs[i])) {
            throw new Error("Arr.flatten item " + i + " was not an array, input: " + xs);
          }
          nativePush.apply(r2, xs[i]);
        }
        return r2;
      };
      var bind$2 = function(xs, f) {
        return flatten$1(map$1(xs, f));
      };
      var forall = function(xs, pred) {
        for (var i = 0, len = xs.length; i < len; ++i) {
          var x = xs[i];
          if (pred(x, i) !== true) {
            return false;
          }
        }
        return true;
      };
      var reverse = function(xs) {
        var r2 = nativeSlice.call(xs, 0);
        r2.reverse();
        return r2;
      };
      var mapToObject = function(xs, f) {
        var r2 = {};
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          r2[String(x)] = f(x, i);
        }
        return r2;
      };
      var pure = function(x) {
        return [x];
      };
      var sort$1 = function(xs, comparator) {
        var copy2 = nativeSlice.call(xs, 0);
        copy2.sort(comparator);
        return copy2;
      };
      var get$d = function(xs, i) {
        return i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();
      };
      var head = function(xs) {
        return get$d(xs, 0);
      };
      var last$2 = function(xs) {
        return get$d(xs, xs.length - 1);
      };
      var findMap = function(arr, f) {
        for (var i = 0; i < arr.length; i++) {
          var r2 = f(arr[i], i);
          if (r2.isSome()) {
            return r2;
          }
        }
        return Optional.none();
      };
      var __assign = function() {
        __assign = Object.assign || function __assign2(t) {
          for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s)
              if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
          }
          return t;
        };
        return __assign.apply(this, arguments);
      };
      function __spreadArray(to, from2, pack) {
        if (pack || arguments.length === 2)
          for (var i = 0, l = from2.length, ar; i < l; i++) {
            if (ar || !(i in from2)) {
              if (!ar)
                ar = Array.prototype.slice.call(from2, 0, i);
              ar[i] = from2[i];
            }
          }
        return to.concat(ar || Array.prototype.slice.call(from2));
      }
      var cached = function(f) {
        var called = false;
        var r2;
        return function() {
          var args = [];
          for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
          }
          if (!called) {
            called = true;
            r2 = f.apply(null, args);
          }
          return r2;
        };
      };
      var DeviceType = function(os, browser, userAgent, mediaMatch2) {
        var isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;
        var isiPhone = os.isiOS() && !isiPad;
        var isMobile = os.isiOS() || os.isAndroid();
        var isTouch = isMobile || mediaMatch2("(pointer:coarse)");
        var isTablet = isiPad || !isiPhone && isMobile && mediaMatch2("(min-device-width:768px)");
        var isPhone = isiPhone || isMobile && !isTablet;
        var iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;
        var isDesktop = !isPhone && !isTablet && !iOSwebview;
        return {
          isiPad: constant(isiPad),
          isiPhone: constant(isiPhone),
          isTablet: constant(isTablet),
          isPhone: constant(isPhone),
          isTouch: constant(isTouch),
          isAndroid: os.isAndroid,
          isiOS: os.isiOS,
          isWebView: constant(iOSwebview),
          isDesktop: constant(isDesktop)
        };
      };
      var firstMatch = function(regexes, s) {
        for (var i = 0; i < regexes.length; i++) {
          var x = regexes[i];
          if (x.test(s)) {
            return x;
          }
        }
        return void 0;
      };
      var find = function(regexes, agent) {
        var r2 = firstMatch(regexes, agent);
        if (!r2) {
          return {
            major: 0,
            minor: 0
          };
        }
        var group = function(i) {
          return Number(agent.replace(r2, "$" + i));
        };
        return nu$2(group(1), group(2));
      };
      var detect$6 = function(versionRegexes, agent) {
        var cleanedAgent = String(agent).toLowerCase();
        if (versionRegexes.length === 0) {
          return unknown$2();
        }
        return find(versionRegexes, cleanedAgent);
      };
      var unknown$2 = function() {
        return nu$2(0, 0);
      };
      var nu$2 = function(major, minor) {
        return {
          major,
          minor
        };
      };
      var Version = {
        nu: nu$2,
        detect: detect$6,
        unknown: unknown$2
      };
      var detectBrowser$1 = function(browsers2, userAgentData) {
        return findMap(userAgentData.brands, function(uaBrand) {
          var lcBrand = uaBrand.brand.toLowerCase();
          return find$1(browsers2, function(browser) {
            var _a;
            return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase());
          }).map(function(info) {
            return {
              current: info.name,
              version: Version.nu(parseInt(uaBrand.version, 10), 0)
            };
          });
        });
      };
      var detect$5 = function(candidates, userAgent) {
        var agent = String(userAgent).toLowerCase();
        return find$1(candidates, function(candidate) {
          return candidate.search(agent);
        });
      };
      var detectBrowser = function(browsers2, userAgent) {
        return detect$5(browsers2, userAgent).map(function(browser) {
          var version = Version.detect(browser.versionRegexes, userAgent);
          return {
            current: browser.name,
            version
          };
        });
      };
      var detectOs = function(oses2, userAgent) {
        return detect$5(oses2, userAgent).map(function(os) {
          var version = Version.detect(os.versionRegexes, userAgent);
          return {
            current: os.name,
            version
          };
        });
      };
      var removeFromStart = function(str, numChars) {
        return str.substring(numChars);
      };
      var checkRange = function(str, substr, start) {
        return substr === "" || str.length >= substr.length && str.substr(start, start + substr.length) === substr;
      };
      var removeLeading = function(str, prefix) {
        return startsWith(str, prefix) ? removeFromStart(str, prefix.length) : str;
      };
      var contains$1 = function(str, substr) {
        return str.indexOf(substr) !== -1;
      };
      var startsWith = function(str, prefix) {
        return checkRange(str, prefix, 0);
      };
      var endsWith = function(str, suffix) {
        return checkRange(str, suffix, str.length - suffix.length);
      };
      var blank = function(r2) {
        return function(s) {
          return s.replace(r2, "");
        };
      };
      var trim = blank(/^\s+|\s+$/g);
      var isNotEmpty = function(s) {
        return s.length > 0;
      };
      var isEmpty$1 = function(s) {
        return !isNotEmpty(s);
      };
      var toFloat = function(value2) {
        var num = parseFloat(value2);
        return isNaN(num) ? Optional.none() : Optional.some(num);
      };
      var normalVersionRegex = /.*?version\/\ ?([0-9]+)\.([0-9]+).*/;
      var checkContains = function(target) {
        return function(uastring) {
          return contains$1(uastring, target);
        };
      };
      var browsers = [
        {
          name: "Edge",
          versionRegexes: [/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],
          search: function(uastring) {
            return contains$1(uastring, "edge/") && contains$1(uastring, "chrome") && contains$1(uastring, "safari") && contains$1(uastring, "applewebkit");
          }
        },
        {
          name: "Chrome",
          brand: "Chromium",
          versionRegexes: [
            /.*?chrome\/([0-9]+)\.([0-9]+).*/,
            normalVersionRegex
          ],
          search: function(uastring) {
            return contains$1(uastring, "chrome") && !contains$1(uastring, "chromeframe");
          }
        },
        {
          name: "IE",
          versionRegexes: [
            /.*?msie\ ?([0-9]+)\.([0-9]+).*/,
            /.*?rv:([0-9]+)\.([0-9]+).*/
          ],
          search: function(uastring) {
            return contains$1(uastring, "msie") || contains$1(uastring, "trident");
          }
        },
        {
          name: "Opera",
          versionRegexes: [
            normalVersionRegex,
            /.*?opera\/([0-9]+)\.([0-9]+).*/
          ],
          search: checkContains("opera")
        },
        {
          name: "Firefox",
          versionRegexes: [/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],
          search: checkContains("firefox")
        },
        {
          name: "Safari",
          versionRegexes: [
            normalVersionRegex,
            /.*?cpu os ([0-9]+)_([0-9]+).*/
          ],
          search: function(uastring) {
            return (contains$1(uastring, "safari") || contains$1(uastring, "mobile/")) && contains$1(uastring, "applewebkit");
          }
        }
      ];
      var oses = [
        {
          name: "Windows",
          search: checkContains("win"),
          versionRegexes: [/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]
        },
        {
          name: "iOS",
          search: function(uastring) {
            return contains$1(uastring, "iphone") || contains$1(uastring, "ipad");
          },
          versionRegexes: [
            /.*?version\/\ ?([0-9]+)\.([0-9]+).*/,
            /.*cpu os ([0-9]+)_([0-9]+).*/,
            /.*cpu iphone os ([0-9]+)_([0-9]+).*/
          ]
        },
        {
          name: "Android",
          search: checkContains("android"),
          versionRegexes: [/.*?android\ ?([0-9]+)\.([0-9]+).*/]
        },
        {
          name: "OSX",
          search: checkContains("mac os x"),
          versionRegexes: [/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]
        },
        {
          name: "Linux",
          search: checkContains("linux"),
          versionRegexes: []
        },
        {
          name: "Solaris",
          search: checkContains("sunos"),
          versionRegexes: []
        },
        {
          name: "FreeBSD",
          search: checkContains("freebsd"),
          versionRegexes: []
        },
        {
          name: "ChromeOS",
          search: checkContains("cros"),
          versionRegexes: [/.*?chrome\/([0-9]+)\.([0-9]+).*/]
        }
      ];
      var PlatformInfo = {
        browsers: constant(browsers),
        oses: constant(oses)
      };
      var edge = "Edge";
      var chrome = "Chrome";
      var ie = "IE";
      var opera = "Opera";
      var firefox = "Firefox";
      var safari = "Safari";
      var unknown$1 = function() {
        return nu$1({
          current: void 0,
          version: Version.unknown()
        });
      };
      var nu$1 = function(info) {
        var current = info.current;
        var version = info.version;
        var isBrowser = function(name2) {
          return function() {
            return current === name2;
          };
        };
        return {
          current,
          version,
          isEdge: isBrowser(edge),
          isChrome: isBrowser(chrome),
          isIE: isBrowser(ie),
          isOpera: isBrowser(opera),
          isFirefox: isBrowser(firefox),
          isSafari: isBrowser(safari)
        };
      };
      var Browser = {
        unknown: unknown$1,
        nu: nu$1,
        edge: constant(edge),
        chrome: constant(chrome),
        ie: constant(ie),
        opera: constant(opera),
        firefox: constant(firefox),
        safari: constant(safari)
      };
      var windows = "Windows";
      var ios = "iOS";
      var android = "Android";
      var linux = "Linux";
      var osx = "OSX";
      var solaris = "Solaris";
      var freebsd = "FreeBSD";
      var chromeos = "ChromeOS";
      var unknown = function() {
        return nu({
          current: void 0,
          version: Version.unknown()
        });
      };
      var nu = function(info) {
        var current = info.current;
        var version = info.version;
        var isOS = function(name2) {
          return function() {
            return current === name2;
          };
        };
        return {
          current,
          version,
          isWindows: isOS(windows),
          isiOS: isOS(ios),
          isAndroid: isOS(android),
          isOSX: isOS(osx),
          isLinux: isOS(linux),
          isSolaris: isOS(solaris),
          isFreeBSD: isOS(freebsd),
          isChromeOS: isOS(chromeos)
        };
      };
      var OperatingSystem = {
        unknown,
        nu,
        windows: constant(windows),
        ios: constant(ios),
        android: constant(android),
        linux: constant(linux),
        osx: constant(osx),
        solaris: constant(solaris),
        freebsd: constant(freebsd),
        chromeos: constant(chromeos)
      };
      var detect$4 = function(userAgent, userAgentDataOpt, mediaMatch2) {
        var browsers2 = PlatformInfo.browsers();
        var oses2 = PlatformInfo.oses();
        var browser = userAgentDataOpt.bind(function(userAgentData) {
          return detectBrowser$1(browsers2, userAgentData);
        }).orThunk(function() {
          return detectBrowser(browsers2, userAgent);
        }).fold(Browser.unknown, Browser.nu);
        var os = detectOs(oses2, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);
        var deviceType = DeviceType(os, browser, userAgent, mediaMatch2);
        return {
          browser,
          os,
          deviceType
        };
      };
      var PlatformDetection = { detect: detect$4 };
      var mediaMatch = function(query) {
        return window.matchMedia(query).matches;
      };
      var platform = cached(function() {
        return PlatformDetection.detect(navigator.userAgent, Optional.from(navigator.userAgentData), mediaMatch);
      });
      var detect$3 = function() {
        return platform();
      };
      var compareDocumentPosition = function(a, b, match) {
        return (a.compareDocumentPosition(b) & match) !== 0;
      };
      var documentPositionContainedBy = function(a, b) {
        return compareDocumentPosition(a, b, Node.DOCUMENT_POSITION_CONTAINED_BY);
      };
      var COMMENT = 8;
      var DOCUMENT = 9;
      var DOCUMENT_FRAGMENT = 11;
      var ELEMENT = 1;
      var TEXT = 3;
      var fromHtml$1 = function(html, scope) {
        var doc = scope || document;
        var div = doc.createElement("div");
        div.innerHTML = html;
        if (!div.hasChildNodes() || div.childNodes.length > 1) {
          console.error("HTML does not have a single root node", html);
          throw new Error("HTML must have a single root node");
        }
        return fromDom$1(div.childNodes[0]);
      };
      var fromTag = function(tag, scope) {
        var doc = scope || document;
        var node = doc.createElement(tag);
        return fromDom$1(node);
      };
      var fromText = function(text, scope) {
        var doc = scope || document;
        var node = doc.createTextNode(text);
        return fromDom$1(node);
      };
      var fromDom$1 = function(node) {
        if (node === null || node === void 0) {
          throw new Error("Node cannot be null or undefined");
        }
        return { dom: node };
      };
      var fromPoint$1 = function(docElm, x, y) {
        return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom$1);
      };
      var SugarElement = {
        fromHtml: fromHtml$1,
        fromTag,
        fromText,
        fromDom: fromDom$1,
        fromPoint: fromPoint$1
      };
      var is$2 = function(element, selector) {
        var dom = element.dom;
        if (dom.nodeType !== ELEMENT) {
          return false;
        } else {
          var elem = dom;
          if (elem.matches !== void 0) {
            return elem.matches(selector);
          } else if (elem.msMatchesSelector !== void 0) {
            return elem.msMatchesSelector(selector);
          } else if (elem.webkitMatchesSelector !== void 0) {
            return elem.webkitMatchesSelector(selector);
          } else if (elem.mozMatchesSelector !== void 0) {
            return elem.mozMatchesSelector(selector);
          } else {
            throw new Error("Browser lacks native selectors");
          }
        }
      };
      var bypassSelector = function(dom) {
        return dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;
      };
      var all$1 = function(selector, scope) {
        var base2 = scope === void 0 ? document : scope.dom;
        return bypassSelector(base2) ? [] : map$1(base2.querySelectorAll(selector), SugarElement.fromDom);
      };
      var one = function(selector, scope) {
        var base2 = scope === void 0 ? document : scope.dom;
        return bypassSelector(base2) ? Optional.none() : Optional.from(base2.querySelector(selector)).map(SugarElement.fromDom);
      };
      var eq$1 = function(e1, e2) {
        return e1.dom === e2.dom;
      };
      var regularContains = function(e1, e2) {
        var d1 = e1.dom;
        var d2 = e2.dom;
        return d1 === d2 ? false : d1.contains(d2);
      };
      var ieContains = function(e1, e2) {
        return documentPositionContainedBy(e1.dom, e2.dom);
      };
      var contains = function(e1, e2) {
        return detect$3().browser.isIE() ? ieContains(e1, e2) : regularContains(e1, e2);
      };
      var is$1 = is$2;
      var keys = Object.keys;
      var hasOwnProperty = Object.hasOwnProperty;
      var each$1 = function(obj, f) {
        var props = keys(obj);
        for (var k = 0, len = props.length; k < len; k++) {
          var i = props[k];
          var x = obj[i];
          f(x, i);
        }
      };
      var map = function(obj, f) {
        return tupleMap(obj, function(x, i) {
          return {
            k: i,
            v: f(x, i)
          };
        });
      };
      var tupleMap = function(obj, f) {
        var r2 = {};
        each$1(obj, function(x, i) {
          var tuple = f(x, i);
          r2[tuple.k] = tuple.v;
        });
        return r2;
      };
      var objAcc = function(r2) {
        return function(x, i) {
          r2[i] = x;
        };
      };
      var internalFilter = function(obj, pred, onTrue, onFalse) {
        var r2 = {};
        each$1(obj, function(x, i) {
          (pred(x, i) ? onTrue : onFalse)(x, i);
        });
        return r2;
      };
      var filter$1 = function(obj, pred) {
        var t = {};
        internalFilter(obj, pred, objAcc(t), noop);
        return t;
      };
      var mapToArray = function(obj, f) {
        var r2 = [];
        each$1(obj, function(value2, name2) {
          r2.push(f(value2, name2));
        });
        return r2;
      };
      var values = function(obj) {
        return mapToArray(obj, identity);
      };
      var size = function(obj) {
        return keys(obj).length;
      };
      var get$c = function(obj, key2) {
        return has$1(obj, key2) ? Optional.from(obj[key2]) : Optional.none();
      };
      var has$1 = function(obj, key2) {
        return hasOwnProperty.call(obj, key2);
      };
      var hasNonNullableKey = function(obj, key2) {
        return has$1(obj, key2) && obj[key2] !== void 0 && obj[key2] !== null;
      };
      var isEmpty = function(r2) {
        for (var x in r2) {
          if (hasOwnProperty.call(r2, x)) {
            return false;
          }
        }
        return true;
      };
      var validSectionList = [
        "tfoot",
        "thead",
        "tbody",
        "colgroup"
      ];
      var isValidSection = function(parentName) {
        return contains$2(validSectionList, parentName);
      };
      var grid = function(rows2, columns2) {
        return {
          rows: rows2,
          columns: columns2
        };
      };
      var address = function(row2, column) {
        return {
          row: row2,
          column
        };
      };
      var detail = function(element, rowspan, colspan) {
        return {
          element,
          rowspan,
          colspan
        };
      };
      var detailnew = function(element, rowspan, colspan, isNew) {
        return {
          element,
          rowspan,
          colspan,
          isNew
        };
      };
      var extended = function(element, rowspan, colspan, row2, column, isLocked) {
        return {
          element,
          rowspan,
          colspan,
          row: row2,
          column,
          isLocked
        };
      };
      var rowdetail = function(element, cells2, section2) {
        return {
          element,
          cells: cells2,
          section: section2
        };
      };
      var rowdetailnew = function(element, cells2, section2, isNew) {
        return {
          element,
          cells: cells2,
          section: section2,
          isNew
        };
      };
      var elementnew = function(element, isNew, isLocked) {
        return {
          element,
          isNew,
          isLocked
        };
      };
      var rowcells = function(element, cells2, section2, isNew) {
        return {
          element,
          cells: cells2,
          section: section2,
          isNew
        };
      };
      var bounds = function(startRow, startCol, finishRow, finishCol) {
        return {
          startRow,
          startCol,
          finishRow,
          finishCol
        };
      };
      var columnext = function(element, colspan, column) {
        return {
          element,
          colspan,
          column
        };
      };
      var colgroup = function(element, columns2) {
        return {
          element,
          columns: columns2
        };
      };
      typeof window !== "undefined" ? window : Function("return this;")();
      var name = function(element) {
        var r2 = element.dom.nodeName;
        return r2.toLowerCase();
      };
      var type$1 = function(element) {
        return element.dom.nodeType;
      };
      var isType = function(t) {
        return function(element) {
          return type$1(element) === t;
        };
      };
      var isComment = function(element) {
        return type$1(element) === COMMENT || name(element) === "#comment";
      };
      var isElement = isType(ELEMENT);
      var isText = isType(TEXT);
      var isDocument = isType(DOCUMENT);
      var isDocumentFragment = isType(DOCUMENT_FRAGMENT);
      var isTag = function(tag) {
        return function(e) {
          return isElement(e) && name(e) === tag;
        };
      };
      var owner = function(element) {
        return SugarElement.fromDom(element.dom.ownerDocument);
      };
      var documentOrOwner = function(dos) {
        return isDocument(dos) ? dos : owner(dos);
      };
      var defaultView = function(element) {
        return SugarElement.fromDom(documentOrOwner(element).dom.defaultView);
      };
      var parent = function(element) {
        return Optional.from(element.dom.parentNode).map(SugarElement.fromDom);
      };
      var parentElement = function(element) {
        return Optional.from(element.dom.parentElement).map(SugarElement.fromDom);
      };
      var parents = function(element, isRoot) {
        var stop = isFunction(isRoot) ? isRoot : never;
        var dom = element.dom;
        var ret = [];
        while (dom.parentNode !== null && dom.parentNode !== void 0) {
          var rawParent = dom.parentNode;
          var p = SugarElement.fromDom(rawParent);
          ret.push(p);
          if (stop(p) === true) {
            break;
          } else {
            dom = rawParent;
          }
        }
        return ret;
      };
      var prevSibling = function(element) {
        return Optional.from(element.dom.previousSibling).map(SugarElement.fromDom);
      };
      var nextSibling = function(element) {
        return Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);
      };
      var children$3 = function(element) {
        return map$1(element.dom.childNodes, SugarElement.fromDom);
      };
      var child$3 = function(element, index) {
        var cs = element.dom.childNodes;
        return Optional.from(cs[index]).map(SugarElement.fromDom);
      };
      var firstChild = function(element) {
        return child$3(element, 0);
      };
      var isShadowRoot = function(dos) {
        return isDocumentFragment(dos) && isNonNullable(dos.dom.host);
      };
      var supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);
      var isSupported$1 = constant(supported);
      var getRootNode = supported ? function(e) {
        return SugarElement.fromDom(e.dom.getRootNode());
      } : documentOrOwner;
      var getShadowRoot = function(e) {
        var r2 = getRootNode(e);
        return isShadowRoot(r2) ? Optional.some(r2) : Optional.none();
      };
      var getShadowHost = function(e) {
        return SugarElement.fromDom(e.dom.host);
      };
      var getOriginalEventTarget = function(event) {
        if (isSupported$1() && isNonNullable(event.target)) {
          var el = SugarElement.fromDom(event.target);
          if (isElement(el) && isOpenShadowHost(el)) {
            if (event.composed && event.composedPath) {
              var composedPath = event.composedPath();
              if (composedPath) {
                return head(composedPath);
              }
            }
          }
        }
        return Optional.from(event.target);
      };
      var isOpenShadowHost = function(element) {
        return isNonNullable(element.dom.shadowRoot);
      };
      var inBody = function(element) {
        var dom = isText(element) ? element.dom.parentNode : element.dom;
        if (dom === void 0 || dom === null || dom.ownerDocument === null) {
          return false;
        }
        var doc = dom.ownerDocument;
        return getShadowRoot(SugarElement.fromDom(dom)).fold(function() {
          return doc.body.contains(dom);
        }, compose1(inBody, getShadowHost));
      };
      var body$1 = function() {
        return getBody$1(SugarElement.fromDom(document));
      };
      var getBody$1 = function(doc) {
        var b = doc.dom.body;
        if (b === null || b === void 0) {
          throw new Error("Body is not available yet");
        }
        return SugarElement.fromDom(b);
      };
      var ancestors$4 = function(scope, predicate, isRoot) {
        return filter$2(parents(scope, isRoot), predicate);
      };
      var children$2 = function(scope, predicate) {
        return filter$2(children$3(scope), predicate);
      };
      var descendants$1 = function(scope, predicate) {
        var result = [];
        each$2(children$3(scope), function(x) {
          if (predicate(x)) {
            result = result.concat([x]);
          }
          result = result.concat(descendants$1(x, predicate));
        });
        return result;
      };
      var ancestors$3 = function(scope, selector, isRoot) {
        return ancestors$4(scope, function(e) {
          return is$2(e, selector);
        }, isRoot);
      };
      var children$1 = function(scope, selector) {
        return children$2(scope, function(e) {
          return is$2(e, selector);
        });
      };
      var descendants = function(scope, selector) {
        return all$1(selector, scope);
      };
      function ClosestOrAncestor(is2, ancestor2, scope, a, isRoot) {
        if (is2(scope, a)) {
          return Optional.some(scope);
        } else if (isFunction(isRoot) && isRoot(scope)) {
          return Optional.none();
        } else {
          return ancestor2(scope, a, isRoot);
        }
      }
      var ancestor$2 = function(scope, predicate, isRoot) {
        var element = scope.dom;
        var stop = isFunction(isRoot) ? isRoot : never;
        while (element.parentNode) {
          element = element.parentNode;
          var el = SugarElement.fromDom(element);
          if (predicate(el)) {
            return Optional.some(el);
          } else if (stop(el)) {
            break;
          }
        }
        return Optional.none();
      };
      var closest$2 = function(scope, predicate, isRoot) {
        var is2 = function(s, test) {
          return test(s);
        };
        return ClosestOrAncestor(is2, ancestor$2, scope, predicate, isRoot);
      };
      var child$2 = function(scope, predicate) {
        var pred = function(node) {
          return predicate(SugarElement.fromDom(node));
        };
        var result = find$1(scope.dom.childNodes, pred);
        return result.map(SugarElement.fromDom);
      };
      var descendant$1 = function(scope, predicate) {
        var descend = function(node) {
          for (var i = 0; i < node.childNodes.length; i++) {
            var child_1 = SugarElement.fromDom(node.childNodes[i]);
            if (predicate(child_1)) {
              return Optional.some(child_1);
            }
            var res = descend(node.childNodes[i]);
            if (res.isSome()) {
              return res;
            }
          }
          return Optional.none();
        };
        return descend(scope.dom);
      };
      var ancestor$1 = function(scope, selector, isRoot) {
        return ancestor$2(scope, function(e) {
          return is$2(e, selector);
        }, isRoot);
      };
      var child$1 = function(scope, selector) {
        return child$2(scope, function(e) {
          return is$2(e, selector);
        });
      };
      var descendant = function(scope, selector) {
        return one(selector, scope);
      };
      var closest$1 = function(scope, selector, isRoot) {
        var is2 = function(element, selector2) {
          return is$2(element, selector2);
        };
        return ClosestOrAncestor(is2, ancestor$1, scope, selector, isRoot);
      };
      var rawSet = function(dom, key2, value2) {
        if (isString(value2) || isBoolean(value2) || isNumber(value2)) {
          dom.setAttribute(key2, value2 + "");
        } else {
          console.error("Invalid call to Attribute.set. Key ", key2, ":: Value ", value2, ":: Element ", dom);
          throw new Error("Attribute value was not simple");
        }
      };
      var set$2 = function(element, key2, value2) {
        rawSet(element.dom, key2, value2);
      };
      var setAll$1 = function(element, attrs) {
        var dom = element.dom;
        each$1(attrs, function(v, k) {
          rawSet(dom, k, v);
        });
      };
      var setOptions = function(element, attrs) {
        each$1(attrs, function(v, k) {
          v.fold(function() {
            remove$7(element, k);
          }, function(value2) {
            rawSet(element.dom, k, value2);
          });
        });
      };
      var get$b = function(element, key2) {
        var v = element.dom.getAttribute(key2);
        return v === null ? void 0 : v;
      };
      var getOpt = function(element, key2) {
        return Optional.from(get$b(element, key2));
      };
      var remove$7 = function(element, key2) {
        element.dom.removeAttribute(key2);
      };
      var clone$2 = function(element) {
        return foldl(element.dom.attributes, function(acc, attr) {
          acc[attr.name] = attr.value;
          return acc;
        }, {});
      };
      var is = function(lhs, rhs, comparator) {
        if (comparator === void 0) {
          comparator = tripleEquals;
        }
        return lhs.exists(function(left2) {
          return comparator(left2, rhs);
        });
      };
      var cat = function(arr) {
        var r2 = [];
        var push = function(x) {
          r2.push(x);
        };
        for (var i = 0; i < arr.length; i++) {
          arr[i].each(push);
        }
        return r2;
      };
      var lift2 = function(oa, ob, f) {
        return oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();
      };
      var bindFrom = function(a, f) {
        return a !== void 0 && a !== null ? f(a) : Optional.none();
      };
      var flatten = function(oot) {
        return oot.bind(identity);
      };
      var someIf = function(b, a) {
        return b ? Optional.some(a) : Optional.none();
      };
      var isSupported = function(dom) {
        return dom.style !== void 0 && isFunction(dom.style.getPropertyValue);
      };
      var internalSet = function(dom, property, value2) {
        if (!isString(value2)) {
          console.error("Invalid call to CSS.set. Property ", property, ":: Value ", value2, ":: Element ", dom);
          throw new Error("CSS value must be a string: " + value2);
        }
        if (isSupported(dom)) {
          dom.style.setProperty(property, value2);
        }
      };
      var internalRemove = function(dom, property) {
        if (isSupported(dom)) {
          dom.style.removeProperty(property);
        }
      };
      var set$1 = function(element, property, value2) {
        var dom = element.dom;
        internalSet(dom, property, value2);
      };
      var setAll = function(element, css2) {
        var dom = element.dom;
        each$1(css2, function(v, k) {
          internalSet(dom, k, v);
        });
      };
      var get$a = function(element, property) {
        var dom = element.dom;
        var styles2 = window.getComputedStyle(dom);
        var r2 = styles2.getPropertyValue(property);
        return r2 === "" && !inBody(element) ? getUnsafeProperty(dom, property) : r2;
      };
      var getUnsafeProperty = function(dom, property) {
        return isSupported(dom) ? dom.style.getPropertyValue(property) : "";
      };
      var getRaw$2 = function(element, property) {
        var dom = element.dom;
        var raw = getUnsafeProperty(dom, property);
        return Optional.from(raw).filter(function(r2) {
          return r2.length > 0;
        });
      };
      var remove$6 = function(element, property) {
        var dom = element.dom;
        internalRemove(dom, property);
        if (is(getOpt(element, "style").map(trim), "")) {
          remove$7(element, "style");
        }
      };
      var copy$2 = function(source, target) {
        var sourceDom = source.dom;
        var targetDom = target.dom;
        if (isSupported(sourceDom) && isSupported(targetDom)) {
          targetDom.style.cssText = sourceDom.style.cssText;
        }
      };
      var getAttrValue = function(cell2, name2, fallback2) {
        if (fallback2 === void 0) {
          fallback2 = 0;
        }
        return getOpt(cell2, name2).map(function(value2) {
          return parseInt(value2, 10);
        }).getOr(fallback2);
      };
      var getSpan = function(cell2, type2) {
        return getAttrValue(cell2, type2, 1);
      };
      var hasColspan = function(cellOrCol) {
        if (isTag("col")(cellOrCol)) {
          return getAttrValue(cellOrCol, "span", 1) > 1;
        } else {
          return getSpan(cellOrCol, "colspan") > 1;
        }
      };
      var hasRowspan = function(cell2) {
        return getSpan(cell2, "rowspan") > 1;
      };
      var getCssValue = function(element, property) {
        return parseInt(get$a(element, property), 10);
      };
      var minWidth = constant(10);
      var minHeight = constant(10);
      var firstLayer = function(scope, selector) {
        return filterFirstLayer(scope, selector, always);
      };
      var filterFirstLayer = function(scope, selector, predicate) {
        return bind$2(children$3(scope), function(x) {
          if (is$2(x, selector)) {
            return predicate(x) ? [x] : [];
          } else {
            return filterFirstLayer(x, selector, predicate);
          }
        });
      };
      var lookup = function(tags, element, isRoot) {
        if (isRoot === void 0) {
          isRoot = never;
        }
        if (isRoot(element)) {
          return Optional.none();
        }
        if (contains$2(tags, name(element))) {
          return Optional.some(element);
        }
        var isRootOrUpperTable = function(elm) {
          return is$2(elm, "table") || isRoot(elm);
        };
        return ancestor$1(element, tags.join(","), isRootOrUpperTable);
      };
      var cell = function(element, isRoot) {
        return lookup([
          "td",
          "th"
        ], element, isRoot);
      };
      var cells$1 = function(ancestor2) {
        return firstLayer(ancestor2, "th,td");
      };
      var columns$1 = function(ancestor2) {
        if (is$2(ancestor2, "colgroup")) {
          return children$1(ancestor2, "col");
        } else {
          return bind$2(columnGroups(ancestor2), function(columnGroup) {
            return children$1(columnGroup, "col");
          });
        }
      };
      var table = function(element, isRoot) {
        return closest$1(element, "table", isRoot);
      };
      var rows$1 = function(ancestor2) {
        return firstLayer(ancestor2, "tr");
      };
      var columnGroups = function(ancestor2) {
        return table(ancestor2).fold(constant([]), function(table2) {
          return children$1(table2, "colgroup");
        });
      };
      var fromRowsOrColGroups = function(elems, getSection) {
        return map$1(elems, function(row2) {
          if (name(row2) === "colgroup") {
            var cells2 = map$1(columns$1(row2), function(column) {
              var colspan = getAttrValue(column, "span", 1);
              return detail(column, 1, colspan);
            });
            return rowdetail(row2, cells2, "colgroup");
          } else {
            var cells2 = map$1(cells$1(row2), function(cell2) {
              var rowspan = getAttrValue(cell2, "rowspan", 1);
              var colspan = getAttrValue(cell2, "colspan", 1);
              return detail(cell2, rowspan, colspan);
            });
            return rowdetail(row2, cells2, getSection(row2));
          }
        });
      };
      var getParentSection = function(group) {
        return parent(group).map(function(parent2) {
          var parentName = name(parent2);
          return isValidSection(parentName) ? parentName : "tbody";
        }).getOr("tbody");
      };
      var fromTable$1 = function(table2) {
        var rows2 = rows$1(table2);
        var columnGroups$1 = columnGroups(table2);
        var elems = __spreadArray(__spreadArray([], columnGroups$1, true), rows2, true);
        return fromRowsOrColGroups(elems, getParentSection);
      };
      var fromPastedRows = function(elems, section2) {
        return fromRowsOrColGroups(elems, function() {
          return section2;
        });
      };
      var addCells = function(gridRow, index, cells2) {
        var existingCells = gridRow.cells;
        var before2 = existingCells.slice(0, index);
        var after2 = existingCells.slice(index);
        var newCells = before2.concat(cells2).concat(after2);
        return setCells(gridRow, newCells);
      };
      var addCell = function(gridRow, index, cell2) {
        return addCells(gridRow, index, [cell2]);
      };
      var mutateCell = function(gridRow, index, cell2) {
        var cells2 = gridRow.cells;
        cells2[index] = cell2;
      };
      var setCells = function(gridRow, cells2) {
        return rowcells(gridRow.element, cells2, gridRow.section, gridRow.isNew);
      };
      var mapCells = function(gridRow, f) {
        var cells2 = gridRow.cells;
        var r2 = map$1(cells2, f);
        return rowcells(gridRow.element, r2, gridRow.section, gridRow.isNew);
      };
      var getCell = function(gridRow, index) {
        return gridRow.cells[index];
      };
      var getCellElement = function(gridRow, index) {
        return getCell(gridRow, index).element;
      };
      var cellLength = function(gridRow) {
        return gridRow.cells.length;
      };
      var extractGridDetails = function(grid2) {
        var result = partition(grid2, function(row2) {
          return row2.section === "colgroup";
        });
        return {
          rows: result.fail,
          cols: result.pass
        };
      };
      var clone$1 = function(gridRow, cloneRow2, cloneCell) {
        var newCells = map$1(gridRow.cells, cloneCell);
        return rowcells(cloneRow2(gridRow.element), newCells, gridRow.section, true);
      };
      var LOCKED_COL_ATTR = "data-snooker-locked-cols";
      var getLockedColumnsFromTable = function(table2) {
        return getOpt(table2, LOCKED_COL_ATTR).bind(function(lockedColStr) {
          return Optional.from(lockedColStr.match(/\d+/g));
        }).map(function(lockedCols) {
          return mapToObject(lockedCols, always);
        });
      };
      var getLockedColumnsFromGrid = function(grid2) {
        var locked = foldl(extractGridDetails(grid2).rows, function(acc, row2) {
          each$2(row2.cells, function(cell2, idx) {
            if (cell2.isLocked) {
              acc[idx] = true;
            }
          });
          return acc;
        }, {});
        var lockedArr = mapToArray(locked, function(_val, key2) {
          return parseInt(key2, 10);
        });
        return sort$1(lockedArr);
      };
      var key = function(row2, column) {
        return row2 + "," + column;
      };
      var getAt = function(warehouse, row2, column) {
        return Optional.from(warehouse.access[key(row2, column)]);
      };
      var findItem = function(warehouse, item, comparator) {
        var filtered = filterItems(warehouse, function(detail2) {
          return comparator(item, detail2.element);
        });
        return filtered.length > 0 ? Optional.some(filtered[0]) : Optional.none();
      };
      var filterItems = function(warehouse, predicate) {
        var all2 = bind$2(warehouse.all, function(r2) {
          return r2.cells;
        });
        return filter$2(all2, predicate);
      };
      var generateColumns = function(rowData) {
        var columnsGroup = {};
        var index = 0;
        each$2(rowData.cells, function(column) {
          var colspan = column.colspan;
          range$1(colspan, function(columnIndex) {
            var colIndex = index + columnIndex;
            columnsGroup[colIndex] = columnext(column.element, colspan, colIndex);
          });
          index += colspan;
        });
        return columnsGroup;
      };
      var generate$1 = function(list) {
        var access = {};
        var cells2 = [];
        var tableOpt = head(list).map(function(rowData) {
          return rowData.element;
        }).bind(table);
        var lockedColumns = tableOpt.bind(getLockedColumnsFromTable).getOr({});
        var maxRows = 0;
        var maxColumns = 0;
        var rowCount = 0;
        var _a = partition(list, function(rowData) {
          return rowData.section === "colgroup";
        }), colgroupRows = _a.pass, rows2 = _a.fail;
        each$2(rows2, function(rowData) {
          var currentRow = [];
          each$2(rowData.cells, function(rowCell) {
            var start = 0;
            while (access[key(rowCount, start)] !== void 0) {
              start++;
            }
            var isLocked = hasNonNullableKey(lockedColumns, start.toString());
            var current = extended(rowCell.element, rowCell.rowspan, rowCell.colspan, rowCount, start, isLocked);
            for (var occupiedColumnPosition = 0; occupiedColumnPosition < rowCell.colspan; occupiedColumnPosition++) {
              for (var occupiedRowPosition = 0; occupiedRowPosition < rowCell.rowspan; occupiedRowPosition++) {
                var rowPosition = rowCount + occupiedRowPosition;
                var columnPosition = start + occupiedColumnPosition;
                var newpos = key(rowPosition, columnPosition);
                access[newpos] = current;
                maxColumns = Math.max(maxColumns, columnPosition + 1);
              }
            }
            currentRow.push(current);
          });
          maxRows++;
          cells2.push(rowdetail(rowData.element, currentRow, rowData.section));
          rowCount++;
        });
        var _b = last$2(colgroupRows).map(function(rowData) {
          var columns3 = generateColumns(rowData);
          var colgroup$1 = colgroup(rowData.element, values(columns3));
          return {
            colgroups: [colgroup$1],
            columns: columns3
          };
        }).getOrThunk(function() {
          return {
            colgroups: [],
            columns: {}
          };
        }), columns2 = _b.columns, colgroups = _b.colgroups;
        var grid$1 = grid(maxRows, maxColumns);
        return {
          grid: grid$1,
          access,
          all: cells2,
          columns: columns2,
          colgroups
        };
      };
      var fromTable = function(table2) {
        var list = fromTable$1(table2);
        return generate$1(list);
      };
      var justCells = function(warehouse) {
        return bind$2(warehouse.all, function(w) {
          return w.cells;
        });
      };
      var justColumns = function(warehouse) {
        return values(warehouse.columns);
      };
      var hasColumns = function(warehouse) {
        return keys(warehouse.columns).length > 0;
      };
      var getColumnAt = function(warehouse, columnIndex) {
        return Optional.from(warehouse.columns[columnIndex]);
      };
      var Warehouse = {
        fromTable,
        generate: generate$1,
        getAt,
        findItem,
        filterItems,
        justCells,
        justColumns,
        hasColumns,
        getColumnAt
      };
      var inSelection = function(bounds2, detail2) {
        var leftEdge = detail2.column;
        var rightEdge = detail2.column + detail2.colspan - 1;
        var topEdge = detail2.row;
        var bottomEdge = detail2.row + detail2.rowspan - 1;
        return leftEdge <= bounds2.finishCol && rightEdge >= bounds2.startCol && (topEdge <= bounds2.finishRow && bottomEdge >= bounds2.startRow);
      };
      var isWithin = function(bounds2, detail2) {
        return detail2.column >= bounds2.startCol && detail2.column + detail2.colspan - 1 <= bounds2.finishCol && detail2.row >= bounds2.startRow && detail2.row + detail2.rowspan - 1 <= bounds2.finishRow;
      };
      var isRectangular = function(warehouse, bounds2) {
        var isRect = true;
        var detailIsWithin = curry(isWithin, bounds2);
        for (var i = bounds2.startRow; i <= bounds2.finishRow; i++) {
          for (var j = bounds2.startCol; j <= bounds2.finishCol; j++) {
            isRect = isRect && Warehouse.getAt(warehouse, i, j).exists(detailIsWithin);
          }
        }
        return isRect ? Optional.some(bounds2) : Optional.none();
      };
      var getBounds = function(detailA, detailB) {
        return bounds(Math.min(detailA.row, detailB.row), Math.min(detailA.column, detailB.column), Math.max(detailA.row + detailA.rowspan - 1, detailB.row + detailB.rowspan - 1), Math.max(detailA.column + detailA.colspan - 1, detailB.column + detailB.colspan - 1));
      };
      var getAnyBox = function(warehouse, startCell, finishCell) {
        var startCoords = Warehouse.findItem(warehouse, startCell, eq$1);
        var finishCoords = Warehouse.findItem(warehouse, finishCell, eq$1);
        return startCoords.bind(function(sc) {
          return finishCoords.map(function(fc) {
            return getBounds(sc, fc);
          });
        });
      };
      var getBox$1 = function(warehouse, startCell, finishCell) {
        return getAnyBox(warehouse, startCell, finishCell).bind(function(bounds2) {
          return isRectangular(warehouse, bounds2);
        });
      };
      var moveBy$1 = function(warehouse, cell2, row2, column) {
        return Warehouse.findItem(warehouse, cell2, eq$1).bind(function(detail2) {
          var startRow = row2 > 0 ? detail2.row + detail2.rowspan - 1 : detail2.row;
          var startCol = column > 0 ? detail2.column + detail2.colspan - 1 : detail2.column;
          var dest = Warehouse.getAt(warehouse, startRow + row2, startCol + column);
          return dest.map(function(d) {
            return d.element;
          });
        });
      };
      var intercepts$1 = function(warehouse, start, finish) {
        return getAnyBox(warehouse, start, finish).map(function(bounds2) {
          var inside = Warehouse.filterItems(warehouse, curry(inSelection, bounds2));
          return map$1(inside, function(detail2) {
            return detail2.element;
          });
        });
      };
      var parentCell = function(warehouse, innerCell) {
        var isContainedBy = function(c1, c2) {
          return contains(c2, c1);
        };
        return Warehouse.findItem(warehouse, innerCell, isContainedBy).map(function(detail2) {
          return detail2.element;
        });
      };
      var moveBy = function(cell2, deltaRow, deltaColumn) {
        return table(cell2).bind(function(table2) {
          var warehouse = getWarehouse(table2);
          return moveBy$1(warehouse, cell2, deltaRow, deltaColumn);
        });
      };
      var intercepts = function(table2, first2, last2) {
        var warehouse = getWarehouse(table2);
        return intercepts$1(warehouse, first2, last2);
      };
      var nestedIntercepts = function(table2, first2, firstTable, last2, lastTable) {
        var warehouse = getWarehouse(table2);
        var optStartCell = eq$1(table2, firstTable) ? Optional.some(first2) : parentCell(warehouse, first2);
        var optLastCell = eq$1(table2, lastTable) ? Optional.some(last2) : parentCell(warehouse, last2);
        return optStartCell.bind(function(startCell) {
          return optLastCell.bind(function(lastCell) {
            return intercepts$1(warehouse, startCell, lastCell);
          });
        });
      };
      var getBox = function(table2, first2, last2) {
        var warehouse = getWarehouse(table2);
        return getBox$1(warehouse, first2, last2);
      };
      var getWarehouse = Warehouse.fromTable;
      var before$4 = function(marker, element) {
        var parent$1 = parent(marker);
        parent$1.each(function(v) {
          v.dom.insertBefore(element.dom, marker.dom);
        });
      };
      var after$5 = function(marker, element) {
        var sibling = nextSibling(marker);
        sibling.fold(function() {
          var parent$1 = parent(marker);
          parent$1.each(function(v) {
            append$1(v, element);
          });
        }, function(v) {
          before$4(v, element);
        });
      };
      var prepend = function(parent2, element) {
        var firstChild$1 = firstChild(parent2);
        firstChild$1.fold(function() {
          append$1(parent2, element);
        }, function(v) {
          parent2.dom.insertBefore(element.dom, v.dom);
        });
      };
      var append$1 = function(parent2, element) {
        parent2.dom.appendChild(element.dom);
      };
      var appendAt = function(parent2, element, index) {
        child$3(parent2, index).fold(function() {
          append$1(parent2, element);
        }, function(v) {
          before$4(v, element);
        });
      };
      var wrap = function(element, wrapper) {
        before$4(element, wrapper);
        append$1(wrapper, element);
      };
      var before$3 = function(marker, elements) {
        each$2(elements, function(x) {
          before$4(marker, x);
        });
      };
      var after$4 = function(marker, elements) {
        each$2(elements, function(x, i) {
          var e = i === 0 ? marker : elements[i - 1];
          after$5(e, x);
        });
      };
      var append = function(parent2, elements) {
        each$2(elements, function(x) {
          append$1(parent2, x);
        });
      };
      var empty = function(element) {
        element.dom.textContent = "";
        each$2(children$3(element), function(rogue) {
          remove$5(rogue);
        });
      };
      var remove$5 = function(element) {
        var dom = element.dom;
        if (dom.parentNode !== null) {
          dom.parentNode.removeChild(dom);
        }
      };
      var unwrap = function(wrapper) {
        var children2 = children$3(wrapper);
        if (children2.length > 0) {
          before$3(wrapper, children2);
        }
        remove$5(wrapper);
      };
      var NodeValue = function(is2, name2) {
        var get2 = function(element) {
          if (!is2(element)) {
            throw new Error("Can only get " + name2 + " value of a " + name2 + " node");
          }
          return getOption2(element).getOr("");
        };
        var getOption2 = function(element) {
          return is2(element) ? Optional.from(element.dom.nodeValue) : Optional.none();
        };
        var set2 = function(element, value2) {
          if (!is2(element)) {
            throw new Error("Can only set raw " + name2 + " value of a " + name2 + " node");
          }
          element.dom.nodeValue = value2;
        };
        return {
          get: get2,
          getOption: getOption2,
          set: set2
        };
      };
      var api$2 = NodeValue(isText, "text");
      var get$9 = function(element) {
        return api$2.get(element);
      };
      var getOption = function(element) {
        return api$2.getOption(element);
      };
      var set = function(element, value2) {
        return api$2.set(element, value2);
      };
      var TagBoundaries = [
        "body",
        "p",
        "div",
        "article",
        "aside",
        "figcaption",
        "figure",
        "footer",
        "header",
        "nav",
        "section",
        "ol",
        "ul",
        "li",
        "table",
        "thead",
        "tbody",
        "tfoot",
        "caption",
        "tr",
        "td",
        "th",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "blockquote",
        "pre",
        "address"
      ];
      function DomUniverse() {
        var clone2 = function(element) {
          return SugarElement.fromDom(element.dom.cloneNode(false));
        };
        var document2 = function(element) {
          return documentOrOwner(element).dom;
        };
        var isBoundary = function(element) {
          if (!isElement(element)) {
            return false;
          }
          if (name(element) === "body") {
            return true;
          }
          return contains$2(TagBoundaries, name(element));
        };
        var isEmptyTag2 = function(element) {
          if (!isElement(element)) {
            return false;
          }
          return contains$2([
            "br",
            "img",
            "hr",
            "input"
          ], name(element));
        };
        var isNonEditable = function(element) {
          return isElement(element) && get$b(element, "contenteditable") === "false";
        };
        var comparePosition = function(element, other) {
          return element.dom.compareDocumentPosition(other.dom);
        };
        var copyAttributesTo = function(source, destination) {
          var as = clone$2(source);
          setAll$1(destination, as);
        };
        var isSpecial = function(element) {
          var tag = name(element);
          return contains$2([
            "script",
            "noscript",
            "iframe",
            "noframes",
            "noembed",
            "title",
            "style",
            "textarea",
            "xmp"
          ], tag);
        };
        var getLanguage = function(element) {
          return isElement(element) ? getOpt(element, "lang") : Optional.none();
        };
        return {
          up: constant({
            selector: ancestor$1,
            closest: closest$1,
            predicate: ancestor$2,
            all: parents
          }),
          down: constant({
            selector: descendants,
            predicate: descendants$1
          }),
          styles: constant({
            get: get$a,
            getRaw: getRaw$2,
            set: set$1,
            remove: remove$6
          }),
          attrs: constant({
            get: get$b,
            set: set$2,
            remove: remove$7,
            copyTo: copyAttributesTo
          }),
          insert: constant({
            before: before$4,
            after: after$5,
            afterAll: after$4,
            append: append$1,
            appendAll: append,
            prepend,
            wrap
          }),
          remove: constant({
            unwrap,
            remove: remove$5
          }),
          create: constant({
            nu: SugarElement.fromTag,
            clone: clone2,
            text: SugarElement.fromText
          }),
          query: constant({
            comparePosition,
            prevSibling,
            nextSibling
          }),
          property: constant({
            children: children$3,
            name,
            parent,
            document: document2,
            isText,
            isComment,
            isElement,
            isSpecial,
            getLanguage,
            getText: get$9,
            setText: set,
            isBoundary,
            isEmptyTag: isEmptyTag2,
            isNonEditable
          }),
          eq: eq$1,
          is: is$1
        };
      }
      var all = function(universe2, look, elements, f) {
        var head2 = elements[0];
        var tail = elements.slice(1);
        return f(universe2, look, head2, tail);
      };
      var oneAll = function(universe2, look, elements) {
        return elements.length > 0 ? all(universe2, look, elements, unsafeOne) : Optional.none();
      };
      var unsafeOne = function(universe2, look, head2, tail) {
        var start = look(universe2, head2);
        return foldr(tail, function(b, a) {
          var current = look(universe2, a);
          return commonElement(universe2, b, current);
        }, start);
      };
      var commonElement = function(universe2, start, end) {
        return start.bind(function(s) {
          return end.filter(curry(universe2.eq, s));
        });
      };
      var eq = function(universe2, item) {
        return curry(universe2.eq, item);
      };
      var ancestors$2 = function(universe2, start, end, isRoot) {
        if (isRoot === void 0) {
          isRoot = never;
        }
        var ps1 = [start].concat(universe2.up().all(start));
        var ps2 = [end].concat(universe2.up().all(end));
        var prune2 = function(path) {
          var index = findIndex(path, isRoot);
          return index.fold(function() {
            return path;
          }, function(ind) {
            return path.slice(0, ind + 1);
          });
        };
        var pruned1 = prune2(ps1);
        var pruned2 = prune2(ps2);
        var shared = find$1(pruned1, function(x) {
          return exists(pruned2, eq(universe2, x));
        });
        return {
          firstpath: pruned1,
          secondpath: pruned2,
          shared
        };
      };
      var sharedOne$1 = oneAll;
      var ancestors$1 = ancestors$2;
      var universe$3 = DomUniverse();
      var sharedOne = function(look, elements) {
        return sharedOne$1(universe$3, function(_universe, element) {
          return look(element);
        }, elements);
      };
      var ancestors = function(start, finish, isRoot) {
        return ancestors$1(universe$3, start, finish, isRoot);
      };
      var lookupTable = function(container) {
        return ancestor$1(container, "table");
      };
      var identify = function(start, finish, isRoot) {
        var getIsRoot2 = function(rootTable) {
          return function(element) {
            return isRoot !== void 0 && isRoot(element) || eq$1(element, rootTable);
          };
        };
        if (eq$1(start, finish)) {
          return Optional.some({
            boxes: Optional.some([start]),
            start,
            finish
          });
        } else {
          return lookupTable(start).bind(function(startTable) {
            return lookupTable(finish).bind(function(finishTable) {
              if (eq$1(startTable, finishTable)) {
                return Optional.some({
                  boxes: intercepts(startTable, start, finish),
                  start,
                  finish
                });
              } else if (contains(startTable, finishTable)) {
                var ancestorCells = ancestors$3(finish, "td,th", getIsRoot2(startTable));
                var finishCell = ancestorCells.length > 0 ? ancestorCells[ancestorCells.length - 1] : finish;
                return Optional.some({
                  boxes: nestedIntercepts(startTable, start, startTable, finish, finishTable),
                  start,
                  finish: finishCell
                });
              } else if (contains(finishTable, startTable)) {
                var ancestorCells = ancestors$3(start, "td,th", getIsRoot2(finishTable));
                var startCell = ancestorCells.length > 0 ? ancestorCells[ancestorCells.length - 1] : start;
                return Optional.some({
                  boxes: nestedIntercepts(finishTable, start, startTable, finish, finishTable),
                  start,
                  finish: startCell
                });
              } else {
                return ancestors(start, finish).shared.bind(function(lca) {
                  return closest$1(lca, "table", isRoot).bind(function(lcaTable) {
                    var finishAncestorCells = ancestors$3(finish, "td,th", getIsRoot2(lcaTable));
                    var finishCell2 = finishAncestorCells.length > 0 ? finishAncestorCells[finishAncestorCells.length - 1] : finish;
                    var startAncestorCells = ancestors$3(start, "td,th", getIsRoot2(lcaTable));
                    var startCell2 = startAncestorCells.length > 0 ? startAncestorCells[startAncestorCells.length - 1] : start;
                    return Optional.some({
                      boxes: nestedIntercepts(lcaTable, start, startTable, finish, finishTable),
                      start: startCell2,
                      finish: finishCell2
                    });
                  });
                });
              }
            });
          });
        }
      };
      var retrieve$1 = function(container, selector) {
        var sels = descendants(container, selector);
        return sels.length > 0 ? Optional.some(sels) : Optional.none();
      };
      var getLast = function(boxes, lastSelectedSelector) {
        return find$1(boxes, function(box) {
          return is$2(box, lastSelectedSelector);
        });
      };
      var getEdges = function(container, firstSelectedSelector, lastSelectedSelector) {
        return descendant(container, firstSelectedSelector).bind(function(first2) {
          return descendant(container, lastSelectedSelector).bind(function(last2) {
            return sharedOne(lookupTable, [
              first2,
              last2
            ]).map(function(table2) {
              return {
                first: first2,
                last: last2,
                table: table2
              };
            });
          });
        });
      };
      var expandTo = function(finish, firstSelectedSelector) {
        return ancestor$1(finish, "table").bind(function(table2) {
          return descendant(table2, firstSelectedSelector).bind(function(start) {
            return identify(start, finish).bind(function(identified) {
              return identified.boxes.map(function(boxes) {
                return {
                  boxes,
                  start: identified.start,
                  finish: identified.finish
                };
              });
            });
          });
        });
      };
      var shiftSelection = function(boxes, deltaRow, deltaColumn, firstSelectedSelector, lastSelectedSelector) {
        return getLast(boxes, lastSelectedSelector).bind(function(last2) {
          return moveBy(last2, deltaRow, deltaColumn).bind(function(finish) {
            return expandTo(finish, firstSelectedSelector);
          });
        });
      };
      var retrieve = function(container, selector) {
        return retrieve$1(container, selector);
      };
      var retrieveBox = function(container, firstSelectedSelector, lastSelectedSelector) {
        return getEdges(container, firstSelectedSelector, lastSelectedSelector).bind(function(edges) {
          var isRoot = function(ancestor2) {
            return eq$1(container, ancestor2);
          };
          var sectionSelector = "thead,tfoot,tbody,table";
          var firstAncestor = ancestor$1(edges.first, sectionSelector, isRoot);
          var lastAncestor = ancestor$1(edges.last, sectionSelector, isRoot);
          return firstAncestor.bind(function(fA) {
            return lastAncestor.bind(function(lA) {
              return eq$1(fA, lA) ? getBox(edges.table, edges.first, edges.last) : Optional.none();
            });
          });
        });
      };
      var generate = function(cases) {
        if (!isArray(cases)) {
          throw new Error("cases must be an array");
        }
        if (cases.length === 0) {
          throw new Error("there must be at least one case");
        }
        var constructors = [];
        var adt2 = {};
        each$2(cases, function(acase, count) {
          var keys$1 = keys(acase);
          if (keys$1.length !== 1) {
            throw new Error("one and only one name per case");
          }
          var key2 = keys$1[0];
          var value2 = acase[key2];
          if (adt2[key2] !== void 0) {
            throw new Error("duplicate key detected:" + key2);
          } else if (key2 === "cata") {
            throw new Error("cannot have a case named cata (sorry)");
          } else if (!isArray(value2)) {
            throw new Error("case arguments must be an array");
          }
          constructors.push(key2);
          adt2[key2] = function() {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
              args[_i] = arguments[_i];
            }
            var argLength = args.length;
            if (argLength !== value2.length) {
              throw new Error("Wrong number of arguments to case " + key2 + ". Expected " + value2.length + " (" + value2 + "), got " + argLength);
            }
            var match = function(branches) {
              var branchKeys = keys(branches);
              if (constructors.length !== branchKeys.length) {
                throw new Error("Wrong number of arguments to match. Expected: " + constructors.join(",") + "\nActual: " + branchKeys.join(","));
              }
              var allReqd = forall(constructors, function(reqKey) {
                return contains$2(branchKeys, reqKey);
              });
              if (!allReqd) {
                throw new Error("Not all branches were specified when using match. Specified: " + branchKeys.join(", ") + "\nRequired: " + constructors.join(", "));
              }
              return branches[key2].apply(null, args);
            };
            return {
              fold: function() {
                var foldArgs = [];
                for (var _i2 = 0; _i2 < arguments.length; _i2++) {
                  foldArgs[_i2] = arguments[_i2];
                }
                if (foldArgs.length !== cases.length) {
                  throw new Error("Wrong number of arguments to fold. Expected " + cases.length + ", got " + foldArgs.length);
                }
                var target = foldArgs[count];
                return target.apply(null, args);
              },
              match,
              log: function(label) {
                console.log(label, {
                  constructors,
                  constructor: key2,
                  params: args
                });
              }
            };
          };
        });
        return adt2;
      };
      var Adt = { generate };
      var type = Adt.generate([
        { none: [] },
        { multiple: ["elements"] },
        { single: ["element"] }
      ]);
      var cata$2 = function(subject, onNone, onMultiple, onSingle) {
        return subject.fold(onNone, onMultiple, onSingle);
      };
      var none$1 = type.none;
      var multiple = type.multiple;
      var single = type.single;
      var Selections = function(lazyRoot, getStart2, selectedSelector) {
        var get2 = function() {
          return retrieve(lazyRoot(), selectedSelector).fold(function() {
            return getStart2().fold(none$1, single);
          }, function(cells2) {
            return multiple(cells2);
          });
        };
        return { get: get2 };
      };
      var global$3 = tinymce.util.Tools.resolve("tinymce.PluginManager");
      var clone = function(original, isDeep) {
        return SugarElement.fromDom(original.dom.cloneNode(isDeep));
      };
      var shallow = function(original) {
        return clone(original, false);
      };
      var deep = function(original) {
        return clone(original, true);
      };
      var shallowAs = function(original, tag) {
        var nu2 = SugarElement.fromTag(tag);
        var attributes = clone$2(original);
        setAll$1(nu2, attributes);
        return nu2;
      };
      var copy$1 = function(original, tag) {
        var nu2 = shallowAs(original, tag);
        var cloneChildren = children$3(deep(original));
        append(nu2, cloneChildren);
        return nu2;
      };
      var mutate$1 = function(original, tag) {
        var nu2 = shallowAs(original, tag);
        before$4(original, nu2);
        var children2 = children$3(original);
        append(nu2, children2);
        remove$5(original);
        return nu2;
      };
      var Dimension = function(name2, getOffset) {
        var set2 = function(element, h) {
          if (!isNumber(h) && !h.match(/^[0-9]+$/)) {
            throw new Error(name2 + ".set accepts only positive integer values. Value was " + h);
          }
          var dom = element.dom;
          if (isSupported(dom)) {
            dom.style[name2] = h + "px";
          }
        };
        var get2 = function(element) {
          var r2 = getOffset(element);
          if (r2 <= 0 || r2 === null) {
            var css2 = get$a(element, name2);
            return parseFloat(css2) || 0;
          }
          return r2;
        };
        var getOuter2 = get2;
        var aggregate = function(element, properties) {
          return foldl(properties, function(acc, property) {
            var val = get$a(element, property);
            var value2 = val === void 0 ? 0 : parseInt(val, 10);
            return isNaN(value2) ? acc : acc + value2;
          }, 0);
        };
        var max = function(element, value2, properties) {
          var cumulativeInclusions = aggregate(element, properties);
          var absoluteMax = value2 > cumulativeInclusions ? value2 - cumulativeInclusions : 0;
          return absoluteMax;
        };
        return {
          set: set2,
          get: get2,
          getOuter: getOuter2,
          aggregate,
          max
        };
      };
      var needManualCalc = function() {
        var browser = detect$3().browser;
        return browser.isIE() || browser.isEdge();
      };
      var toNumber = function(px, fallback2) {
        return toFloat(px).getOr(fallback2);
      };
      var getProp = function(element, name2, fallback2) {
        return toNumber(get$a(element, name2), fallback2);
      };
      var getBoxSizing = function(element) {
        return get$a(element, "box-sizing");
      };
      var calcContentBoxSize = function(element, size2, upper, lower) {
        var paddingUpper = getProp(element, "padding-" + upper, 0);
        var paddingLower = getProp(element, "padding-" + lower, 0);
        var borderUpper = getProp(element, "border-" + upper + "-width", 0);
        var borderLower = getProp(element, "border-" + lower + "-width", 0);
        return size2 - paddingUpper - paddingLower - borderUpper - borderLower;
      };
      var getCalculatedHeight = function(element, boxSizing) {
        var dom = element.dom;
        var height2 = dom.getBoundingClientRect().height || dom.offsetHeight;
        return boxSizing === "border-box" ? height2 : calcContentBoxSize(element, height2, "top", "bottom");
      };
      var getCalculatedWidth = function(element, boxSizing) {
        var dom = element.dom;
        var width2 = dom.getBoundingClientRect().width || dom.offsetWidth;
        return boxSizing === "border-box" ? width2 : calcContentBoxSize(element, width2, "left", "right");
      };
      var getHeight$1 = function(element) {
        return needManualCalc() ? getCalculatedHeight(element, getBoxSizing(element)) : getProp(element, "height", element.dom.offsetHeight);
      };
      var getWidth = function(element) {
        return needManualCalc() ? getCalculatedWidth(element, getBoxSizing(element)) : getProp(element, "width", element.dom.offsetWidth);
      };
      var getInnerWidth = function(element) {
        return getCalculatedWidth(element, "content-box");
      };
      var api$1 = Dimension("width", function(element) {
        return element.dom.offsetWidth;
      });
      var get$8 = function(element) {
        return api$1.get(element);
      };
      var getOuter$2 = function(element) {
        return api$1.getOuter(element);
      };
      var getInner = getInnerWidth;
      var getRuntime$1 = getWidth;
      var columns = function(warehouse, isValidCell) {
        if (isValidCell === void 0) {
          isValidCell = always;
        }
        var grid2 = warehouse.grid;
        var cols = range$1(grid2.columns, identity);
        var rowsArr = range$1(grid2.rows, identity);
        return map$1(cols, function(col2) {
          var getBlock = function() {
            return bind$2(rowsArr, function(r2) {
              return Warehouse.getAt(warehouse, r2, col2).filter(function(detail2) {
                return detail2.column === col2;
              }).toArray();
            });
          };
          var isValid = function(detail2) {
            return detail2.colspan === 1 && isValidCell(detail2.element);
          };
          var getFallback = function() {
            return Warehouse.getAt(warehouse, 0, col2);
          };
          return decide(getBlock, isValid, getFallback);
        });
      };
      var decide = function(getBlock, isValid, getFallback) {
        var inBlock = getBlock();
        var validInBlock = find$1(inBlock, isValid);
        var detailOption = validInBlock.orThunk(function() {
          return Optional.from(inBlock[0]).orThunk(getFallback);
        });
        return detailOption.map(function(detail2) {
          return detail2.element;
        });
      };
      var rows = function(warehouse) {
        var grid2 = warehouse.grid;
        var rowsArr = range$1(grid2.rows, identity);
        var cols = range$1(grid2.columns, identity);
        return map$1(rowsArr, function(row2) {
          var getBlock = function() {
            return bind$2(cols, function(c) {
              return Warehouse.getAt(warehouse, row2, c).filter(function(detail2) {
                return detail2.row === row2;
              }).fold(constant([]), function(detail2) {
                return [detail2];
              });
            });
          };
          var isSingle = function(detail2) {
            return detail2.rowspan === 1;
          };
          var getFallback = function() {
            return Warehouse.getAt(warehouse, row2, 0);
          };
          return decide(getBlock, isSingle, getFallback);
        });
      };
      var deduce = function(xs, index) {
        if (index < 0 || index >= xs.length - 1) {
          return Optional.none();
        }
        var current = xs[index].fold(function() {
          var rest = reverse(xs.slice(0, index));
          return findMap(rest, function(a, i) {
            return a.map(function(aa) {
              return {
                value: aa,
                delta: i + 1
              };
            });
          });
        }, function(c) {
          return Optional.some({
            value: c,
            delta: 0
          });
        });
        var next2 = xs[index + 1].fold(function() {
          var rest = xs.slice(index + 1);
          return findMap(rest, function(a, i) {
            return a.map(function(aa) {
              return {
                value: aa,
                delta: i + 1
              };
            });
          });
        }, function(n) {
          return Optional.some({
            value: n,
            delta: 1
          });
        });
        return current.bind(function(c) {
          return next2.map(function(n) {
            var extras = n.delta + c.delta;
            return Math.abs(n.value - c.value) / extras;
          });
        });
      };
      var onDirection = function(isLtr, isRtl) {
        return function(element) {
          return getDirection(element) === "rtl" ? isRtl : isLtr;
        };
      };
      var getDirection = function(element) {
        return get$a(element, "direction") === "rtl" ? "rtl" : "ltr";
      };
      var api = Dimension("height", function(element) {
        var dom = element.dom;
        return inBody(element) ? dom.getBoundingClientRect().height : dom.offsetHeight;
      });
      var get$7 = function(element) {
        return api.get(element);
      };
      var getOuter$1 = function(element) {
        return api.getOuter(element);
      };
      var getRuntime = getHeight$1;
      var r = function(left2, top) {
        var translate2 = function(x, y) {
          return r(left2 + x, top + y);
        };
        return {
          left: left2,
          top,
          translate: translate2
        };
      };
      var SugarPosition = r;
      var boxPosition = function(dom) {
        var box = dom.getBoundingClientRect();
        return SugarPosition(box.left, box.top);
      };
      var firstDefinedOrZero = function(a, b) {
        if (a !== void 0) {
          return a;
        } else {
          return b !== void 0 ? b : 0;
        }
      };
      var absolute = function(element) {
        var doc = element.dom.ownerDocument;
        var body2 = doc.body;
        var win = doc.defaultView;
        var html = doc.documentElement;
        if (body2 === element.dom) {
          return SugarPosition(body2.offsetLeft, body2.offsetTop);
        }
        var scrollTop = firstDefinedOrZero(win === null || win === void 0 ? void 0 : win.pageYOffset, html.scrollTop);
        var scrollLeft = firstDefinedOrZero(win === null || win === void 0 ? void 0 : win.pageXOffset, html.scrollLeft);
        var clientTop = firstDefinedOrZero(html.clientTop, body2.clientTop);
        var clientLeft = firstDefinedOrZero(html.clientLeft, body2.clientLeft);
        return viewport(element).translate(scrollLeft - clientLeft, scrollTop - clientTop);
      };
      var viewport = function(element) {
        var dom = element.dom;
        var doc = dom.ownerDocument;
        var body2 = doc.body;
        if (body2 === dom) {
          return SugarPosition(body2.offsetLeft, body2.offsetTop);
        }
        if (!inBody(element)) {
          return SugarPosition(0, 0);
        }
        return boxPosition(dom);
      };
      var rowInfo = function(row2, y) {
        return {
          row: row2,
          y
        };
      };
      var colInfo = function(col2, x) {
        return {
          col: col2,
          x
        };
      };
      var rtlEdge = function(cell2) {
        var pos = absolute(cell2);
        return pos.left + getOuter$2(cell2);
      };
      var ltrEdge = function(cell2) {
        return absolute(cell2).left;
      };
      var getLeftEdge = function(index, cell2) {
        return colInfo(index, ltrEdge(cell2));
      };
      var getRightEdge = function(index, cell2) {
        return colInfo(index, rtlEdge(cell2));
      };
      var getTop$1 = function(cell2) {
        return absolute(cell2).top;
      };
      var getTopEdge = function(index, cell2) {
        return rowInfo(index, getTop$1(cell2));
      };
      var getBottomEdge = function(index, cell2) {
        return rowInfo(index, getTop$1(cell2) + getOuter$1(cell2));
      };
      var findPositions = function(getInnerEdge, getOuterEdge, array) {
        if (array.length === 0) {
          return [];
        }
        var lines = map$1(array.slice(1), function(cellOption, index) {
          return cellOption.map(function(cell2) {
            return getInnerEdge(index, cell2);
          });
        });
        var lastLine = array[array.length - 1].map(function(cell2) {
          return getOuterEdge(array.length - 1, cell2);
        });
        return lines.concat([lastLine]);
      };
      var negate = function(step) {
        return -step;
      };
      var height = {
        delta: identity,
        positions: function(optElements) {
          return findPositions(getTopEdge, getBottomEdge, optElements);
        },
        edge: getTop$1
      };
      var ltr$1 = {
        delta: identity,
        edge: ltrEdge,
        positions: function(optElements) {
          return findPositions(getLeftEdge, getRightEdge, optElements);
        }
      };
      var rtl$1 = {
        delta: negate,
        edge: rtlEdge,
        positions: function(optElements) {
          return findPositions(getRightEdge, getLeftEdge, optElements);
        }
      };
      var detect$2 = onDirection(ltr$1, rtl$1);
      var width = {
        delta: function(amount, table2) {
          return detect$2(table2).delta(amount, table2);
        },
        positions: function(cols, table2) {
          return detect$2(table2).positions(cols, table2);
        },
        edge: function(cell2) {
          return detect$2(cell2).edge(cell2);
        }
      };
      var units = {
        unsupportedLength: [
          "em",
          "ex",
          "cap",
          "ch",
          "ic",
          "rem",
          "lh",
          "rlh",
          "vw",
          "vh",
          "vi",
          "vb",
          "vmin",
          "vmax",
          "cm",
          "mm",
          "Q",
          "in",
          "pc",
          "pt",
          "px"
        ],
        fixed: [
          "px",
          "pt"
        ],
        relative: ["%"],
        empty: [""]
      };
      var pattern = function() {
        var decimalDigits = "[0-9]+";
        var signedInteger = "[+-]?" + decimalDigits;
        var exponentPart = "[eE]" + signedInteger;
        var dot = "\\.";
        var opt = function(input) {
          return "(?:" + input + ")?";
        };
        var unsignedDecimalLiteral = [
          "Infinity",
          decimalDigits + dot + opt(decimalDigits) + opt(exponentPart),
          dot + decimalDigits + opt(exponentPart),
          decimalDigits + opt(exponentPart)
        ].join("|");
        var float = "[+-]?(?:" + unsignedDecimalLiteral + ")";
        return new RegExp("^(" + float + ")(.*)$");
      }();
      var isUnit = function(unit, accepted) {
        return exists(accepted, function(acc) {
          return exists(units[acc], function(check) {
            return unit === check;
          });
        });
      };
      var parse = function(input, accepted) {
        var match = Optional.from(pattern.exec(input));
        return match.bind(function(array) {
          var value2 = Number(array[1]);
          var unitRaw = array[2];
          if (isUnit(unitRaw, accepted)) {
            return Optional.some({
              value: value2,
              unit: unitRaw
            });
          } else {
            return Optional.none();
          }
        });
      };
      var rPercentageBasedSizeRegex = /(\d+(\.\d+)?)%/;
      var rPixelBasedSizeRegex = /(\d+(\.\d+)?)px|em/;
      var isCol$2 = isTag("col");
      var getPercentSize = function(elm, outerGetter, innerGetter) {
        var relativeParent = parentElement(elm).getOrThunk(function() {
          return getBody$1(owner(elm));
        });
        return outerGetter(elm) / innerGetter(relativeParent) * 100;
      };
      var setPixelWidth = function(cell2, amount) {
        set$1(cell2, "width", amount + "px");
      };
      var setPercentageWidth = function(cell2, amount) {
        set$1(cell2, "width", amount + "%");
      };
      var setHeight = function(cell2, amount) {
        set$1(cell2, "height", amount + "px");
      };
      var getHeightValue = function(cell2) {
        return getRuntime(cell2) + "px";
      };
      var convert = function(cell2, number, getter, setter) {
        var newSize = table(cell2).map(function(table2) {
          var total2 = getter(table2);
          return Math.floor(number / 100 * total2);
        }).getOr(number);
        setter(cell2, newSize);
        return newSize;
      };
      var normalizePixelSize = function(value2, cell2, getter, setter) {
        var number = parseFloat(value2);
        return endsWith(value2, "%") && name(cell2) !== "table" ? convert(cell2, number, getter, setter) : number;
      };
      var getTotalHeight = function(cell2) {
        var value2 = getHeightValue(cell2);
        if (!value2) {
          return get$7(cell2);
        }
        return normalizePixelSize(value2, cell2, get$7, setHeight);
      };
      var get$6 = function(cell2, type2, f) {
        var v = f(cell2);
        var span = getSpan(cell2, type2);
        return v / span;
      };
      var getRaw$1 = function(element, prop) {
        return getRaw$2(element, prop).orThunk(function() {
          return getOpt(element, prop).map(function(val) {
            return val + "px";
          });
        });
      };
      var getRawWidth$1 = function(element) {
        return getRaw$1(element, "width");
      };
      var getRawHeight = function(element) {
        return getRaw$1(element, "height");
      };
      var getPercentageWidth = function(cell2) {
        return getPercentSize(cell2, get$8, getInner);
      };
      var getPixelWidth$1 = function(cell2) {
        return isCol$2(cell2) ? get$8(cell2) : getRuntime$1(cell2);
      };
      var getHeight = function(cell2) {
        return get$6(cell2, "rowspan", getTotalHeight);
      };
      var getGenericWidth = function(cell2) {
        var width2 = getRawWidth$1(cell2);
        return width2.bind(function(w) {
          return parse(w, [
            "fixed",
            "relative",
            "empty"
          ]);
        });
      };
      var setGenericWidth = function(cell2, amount, unit) {
        set$1(cell2, "width", amount + unit);
      };
      var getPixelTableWidth = function(table2) {
        return get$8(table2) + "px";
      };
      var getPercentTableWidth = function(table2) {
        return getPercentSize(table2, get$8, getInner) + "%";
      };
      var isPercentSizing$1 = function(table2) {
        return getRawWidth$1(table2).exists(function(size2) {
          return rPercentageBasedSizeRegex.test(size2);
        });
      };
      var isPixelSizing$1 = function(table2) {
        return getRawWidth$1(table2).exists(function(size2) {
          return rPixelBasedSizeRegex.test(size2);
        });
      };
      var isNoneSizing$1 = function(table2) {
        return getRawWidth$1(table2).isNone();
      };
      var percentageBasedSizeRegex = constant(rPercentageBasedSizeRegex);
      var isCol$1 = isTag("col");
      var getRawW = function(cell2) {
        return getRawWidth$1(cell2).getOrThunk(function() {
          return getPixelWidth$1(cell2) + "px";
        });
      };
      var getRawH = function(cell2) {
        return getRawHeight(cell2).getOrThunk(function() {
          return getHeight(cell2) + "px";
        });
      };
      var justCols = function(warehouse) {
        return map$1(Warehouse.justColumns(warehouse), function(column) {
          return Optional.from(column.element);
        });
      };
      var isValidColumn = function(cell2) {
        var browser = detect$3().browser;
        var supportsColWidths = browser.isChrome() || browser.isFirefox();
        return isCol$1(cell2) ? supportsColWidths : true;
      };
      var getDimension = function(cellOpt, index, backups, filter2, getter, fallback2) {
        return cellOpt.filter(filter2).fold(function() {
          return fallback2(deduce(backups, index));
        }, function(cell2) {
          return getter(cell2);
        });
      };
      var getWidthFrom = function(warehouse, table2, getWidth2, fallback2) {
        var columnCells = columns(warehouse);
        var columns$12 = Warehouse.hasColumns(warehouse) ? justCols(warehouse) : columnCells;
        var backups = [Optional.some(width.edge(table2))].concat(map$1(width.positions(columnCells, table2), function(pos) {
          return pos.map(function(p) {
            return p.x;
          });
        }));
        var colFilter = not(hasColspan);
        return map$1(columns$12, function(cellOption, c) {
          return getDimension(cellOption, c, backups, colFilter, function(column) {
            if (isValidColumn(column)) {
              return getWidth2(column);
            } else {
              var cell2 = bindFrom(columnCells[c], identity);
              return getDimension(cell2, c, backups, colFilter, function(cell3) {
                return fallback2(Optional.some(get$8(cell3)));
              }, fallback2);
            }
          }, fallback2);
        });
      };
      var getDeduced = function(deduced) {
        return deduced.map(function(d) {
          return d + "px";
        }).getOr("");
      };
      var getRawWidths = function(warehouse, table2) {
        return getWidthFrom(warehouse, table2, getRawW, getDeduced);
      };
      var getPercentageWidths = function(warehouse, table2, tableSize) {
        return getWidthFrom(warehouse, table2, getPercentageWidth, function(deduced) {
          return deduced.fold(function() {
            return tableSize.minCellWidth();
          }, function(cellWidth) {
            return cellWidth / tableSize.pixelWidth() * 100;
          });
        });
      };
      var getPixelWidths = function(warehouse, table2, tableSize) {
        return getWidthFrom(warehouse, table2, getPixelWidth$1, function(deduced) {
          return deduced.getOrThunk(tableSize.minCellWidth);
        });
      };
      var getHeightFrom = function(warehouse, table2, direction, getHeight2, fallback2) {
        var rows$12 = rows(warehouse);
        var backups = [Optional.some(direction.edge(table2))].concat(map$1(direction.positions(rows$12, table2), function(pos) {
          return pos.map(function(p) {
            return p.y;
          });
        }));
        return map$1(rows$12, function(cellOption, c) {
          return getDimension(cellOption, c, backups, not(hasRowspan), getHeight2, fallback2);
        });
      };
      var getPixelHeights = function(warehouse, table2, direction) {
        return getHeightFrom(warehouse, table2, direction, getHeight, function(deduced) {
          return deduced.getOrThunk(minHeight);
        });
      };
      var getRawHeights = function(warehouse, table2, direction) {
        return getHeightFrom(warehouse, table2, direction, getRawH, getDeduced);
      };
      var widthLookup = function(table2, getter) {
        return function() {
          if (inBody(table2)) {
            return getter(table2);
          } else {
            return parseFloat(getRaw$2(table2, "width").getOr("0"));
          }
        };
      };
      var noneSize = function(table2) {
        var getWidth2 = widthLookup(table2, get$8);
        var zero2 = constant(0);
        var getWidths = function(warehouse, tableSize) {
          return getPixelWidths(warehouse, table2, tableSize);
        };
        return {
          width: getWidth2,
          pixelWidth: getWidth2,
          getWidths,
          getCellDelta: zero2,
          singleColumnWidth: constant([0]),
          minCellWidth: zero2,
          setElementWidth: noop,
          adjustTableWidth: noop,
          isRelative: true,
          label: "none"
        };
      };
      var percentageSize = function(table2) {
        var getFloatWidth = widthLookup(table2, function(elem) {
          return parseFloat(getPercentTableWidth(elem));
        });
        var getWidth2 = widthLookup(table2, get$8);
        var getCellDelta = function(delta) {
          return delta / getWidth2() * 100;
        };
        var singleColumnWidth = function(w, _delta) {
          return [100 - w];
        };
        var minCellWidth = function() {
          return minWidth() / getWidth2() * 100;
        };
        var adjustTableWidth = function(delta) {
          var currentWidth = getFloatWidth();
          var change = delta / 100 * currentWidth;
          var newWidth = currentWidth + change;
          setPercentageWidth(table2, newWidth);
        };
        var getWidths = function(warehouse, tableSize) {
          return getPercentageWidths(warehouse, table2, tableSize);
        };
        return {
          width: getFloatWidth,
          pixelWidth: getWidth2,
          getWidths,
          getCellDelta,
          singleColumnWidth,
          minCellWidth,
          setElementWidth: setPercentageWidth,
          adjustTableWidth,
          isRelative: true,
          label: "percent"
        };
      };
      var pixelSize = function(table2) {
        var getWidth2 = widthLookup(table2, get$8);
        var getCellDelta = identity;
        var singleColumnWidth = function(w, delta) {
          var newNext = Math.max(minWidth(), w + delta);
          return [newNext - w];
        };
        var adjustTableWidth = function(delta) {
          var newWidth = getWidth2() + delta;
          setPixelWidth(table2, newWidth);
        };
        var getWidths = function(warehouse, tableSize) {
          return getPixelWidths(warehouse, table2, tableSize);
        };
        return {
          width: getWidth2,
          pixelWidth: getWidth2,
          getWidths,
          getCellDelta,
          singleColumnWidth,
          minCellWidth: minWidth,
          setElementWidth: setPixelWidth,
          adjustTableWidth,
          isRelative: false,
          label: "pixel"
        };
      };
      var chooseSize = function(element, width2) {
        var percentMatch = percentageBasedSizeRegex().exec(width2);
        if (percentMatch !== null) {
          return percentageSize(element);
        } else {
          return pixelSize(element);
        }
      };
      var getTableSize = function(table2) {
        var width2 = getRawWidth$1(table2);
        return width2.fold(function() {
          return noneSize(table2);
        }, function(w) {
          return chooseSize(table2, w);
        });
      };
      var TableSize = {
        getTableSize,
        pixelSize,
        percentageSize,
        noneSize
      };
      var statsStruct = function(minRow, minCol, maxRow, maxCol, allCells, selectedCells) {
        return {
          minRow,
          minCol,
          maxRow,
          maxCol,
          allCells,
          selectedCells
        };
      };
      var findSelectedStats = function(house, isSelected) {
        var totalColumns = house.grid.columns;
        var totalRows = house.grid.rows;
        var minRow = totalRows;
        var minCol = totalColumns;
        var maxRow = 0;
        var maxCol = 0;
        var allCells = [];
        var selectedCells = [];
        each$1(house.access, function(detail2) {
          allCells.push(detail2);
          if (isSelected(detail2)) {
            selectedCells.push(detail2);
            var startRow = detail2.row;
            var endRow = startRow + detail2.rowspan - 1;
            var startCol = detail2.column;
            var endCol = startCol + detail2.colspan - 1;
            if (startRow < minRow) {
              minRow = startRow;
            } else if (endRow > maxRow) {
              maxRow = endRow;
            }
            if (startCol < minCol) {
              minCol = startCol;
            } else if (endCol > maxCol) {
              maxCol = endCol;
            }
          }
        });
        return statsStruct(minRow, minCol, maxRow, maxCol, allCells, selectedCells);
      };
      var makeCell = function(list, seenSelected, rowIndex) {
        var row2 = list[rowIndex].element;
        var td = SugarElement.fromTag("td");
        append$1(td, SugarElement.fromTag("br"));
        var f = seenSelected ? append$1 : prepend;
        f(row2, td);
      };
      var fillInGaps = function(list, house, stats, isSelected) {
        var totalColumns = house.grid.columns;
        var totalRows = house.grid.rows;
        for (var i = 0; i < totalRows; i++) {
          var seenSelected = false;
          for (var j = 0; j < totalColumns; j++) {
            if (!(i < stats.minRow || i > stats.maxRow || j < stats.minCol || j > stats.maxCol)) {
              var needCell = Warehouse.getAt(house, i, j).filter(isSelected).isNone();
              if (needCell) {
                makeCell(list, seenSelected, i);
              } else {
                seenSelected = true;
              }
            }
          }
        }
      };
      var clean = function(replica, stats, house, widthDelta) {
        each$1(house.columns, function(col2) {
          if (col2.column < stats.minCol || col2.column > stats.maxCol) {
            remove$5(col2.element);
          }
        });
        var emptyRows = filter$2(firstLayer(replica, "tr"), function(row2) {
          return row2.dom.childElementCount === 0;
        });
        each$2(emptyRows, remove$5);
        if (stats.minCol === stats.maxCol || stats.minRow === stats.maxRow) {
          each$2(firstLayer(replica, "th,td"), function(cell2) {
            remove$7(cell2, "rowspan");
            remove$7(cell2, "colspan");
          });
        }
        remove$7(replica, LOCKED_COL_ATTR);
        remove$7(replica, "data-snooker-col-series");
        var tableSize = TableSize.getTableSize(replica);
        tableSize.adjustTableWidth(widthDelta);
      };
      var getTableWidthDelta = function(table2, warehouse, tableSize, stats) {
        if (stats.minCol === 0 && warehouse.grid.columns === stats.maxCol + 1) {
          return 0;
        }
        var colWidths = getPixelWidths(warehouse, table2, tableSize);
        var allColsWidth = foldl(colWidths, function(acc, width2) {
          return acc + width2;
        }, 0);
        var selectedColsWidth = foldl(colWidths.slice(stats.minCol, stats.maxCol + 1), function(acc, width2) {
          return acc + width2;
        }, 0);
        var newWidth = selectedColsWidth / allColsWidth * tableSize.pixelWidth();
        var delta = newWidth - tableSize.pixelWidth();
        return tableSize.getCellDelta(delta);
      };
      var extract$1 = function(table2, selectedSelector) {
        var isSelected = function(detail2) {
          return is$2(detail2.element, selectedSelector);
        };
        var replica = deep(table2);
        var list = fromTable$1(replica);
        var tableSize = TableSize.getTableSize(table2);
        var replicaHouse = Warehouse.generate(list);
        var replicaStats = findSelectedStats(replicaHouse, isSelected);
        var selector = "th:not(" + selectedSelector + "),td:not(" + selectedSelector + ")";
        var unselectedCells = filterFirstLayer(replica, "th,td", function(cell2) {
          return is$2(cell2, selector);
        });
        each$2(unselectedCells, remove$5);
        fillInGaps(list, replicaHouse, replicaStats, isSelected);
        var house = Warehouse.fromTable(table2);
        var widthDelta = getTableWidthDelta(table2, house, tableSize, replicaStats);
        clean(replica, replicaStats, replicaHouse, widthDelta);
        return replica;
      };
      var nbsp = "\xA0";
      var getEnd = function(element) {
        return name(element) === "img" ? 1 : getOption(element).fold(function() {
          return children$3(element).length;
        }, function(v) {
          return v.length;
        });
      };
      var isTextNodeWithCursorPosition = function(el) {
        return getOption(el).filter(function(text) {
          return text.trim().length !== 0 || text.indexOf(nbsp) > -1;
        }).isSome();
      };
      var elementsWithCursorPosition = [
        "img",
        "br"
      ];
      var isCursorPosition = function(elem) {
        var hasCursorPosition = isTextNodeWithCursorPosition(elem);
        return hasCursorPosition || contains$2(elementsWithCursorPosition, name(elem));
      };
      var first = function(element) {
        return descendant$1(element, isCursorPosition);
      };
      var last$1 = function(element) {
        return descendantRtl(element, isCursorPosition);
      };
      var descendantRtl = function(scope, predicate) {
        var descend = function(element) {
          var children2 = children$3(element);
          for (var i = children2.length - 1; i >= 0; i--) {
            var child2 = children2[i];
            if (predicate(child2)) {
              return Optional.some(child2);
            }
            var res = descend(child2);
            if (res.isSome()) {
              return res;
            }
          }
          return Optional.none();
        };
        return descend(scope);
      };
      var transferableAttributes = {
        scope: [
          "row",
          "col"
        ]
      };
      var createCell = function(doc) {
        return function() {
          var td = SugarElement.fromTag("td", doc.dom);
          append$1(td, SugarElement.fromTag("br", doc.dom));
          return td;
        };
      };
      var createCol = function(doc) {
        return function() {
          return SugarElement.fromTag("col", doc.dom);
        };
      };
      var createColgroup = function(doc) {
        return function() {
          return SugarElement.fromTag("colgroup", doc.dom);
        };
      };
      var createRow$1 = function(doc) {
        return function() {
          return SugarElement.fromTag("tr", doc.dom);
        };
      };
      var replace$1 = function(cell2, tag, attrs) {
        var replica = copy$1(cell2, tag);
        each$1(attrs, function(v, k) {
          if (v === null) {
            remove$7(replica, k);
          } else {
            set$2(replica, k, v);
          }
        });
        return replica;
      };
      var pasteReplace = function(cell2) {
        return cell2;
      };
      var cloneFormats = function(oldCell, newCell, formats) {
        var first$1 = first(oldCell);
        return first$1.map(function(firstText) {
          var formatSelector = formats.join(",");
          var parents2 = ancestors$3(firstText, formatSelector, function(element) {
            return eq$1(element, oldCell);
          });
          return foldr(parents2, function(last2, parent2) {
            var clonedFormat = shallow(parent2);
            remove$7(clonedFormat, "contenteditable");
            append$1(last2, clonedFormat);
            return clonedFormat;
          }, newCell);
        }).getOr(newCell);
      };
      var cloneAppropriateAttributes = function(original, clone2) {
        each$1(transferableAttributes, function(validAttributes, attributeName) {
          return getOpt(original, attributeName).filter(function(attribute) {
            return contains$2(validAttributes, attribute);
          }).each(function(attribute) {
            return set$2(clone2, attributeName, attribute);
          });
        });
      };
      var cellOperations = function(mutate2, doc, formatsToClone) {
        var cloneCss = function(prev2, clone2) {
          copy$2(prev2.element, clone2);
          remove$6(clone2, "height");
          if (prev2.colspan !== 1) {
            remove$6(clone2, "width");
          }
        };
        var newCell = function(prev2) {
          var td = SugarElement.fromTag(name(prev2.element), doc.dom);
          var formats = formatsToClone.getOr([
            "strong",
            "em",
            "b",
            "i",
            "span",
            "font",
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "p",
            "div"
          ]);
          var lastNode = formats.length > 0 ? cloneFormats(prev2.element, td, formats) : td;
          append$1(lastNode, SugarElement.fromTag("br"));
          cloneCss(prev2, td);
          cloneAppropriateAttributes(prev2.element, td);
          mutate2(prev2.element, td);
          return td;
        };
        var newCol = function(prev2) {
          var col2 = SugarElement.fromTag(name(prev2.element), doc.dom);
          cloneCss(prev2, col2);
          mutate2(prev2.element, col2);
          return col2;
        };
        return {
          col: newCol,
          colgroup: createColgroup(doc),
          row: createRow$1(doc),
          cell: newCell,
          replace: replace$1,
          colGap: createCol(doc),
          gap: createCell(doc)
        };
      };
      var paste$1 = function(doc) {
        return {
          col: createCol(doc),
          colgroup: createColgroup(doc),
          row: createRow$1(doc),
          cell: createCell(doc),
          replace: pasteReplace,
          colGap: createCol(doc),
          gap: createCell(doc)
        };
      };
      var fromHtml = function(html, scope) {
        var doc = scope || document;
        var div = doc.createElement("div");
        div.innerHTML = html;
        return children$3(SugarElement.fromDom(div));
      };
      var fromDom = function(nodes) {
        return map$1(nodes, SugarElement.fromDom);
      };
      var getNodeName = function(elm) {
        return elm.nodeName.toLowerCase();
      };
      var getBody = function(editor) {
        return SugarElement.fromDom(editor.getBody());
      };
      var getPixelWidth = function(elm) {
        return elm.getBoundingClientRect().width;
      };
      var getPixelHeight = function(elm) {
        return elm.getBoundingClientRect().height;
      };
      var getIsRoot = function(editor) {
        return function(element) {
          return eq$1(element, getBody(editor));
        };
      };
      var removePxSuffix = function(size2) {
        return size2 ? size2.replace(/px$/, "") : "";
      };
      var addPxSuffix = function(size2) {
        return /^\d+(\.\d+)?$/.test(size2) ? size2 + "px" : size2;
      };
      var removeDataStyle = function(table2) {
        remove$7(table2, "data-mce-style");
        var removeStyleAttribute = function(element) {
          return remove$7(element, "data-mce-style");
        };
        each$2(cells$1(table2), removeStyleAttribute);
        each$2(columns$1(table2), removeStyleAttribute);
        each$2(rows$1(table2), removeStyleAttribute);
      };
      var getRawWidth = function(editor, elm) {
        var raw = editor.dom.getStyle(elm, "width") || editor.dom.getAttrib(elm, "width");
        return Optional.from(raw).filter(isNotEmpty);
      };
      var isPercentage$1 = function(value2) {
        return /^(\d+(\.\d+)?)%$/.test(value2);
      };
      var isPixel = function(value2) {
        return /^(\d+(\.\d+)?)px$/.test(value2);
      };
      var getSelectionStart = function(editor) {
        return SugarElement.fromDom(editor.selection.getStart());
      };
      var getSelectionEnd = function(editor) {
        return SugarElement.fromDom(editor.selection.getEnd());
      };
      var selection = function(selections) {
        return cata$2(selections.get(), constant([]), identity, pure);
      };
      var unmergable = function(selections) {
        var hasSpan = function(elem, type2) {
          return getOpt(elem, type2).exists(function(span) {
            return parseInt(span, 10) > 1;
          });
        };
        var hasRowOrColSpan = function(elem) {
          return hasSpan(elem, "rowspan") || hasSpan(elem, "colspan");
        };
        var candidates = selection(selections);
        return candidates.length > 0 && forall(candidates, hasRowOrColSpan) ? Optional.some(candidates) : Optional.none();
      };
      var mergable = function(table2, selections, ephemera2) {
        return cata$2(selections.get(), Optional.none, function(cells2) {
          if (cells2.length <= 1) {
            return Optional.none();
          } else {
            return retrieveBox(table2, ephemera2.firstSelectedSelector, ephemera2.lastSelectedSelector).map(function(bounds2) {
              return {
                bounds: bounds2,
                cells: cells2
              };
            });
          }
        }, Optional.none);
      };
      var strSelected = "data-mce-selected";
      var strSelectedSelector = "td[" + strSelected + "],th[" + strSelected + "]";
      var strAttributeSelector = "[" + strSelected + "]";
      var strFirstSelected = "data-mce-first-selected";
      var strFirstSelectedSelector = "td[" + strFirstSelected + "],th[" + strFirstSelected + "]";
      var strLastSelected = "data-mce-last-selected";
      var strLastSelectedSelector = "td[" + strLastSelected + "],th[" + strLastSelected + "]";
      var attributeSelector = strAttributeSelector;
      var ephemera = {
        selected: strSelected,
        selectedSelector: strSelectedSelector,
        firstSelected: strFirstSelected,
        firstSelectedSelector: strFirstSelectedSelector,
        lastSelected: strLastSelected,
        lastSelectedSelector: strLastSelectedSelector
      };
      var noMenu = function(cell2) {
        return {
          element: cell2,
          mergable: Optional.none(),
          unmergable: Optional.none(),
          selection: [cell2]
        };
      };
      var forMenu = function(selections, table2, cell2) {
        return {
          element: cell2,
          mergable: mergable(table2, selections, ephemera),
          unmergable: unmergable(selections),
          selection: selection(selections)
        };
      };
      var paste = function(element, clipboard, generators) {
        return {
          element,
          clipboard,
          generators
        };
      };
      var pasteRows = function(selections, cell2, clipboard, generators) {
        return {
          selection: selection(selections),
          clipboard,
          generators
        };
      };
      var getSelectionCellFallback = function(element) {
        return table(element).bind(function(table2) {
          return retrieve(table2, ephemera.firstSelectedSelector);
        }).fold(constant(element), function(cells2) {
          return cells2[0];
        });
      };
      var getSelectionFromSelector = function(selector) {
        return function(initCell, isRoot) {
          var cellName = name(initCell);
          var cell2 = cellName === "col" || cellName === "colgroup" ? getSelectionCellFallback(initCell) : initCell;
          return closest$1(cell2, selector, isRoot);
        };
      };
      var getSelectionCellOrCaption = getSelectionFromSelector("th,td,caption");
      var getSelectionCell = getSelectionFromSelector("th,td");
      var getCellsFromSelection = function(selections) {
        return selection(selections);
      };
      var getRowsFromSelection = function(selected, selector) {
        var cellOpt = getSelectionCell(selected);
        var rowsOpt = cellOpt.bind(function(cell2) {
          return table(cell2);
        }).map(function(table2) {
          return rows$1(table2);
        });
        return lift2(cellOpt, rowsOpt, function(cell2, rows2) {
          return filter$2(rows2, function(row2) {
            return exists(fromDom(row2.dom.cells), function(rowCell) {
              return get$b(rowCell, selector) === "1" || eq$1(rowCell, cell2);
            });
          });
        }).getOr([]);
      };
      var extractSelected = function(cells2) {
        return table(cells2[0]).map(function(table2) {
          var replica = extract$1(table2, attributeSelector);
          removeDataStyle(replica);
          return [replica];
        });
      };
      var serializeElements = function(editor, elements) {
        return map$1(elements, function(elm) {
          return editor.selection.serializer.serialize(elm.dom, {});
        }).join("");
      };
      var getTextContent = function(elements) {
        return map$1(elements, function(element) {
          return element.dom.innerText;
        }).join("");
      };
      var registerEvents = function(editor, selections, actions) {
        editor.on("BeforeGetContent", function(e) {
          var multiCellContext = function(cells2) {
            e.preventDefault();
            extractSelected(cells2).each(function(elements) {
              e.content = e.format === "text" ? getTextContent(elements) : serializeElements(editor, elements);
            });
          };
          if (e.selection === true) {
            cata$2(selections.get(), noop, multiCellContext, noop);
          }
        });
        editor.on("BeforeSetContent", function(e) {
          if (e.selection === true && e.paste === true) {
            var selectedCells = getCellsFromSelection(selections);
            head(selectedCells).each(function(cell2) {
              table(cell2).each(function(table2) {
                var elements = filter$2(fromHtml(e.content), function(content) {
                  return name(content) !== "meta";
                });
                var isTable = isTag("table");
                if (elements.length === 1 && isTable(elements[0])) {
                  e.preventDefault();
                  var doc = SugarElement.fromDom(editor.getDoc());
                  var generators = paste$1(doc);
                  var targets = paste(cell2, elements[0], generators);
                  actions.pasteCells(table2, targets).each(function() {
                    editor.focus();
                  });
                }
              });
            });
          }
        });
      };
      var adt$7 = Adt.generate([
        { none: [] },
        { only: ["index"] },
        {
          left: [
            "index",
            "next"
          ]
        },
        {
          middle: [
            "prev",
            "index",
            "next"
          ]
        },
        {
          right: [
            "prev",
            "index"
          ]
        }
      ]);
      var ColumnContext = __assign({}, adt$7);
      var neighbours = function(input, index) {
        if (input.length === 0) {
          return ColumnContext.none();
        }
        if (input.length === 1) {
          return ColumnContext.only(0);
        }
        if (index === 0) {
          return ColumnContext.left(0, 1);
        }
        if (index === input.length - 1) {
          return ColumnContext.right(index - 1, index);
        }
        if (index > 0 && index < input.length - 1) {
          return ColumnContext.middle(index - 1, index, index + 1);
        }
        return ColumnContext.none();
      };
      var determine = function(input, column, step, tableSize, resize2) {
        var result = input.slice(0);
        var context = neighbours(input, column);
        var onNone = constant(map$1(result, constant(0)));
        var onOnly = function(index) {
          return tableSize.singleColumnWidth(result[index], step);
        };
        var onLeft = function(index, next2) {
          return resize2.calcLeftEdgeDeltas(result, index, next2, step, tableSize.minCellWidth(), tableSize.isRelative);
        };
        var onMiddle = function(prev2, index, next2) {
          return resize2.calcMiddleDeltas(result, prev2, index, next2, step, tableSize.minCellWidth(), tableSize.isRelative);
        };
        var onRight = function(prev2, index) {
          return resize2.calcRightEdgeDeltas(result, prev2, index, step, tableSize.minCellWidth(), tableSize.isRelative);
        };
        return context.fold(onNone, onOnly, onLeft, onMiddle, onRight);
      };
      var total = function(start, end, measures) {
        var r2 = 0;
        for (var i = start; i < end; i++) {
          r2 += measures[i] !== void 0 ? measures[i] : 0;
        }
        return r2;
      };
      var recalculateWidthForCells = function(warehouse, widths) {
        var all2 = Warehouse.justCells(warehouse);
        return map$1(all2, function(cell2) {
          var width2 = total(cell2.column, cell2.column + cell2.colspan, widths);
          return {
            element: cell2.element,
            width: width2,
            colspan: cell2.colspan
          };
        });
      };
      var recalculateWidthForColumns = function(warehouse, widths) {
        var groups = Warehouse.justColumns(warehouse);
        return map$1(groups, function(column, index) {
          return {
            element: column.element,
            width: widths[index],
            colspan: column.colspan
          };
        });
      };
      var recalculateHeightForCells = function(warehouse, heights) {
        var all2 = Warehouse.justCells(warehouse);
        return map$1(all2, function(cell2) {
          var height2 = total(cell2.row, cell2.row + cell2.rowspan, heights);
          return {
            element: cell2.element,
            height: height2,
            rowspan: cell2.rowspan
          };
        });
      };
      var matchRowHeight = function(warehouse, heights) {
        return map$1(warehouse.all, function(row2, i) {
          return {
            element: row2.element,
            height: heights[i]
          };
        });
      };
      var sumUp = function(newSize) {
        return foldr(newSize, function(b, a) {
          return b + a;
        }, 0);
      };
      var recalculate = function(warehouse, widths) {
        if (Warehouse.hasColumns(warehouse)) {
          return recalculateWidthForColumns(warehouse, widths);
        } else {
          return recalculateWidthForCells(warehouse, widths);
        }
      };
      var recalculateAndApply = function(warehouse, widths, tableSize) {
        var newSizes = recalculate(warehouse, widths);
        each$2(newSizes, function(cell2) {
          tableSize.setElementWidth(cell2.element, cell2.width);
        });
      };
      var adjustWidth = function(table2, delta, index, resizing, tableSize) {
        var warehouse = Warehouse.fromTable(table2);
        var step = tableSize.getCellDelta(delta);
        var widths = tableSize.getWidths(warehouse, tableSize);
        var isLastColumn = index === warehouse.grid.columns - 1;
        var clampedStep = resizing.clampTableDelta(widths, index, step, tableSize.minCellWidth(), isLastColumn);
        var deltas = determine(widths, index, clampedStep, tableSize, resizing);
        var newWidths = map$1(deltas, function(dx, i) {
          return dx + widths[i];
        });
        recalculateAndApply(warehouse, newWidths, tableSize);
        resizing.resizeTable(tableSize.adjustTableWidth, clampedStep, isLastColumn);
      };
      var adjustHeight = function(table2, delta, index, direction) {
        var warehouse = Warehouse.fromTable(table2);
        var heights = getPixelHeights(warehouse, table2, direction);
        var newHeights = map$1(heights, function(dy, i) {
          return index === i ? Math.max(delta + dy, minHeight()) : dy;
        });
        var newCellSizes = recalculateHeightForCells(warehouse, newHeights);
        var newRowSizes = matchRowHeight(warehouse, newHeights);
        each$2(newRowSizes, function(row2) {
          setHeight(row2.element, row2.height);
        });
        each$2(newCellSizes, function(cell2) {
          setHeight(cell2.element, cell2.height);
        });
        var total2 = sumUp(newHeights);
        setHeight(table2, total2);
      };
      var adjustAndRedistributeWidths$1 = function(_table, list, details, tableSize, resizeBehaviour) {
        var warehouse = Warehouse.generate(list);
        var sizes = tableSize.getWidths(warehouse, tableSize);
        var tablePixelWidth = tableSize.pixelWidth();
        var _a = resizeBehaviour.calcRedestributedWidths(sizes, tablePixelWidth, details.pixelDelta, tableSize.isRelative), newSizes = _a.newSizes, delta = _a.delta;
        recalculateAndApply(warehouse, newSizes, tableSize);
        tableSize.adjustTableWidth(delta);
      };
      var adjustWidthTo = function(_table, list, _info, tableSize) {
        var warehouse = Warehouse.generate(list);
        var widths = tableSize.getWidths(warehouse, tableSize);
        recalculateAndApply(warehouse, widths, tableSize);
      };
      var zero = function(array) {
        return map$1(array, constant(0));
      };
      var surround = function(sizes, startIndex, endIndex, results, f) {
        return f(sizes.slice(0, startIndex)).concat(results).concat(f(sizes.slice(endIndex)));
      };
      var clampDeltaHelper = function(predicate) {
        return function(sizes, index, delta, minCellSize) {
          if (!predicate(delta)) {
            return delta;
          } else {
            var newSize = Math.max(minCellSize, sizes[index] - Math.abs(delta));
            var diff = Math.abs(newSize - sizes[index]);
            return delta >= 0 ? diff : -diff;
          }
        };
      };
      var clampNegativeDelta = clampDeltaHelper(function(delta) {
        return delta < 0;
      });
      var clampDelta = clampDeltaHelper(always);
      var resizeTable = function() {
        var calcFixedDeltas = function(sizes, index, next2, delta, minCellSize) {
          var clampedDelta = clampNegativeDelta(sizes, index, delta, minCellSize);
          return surround(sizes, index, next2 + 1, [
            clampedDelta,
            0
          ], zero);
        };
        var calcRelativeDeltas = function(sizes, index, delta, minCellSize) {
          var ratio = (100 + delta) / 100;
          var newThis = Math.max(minCellSize, (sizes[index] + delta) / ratio);
          return map$1(sizes, function(size2, idx) {
            var newSize = idx === index ? newThis : size2 / ratio;
            return newSize - size2;
          });
        };
        var calcLeftEdgeDeltas = function(sizes, index, next2, delta, minCellSize, isRelative) {
          if (isRelative) {
            return calcRelativeDeltas(sizes, index, delta, minCellSize);
          } else {
            return calcFixedDeltas(sizes, index, next2, delta, minCellSize);
          }
        };
        var calcMiddleDeltas = function(sizes, _prev, index, next2, delta, minCellSize, isRelative) {
          return calcLeftEdgeDeltas(sizes, index, next2, delta, minCellSize, isRelative);
        };
        var resizeTable2 = function(resizer, delta) {
          return resizer(delta);
        };
        var calcRightEdgeDeltas = function(sizes, _prev, index, delta, minCellSize, isRelative) {
          if (isRelative) {
            return calcRelativeDeltas(sizes, index, delta, minCellSize);
          } else {
            var clampedDelta = clampNegativeDelta(sizes, index, delta, minCellSize);
            return zero(sizes.slice(0, index)).concat([clampedDelta]);
          }
        };
        var calcRedestributedWidths = function(sizes, totalWidth, pixelDelta, isRelative) {
          if (isRelative) {
            var tableWidth = totalWidth + pixelDelta;
            var ratio_1 = tableWidth / totalWidth;
            var newSizes = map$1(sizes, function(size2) {
              return size2 / ratio_1;
            });
            return {
              delta: ratio_1 * 100 - 100,
              newSizes
            };
          } else {
            return {
              delta: pixelDelta,
              newSizes: sizes
            };
          }
        };
        return {
          resizeTable: resizeTable2,
          clampTableDelta: clampNegativeDelta,
          calcLeftEdgeDeltas,
          calcMiddleDeltas,
          calcRightEdgeDeltas,
          calcRedestributedWidths
        };
      };
      var preserveTable = function() {
        var calcLeftEdgeDeltas = function(sizes, index, next2, delta, minCellSize) {
          var idx = delta >= 0 ? next2 : index;
          var clampedDelta = clampDelta(sizes, idx, delta, minCellSize);
          return surround(sizes, index, next2 + 1, [
            clampedDelta,
            -clampedDelta
          ], zero);
        };
        var calcMiddleDeltas = function(sizes, _prev, index, next2, delta, minCellSize) {
          return calcLeftEdgeDeltas(sizes, index, next2, delta, minCellSize);
        };
        var resizeTable2 = function(resizer, delta, isLastColumn) {
          if (isLastColumn) {
            resizer(delta);
          }
        };
        var calcRightEdgeDeltas = function(sizes, _prev, _index, delta, _minCellSize, isRelative) {
          if (isRelative) {
            return zero(sizes);
          } else {
            var diff = delta / sizes.length;
            return map$1(sizes, constant(diff));
          }
        };
        var clampTableDelta = function(sizes, index, delta, minCellSize, isLastColumn) {
          if (isLastColumn) {
            if (delta >= 0) {
              return delta;
            } else {
              var maxDelta = foldl(sizes, function(a, b) {
                return a + b - minCellSize;
              }, 0);
              return Math.max(-maxDelta, delta);
            }
          } else {
            return clampNegativeDelta(sizes, index, delta, minCellSize);
          }
        };
        var calcRedestributedWidths = function(sizes, _totalWidth, _pixelDelta, _isRelative) {
          return {
            delta: 0,
            newSizes: sizes
          };
        };
        return {
          resizeTable: resizeTable2,
          clampTableDelta,
          calcLeftEdgeDeltas,
          calcMiddleDeltas,
          calcRightEdgeDeltas,
          calcRedestributedWidths
        };
      };
      var only = function(element, isResizable2) {
        var parent2 = Optional.from(element.dom.documentElement).map(SugarElement.fromDom).getOr(element);
        return {
          parent: constant(parent2),
          view: constant(element),
          origin: constant(SugarPosition(0, 0)),
          isResizable: isResizable2
        };
      };
      var detached = function(editable, chrome2, isResizable2) {
        var origin = function() {
          return absolute(chrome2);
        };
        return {
          parent: constant(chrome2),
          view: constant(editable),
          origin,
          isResizable: isResizable2
        };
      };
      var body = function(editable, chrome2, isResizable2) {
        return {
          parent: constant(chrome2),
          view: constant(editable),
          origin: constant(SugarPosition(0, 0)),
          isResizable: isResizable2
        };
      };
      var ResizeWire = {
        only,
        detached,
        body
      };
      var adt$6 = Adt.generate([
        { invalid: ["raw"] },
        { pixels: ["value"] },
        { percent: ["value"] }
      ]);
      var validateFor = function(suffix, type2, value2) {
        var rawAmount = value2.substring(0, value2.length - suffix.length);
        var amount = parseFloat(rawAmount);
        return rawAmount === amount.toString() ? type2(amount) : adt$6.invalid(value2);
      };
      var from = function(value2) {
        if (endsWith(value2, "%")) {
          return validateFor("%", adt$6.percent, value2);
        }
        if (endsWith(value2, "px")) {
          return validateFor("px", adt$6.pixels, value2);
        }
        return adt$6.invalid(value2);
      };
      var Size = __assign(__assign({}, adt$6), { from });
      var redistributeToPercent = function(widths, totalWidth) {
        return map$1(widths, function(w) {
          var colType = Size.from(w);
          return colType.fold(function() {
            return w;
          }, function(px) {
            var ratio = px / totalWidth * 100;
            return ratio + "%";
          }, function(pc) {
            return pc + "%";
          });
        });
      };
      var redistributeToPx = function(widths, totalWidth, newTotalWidth) {
        var scale = newTotalWidth / totalWidth;
        return map$1(widths, function(w) {
          var colType = Size.from(w);
          return colType.fold(function() {
            return w;
          }, function(px) {
            return px * scale + "px";
          }, function(pc) {
            return pc / 100 * newTotalWidth + "px";
          });
        });
      };
      var redistributeEmpty = function(newWidthType, columns2) {
        var f = newWidthType.fold(function() {
          return constant("");
        }, function(pixels) {
          var num = pixels / columns2;
          return constant(num + "px");
        }, function() {
          var num = 100 / columns2;
          return constant(num + "%");
        });
        return range$1(columns2, f);
      };
      var redistributeValues = function(newWidthType, widths, totalWidth) {
        return newWidthType.fold(function() {
          return widths;
        }, function(px) {
          return redistributeToPx(widths, totalWidth, px);
        }, function(_pc) {
          return redistributeToPercent(widths, totalWidth);
        });
      };
      var redistribute$1 = function(widths, totalWidth, newWidth) {
        var newType = Size.from(newWidth);
        var floats = forall(widths, function(s) {
          return s === "0px";
        }) ? redistributeEmpty(newType, widths.length) : redistributeValues(newType, widths, totalWidth);
        return normalize(floats);
      };
      var sum = function(values2, fallback2) {
        if (values2.length === 0) {
          return fallback2;
        }
        return foldr(values2, function(rest, v) {
          return Size.from(v).fold(constant(0), identity, identity) + rest;
        }, 0);
      };
      var roundDown = function(num, unit) {
        var floored = Math.floor(num);
        return {
          value: floored + unit,
          remainder: num - floored
        };
      };
      var add$3 = function(value2, amount) {
        return Size.from(value2).fold(constant(value2), function(px) {
          return px + amount + "px";
        }, function(pc) {
          return pc + amount + "%";
        });
      };
      var normalize = function(values2) {
        if (values2.length === 0) {
          return values2;
        }
        var scan2 = foldr(values2, function(rest, value2) {
          var info = Size.from(value2).fold(function() {
            return {
              value: value2,
              remainder: 0
            };
          }, function(num) {
            return roundDown(num, "px");
          }, function(num) {
            return {
              value: num + "%",
              remainder: 0
            };
          });
          return {
            output: [info.value].concat(rest.output),
            remainder: rest.remainder + info.remainder
          };
        }, {
          output: [],
          remainder: 0
        });
        var r2 = scan2.output;
        return r2.slice(0, r2.length - 1).concat([add$3(r2[r2.length - 1], Math.round(scan2.remainder))]);
      };
      var validate = Size.from;
      var redistributeToW = function(newWidths, cells2, unit) {
        each$2(cells2, function(cell2) {
          var widths = newWidths.slice(cell2.column, cell2.colspan + cell2.column);
          var w = sum(widths, minWidth());
          set$1(cell2.element, "width", w + unit);
        });
      };
      var redistributeToColumns = function(newWidths, columns2, unit) {
        each$2(columns2, function(column, index) {
          var width2 = sum([newWidths[index]], minWidth());
          set$1(column.element, "width", width2 + unit);
        });
      };
      var redistributeToH = function(newHeights, rows2, cells2, unit) {
        each$2(cells2, function(cell2) {
          var heights = newHeights.slice(cell2.row, cell2.rowspan + cell2.row);
          var h = sum(heights, minHeight());
          set$1(cell2.element, "height", h + unit);
        });
        each$2(rows2, function(row2, i) {
          set$1(row2.element, "height", newHeights[i]);
        });
      };
      var getUnit = function(newSize) {
        return validate(newSize).fold(constant("px"), constant("px"), constant("%"));
      };
      var redistribute = function(table2, optWidth, optHeight) {
        var warehouse = Warehouse.fromTable(table2);
        var rows2 = warehouse.all;
        var cells2 = Warehouse.justCells(warehouse);
        var columns2 = Warehouse.justColumns(warehouse);
        optWidth.each(function(newWidth) {
          var widthUnit = getUnit(newWidth);
          var totalWidth = get$8(table2);
          var oldWidths = getRawWidths(warehouse, table2);
          var nuWidths = redistribute$1(oldWidths, totalWidth, newWidth);
          if (Warehouse.hasColumns(warehouse)) {
            redistributeToColumns(nuWidths, columns2, widthUnit);
          } else {
            redistributeToW(nuWidths, cells2, widthUnit);
          }
          set$1(table2, "width", newWidth);
        });
        optHeight.each(function(newHeight) {
          var hUnit = getUnit(newHeight);
          var totalHeight = get$7(table2);
          var oldHeights = getRawHeights(warehouse, table2, height);
          var nuHeights = redistribute$1(oldHeights, totalHeight, newHeight);
          redistributeToH(nuHeights, rows2, cells2, hUnit);
          set$1(table2, "height", newHeight);
        });
      };
      var isPercentSizing = isPercentSizing$1;
      var isPixelSizing = isPixelSizing$1;
      var isNoneSizing = isNoneSizing$1;
      var getGridSize = function(table2) {
        var warehouse = Warehouse.fromTable(table2);
        return warehouse.grid;
      };
      var Event = function(fields) {
        var handlers = [];
        var bind2 = function(handler) {
          if (handler === void 0) {
            throw new Error("Event bind error: undefined handler");
          }
          handlers.push(handler);
        };
        var unbind2 = function(handler) {
          handlers = filter$2(handlers, function(h) {
            return h !== handler;
          });
        };
        var trigger = function() {
          var args = [];
          for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
          }
          var event = {};
          each$2(fields, function(name2, i) {
            event[name2] = args[i];
          });
          each$2(handlers, function(handler) {
            handler(event);
          });
        };
        return {
          bind: bind2,
          unbind: unbind2,
          trigger
        };
      };
      var create$4 = function(typeDefs) {
        var registry = map(typeDefs, function(event) {
          return {
            bind: event.bind,
            unbind: event.unbind
          };
        });
        var trigger = map(typeDefs, function(event) {
          return event.trigger;
        });
        return {
          registry,
          trigger
        };
      };
      var last = function(fn, rate) {
        var timer = null;
        var cancel = function() {
          if (!isNull(timer)) {
            clearTimeout(timer);
            timer = null;
          }
        };
        var throttle = function() {
          var args = [];
          for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
          }
          cancel();
          timer = setTimeout(function() {
            timer = null;
            fn.apply(null, args);
          }, rate);
        };
        return {
          cancel,
          throttle
        };
      };
      var sort = function(arr) {
        return arr.slice(0).sort();
      };
      var reqMessage = function(required, keys2) {
        throw new Error("All required keys (" + sort(required).join(", ") + ") were not specified. Specified keys were: " + sort(keys2).join(", ") + ".");
      };
      var unsuppMessage = function(unsupported) {
        throw new Error("Unsupported keys for object: " + sort(unsupported).join(", "));
      };
      var validateStrArr = function(label, array) {
        if (!isArray(array)) {
          throw new Error("The " + label + " fields must be an array. Was: " + array + ".");
        }
        each$2(array, function(a) {
          if (!isString(a)) {
            throw new Error("The value " + a + " in the " + label + " fields was not a string.");
          }
        });
      };
      var invalidTypeMessage = function(incorrect, type2) {
        throw new Error("All values need to be of type: " + type2 + ". Keys (" + sort(incorrect).join(", ") + ") were not.");
      };
      var checkDupes = function(everything) {
        var sorted = sort(everything);
        var dupe = find$1(sorted, function(s, i) {
          return i < sorted.length - 1 && s === sorted[i + 1];
        });
        dupe.each(function(d) {
          throw new Error("The field: " + d + " occurs more than once in the combined fields: [" + sorted.join(", ") + "].");
        });
      };
      var base = function(handleUnsupported, required) {
        return baseWith(handleUnsupported, required, {
          validate: isFunction,
          label: "function"
        });
      };
      var baseWith = function(handleUnsupported, required, pred) {
        if (required.length === 0) {
          throw new Error("You must specify at least one required field.");
        }
        validateStrArr("required", required);
        checkDupes(required);
        return function(obj) {
          var keys$1 = keys(obj);
          var allReqd = forall(required, function(req) {
            return contains$2(keys$1, req);
          });
          if (!allReqd) {
            reqMessage(required, keys$1);
          }
          handleUnsupported(required, keys$1);
          var invalidKeys = filter$2(required, function(key2) {
            return !pred.validate(obj[key2], key2);
          });
          if (invalidKeys.length > 0) {
            invalidTypeMessage(invalidKeys, pred.label);
          }
          return obj;
        };
      };
      var handleExact = function(required, keys2) {
        var unsupported = filter$2(keys2, function(key2) {
          return !contains$2(required, key2);
        });
        if (unsupported.length > 0) {
          unsuppMessage(unsupported);
        }
      };
      var exactly = function(required) {
        return base(handleExact, required);
      };
      var DragMode = exactly([
        "compare",
        "extract",
        "mutate",
        "sink"
      ]);
      var DragSink = exactly([
        "element",
        "start",
        "stop",
        "destroy"
      ]);
      var DragApi = exactly([
        "forceDrop",
        "drop",
        "move",
        "delayDrop"
      ]);
      var InDrag = function() {
        var previous = Optional.none();
        var reset = function() {
          previous = Optional.none();
        };
        var update2 = function(mode, nu2) {
          var result = previous.map(function(old) {
            return mode.compare(old, nu2);
          });
          previous = Optional.some(nu2);
          return result;
        };
        var onEvent = function(event, mode) {
          var dataOption = mode.extract(event);
          dataOption.each(function(data) {
            var offset = update2(mode, data);
            offset.each(function(d) {
              events.trigger.move(d);
            });
          });
        };
        var events = create$4({ move: Event(["info"]) });
        return {
          onEvent,
          reset,
          events: events.registry
        };
      };
      var NoDrag = function() {
        var events = create$4({ move: Event(["info"]) });
        return {
          onEvent: noop,
          reset: noop,
          events: events.registry
        };
      };
      var Movement = function() {
        var noDragState = NoDrag();
        var inDragState = InDrag();
        var dragState = noDragState;
        var on2 = function() {
          dragState.reset();
          dragState = inDragState;
        };
        var off = function() {
          dragState.reset();
          dragState = noDragState;
        };
        var onEvent = function(event, mode) {
          dragState.onEvent(event, mode);
        };
        var isOn = function() {
          return dragState === inDragState;
        };
        return {
          on: on2,
          off,
          isOn,
          onEvent,
          events: inDragState.events
        };
      };
      var setup = function(mutation, mode, settings) {
        var active = false;
        var events = create$4({
          start: Event([]),
          stop: Event([])
        });
        var movement = Movement();
        var drop = function() {
          sink2.stop();
          if (movement.isOn()) {
            movement.off();
            events.trigger.stop();
          }
        };
        var throttledDrop = last(drop, 200);
        var go2 = function(parent2) {
          sink2.start(parent2);
          movement.on();
          events.trigger.start();
        };
        var mousemove = function(event) {
          throttledDrop.cancel();
          movement.onEvent(event, mode);
        };
        movement.events.move.bind(function(event) {
          mode.mutate(mutation, event.info);
        });
        var on2 = function() {
          active = true;
        };
        var off = function() {
          active = false;
        };
        var runIfActive = function(f) {
          return function() {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
              args[_i] = arguments[_i];
            }
            if (active) {
              f.apply(null, args);
            }
          };
        };
        var sink2 = mode.sink(DragApi({
          forceDrop: drop,
          drop: runIfActive(drop),
          move: runIfActive(mousemove),
          delayDrop: runIfActive(throttledDrop.throttle)
        }), settings);
        var destroy2 = function() {
          sink2.destroy();
        };
        return {
          element: sink2.element,
          go: go2,
          on: on2,
          off,
          destroy: destroy2,
          events: events.registry
        };
      };
      var mkEvent = function(target, x, y, stop, prevent, kill, raw) {
        return {
          target,
          x,
          y,
          stop,
          prevent,
          kill,
          raw
        };
      };
      var fromRawEvent$1 = function(rawEvent) {
        var target = SugarElement.fromDom(getOriginalEventTarget(rawEvent).getOr(rawEvent.target));
        var stop = function() {
          return rawEvent.stopPropagation();
        };
        var prevent = function() {
          return rawEvent.preventDefault();
        };
        var kill = compose(prevent, stop);
        return mkEvent(target, rawEvent.clientX, rawEvent.clientY, stop, prevent, kill, rawEvent);
      };
      var handle$2 = function(filter2, handler) {
        return function(rawEvent) {
          if (filter2(rawEvent)) {
            handler(fromRawEvent$1(rawEvent));
          }
        };
      };
      var binder = function(element, event, filter2, handler, useCapture) {
        var wrapped = handle$2(filter2, handler);
        element.dom.addEventListener(event, wrapped, useCapture);
        return { unbind: curry(unbind, element, event, wrapped, useCapture) };
      };
      var bind$1 = function(element, event, filter2, handler) {
        return binder(element, event, filter2, handler, false);
      };
      var unbind = function(element, event, handler, useCapture) {
        element.dom.removeEventListener(event, handler, useCapture);
      };
      var filter = always;
      var bind = function(element, event, handler) {
        return bind$1(element, event, filter, handler);
      };
      var fromRawEvent = fromRawEvent$1;
      var read = function(element, attr) {
        var value2 = get$b(element, attr);
        return value2 === void 0 || value2 === "" ? [] : value2.split(" ");
      };
      var add$2 = function(element, attr, id) {
        var old = read(element, attr);
        var nu2 = old.concat([id]);
        set$2(element, attr, nu2.join(" "));
        return true;
      };
      var remove$4 = function(element, attr, id) {
        var nu2 = filter$2(read(element, attr), function(v) {
          return v !== id;
        });
        if (nu2.length > 0) {
          set$2(element, attr, nu2.join(" "));
        } else {
          remove$7(element, attr);
        }
        return false;
      };
      var supports = function(element) {
        return element.dom.classList !== void 0;
      };
      var get$5 = function(element) {
        return read(element, "class");
      };
      var add$1 = function(element, clazz) {
        return add$2(element, "class", clazz);
      };
      var remove$3 = function(element, clazz) {
        return remove$4(element, "class", clazz);
      };
      var add = function(element, clazz) {
        if (supports(element)) {
          element.dom.classList.add(clazz);
        } else {
          add$1(element, clazz);
        }
      };
      var cleanClass = function(element) {
        var classList = supports(element) ? element.dom.classList : get$5(element);
        if (classList.length === 0) {
          remove$7(element, "class");
        }
      };
      var remove$2 = function(element, clazz) {
        if (supports(element)) {
          var classList = element.dom.classList;
          classList.remove(clazz);
        } else {
          remove$3(element, clazz);
        }
        cleanClass(element);
      };
      var has = function(element, clazz) {
        return supports(element) && element.dom.classList.contains(clazz);
      };
      var css = function(namespace) {
        var dashNamespace = namespace.replace(/\./g, "-");
        var resolve2 = function(str) {
          return dashNamespace + "-" + str;
        };
        return { resolve: resolve2 };
      };
      var styles$1 = css("ephox-dragster");
      var resolve$1 = styles$1.resolve;
      var Blocker = function(options) {
        var settings = __assign({ layerClass: resolve$1("blocker") }, options);
        var div = SugarElement.fromTag("div");
        set$2(div, "role", "presentation");
        setAll(div, {
          position: "fixed",
          left: "0px",
          top: "0px",
          width: "100%",
          height: "100%"
        });
        add(div, resolve$1("blocker"));
        add(div, settings.layerClass);
        var element = constant(div);
        var destroy2 = function() {
          remove$5(div);
        };
        return {
          element,
          destroy: destroy2
        };
      };
      var compare = function(old, nu2) {
        return SugarPosition(nu2.left - old.left, nu2.top - old.top);
      };
      var extract = function(event) {
        return Optional.some(SugarPosition(event.x, event.y));
      };
      var mutate = function(mutation, info) {
        mutation.mutate(info.left, info.top);
      };
      var sink = function(dragApi, settings) {
        var blocker = Blocker(settings);
        var mdown = bind(blocker.element(), "mousedown", dragApi.forceDrop);
        var mup = bind(blocker.element(), "mouseup", dragApi.drop);
        var mmove = bind(blocker.element(), "mousemove", dragApi.move);
        var mout = bind(blocker.element(), "mouseout", dragApi.delayDrop);
        var destroy2 = function() {
          blocker.destroy();
          mup.unbind();
          mmove.unbind();
          mout.unbind();
          mdown.unbind();
        };
        var start = function(parent2) {
          append$1(parent2, blocker.element());
        };
        var stop = function() {
          remove$5(blocker.element());
        };
        return DragSink({
          element: blocker.element,
          start,
          stop,
          destroy: destroy2
        });
      };
      var MouseDrag = DragMode({
        compare,
        extract,
        sink,
        mutate
      });
      var transform$1 = function(mutation, settings) {
        if (settings === void 0) {
          settings = {};
        }
        var mode = settings.mode !== void 0 ? settings.mode : MouseDrag;
        return setup(mutation, mode, settings);
      };
      var closest = function(target) {
        return closest$1(target, "[contenteditable]");
      };
      var isEditable$1 = function(element, assumeEditable) {
        if (assumeEditable === void 0) {
          assumeEditable = false;
        }
        if (!detect$3().browser.isIE() && inBody(element)) {
          return element.dom.isContentEditable;
        } else {
          return closest(element).fold(constant(assumeEditable), function(editable) {
            return getRaw(editable) === "true";
          });
        }
      };
      var getRaw = function(element) {
        return element.dom.contentEditable;
      };
      var styles = css("ephox-snooker");
      var resolve = styles.resolve;
      var Mutation = function() {
        var events = create$4({
          drag: Event([
            "xDelta",
            "yDelta"
          ])
        });
        var mutate2 = function(x, y) {
          events.trigger.drag(x, y);
        };
        return {
          mutate: mutate2,
          events: events.registry
        };
      };
      var BarMutation = function() {
        var events = create$4({
          drag: Event([
            "xDelta",
            "yDelta",
            "target"
          ])
        });
        var target = Optional.none();
        var delegate = Mutation();
        delegate.events.drag.bind(function(event) {
          target.each(function(t) {
            events.trigger.drag(event.xDelta, event.yDelta, t);
          });
        });
        var assign = function(t) {
          target = Optional.some(t);
        };
        var get2 = function() {
          return target;
        };
        return {
          assign,
          get: get2,
          mutate: delegate.mutate,
          events: events.registry
        };
      };
      var col = function(column, x, y, w, h) {
        var bar = SugarElement.fromTag("div");
        setAll(bar, {
          position: "absolute",
          left: x - w / 2 + "px",
          top: y + "px",
          height: h + "px",
          width: w + "px"
        });
        setAll$1(bar, {
          "data-column": column,
          "role": "presentation"
        });
        return bar;
      };
      var row = function(r2, x, y, w, h) {
        var bar = SugarElement.fromTag("div");
        setAll(bar, {
          position: "absolute",
          left: x + "px",
          top: y - h / 2 + "px",
          height: h + "px",
          width: w + "px"
        });
        setAll$1(bar, {
          "data-row": r2,
          "role": "presentation"
        });
        return bar;
      };
      var resizeBar = resolve("resizer-bar");
      var resizeRowBar = resolve("resizer-rows");
      var resizeColBar = resolve("resizer-cols");
      var BAR_THICKNESS = 7;
      var resizableRows = function(warehouse, isResizable2) {
        return bind$2(warehouse.all, function(row2, i) {
          return isResizable2(row2.element) ? [i] : [];
        });
      };
      var resizableColumns = function(warehouse, isResizable2) {
        var resizableCols = [];
        range$1(warehouse.grid.columns, function(index) {
          var colElmOpt = Warehouse.getColumnAt(warehouse, index).map(function(col2) {
            return col2.element;
          });
          if (colElmOpt.forall(isResizable2)) {
            resizableCols.push(index);
          }
        });
        return filter$2(resizableCols, function(colIndex) {
          var columnCells = Warehouse.filterItems(warehouse, function(cell2) {
            return cell2.column === colIndex;
          });
          return forall(columnCells, function(cell2) {
            return isResizable2(cell2.element);
          });
        });
      };
      var destroy = function(wire) {
        var previous = descendants(wire.parent(), "." + resizeBar);
        each$2(previous, remove$5);
      };
      var drawBar = function(wire, positions, create2) {
        var origin = wire.origin();
        each$2(positions, function(cpOption) {
          cpOption.each(function(cp) {
            var bar = create2(origin, cp);
            add(bar, resizeBar);
            append$1(wire.parent(), bar);
          });
        });
      };
      var refreshCol = function(wire, colPositions, position, tableHeight) {
        drawBar(wire, colPositions, function(origin, cp) {
          var colBar = col(cp.col, cp.x - origin.left, position.top - origin.top, BAR_THICKNESS, tableHeight);
          add(colBar, resizeColBar);
          return colBar;
        });
      };
      var refreshRow = function(wire, rowPositions, position, tableWidth) {
        drawBar(wire, rowPositions, function(origin, cp) {
          var rowBar = row(cp.row, position.left - origin.left, cp.y - origin.top, tableWidth, BAR_THICKNESS);
          add(rowBar, resizeRowBar);
          return rowBar;
        });
      };
      var refreshGrid = function(warhouse, wire, table2, rows2, cols) {
        var position = absolute(table2);
        var isResizable2 = wire.isResizable;
        var rowPositions = rows2.length > 0 ? height.positions(rows2, table2) : [];
        var resizableRowBars = rowPositions.length > 0 ? resizableRows(warhouse, isResizable2) : [];
        var resizableRowPositions = filter$2(rowPositions, function(_pos, i) {
          return exists(resizableRowBars, function(barIndex) {
            return i === barIndex;
          });
        });
        refreshRow(wire, resizableRowPositions, position, getOuter$2(table2));
        var colPositions = cols.length > 0 ? width.positions(cols, table2) : [];
        var resizableColBars = colPositions.length > 0 ? resizableColumns(warhouse, isResizable2) : [];
        var resizableColPositions = filter$2(colPositions, function(_pos, i) {
          return exists(resizableColBars, function(barIndex) {
            return i === barIndex;
          });
        });
        refreshCol(wire, resizableColPositions, position, getOuter$1(table2));
      };
      var refresh = function(wire, table2) {
        destroy(wire);
        if (wire.isResizable(table2)) {
          var warehouse = Warehouse.fromTable(table2);
          var rows$12 = rows(warehouse);
          var cols = columns(warehouse);
          refreshGrid(warehouse, wire, table2, rows$12, cols);
        }
      };
      var each = function(wire, f) {
        var bars = descendants(wire.parent(), "." + resizeBar);
        each$2(bars, f);
      };
      var hide = function(wire) {
        each(wire, function(bar) {
          set$1(bar, "display", "none");
        });
      };
      var show = function(wire) {
        each(wire, function(bar) {
          set$1(bar, "display", "block");
        });
      };
      var isRowBar = function(element) {
        return has(element, resizeRowBar);
      };
      var isColBar = function(element) {
        return has(element, resizeColBar);
      };
      var resizeBarDragging = resolve("resizer-bar-dragging");
      var BarManager = function(wire) {
        var mutation = BarMutation();
        var resizing = transform$1(mutation, {});
        var hoverTable = Optional.none();
        var getResizer = function(element, type2) {
          return Optional.from(get$b(element, type2));
        };
        mutation.events.drag.bind(function(event) {
          getResizer(event.target, "data-row").each(function(_dataRow) {
            var currentRow = getCssValue(event.target, "top");
            set$1(event.target, "top", currentRow + event.yDelta + "px");
          });
          getResizer(event.target, "data-column").each(function(_dataCol) {
            var currentCol = getCssValue(event.target, "left");
            set$1(event.target, "left", currentCol + event.xDelta + "px");
          });
        });
        var getDelta = function(target, dir) {
          var newX = getCssValue(target, dir);
          var oldX = getAttrValue(target, "data-initial-" + dir, 0);
          return newX - oldX;
        };
        resizing.events.stop.bind(function() {
          mutation.get().each(function(target) {
            hoverTable.each(function(table2) {
              getResizer(target, "data-row").each(function(row2) {
                var delta = getDelta(target, "top");
                remove$7(target, "data-initial-top");
                events.trigger.adjustHeight(table2, delta, parseInt(row2, 10));
              });
              getResizer(target, "data-column").each(function(column) {
                var delta = getDelta(target, "left");
                remove$7(target, "data-initial-left");
                events.trigger.adjustWidth(table2, delta, parseInt(column, 10));
              });
              refresh(wire, table2);
            });
          });
        });
        var handler = function(target, dir) {
          events.trigger.startAdjust();
          mutation.assign(target);
          set$2(target, "data-initial-" + dir, getCssValue(target, dir));
          add(target, resizeBarDragging);
          set$1(target, "opacity", "0.2");
          resizing.go(wire.parent());
        };
        var mousedown = bind(wire.parent(), "mousedown", function(event) {
          if (isRowBar(event.target)) {
            handler(event.target, "top");
          }
          if (isColBar(event.target)) {
            handler(event.target, "left");
          }
        });
        var isRoot = function(e) {
          return eq$1(e, wire.view());
        };
        var findClosestEditableTable = function(target) {
          return closest$1(target, "table", isRoot).filter(isEditable$1);
        };
        var mouseover = bind(wire.view(), "mouseover", function(event) {
          findClosestEditableTable(event.target).fold(function() {
            if (inBody(event.target)) {
              destroy(wire);
            }
          }, function(table2) {
            hoverTable = Optional.some(table2);
            refresh(wire, table2);
          });
        });
        var destroy$1 = function() {
          mousedown.unbind();
          mouseover.unbind();
          resizing.destroy();
          destroy(wire);
        };
        var refresh$1 = function(tbl) {
          refresh(wire, tbl);
        };
        var events = create$4({
          adjustHeight: Event([
            "table",
            "delta",
            "row"
          ]),
          adjustWidth: Event([
            "table",
            "delta",
            "column"
          ]),
          startAdjust: Event([])
        });
        return {
          destroy: destroy$1,
          refresh: refresh$1,
          on: resizing.on,
          off: resizing.off,
          hideBars: curry(hide, wire),
          showBars: curry(show, wire),
          events: events.registry
        };
      };
      var create$3 = function(wire, resizing, lazySizing) {
        var hdirection = height;
        var vdirection = width;
        var manager = BarManager(wire);
        var events = create$4({
          beforeResize: Event([
            "table",
            "type"
          ]),
          afterResize: Event([
            "table",
            "type"
          ]),
          startDrag: Event([])
        });
        manager.events.adjustHeight.bind(function(event) {
          var table2 = event.table;
          events.trigger.beforeResize(table2, "row");
          var delta = hdirection.delta(event.delta, table2);
          adjustHeight(table2, delta, event.row, hdirection);
          events.trigger.afterResize(table2, "row");
        });
        manager.events.startAdjust.bind(function(_event) {
          events.trigger.startDrag();
        });
        manager.events.adjustWidth.bind(function(event) {
          var table2 = event.table;
          events.trigger.beforeResize(table2, "col");
          var delta = vdirection.delta(event.delta, table2);
          var tableSize = lazySizing(table2);
          adjustWidth(table2, delta, event.column, resizing, tableSize);
          events.trigger.afterResize(table2, "col");
        });
        return {
          on: manager.on,
          off: manager.off,
          hideBars: manager.hideBars,
          showBars: manager.showBars,
          destroy: manager.destroy,
          events: events.registry
        };
      };
      var TableResize = { create: create$3 };
      var fireNewRow = function(editor, row2) {
        return editor.fire("newrow", { node: row2 });
      };
      var fireNewCell = function(editor, cell2) {
        return editor.fire("newcell", { node: cell2 });
      };
      var fireObjectResizeStart = function(editor, target, width2, height2, origin) {
        editor.fire("ObjectResizeStart", {
          target,
          width: width2,
          height: height2,
          origin
        });
      };
      var fireObjectResized = function(editor, target, width2, height2, origin) {
        editor.fire("ObjectResized", {
          target,
          width: width2,
          height: height2,
          origin
        });
      };
      var fireTableSelectionChange = function(editor, cells2, start, finish, otherCells) {
        editor.fire("TableSelectionChange", {
          cells: cells2,
          start,
          finish,
          otherCells
        });
      };
      var fireTableSelectionClear = function(editor) {
        editor.fire("TableSelectionClear");
      };
      var fireTableModified = function(editor, table2, data) {
        editor.fire("TableModified", __assign(__assign({}, data), { table: table2 }));
      };
      var styleModified = {
        structure: false,
        style: true
      };
      var structureModified = {
        structure: true,
        style: false
      };
      var styleAndStructureModified = {
        structure: true,
        style: true
      };
      var defaultTableToolbar = "tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol";
      var defaultStyles = {
        "border-collapse": "collapse",
        "width": "100%"
      };
      var defaultCellBorderWidths = range$1(5, function(i) {
        var size2 = i + 1 + "px";
        return {
          title: size2,
          value: size2
        };
      });
      var defaultCellBorderStyles = map$1([
        "Solid",
        "Dotted",
        "Dashed",
        "Double",
        "Groove",
        "Ridge",
        "Inset",
        "Outset",
        "None",
        "Hidden"
      ], function(type2) {
        return {
          title: type2,
          value: type2.toLowerCase()
        };
      });
      var determineDefaultStyles = function(editor) {
        var _a;
        if (isPixelsForced(editor)) {
          var dom = editor.dom;
          var parentBlock = (_a = dom.getParent(editor.selection.getStart(), dom.isBlock)) !== null && _a !== void 0 ? _a : editor.getBody();
          var contentWidth = getInner(SugarElement.fromDom(parentBlock));
          return __assign(__assign({}, defaultStyles), { width: contentWidth + "px" });
        } else if (isResponsiveForced(editor)) {
          return filter$1(defaultStyles, function(_value, key2) {
            return key2 !== "width";
          });
        } else {
          return defaultStyles;
        }
      };
      var defaultAttributes = { border: "1" };
      var defaultColumnResizingBehaviour = "preservetable";
      var getTableSizingMode = function(editor) {
        return editor.getParam("table_sizing_mode", "auto");
      };
      var getTableResponseWidth = function(editor) {
        return editor.getParam("table_responsive_width");
      };
      var getTableBorderWidths = function(editor) {
        return editor.getParam("table_border_widths", defaultCellBorderWidths, "array");
      };
      var getTableBorderStyles = function(editor) {
        return editor.getParam("table_border_styles", defaultCellBorderStyles, "array");
      };
      var getDefaultAttributes = function(editor) {
        return editor.getParam("table_default_attributes", defaultAttributes, "object");
      };
      var getDefaultStyles = function(editor) {
        return editor.getParam("table_default_styles", determineDefaultStyles(editor), "object");
      };
      var hasTableResizeBars = function(editor) {
        return editor.getParam("table_resize_bars", true, "boolean");
      };
      var hasTabNavigation = function(editor) {
        return editor.getParam("table_tab_navigation", true, "boolean");
      };
      var hasAdvancedCellTab = function(editor) {
        return editor.getParam("table_cell_advtab", true, "boolean");
      };
      var hasAdvancedRowTab = function(editor) {
        return editor.getParam("table_row_advtab", true, "boolean");
      };
      var hasAdvancedTableTab = function(editor) {
        return editor.getParam("table_advtab", true, "boolean");
      };
      var hasAppearanceOptions = function(editor) {
        return editor.getParam("table_appearance_options", true, "boolean");
      };
      var hasTableGrid = function(editor) {
        return editor.getParam("table_grid", true, "boolean");
      };
      var shouldStyleWithCss = function(editor) {
        return editor.getParam("table_style_by_css", false, "boolean");
      };
      var getCellClassList = function(editor) {
        return editor.getParam("table_cell_class_list", [], "array");
      };
      var getRowClassList = function(editor) {
        return editor.getParam("table_row_class_list", [], "array");
      };
      var getTableClassList = function(editor) {
        return editor.getParam("table_class_list", [], "array");
      };
      var isPercentagesForced = function(editor) {
        return getTableSizingMode(editor) === "relative" || getTableResponseWidth(editor) === true;
      };
      var isPixelsForced = function(editor) {
        return getTableSizingMode(editor) === "fixed" || getTableResponseWidth(editor) === false;
      };
      var isResponsiveForced = function(editor) {
        return getTableSizingMode(editor) === "responsive";
      };
      var getToolbar = function(editor) {
        return editor.getParam("table_toolbar", defaultTableToolbar);
      };
      var useColumnGroup = function(editor) {
        return editor.getParam("table_use_colgroups", false, "boolean");
      };
      var getTableHeaderType = function(editor) {
        var defaultValue = "section";
        var value2 = editor.getParam("table_header_type", defaultValue, "string");
        var validValues = [
          "section",
          "cells",
          "sectionCells",
          "auto"
        ];
        if (!contains$2(validValues, value2)) {
          return defaultValue;
        } else {
          return value2;
        }
      };
      var getColumnResizingBehaviour = function(editor) {
        var validModes = [
          "preservetable",
          "resizetable"
        ];
        var givenMode = editor.getParam("table_column_resizing", defaultColumnResizingBehaviour, "string");
        return find$1(validModes, function(mode) {
          return mode === givenMode;
        }).getOr(defaultColumnResizingBehaviour);
      };
      var isPreserveTableColumnResizing = function(editor) {
        return getColumnResizingBehaviour(editor) === "preservetable";
      };
      var isResizeTableColumnResizing = function(editor) {
        return getColumnResizingBehaviour(editor) === "resizetable";
      };
      var getCloneElements = function(editor) {
        var cloneElements = editor.getParam("table_clone_elements");
        if (isString(cloneElements)) {
          return Optional.some(cloneElements.split(/[ ,]/));
        } else if (Array.isArray(cloneElements)) {
          return Optional.some(cloneElements);
        } else {
          return Optional.none();
        }
      };
      var hasObjectResizing = function(editor) {
        var objectResizing = editor.getParam("object_resizing", true);
        return isString(objectResizing) ? objectResizing === "table" : objectResizing;
      };
      var getTableBackgroundColorMap = function(editor) {
        return editor.getParam("table_background_color_map", [], "array");
      };
      var getTableBorderColorMap = function(editor) {
        return editor.getParam("table_border_color_map", [], "array");
      };
      var get$4 = function(editor, table2) {
        if (isPercentagesForced(editor)) {
          return TableSize.percentageSize(table2);
        } else if (isPixelsForced(editor)) {
          return TableSize.pixelSize(table2);
        } else {
          return TableSize.getTableSize(table2);
        }
      };
      var cleanupLegacyAttributes = function(element) {
        remove$7(element, "width");
      };
      var convertToPercentSize = function(table2) {
        var newWidth = getPercentTableWidth(table2);
        redistribute(table2, Optional.some(newWidth), Optional.none());
        cleanupLegacyAttributes(table2);
      };
      var convertToPixelSize = function(table2) {
        var newWidth = getPixelTableWidth(table2);
        redistribute(table2, Optional.some(newWidth), Optional.none());
        cleanupLegacyAttributes(table2);
      };
      var convertToNoneSize = function(table2) {
        remove$6(table2, "width");
        var columns2 = columns$1(table2);
        var rowElements = columns2.length > 0 ? columns2 : cells$1(table2);
        each$2(rowElements, function(cell2) {
          remove$6(cell2, "width");
          cleanupLegacyAttributes(cell2);
        });
        cleanupLegacyAttributes(table2);
      };
      var enforcePercentage = convertToPercentSize;
      var enforcePixels = convertToPixelSize;
      var enforceNone = convertToNoneSize;
      var syncPixels = function(table2) {
        var warehouse = Warehouse.fromTable(table2);
        if (!Warehouse.hasColumns(warehouse)) {
          each$2(cells$1(table2), function(cell2) {
            var computedWidth = get$a(cell2, "width");
            set$1(cell2, "width", computedWidth);
            remove$7(cell2, "width");
          });
        }
      };
      var createContainer = function() {
        var container = SugarElement.fromTag("div");
        setAll(container, {
          position: "static",
          height: "0",
          width: "0",
          padding: "0",
          margin: "0",
          border: "0"
        });
        append$1(body$1(), container);
        return container;
      };
      var get$3 = function(editor, isResizable2) {
        return editor.inline ? ResizeWire.body(getBody(editor), createContainer(), isResizable2) : ResizeWire.only(SugarElement.fromDom(editor.getDoc()), isResizable2);
      };
      var remove$1 = function(editor, wire) {
        if (editor.inline) {
          remove$5(wire.parent());
        }
      };
      var barResizerPrefix = "bar-";
      var isResizable = function(elm) {
        return get$b(elm, "data-mce-resize") !== "false";
      };
      var getResizeHandler = function(editor) {
        var selectionRng = Optional.none();
        var resize2 = Optional.none();
        var wire = Optional.none();
        var startW;
        var startRawW;
        var isTable = function(elm) {
          return elm.nodeName === "TABLE";
        };
        var lazyResize = function() {
          return resize2;
        };
        var lazyWire = function() {
          return wire.getOr(ResizeWire.only(SugarElement.fromDom(editor.getBody()), isResizable));
        };
        var lazySizing = function(table2) {
          return get$4(editor, table2);
        };
        var lazyResizingBehaviour = function() {
          return isPreserveTableColumnResizing(editor) ? preserveTable() : resizeTable();
        };
        var getNumColumns = function(table2) {
          return getGridSize(table2).columns;
        };
        var afterCornerResize = function(table2, origin, width2) {
          var isRightEdgeResize = endsWith(origin, "e");
          if (startRawW === "") {
            enforcePercentage(table2);
          }
          if (width2 !== startW && startRawW !== "") {
            set$1(table2, "width", startRawW);
            var resizing = lazyResizingBehaviour();
            var tableSize = lazySizing(table2);
            var col2 = isPreserveTableColumnResizing(editor) || isRightEdgeResize ? getNumColumns(table2) - 1 : 0;
            adjustWidth(table2, width2 - startW, col2, resizing, tableSize);
          } else if (isPercentage$1(startRawW)) {
            var percentW = parseFloat(startRawW.replace("%", ""));
            var targetPercentW = width2 * percentW / startW;
            set$1(table2, "width", targetPercentW + "%");
          }
          if (isPixel(startRawW)) {
            syncPixels(table2);
          }
        };
        var destroy2 = function() {
          resize2.each(function(sz) {
            sz.destroy();
          });
          wire.each(function(w) {
            remove$1(editor, w);
          });
        };
        editor.on("init", function() {
          var rawWire = get$3(editor, isResizable);
          wire = Optional.some(rawWire);
          if (hasObjectResizing(editor) && hasTableResizeBars(editor)) {
            var resizing = lazyResizingBehaviour();
            var sz = TableResize.create(rawWire, resizing, lazySizing);
            sz.on();
            sz.events.startDrag.bind(function(_event) {
              selectionRng = Optional.some(editor.selection.getRng());
            });
            sz.events.beforeResize.bind(function(event) {
              var rawTable = event.table.dom;
              fireObjectResizeStart(editor, rawTable, getPixelWidth(rawTable), getPixelHeight(rawTable), barResizerPrefix + event.type);
            });
            sz.events.afterResize.bind(function(event) {
              var table2 = event.table;
              var rawTable = table2.dom;
              removeDataStyle(table2);
              selectionRng.each(function(rng) {
                editor.selection.setRng(rng);
                editor.focus();
              });
              fireObjectResized(editor, rawTable, getPixelWidth(rawTable), getPixelHeight(rawTable), barResizerPrefix + event.type);
              editor.undoManager.add();
            });
            resize2 = Optional.some(sz);
          }
        });
        editor.on("ObjectResizeStart", function(e) {
          var targetElm = e.target;
          if (isTable(targetElm)) {
            var table2 = SugarElement.fromDom(targetElm);
            each$2(editor.dom.select(".mce-clonedresizable"), function(clone2) {
              editor.dom.addClass(clone2, "mce-" + getColumnResizingBehaviour(editor) + "-columns");
            });
            if (!isPixelSizing(table2) && isPixelsForced(editor)) {
              enforcePixels(table2);
            } else if (!isPercentSizing(table2) && isPercentagesForced(editor)) {
              enforcePercentage(table2);
            }
            if (isNoneSizing(table2) && startsWith(e.origin, barResizerPrefix)) {
              enforcePercentage(table2);
            }
            startW = e.width;
            startRawW = isResponsiveForced(editor) ? "" : getRawWidth(editor, targetElm).getOr("");
          }
        });
        editor.on("ObjectResized", function(e) {
          var targetElm = e.target;
          if (isTable(targetElm)) {
            var table2 = SugarElement.fromDom(targetElm);
            var origin_1 = e.origin;
            if (startsWith(origin_1, "corner-")) {
              afterCornerResize(table2, origin_1, e.width);
            }
            removeDataStyle(table2);
            fireTableModified(editor, table2.dom, styleModified);
          }
        });
        editor.on("SwitchMode", function() {
          lazyResize().each(function(resize3) {
            if (editor.mode.isReadOnly()) {
              resize3.hideBars();
            } else {
              resize3.showBars();
            }
          });
        });
        return {
          lazyResize,
          lazyWire,
          destroy: destroy2
        };
      };
      var point = function(element, offset) {
        return {
          element,
          offset
        };
      };
      var scan$1 = function(universe2, element, direction) {
        if (universe2.property().isText(element) && universe2.property().getText(element).trim().length === 0 || universe2.property().isComment(element)) {
          return direction(element).bind(function(elem) {
            return scan$1(universe2, elem, direction).orThunk(function() {
              return Optional.some(elem);
            });
          });
        } else {
          return Optional.none();
        }
      };
      var toEnd = function(universe2, element) {
        if (universe2.property().isText(element)) {
          return universe2.property().getText(element).length;
        }
        var children2 = universe2.property().children(element);
        return children2.length;
      };
      var freefallRtl$2 = function(universe2, element) {
        var candidate = scan$1(universe2, element, universe2.query().prevSibling).getOr(element);
        if (universe2.property().isText(candidate)) {
          return point(candidate, toEnd(universe2, candidate));
        }
        var children2 = universe2.property().children(candidate);
        return children2.length > 0 ? freefallRtl$2(universe2, children2[children2.length - 1]) : point(candidate, toEnd(universe2, candidate));
      };
      var freefallRtl$1 = freefallRtl$2;
      var universe$2 = DomUniverse();
      var freefallRtl = function(element) {
        return freefallRtl$1(universe$2, element);
      };
      var halve = function(main, other) {
        var colspan = getSpan(main, "colspan");
        if (colspan === 1) {
          var width2 = getGenericWidth(main);
          width2.each(function(w) {
            var newWidth = w.value / 2;
            setGenericWidth(main, newWidth, w.unit);
            setGenericWidth(other, newWidth, w.unit);
          });
        }
      };
      var isHeaderCell = isTag("th");
      var isHeaderCells = function(cells2) {
        return forall(cells2, function(cell2) {
          return isHeaderCell(cell2.element);
        });
      };
      var getRowHeaderType = function(isHeaderRow, isHeaderCells2) {
        if (isHeaderRow && isHeaderCells2) {
          return "sectionCells";
        } else if (isHeaderRow) {
          return "section";
        } else {
          return "cells";
        }
      };
      var getRowType$1 = function(row2) {
        var isHeaderRow = row2.section === "thead";
        var isHeaderCells2 = is(findCommonCellType(row2.cells), "th");
        if (isHeaderRow || isHeaderCells2) {
          return {
            type: "header",
            subType: getRowHeaderType(isHeaderRow, isHeaderCells2)
          };
        } else if (row2.section === "tfoot") {
          return { type: "footer" };
        } else {
          return { type: "body" };
        }
      };
      var findCommonCellType = function(cells2) {
        var headerCells = filter$2(cells2, function(cell2) {
          return isHeaderCell(cell2.element);
        });
        if (headerCells.length === 0) {
          return Optional.some("td");
        } else if (headerCells.length === cells2.length) {
          return Optional.some("th");
        } else {
          return Optional.none();
        }
      };
      var findCommonRowType = function(rows2) {
        var rowTypes = map$1(rows2, function(row2) {
          return getRowType$1(row2).type;
        });
        var hasHeader = contains$2(rowTypes, "header");
        var hasFooter = contains$2(rowTypes, "footer");
        if (!hasHeader && !hasFooter) {
          return Optional.some("body");
        } else {
          var hasBody = contains$2(rowTypes, "body");
          if (hasHeader && !hasBody && !hasFooter) {
            return Optional.some("header");
          } else if (!hasHeader && !hasBody && hasFooter) {
            return Optional.some("footer");
          } else {
            return Optional.none();
          }
        }
      };
      var findTableRowHeaderType = function(warehouse) {
        return findMap(warehouse.all, function(row2) {
          var rowType = getRowType$1(row2);
          return rowType.type === "header" ? Optional.from(rowType.subType) : Optional.none();
        });
      };
      var transformCell = function(cell2, comparator, substitution) {
        return elementnew(substitution(cell2.element, comparator), true, cell2.isLocked);
      };
      var transformRow = function(row2, section2) {
        return row2.section !== section2 ? rowcells(row2.element, row2.cells, section2, row2.isNew) : row2;
      };
      var section = function() {
        return {
          transformRow,
          transformCell: function(cell2, comparator, substitution) {
            var newCell = substitution(cell2.element, comparator);
            var fixedCell = name(newCell) !== "td" ? mutate$1(newCell, "td") : newCell;
            return elementnew(fixedCell, cell2.isNew, cell2.isLocked);
          }
        };
      };
      var sectionCells = function() {
        return {
          transformRow,
          transformCell
        };
      };
      var cells = function() {
        return {
          transformRow: function(row2, section2) {
            var newSection = section2 === "thead" ? "tbody" : section2;
            return transformRow(row2, newSection);
          },
          transformCell
        };
      };
      var fallback = function() {
        return {
          transformRow: identity,
          transformCell
        };
      };
      var getTableSectionType = function(table2, fallback2) {
        var warehouse = Warehouse.fromTable(table2);
        var type2 = findTableRowHeaderType(warehouse).getOr(fallback2);
        switch (type2) {
          case "section":
            return section();
          case "sectionCells":
            return sectionCells();
          case "cells":
            return cells();
        }
      };
      var TableSection = {
        getTableSectionType,
        section,
        sectionCells,
        cells,
        fallback
      };
      var setIfNot = function(element, property, value2, ignore) {
        if (value2 === ignore) {
          remove$7(element, property);
        } else {
          set$2(element, property, value2);
        }
      };
      var insert$1 = function(table2, selector, element) {
        last$2(children$1(table2, selector)).fold(function() {
          return prepend(table2, element);
        }, function(child2) {
          return after$5(child2, element);
        });
      };
      var generateSection = function(table2, sectionName) {
        var section2 = child$1(table2, sectionName).getOrThunk(function() {
          var newSection = SugarElement.fromTag(sectionName, owner(table2).dom);
          if (sectionName === "thead") {
            insert$1(table2, "caption,colgroup", newSection);
          } else if (sectionName === "colgroup") {
            insert$1(table2, "caption", newSection);
          } else {
            append$1(table2, newSection);
          }
          return newSection;
        });
        empty(section2);
        return section2;
      };
      var render$1 = function(table2, grid2) {
        var newRows = [];
        var newCells = [];
        var syncRows = function(gridSection) {
          return map$1(gridSection, function(row2) {
            if (row2.isNew) {
              newRows.push(row2.element);
            }
            var tr = row2.element;
            empty(tr);
            each$2(row2.cells, function(cell2) {
              if (cell2.isNew) {
                newCells.push(cell2.element);
              }
              setIfNot(cell2.element, "colspan", cell2.colspan, 1);
              setIfNot(cell2.element, "rowspan", cell2.rowspan, 1);
              append$1(tr, cell2.element);
            });
            return tr;
          });
        };
        var syncColGroup = function(gridSection) {
          return bind$2(gridSection, function(colGroup) {
            return map$1(colGroup.cells, function(col2) {
              setIfNot(col2.element, "span", col2.colspan, 1);
              return col2.element;
            });
          });
        };
        var renderSection = function(gridSection, sectionName) {
          var section2 = generateSection(table2, sectionName);
          var sync2 = sectionName === "colgroup" ? syncColGroup : syncRows;
          var sectionElems = sync2(gridSection);
          append(section2, sectionElems);
        };
        var removeSection = function(sectionName) {
          child$1(table2, sectionName).each(remove$5);
        };
        var renderOrRemoveSection = function(gridSection, sectionName) {
          if (gridSection.length > 0) {
            renderSection(gridSection, sectionName);
          } else {
            removeSection(sectionName);
          }
        };
        var headSection = [];
        var bodySection = [];
        var footSection = [];
        var columnGroupsSection = [];
        each$2(grid2, function(row2) {
          switch (row2.section) {
            case "thead":
              headSection.push(row2);
              break;
            case "tbody":
              bodySection.push(row2);
              break;
            case "tfoot":
              footSection.push(row2);
              break;
            case "colgroup":
              columnGroupsSection.push(row2);
              break;
          }
        });
        renderOrRemoveSection(columnGroupsSection, "colgroup");
        renderOrRemoveSection(headSection, "thead");
        renderOrRemoveSection(bodySection, "tbody");
        renderOrRemoveSection(footSection, "tfoot");
        return {
          newRows,
          newCells
        };
      };
      var copy = function(grid2) {
        return map$1(grid2, function(row2) {
          var tr = shallow(row2.element);
          each$2(row2.cells, function(cell2) {
            var clonedCell = deep(cell2.element);
            setIfNot(clonedCell, "colspan", cell2.colspan, 1);
            setIfNot(clonedCell, "rowspan", cell2.rowspan, 1);
            append$1(tr, clonedCell);
          });
          return tr;
        });
      };
      var getColumn = function(grid2, index) {
        return map$1(grid2, function(row2) {
          return getCell(row2, index);
        });
      };
      var getRow = function(grid2, index) {
        return grid2[index];
      };
      var findDiff = function(xs, comp) {
        if (xs.length === 0) {
          return 0;
        }
        var first2 = xs[0];
        var index = findIndex(xs, function(x) {
          return !comp(first2.element, x.element);
        });
        return index.getOr(xs.length);
      };
      var subgrid = function(grid2, row2, column, comparator) {
        var gridRow = getRow(grid2, row2);
        var isColRow = gridRow.section === "colgroup";
        var colspan = findDiff(gridRow.cells.slice(column), comparator);
        var rowspan = isColRow ? 1 : findDiff(getColumn(grid2.slice(row2), column), comparator);
        return {
          colspan,
          rowspan
        };
      };
      var toDetails = function(grid2, comparator) {
        var seen = map$1(grid2, function(row2) {
          return map$1(row2.cells, never);
        });
        var updateSeen = function(rowIndex, columnIndex, rowspan, colspan) {
          for (var row2 = rowIndex; row2 < rowIndex + rowspan; row2++) {
            for (var column = columnIndex; column < columnIndex + colspan; column++) {
              seen[row2][column] = true;
            }
          }
        };
        return map$1(grid2, function(row2, rowIndex) {
          var details = bind$2(row2.cells, function(cell2, columnIndex) {
            if (seen[rowIndex][columnIndex] === false) {
              var result = subgrid(grid2, rowIndex, columnIndex, comparator);
              updateSeen(rowIndex, columnIndex, result.rowspan, result.colspan);
              return [detailnew(cell2.element, result.rowspan, result.colspan, cell2.isNew)];
            } else {
              return [];
            }
          });
          return rowdetailnew(row2.element, details, row2.section, row2.isNew);
        });
      };
      var toGrid = function(warehouse, generators, isNew) {
        var grid2 = [];
        each$2(warehouse.colgroups, function(colgroup2) {
          var colgroupCols = [];
          for (var columnIndex2 = 0; columnIndex2 < warehouse.grid.columns; columnIndex2++) {
            var element2 = Warehouse.getColumnAt(warehouse, columnIndex2).map(function(column) {
              return elementnew(column.element, isNew, false);
            }).getOrThunk(function() {
              return elementnew(generators.colGap(), true, false);
            });
            colgroupCols.push(element2);
          }
          grid2.push(rowcells(colgroup2.element, colgroupCols, "colgroup", isNew));
        });
        for (var rowIndex = 0; rowIndex < warehouse.grid.rows; rowIndex++) {
          var rowCells = [];
          for (var columnIndex = 0; columnIndex < warehouse.grid.columns; columnIndex++) {
            var element = Warehouse.getAt(warehouse, rowIndex, columnIndex).map(function(item) {
              return elementnew(item.element, isNew, item.isLocked);
            }).getOrThunk(function() {
              return elementnew(generators.gap(), true, false);
            });
            rowCells.push(element);
          }
          var rowDetail = warehouse.all[rowIndex];
          var row2 = rowcells(rowDetail.element, rowCells, rowDetail.section, isNew);
          grid2.push(row2);
        }
        return grid2;
      };
      var fromWarehouse = function(warehouse, generators) {
        return toGrid(warehouse, generators, false);
      };
      var toDetailList = function(grid2) {
        return toDetails(grid2, eq$1);
      };
      var findInWarehouse = function(warehouse, element) {
        return findMap(warehouse.all, function(r2) {
          return find$1(r2.cells, function(e) {
            return eq$1(element, e.element);
          });
        });
      };
      var extractCells = function(warehouse, target, predicate) {
        var details = map$1(target.selection, function(cell$1) {
          return cell(cell$1).bind(function(lc) {
            return findInWarehouse(warehouse, lc);
          }).filter(predicate);
        });
        var cells2 = cat(details);
        return someIf(cells2.length > 0, cells2);
      };
      var run = function(operation, extract2, adjustment, postAction, genWrappers) {
        return function(wire, table2, target, generators, behaviours) {
          var warehouse = Warehouse.fromTable(table2);
          var tableSection = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.section).getOrThunk(TableSection.fallback);
          var output = extract2(warehouse, target).map(function(info) {
            var model = fromWarehouse(warehouse, generators);
            var result = operation(model, info, eq$1, genWrappers(generators), tableSection);
            var lockedColumns = getLockedColumnsFromGrid(result.grid);
            var grid2 = toDetailList(result.grid);
            return {
              info,
              grid: grid2,
              cursor: result.cursor,
              lockedColumns
            };
          });
          return output.bind(function(out) {
            var newElements = render$1(table2, out.grid);
            var tableSizing = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.sizing).getOrThunk(function() {
              return TableSize.getTableSize(table2);
            });
            var resizing = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.resize).getOrThunk(preserveTable);
            adjustment(table2, out.grid, out.info, {
              sizing: tableSizing,
              resize: resizing,
              section: tableSection
            });
            postAction(table2);
            refresh(wire, table2);
            remove$7(table2, LOCKED_COL_ATTR);
            if (out.lockedColumns.length > 0) {
              set$2(table2, LOCKED_COL_ATTR, out.lockedColumns.join(","));
            }
            return Optional.some({
              cursor: out.cursor,
              newRows: newElements.newRows,
              newCells: newElements.newCells
            });
          });
        };
      };
      var onPaste = function(warehouse, target) {
        return cell(target.element).bind(function(cell2) {
          return findInWarehouse(warehouse, cell2).map(function(details) {
            var value2 = __assign(__assign({}, details), {
              generators: target.generators,
              clipboard: target.clipboard
            });
            return value2;
          });
        });
      };
      var onPasteByEditor = function(warehouse, target) {
        return extractCells(warehouse, target, always).map(function(cells2) {
          return {
            cells: cells2,
            generators: target.generators,
            clipboard: target.clipboard
          };
        });
      };
      var onMergable = function(_warehouse, target) {
        return target.mergable;
      };
      var onUnmergable = function(_warehouse, target) {
        return target.unmergable;
      };
      var onCells = function(warehouse, target) {
        return extractCells(warehouse, target, always);
      };
      var onUnlockedCells = function(warehouse, target) {
        return extractCells(warehouse, target, function(detail2) {
          return !detail2.isLocked;
        });
      };
      var isUnlockedTableCell = function(warehouse, cell2) {
        return findInWarehouse(warehouse, cell2).exists(function(detail2) {
          return !detail2.isLocked;
        });
      };
      var allUnlocked = function(warehouse, cells2) {
        return forall(cells2, function(cell2) {
          return isUnlockedTableCell(warehouse, cell2);
        });
      };
      var onUnlockedMergable = function(warehouse, target) {
        return onMergable(warehouse, target).filter(function(mergeable) {
          return allUnlocked(warehouse, mergeable.cells);
        });
      };
      var onUnlockedUnmergable = function(warehouse, target) {
        return onUnmergable(warehouse, target).filter(function(cells2) {
          return allUnlocked(warehouse, cells2);
        });
      };
      var merge$2 = function(grid2, bounds2, comparator, substitution) {
        var rows2 = extractGridDetails(grid2).rows;
        if (rows2.length === 0) {
          return grid2;
        }
        for (var i = bounds2.startRow; i <= bounds2.finishRow; i++) {
          for (var j = bounds2.startCol; j <= bounds2.finishCol; j++) {
            var row2 = rows2[i];
            var isLocked = getCell(row2, j).isLocked;
            mutateCell(row2, j, elementnew(substitution(), false, isLocked));
          }
        }
        return grid2;
      };
      var unmerge = function(grid2, target, comparator, substitution) {
        var rows2 = extractGridDetails(grid2).rows;
        var first2 = true;
        for (var i = 0; i < rows2.length; i++) {
          for (var j = 0; j < cellLength(rows2[0]); j++) {
            var row2 = rows2[i];
            var currentCell = getCell(row2, j);
            var currentCellElm = currentCell.element;
            var isToReplace = comparator(currentCellElm, target);
            if (isToReplace === true && first2 === false) {
              mutateCell(row2, j, elementnew(substitution(), true, currentCell.isLocked));
            } else if (isToReplace === true) {
              first2 = false;
            }
          }
        }
        return grid2;
      };
      var uniqueCells = function(row2, comparator) {
        return foldl(row2, function(rest, cell2) {
          return exists(rest, function(currentCell) {
            return comparator(currentCell.element, cell2.element);
          }) ? rest : rest.concat([cell2]);
        }, []);
      };
      var splitCols = function(grid2, index, comparator, substitution) {
        if (index > 0 && index < grid2[0].cells.length) {
          each$2(grid2, function(row2) {
            var prevCell = row2.cells[index - 1];
            var current = row2.cells[index];
            var isToReplace = comparator(current.element, prevCell.element);
            if (isToReplace) {
              mutateCell(row2, index, elementnew(substitution(), true, current.isLocked));
            }
          });
        }
        return grid2;
      };
      var splitRows = function(grid2, index, comparator, substitution) {
        var rows2 = extractGridDetails(grid2).rows;
        if (index > 0 && index < rows2.length) {
          var rowPrevCells = rows2[index - 1].cells;
          var cells2 = uniqueCells(rowPrevCells, comparator);
          each$2(cells2, function(cell2) {
            var replacement = Optional.none();
            for (var i = index; i < rows2.length; i++) {
              var _loop_1 = function(j2) {
                var row2 = rows2[i];
                var current = getCell(row2, j2);
                var isToReplace = comparator(current.element, cell2.element);
                if (isToReplace) {
                  if (replacement.isNone()) {
                    replacement = Optional.some(substitution());
                  }
                  replacement.each(function(sub) {
                    mutateCell(row2, j2, elementnew(sub, true, current.isLocked));
                  });
                }
              };
              for (var j = 0; j < cellLength(rows2[0]); j++) {
                _loop_1(j);
              }
            }
          });
        }
        return grid2;
      };
      var value$1 = function(o) {
        var or = function(_opt) {
          return value$1(o);
        };
        var orThunk = function(_f) {
          return value$1(o);
        };
        var map2 = function(f) {
          return value$1(f(o));
        };
        var mapError = function(_f) {
          return value$1(o);
        };
        var each2 = function(f) {
          f(o);
        };
        var bind2 = function(f) {
          return f(o);
        };
        var fold = function(_, onValue) {
          return onValue(o);
        };
        var exists2 = function(f) {
          return f(o);
        };
        var forall2 = function(f) {
          return f(o);
        };
        var toOptional = function() {
          return Optional.some(o);
        };
        return {
          isValue: always,
          isError: never,
          getOr: constant(o),
          getOrThunk: constant(o),
          getOrDie: constant(o),
          or,
          orThunk,
          fold,
          map: map2,
          mapError,
          each: each2,
          bind: bind2,
          exists: exists2,
          forall: forall2,
          toOptional
        };
      };
      var error = function(message) {
        var getOrThunk = function(f) {
          return f();
        };
        var getOrDie = function() {
          return die(String(message))();
        };
        var or = identity;
        var orThunk = function(f) {
          return f();
        };
        var map2 = function(_f) {
          return error(message);
        };
        var mapError = function(f) {
          return error(f(message));
        };
        var bind2 = function(_f) {
          return error(message);
        };
        var fold = function(onError, _) {
          return onError(message);
        };
        return {
          isValue: never,
          isError: always,
          getOr: identity,
          getOrThunk,
          getOrDie,
          or,
          orThunk,
          fold,
          map: map2,
          mapError,
          each: noop,
          bind: bind2,
          exists: never,
          forall: always,
          toOptional: Optional.none
        };
      };
      var fromOption = function(opt, err) {
        return opt.fold(function() {
          return error(err);
        }, value$1);
      };
      var Result = {
        value: value$1,
        error,
        fromOption
      };
      var measure = function(startAddress, gridA, gridB) {
        if (startAddress.row >= gridA.length || startAddress.column > cellLength(gridA[0])) {
          return Result.error("invalid start address out of table bounds, row: " + startAddress.row + ", column: " + startAddress.column);
        }
        var rowRemainder = gridA.slice(startAddress.row);
        var colRemainder = rowRemainder[0].cells.slice(startAddress.column);
        var colRequired = cellLength(gridB[0]);
        var rowRequired = gridB.length;
        return Result.value({
          rowDelta: rowRemainder.length - rowRequired,
          colDelta: colRemainder.length - colRequired
        });
      };
      var measureWidth = function(gridA, gridB) {
        var colLengthA = cellLength(gridA[0]);
        var colLengthB = cellLength(gridB[0]);
        return {
          rowDelta: 0,
          colDelta: colLengthA - colLengthB
        };
      };
      var measureHeight = function(gridA, gridB) {
        var rowLengthA = gridA.length;
        var rowLengthB = gridB.length;
        return {
          rowDelta: rowLengthA - rowLengthB,
          colDelta: 0
        };
      };
      var generateElements = function(amount, row2, generators, isLocked) {
        var generator = row2.section === "colgroup" ? generators.col : generators.cell;
        return range$1(amount, function(idx) {
          return elementnew(generator(), true, isLocked(idx));
        });
      };
      var rowFill = function(grid2, amount, generators, lockedColumns) {
        var exampleRow = grid2[grid2.length - 1];
        return grid2.concat(range$1(amount, function() {
          var generator = exampleRow.section === "colgroup" ? generators.colgroup : generators.row;
          var row2 = clone$1(exampleRow, generator, identity);
          var elements = generateElements(row2.cells.length, row2, generators, function(idx) {
            return has$1(lockedColumns, idx.toString());
          });
          return setCells(row2, elements);
        }));
      };
      var colFill = function(grid2, amount, generators, startIndex) {
        return map$1(grid2, function(row2) {
          var newChildren = generateElements(amount, row2, generators, never);
          return addCells(row2, startIndex, newChildren);
        });
      };
      var lockedColFill = function(grid2, generators, lockedColumns) {
        return map$1(grid2, function(row2) {
          return foldl(lockedColumns, function(acc, colNum) {
            var newChild = generateElements(1, row2, generators, always)[0];
            return addCell(acc, colNum, newChild);
          }, row2);
        });
      };
      var tailor = function(gridA, delta, generators) {
        var fillCols = delta.colDelta < 0 ? colFill : identity;
        var fillRows = delta.rowDelta < 0 ? rowFill : identity;
        var lockedColumns = getLockedColumnsFromGrid(gridA);
        var gridWidth = cellLength(gridA[0]);
        var isLastColLocked = exists(lockedColumns, function(locked) {
          return locked === gridWidth - 1;
        });
        var modifiedCols = fillCols(gridA, Math.abs(delta.colDelta), generators, isLastColLocked ? gridWidth - 1 : gridWidth);
        var newLockedColumns = getLockedColumnsFromGrid(modifiedCols);
        return fillRows(modifiedCols, Math.abs(delta.rowDelta), generators, mapToObject(newLockedColumns, always));
      };
      var isSpanning = function(grid2, row2, col2, comparator) {
        var candidate = getCell(grid2[row2], col2);
        var matching = curry(comparator, candidate.element);
        var currentRow = grid2[row2];
        return grid2.length > 1 && cellLength(currentRow) > 1 && (col2 > 0 && matching(getCellElement(currentRow, col2 - 1)) || col2 < currentRow.cells.length - 1 && matching(getCellElement(currentRow, col2 + 1)) || row2 > 0 && matching(getCellElement(grid2[row2 - 1], col2)) || row2 < grid2.length - 1 && matching(getCellElement(grid2[row2 + 1], col2)));
      };
      var mergeTables = function(startAddress, gridA, gridB, generator, comparator, lockedColumns) {
        var startRow = startAddress.row;
        var startCol = startAddress.column;
        var mergeHeight = gridB.length;
        var mergeWidth = cellLength(gridB[0]);
        var endRow = startRow + mergeHeight;
        var endCol = startCol + mergeWidth + lockedColumns.length;
        var lockedColumnObj = mapToObject(lockedColumns, always);
        for (var r2 = startRow; r2 < endRow; r2++) {
          var skippedCol = 0;
          for (var c = startCol; c < endCol; c++) {
            if (lockedColumnObj[c]) {
              skippedCol++;
              continue;
            }
            if (isSpanning(gridA, r2, c, comparator)) {
              unmerge(gridA, getCellElement(gridA[r2], c), comparator, generator.cell);
            }
            var gridBColIndex = c - startCol - skippedCol;
            var newCell = getCell(gridB[r2 - startRow], gridBColIndex);
            var newCellElm = newCell.element;
            var replacement = generator.replace(newCellElm);
            mutateCell(gridA[r2], c, elementnew(replacement, true, newCell.isLocked));
          }
        }
        return gridA;
      };
      var getValidStartAddress = function(currentStartAddress, grid2, lockedColumns) {
        var gridColLength = cellLength(grid2[0]);
        var adjustedRowAddress = extractGridDetails(grid2).cols.length + currentStartAddress.row;
        var possibleColAddresses = range$1(gridColLength - currentStartAddress.column, function(num) {
          return num + currentStartAddress.column;
        });
        var validColAddress = find$1(possibleColAddresses, function(num) {
          return forall(lockedColumns, function(col2) {
            return col2 !== num;
          });
        }).getOr(gridColLength - 1);
        return {
          row: adjustedRowAddress,
          column: validColAddress
        };
      };
      var getLockedColumnsWithinBounds = function(startAddress, grid2, lockedColumns) {
        return filter$2(lockedColumns, function(colNum) {
          return colNum >= startAddress.column && colNum <= cellLength(grid2[0]) + startAddress.column;
        });
      };
      var merge$1 = function(startAddress, gridA, gridB, generator, comparator) {
        var lockedColumns = getLockedColumnsFromGrid(gridA);
        var validStartAddress = getValidStartAddress(startAddress, gridA, lockedColumns);
        var gridBRows = extractGridDetails(gridB).rows;
        var lockedColumnsWithinBounds = getLockedColumnsWithinBounds(validStartAddress, gridBRows, lockedColumns);
        var result = measure(validStartAddress, gridA, gridBRows);
        return result.map(function(diff) {
          var delta = __assign(__assign({}, diff), { colDelta: diff.colDelta - lockedColumnsWithinBounds.length });
          var fittedGrid = tailor(gridA, delta, generator);
          var newLockedColumns = getLockedColumnsFromGrid(fittedGrid);
          var newLockedColumnsWithinBounds = getLockedColumnsWithinBounds(validStartAddress, gridBRows, newLockedColumns);
          return mergeTables(validStartAddress, fittedGrid, gridBRows, generator, comparator, newLockedColumnsWithinBounds);
        });
      };
      var insertCols = function(index, gridA, gridB, generator, comparator) {
        splitCols(gridA, index, comparator, generator.cell);
        var delta = measureHeight(gridB, gridA);
        var fittedNewGrid = tailor(gridB, delta, generator);
        var secondDelta = measureHeight(gridA, fittedNewGrid);
        var fittedOldGrid = tailor(gridA, secondDelta, generator);
        return map$1(fittedOldGrid, function(gridRow, i) {
          return addCells(gridRow, index, fittedNewGrid[i].cells);
        });
      };
      var insertRows = function(index, gridA, gridB, generator, comparator) {
        splitRows(gridA, index, comparator, generator.cell);
        var locked = getLockedColumnsFromGrid(gridA);
        var diff = measureWidth(gridA, gridB);
        var delta = __assign(__assign({}, diff), { colDelta: diff.colDelta - locked.length });
        var fittedOldGrid = tailor(gridA, delta, generator);
        var _a = extractGridDetails(fittedOldGrid), oldCols = _a.cols, oldRows = _a.rows;
        var newLocked = getLockedColumnsFromGrid(fittedOldGrid);
        var secondDiff = measureWidth(gridB, gridA);
        var secondDelta = __assign(__assign({}, secondDiff), { colDelta: secondDiff.colDelta + newLocked.length });
        var fittedGridB = lockedColFill(gridB, generator, newLocked);
        var fittedNewGrid = tailor(fittedGridB, secondDelta, generator);
        return oldCols.concat(oldRows.slice(0, index)).concat(fittedNewGrid).concat(oldRows.slice(index, oldRows.length));
      };
      var cloneRow = function(row2, cloneCell, comparator, substitution) {
        return clone$1(row2, function(elem) {
          return substitution(elem, comparator);
        }, cloneCell);
      };
      var insertRowAt = function(grid2, index, example, comparator, substitution) {
        var _a = extractGridDetails(grid2), rows2 = _a.rows, cols = _a.cols;
        var before2 = rows2.slice(0, index);
        var after2 = rows2.slice(index);
        var newRow = cloneRow(rows2[example], function(ex, c) {
          var withinSpan = index > 0 && index < rows2.length && comparator(getCellElement(rows2[index - 1], c), getCellElement(rows2[index], c));
          var ret = withinSpan ? getCell(rows2[index], c) : elementnew(substitution(ex.element, comparator), true, ex.isLocked);
          return ret;
        }, comparator, substitution);
        return cols.concat(before2).concat([newRow]).concat(after2);
      };
      var getElementFor = function(row2, column, section2, withinSpan, example, comparator, substitution) {
        if (section2 === "colgroup" || !withinSpan) {
          var cell2 = getCell(row2, example);
          return elementnew(substitution(cell2.element, comparator), true, false);
        } else {
          return getCell(row2, column);
        }
      };
      var insertColumnAt = function(grid2, index, example, comparator, substitution) {
        return map$1(grid2, function(row2) {
          var withinSpan = index > 0 && index < cellLength(row2) && comparator(getCellElement(row2, index - 1), getCellElement(row2, index));
          var sub = getElementFor(row2, index, row2.section, withinSpan, example, comparator, substitution);
          return addCell(row2, index, sub);
        });
      };
      var deleteColumnsAt = function(grid2, columns2) {
        return bind$2(grid2, function(row2) {
          var existingCells = row2.cells;
          var cells2 = foldr(columns2, function(acc, column) {
            return column >= 0 && column < acc.length ? acc.slice(0, column).concat(acc.slice(column + 1)) : acc;
          }, existingCells);
          return cells2.length > 0 ? [rowcells(row2.element, cells2, row2.section, row2.isNew)] : [];
        });
      };
      var deleteRowsAt = function(grid2, start, finish) {
        var _a = extractGridDetails(grid2), rows2 = _a.rows, cols = _a.cols;
        return cols.concat(rows2.slice(0, start)).concat(rows2.slice(finish + 1));
      };
      var notInStartRow = function(grid2, rowIndex, colIndex, comparator) {
        return getCellElement(grid2[rowIndex], colIndex) !== void 0 && (rowIndex > 0 && comparator(getCellElement(grid2[rowIndex - 1], colIndex), getCellElement(grid2[rowIndex], colIndex)));
      };
      var notInStartColumn = function(row2, index, comparator) {
        return index > 0 && comparator(getCellElement(row2, index - 1), getCellElement(row2, index));
      };
      var isDuplicatedCell = function(grid2, rowIndex, colIndex, comparator) {
        return notInStartRow(grid2, rowIndex, colIndex, comparator) || notInStartColumn(grid2[rowIndex], colIndex, comparator);
      };
      var rowReplacerPredicate = function(targetRow, columnHeaders) {
        var entireTableIsHeader = forall(columnHeaders, identity) && isHeaderCells(targetRow.cells);
        return entireTableIsHeader ? always : function(cell2, _rowIndex, colIndex) {
          var type2 = name(cell2.element);
          return !(type2 === "th" && columnHeaders[colIndex]);
        };
      };
      var columnReplacePredicate = function(targetColumn, rowHeaders) {
        var entireTableIsHeader = forall(rowHeaders, identity) && isHeaderCells(targetColumn);
        return entireTableIsHeader ? always : function(cell2, rowIndex, _colIndex) {
          var type2 = name(cell2.element);
          return !(type2 === "th" && rowHeaders[rowIndex]);
        };
      };
      var determineScope = function(applyScope, element, newScope, isInHeader) {
        var hasSpan = function(scope) {
          return scope === "row" ? hasRowspan(element) : hasColspan(element);
        };
        var getScope = function(scope) {
          return hasSpan(scope) ? scope + "group" : scope;
        };
        if (applyScope) {
          return isHeaderCell(element) ? getScope(newScope) : null;
        } else if (isInHeader && isHeaderCell(element)) {
          var oppositeScope = newScope === "row" ? "col" : "row";
          return getScope(oppositeScope);
        } else {
          return null;
        }
      };
      var rowScopeGenerator = function(applyScope, columnHeaders) {
        return function(cell2, rowIndex, columnIndex) {
          return Optional.some(determineScope(applyScope, cell2.element, "col", columnHeaders[columnIndex]));
        };
      };
      var columnScopeGenerator = function(applyScope, rowHeaders) {
        return function(cell2, rowIndex) {
          return Optional.some(determineScope(applyScope, cell2.element, "row", rowHeaders[rowIndex]));
        };
      };
      var replace = function(cell2, comparator, substitute) {
        return elementnew(substitute(cell2.element, comparator), true, cell2.isLocked);
      };
      var replaceIn = function(grid2, targets, comparator, substitute, replacer, genScope, shouldReplace) {
        var isTarget = function(cell2) {
          return exists(targets, function(target) {
            return comparator(cell2.element, target.element);
          });
        };
        return map$1(grid2, function(row2, rowIndex) {
          return mapCells(row2, function(cell2, colIndex) {
            if (isTarget(cell2)) {
              var newCell_1 = shouldReplace(cell2, rowIndex, colIndex) ? replacer(cell2, comparator, substitute) : cell2;
              genScope(newCell_1, rowIndex, colIndex).each(function(scope) {
                setOptions(newCell_1.element, { scope: Optional.from(scope) });
              });
              return newCell_1;
            } else {
              return cell2;
            }
          });
        });
      };
      var getColumnCells = function(rows2, columnIndex, comparator) {
        return bind$2(rows2, function(row2, i) {
          return isDuplicatedCell(rows2, i, columnIndex, comparator) ? [] : [getCell(row2, columnIndex)];
        });
      };
      var getRowCells = function(rows2, rowIndex, comparator) {
        var targetRow = rows2[rowIndex];
        return bind$2(targetRow.cells, function(item, i) {
          return isDuplicatedCell(rows2, rowIndex, i, comparator) ? [] : [item];
        });
      };
      var replaceColumns = function(grid2, indexes, applyScope, comparator, substitution) {
        var rows2 = extractGridDetails(grid2).rows;
        var targets = bind$2(indexes, function(index) {
          return getColumnCells(rows2, index, comparator);
        });
        var rowHeaders = map$1(grid2, function(row2) {
          return isHeaderCells(row2.cells);
        });
        var shouldReplaceCell = columnReplacePredicate(targets, rowHeaders);
        var scopeGenerator = columnScopeGenerator(applyScope, rowHeaders);
        return replaceIn(grid2, targets, comparator, substitution, replace, scopeGenerator, shouldReplaceCell);
      };
      var replaceRows = function(grid2, indexes, section2, applyScope, comparator, substitution, tableSection) {
        var _a = extractGridDetails(grid2), cols = _a.cols, rows2 = _a.rows;
        var targetRow = rows2[indexes[0]];
        var targets = bind$2(indexes, function(index) {
          return getRowCells(rows2, index, comparator);
        });
        var columnHeaders = map$1(targetRow.cells, function(_cell, index) {
          return isHeaderCells(getColumnCells(rows2, index, comparator));
        });
        var newRows = __spreadArray([], rows2, true);
        each$2(indexes, function(index) {
          newRows[index] = tableSection.transformRow(rows2[index], section2);
        });
        var newGrid = cols.concat(newRows);
        var shouldReplaceCell = rowReplacerPredicate(targetRow, columnHeaders);
        var scopeGenerator = rowScopeGenerator(applyScope, columnHeaders);
        return replaceIn(newGrid, targets, comparator, substitution, tableSection.transformCell, scopeGenerator, shouldReplaceCell);
      };
      var replaceCells = function(grid2, details, comparator, substitution) {
        var rows2 = extractGridDetails(grid2).rows;
        var targetCells = map$1(details, function(detail2) {
          return getCell(rows2[detail2.row], detail2.column);
        });
        return replaceIn(grid2, targetCells, comparator, substitution, replace, Optional.none, always);
      };
      var uniqueColumns = function(details) {
        var uniqueCheck = function(rest, detail2) {
          var columnExists = exists(rest, function(currentDetail) {
            return currentDetail.column === detail2.column;
          });
          return columnExists ? rest : rest.concat([detail2]);
        };
        return foldl(details, uniqueCheck, []).sort(function(detailA, detailB) {
          return detailA.column - detailB.column;
        });
      };
      var isCol = isTag("col");
      var isColgroup = isTag("colgroup");
      var isRow$1 = function(element) {
        return name(element) === "tr" || isColgroup(element);
      };
      var elementToData = function(element) {
        var colspan = getAttrValue(element, "colspan", 1);
        var rowspan = getAttrValue(element, "rowspan", 1);
        return {
          element,
          colspan,
          rowspan
        };
      };
      var modification = function(generators, toData) {
        if (toData === void 0) {
          toData = elementToData;
        }
        var nuCell = function(data) {
          return isCol(data.element) ? generators.col(data) : generators.cell(data);
        };
        var nuRow = function(data) {
          return isColgroup(data.element) ? generators.colgroup(data) : generators.row(data);
        };
        var add2 = function(element) {
          if (isRow$1(element)) {
            return nuRow({ element });
          } else {
            var replacement = nuCell(toData(element));
            recent = Optional.some({
              item: element,
              replacement
            });
            return replacement;
          }
        };
        var recent = Optional.none();
        var getOrInit = function(element, comparator) {
          return recent.fold(function() {
            return add2(element);
          }, function(p) {
            return comparator(element, p.item) ? p.replacement : add2(element);
          });
        };
        return { getOrInit };
      };
      var transform = function(tag) {
        return function(generators) {
          var list = [];
          var find2 = function(element, comparator) {
            return find$1(list, function(x) {
              return comparator(x.item, element);
            });
          };
          var makeNew = function(element) {
            var attrs = tag === "td" ? { scope: null } : {};
            var cell2 = generators.replace(element, tag, attrs);
            list.push({
              item: element,
              sub: cell2
            });
            return cell2;
          };
          var replaceOrInit = function(element, comparator) {
            if (isRow$1(element) || isCol(element)) {
              return element;
            } else {
              return find2(element, comparator).fold(function() {
                return makeNew(element);
              }, function(p) {
                return comparator(element, p.item) ? p.sub : makeNew(element);
              });
            }
          };
          return { replaceOrInit };
        };
      };
      var getScopeAttribute = function(cell2) {
        return getOpt(cell2, "scope").map(function(attribute) {
          return attribute.substr(0, 3);
        });
      };
      var merging = function(generators) {
        var unmerge2 = function(cell2) {
          var scope = getScopeAttribute(cell2);
          scope.each(function(attribute) {
            return set$2(cell2, "scope", attribute);
          });
          return function() {
            var raw = generators.cell({
              element: cell2,
              colspan: 1,
              rowspan: 1
            });
            remove$6(raw, "width");
            remove$6(cell2, "width");
            scope.each(function(attribute) {
              return set$2(raw, "scope", attribute);
            });
            return raw;
          };
        };
        var merge2 = function(cells2) {
          var getScopeProperty = function() {
            var stringAttributes = cat(map$1(cells2, getScopeAttribute));
            if (stringAttributes.length === 0) {
              return Optional.none();
            } else {
              var baseScope_1 = stringAttributes[0];
              var scopes_1 = [
                "row",
                "col"
              ];
              var isMixed = exists(stringAttributes, function(attribute) {
                return attribute !== baseScope_1 && contains$2(scopes_1, attribute);
              });
              return isMixed ? Optional.none() : Optional.from(baseScope_1);
            }
          };
          remove$6(cells2[0], "width");
          getScopeProperty().fold(function() {
            return remove$7(cells2[0], "scope");
          }, function(attribute) {
            return set$2(cells2[0], "scope", attribute + "group");
          });
          return constant(cells2[0]);
        };
        return {
          unmerge: unmerge2,
          merge: merge2
        };
      };
      var Generators = {
        modification,
        transform,
        merging
      };
      var blockList = [
        "body",
        "p",
        "div",
        "article",
        "aside",
        "figcaption",
        "figure",
        "footer",
        "header",
        "nav",
        "section",
        "ol",
        "ul",
        "table",
        "thead",
        "tfoot",
        "tbody",
        "caption",
        "tr",
        "td",
        "th",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "blockquote",
        "pre",
        "address"
      ];
      var isList$1 = function(universe2, item) {
        var tagName = universe2.property().name(item);
        return contains$2([
          "ol",
          "ul"
        ], tagName);
      };
      var isBlock$1 = function(universe2, item) {
        var tagName = universe2.property().name(item);
        return contains$2(blockList, tagName);
      };
      var isEmptyTag$1 = function(universe2, item) {
        return contains$2([
          "br",
          "img",
          "hr",
          "input"
        ], universe2.property().name(item));
      };
      var universe$1 = DomUniverse();
      var isBlock = function(element) {
        return isBlock$1(universe$1, element);
      };
      var isList = function(element) {
        return isList$1(universe$1, element);
      };
      var isEmptyTag = function(element) {
        return isEmptyTag$1(universe$1, element);
      };
      var merge = function(cells2) {
        var isBr2 = function(el) {
          return name(el) === "br";
        };
        var advancedBr = function(children2) {
          return forall(children2, function(c) {
            return isBr2(c) || isText(c) && get$9(c).trim().length === 0;
          });
        };
        var isListItem = function(el) {
          return name(el) === "li" || ancestor$2(el, isList).isSome();
        };
        var siblingIsBlock = function(el) {
          return nextSibling(el).map(function(rightSibling) {
            if (isBlock(rightSibling)) {
              return true;
            }
            if (isEmptyTag(rightSibling)) {
              return name(rightSibling) === "img" ? false : true;
            }
            return false;
          }).getOr(false);
        };
        var markCell = function(cell2) {
          return last$1(cell2).bind(function(rightEdge) {
            var rightSiblingIsBlock = siblingIsBlock(rightEdge);
            return parent(rightEdge).map(function(parent2) {
              return rightSiblingIsBlock === true || isListItem(parent2) || isBr2(rightEdge) || isBlock(parent2) && !eq$1(cell2, parent2) ? [] : [SugarElement.fromTag("br")];
            });
          }).getOr([]);
        };
        var markContent = function() {
          var content = bind$2(cells2, function(cell2) {
            var children2 = children$3(cell2);
            return advancedBr(children2) ? [] : children2.concat(markCell(cell2));
          });
          return content.length === 0 ? [SugarElement.fromTag("br")] : content;
        };
        var contents = markContent();
        empty(cells2[0]);
        append(cells2[0], contents);
      };
      var isEditable = function(elem) {
        return isEditable$1(elem, true);
      };
      var prune = function(table2) {
        var cells2 = cells$1(table2);
        if (cells2.length === 0) {
          remove$5(table2);
        }
      };
      var outcome = function(grid2, cursor) {
        return {
          grid: grid2,
          cursor
        };
      };
      var findEditableCursorPosition = function(rows2) {
        return findMap(rows2, function(row2) {
          return findMap(row2.cells, function(cell2) {
            var elem = cell2.element;
            return someIf(isEditable(elem), elem);
          });
        });
      };
      var elementFromGrid = function(grid2, row2, column) {
        var _a, _b;
        var rows2 = extractGridDetails(grid2).rows;
        return Optional.from((_b = (_a = rows2[row2]) === null || _a === void 0 ? void 0 : _a.cells[column]) === null || _b === void 0 ? void 0 : _b.element).filter(isEditable).orThunk(function() {
          return findEditableCursorPosition(rows2);
        });
      };
      var bundle = function(grid2, row2, column) {
        var cursorElement = elementFromGrid(grid2, row2, column);
        return outcome(grid2, cursorElement);
      };
      var uniqueRows = function(details) {
        var rowCompilation = function(rest, detail2) {
          var rowExists = exists(rest, function(currentDetail) {
            return currentDetail.row === detail2.row;
          });
          return rowExists ? rest : rest.concat([detail2]);
        };
        return foldl(details, rowCompilation, []).sort(function(detailA, detailB) {
          return detailA.row - detailB.row;
        });
      };
      var opInsertRowsBefore = function(grid2, details, comparator, genWrappers) {
        var targetIndex = details[0].row;
        var rows2 = uniqueRows(details);
        var newGrid = foldr(rows2, function(acc, row2) {
          var newG = insertRowAt(acc.grid, targetIndex, row2.row + acc.delta, comparator, genWrappers.getOrInit);
          return {
            grid: newG,
            delta: acc.delta + 1
          };
        }, {
          grid: grid2,
          delta: 0
        }).grid;
        return bundle(newGrid, targetIndex, details[0].column);
      };
      var opInsertRowsAfter = function(grid2, details, comparator, genWrappers) {
        var rows2 = uniqueRows(details);
        var target = rows2[rows2.length - 1];
        var targetIndex = target.row + target.rowspan;
        var newGrid = foldr(rows2, function(newG, row2) {
          return insertRowAt(newG, targetIndex, row2.row, comparator, genWrappers.getOrInit);
        }, grid2);
        return bundle(newGrid, targetIndex, details[0].column);
      };
      var opInsertColumnsBefore = function(grid2, extractDetail, comparator, genWrappers) {
        var details = extractDetail.details;
        var columns2 = uniqueColumns(details);
        var targetIndex = columns2[0].column;
        var newGrid = foldr(columns2, function(acc, col2) {
          var newG = insertColumnAt(acc.grid, targetIndex, col2.column + acc.delta, comparator, genWrappers.getOrInit);
          return {
            grid: newG,
            delta: acc.delta + 1
          };
        }, {
          grid: grid2,
          delta: 0
        }).grid;
        return bundle(newGrid, details[0].row, targetIndex);
      };
      var opInsertColumnsAfter = function(grid2, extractDetail, comparator, genWrappers) {
        var details = extractDetail.details;
        var target = details[details.length - 1];
        var targetIndex = target.column + target.colspan;
        var columns2 = uniqueColumns(details);
        var newGrid = foldr(columns2, function(newG, col2) {
          return insertColumnAt(newG, targetIndex, col2.column, comparator, genWrappers.getOrInit);
        }, grid2);
        return bundle(newGrid, details[0].row, targetIndex);
      };
      var opMakeColumnsHeader = function(initialGrid, details, comparator, genWrappers) {
        var columns2 = uniqueColumns(details);
        var columnIndexes = map$1(columns2, function(detail2) {
          return detail2.column;
        });
        var newGrid = replaceColumns(initialGrid, columnIndexes, true, comparator, genWrappers.replaceOrInit);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      var opMakeCellsHeader = function(initialGrid, details, comparator, genWrappers) {
        var newGrid = replaceCells(initialGrid, details, comparator, genWrappers.replaceOrInit);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      var opUnmakeColumnsHeader = function(initialGrid, details, comparator, genWrappers) {
        var columns2 = uniqueColumns(details);
        var columnIndexes = map$1(columns2, function(detail2) {
          return detail2.column;
        });
        var newGrid = replaceColumns(initialGrid, columnIndexes, false, comparator, genWrappers.replaceOrInit);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      var opUnmakeCellsHeader = function(initialGrid, details, comparator, genWrappers) {
        var newGrid = replaceCells(initialGrid, details, comparator, genWrappers.replaceOrInit);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      var makeRowsSection = function(section2, applyScope) {
        return function(initialGrid, details, comparator, genWrappers, tableSection) {
          var rows2 = uniqueRows(details);
          var rowIndexes = map$1(rows2, function(detail2) {
            return detail2.row;
          });
          var newGrid = replaceRows(initialGrid, rowIndexes, section2, applyScope, comparator, genWrappers.replaceOrInit, tableSection);
          return bundle(newGrid, details[0].row, details[0].column);
        };
      };
      var opMakeRowsHeader = makeRowsSection("thead", true);
      var opMakeRowsBody = makeRowsSection("tbody", false);
      var opMakeRowsFooter = makeRowsSection("tfoot", false);
      var opEraseColumns = function(grid2, extractDetail, _comparator, _genWrappers) {
        var columns2 = uniqueColumns(extractDetail.details);
        var newGrid = deleteColumnsAt(grid2, map$1(columns2, function(column) {
          return column.column;
        }));
        var maxColIndex = newGrid.length > 0 ? newGrid[0].cells.length - 1 : 0;
        return bundle(newGrid, columns2[0].row, Math.min(columns2[0].column, maxColIndex));
      };
      var opEraseRows = function(grid2, details, _comparator, _genWrappers) {
        var rows2 = uniqueRows(details);
        var newGrid = deleteRowsAt(grid2, rows2[0].row, rows2[rows2.length - 1].row);
        var maxRowIndex = newGrid.length > 0 ? newGrid.length - 1 : 0;
        return bundle(newGrid, Math.min(details[0].row, maxRowIndex), details[0].column);
      };
      var opMergeCells = function(grid2, mergable2, comparator, genWrappers) {
        var cells2 = mergable2.cells;
        merge(cells2);
        var newGrid = merge$2(grid2, mergable2.bounds, comparator, genWrappers.merge(cells2));
        return outcome(newGrid, Optional.from(cells2[0]));
      };
      var opUnmergeCells = function(grid2, unmergable2, comparator, genWrappers) {
        var unmerge$1 = function(b, cell2) {
          return unmerge(b, cell2, comparator, genWrappers.unmerge(cell2));
        };
        var newGrid = foldr(unmergable2, unmerge$1, grid2);
        return outcome(newGrid, Optional.from(unmergable2[0]));
      };
      var opPasteCells = function(grid2, pasteDetails, comparator, _genWrappers) {
        var gridify = function(table2, generators) {
          var wh = Warehouse.fromTable(table2);
          return toGrid(wh, generators, true);
        };
        var gridB = gridify(pasteDetails.clipboard, pasteDetails.generators);
        var startAddress = address(pasteDetails.row, pasteDetails.column);
        var mergedGrid = merge$1(startAddress, grid2, gridB, pasteDetails.generators, comparator);
        return mergedGrid.fold(function() {
          return outcome(grid2, Optional.some(pasteDetails.element));
        }, function(newGrid) {
          return bundle(newGrid, pasteDetails.row, pasteDetails.column);
        });
      };
      var gridifyRows = function(rows2, generators, context) {
        var pasteDetails = fromPastedRows(rows2, context.section);
        var wh = Warehouse.generate(pasteDetails);
        return toGrid(wh, generators, true);
      };
      var opPasteColsBefore = function(grid2, pasteDetails, comparator, _genWrappers) {
        var rows2 = extractGridDetails(grid2).rows;
        var index = pasteDetails.cells[0].column;
        var context = rows2[pasteDetails.cells[0].row];
        var gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);
        var mergedGrid = insertCols(index, grid2, gridB, pasteDetails.generators, comparator);
        return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);
      };
      var opPasteColsAfter = function(grid2, pasteDetails, comparator, _genWrappers) {
        var rows2 = extractGridDetails(grid2).rows;
        var index = pasteDetails.cells[pasteDetails.cells.length - 1].column + pasteDetails.cells[pasteDetails.cells.length - 1].colspan;
        var context = rows2[pasteDetails.cells[0].row];
        var gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);
        var mergedGrid = insertCols(index, grid2, gridB, pasteDetails.generators, comparator);
        return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);
      };
      var opPasteRowsBefore = function(grid2, pasteDetails, comparator, _genWrappers) {
        var rows2 = extractGridDetails(grid2).rows;
        var index = pasteDetails.cells[0].row;
        var context = rows2[index];
        var gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);
        var mergedGrid = insertRows(index, grid2, gridB, pasteDetails.generators, comparator);
        return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);
      };
      var opPasteRowsAfter = function(grid2, pasteDetails, comparator, _genWrappers) {
        var rows2 = extractGridDetails(grid2).rows;
        var index = pasteDetails.cells[pasteDetails.cells.length - 1].row + pasteDetails.cells[pasteDetails.cells.length - 1].rowspan;
        var context = rows2[pasteDetails.cells[0].row];
        var gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);
        var mergedGrid = insertRows(index, grid2, gridB, pasteDetails.generators, comparator);
        return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);
      };
      var opGetColumnsType = function(table2, target) {
        var house = Warehouse.fromTable(table2);
        var details = onCells(house, target);
        return details.bind(function(selectedCells) {
          var lastSelectedCell = selectedCells[selectedCells.length - 1];
          var minColRange = selectedCells[0].column;
          var maxColRange = lastSelectedCell.column + lastSelectedCell.colspan;
          var selectedColumnCells = flatten$1(map$1(house.all, function(row2) {
            return filter$2(row2.cells, function(cell2) {
              return cell2.column >= minColRange && cell2.column < maxColRange;
            });
          }));
          return findCommonCellType(selectedColumnCells);
        }).getOr("");
      };
      var opGetCellsType = function(table2, target) {
        var house = Warehouse.fromTable(table2);
        var details = onCells(house, target);
        return details.bind(findCommonCellType).getOr("");
      };
      var opGetRowsType = function(table2, target) {
        var house = Warehouse.fromTable(table2);
        var details = onCells(house, target);
        return details.bind(function(selectedCells) {
          var lastSelectedCell = selectedCells[selectedCells.length - 1];
          var minRowRange = selectedCells[0].row;
          var maxRowRange = lastSelectedCell.row + lastSelectedCell.rowspan;
          var selectedRows = house.all.slice(minRowRange, maxRowRange);
          return findCommonRowType(selectedRows);
        }).getOr("");
      };
      var resize = function(table2, list, details, behaviours) {
        return adjustWidthTo(table2, list, details, behaviours.sizing);
      };
      var adjustAndRedistributeWidths = function(table2, list, details, behaviours) {
        return adjustAndRedistributeWidths$1(table2, list, details, behaviours.sizing, behaviours.resize);
      };
      var firstColumnIsLocked = function(_warehouse, details) {
        return exists(details, function(detail2) {
          return detail2.column === 0 && detail2.isLocked;
        });
      };
      var lastColumnIsLocked = function(warehouse, details) {
        return exists(details, function(detail2) {
          return detail2.column + detail2.colspan >= warehouse.grid.columns && detail2.isLocked;
        });
      };
      var getColumnsWidth = function(warehouse, details) {
        var columns$12 = columns(warehouse);
        var uniqueCols = uniqueColumns(details);
        return foldl(uniqueCols, function(acc, detail2) {
          var column = columns$12[detail2.column];
          var colWidth = column.map(getOuter$2).getOr(0);
          return acc + colWidth;
        }, 0);
      };
      var insertColumnsExtractor = function(before2) {
        return function(warehouse, target) {
          return onCells(warehouse, target).filter(function(details) {
            var checkLocked = before2 ? firstColumnIsLocked : lastColumnIsLocked;
            return !checkLocked(warehouse, details);
          }).map(function(details) {
            return {
              details,
              pixelDelta: getColumnsWidth(warehouse, details)
            };
          });
        };
      };
      var eraseColumnsExtractor = function(warehouse, target) {
        return onUnlockedCells(warehouse, target).map(function(details) {
          return {
            details,
            pixelDelta: -getColumnsWidth(warehouse, details)
          };
        });
      };
      var pasteColumnsExtractor = function(before2) {
        return function(warehouse, target) {
          return onPasteByEditor(warehouse, target).filter(function(details) {
            var checkLocked = before2 ? firstColumnIsLocked : lastColumnIsLocked;
            return !checkLocked(warehouse, details.cells);
          });
        };
      };
      var headerCellGenerator = Generators.transform("th");
      var bodyCellGenerator = Generators.transform("td");
      var insertRowsBefore = run(opInsertRowsBefore, onCells, noop, noop, Generators.modification);
      var insertRowsAfter = run(opInsertRowsAfter, onCells, noop, noop, Generators.modification);
      var insertColumnsBefore = run(opInsertColumnsBefore, insertColumnsExtractor(true), adjustAndRedistributeWidths, noop, Generators.modification);
      var insertColumnsAfter = run(opInsertColumnsAfter, insertColumnsExtractor(false), adjustAndRedistributeWidths, noop, Generators.modification);
      var eraseColumns = run(opEraseColumns, eraseColumnsExtractor, adjustAndRedistributeWidths, prune, Generators.modification);
      var eraseRows = run(opEraseRows, onCells, noop, prune, Generators.modification);
      var makeColumnsHeader = run(opMakeColumnsHeader, onUnlockedCells, noop, noop, headerCellGenerator);
      var unmakeColumnsHeader = run(opUnmakeColumnsHeader, onUnlockedCells, noop, noop, bodyCellGenerator);
      var makeRowsHeader = run(opMakeRowsHeader, onUnlockedCells, noop, noop, headerCellGenerator);
      var makeRowsBody = run(opMakeRowsBody, onUnlockedCells, noop, noop, bodyCellGenerator);
      var makeRowsFooter = run(opMakeRowsFooter, onUnlockedCells, noop, noop, bodyCellGenerator);
      var makeCellsHeader = run(opMakeCellsHeader, onUnlockedCells, noop, noop, headerCellGenerator);
      var unmakeCellsHeader = run(opUnmakeCellsHeader, onUnlockedCells, noop, noop, bodyCellGenerator);
      var mergeCells = run(opMergeCells, onUnlockedMergable, resize, noop, Generators.merging);
      var unmergeCells = run(opUnmergeCells, onUnlockedUnmergable, resize, noop, Generators.merging);
      var pasteCells = run(opPasteCells, onPaste, resize, noop, Generators.modification);
      var pasteColsBefore = run(opPasteColsBefore, pasteColumnsExtractor(true), noop, noop, Generators.modification);
      var pasteColsAfter = run(opPasteColsAfter, pasteColumnsExtractor(false), noop, noop, Generators.modification);
      var pasteRowsBefore = run(opPasteRowsBefore, onPasteByEditor, noop, noop, Generators.modification);
      var pasteRowsAfter = run(opPasteRowsAfter, onPasteByEditor, noop, noop, Generators.modification);
      var getColumnsType = opGetColumnsType;
      var getCellsType = opGetCellsType;
      var getRowsType = opGetRowsType;
      var TableActions = function(editor, cellSelection, lazyWire) {
        var isTableBody = function(editor2) {
          return name(getBody(editor2)) === "table";
        };
        var lastRowGuard = function(table2) {
          return isTableBody(editor) === false || getGridSize(table2).rows > 1;
        };
        var lastColumnGuard = function(table2) {
          return isTableBody(editor) === false || getGridSize(table2).columns > 1;
        };
        var cloneFormats2 = getCloneElements(editor);
        var colMutationOp = isResizeTableColumnResizing(editor) ? noop : halve;
        var getTableSectionType2 = function(table2) {
          switch (getTableHeaderType(editor)) {
            case "section":
              return TableSection.section();
            case "sectionCells":
              return TableSection.sectionCells();
            case "cells":
              return TableSection.cells();
            default:
              return TableSection.getTableSectionType(table2, "section");
          }
        };
        var setSelectionFromAction = function(table2, result) {
          return result.cursor.fold(function() {
            var cells2 = cells$1(table2);
            return head(cells2).filter(inBody).map(function(firstCell) {
              cellSelection.clear(table2);
              var rng = editor.dom.createRng();
              rng.selectNode(firstCell.dom);
              editor.selection.setRng(rng);
              set$2(firstCell, "data-mce-selected", "1");
              return rng;
            });
          }, function(cell2) {
            var des = freefallRtl(cell2);
            var rng = editor.dom.createRng();
            rng.setStart(des.element.dom, des.offset);
            rng.setEnd(des.element.dom, des.offset);
            editor.selection.setRng(rng);
            cellSelection.clear(table2);
            return Optional.some(rng);
          });
        };
        var execute = function(operation, guard, mutate2, lazyWire2, effect) {
          return function(table2, target, noEvents) {
            if (noEvents === void 0) {
              noEvents = false;
            }
            removeDataStyle(table2);
            var wire = lazyWire2();
            var doc = SugarElement.fromDom(editor.getDoc());
            var generators = cellOperations(mutate2, doc, cloneFormats2);
            var behaviours = {
              sizing: get$4(editor, table2),
              resize: isResizeTableColumnResizing(editor) ? resizeTable() : preserveTable(),
              section: getTableSectionType2(table2)
            };
            return guard(table2) ? operation(wire, table2, target, generators, behaviours).bind(function(result) {
              each$2(result.newRows, function(row2) {
                fireNewRow(editor, row2.dom);
              });
              each$2(result.newCells, function(cell2) {
                fireNewCell(editor, cell2.dom);
              });
              var range2 = setSelectionFromAction(table2, result);
              if (inBody(table2)) {
                removeDataStyle(table2);
                if (!noEvents) {
                  fireTableModified(editor, table2.dom, effect);
                }
              }
              return range2.map(function(rng) {
                return {
                  rng,
                  effect
                };
              });
            }) : Optional.none();
          };
        };
        var deleteRow = execute(eraseRows, lastRowGuard, noop, lazyWire, structureModified);
        var deleteColumn = execute(eraseColumns, lastColumnGuard, noop, lazyWire, structureModified);
        var insertRowsBefore$1 = execute(insertRowsBefore, always, noop, lazyWire, structureModified);
        var insertRowsAfter$1 = execute(insertRowsAfter, always, noop, lazyWire, structureModified);
        var insertColumnsBefore$1 = execute(insertColumnsBefore, always, colMutationOp, lazyWire, structureModified);
        var insertColumnsAfter$1 = execute(insertColumnsAfter, always, colMutationOp, lazyWire, structureModified);
        var mergeCells$1 = execute(mergeCells, always, noop, lazyWire, structureModified);
        var unmergeCells$1 = execute(unmergeCells, always, noop, lazyWire, structureModified);
        var pasteColsBefore$1 = execute(pasteColsBefore, always, noop, lazyWire, structureModified);
        var pasteColsAfter$1 = execute(pasteColsAfter, always, noop, lazyWire, structureModified);
        var pasteRowsBefore$1 = execute(pasteRowsBefore, always, noop, lazyWire, structureModified);
        var pasteRowsAfter$1 = execute(pasteRowsAfter, always, noop, lazyWire, structureModified);
        var pasteCells$1 = execute(pasteCells, always, noop, lazyWire, styleAndStructureModified);
        var makeCellsHeader$1 = execute(makeCellsHeader, always, noop, lazyWire, structureModified);
        var unmakeCellsHeader$1 = execute(unmakeCellsHeader, always, noop, lazyWire, structureModified);
        var makeColumnsHeader$1 = execute(makeColumnsHeader, always, noop, lazyWire, structureModified);
        var unmakeColumnsHeader$1 = execute(unmakeColumnsHeader, always, noop, lazyWire, structureModified);
        var makeRowsHeader$1 = execute(makeRowsHeader, always, noop, lazyWire, structureModified);
        var makeRowsBody$1 = execute(makeRowsBody, always, noop, lazyWire, structureModified);
        var makeRowsFooter$1 = execute(makeRowsFooter, always, noop, lazyWire, structureModified);
        var getTableCellType = getCellsType;
        var getTableColType = getColumnsType;
        var getTableRowType = getRowsType;
        return {
          deleteRow,
          deleteColumn,
          insertRowsBefore: insertRowsBefore$1,
          insertRowsAfter: insertRowsAfter$1,
          insertColumnsBefore: insertColumnsBefore$1,
          insertColumnsAfter: insertColumnsAfter$1,
          mergeCells: mergeCells$1,
          unmergeCells: unmergeCells$1,
          pasteColsBefore: pasteColsBefore$1,
          pasteColsAfter: pasteColsAfter$1,
          pasteRowsBefore: pasteRowsBefore$1,
          pasteRowsAfter: pasteRowsAfter$1,
          pasteCells: pasteCells$1,
          makeCellsHeader: makeCellsHeader$1,
          unmakeCellsHeader: unmakeCellsHeader$1,
          makeColumnsHeader: makeColumnsHeader$1,
          unmakeColumnsHeader: unmakeColumnsHeader$1,
          makeRowsHeader: makeRowsHeader$1,
          makeRowsBody: makeRowsBody$1,
          makeRowsFooter: makeRowsFooter$1,
          getTableRowType,
          getTableCellType,
          getTableColType
        };
      };
      var DefaultRenderOptions = {
        styles: {
          "border-collapse": "collapse",
          "width": "100%"
        },
        attributes: { border: "1" },
        colGroups: false
      };
      var tableHeaderCell = function() {
        return SugarElement.fromTag("th");
      };
      var tableCell = function() {
        return SugarElement.fromTag("td");
      };
      var tableColumn = function() {
        return SugarElement.fromTag("col");
      };
      var createRow = function(columns2, rowHeaders, columnHeaders, rowIndex) {
        var tr = SugarElement.fromTag("tr");
        for (var j = 0; j < columns2; j++) {
          var td = rowIndex < rowHeaders || j < columnHeaders ? tableHeaderCell() : tableCell();
          if (j < columnHeaders) {
            set$2(td, "scope", "row");
          }
          if (rowIndex < rowHeaders) {
            set$2(td, "scope", "col");
          }
          append$1(td, SugarElement.fromTag("br"));
          append$1(tr, td);
        }
        return tr;
      };
      var createGroupRow = function(columns2) {
        var columnGroup = SugarElement.fromTag("colgroup");
        range$1(columns2, function() {
          return append$1(columnGroup, tableColumn());
        });
        return columnGroup;
      };
      var createRows = function(rows2, columns2, rowHeaders, columnHeaders) {
        return range$1(rows2, function(r2) {
          return createRow(columns2, rowHeaders, columnHeaders, r2);
        });
      };
      var render = function(rows2, columns2, rowHeaders, columnHeaders, headerType, renderOpts) {
        if (renderOpts === void 0) {
          renderOpts = DefaultRenderOptions;
        }
        var table2 = SugarElement.fromTag("table");
        var rowHeadersGoInThead = headerType !== "cells";
        setAll(table2, renderOpts.styles);
        setAll$1(table2, renderOpts.attributes);
        if (renderOpts.colGroups) {
          append$1(table2, createGroupRow(columns2));
        }
        var actualRowHeaders = Math.min(rows2, rowHeaders);
        if (rowHeadersGoInThead && rowHeaders > 0) {
          var thead = SugarElement.fromTag("thead");
          append$1(table2, thead);
          var theadRowHeaders = headerType === "sectionCells" ? actualRowHeaders : 0;
          var theadRows = createRows(rowHeaders, columns2, theadRowHeaders, columnHeaders);
          append(thead, theadRows);
        }
        var tbody = SugarElement.fromTag("tbody");
        append$1(table2, tbody);
        var numRows = rowHeadersGoInThead ? rows2 - actualRowHeaders : rows2;
        var numRowHeaders = rowHeadersGoInThead ? 0 : rowHeaders;
        var tbodyRows = createRows(numRows, columns2, numRowHeaders, columnHeaders);
        append(tbody, tbodyRows);
        return table2;
      };
      var get$2 = function(element) {
        return element.dom.innerHTML;
      };
      var getOuter = function(element) {
        var container = SugarElement.fromTag("div");
        var clone2 = SugarElement.fromDom(element.dom.cloneNode(true));
        append$1(container, clone2);
        return get$2(container);
      };
      var placeCaretInCell = function(editor, cell2) {
        editor.selection.select(cell2.dom, true);
        editor.selection.collapse(true);
      };
      var selectFirstCellInTable = function(editor, tableElm) {
        descendant(tableElm, "td,th").each(curry(placeCaretInCell, editor));
      };
      var fireEvents = function(editor, table2) {
        each$2(descendants(table2, "tr"), function(row2) {
          fireNewRow(editor, row2.dom);
          each$2(descendants(row2, "th,td"), function(cell2) {
            fireNewCell(editor, cell2.dom);
          });
        });
      };
      var isPercentage = function(width2) {
        return isString(width2) && width2.indexOf("%") !== -1;
      };
      var insert = function(editor, columns2, rows2, colHeaders, rowHeaders) {
        var defaultStyles2 = getDefaultStyles(editor);
        var options = {
          styles: defaultStyles2,
          attributes: getDefaultAttributes(editor),
          colGroups: useColumnGroup(editor)
        };
        editor.undoManager.ignore(function() {
          var table2 = render(rows2, columns2, rowHeaders, colHeaders, getTableHeaderType(editor), options);
          set$2(table2, "data-mce-id", "__mce");
          var html = getOuter(table2);
          editor.insertContent(html);
          editor.addVisual();
        });
        return descendant(getBody(editor), 'table[data-mce-id="__mce"]').map(function(table2) {
          if (isPixelsForced(editor)) {
            enforcePixels(table2);
          } else if (isResponsiveForced(editor)) {
            enforceNone(table2);
          } else if (isPercentagesForced(editor) || isPercentage(defaultStyles2.width)) {
            enforcePercentage(table2);
          }
          removeDataStyle(table2);
          remove$7(table2, "data-mce-id");
          fireEvents(editor, table2);
          selectFirstCellInTable(editor, table2);
          return table2.dom;
        }).getOr(null);
      };
      var insertTableWithDataValidation = function(editor, rows2, columns2, options, errorMsg) {
        if (options === void 0) {
          options = {};
        }
        var checkInput = function(val) {
          return isNumber(val) && val > 0;
        };
        if (checkInput(rows2) && checkInput(columns2)) {
          var headerRows = options.headerRows || 0;
          var headerColumns = options.headerColumns || 0;
          return insert(editor, columns2, rows2, headerColumns, headerRows);
        } else {
          console.error(errorMsg);
          return null;
        }
      };
      var getClipboardElements = function(getClipboard) {
        return function() {
          return getClipboard().fold(function() {
            return [];
          }, function(elems) {
            return map$1(elems, function(e) {
              return e.dom;
            });
          });
        };
      };
      var setClipboardElements = function(setClipboard) {
        return function(elems) {
          var elmsOpt = elems.length > 0 ? Optional.some(fromDom(elems)) : Optional.none();
          setClipboard(elmsOpt);
        };
      };
      var insertTable = function(editor) {
        return function(columns2, rows2, options) {
          if (options === void 0) {
            options = {};
          }
          var table2 = insertTableWithDataValidation(editor, rows2, columns2, options, "Invalid values for insertTable - rows and columns values are required to insert a table.");
          editor.undoManager.add();
          return table2;
        };
      };
      var getApi = function(editor, clipboard, resizeHandler, selectionTargets) {
        return {
          insertTable: insertTable(editor),
          setClipboardRows: setClipboardElements(clipboard.setRows),
          getClipboardRows: getClipboardElements(clipboard.getRows),
          setClipboardCols: setClipboardElements(clipboard.setColumns),
          getClipboardCols: getClipboardElements(clipboard.getColumns),
          resizeHandler,
          selectionTargets
        };
      };
      var constrainSpan = function(element, property, value2) {
        var currentColspan = getAttrValue(element, property, 1);
        if (value2 === 1 || currentColspan <= 1) {
          remove$7(element, property);
        } else {
          set$2(element, property, Math.min(value2, currentColspan));
        }
      };
      var generateColGroup = function(house, minColRange, maxColRange) {
        if (Warehouse.hasColumns(house)) {
          var colsToCopy = filter$2(Warehouse.justColumns(house), function(col2) {
            return col2.column >= minColRange && col2.column < maxColRange;
          });
          var copiedCols = map$1(colsToCopy, function(c) {
            var clonedCol = deep(c.element);
            constrainSpan(clonedCol, "span", maxColRange - minColRange);
            return clonedCol;
          });
          var fakeColgroup = SugarElement.fromTag("colgroup");
          append(fakeColgroup, copiedCols);
          return [fakeColgroup];
        } else {
          return [];
        }
      };
      var generateRows = function(house, minColRange, maxColRange) {
        return map$1(house.all, function(row2) {
          var cellsToCopy = filter$2(row2.cells, function(cell2) {
            return cell2.column >= minColRange && cell2.column < maxColRange;
          });
          var copiedCells = map$1(cellsToCopy, function(cell2) {
            var clonedCell = deep(cell2.element);
            constrainSpan(clonedCell, "colspan", maxColRange - minColRange);
            return clonedCell;
          });
          var fakeTR = SugarElement.fromTag("tr");
          append(fakeTR, copiedCells);
          return fakeTR;
        });
      };
      var copyCols = function(table2, target) {
        var house = Warehouse.fromTable(table2);
        var details = onUnlockedCells(house, target);
        return details.map(function(selectedCells) {
          var lastSelectedCell = selectedCells[selectedCells.length - 1];
          var minColRange = selectedCells[0].column;
          var maxColRange = lastSelectedCell.column + lastSelectedCell.colspan;
          var fakeColGroups = generateColGroup(house, minColRange, maxColRange);
          var fakeRows = generateRows(house, minColRange, maxColRange);
          return __spreadArray(__spreadArray([], fakeColGroups, true), fakeRows, true);
        });
      };
      var copyRows = function(table2, target, generators) {
        var warehouse = Warehouse.fromTable(table2);
        var details = onCells(warehouse, target);
        return details.bind(function(selectedCells) {
          var grid2 = toGrid(warehouse, generators, false);
          var rows2 = extractGridDetails(grid2).rows;
          var slicedGrid = rows2.slice(selectedCells[0].row, selectedCells[selectedCells.length - 1].row + selectedCells[selectedCells.length - 1].rowspan);
          var filteredGrid = bind$2(slicedGrid, function(row2) {
            var newCells = filter$2(row2.cells, function(cell2) {
              return !cell2.isLocked;
            });
            return newCells.length > 0 ? [__assign(__assign({}, row2), { cells: newCells })] : [];
          });
          var slicedDetails = toDetailList(filteredGrid);
          return someIf(slicedDetails.length > 0, slicedDetails);
        }).map(function(slicedDetails) {
          return copy(slicedDetails);
        });
      };
      var global$2 = tinymce.util.Tools.resolve("tinymce.util.Tools");
      var getTDTHOverallStyle = function(dom, elm, name2) {
        var cells2 = dom.select("td,th", elm);
        var firstChildStyle;
        var checkChildren = function(firstChildStyle2, elms) {
          for (var i = 0; i < elms.length; i++) {
            var currentStyle = dom.getStyle(elms[i], name2);
            if (typeof firstChildStyle2 === "undefined") {
              firstChildStyle2 = currentStyle;
            }
            if (firstChildStyle2 !== currentStyle) {
              return "";
            }
          }
          return firstChildStyle2;
        };
        return checkChildren(firstChildStyle, cells2);
      };
      var applyAlign = function(editor, elm, name2) {
        if (name2) {
          editor.formatter.apply("align" + name2, {}, elm);
        }
      };
      var applyVAlign = function(editor, elm, name2) {
        if (name2) {
          editor.formatter.apply("valign" + name2, {}, elm);
        }
      };
      var unApplyAlign = function(editor, elm) {
        global$2.each("left center right".split(" "), function(name2) {
          editor.formatter.remove("align" + name2, {}, elm);
        });
      };
      var unApplyVAlign = function(editor, elm) {
        global$2.each("top middle bottom".split(" "), function(name2) {
          editor.formatter.remove("valign" + name2, {}, elm);
        });
      };
      var verticalAlignValues = [
        {
          text: "None",
          value: ""
        },
        {
          text: "Top",
          value: "top"
        },
        {
          text: "Middle",
          value: "middle"
        },
        {
          text: "Bottom",
          value: "bottom"
        }
      ];
      var hexColour = function(value2) {
        return { value: value2 };
      };
      var shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
      var longformRegex = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i;
      var isHexString = function(hex) {
        return shorthandRegex.test(hex) || longformRegex.test(hex);
      };
      var normalizeHex = function(hex) {
        return removeLeading(hex, "#").toUpperCase();
      };
      var fromString$1 = function(hex) {
        return isHexString(hex) ? Optional.some({ value: normalizeHex(hex) }) : Optional.none();
      };
      var toHex = function(component) {
        var hex = component.toString(16);
        return (hex.length === 1 ? "0" + hex : hex).toUpperCase();
      };
      var fromRgba = function(rgbaColour2) {
        var value2 = toHex(rgbaColour2.red) + toHex(rgbaColour2.green) + toHex(rgbaColour2.blue);
        return hexColour(value2);
      };
      var rgbRegex = /^rgb\((\d+),\s*(\d+),\s*(\d+)\)/;
      var rgbaRegex = /^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/;
      var rgbaColour = function(red, green, blue, alpha) {
        return {
          red,
          green,
          blue,
          alpha
        };
      };
      var fromStringValues = function(red, green, blue, alpha) {
        var r2 = parseInt(red, 10);
        var g = parseInt(green, 10);
        var b = parseInt(blue, 10);
        var a = parseFloat(alpha);
        return rgbaColour(r2, g, b, a);
      };
      var fromString = function(rgbaString) {
        if (rgbaString === "transparent") {
          return Optional.some(rgbaColour(0, 0, 0, 0));
        }
        var rgbMatch = rgbRegex.exec(rgbaString);
        if (rgbMatch !== null) {
          return Optional.some(fromStringValues(rgbMatch[1], rgbMatch[2], rgbMatch[3], "1"));
        }
        var rgbaMatch = rgbaRegex.exec(rgbaString);
        if (rgbaMatch !== null) {
          return Optional.some(fromStringValues(rgbaMatch[1], rgbaMatch[2], rgbaMatch[3], rgbaMatch[4]));
        }
        return Optional.none();
      };
      var anyToHex = function(color) {
        return fromString$1(color).orThunk(function() {
          return fromString(color).map(fromRgba);
        }).getOrThunk(function() {
          var canvas = document.createElement("canvas");
          canvas.height = 1;
          canvas.width = 1;
          var canvasContext = canvas.getContext("2d");
          canvasContext.clearRect(0, 0, canvas.width, canvas.height);
          canvasContext.fillStyle = "#FFFFFF";
          canvasContext.fillStyle = color;
          canvasContext.fillRect(0, 0, 1, 1);
          var rgba = canvasContext.getImageData(0, 0, 1, 1).data;
          var r2 = rgba[0];
          var g = rgba[1];
          var b = rgba[2];
          var a = rgba[3];
          return fromRgba(rgbaColour(r2, g, b, a));
        });
      };
      var Cell = function(initial) {
        var value2 = initial;
        var get2 = function() {
          return value2;
        };
        var set2 = function(v) {
          value2 = v;
        };
        return {
          get: get2,
          set: set2
        };
      };
      var singleton = function(doRevoke) {
        var subject = Cell(Optional.none());
        var revoke = function() {
          return subject.get().each(doRevoke);
        };
        var clear2 = function() {
          revoke();
          subject.set(Optional.none());
        };
        var isSet = function() {
          return subject.get().isSome();
        };
        var get2 = function() {
          return subject.get();
        };
        var set2 = function(s) {
          revoke();
          subject.set(Optional.some(s));
        };
        return {
          clear: clear2,
          isSet,
          get: get2,
          set: set2
        };
      };
      var unbindable = function() {
        return singleton(function(s) {
          return s.unbind();
        });
      };
      var value = function() {
        var subject = singleton(noop);
        var on2 = function(f) {
          return subject.get().each(f);
        };
        return __assign(__assign({}, subject), { on: on2 });
      };
      var onSetupToggle = function(editor, selections, formatName, formatValue) {
        return function(api2) {
          var boundCallback = unbindable();
          var isNone = isEmpty$1(formatValue);
          var init = function() {
            var selectedCells = getCellsFromSelection(selections);
            var checkNode = function(cell2) {
              return editor.formatter.match(formatName, { value: formatValue }, cell2.dom, isNone);
            };
            if (isNone) {
              api2.setActive(!exists(selectedCells, checkNode));
              boundCallback.set(editor.formatter.formatChanged(formatName, function(match) {
                return api2.setActive(!match);
              }, true));
            } else {
              api2.setActive(forall(selectedCells, checkNode));
              boundCallback.set(editor.formatter.formatChanged(formatName, api2.setActive, false, { value: formatValue }));
            }
          };
          editor.initialized ? init() : editor.on("init", init);
          return boundCallback.clear;
        };
      };
      var isListGroup = function(item) {
        return hasNonNullableKey(item, "menu");
      };
      var buildListItems = function(items) {
        return map$1(items, function(item) {
          var text = item.text || item.title;
          if (isListGroup(item)) {
            return {
              text,
              items: buildListItems(item.menu)
            };
          } else {
            return {
              text,
              value: item.value
            };
          }
        });
      };
      var buildMenuItems = function(editor, selections, items, format, onAction) {
        return map$1(items, function(item) {
          var text = item.text || item.title;
          if (isListGroup(item)) {
            return {
              type: "nestedmenuitem",
              text,
              getSubmenuItems: function() {
                return buildMenuItems(editor, selections, item.menu, format, onAction);
              }
            };
          } else {
            return {
              text,
              type: "togglemenuitem",
              onAction: function() {
                return onAction(item.value);
              },
              onSetup: onSetupToggle(editor, selections, format, item.value)
            };
          }
        });
      };
      var applyTableCellStyle = function(editor, style) {
        return function(value2) {
          var _a;
          editor.execCommand("mceTableApplyCellStyle", false, (_a = {}, _a[style] = value2, _a));
        };
      };
      var filterNoneItem = function(list) {
        return bind$2(list, function(item) {
          if (isListGroup(item)) {
            return [__assign(__assign({}, item), { menu: filterNoneItem(item.menu) })];
          } else {
            return isNotEmpty(item.value) ? [item] : [];
          }
        });
      };
      var generateMenuItemsCallback = function(editor, selections, items, format, onAction) {
        return function(callback) {
          return callback(buildMenuItems(editor, selections, items, format, onAction));
        };
      };
      var buildColorMenu = function(editor, colorList, style) {
        var colorMap = map$1(colorList, function(entry) {
          return {
            text: entry.title,
            value: "#" + anyToHex(entry.value).value,
            type: "choiceitem"
          };
        });
        return [{
          type: "fancymenuitem",
          fancytype: "colorswatch",
          initData: {
            colors: colorMap.length > 0 ? colorMap : void 0,
            allowCustomColors: false
          },
          onAction: function(data) {
            var _a;
            var value2 = data.value === "remove" ? "" : data.value;
            editor.execCommand("mceTableApplyCellStyle", false, (_a = {}, _a[style] = value2, _a));
          }
        }];
      };
      var changeRowHeader = function(editor) {
        return function() {
          var currentType = editor.queryCommandValue("mceTableRowType");
          var newType = currentType === "header" ? "body" : "header";
          editor.execCommand("mceTableRowType", false, { type: newType });
        };
      };
      var changeColumnHeader = function(editor) {
        return function() {
          var currentType = editor.queryCommandValue("mceTableColType");
          var newType = currentType === "th" ? "td" : "th";
          editor.execCommand("mceTableColType", false, { type: newType });
        };
      };
      var getClassList$1 = function(editor) {
        var classes = buildListItems(getCellClassList(editor));
        if (classes.length > 0) {
          return Optional.some({
            name: "class",
            type: "listbox",
            label: "Class",
            items: classes
          });
        }
        return Optional.none();
      };
      var children = [
        {
          name: "width",
          type: "input",
          label: "Width"
        },
        {
          name: "height",
          type: "input",
          label: "Height"
        },
        {
          name: "celltype",
          type: "listbox",
          label: "Cell type",
          items: [
            {
              text: "Cell",
              value: "td"
            },
            {
              text: "Header cell",
              value: "th"
            }
          ]
        },
        {
          name: "scope",
          type: "listbox",
          label: "Scope",
          items: [
            {
              text: "None",
              value: ""
            },
            {
              text: "Row",
              value: "row"
            },
            {
              text: "Column",
              value: "col"
            },
            {
              text: "Row group",
              value: "rowgroup"
            },
            {
              text: "Column group",
              value: "colgroup"
            }
          ]
        },
        {
          name: "halign",
          type: "listbox",
          label: "Horizontal align",
          items: [
            {
              text: "None",
              value: ""
            },
            {
              text: "Left",
              value: "left"
            },
            {
              text: "Center",
              value: "center"
            },
            {
              text: "Right",
              value: "right"
            }
          ]
        },
        {
          name: "valign",
          type: "listbox",
          label: "Vertical align",
          items: verticalAlignValues
        }
      ];
      var getItems$2 = function(editor) {
        return children.concat(getClassList$1(editor).toArray());
      };
      var getAdvancedTab = function(editor, dialogName) {
        var emptyBorderStyle = [{
          text: "Select...",
          value: ""
        }];
        var advTabItems = [
          {
            name: "borderstyle",
            type: "listbox",
            label: "Border style",
            items: emptyBorderStyle.concat(buildListItems(getTableBorderStyles(editor)))
          },
          {
            name: "bordercolor",
            type: "colorinput",
            label: "Border color"
          },
          {
            name: "backgroundcolor",
            type: "colorinput",
            label: "Background color"
          }
        ];
        var borderWidth = {
          name: "borderwidth",
          type: "input",
          label: "Border width"
        };
        var items = dialogName === "cell" ? [borderWidth].concat(advTabItems) : advTabItems;
        return {
          title: "Advanced",
          name: "advanced",
          items
        };
      };
      var modifiers = function(testTruthy) {
        return function(editor, node) {
          var dom = editor.dom;
          var setAttrib = function(attr, value2) {
            if (!testTruthy || value2) {
              dom.setAttrib(node, attr, value2);
            }
          };
          var setStyle = function(prop, value2) {
            if (!testTruthy || value2) {
              dom.setStyle(node, prop, value2);
            }
          };
          var setFormat = function(formatName, value2) {
            if (!testTruthy || value2) {
              if (value2 === "") {
                editor.formatter.remove(formatName, { value: null }, node, true);
              } else {
                editor.formatter.apply(formatName, { value: value2 }, node);
              }
            }
          };
          return {
            setAttrib,
            setStyle,
            setFormat
          };
        };
      };
      var DomModifier = {
        normal: modifiers(false),
        ifTruthy: modifiers(true)
      };
      var rgbToHex = function(dom) {
        return function(value2) {
          return startsWith(value2, "rgb") ? dom.toHex(value2) : value2;
        };
      };
      var extractAdvancedStyles = function(dom, elm) {
        var element = SugarElement.fromDom(elm);
        return {
          borderwidth: getRaw$2(element, "border-width").getOr(""),
          borderstyle: getRaw$2(element, "border-style").getOr(""),
          bordercolor: getRaw$2(element, "border-color").map(rgbToHex(dom)).getOr(""),
          backgroundcolor: getRaw$2(element, "background-color").map(rgbToHex(dom)).getOr("")
        };
      };
      var getSharedValues = function(data) {
        var baseData = data[0];
        var comparisonData = data.slice(1);
        each$2(comparisonData, function(items) {
          each$2(keys(baseData), function(key2) {
            each$1(items, function(itemValue, itemKey) {
              var comparisonValue = baseData[key2];
              if (comparisonValue !== "" && key2 === itemKey) {
                if (comparisonValue !== itemValue) {
                  baseData[key2] = "";
                }
              }
            });
          });
        });
        return baseData;
      };
      var getAlignment = function(formats, formatName, editor, elm) {
        return find$1(formats, function(name2) {
          return !isUndefined(editor.formatter.matchNode(elm, formatName + name2));
        }).getOr("");
      };
      var getHAlignment = curry(getAlignment, [
        "left",
        "center",
        "right"
      ], "align");
      var getVAlignment = curry(getAlignment, [
        "top",
        "middle",
        "bottom"
      ], "valign");
      var extractDataFromSettings = function(editor, hasAdvTableTab) {
        var style = getDefaultStyles(editor);
        var attrs = getDefaultAttributes(editor);
        var extractAdvancedStyleData = function(dom) {
          return {
            borderstyle: get$c(style, "border-style").getOr(""),
            bordercolor: rgbToHex(dom)(get$c(style, "border-color").getOr("")),
            backgroundcolor: rgbToHex(dom)(get$c(style, "background-color").getOr(""))
          };
        };
        var defaultData = {
          height: "",
          width: "100%",
          cellspacing: "",
          cellpadding: "",
          caption: false,
          class: "",
          align: "",
          border: ""
        };
        var getBorder = function() {
          var borderWidth = style["border-width"];
          if (shouldStyleWithCss(editor) && borderWidth) {
            return { border: borderWidth };
          }
          return get$c(attrs, "border").fold(function() {
            return {};
          }, function(border) {
            return { border };
          });
        };
        var advStyle = hasAdvTableTab ? extractAdvancedStyleData(editor.dom) : {};
        var getCellPaddingCellSpacing = function() {
          var spacing = get$c(style, "border-spacing").or(get$c(attrs, "cellspacing")).fold(function() {
            return {};
          }, function(cellspacing) {
            return { cellspacing };
          });
          var padding = get$c(style, "border-padding").or(get$c(attrs, "cellpadding")).fold(function() {
            return {};
          }, function(cellpadding) {
            return { cellpadding };
          });
          return __assign(__assign({}, spacing), padding);
        };
        var data = __assign(__assign(__assign(__assign(__assign(__assign({}, defaultData), style), attrs), advStyle), getBorder()), getCellPaddingCellSpacing());
        return data;
      };
      var getRowType = function(elm) {
        return table(SugarElement.fromDom(elm)).map(function(table2) {
          var target = { selection: fromDom(elm.cells) };
          return getRowsType(table2, target);
        }).getOr("");
      };
      var extractDataFromTableElement = function(editor, elm, hasAdvTableTab) {
        var getBorder = function(dom2, elm2) {
          var optBorderWidth = getRaw$2(SugarElement.fromDom(elm2), "border-width");
          if (shouldStyleWithCss(editor) && optBorderWidth.isSome()) {
            return optBorderWidth.getOr("");
          }
          return dom2.getAttrib(elm2, "border") || getTDTHOverallStyle(editor.dom, elm2, "border-width") || getTDTHOverallStyle(editor.dom, elm2, "border");
        };
        var dom = editor.dom;
        var cellspacing = shouldStyleWithCss(editor) ? dom.getStyle(elm, "border-spacing") || dom.getAttrib(elm, "cellspacing") : dom.getAttrib(elm, "cellspacing") || dom.getStyle(elm, "border-spacing");
        var cellpadding = shouldStyleWithCss(editor) ? getTDTHOverallStyle(dom, elm, "padding") || dom.getAttrib(elm, "cellpadding") : dom.getAttrib(elm, "cellpadding") || getTDTHOverallStyle(dom, elm, "padding");
        return __assign({
          width: dom.getStyle(elm, "width") || dom.getAttrib(elm, "width"),
          height: dom.getStyle(elm, "height") || dom.getAttrib(elm, "height"),
          cellspacing,
          cellpadding,
          border: getBorder(dom, elm),
          caption: !!dom.select("caption", elm)[0],
          class: dom.getAttrib(elm, "class", ""),
          align: getHAlignment(editor, elm)
        }, hasAdvTableTab ? extractAdvancedStyles(dom, elm) : {});
      };
      var extractDataFromRowElement = function(editor, elm, hasAdvancedRowTab2) {
        var dom = editor.dom;
        return __assign({
          height: dom.getStyle(elm, "height") || dom.getAttrib(elm, "height"),
          class: dom.getAttrib(elm, "class", ""),
          type: getRowType(elm),
          align: getHAlignment(editor, elm)
        }, hasAdvancedRowTab2 ? extractAdvancedStyles(dom, elm) : {});
      };
      var extractDataFromCellElement = function(editor, cell2, hasAdvancedCellTab2, column) {
        var dom = editor.dom;
        var colElm = column.getOr(cell2);
        var getStyle = function(element, style) {
          return dom.getStyle(element, style) || dom.getAttrib(element, style);
        };
        return __assign({
          width: getStyle(colElm, "width"),
          height: getStyle(cell2, "height"),
          scope: dom.getAttrib(cell2, "scope"),
          celltype: getNodeName(cell2),
          class: dom.getAttrib(cell2, "class", ""),
          halign: getHAlignment(editor, cell2),
          valign: getVAlignment(editor, cell2)
        }, hasAdvancedCellTab2 ? extractAdvancedStyles(dom, cell2) : {});
      };
      var getSelectedCells = function(table2, cells2) {
        var warehouse = Warehouse.fromTable(table2);
        var allCells = Warehouse.justCells(warehouse);
        var filtered = filter$2(allCells, function(cellA) {
          return exists(cells2, function(cellB) {
            return eq$1(cellA.element, cellB);
          });
        });
        return map$1(filtered, function(cell2) {
          return {
            element: cell2.element.dom,
            column: Warehouse.getColumnAt(warehouse, cell2.column).map(function(col2) {
              return col2.element.dom;
            })
          };
        });
      };
      var updateSimpleProps$1 = function(modifier, colModifier, data) {
        modifier.setAttrib("scope", data.scope);
        modifier.setAttrib("class", data.class);
        modifier.setStyle("height", addPxSuffix(data.height));
        colModifier.setStyle("width", addPxSuffix(data.width));
      };
      var updateAdvancedProps$1 = function(modifier, data) {
        modifier.setFormat("tablecellbackgroundcolor", data.backgroundcolor);
        modifier.setFormat("tablecellbordercolor", data.bordercolor);
        modifier.setFormat("tablecellborderstyle", data.borderstyle);
        modifier.setFormat("tablecellborderwidth", addPxSuffix(data.borderwidth));
      };
      var applyStyleData$1 = function(editor, cells2, data) {
        var isSingleCell = cells2.length === 1;
        each$2(cells2, function(item) {
          var cellElm = item.element;
          var modifier = isSingleCell ? DomModifier.normal(editor, cellElm) : DomModifier.ifTruthy(editor, cellElm);
          var colModifier = item.column.map(function(col2) {
            return isSingleCell ? DomModifier.normal(editor, col2) : DomModifier.ifTruthy(editor, col2);
          }).getOr(modifier);
          updateSimpleProps$1(modifier, colModifier, data);
          if (hasAdvancedCellTab(editor)) {
            updateAdvancedProps$1(modifier, data);
          }
          if (isSingleCell) {
            unApplyAlign(editor, cellElm);
            unApplyVAlign(editor, cellElm);
          }
          if (data.halign) {
            applyAlign(editor, cellElm, data.halign);
          }
          if (data.valign) {
            applyVAlign(editor, cellElm, data.valign);
          }
        });
      };
      var applyStructureData$1 = function(editor, data) {
        editor.execCommand("mceTableCellType", false, {
          type: data.celltype,
          no_events: true
        });
      };
      var applyCellData = function(editor, cells2, oldData, data) {
        var modifiedData = filter$1(data, function(value2, key2) {
          return oldData[key2] !== value2;
        });
        if (size(modifiedData) > 0 && cells2.length >= 1) {
          table(cells2[0]).each(function(table2) {
            var selectedCells = getSelectedCells(table2, cells2);
            var styleModified2 = size(filter$1(modifiedData, function(_value, key2) {
              return key2 !== "scope" && key2 !== "celltype";
            })) > 0;
            var structureModified2 = has$1(modifiedData, "celltype");
            if (styleModified2 || has$1(modifiedData, "scope")) {
              applyStyleData$1(editor, selectedCells, data);
            }
            if (structureModified2) {
              applyStructureData$1(editor, data);
            }
            fireTableModified(editor, table2.dom, {
              structure: structureModified2,
              style: styleModified2
            });
          });
        }
      };
      var onSubmitCellForm = function(editor, cells2, oldData, api2) {
        var data = api2.getData();
        api2.close();
        editor.undoManager.transact(function() {
          applyCellData(editor, cells2, oldData, data);
          editor.focus();
        });
      };
      var getData = function(editor, cells2) {
        var cellsData = table(cells2[0]).map(function(table2) {
          return map$1(getSelectedCells(table2, cells2), function(item) {
            return extractDataFromCellElement(editor, item.element, hasAdvancedCellTab(editor), item.column);
          });
        });
        return getSharedValues(cellsData.getOrDie());
      };
      var open$2 = function(editor, selections) {
        var cells2 = getCellsFromSelection(selections);
        if (cells2.length === 0) {
          return;
        }
        var data = getData(editor, cells2);
        var dialogTabPanel = {
          type: "tabpanel",
          tabs: [
            {
              title: "General",
              name: "general",
              items: getItems$2(editor)
            },
            getAdvancedTab(editor, "cell")
          ]
        };
        var dialogPanel = {
          type: "panel",
          items: [{
            type: "grid",
            columns: 2,
            items: getItems$2(editor)
          }]
        };
        editor.windowManager.open({
          title: "Cell Properties",
          size: "normal",
          body: hasAdvancedCellTab(editor) ? dialogTabPanel : dialogPanel,
          buttons: [
            {
              type: "cancel",
              name: "cancel",
              text: "Cancel"
            },
            {
              type: "submit",
              name: "save",
              text: "Save",
              primary: true
            }
          ],
          initialData: data,
          onSubmit: curry(onSubmitCellForm, editor, cells2, data)
        });
      };
      var getClassList = function(editor) {
        var classes = buildListItems(getRowClassList(editor));
        if (classes.length > 0) {
          return Optional.some({
            name: "class",
            type: "listbox",
            label: "Class",
            items: classes
          });
        }
        return Optional.none();
      };
      var formChildren = [
        {
          type: "listbox",
          name: "type",
          label: "Row type",
          items: [
            {
              text: "Header",
              value: "header"
            },
            {
              text: "Body",
              value: "body"
            },
            {
              text: "Footer",
              value: "footer"
            }
          ]
        },
        {
          type: "listbox",
          name: "align",
          label: "Alignment",
          items: [
            {
              text: "None",
              value: ""
            },
            {
              text: "Left",
              value: "left"
            },
            {
              text: "Center",
              value: "center"
            },
            {
              text: "Right",
              value: "right"
            }
          ]
        },
        {
          label: "Height",
          name: "height",
          type: "input"
        }
      ];
      var getItems$1 = function(editor) {
        return formChildren.concat(getClassList(editor).toArray());
      };
      var updateSimpleProps = function(modifier, data) {
        modifier.setAttrib("class", data.class);
        modifier.setStyle("height", addPxSuffix(data.height));
      };
      var updateAdvancedProps = function(modifier, data) {
        modifier.setStyle("background-color", data.backgroundcolor);
        modifier.setStyle("border-color", data.bordercolor);
        modifier.setStyle("border-style", data.borderstyle);
      };
      var applyStyleData = function(editor, rows2, data, oldData) {
        var isSingleRow = rows2.length === 1;
        each$2(rows2, function(rowElm) {
          var modifier = isSingleRow ? DomModifier.normal(editor, rowElm) : DomModifier.ifTruthy(editor, rowElm);
          updateSimpleProps(modifier, data);
          if (hasAdvancedRowTab(editor)) {
            updateAdvancedProps(modifier, data);
          }
          if (data.align !== oldData.align) {
            unApplyAlign(editor, rowElm);
            applyAlign(editor, rowElm, data.align);
          }
        });
      };
      var applyStructureData = function(editor, data) {
        editor.execCommand("mceTableRowType", false, {
          type: data.type,
          no_events: true
        });
      };
      var applyRowData = function(editor, rows2, oldData, data) {
        var modifiedData = filter$1(data, function(value2, key2) {
          return oldData[key2] !== value2;
        });
        if (size(modifiedData) > 0) {
          var typeModified_1 = has$1(modifiedData, "type");
          var styleModified_1 = typeModified_1 ? size(modifiedData) > 1 : true;
          if (styleModified_1) {
            applyStyleData(editor, rows2, data, oldData);
          }
          if (typeModified_1) {
            applyStructureData(editor, data);
          }
          table(SugarElement.fromDom(rows2[0])).each(function(table2) {
            return fireTableModified(editor, table2.dom, {
              structure: typeModified_1,
              style: styleModified_1
            });
          });
        }
      };
      var onSubmitRowForm = function(editor, rows2, oldData, api2) {
        var data = api2.getData();
        api2.close();
        editor.undoManager.transact(function() {
          applyRowData(editor, rows2, oldData, data);
          editor.focus();
        });
      };
      var open$1 = function(editor) {
        var rows2 = getRowsFromSelection(getSelectionStart(editor), ephemera.selected);
        if (rows2.length === 0) {
          return;
        }
        var rowsData = map$1(rows2, function(rowElm) {
          return extractDataFromRowElement(editor, rowElm.dom, hasAdvancedRowTab(editor));
        });
        var data = getSharedValues(rowsData);
        var dialogTabPanel = {
          type: "tabpanel",
          tabs: [
            {
              title: "General",
              name: "general",
              items: getItems$1(editor)
            },
            getAdvancedTab(editor, "row")
          ]
        };
        var dialogPanel = {
          type: "panel",
          items: [{
            type: "grid",
            columns: 2,
            items: getItems$1(editor)
          }]
        };
        editor.windowManager.open({
          title: "Row Properties",
          size: "normal",
          body: hasAdvancedRowTab(editor) ? dialogTabPanel : dialogPanel,
          buttons: [
            {
              type: "cancel",
              name: "cancel",
              text: "Cancel"
            },
            {
              type: "submit",
              name: "save",
              text: "Save",
              primary: true
            }
          ],
          initialData: data,
          onSubmit: curry(onSubmitRowForm, editor, map$1(rows2, function(r2) {
            return r2.dom;
          }), data)
        });
      };
      var getItems = function(editor, classes, insertNewTable) {
        var rowColCountItems = !insertNewTable ? [] : [
          {
            type: "input",
            name: "cols",
            label: "Cols",
            inputMode: "numeric"
          },
          {
            type: "input",
            name: "rows",
            label: "Rows",
            inputMode: "numeric"
          }
        ];
        var alwaysItems = [
          {
            type: "input",
            name: "width",
            label: "Width"
          },
          {
            type: "input",
            name: "height",
            label: "Height"
          }
        ];
        var appearanceItems = hasAppearanceOptions(editor) ? [
          {
            type: "input",
            name: "cellspacing",
            label: "Cell spacing",
            inputMode: "numeric"
          },
          {
            type: "input",
            name: "cellpadding",
            label: "Cell padding",
            inputMode: "numeric"
          },
          {
            type: "input",
            name: "border",
            label: "Border width"
          },
          {
            type: "label",
            label: "Caption",
            items: [{
              type: "checkbox",
              name: "caption",
              label: "Show caption"
            }]
          }
        ] : [];
        var alignmentItem = [{
          type: "listbox",
          name: "align",
          label: "Alignment",
          items: [
            {
              text: "None",
              value: ""
            },
            {
              text: "Left",
              value: "left"
            },
            {
              text: "Center",
              value: "center"
            },
            {
              text: "Right",
              value: "right"
            }
          ]
        }];
        var classListItem = classes.length > 0 ? [{
          type: "listbox",
          name: "class",
          label: "Class",
          items: classes
        }] : [];
        return rowColCountItems.concat(alwaysItems).concat(appearanceItems).concat(alignmentItem).concat(classListItem);
      };
      var styleTDTH = function(dom, elm, name2, value2) {
        if (elm.tagName === "TD" || elm.tagName === "TH") {
          if (isString(name2)) {
            dom.setStyle(elm, name2, value2);
          } else {
            dom.setStyle(elm, name2);
          }
        } else {
          if (elm.children) {
            for (var i = 0; i < elm.children.length; i++) {
              styleTDTH(dom, elm.children[i], name2, value2);
            }
          }
        }
      };
      var applyDataToElement = function(editor, tableElm, data) {
        var dom = editor.dom;
        var attrs = {};
        var styles2 = {};
        attrs.class = data.class;
        styles2.height = addPxSuffix(data.height);
        if (dom.getAttrib(tableElm, "width") && !shouldStyleWithCss(editor)) {
          attrs.width = removePxSuffix(data.width);
        } else {
          styles2.width = addPxSuffix(data.width);
        }
        if (shouldStyleWithCss(editor)) {
          styles2["border-width"] = addPxSuffix(data.border);
          styles2["border-spacing"] = addPxSuffix(data.cellspacing);
        } else {
          attrs.border = data.border;
          attrs.cellpadding = data.cellpadding;
          attrs.cellspacing = data.cellspacing;
        }
        if (shouldStyleWithCss(editor) && tableElm.children) {
          for (var i = 0; i < tableElm.children.length; i++) {
            styleTDTH(dom, tableElm.children[i], {
              "border-width": addPxSuffix(data.border),
              "padding": addPxSuffix(data.cellpadding)
            });
            if (hasAdvancedTableTab(editor)) {
              styleTDTH(dom, tableElm.children[i], { "border-color": data.bordercolor });
            }
          }
        }
        if (hasAdvancedTableTab(editor)) {
          styles2["background-color"] = data.backgroundcolor;
          styles2["border-color"] = data.bordercolor;
          styles2["border-style"] = data.borderstyle;
        }
        attrs.style = dom.serializeStyle(__assign(__assign({}, getDefaultStyles(editor)), styles2));
        dom.setAttribs(tableElm, __assign(__assign({}, getDefaultAttributes(editor)), attrs));
      };
      var onSubmitTableForm = function(editor, tableElm, oldData, api2) {
        var dom = editor.dom;
        var data = api2.getData();
        var modifiedData = filter$1(data, function(value2, key2) {
          return oldData[key2] !== value2;
        });
        api2.close();
        if (data.class === "") {
          delete data.class;
        }
        editor.undoManager.transact(function() {
          if (!tableElm) {
            var cols = parseInt(data.cols, 10) || 1;
            var rows2 = parseInt(data.rows, 10) || 1;
            tableElm = insert(editor, cols, rows2, 0, 0);
          }
          if (size(modifiedData) > 0) {
            applyDataToElement(editor, tableElm, data);
            var captionElm = dom.select("caption", tableElm)[0];
            if (captionElm && !data.caption || !captionElm && data.caption) {
              editor.execCommand("mceTableToggleCaption");
            }
            if (data.align === "") {
              unApplyAlign(editor, tableElm);
            } else {
              applyAlign(editor, tableElm, data.align);
            }
          }
          editor.focus();
          editor.addVisual();
          if (size(modifiedData) > 0) {
            var captionModified = has$1(modifiedData, "caption");
            var styleModified2 = captionModified ? size(modifiedData) > 1 : true;
            fireTableModified(editor, tableElm, {
              structure: captionModified,
              style: styleModified2
            });
          }
        });
      };
      var open = function(editor, insertNewTable) {
        var dom = editor.dom;
        var tableElm;
        var data = extractDataFromSettings(editor, hasAdvancedTableTab(editor));
        if (insertNewTable === false) {
          tableElm = dom.getParent(editor.selection.getStart(), "table", editor.getBody());
          if (tableElm) {
            data = extractDataFromTableElement(editor, tableElm, hasAdvancedTableTab(editor));
          } else {
            if (hasAdvancedTableTab(editor)) {
              data.borderstyle = "";
              data.bordercolor = "";
              data.backgroundcolor = "";
            }
          }
        } else {
          data.cols = "1";
          data.rows = "1";
          if (hasAdvancedTableTab(editor)) {
            data.borderstyle = "";
            data.bordercolor = "";
            data.backgroundcolor = "";
          }
        }
        var classes = buildListItems(getTableClassList(editor));
        if (classes.length > 0) {
          if (data.class) {
            data.class = data.class.replace(/\s*mce\-item\-table\s*/g, "");
          }
        }
        var generalPanel = {
          type: "grid",
          columns: 2,
          items: getItems(editor, classes, insertNewTable)
        };
        var nonAdvancedForm = function() {
          return {
            type: "panel",
            items: [generalPanel]
          };
        };
        var advancedForm = function() {
          return {
            type: "tabpanel",
            tabs: [
              {
                title: "General",
                name: "general",
                items: [generalPanel]
              },
              getAdvancedTab(editor, "table")
            ]
          };
        };
        var dialogBody = hasAdvancedTableTab(editor) ? advancedForm() : nonAdvancedForm();
        editor.windowManager.open({
          title: "Table Properties",
          size: "normal",
          body: dialogBody,
          onSubmit: curry(onSubmitTableForm, editor, tableElm, data),
          buttons: [
            {
              type: "cancel",
              name: "cancel",
              text: "Cancel"
            },
            {
              type: "submit",
              name: "save",
              text: "Save",
              primary: true
            }
          ],
          initialData: data
        });
      };
      var getSelectionStartCellOrCaption = function(editor) {
        return getSelectionCellOrCaption(getSelectionStart(editor), getIsRoot(editor));
      };
      var getSelectionStartCell = function(editor) {
        return getSelectionCell(getSelectionStart(editor), getIsRoot(editor));
      };
      var registerCommands = function(editor, actions, cellSelection, selections, clipboard) {
        var isRoot = getIsRoot(editor);
        var eraseTable = function() {
          return getSelectionStartCellOrCaption(editor).each(function(cellOrCaption) {
            table(cellOrCaption, isRoot).filter(not(isRoot)).each(function(table2) {
              var cursor = SugarElement.fromText("");
              after$5(table2, cursor);
              remove$5(table2);
              if (editor.dom.isEmpty(editor.getBody())) {
                editor.setContent("");
                editor.selection.setCursorLocation();
              } else {
                var rng = editor.dom.createRng();
                rng.setStart(cursor.dom, 0);
                rng.setEnd(cursor.dom, 0);
                editor.selection.setRng(rng);
                editor.nodeChanged();
              }
            });
          });
        };
        var setSizingMode = function(sizing) {
          return getSelectionStartCellOrCaption(editor).each(function(cellOrCaption) {
            var isForcedSizing = isResponsiveForced(editor) || isPixelsForced(editor) || isPercentagesForced(editor);
            if (!isForcedSizing) {
              table(cellOrCaption, isRoot).each(function(table2) {
                if (sizing === "relative" && !isPercentSizing(table2)) {
                  enforcePercentage(table2);
                } else if (sizing === "fixed" && !isPixelSizing(table2)) {
                  enforcePixels(table2);
                } else if (sizing === "responsive" && !isNoneSizing(table2)) {
                  enforceNone(table2);
                }
                removeDataStyle(table2);
                fireTableModified(editor, table2.dom, structureModified);
              });
            }
          });
        };
        var getTableFromCell = function(cell2) {
          return table(cell2, isRoot);
        };
        var performActionOnSelection = function(action) {
          return getSelectionStartCell(editor).bind(function(cell2) {
            return getTableFromCell(cell2).map(function(table2) {
              return action(table2, cell2);
            });
          });
        };
        var toggleTableClass = function(_ui, clazz) {
          performActionOnSelection(function(table2) {
            editor.formatter.toggle("tableclass", { value: clazz }, table2.dom);
            fireTableModified(editor, table2.dom, styleModified);
          });
        };
        var toggleTableCellClass = function(_ui, clazz) {
          performActionOnSelection(function(table2) {
            var selectedCells = getCellsFromSelection(selections);
            var allHaveClass = forall(selectedCells, function(cell2) {
              return editor.formatter.match("tablecellclass", { value: clazz }, cell2.dom);
            });
            var formatterAction = allHaveClass ? editor.formatter.remove : editor.formatter.apply;
            each$2(selectedCells, function(cell2) {
              return formatterAction("tablecellclass", { value: clazz }, cell2.dom);
            });
            fireTableModified(editor, table2.dom, styleModified);
          });
        };
        var toggleCaption = function() {
          getSelectionStartCellOrCaption(editor).each(function(cellOrCaption) {
            table(cellOrCaption, isRoot).each(function(table2) {
              child$1(table2, "caption").fold(function() {
                var caption = SugarElement.fromTag("caption");
                append$1(caption, SugarElement.fromText("Caption"));
                appendAt(table2, caption, 0);
                editor.selection.setCursorLocation(caption.dom, 0);
              }, function(caption) {
                if (isTag("caption")(cellOrCaption)) {
                  one("td", table2).each(function(td) {
                    return editor.selection.setCursorLocation(td.dom, 0);
                  });
                }
                remove$5(caption);
              });
              fireTableModified(editor, table2.dom, structureModified);
            });
          });
        };
        var postExecute = function(_data) {
          editor.focus();
        };
        var actOnSelection = function(execute, noEvents) {
          if (noEvents === void 0) {
            noEvents = false;
          }
          return performActionOnSelection(function(table2, startCell) {
            var targets = forMenu(selections, table2, startCell);
            execute(table2, targets, noEvents).each(postExecute);
          });
        };
        var copyRowSelection = function() {
          return performActionOnSelection(function(table2, startCell) {
            var targets = forMenu(selections, table2, startCell);
            var generators = cellOperations(noop, SugarElement.fromDom(editor.getDoc()), Optional.none());
            return copyRows(table2, targets, generators);
          });
        };
        var copyColSelection = function() {
          return performActionOnSelection(function(table2, startCell) {
            var targets = forMenu(selections, table2, startCell);
            return copyCols(table2, targets);
          });
        };
        var pasteOnSelection = function(execute, getRows) {
          return getRows().each(function(rows2) {
            var clonedRows = map$1(rows2, function(row2) {
              return deep(row2);
            });
            performActionOnSelection(function(table2, startCell) {
              var generators = paste$1(SugarElement.fromDom(editor.getDoc()));
              var targets = pasteRows(selections, startCell, clonedRows, generators);
              execute(table2, targets).each(postExecute);
            });
          });
        };
        var actOnType = function(getAction) {
          return function(_ui, args) {
            return get$c(args, "type").each(function(type2) {
              actOnSelection(getAction(type2), args.no_events);
            });
          };
        };
        each$1({
          mceTableSplitCells: function() {
            return actOnSelection(actions.unmergeCells);
          },
          mceTableMergeCells: function() {
            return actOnSelection(actions.mergeCells);
          },
          mceTableInsertRowBefore: function() {
            return actOnSelection(actions.insertRowsBefore);
          },
          mceTableInsertRowAfter: function() {
            return actOnSelection(actions.insertRowsAfter);
          },
          mceTableInsertColBefore: function() {
            return actOnSelection(actions.insertColumnsBefore);
          },
          mceTableInsertColAfter: function() {
            return actOnSelection(actions.insertColumnsAfter);
          },
          mceTableDeleteCol: function() {
            return actOnSelection(actions.deleteColumn);
          },
          mceTableDeleteRow: function() {
            return actOnSelection(actions.deleteRow);
          },
          mceTableCutCol: function() {
            return copyColSelection().each(function(selection2) {
              clipboard.setColumns(selection2);
              actOnSelection(actions.deleteColumn);
            });
          },
          mceTableCutRow: function() {
            return copyRowSelection().each(function(selection2) {
              clipboard.setRows(selection2);
              actOnSelection(actions.deleteRow);
            });
          },
          mceTableCopyCol: function() {
            return copyColSelection().each(function(selection2) {
              return clipboard.setColumns(selection2);
            });
          },
          mceTableCopyRow: function() {
            return copyRowSelection().each(function(selection2) {
              return clipboard.setRows(selection2);
            });
          },
          mceTablePasteColBefore: function() {
            return pasteOnSelection(actions.pasteColsBefore, clipboard.getColumns);
          },
          mceTablePasteColAfter: function() {
            return pasteOnSelection(actions.pasteColsAfter, clipboard.getColumns);
          },
          mceTablePasteRowBefore: function() {
            return pasteOnSelection(actions.pasteRowsBefore, clipboard.getRows);
          },
          mceTablePasteRowAfter: function() {
            return pasteOnSelection(actions.pasteRowsAfter, clipboard.getRows);
          },
          mceTableDelete: eraseTable,
          mceTableCellToggleClass: toggleTableCellClass,
          mceTableToggleClass: toggleTableClass,
          mceTableToggleCaption: toggleCaption,
          mceTableSizingMode: function(_ui, sizing) {
            return setSizingMode(sizing);
          },
          mceTableCellType: actOnType(function(type2) {
            return type2 === "th" ? actions.makeCellsHeader : actions.unmakeCellsHeader;
          }),
          mceTableColType: actOnType(function(type2) {
            return type2 === "th" ? actions.makeColumnsHeader : actions.unmakeColumnsHeader;
          }),
          mceTableRowType: actOnType(function(type2) {
            switch (type2) {
              case "header":
                return actions.makeRowsHeader;
              case "footer":
                return actions.makeRowsFooter;
              default:
                return actions.makeRowsBody;
            }
          })
        }, function(func, name2) {
          return editor.addCommand(name2, func);
        });
        each$1({
          mceTableProps: curry(open, editor, false),
          mceTableRowProps: curry(open$1, editor),
          mceTableCellProps: curry(open$2, editor, selections)
        }, function(func, name2) {
          return editor.addCommand(name2, function() {
            return func();
          });
        });
        editor.addCommand("mceInsertTable", function(_ui, args) {
          if (isObject(args) && keys(args).length > 0) {
            insertTableWithDataValidation(editor, args.rows, args.columns, args.options, "Invalid values for mceInsertTable - rows and columns values are required to insert a table.");
          } else {
            open(editor, true);
          }
        });
        editor.addCommand("mceTableApplyCellStyle", function(_ui, args) {
          var getFormatName = function(style) {
            return "tablecell" + style.toLowerCase().replace("-", "");
          };
          if (!isObject(args)) {
            return;
          }
          var cells2 = getCellsFromSelection(selections);
          if (cells2.length === 0) {
            return;
          }
          var validArgs = filter$1(args, function(value2, style) {
            return editor.formatter.has(getFormatName(style)) && isString(value2);
          });
          if (isEmpty(validArgs)) {
            return;
          }
          each$1(validArgs, function(value2, style) {
            each$2(cells2, function(cell2) {
              DomModifier.normal(editor, cell2.dom).setFormat(getFormatName(style), value2);
            });
          });
          getTableFromCell(cells2[0]).each(function(table2) {
            return fireTableModified(editor, table2.dom, styleModified);
          });
        });
      };
      var registerQueryCommands = function(editor, actions, selections) {
        var isRoot = getIsRoot(editor);
        var lookupOnSelection = function(action) {
          return getSelectionCell(getSelectionStart(editor)).bind(function(cell2) {
            return table(cell2, isRoot).map(function(table2) {
              var targets = forMenu(selections, table2, cell2);
              return action(table2, targets);
            });
          }).getOr("");
        };
        each$1({
          mceTableRowType: function() {
            return lookupOnSelection(actions.getTableRowType);
          },
          mceTableCellType: function() {
            return lookupOnSelection(actions.getTableCellType);
          },
          mceTableColType: function() {
            return lookupOnSelection(actions.getTableColType);
          }
        }, function(func, name2) {
          return editor.addQueryValueHandler(name2, func);
        });
      };
      var Clipboard = function() {
        var rows2 = value();
        var cols = value();
        return {
          getRows: rows2.get,
          setRows: function(r2) {
            r2.fold(rows2.clear, rows2.set);
            cols.clear();
          },
          clearRows: rows2.clear,
          getColumns: cols.get,
          setColumns: function(c) {
            c.fold(cols.clear, cols.set);
            rows2.clear();
          },
          clearColumns: cols.clear
        };
      };
      var genericBase = {
        remove_similar: true,
        inherit: false
      };
      var cellBase = __assign({ selector: "td,th" }, genericBase);
      var cellFormats = {
        tablecellbackgroundcolor: __assign({ styles: { backgroundColor: "%value" } }, cellBase),
        tablecellverticalalign: __assign({ styles: { "vertical-align": "%value" } }, cellBase),
        tablecellbordercolor: __assign({ styles: { borderColor: "%value" } }, cellBase),
        tablecellclass: __assign({ classes: ["%value"] }, cellBase),
        tableclass: __assign({
          selector: "table",
          classes: ["%value"]
        }, genericBase),
        tablecellborderstyle: __assign({ styles: { borderStyle: "%value" } }, cellBase),
        tablecellborderwidth: __assign({ styles: { borderWidth: "%value" } }, cellBase)
      };
      var registerFormats = function(editor) {
        editor.formatter.register(cellFormats);
      };
      var adt$5 = Adt.generate([
        { none: ["current"] },
        { first: ["current"] },
        {
          middle: [
            "current",
            "target"
          ]
        },
        { last: ["current"] }
      ]);
      var none = function(current) {
        if (current === void 0) {
          current = void 0;
        }
        return adt$5.none(current);
      };
      var CellLocation = __assign(__assign({}, adt$5), { none });
      var walk = function(all2, current, index, direction, isEligible) {
        if (isEligible === void 0) {
          isEligible = always;
        }
        var forwards = direction === 1;
        if (!forwards && index <= 0) {
          return CellLocation.first(all2[0]);
        } else if (forwards && index >= all2.length - 1) {
          return CellLocation.last(all2[all2.length - 1]);
        } else {
          var newIndex = index + direction;
          var elem = all2[newIndex];
          return isEligible(elem) ? CellLocation.middle(current, elem) : walk(all2, current, newIndex, direction, isEligible);
        }
      };
      var detect$1 = function(current, isRoot) {
        return table(current, isRoot).bind(function(table2) {
          var all2 = cells$1(table2);
          var index = findIndex(all2, function(x) {
            return eq$1(current, x);
          });
          return index.map(function(index2) {
            return {
              index: index2,
              all: all2
            };
          });
        });
      };
      var next = function(current, isEligible, isRoot) {
        var detection = detect$1(current, isRoot);
        return detection.fold(function() {
          return CellLocation.none(current);
        }, function(info) {
          return walk(info.all, current, info.index, 1, isEligible);
        });
      };
      var prev = function(current, isEligible, isRoot) {
        var detection = detect$1(current, isRoot);
        return detection.fold(function() {
          return CellLocation.none();
        }, function(info) {
          return walk(info.all, current, info.index, -1, isEligible);
        });
      };
      var create$2 = function(start, soffset, finish, foffset) {
        return {
          start,
          soffset,
          finish,
          foffset
        };
      };
      var SimRange = { create: create$2 };
      var adt$4 = Adt.generate([
        { before: ["element"] },
        {
          on: [
            "element",
            "offset"
          ]
        },
        { after: ["element"] }
      ]);
      var cata$1 = function(subject, onBefore, onOn, onAfter) {
        return subject.fold(onBefore, onOn, onAfter);
      };
      var getStart$1 = function(situ) {
        return situ.fold(identity, identity, identity);
      };
      var before$2 = adt$4.before;
      var on = adt$4.on;
      var after$3 = adt$4.after;
      var Situ = {
        before: before$2,
        on,
        after: after$3,
        cata: cata$1,
        getStart: getStart$1
      };
      var adt$3 = Adt.generate([
        { domRange: ["rng"] },
        {
          relative: [
            "startSitu",
            "finishSitu"
          ]
        },
        {
          exact: [
            "start",
            "soffset",
            "finish",
            "foffset"
          ]
        }
      ]);
      var exactFromRange = function(simRange) {
        return adt$3.exact(simRange.start, simRange.soffset, simRange.finish, simRange.foffset);
      };
      var getStart = function(selection2) {
        return selection2.match({
          domRange: function(rng) {
            return SugarElement.fromDom(rng.startContainer);
          },
          relative: function(startSitu, _finishSitu) {
            return Situ.getStart(startSitu);
          },
          exact: function(start, _soffset, _finish, _foffset) {
            return start;
          }
        });
      };
      var domRange = adt$3.domRange;
      var relative = adt$3.relative;
      var exact = adt$3.exact;
      var getWin = function(selection2) {
        var start = getStart(selection2);
        return defaultView(start);
      };
      var range = SimRange.create;
      var SimSelection = {
        domRange,
        relative,
        exact,
        exactFromRange,
        getWin,
        range
      };
      var selectNode = function(win, element) {
        var rng = win.document.createRange();
        rng.selectNode(element.dom);
        return rng;
      };
      var selectNodeContents = function(win, element) {
        var rng = win.document.createRange();
        selectNodeContentsUsing(rng, element);
        return rng;
      };
      var selectNodeContentsUsing = function(rng, element) {
        return rng.selectNodeContents(element.dom);
      };
      var setStart = function(rng, situ) {
        situ.fold(function(e) {
          rng.setStartBefore(e.dom);
        }, function(e, o) {
          rng.setStart(e.dom, o);
        }, function(e) {
          rng.setStartAfter(e.dom);
        });
      };
      var setFinish = function(rng, situ) {
        situ.fold(function(e) {
          rng.setEndBefore(e.dom);
        }, function(e, o) {
          rng.setEnd(e.dom, o);
        }, function(e) {
          rng.setEndAfter(e.dom);
        });
      };
      var relativeToNative = function(win, startSitu, finishSitu) {
        var range2 = win.document.createRange();
        setStart(range2, startSitu);
        setFinish(range2, finishSitu);
        return range2;
      };
      var exactToNative = function(win, start, soffset, finish, foffset) {
        var rng = win.document.createRange();
        rng.setStart(start.dom, soffset);
        rng.setEnd(finish.dom, foffset);
        return rng;
      };
      var toRect = function(rect) {
        return {
          left: rect.left,
          top: rect.top,
          right: rect.right,
          bottom: rect.bottom,
          width: rect.width,
          height: rect.height
        };
      };
      var getFirstRect$1 = function(rng) {
        var rects = rng.getClientRects();
        var rect = rects.length > 0 ? rects[0] : rng.getBoundingClientRect();
        return rect.width > 0 || rect.height > 0 ? Optional.some(rect).map(toRect) : Optional.none();
      };
      var adt$2 = Adt.generate([
        {
          ltr: [
            "start",
            "soffset",
            "finish",
            "foffset"
          ]
        },
        {
          rtl: [
            "start",
            "soffset",
            "finish",
            "foffset"
          ]
        }
      ]);
      var fromRange = function(win, type2, range2) {
        return type2(SugarElement.fromDom(range2.startContainer), range2.startOffset, SugarElement.fromDom(range2.endContainer), range2.endOffset);
      };
      var getRanges = function(win, selection2) {
        return selection2.match({
          domRange: function(rng) {
            return {
              ltr: constant(rng),
              rtl: Optional.none
            };
          },
          relative: function(startSitu, finishSitu) {
            return {
              ltr: cached(function() {
                return relativeToNative(win, startSitu, finishSitu);
              }),
              rtl: cached(function() {
                return Optional.some(relativeToNative(win, finishSitu, startSitu));
              })
            };
          },
          exact: function(start, soffset, finish, foffset) {
            return {
              ltr: cached(function() {
                return exactToNative(win, start, soffset, finish, foffset);
              }),
              rtl: cached(function() {
                return Optional.some(exactToNative(win, finish, foffset, start, soffset));
              })
            };
          }
        });
      };
      var doDiagnose = function(win, ranges) {
        var rng = ranges.ltr();
        if (rng.collapsed) {
          var reversed = ranges.rtl().filter(function(rev) {
            return rev.collapsed === false;
          });
          return reversed.map(function(rev) {
            return adt$2.rtl(SugarElement.fromDom(rev.endContainer), rev.endOffset, SugarElement.fromDom(rev.startContainer), rev.startOffset);
          }).getOrThunk(function() {
            return fromRange(win, adt$2.ltr, rng);
          });
        } else {
          return fromRange(win, adt$2.ltr, rng);
        }
      };
      var diagnose = function(win, selection2) {
        var ranges = getRanges(win, selection2);
        return doDiagnose(win, ranges);
      };
      var asLtrRange = function(win, selection2) {
        var diagnosis = diagnose(win, selection2);
        return diagnosis.match({
          ltr: function(start, soffset, finish, foffset) {
            var rng = win.document.createRange();
            rng.setStart(start.dom, soffset);
            rng.setEnd(finish.dom, foffset);
            return rng;
          },
          rtl: function(start, soffset, finish, foffset) {
            var rng = win.document.createRange();
            rng.setStart(finish.dom, foffset);
            rng.setEnd(start.dom, soffset);
            return rng;
          }
        });
      };
      adt$2.ltr;
      adt$2.rtl;
      var searchForPoint = function(rectForOffset, x, y, maxX, length) {
        if (length === 0) {
          return 0;
        } else if (x === maxX) {
          return length - 1;
        }
        var xDelta = maxX;
        for (var i = 1; i < length; i++) {
          var rect = rectForOffset(i);
          var curDeltaX = Math.abs(x - rect.left);
          if (y <= rect.bottom) {
            if (y < rect.top || curDeltaX > xDelta) {
              return i - 1;
            } else {
              xDelta = curDeltaX;
            }
          }
        }
        return 0;
      };
      var inRect = function(rect, x, y) {
        return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;
      };
      var locateOffset = function(doc, textnode, x, y, rect) {
        var rangeForOffset = function(o) {
          var r2 = doc.dom.createRange();
          r2.setStart(textnode.dom, o);
          r2.collapse(true);
          return r2;
        };
        var rectForOffset = function(o) {
          var r2 = rangeForOffset(o);
          return r2.getBoundingClientRect();
        };
        var length = get$9(textnode).length;
        var offset = searchForPoint(rectForOffset, x, y, rect.right, length);
        return rangeForOffset(offset);
      };
      var locate$1 = function(doc, node, x, y) {
        var r2 = doc.dom.createRange();
        r2.selectNode(node.dom);
        var rects = r2.getClientRects();
        var foundRect = findMap(rects, function(rect) {
          return inRect(rect, x, y) ? Optional.some(rect) : Optional.none();
        });
        return foundRect.map(function(rect) {
          return locateOffset(doc, node, x, y, rect);
        });
      };
      var searchInChildren = function(doc, node, x, y) {
        var r2 = doc.dom.createRange();
        var nodes = children$3(node);
        return findMap(nodes, function(n) {
          r2.selectNode(n.dom);
          return inRect(r2.getBoundingClientRect(), x, y) ? locateNode(doc, n, x, y) : Optional.none();
        });
      };
      var locateNode = function(doc, node, x, y) {
        return isText(node) ? locate$1(doc, node, x, y) : searchInChildren(doc, node, x, y);
      };
      var locate = function(doc, node, x, y) {
        var r2 = doc.dom.createRange();
        r2.selectNode(node.dom);
        var rect = r2.getBoundingClientRect();
        var boundedX = Math.max(rect.left, Math.min(rect.right, x));
        var boundedY = Math.max(rect.top, Math.min(rect.bottom, y));
        return locateNode(doc, node, boundedX, boundedY);
      };
      var COLLAPSE_TO_LEFT = true;
      var COLLAPSE_TO_RIGHT = false;
      var getCollapseDirection = function(rect, x) {
        return x - rect.left < rect.right - x ? COLLAPSE_TO_LEFT : COLLAPSE_TO_RIGHT;
      };
      var createCollapsedNode = function(doc, target, collapseDirection) {
        var r2 = doc.dom.createRange();
        r2.selectNode(target.dom);
        r2.collapse(collapseDirection);
        return r2;
      };
      var locateInElement = function(doc, node, x) {
        var cursorRange = doc.dom.createRange();
        cursorRange.selectNode(node.dom);
        var rect = cursorRange.getBoundingClientRect();
        var collapseDirection = getCollapseDirection(rect, x);
        var f = collapseDirection === COLLAPSE_TO_LEFT ? first : last$1;
        return f(node).map(function(target) {
          return createCollapsedNode(doc, target, collapseDirection);
        });
      };
      var locateInEmpty = function(doc, node, x) {
        var rect = node.dom.getBoundingClientRect();
        var collapseDirection = getCollapseDirection(rect, x);
        return Optional.some(createCollapsedNode(doc, node, collapseDirection));
      };
      var search = function(doc, node, x) {
        var f = children$3(node).length === 0 ? locateInEmpty : locateInElement;
        return f(doc, node, x);
      };
      var caretPositionFromPoint = function(doc, x, y) {
        var _a, _b;
        return Optional.from((_b = (_a = doc.dom).caretPositionFromPoint) === null || _b === void 0 ? void 0 : _b.call(_a, x, y)).bind(function(pos) {
          if (pos.offsetNode === null) {
            return Optional.none();
          }
          var r2 = doc.dom.createRange();
          r2.setStart(pos.offsetNode, pos.offset);
          r2.collapse();
          return Optional.some(r2);
        });
      };
      var caretRangeFromPoint = function(doc, x, y) {
        var _a, _b;
        return Optional.from((_b = (_a = doc.dom).caretRangeFromPoint) === null || _b === void 0 ? void 0 : _b.call(_a, x, y));
      };
      var searchTextNodes = function(doc, node, x, y) {
        var r2 = doc.dom.createRange();
        r2.selectNode(node.dom);
        var rect = r2.getBoundingClientRect();
        var boundedX = Math.max(rect.left, Math.min(rect.right, x));
        var boundedY = Math.max(rect.top, Math.min(rect.bottom, y));
        return locate(doc, node, boundedX, boundedY);
      };
      var searchFromPoint = function(doc, x, y) {
        return SugarElement.fromPoint(doc, x, y).bind(function(elem) {
          var fallback2 = function() {
            return search(doc, elem, x);
          };
          return children$3(elem).length === 0 ? fallback2() : searchTextNodes(doc, elem, x, y).orThunk(fallback2);
        });
      };
      var availableSearch = function() {
        if (document.caretPositionFromPoint) {
          return caretPositionFromPoint;
        } else if (document.caretRangeFromPoint) {
          return caretRangeFromPoint;
        } else {
          return searchFromPoint;
        }
      }();
      var fromPoint = function(win, x, y) {
        var doc = SugarElement.fromDom(win.document);
        return availableSearch(doc, x, y).map(function(rng) {
          return SimRange.create(SugarElement.fromDom(rng.startContainer), rng.startOffset, SugarElement.fromDom(rng.endContainer), rng.endOffset);
        });
      };
      var beforeSpecial = function(element, offset) {
        var name$1 = name(element);
        if (name$1 === "input") {
          return Situ.after(element);
        } else if (!contains$2([
          "br",
          "img"
        ], name$1)) {
          return Situ.on(element, offset);
        } else {
          return offset === 0 ? Situ.before(element) : Situ.after(element);
        }
      };
      var preprocessRelative = function(startSitu, finishSitu) {
        var start = startSitu.fold(Situ.before, beforeSpecial, Situ.after);
        var finish = finishSitu.fold(Situ.before, beforeSpecial, Situ.after);
        return SimSelection.relative(start, finish);
      };
      var preprocessExact = function(start, soffset, finish, foffset) {
        var startSitu = beforeSpecial(start, soffset);
        var finishSitu = beforeSpecial(finish, foffset);
        return SimSelection.relative(startSitu, finishSitu);
      };
      var preprocess = function(selection2) {
        return selection2.match({
          domRange: function(rng) {
            var start = SugarElement.fromDom(rng.startContainer);
            var finish = SugarElement.fromDom(rng.endContainer);
            return preprocessExact(start, rng.startOffset, finish, rng.endOffset);
          },
          relative: preprocessRelative,
          exact: preprocessExact
        });
      };
      var makeRange = function(start, soffset, finish, foffset) {
        var doc = owner(start);
        var rng = doc.dom.createRange();
        rng.setStart(start.dom, soffset);
        rng.setEnd(finish.dom, foffset);
        return rng;
      };
      var after$2 = function(start, soffset, finish, foffset) {
        var r2 = makeRange(start, soffset, finish, foffset);
        var same = eq$1(start, finish) && soffset === foffset;
        return r2.collapsed && !same;
      };
      var getNativeSelection = function(win) {
        return Optional.from(win.getSelection());
      };
      var doSetNativeRange = function(win, rng) {
        getNativeSelection(win).each(function(selection2) {
          selection2.removeAllRanges();
          selection2.addRange(rng);
        });
      };
      var doSetRange = function(win, start, soffset, finish, foffset) {
        var rng = exactToNative(win, start, soffset, finish, foffset);
        doSetNativeRange(win, rng);
      };
      var setLegacyRtlRange = function(win, selection2, start, soffset, finish, foffset) {
        selection2.collapse(start.dom, soffset);
        selection2.extend(finish.dom, foffset);
      };
      var setRangeFromRelative = function(win, relative2) {
        return diagnose(win, relative2).match({
          ltr: function(start, soffset, finish, foffset) {
            doSetRange(win, start, soffset, finish, foffset);
          },
          rtl: function(start, soffset, finish, foffset) {
            getNativeSelection(win).each(function(selection2) {
              if (selection2.setBaseAndExtent) {
                selection2.setBaseAndExtent(start.dom, soffset, finish.dom, foffset);
              } else if (selection2.extend) {
                try {
                  setLegacyRtlRange(win, selection2, start, soffset, finish, foffset);
                } catch (e) {
                  doSetRange(win, finish, foffset, start, soffset);
                }
              } else {
                doSetRange(win, finish, foffset, start, soffset);
              }
            });
          }
        });
      };
      var setExact = function(win, start, soffset, finish, foffset) {
        var relative2 = preprocessExact(start, soffset, finish, foffset);
        setRangeFromRelative(win, relative2);
      };
      var setRelative = function(win, startSitu, finishSitu) {
        var relative2 = preprocessRelative(startSitu, finishSitu);
        setRangeFromRelative(win, relative2);
      };
      var toNative = function(selection2) {
        var win = SimSelection.getWin(selection2).dom;
        var getDomRange = function(start, soffset, finish, foffset) {
          return exactToNative(win, start, soffset, finish, foffset);
        };
        var filtered = preprocess(selection2);
        return diagnose(win, filtered).match({
          ltr: getDomRange,
          rtl: getDomRange
        });
      };
      var readRange = function(selection2) {
        if (selection2.rangeCount > 0) {
          var firstRng = selection2.getRangeAt(0);
          var lastRng = selection2.getRangeAt(selection2.rangeCount - 1);
          return Optional.some(SimRange.create(SugarElement.fromDom(firstRng.startContainer), firstRng.startOffset, SugarElement.fromDom(lastRng.endContainer), lastRng.endOffset));
        } else {
          return Optional.none();
        }
      };
      var doGetExact = function(selection2) {
        if (selection2.anchorNode === null || selection2.focusNode === null) {
          return readRange(selection2);
        } else {
          var anchor = SugarElement.fromDom(selection2.anchorNode);
          var focus_1 = SugarElement.fromDom(selection2.focusNode);
          return after$2(anchor, selection2.anchorOffset, focus_1, selection2.focusOffset) ? Optional.some(SimRange.create(anchor, selection2.anchorOffset, focus_1, selection2.focusOffset)) : readRange(selection2);
        }
      };
      var setToElement = function(win, element, selectNodeContents$1) {
        if (selectNodeContents$1 === void 0) {
          selectNodeContents$1 = true;
        }
        var rngGetter = selectNodeContents$1 ? selectNodeContents : selectNode;
        var rng = rngGetter(win, element);
        doSetNativeRange(win, rng);
      };
      var getExact = function(win) {
        return getNativeSelection(win).filter(function(sel) {
          return sel.rangeCount > 0;
        }).bind(doGetExact);
      };
      var get$1 = function(win) {
        return getExact(win).map(function(range2) {
          return SimSelection.exact(range2.start, range2.soffset, range2.finish, range2.foffset);
        });
      };
      var getFirstRect = function(win, selection2) {
        var rng = asLtrRange(win, selection2);
        return getFirstRect$1(rng);
      };
      var getAtPoint = function(win, x, y) {
        return fromPoint(win, x, y);
      };
      var clear = function(win) {
        getNativeSelection(win).each(function(selection2) {
          return selection2.removeAllRanges();
        });
      };
      var global$1 = tinymce.util.Tools.resolve("tinymce.util.VK");
      var forward = function(editor, isRoot, cell2) {
        return go$1(editor, isRoot, next(cell2, isEditable$1));
      };
      var backward = function(editor, isRoot, cell2) {
        return go$1(editor, isRoot, prev(cell2, isEditable$1));
      };
      var getCellFirstCursorPosition = function(editor, cell2) {
        var selection2 = SimSelection.exact(cell2, 0, cell2, 0);
        return toNative(selection2);
      };
      var go$1 = function(editor, isRoot, cell2) {
        return cell2.fold(Optional.none, Optional.none, function(current, next2) {
          return first(next2).map(function(cell3) {
            return getCellFirstCursorPosition(editor, cell3);
          });
        }, function(current) {
          editor.execCommand("mceTableInsertRowAfter");
          return forward(editor, isRoot, current);
        });
      };
      var rootElements = [
        "table",
        "li",
        "dl"
      ];
      var handle$1 = function(event, editor, cellSelection) {
        if (event.keyCode === global$1.TAB) {
          var body_1 = getBody(editor);
          var isRoot_1 = function(element) {
            var name$1 = name(element);
            return eq$1(element, body_1) || contains$2(rootElements, name$1);
          };
          var rng = editor.selection.getRng();
          var container = SugarElement.fromDom(event.shiftKey ? rng.startContainer : rng.endContainer);
          cell(container, isRoot_1).each(function(cell2) {
            event.preventDefault();
            table(cell2, isRoot_1).each(cellSelection.clear);
            editor.selection.collapse(event.shiftKey);
            var navigation = event.shiftKey ? backward : forward;
            var rng2 = navigation(editor, isRoot_1, cell2);
            rng2.each(function(range2) {
              editor.selection.setRng(range2);
            });
          });
        }
      };
      var create$1 = function(selection2, kill) {
        return {
          selection: selection2,
          kill
        };
      };
      var Response = { create: create$1 };
      var create = function(start, soffset, finish, foffset) {
        return {
          start: Situ.on(start, soffset),
          finish: Situ.on(finish, foffset)
        };
      };
      var Situs = { create };
      var convertToRange = function(win, selection2) {
        var rng = asLtrRange(win, selection2);
        return SimRange.create(SugarElement.fromDom(rng.startContainer), rng.startOffset, SugarElement.fromDom(rng.endContainer), rng.endOffset);
      };
      var makeSitus = Situs.create;
      var sync = function(container, isRoot, start, soffset, finish, foffset, selectRange) {
        if (!(eq$1(start, finish) && soffset === foffset)) {
          return closest$1(start, "td,th", isRoot).bind(function(s) {
            return closest$1(finish, "td,th", isRoot).bind(function(f) {
              return detect(container, isRoot, s, f, selectRange);
            });
          });
        } else {
          return Optional.none();
        }
      };
      var detect = function(container, isRoot, start, finish, selectRange) {
        if (!eq$1(start, finish)) {
          return identify(start, finish, isRoot).bind(function(cellSel) {
            var boxes = cellSel.boxes.getOr([]);
            if (boxes.length > 1) {
              selectRange(container, boxes, cellSel.start, cellSel.finish);
              return Optional.some(Response.create(Optional.some(makeSitus(start, 0, start, getEnd(start))), true));
            } else {
              return Optional.none();
            }
          });
        } else {
          return Optional.none();
        }
      };
      var update = function(rows2, columns2, container, selected, annotations) {
        var updateSelection = function(newSels) {
          annotations.clearBeforeUpdate(container);
          annotations.selectRange(container, newSels.boxes, newSels.start, newSels.finish);
          return newSels.boxes;
        };
        return shiftSelection(selected, rows2, columns2, annotations.firstSelectedSelector, annotations.lastSelectedSelector).map(updateSelection);
      };
      var traverse = function(item, mode) {
        return {
          item,
          mode
        };
      };
      var backtrack = function(universe2, item, _direction, transition) {
        if (transition === void 0) {
          transition = sidestep;
        }
        return universe2.property().parent(item).map(function(p) {
          return traverse(p, transition);
        });
      };
      var sidestep = function(universe2, item, direction, transition) {
        if (transition === void 0) {
          transition = advance;
        }
        return direction.sibling(universe2, item).map(function(p) {
          return traverse(p, transition);
        });
      };
      var advance = function(universe2, item, direction, transition) {
        if (transition === void 0) {
          transition = advance;
        }
        var children2 = universe2.property().children(item);
        var result = direction.first(children2);
        return result.map(function(r2) {
          return traverse(r2, transition);
        });
      };
      var successors = [
        {
          current: backtrack,
          next: sidestep,
          fallback: Optional.none()
        },
        {
          current: sidestep,
          next: advance,
          fallback: Optional.some(backtrack)
        },
        {
          current: advance,
          next: advance,
          fallback: Optional.some(sidestep)
        }
      ];
      var go = function(universe2, item, mode, direction, rules) {
        if (rules === void 0) {
          rules = successors;
        }
        var ruleOpt = find$1(rules, function(succ) {
          return succ.current === mode;
        });
        return ruleOpt.bind(function(rule) {
          return rule.current(universe2, item, direction, rule.next).orThunk(function() {
            return rule.fallback.bind(function(fb) {
              return go(universe2, item, fb, direction);
            });
          });
        });
      };
      var left$1 = function() {
        var sibling = function(universe2, item) {
          return universe2.query().prevSibling(item);
        };
        var first2 = function(children2) {
          return children2.length > 0 ? Optional.some(children2[children2.length - 1]) : Optional.none();
        };
        return {
          sibling,
          first: first2
        };
      };
      var right$1 = function() {
        var sibling = function(universe2, item) {
          return universe2.query().nextSibling(item);
        };
        var first2 = function(children2) {
          return children2.length > 0 ? Optional.some(children2[0]) : Optional.none();
        };
        return {
          sibling,
          first: first2
        };
      };
      var Walkers = {
        left: left$1,
        right: right$1
      };
      var hone = function(universe2, item, predicate, mode, direction, isRoot) {
        var next2 = go(universe2, item, mode, direction);
        return next2.bind(function(n) {
          if (isRoot(n.item)) {
            return Optional.none();
          } else {
            return predicate(n.item) ? Optional.some(n.item) : hone(universe2, n.item, predicate, n.mode, direction, isRoot);
          }
        });
      };
      var left = function(universe2, item, predicate, isRoot) {
        return hone(universe2, item, predicate, sidestep, Walkers.left(), isRoot);
      };
      var right = function(universe2, item, predicate, isRoot) {
        return hone(universe2, item, predicate, sidestep, Walkers.right(), isRoot);
      };
      var isLeaf = function(universe2) {
        return function(element) {
          return universe2.property().children(element).length === 0;
        };
      };
      var before$1 = function(universe2, item, isRoot) {
        return seekLeft$1(universe2, item, isLeaf(universe2), isRoot);
      };
      var after$1 = function(universe2, item, isRoot) {
        return seekRight$1(universe2, item, isLeaf(universe2), isRoot);
      };
      var seekLeft$1 = left;
      var seekRight$1 = right;
      var universe = DomUniverse();
      var before = function(element, isRoot) {
        return before$1(universe, element, isRoot);
      };
      var after = function(element, isRoot) {
        return after$1(universe, element, isRoot);
      };
      var seekLeft = function(element, predicate, isRoot) {
        return seekLeft$1(universe, element, predicate, isRoot);
      };
      var seekRight = function(element, predicate, isRoot) {
        return seekRight$1(universe, element, predicate, isRoot);
      };
      var ancestor = function(scope, predicate, isRoot) {
        return ancestor$2(scope, predicate, isRoot).isSome();
      };
      var adt$1 = Adt.generate([
        { none: ["message"] },
        { success: [] },
        { failedUp: ["cell"] },
        { failedDown: ["cell"] }
      ]);
      var isOverlapping = function(bridge, before2, after2) {
        var beforeBounds = bridge.getRect(before2);
        var afterBounds = bridge.getRect(after2);
        return afterBounds.right > beforeBounds.left && afterBounds.left < beforeBounds.right;
      };
      var isRow = function(elem) {
        return closest$1(elem, "tr");
      };
      var verify = function(bridge, before2, beforeOffset, after2, afterOffset, failure, isRoot) {
        return closest$1(after2, "td,th", isRoot).bind(function(afterCell) {
          return closest$1(before2, "td,th", isRoot).map(function(beforeCell) {
            if (!eq$1(afterCell, beforeCell)) {
              return sharedOne(isRow, [
                afterCell,
                beforeCell
              ]).fold(function() {
                return isOverlapping(bridge, beforeCell, afterCell) ? adt$1.success() : failure(beforeCell);
              }, function(_sharedRow) {
                return failure(beforeCell);
              });
            } else {
              return eq$1(after2, afterCell) && getEnd(afterCell) === afterOffset ? failure(beforeCell) : adt$1.none("in same cell");
            }
          });
        }).getOr(adt$1.none("default"));
      };
      var cata = function(subject, onNone, onSuccess, onFailedUp, onFailedDown) {
        return subject.fold(onNone, onSuccess, onFailedUp, onFailedDown);
      };
      var BeforeAfter = __assign(__assign({}, adt$1), {
        verify,
        cata
      });
      var inParent = function(parent2, children2, element, index) {
        return {
          parent: parent2,
          children: children2,
          element,
          index
        };
      };
      var indexInParent = function(element) {
        return parent(element).bind(function(parent2) {
          var children2 = children$3(parent2);
          return indexOf(children2, element).map(function(index) {
            return inParent(parent2, children2, element, index);
          });
        });
      };
      var indexOf = function(elements, element) {
        return findIndex(elements, curry(eq$1, element));
      };
      var isBr = function(elem) {
        return name(elem) === "br";
      };
      var gatherer = function(cand, gather, isRoot) {
        return gather(cand, isRoot).bind(function(target) {
          return isText(target) && get$9(target).trim().length === 0 ? gatherer(target, gather, isRoot) : Optional.some(target);
        });
      };
      var handleBr = function(isRoot, element, direction) {
        return direction.traverse(element).orThunk(function() {
          return gatherer(element, direction.gather, isRoot);
        }).map(direction.relative);
      };
      var findBr = function(element, offset) {
        return child$3(element, offset).filter(isBr).orThunk(function() {
          return child$3(element, offset - 1).filter(isBr);
        });
      };
      var handleParent = function(isRoot, element, offset, direction) {
        return findBr(element, offset).bind(function(br) {
          return direction.traverse(br).fold(function() {
            return gatherer(br, direction.gather, isRoot).map(direction.relative);
          }, function(adjacent) {
            return indexInParent(adjacent).map(function(info) {
              return Situ.on(info.parent, info.index);
            });
          });
        });
      };
      var tryBr = function(isRoot, element, offset, direction) {
        var target = isBr(element) ? handleBr(isRoot, element, direction) : handleParent(isRoot, element, offset, direction);
        return target.map(function(tgt) {
          return {
            start: tgt,
            finish: tgt
          };
        });
      };
      var process = function(analysis) {
        return BeforeAfter.cata(analysis, function(_message) {
          return Optional.none();
        }, function() {
          return Optional.none();
        }, function(cell2) {
          return Optional.some(point(cell2, 0));
        }, function(cell2) {
          return Optional.some(point(cell2, getEnd(cell2)));
        });
      };
      var moveDown = function(caret, amount) {
        return {
          left: caret.left,
          top: caret.top + amount,
          right: caret.right,
          bottom: caret.bottom + amount
        };
      };
      var moveUp = function(caret, amount) {
        return {
          left: caret.left,
          top: caret.top - amount,
          right: caret.right,
          bottom: caret.bottom - amount
        };
      };
      var translate = function(caret, xDelta, yDelta) {
        return {
          left: caret.left + xDelta,
          top: caret.top + yDelta,
          right: caret.right + xDelta,
          bottom: caret.bottom + yDelta
        };
      };
      var getTop = function(caret) {
        return caret.top;
      };
      var getBottom = function(caret) {
        return caret.bottom;
      };
      var getPartialBox = function(bridge, element, offset) {
        if (offset >= 0 && offset < getEnd(element)) {
          return bridge.getRangedRect(element, offset, element, offset + 1);
        } else if (offset > 0) {
          return bridge.getRangedRect(element, offset - 1, element, offset);
        }
        return Optional.none();
      };
      var toCaret = function(rect) {
        return {
          left: rect.left,
          top: rect.top,
          right: rect.right,
          bottom: rect.bottom
        };
      };
      var getElemBox = function(bridge, element) {
        return Optional.some(bridge.getRect(element));
      };
      var getBoxAt = function(bridge, element, offset) {
        if (isElement(element)) {
          return getElemBox(bridge, element).map(toCaret);
        } else if (isText(element)) {
          return getPartialBox(bridge, element, offset).map(toCaret);
        } else {
          return Optional.none();
        }
      };
      var getEntireBox = function(bridge, element) {
        if (isElement(element)) {
          return getElemBox(bridge, element).map(toCaret);
        } else if (isText(element)) {
          return bridge.getRangedRect(element, 0, element, getEnd(element)).map(toCaret);
        } else {
          return Optional.none();
        }
      };
      var JUMP_SIZE = 5;
      var NUM_RETRIES = 100;
      var adt = Adt.generate([
        { none: [] },
        { retry: ["caret"] }
      ]);
      var isOutside = function(caret, box) {
        return caret.left < box.left || Math.abs(box.right - caret.left) < 1 || caret.left > box.right;
      };
      var inOutsideBlock = function(bridge, element, caret) {
        return closest$2(element, isBlock).fold(never, function(cell2) {
          return getEntireBox(bridge, cell2).exists(function(box) {
            return isOutside(caret, box);
          });
        });
      };
      var adjustDown = function(bridge, element, guessBox, original, caret) {
        var lowerCaret = moveDown(caret, JUMP_SIZE);
        if (Math.abs(guessBox.bottom - original.bottom) < 1) {
          return adt.retry(lowerCaret);
        } else if (guessBox.top > caret.bottom) {
          return adt.retry(lowerCaret);
        } else if (guessBox.top === caret.bottom) {
          return adt.retry(moveDown(caret, 1));
        } else {
          return inOutsideBlock(bridge, element, caret) ? adt.retry(translate(lowerCaret, JUMP_SIZE, 0)) : adt.none();
        }
      };
      var adjustUp = function(bridge, element, guessBox, original, caret) {
        var higherCaret = moveUp(caret, JUMP_SIZE);
        if (Math.abs(guessBox.top - original.top) < 1) {
          return adt.retry(higherCaret);
        } else if (guessBox.bottom < caret.top) {
          return adt.retry(higherCaret);
        } else if (guessBox.bottom === caret.top) {
          return adt.retry(moveUp(caret, 1));
        } else {
          return inOutsideBlock(bridge, element, caret) ? adt.retry(translate(higherCaret, JUMP_SIZE, 0)) : adt.none();
        }
      };
      var upMovement = {
        point: getTop,
        adjuster: adjustUp,
        move: moveUp,
        gather: before
      };
      var downMovement = {
        point: getBottom,
        adjuster: adjustDown,
        move: moveDown,
        gather: after
      };
      var isAtTable = function(bridge, x, y) {
        return bridge.elementFromPoint(x, y).filter(function(elm) {
          return name(elm) === "table";
        }).isSome();
      };
      var adjustForTable = function(bridge, movement, original, caret, numRetries) {
        return adjustTil(bridge, movement, original, movement.move(caret, JUMP_SIZE), numRetries);
      };
      var adjustTil = function(bridge, movement, original, caret, numRetries) {
        if (numRetries === 0) {
          return Optional.some(caret);
        }
        if (isAtTable(bridge, caret.left, movement.point(caret))) {
          return adjustForTable(bridge, movement, original, caret, numRetries - 1);
        }
        return bridge.situsFromPoint(caret.left, movement.point(caret)).bind(function(guess) {
          return guess.start.fold(Optional.none, function(element) {
            return getEntireBox(bridge, element).bind(function(guessBox) {
              return movement.adjuster(bridge, element, guessBox, original, caret).fold(Optional.none, function(newCaret) {
                return adjustTil(bridge, movement, original, newCaret, numRetries - 1);
              });
            }).orThunk(function() {
              return Optional.some(caret);
            });
          }, Optional.none);
        });
      };
      var ieTryDown = function(bridge, caret) {
        return bridge.situsFromPoint(caret.left, caret.bottom + JUMP_SIZE);
      };
      var ieTryUp = function(bridge, caret) {
        return bridge.situsFromPoint(caret.left, caret.top - JUMP_SIZE);
      };
      var checkScroll = function(movement, adjusted, bridge) {
        if (movement.point(adjusted) > bridge.getInnerHeight()) {
          return Optional.some(movement.point(adjusted) - bridge.getInnerHeight());
        } else if (movement.point(adjusted) < 0) {
          return Optional.some(-movement.point(adjusted));
        } else {
          return Optional.none();
        }
      };
      var retry = function(movement, bridge, caret) {
        var moved = movement.move(caret, JUMP_SIZE);
        var adjusted = adjustTil(bridge, movement, caret, moved, NUM_RETRIES).getOr(moved);
        return checkScroll(movement, adjusted, bridge).fold(function() {
          return bridge.situsFromPoint(adjusted.left, movement.point(adjusted));
        }, function(delta) {
          bridge.scrollBy(0, delta);
          return bridge.situsFromPoint(adjusted.left, movement.point(adjusted) - delta);
        });
      };
      var Retries = {
        tryUp: curry(retry, upMovement),
        tryDown: curry(retry, downMovement),
        ieTryUp,
        ieTryDown,
        getJumpSize: constant(JUMP_SIZE)
      };
      var MAX_RETRIES = 20;
      var findSpot = function(bridge, isRoot, direction) {
        return bridge.getSelection().bind(function(sel) {
          return tryBr(isRoot, sel.finish, sel.foffset, direction).fold(function() {
            return Optional.some(point(sel.finish, sel.foffset));
          }, function(brNeighbour) {
            var range2 = bridge.fromSitus(brNeighbour);
            var analysis = BeforeAfter.verify(bridge, sel.finish, sel.foffset, range2.finish, range2.foffset, direction.failure, isRoot);
            return process(analysis);
          });
        });
      };
      var scan = function(bridge, isRoot, element, offset, direction, numRetries) {
        if (numRetries === 0) {
          return Optional.none();
        }
        return tryCursor(bridge, isRoot, element, offset, direction).bind(function(situs) {
          var range2 = bridge.fromSitus(situs);
          var analysis = BeforeAfter.verify(bridge, element, offset, range2.finish, range2.foffset, direction.failure, isRoot);
          return BeforeAfter.cata(analysis, function() {
            return Optional.none();
          }, function() {
            return Optional.some(situs);
          }, function(cell2) {
            if (eq$1(element, cell2) && offset === 0) {
              return tryAgain(bridge, element, offset, moveUp, direction);
            } else {
              return scan(bridge, isRoot, cell2, 0, direction, numRetries - 1);
            }
          }, function(cell2) {
            if (eq$1(element, cell2) && offset === getEnd(cell2)) {
              return tryAgain(bridge, element, offset, moveDown, direction);
            } else {
              return scan(bridge, isRoot, cell2, getEnd(cell2), direction, numRetries - 1);
            }
          });
        });
      };
      var tryAgain = function(bridge, element, offset, move, direction) {
        return getBoxAt(bridge, element, offset).bind(function(box) {
          return tryAt(bridge, direction, move(box, Retries.getJumpSize()));
        });
      };
      var tryAt = function(bridge, direction, box) {
        var browser = detect$3().browser;
        if (browser.isChrome() || browser.isSafari() || browser.isFirefox() || browser.isEdge()) {
          return direction.otherRetry(bridge, box);
        } else if (browser.isIE()) {
          return direction.ieRetry(bridge, box);
        } else {
          return Optional.none();
        }
      };
      var tryCursor = function(bridge, isRoot, element, offset, direction) {
        return getBoxAt(bridge, element, offset).bind(function(box) {
          return tryAt(bridge, direction, box);
        });
      };
      var handle = function(bridge, isRoot, direction) {
        return findSpot(bridge, isRoot, direction).bind(function(spot) {
          return scan(bridge, isRoot, spot.element, spot.offset, direction, MAX_RETRIES).map(bridge.fromSitus);
        });
      };
      var inSameTable = function(elem, table2) {
        return ancestor(elem, function(e) {
          return parent(e).exists(function(p) {
            return eq$1(p, table2);
          });
        });
      };
      var simulate = function(bridge, isRoot, direction, initial, anchor) {
        return closest$1(initial, "td,th", isRoot).bind(function(start) {
          return closest$1(start, "table", isRoot).bind(function(table2) {
            if (!inSameTable(anchor, table2)) {
              return Optional.none();
            }
            return handle(bridge, isRoot, direction).bind(function(range2) {
              return closest$1(range2.finish, "td,th", isRoot).map(function(finish) {
                return {
                  start,
                  finish,
                  range: range2
                };
              });
            });
          });
        });
      };
      var navigate = function(bridge, isRoot, direction, initial, anchor, precheck) {
        if (detect$3().browser.isIE()) {
          return Optional.none();
        } else {
          return precheck(initial, isRoot).orThunk(function() {
            return simulate(bridge, isRoot, direction, initial, anchor).map(function(info) {
              var range2 = info.range;
              return Response.create(Optional.some(makeSitus(range2.start, range2.soffset, range2.finish, range2.foffset)), true);
            });
          });
        }
      };
      var firstUpCheck = function(initial, isRoot) {
        return closest$1(initial, "tr", isRoot).bind(function(startRow) {
          return closest$1(startRow, "table", isRoot).bind(function(table2) {
            var rows2 = descendants(table2, "tr");
            if (eq$1(startRow, rows2[0])) {
              return seekLeft(table2, function(element) {
                return last$1(element).isSome();
              }, isRoot).map(function(last2) {
                var lastOffset = getEnd(last2);
                return Response.create(Optional.some(makeSitus(last2, lastOffset, last2, lastOffset)), true);
              });
            } else {
              return Optional.none();
            }
          });
        });
      };
      var lastDownCheck = function(initial, isRoot) {
        return closest$1(initial, "tr", isRoot).bind(function(startRow) {
          return closest$1(startRow, "table", isRoot).bind(function(table2) {
            var rows2 = descendants(table2, "tr");
            if (eq$1(startRow, rows2[rows2.length - 1])) {
              return seekRight(table2, function(element) {
                return first(element).isSome();
              }, isRoot).map(function(first2) {
                return Response.create(Optional.some(makeSitus(first2, 0, first2, 0)), true);
              });
            } else {
              return Optional.none();
            }
          });
        });
      };
      var select = function(bridge, container, isRoot, direction, initial, anchor, selectRange) {
        return simulate(bridge, isRoot, direction, initial, anchor).bind(function(info) {
          return detect(container, isRoot, info.start, info.finish, selectRange);
        });
      };
      var findCell = function(target, isRoot) {
        return closest$1(target, "td,th", isRoot);
      };
      var MouseSelection = function(bridge, container, isRoot, annotations) {
        var cursor = value();
        var clearstate = cursor.clear;
        var applySelection = function(event) {
          cursor.on(function(start) {
            annotations.clearBeforeUpdate(container);
            findCell(event.target, isRoot).each(function(finish) {
              identify(start, finish, isRoot).each(function(cellSel) {
                var boxes = cellSel.boxes.getOr([]);
                if (boxes.length === 1) {
                  var singleCell = boxes[0];
                  var isNonEditableCell = getRaw(singleCell) === "false";
                  var isCellClosestContentEditable = is(closest(event.target), singleCell, eq$1);
                  if (isNonEditableCell && isCellClosestContentEditable) {
                    annotations.selectRange(container, boxes, singleCell, singleCell);
                    bridge.selectContents(singleCell);
                  }
                } else if (boxes.length > 1) {
                  annotations.selectRange(container, boxes, cellSel.start, cellSel.finish);
                  bridge.selectContents(finish);
                }
              });
            });
          });
        };
        var mousedown = function(event) {
          annotations.clear(container);
          findCell(event.target, isRoot).each(cursor.set);
        };
        var mouseover = function(event) {
          applySelection(event);
        };
        var mouseup = function(event) {
          applySelection(event);
          clearstate();
        };
        return {
          clearstate,
          mousedown,
          mouseover,
          mouseup
        };
      };
      var down = {
        traverse: nextSibling,
        gather: after,
        relative: Situ.before,
        otherRetry: Retries.tryDown,
        ieRetry: Retries.ieTryDown,
        failure: BeforeAfter.failedDown
      };
      var up = {
        traverse: prevSibling,
        gather: before,
        relative: Situ.before,
        otherRetry: Retries.tryUp,
        ieRetry: Retries.ieTryUp,
        failure: BeforeAfter.failedUp
      };
      var isKey = function(key2) {
        return function(keycode) {
          return keycode === key2;
        };
      };
      var isUp = isKey(38);
      var isDown = isKey(40);
      var isNavigation = function(keycode) {
        return keycode >= 37 && keycode <= 40;
      };
      var ltr = {
        isBackward: isKey(37),
        isForward: isKey(39)
      };
      var rtl = {
        isBackward: isKey(39),
        isForward: isKey(37)
      };
      var get = function(_DOC) {
        var doc = _DOC !== void 0 ? _DOC.dom : document;
        var x = doc.body.scrollLeft || doc.documentElement.scrollLeft;
        var y = doc.body.scrollTop || doc.documentElement.scrollTop;
        return SugarPosition(x, y);
      };
      var by = function(x, y, _DOC) {
        var doc = _DOC !== void 0 ? _DOC.dom : document;
        var win = doc.defaultView;
        if (win) {
          win.scrollBy(x, y);
        }
      };
      var WindowBridge = function(win) {
        var elementFromPoint = function(x, y) {
          return SugarElement.fromPoint(SugarElement.fromDom(win.document), x, y);
        };
        var getRect = function(element) {
          return element.dom.getBoundingClientRect();
        };
        var getRangedRect = function(start, soffset, finish, foffset) {
          var sel = SimSelection.exact(start, soffset, finish, foffset);
          return getFirstRect(win, sel);
        };
        var getSelection = function() {
          return get$1(win).map(function(exactAdt) {
            return convertToRange(win, exactAdt);
          });
        };
        var fromSitus = function(situs) {
          var relative2 = SimSelection.relative(situs.start, situs.finish);
          return convertToRange(win, relative2);
        };
        var situsFromPoint = function(x, y) {
          return getAtPoint(win, x, y).map(function(exact2) {
            return Situs.create(exact2.start, exact2.soffset, exact2.finish, exact2.foffset);
          });
        };
        var clearSelection = function() {
          clear(win);
        };
        var collapseSelection = function(toStart) {
          if (toStart === void 0) {
            toStart = false;
          }
          get$1(win).each(function(sel) {
            return sel.fold(function(rng) {
              return rng.collapse(toStart);
            }, function(startSitu, finishSitu) {
              var situ = toStart ? startSitu : finishSitu;
              setRelative(win, situ, situ);
            }, function(start, soffset, finish, foffset) {
              var node = toStart ? start : finish;
              var offset = toStart ? soffset : foffset;
              setExact(win, node, offset, node, offset);
            });
          });
        };
        var selectNode2 = function(element) {
          setToElement(win, element, false);
        };
        var selectContents = function(element) {
          setToElement(win, element);
        };
        var setSelection = function(sel) {
          setExact(win, sel.start, sel.soffset, sel.finish, sel.foffset);
        };
        var setRelativeSelection = function(start, finish) {
          setRelative(win, start, finish);
        };
        var getInnerHeight = function() {
          return win.innerHeight;
        };
        var getScrollY = function() {
          var pos = get(SugarElement.fromDom(win.document));
          return pos.top;
        };
        var scrollBy = function(x, y) {
          by(x, y, SugarElement.fromDom(win.document));
        };
        return {
          elementFromPoint,
          getRect,
          getRangedRect,
          getSelection,
          fromSitus,
          situsFromPoint,
          clearSelection,
          collapseSelection,
          setSelection,
          setRelativeSelection,
          selectNode: selectNode2,
          selectContents,
          getInnerHeight,
          getScrollY,
          scrollBy
        };
      };
      var rc = function(rows2, cols) {
        return {
          rows: rows2,
          cols
        };
      };
      var mouse = function(win, container, isRoot, annotations) {
        var bridge = WindowBridge(win);
        var handlers = MouseSelection(bridge, container, isRoot, annotations);
        return {
          clearstate: handlers.clearstate,
          mousedown: handlers.mousedown,
          mouseover: handlers.mouseover,
          mouseup: handlers.mouseup
        };
      };
      var keyboard = function(win, container, isRoot, annotations) {
        var bridge = WindowBridge(win);
        var clearToNavigate = function() {
          annotations.clear(container);
          return Optional.none();
        };
        var keydown = function(event, start, soffset, finish, foffset, direction) {
          var realEvent = event.raw;
          var keycode = realEvent.which;
          var shiftKey = realEvent.shiftKey === true;
          var handler = retrieve$1(container, annotations.selectedSelector).fold(function() {
            if (isNavigation(keycode) && !shiftKey) {
              annotations.clearBeforeUpdate(container);
            }
            if (isDown(keycode) && shiftKey) {
              return curry(select, bridge, container, isRoot, down, finish, start, annotations.selectRange);
            } else if (isUp(keycode) && shiftKey) {
              return curry(select, bridge, container, isRoot, up, finish, start, annotations.selectRange);
            } else if (isDown(keycode)) {
              return curry(navigate, bridge, isRoot, down, finish, start, lastDownCheck);
            } else if (isUp(keycode)) {
              return curry(navigate, bridge, isRoot, up, finish, start, firstUpCheck);
            } else {
              return Optional.none;
            }
          }, function(selected) {
            var update$1 = function(attempts) {
              return function() {
                var navigation = findMap(attempts, function(delta) {
                  return update(delta.rows, delta.cols, container, selected, annotations);
                });
                return navigation.fold(function() {
                  return getEdges(container, annotations.firstSelectedSelector, annotations.lastSelectedSelector).map(function(edges) {
                    var relative2 = isDown(keycode) || direction.isForward(keycode) ? Situ.after : Situ.before;
                    bridge.setRelativeSelection(Situ.on(edges.first, 0), relative2(edges.table));
                    annotations.clear(container);
                    return Response.create(Optional.none(), true);
                  });
                }, function(_) {
                  return Optional.some(Response.create(Optional.none(), true));
                });
              };
            };
            if (isDown(keycode) && shiftKey) {
              return update$1([rc(1, 0)]);
            } else if (isUp(keycode) && shiftKey) {
              return update$1([rc(-1, 0)]);
            } else if (direction.isBackward(keycode) && shiftKey) {
              return update$1([
                rc(0, -1),
                rc(-1, 0)
              ]);
            } else if (direction.isForward(keycode) && shiftKey) {
              return update$1([
                rc(0, 1),
                rc(1, 0)
              ]);
            } else if (isNavigation(keycode) && !shiftKey) {
              return clearToNavigate;
            } else {
              return Optional.none;
            }
          });
          return handler();
        };
        var keyup = function(event, start, soffset, finish, foffset) {
          return retrieve$1(container, annotations.selectedSelector).fold(function() {
            var realEvent = event.raw;
            var keycode = realEvent.which;
            var shiftKey = realEvent.shiftKey === true;
            if (!shiftKey) {
              return Optional.none();
            }
            if (isNavigation(keycode)) {
              return sync(container, isRoot, start, soffset, finish, foffset, annotations.selectRange);
            } else {
              return Optional.none();
            }
          }, Optional.none);
        };
        return {
          keydown,
          keyup
        };
      };
      var external = function(win, container, isRoot, annotations) {
        var bridge = WindowBridge(win);
        return function(start, finish) {
          annotations.clearBeforeUpdate(container);
          identify(start, finish, isRoot).each(function(cellSel) {
            var boxes = cellSel.boxes.getOr([]);
            annotations.selectRange(container, boxes, cellSel.start, cellSel.finish);
            bridge.selectContents(finish);
            bridge.collapseSelection();
          });
        };
      };
      var remove = function(element, classes) {
        each$2(classes, function(x) {
          remove$2(element, x);
        });
      };
      var addClass = function(clazz) {
        return function(element) {
          add(element, clazz);
        };
      };
      var removeClasses = function(classes) {
        return function(element) {
          remove(element, classes);
        };
      };
      var byClass = function(ephemera2) {
        var addSelectionClass = addClass(ephemera2.selected);
        var removeSelectionClasses = removeClasses([
          ephemera2.selected,
          ephemera2.lastSelected,
          ephemera2.firstSelected
        ]);
        var clear2 = function(container) {
          var sels = descendants(container, ephemera2.selectedSelector);
          each$2(sels, removeSelectionClasses);
        };
        var selectRange = function(container, cells2, start, finish) {
          clear2(container);
          each$2(cells2, addSelectionClass);
          add(start, ephemera2.firstSelected);
          add(finish, ephemera2.lastSelected);
        };
        return {
          clearBeforeUpdate: clear2,
          clear: clear2,
          selectRange,
          selectedSelector: ephemera2.selectedSelector,
          firstSelectedSelector: ephemera2.firstSelectedSelector,
          lastSelectedSelector: ephemera2.lastSelectedSelector
        };
      };
      var byAttr = function(ephemera2, onSelection, onClear) {
        var removeSelectionAttributes = function(element) {
          remove$7(element, ephemera2.selected);
          remove$7(element, ephemera2.firstSelected);
          remove$7(element, ephemera2.lastSelected);
        };
        var addSelectionAttribute = function(element) {
          set$2(element, ephemera2.selected, "1");
        };
        var clear2 = function(container) {
          clearBeforeUpdate(container);
          onClear();
        };
        var clearBeforeUpdate = function(container) {
          var sels = descendants(container, ephemera2.selectedSelector + "," + ephemera2.firstSelectedSelector + "," + ephemera2.lastSelectedSelector);
          each$2(sels, removeSelectionAttributes);
        };
        var selectRange = function(container, cells2, start, finish) {
          clear2(container);
          each$2(cells2, addSelectionAttribute);
          set$2(start, ephemera2.firstSelected, "1");
          set$2(finish, ephemera2.lastSelected, "1");
          onSelection(cells2, start, finish);
        };
        return {
          clearBeforeUpdate,
          clear: clear2,
          selectRange,
          selectedSelector: ephemera2.selectedSelector,
          firstSelectedSelector: ephemera2.firstSelectedSelector,
          lastSelectedSelector: ephemera2.lastSelectedSelector
        };
      };
      var SelectionAnnotation = {
        byClass,
        byAttr
      };
      var getUpOrLeftCells = function(grid2, selectedCells) {
        var upGrid = grid2.slice(0, selectedCells[selectedCells.length - 1].row + 1);
        var upDetails = toDetailList(upGrid);
        return bind$2(upDetails, function(detail2) {
          var slicedCells = detail2.cells.slice(0, selectedCells[selectedCells.length - 1].column + 1);
          return map$1(slicedCells, function(cell2) {
            return cell2.element;
          });
        });
      };
      var getDownOrRightCells = function(grid2, selectedCells) {
        var downGrid = grid2.slice(selectedCells[0].row + selectedCells[0].rowspan - 1, grid2.length);
        var downDetails = toDetailList(downGrid);
        return bind$2(downDetails, function(detail2) {
          var slicedCells = detail2.cells.slice(selectedCells[0].column + selectedCells[0].colspan - 1, detail2.cells.length);
          return map$1(slicedCells, function(cell2) {
            return cell2.element;
          });
        });
      };
      var getOtherCells = function(table2, target, generators) {
        var warehouse = Warehouse.fromTable(table2);
        var details = onCells(warehouse, target);
        return details.map(function(selectedCells) {
          var grid2 = toGrid(warehouse, generators, false);
          var upOrLeftCells = getUpOrLeftCells(grid2, selectedCells);
          var downOrRightCells = getDownOrRightCells(grid2, selectedCells);
          return {
            upOrLeftCells,
            downOrRightCells
          };
        });
      };
      var global = tinymce.util.Tools.resolve("tinymce.Env");
      var hasInternalTarget = function(e) {
        return has(SugarElement.fromDom(e.target), "ephox-snooker-resizer-bar") === false;
      };
      function CellSelection(editor, lazyResize, selectionTargets) {
        var onSelection = function(cells2, start, finish) {
          selectionTargets.targets().each(function(targets) {
            var tableOpt = table(start);
            tableOpt.each(function(table2) {
              var cloneFormats2 = getCloneElements(editor);
              var generators = cellOperations(noop, SugarElement.fromDom(editor.getDoc()), cloneFormats2);
              var otherCells = getOtherCells(table2, targets, generators);
              fireTableSelectionChange(editor, cells2, start, finish, otherCells);
            });
          });
        };
        var onClear = function() {
          return fireTableSelectionClear(editor);
        };
        var annotations = SelectionAnnotation.byAttr(ephemera, onSelection, onClear);
        editor.on("init", function(_e) {
          var win = editor.getWin();
          var body2 = getBody(editor);
          var isRoot = getIsRoot(editor);
          var syncSelection = function() {
            var sel = editor.selection;
            var start = SugarElement.fromDom(sel.getStart());
            var end = SugarElement.fromDom(sel.getEnd());
            var shared = sharedOne(table, [
              start,
              end
            ]);
            shared.fold(function() {
              return annotations.clear(body2);
            }, noop);
          };
          var mouseHandlers = mouse(win, body2, isRoot, annotations);
          var keyHandlers = keyboard(win, body2, isRoot, annotations);
          var external$1 = external(win, body2, isRoot, annotations);
          var hasShiftKey = function(event) {
            return event.raw.shiftKey === true;
          };
          editor.on("TableSelectorChange", function(e) {
            return external$1(e.start, e.finish);
          });
          var handleResponse = function(event, response) {
            if (!hasShiftKey(event)) {
              return;
            }
            if (response.kill) {
              event.kill();
            }
            response.selection.each(function(ns) {
              var relative2 = SimSelection.relative(ns.start, ns.finish);
              var rng = asLtrRange(win, relative2);
              editor.selection.setRng(rng);
            });
          };
          var keyup = function(event) {
            var wrappedEvent = fromRawEvent(event);
            if (wrappedEvent.raw.shiftKey && isNavigation(wrappedEvent.raw.which)) {
              var rng = editor.selection.getRng();
              var start = SugarElement.fromDom(rng.startContainer);
              var end = SugarElement.fromDom(rng.endContainer);
              keyHandlers.keyup(wrappedEvent, start, rng.startOffset, end, rng.endOffset).each(function(response) {
                handleResponse(wrappedEvent, response);
              });
            }
          };
          var keydown = function(event) {
            var wrappedEvent = fromRawEvent(event);
            lazyResize().each(function(resize2) {
              return resize2.hideBars();
            });
            var rng = editor.selection.getRng();
            var start = SugarElement.fromDom(rng.startContainer);
            var end = SugarElement.fromDom(rng.endContainer);
            var direction = onDirection(ltr, rtl)(SugarElement.fromDom(editor.selection.getStart()));
            keyHandlers.keydown(wrappedEvent, start, rng.startOffset, end, rng.endOffset, direction).each(function(response) {
              handleResponse(wrappedEvent, response);
            });
            lazyResize().each(function(resize2) {
              return resize2.showBars();
            });
          };
          var isLeftMouse = function(raw) {
            return raw.button === 0;
          };
          var isLeftButtonPressed = function(raw) {
            if (raw.buttons === void 0) {
              return true;
            }
            if (global.browser.isEdge() && raw.buttons === 0) {
              return true;
            }
            return (raw.buttons & 1) !== 0;
          };
          var dragStart = function(_e2) {
            mouseHandlers.clearstate();
          };
          var mouseDown = function(e) {
            if (isLeftMouse(e) && hasInternalTarget(e)) {
              mouseHandlers.mousedown(fromRawEvent(e));
            }
          };
          var mouseOver = function(e) {
            if (isLeftButtonPressed(e) && hasInternalTarget(e)) {
              mouseHandlers.mouseover(fromRawEvent(e));
            }
          };
          var mouseUp = function(e) {
            if (isLeftMouse(e) && hasInternalTarget(e)) {
              mouseHandlers.mouseup(fromRawEvent(e));
            }
          };
          var getDoubleTap = function() {
            var lastTarget = Cell(SugarElement.fromDom(body2));
            var lastTimeStamp = Cell(0);
            var touchEnd = function(t) {
              var target = SugarElement.fromDom(t.target);
              if (name(target) === "td" || name(target) === "th") {
                var lT = lastTarget.get();
                var lTS = lastTimeStamp.get();
                if (eq$1(lT, target) && t.timeStamp - lTS < 300) {
                  t.preventDefault();
                  external$1(target, target);
                }
              }
              lastTarget.set(target);
              lastTimeStamp.set(t.timeStamp);
            };
            return { touchEnd };
          };
          var doubleTap = getDoubleTap();
          editor.on("dragstart", dragStart);
          editor.on("mousedown", mouseDown);
          editor.on("mouseover", mouseOver);
          editor.on("mouseup", mouseUp);
          editor.on("touchend", doubleTap.touchEnd);
          editor.on("keyup", keyup);
          editor.on("keydown", keydown);
          editor.on("NodeChange", syncSelection);
        });
        return { clear: annotations.clear };
      }
      var child = function(scope, selector) {
        return child$1(scope, selector).isSome();
      };
      var getSelectionTargets = function(editor, selections) {
        var targets = Cell(Optional.none());
        var changeHandlers = Cell([]);
        var selectionDetails = Optional.none();
        var isCaption = isTag("caption");
        var isDisabledForSelection = function(key2) {
          return selectionDetails.forall(function(details) {
            return !details[key2];
          });
        };
        var getStart2 = function() {
          return getSelectionCellOrCaption(getSelectionStart(editor), getIsRoot(editor));
        };
        var getEnd2 = function() {
          return getSelectionCellOrCaption(getSelectionEnd(editor), getIsRoot(editor));
        };
        var findTargets = function() {
          return getStart2().bind(function(startCellOrCaption) {
            return flatten(lift2(table(startCellOrCaption), getEnd2().bind(table), function(startTable, endTable) {
              if (eq$1(startTable, endTable)) {
                if (isCaption(startCellOrCaption)) {
                  return Optional.some(noMenu(startCellOrCaption));
                } else {
                  return Optional.some(forMenu(selections, startTable, startCellOrCaption));
                }
              }
              return Optional.none();
            }));
          });
        };
        var getExtractedDetails = function(targets2) {
          var tableOpt = table(targets2.element);
          return tableOpt.map(function(table2) {
            var warehouse = Warehouse.fromTable(table2);
            var selectedCells = onCells(warehouse, targets2).getOr([]);
            var locked = foldl(selectedCells, function(acc, cell2) {
              if (cell2.isLocked) {
                acc.onAny = true;
                if (cell2.column === 0) {
                  acc.onFirst = true;
                } else if (cell2.column + cell2.colspan >= warehouse.grid.columns) {
                  acc.onLast = true;
                }
              }
              return acc;
            }, {
              onAny: false,
              onFirst: false,
              onLast: false
            });
            return {
              mergeable: onUnlockedMergable(warehouse, targets2).isSome(),
              unmergeable: onUnlockedUnmergable(warehouse, targets2).isSome(),
              locked
            };
          });
        };
        var resetTargets = function() {
          targets.set(cached(findTargets)());
          selectionDetails = targets.get().bind(getExtractedDetails);
          each$2(changeHandlers.get(), function(handler) {
            return handler();
          });
        };
        var setupHandler = function(handler) {
          handler();
          changeHandlers.set(changeHandlers.get().concat([handler]));
          return function() {
            changeHandlers.set(filter$2(changeHandlers.get(), function(h) {
              return h !== handler;
            }));
          };
        };
        var onSetup = function(api2, isDisabled) {
          return setupHandler(function() {
            return targets.get().fold(function() {
              api2.setDisabled(true);
            }, function(targets2) {
              api2.setDisabled(isDisabled(targets2));
            });
          });
        };
        var onSetupWithToggle = function(api2, isDisabled, isActive) {
          return setupHandler(function() {
            return targets.get().fold(function() {
              api2.setDisabled(true);
              api2.setActive(false);
            }, function(targets2) {
              api2.setDisabled(isDisabled(targets2));
              api2.setActive(isActive(targets2));
            });
          });
        };
        var isDisabledFromLocked = function(lockedDisable) {
          return selectionDetails.exists(function(details) {
            return details.locked[lockedDisable];
          });
        };
        var onSetupTable = function(api2) {
          return onSetup(api2, function(_) {
            return false;
          });
        };
        var onSetupCellOrRow = function(api2) {
          return onSetup(api2, function(targets2) {
            return isCaption(targets2.element);
          });
        };
        var onSetupColumn = function(lockedDisable) {
          return function(api2) {
            return onSetup(api2, function(targets2) {
              return isCaption(targets2.element) || isDisabledFromLocked(lockedDisable);
            });
          };
        };
        var onSetupPasteable = function(getClipboardData) {
          return function(api2) {
            return onSetup(api2, function(targets2) {
              return isCaption(targets2.element) || getClipboardData().isNone();
            });
          };
        };
        var onSetupPasteableColumn = function(getClipboardData, lockedDisable) {
          return function(api2) {
            return onSetup(api2, function(targets2) {
              return isCaption(targets2.element) || getClipboardData().isNone() || isDisabledFromLocked(lockedDisable);
            });
          };
        };
        var onSetupMergeable = function(api2) {
          return onSetup(api2, function(_targets) {
            return isDisabledForSelection("mergeable");
          });
        };
        var onSetupUnmergeable = function(api2) {
          return onSetup(api2, function(_targets) {
            return isDisabledForSelection("unmergeable");
          });
        };
        var onSetupTableWithCaption = function(api2) {
          return onSetupWithToggle(api2, never, function(targets2) {
            var tableOpt = table(targets2.element, getIsRoot(editor));
            return tableOpt.exists(function(table2) {
              return child(table2, "caption");
            });
          });
        };
        var onSetupTableHeaders = function(command, headerType) {
          return function(api2) {
            return onSetupWithToggle(api2, function(targets2) {
              return isCaption(targets2.element);
            }, function() {
              return editor.queryCommandValue(command) === headerType;
            });
          };
        };
        var onSetupTableRowHeaders = onSetupTableHeaders("mceTableRowType", "header");
        var onSetupTableColumnHeaders = onSetupTableHeaders("mceTableColType", "th");
        editor.on("NodeChange ExecCommand TableSelectorChange", resetTargets);
        return {
          onSetupTable,
          onSetupCellOrRow,
          onSetupColumn,
          onSetupPasteable,
          onSetupPasteableColumn,
          onSetupMergeable,
          onSetupUnmergeable,
          resetTargets,
          onSetupTableWithCaption,
          onSetupTableRowHeaders,
          onSetupTableColumnHeaders,
          targets: targets.get
        };
      };
      var addButtons = function(editor, selections, selectionTargets, clipboard) {
        editor.ui.registry.addMenuButton("table", {
          tooltip: "Table",
          icon: "table",
          fetch: function(callback) {
            return callback("inserttable | cell row column | advtablesort | tableprops deletetable");
          }
        });
        var cmd = function(command) {
          return function() {
            return editor.execCommand(command);
          };
        };
        editor.ui.registry.addButton("tableprops", {
          tooltip: "Table properties",
          onAction: cmd("mceTableProps"),
          icon: "table",
          onSetup: selectionTargets.onSetupTable
        });
        editor.ui.registry.addButton("tabledelete", {
          tooltip: "Delete table",
          onAction: cmd("mceTableDelete"),
          icon: "table-delete-table",
          onSetup: selectionTargets.onSetupTable
        });
        editor.ui.registry.addButton("tablecellprops", {
          tooltip: "Cell properties",
          onAction: cmd("mceTableCellProps"),
          icon: "table-cell-properties",
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addButton("tablemergecells", {
          tooltip: "Merge cells",
          onAction: cmd("mceTableMergeCells"),
          icon: "table-merge-cells",
          onSetup: selectionTargets.onSetupMergeable
        });
        editor.ui.registry.addButton("tablesplitcells", {
          tooltip: "Split cell",
          onAction: cmd("mceTableSplitCells"),
          icon: "table-split-cells",
          onSetup: selectionTargets.onSetupUnmergeable
        });
        editor.ui.registry.addButton("tableinsertrowbefore", {
          tooltip: "Insert row before",
          onAction: cmd("mceTableInsertRowBefore"),
          icon: "table-insert-row-above",
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addButton("tableinsertrowafter", {
          tooltip: "Insert row after",
          onAction: cmd("mceTableInsertRowAfter"),
          icon: "table-insert-row-after",
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addButton("tabledeleterow", {
          tooltip: "Delete row",
          onAction: cmd("mceTableDeleteRow"),
          icon: "table-delete-row",
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addButton("tablerowprops", {
          tooltip: "Row properties",
          onAction: cmd("mceTableRowProps"),
          icon: "table-row-properties",
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addButton("tableinsertcolbefore", {
          tooltip: "Insert column before",
          onAction: cmd("mceTableInsertColBefore"),
          icon: "table-insert-column-before",
          onSetup: selectionTargets.onSetupColumn("onFirst")
        });
        editor.ui.registry.addButton("tableinsertcolafter", {
          tooltip: "Insert column after",
          onAction: cmd("mceTableInsertColAfter"),
          icon: "table-insert-column-after",
          onSetup: selectionTargets.onSetupColumn("onLast")
        });
        editor.ui.registry.addButton("tabledeletecol", {
          tooltip: "Delete column",
          onAction: cmd("mceTableDeleteCol"),
          icon: "table-delete-column",
          onSetup: selectionTargets.onSetupColumn("onAny")
        });
        editor.ui.registry.addButton("tablecutrow", {
          tooltip: "Cut row",
          icon: "cut-row",
          onAction: cmd("mceTableCutRow"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addButton("tablecopyrow", {
          tooltip: "Copy row",
          icon: "duplicate-row",
          onAction: cmd("mceTableCopyRow"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addButton("tablepasterowbefore", {
          tooltip: "Paste row before",
          icon: "paste-row-before",
          onAction: cmd("mceTablePasteRowBefore"),
          onSetup: selectionTargets.onSetupPasteable(clipboard.getRows)
        });
        editor.ui.registry.addButton("tablepasterowafter", {
          tooltip: "Paste row after",
          icon: "paste-row-after",
          onAction: cmd("mceTablePasteRowAfter"),
          onSetup: selectionTargets.onSetupPasteable(clipboard.getRows)
        });
        editor.ui.registry.addButton("tablecutcol", {
          tooltip: "Cut column",
          icon: "cut-column",
          onAction: cmd("mceTableCutCol"),
          onSetup: selectionTargets.onSetupColumn("onAny")
        });
        editor.ui.registry.addButton("tablecopycol", {
          tooltip: "Copy column",
          icon: "duplicate-column",
          onAction: cmd("mceTableCopyCol"),
          onSetup: selectionTargets.onSetupColumn("onAny")
        });
        editor.ui.registry.addButton("tablepastecolbefore", {
          tooltip: "Paste column before",
          icon: "paste-column-before",
          onAction: cmd("mceTablePasteColBefore"),
          onSetup: selectionTargets.onSetupPasteableColumn(clipboard.getColumns, "onFirst")
        });
        editor.ui.registry.addButton("tablepastecolafter", {
          tooltip: "Paste column after",
          icon: "paste-column-after",
          onAction: cmd("mceTablePasteColAfter"),
          onSetup: selectionTargets.onSetupPasteableColumn(clipboard.getColumns, "onLast")
        });
        editor.ui.registry.addButton("tableinsertdialog", {
          tooltip: "Insert table",
          onAction: cmd("mceInsertTable"),
          icon: "table"
        });
        var tableClassList = filterNoneItem(getTableClassList(editor));
        if (tableClassList.length !== 0) {
          editor.ui.registry.addMenuButton("tableclass", {
            icon: "table-classes",
            tooltip: "Table styles",
            fetch: generateMenuItemsCallback(editor, selections, tableClassList, "tableclass", function(value2) {
              return editor.execCommand("mceTableToggleClass", false, value2);
            }),
            onSetup: selectionTargets.onSetupTable
          });
        }
        var tableCellClassList = filterNoneItem(getCellClassList(editor));
        if (tableCellClassList.length !== 0) {
          editor.ui.registry.addMenuButton("tablecellclass", {
            icon: "table-cell-classes",
            tooltip: "Cell styles",
            fetch: generateMenuItemsCallback(editor, selections, tableCellClassList, "tablecellclass", function(value2) {
              return editor.execCommand("mceTableCellToggleClass", false, value2);
            }),
            onSetup: selectionTargets.onSetupCellOrRow
          });
        }
        editor.ui.registry.addMenuButton("tablecellvalign", {
          icon: "vertical-align",
          tooltip: "Vertical align",
          fetch: generateMenuItemsCallback(editor, selections, verticalAlignValues, "tablecellverticalalign", applyTableCellStyle(editor, "vertical-align")),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuButton("tablecellborderwidth", {
          icon: "border-width",
          tooltip: "Border width",
          fetch: generateMenuItemsCallback(editor, selections, getTableBorderWidths(editor), "tablecellborderwidth", applyTableCellStyle(editor, "border-width")),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuButton("tablecellborderstyle", {
          icon: "border-style",
          tooltip: "Border style",
          fetch: generateMenuItemsCallback(editor, selections, getTableBorderStyles(editor), "tablecellborderstyle", applyTableCellStyle(editor, "border-style")),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addToggleButton("tablecaption", {
          tooltip: "Table caption",
          onAction: cmd("mceTableToggleCaption"),
          icon: "table-caption",
          onSetup: selectionTargets.onSetupTableWithCaption
        });
        editor.ui.registry.addMenuButton("tablecellbackgroundcolor", {
          icon: "cell-background-color",
          tooltip: "Background color",
          fetch: function(callback) {
            return callback(buildColorMenu(editor, getTableBackgroundColorMap(editor), "background-color"));
          },
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuButton("tablecellbordercolor", {
          icon: "cell-border-color",
          tooltip: "Border color",
          fetch: function(callback) {
            return callback(buildColorMenu(editor, getTableBorderColorMap(editor), "border-color"));
          },
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addToggleButton("tablerowheader", {
          tooltip: "Row header",
          icon: "table-top-header",
          onAction: changeRowHeader(editor),
          onSetup: selectionTargets.onSetupTableRowHeaders
        });
        editor.ui.registry.addToggleButton("tablecolheader", {
          tooltip: "Column header",
          icon: "table-left-header",
          onAction: changeColumnHeader(editor),
          onSetup: selectionTargets.onSetupTableColumnHeaders
        });
      };
      var addToolbars = function(editor) {
        var isTable = function(table2) {
          return editor.dom.is(table2, "table") && editor.getBody().contains(table2);
        };
        var toolbar = getToolbar(editor);
        if (toolbar.length > 0) {
          editor.ui.registry.addContextToolbar("table", {
            predicate: isTable,
            items: toolbar,
            scope: "node",
            position: "node"
          });
        }
      };
      var addMenuItems = function(editor, selections, selectionTargets, clipboard) {
        var cmd = function(command) {
          return function() {
            return editor.execCommand(command);
          };
        };
        var insertTableAction = function(data) {
          editor.execCommand("mceInsertTable", false, {
            rows: data.numRows,
            columns: data.numColumns
          });
        };
        var tableProperties = {
          text: "Table properties",
          onSetup: selectionTargets.onSetupTable,
          onAction: cmd("mceTableProps")
        };
        var deleteTable = {
          text: "Delete table",
          icon: "table-delete-table",
          onSetup: selectionTargets.onSetupTable,
          onAction: cmd("mceTableDelete")
        };
        editor.ui.registry.addMenuItem("tableinsertrowbefore", {
          text: "Insert row before",
          icon: "table-insert-row-above",
          onAction: cmd("mceTableInsertRowBefore"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuItem("tableinsertrowafter", {
          text: "Insert row after",
          icon: "table-insert-row-after",
          onAction: cmd("mceTableInsertRowAfter"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuItem("tabledeleterow", {
          text: "Delete row",
          icon: "table-delete-row",
          onAction: cmd("mceTableDeleteRow"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuItem("tablerowprops", {
          text: "Row properties",
          icon: "table-row-properties",
          onAction: cmd("mceTableRowProps"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuItem("tablecutrow", {
          text: "Cut row",
          icon: "cut-row",
          onAction: cmd("mceTableCutRow"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuItem("tablecopyrow", {
          text: "Copy row",
          icon: "duplicate-row",
          onAction: cmd("mceTableCopyRow"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuItem("tablepasterowbefore", {
          text: "Paste row before",
          icon: "paste-row-before",
          onAction: cmd("mceTablePasteRowBefore"),
          onSetup: selectionTargets.onSetupPasteable(clipboard.getRows)
        });
        editor.ui.registry.addMenuItem("tablepasterowafter", {
          text: "Paste row after",
          icon: "paste-row-after",
          onAction: cmd("mceTablePasteRowAfter"),
          onSetup: selectionTargets.onSetupPasteable(clipboard.getRows)
        });
        var row2 = {
          type: "nestedmenuitem",
          text: "Row",
          getSubmenuItems: constant("tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter")
        };
        editor.ui.registry.addMenuItem("tableinsertcolumnbefore", {
          text: "Insert column before",
          icon: "table-insert-column-before",
          onAction: cmd("mceTableInsertColBefore"),
          onSetup: selectionTargets.onSetupColumn("onFirst")
        });
        editor.ui.registry.addMenuItem("tableinsertcolumnafter", {
          text: "Insert column after",
          icon: "table-insert-column-after",
          onAction: cmd("mceTableInsertColAfter"),
          onSetup: selectionTargets.onSetupColumn("onLast")
        });
        editor.ui.registry.addMenuItem("tabledeletecolumn", {
          text: "Delete column",
          icon: "table-delete-column",
          onAction: cmd("mceTableDeleteCol"),
          onSetup: selectionTargets.onSetupColumn("onAny")
        });
        editor.ui.registry.addMenuItem("tablecutcolumn", {
          text: "Cut column",
          icon: "cut-column",
          onAction: cmd("mceTableCutCol"),
          onSetup: selectionTargets.onSetupColumn("onAny")
        });
        editor.ui.registry.addMenuItem("tablecopycolumn", {
          text: "Copy column",
          icon: "duplicate-column",
          onAction: cmd("mceTableCopyCol"),
          onSetup: selectionTargets.onSetupColumn("onAny")
        });
        editor.ui.registry.addMenuItem("tablepastecolumnbefore", {
          text: "Paste column before",
          icon: "paste-column-before",
          onAction: cmd("mceTablePasteColBefore"),
          onSetup: selectionTargets.onSetupPasteableColumn(clipboard.getColumns, "onFirst")
        });
        editor.ui.registry.addMenuItem("tablepastecolumnafter", {
          text: "Paste column after",
          icon: "paste-column-after",
          onAction: cmd("mceTablePasteColAfter"),
          onSetup: selectionTargets.onSetupPasteableColumn(clipboard.getColumns, "onLast")
        });
        var column = {
          type: "nestedmenuitem",
          text: "Column",
          getSubmenuItems: constant("tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter")
        };
        editor.ui.registry.addMenuItem("tablecellprops", {
          text: "Cell properties",
          icon: "table-cell-properties",
          onAction: cmd("mceTableCellProps"),
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addMenuItem("tablemergecells", {
          text: "Merge cells",
          icon: "table-merge-cells",
          onAction: cmd("mceTableMergeCells"),
          onSetup: selectionTargets.onSetupMergeable
        });
        editor.ui.registry.addMenuItem("tablesplitcells", {
          text: "Split cell",
          icon: "table-split-cells",
          onAction: cmd("mceTableSplitCells"),
          onSetup: selectionTargets.onSetupUnmergeable
        });
        var cell2 = {
          type: "nestedmenuitem",
          text: "Cell",
          getSubmenuItems: constant("tablecellprops tablemergecells tablesplitcells")
        };
        if (hasTableGrid(editor) === false) {
          editor.ui.registry.addMenuItem("inserttable", {
            text: "Table",
            icon: "table",
            onAction: cmd("mceInsertTable")
          });
        } else {
          editor.ui.registry.addNestedMenuItem("inserttable", {
            text: "Table",
            icon: "table",
            getSubmenuItems: function() {
              return [{
                type: "fancymenuitem",
                fancytype: "inserttable",
                onAction: insertTableAction
              }];
            }
          });
        }
        editor.ui.registry.addMenuItem("inserttabledialog", {
          text: "Insert table",
          icon: "table",
          onAction: cmd("mceInsertTable")
        });
        editor.ui.registry.addMenuItem("tableprops", tableProperties);
        editor.ui.registry.addMenuItem("deletetable", deleteTable);
        editor.ui.registry.addNestedMenuItem("row", row2);
        editor.ui.registry.addNestedMenuItem("column", column);
        editor.ui.registry.addNestedMenuItem("cell", cell2);
        editor.ui.registry.addContextMenu("table", {
          update: function() {
            selectionTargets.resetTargets();
            return selectionTargets.targets().fold(constant(""), function(targets) {
              if (name(targets.element) === "caption") {
                return "tableprops deletetable";
              } else {
                return "cell row column | advtablesort | tableprops deletetable";
              }
            });
          }
        });
        var tableClassList = filterNoneItem(getTableClassList(editor));
        if (tableClassList.length !== 0) {
          editor.ui.registry.addNestedMenuItem("tableclass", {
            icon: "table-classes",
            text: "Table styles",
            getSubmenuItems: function() {
              return buildMenuItems(editor, selections, tableClassList, "tableclass", function(value2) {
                return editor.execCommand("mceTableToggleClass", false, value2);
              });
            },
            onSetup: selectionTargets.onSetupTable
          });
        }
        var tableCellClassList = filterNoneItem(getCellClassList(editor));
        if (tableCellClassList.length !== 0) {
          editor.ui.registry.addNestedMenuItem("tablecellclass", {
            icon: "table-cell-classes",
            text: "Cell styles",
            getSubmenuItems: function() {
              return buildMenuItems(editor, selections, tableCellClassList, "tablecellclass", function(value2) {
                return editor.execCommand("mceTableCellToggleClass", false, value2);
              });
            },
            onSetup: selectionTargets.onSetupCellOrRow
          });
        }
        editor.ui.registry.addNestedMenuItem("tablecellvalign", {
          icon: "vertical-align",
          text: "Vertical align",
          getSubmenuItems: function() {
            return buildMenuItems(editor, selections, verticalAlignValues, "tablecellverticalalign", applyTableCellStyle(editor, "vertical-align"));
          },
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addNestedMenuItem("tablecellborderwidth", {
          icon: "border-width",
          text: "Border width",
          getSubmenuItems: function() {
            return buildMenuItems(editor, selections, getTableBorderWidths(editor), "tablecellborderwidth", applyTableCellStyle(editor, "border-width"));
          },
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addNestedMenuItem("tablecellborderstyle", {
          icon: "border-style",
          text: "Border style",
          getSubmenuItems: function() {
            return buildMenuItems(editor, selections, getTableBorderStyles(editor), "tablecellborderstyle", applyTableCellStyle(editor, "border-style"));
          },
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addToggleMenuItem("tablecaption", {
          icon: "table-caption",
          text: "Table caption",
          onAction: cmd("mceTableToggleCaption"),
          onSetup: selectionTargets.onSetupTableWithCaption
        });
        editor.ui.registry.addNestedMenuItem("tablecellbackgroundcolor", {
          icon: "cell-background-color",
          text: "Background color",
          getSubmenuItems: function() {
            return buildColorMenu(editor, getTableBackgroundColorMap(editor), "background-color");
          },
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addNestedMenuItem("tablecellbordercolor", {
          icon: "cell-border-color",
          text: "Border color",
          getSubmenuItems: function() {
            return buildColorMenu(editor, getTableBorderColorMap(editor), "border-color");
          },
          onSetup: selectionTargets.onSetupCellOrRow
        });
        editor.ui.registry.addToggleMenuItem("tablerowheader", {
          text: "Row header",
          icon: "table-top-header",
          onAction: changeRowHeader(editor),
          onSetup: selectionTargets.onSetupTableRowHeaders
        });
        editor.ui.registry.addToggleMenuItem("tablecolheader", {
          text: "Column header",
          icon: "table-left-header",
          onAction: changeColumnHeader(editor),
          onSetup: selectionTargets.onSetupTableColumnHeaders
        });
      };
      var Plugin = function(editor) {
        var selections = Selections(function() {
          return getBody(editor);
        }, function() {
          return getSelectionCell(getSelectionStart(editor), getIsRoot(editor));
        }, ephemera.selectedSelector);
        var selectionTargets = getSelectionTargets(editor, selections);
        var resizeHandler = getResizeHandler(editor);
        var cellSelection = CellSelection(editor, resizeHandler.lazyResize, selectionTargets);
        var actions = TableActions(editor, cellSelection, resizeHandler.lazyWire);
        var clipboard = Clipboard();
        registerCommands(editor, actions, cellSelection, selections, clipboard);
        registerQueryCommands(editor, actions, selections);
        registerEvents(editor, selections, actions);
        addMenuItems(editor, selections, selectionTargets, clipboard);
        addButtons(editor, selections, selectionTargets, clipboard);
        addToolbars(editor);
        editor.on("PreInit", function() {
          editor.serializer.addTempAttr(ephemera.firstSelected);
          editor.serializer.addTempAttr(ephemera.lastSelected);
          registerFormats(editor);
        });
        if (hasTabNavigation(editor)) {
          editor.on("keydown", function(e) {
            handle$1(e, editor, cellSelection);
          });
        }
        editor.on("remove", function() {
          resizeHandler.destroy();
        });
        return getApi(editor, clipboard, resizeHandler, selectionTargets);
      };
      function Plugin$1() {
        global$3.add("table", Plugin);
      }
      Plugin$1();
    })();
  }
});

// node_modules/tinymce/plugins/table/index.js
var require_table = __commonJS({
  "node_modules/tinymce/plugins/table/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_table
var tinymce_plugins_table_default = require_table();
export {
  tinymce_plugins_table_default as default
};
//# sourceMappingURL=tinymce_plugins_table.js.map
