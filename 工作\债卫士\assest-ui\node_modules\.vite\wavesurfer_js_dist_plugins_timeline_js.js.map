{"version": 3, "sources": ["../wavesurfer.js/dist/plugins/timeline.js", "dep:wavesurfer_js_dist_plugins_timeline_js"], "sourcesContent": ["class t{constructor(){this.listeners={}}on(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),null==i?void 0:i.once){const i=()=>{this.un(t,i),this.un(t,e)};return this.on(t,i),i}return()=>this.un(t,e)}un(t,e){var i;null===(i=this.listeners[t])||void 0===i||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach((t=>t(...e)))}}class e extends t{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}_init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t()))}}function i(t,e){const n=e.xmlns?document.createElementNS(e.xmlns,t):document.createElement(t);for(const[t,s]of Object.entries(e))if(\"children\"===t&&s)for(const[t,e]of Object.entries(s))e instanceof Node?n.appendChild(e):\"string\"==typeof e?n.appendChild(document.createTextNode(e)):n.appendChild(i(t,e));else\"style\"===t?Object.assign(n.style,s):\"textContent\"===t?n.textContent=s:n.setAttribute(t,s.toString());return n}function n(t,e,n){return i(t,e||{})}const s={height:20,timeOffset:0,formatTimeCallback:t=>{if(t/60>1){return`${Math.floor(t/60)}:${`${(t=Math.round(t%60))<10?\"0\":\"\"}${t}`}`}return`${Math.round(1e3*t)/1e3}`}};class r extends e{constructor(t){super(t||{}),this.options=Object.assign({},s,t),this.timelineWrapper=this.initTimelineWrapper()}static create(t){return new r(t)}onInit(){var t;if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");let e=this.wavesurfer.getWrapper();if(this.options.container instanceof HTMLElement)e=this.options.container;else if(\"string\"==typeof this.options.container){const t=document.querySelector(this.options.container);if(!t)throw Error(`No Timeline container found matching ${this.options.container}`);e=t}this.options.insertPosition?(e.firstElementChild||e).insertAdjacentElement(this.options.insertPosition,this.timelineWrapper):e.appendChild(this.timelineWrapper),this.subscriptions.push(this.wavesurfer.on(\"redraw\",(()=>this.initTimeline()))),((null===(t=this.wavesurfer)||void 0===t?void 0:t.getDuration())||this.options.duration)&&this.initTimeline()}destroy(){this.timelineWrapper.remove(),super.destroy()}initTimelineWrapper(){return n(\"div\",{part:\"timeline-wrapper\",style:{pointerEvents:\"none\"}})}defaultTimeInterval(t){return t>=25?1:5*t>=25?5:15*t>=25?15:60*Math.ceil(.5/t)}defaultPrimaryLabelInterval(t){return t>=25?10:5*t>=25?6:4}defaultSecondaryLabelInterval(t){return t>=25?5:2}virtualAppend(t,e,i){let n=!1;const s=(s,r)=>{if(!this.wavesurfer)return;const o=i.clientWidth,l=t>s&&t+o<r;l!==n&&(n=l,l?e.appendChild(i):i.remove())};if(!this.wavesurfer)return;const r=this.wavesurfer.getScroll(),o=r+this.wavesurfer.getWidth();s(r,o),this.subscriptions.push(this.wavesurfer.on(\"scroll\",((t,e,i,n)=>{s(i,n)})))}initTimeline(){var t,e,i,s,r,o,l,a;const h=null!==(i=null!==(e=null===(t=this.wavesurfer)||void 0===t?void 0:t.getDuration())&&void 0!==e?e:this.options.duration)&&void 0!==i?i:0,p=((null===(s=this.wavesurfer)||void 0===s?void 0:s.getWrapper().scrollWidth)||this.timelineWrapper.scrollWidth)/h,u=null!==(r=this.options.timeInterval)&&void 0!==r?r:this.defaultTimeInterval(p),d=null!==(o=this.options.primaryLabelInterval)&&void 0!==o?o:this.defaultPrimaryLabelInterval(p),c=this.options.primaryLabelSpacing,f=null!==(l=this.options.secondaryLabelInterval)&&void 0!==l?l:this.defaultSecondaryLabelInterval(p),v=this.options.secondaryLabelSpacing,m=\"beforebegin\"===this.options.insertPosition,y=n(\"div\",{style:Object.assign({height:`${this.options.height}px`,overflow:\"hidden\",fontSize:this.options.height/2+\"px\",whiteSpace:\"nowrap\"},m?{position:\"absolute\",top:\"0\",left:\"0\",right:\"0\",zIndex:\"2\"}:{position:\"relative\"})});y.setAttribute(\"part\",\"timeline\"),\"string\"==typeof this.options.style?y.setAttribute(\"style\",y.getAttribute(\"style\")+this.options.style):\"object\"==typeof this.options.style&&Object.assign(y.style,this.options.style);const b=n(\"div\",{style:{width:\"0\",height:\"50%\",display:\"flex\",flexDirection:\"column\",justifyContent:m?\"flex-start\":\"flex-end\",top:m?\"0\":\"auto\",bottom:m?\"auto\":\"0\",overflow:\"visible\",borderLeft:\"1px solid currentColor\",opacity:`${null!==(a=this.options.secondaryLabelOpacity)&&void 0!==a?a:.25}`,position:\"absolute\",zIndex:\"1\"}});for(let t=0,e=0;t<h;t+=u,e++){const i=b.cloneNode(),n=Math.round(100*t)%Math.round(100*d)==0||c&&e%c==0,s=Math.round(100*t)%Math.round(100*f)==0||v&&e%v==0;(n||s)&&(i.style.height=\"100%\",i.style.textIndent=\"3px\",i.textContent=this.options.formatTimeCallback(t),n&&(i.style.opacity=\"1\"));const r=n?\"primary\":s?\"secondary\":\"tick\";i.setAttribute(\"part\",`timeline-notch timeline-notch-${r}`);const o=Math.round(100*(t+this.options.timeOffset))/100*p;i.style.left=`${o}px`,this.virtualAppend(o,y,i)}this.timelineWrapper.innerHTML=\"\",this.timelineWrapper.appendChild(y),this.emit(\"ready\")}}export{r as default};\n", "import d from \"./node_modules/wavesurfer.js/dist/plugins/timeline.js\";export default d;"], "mappings": ";;;AAAA,cAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU;AAAA;AAAA,EAAG,GAAG,IAAE,IAAE,IAAE;AAAC,QAAG,KAAK,UAAU,OAAK,MAAK,UAAU,MAAG,IAAI,QAAK,KAAK,UAAU,IAAG,IAAI,KAAG,AAAM,MAAN,OAAQ,SAAO,GAAE,MAAK;AAAC,YAAM,KAAE,MAAI;AAAC,aAAK,GAAG,IAAE,KAAG,KAAK,GAAG,IAAE;AAAA;AAAI,aAAO,KAAK,GAAG,IAAE,KAAG;AAAA;AAAE,WAAM,MAAI,KAAK,GAAG,IAAE;AAAA;AAAA,EAAG,GAAG,IAAE,IAAE;AAAC,QAAI;AAAE,IAAQ,MAAE,KAAK,UAAU,SAAzB,QAA8B,AAAS,OAAT,UAAY,GAAE,OAAO;AAAA;AAAA,EAAG,KAAK,IAAE,IAAE;AAAC,WAAO,KAAK,GAAG,IAAE,IAAE,EAAC,MAAK;AAAA;AAAA,EAAK,QAAO;AAAC,SAAK,YAAU;AAAA;AAAA,EAAG,KAAK,OAAK,IAAE;AAAC,SAAK,UAAU,OAAI,KAAK,UAAU,IAAG,QAAS,QAAG,GAAE,GAAG;AAAA;AAAA;AAAM,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE;AAAC,aAAQ,KAAK,gBAAc,IAAG,KAAK,UAAQ;AAAA;AAAA,EAAE,SAAQ;AAAA;AAAA,EAAE,MAAM,IAAE;AAAC,SAAK,aAAW,IAAE,KAAK;AAAA;AAAA,EAAS,UAAS;AAAC,SAAK,KAAK,YAAW,KAAK,cAAc,QAAS,QAAG;AAAA;AAAA;AAAO,WAAW,IAAE,IAAE;AAAC,QAAM,KAAE,GAAE,QAAM,SAAS,gBAAgB,GAAE,OAAM,MAAG,SAAS,cAAc;AAAG,aAAS,CAAC,IAAE,OAAK,OAAO,QAAQ;AAAG,QAAG,AAAa,OAAb,cAAgB;AAAE,iBAAS,CAAC,IAAE,OAAK,OAAO,QAAQ;AAAG,sBAAa,OAAK,GAAE,YAAY,MAAG,AAAU,OAAO,MAAjB,WAAmB,GAAE,YAAY,SAAS,eAAe,OAAI,GAAE,YAAY,EAAE,IAAE;AAAA;AAAQ,MAAU,OAAV,UAAY,OAAO,OAAO,GAAE,OAAM,MAAG,AAAgB,OAAhB,gBAAkB,GAAE,cAAY,KAAE,GAAE,aAAa,IAAE,GAAE;AAAY,SAAO;AAAA;AAAE,WAAW,IAAE,IAAE,IAAE;AAAC,SAAO,EAAE,IAAE,MAAG;AAAA;AAAI,IAAM,IAAE,EAAC,QAAO,IAAG,YAAW,GAAE,oBAAmB,QAAG;AAAC,MAAG,KAAE,KAAG,GAAE;AAAC,WAAM,GAAG,KAAK,MAAM,KAAE,OAAO,GAAI,MAAE,KAAK,MAAM,KAAE,OAAK,KAAG,MAAI,KAAK;AAAA;AAAM,SAAM,GAAG,KAAK,MAAM,MAAI,MAAG;AAAA;AAAQ,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE;AAAC,UAAM,MAAG,KAAI,KAAK,UAAQ,OAAO,OAAO,IAAG,GAAE,KAAG,KAAK,kBAAgB,KAAK;AAAA;AAAA,SAA6B,OAAO,IAAE;AAAC,WAAO,IAAI,EAAE;AAAA;AAAA,EAAG,SAAQ;AAAC,QAAI;AAAE,QAAG,CAAC,KAAK;AAAW,YAAM,MAAM;AAAiC,QAAI,KAAE,KAAK,WAAW;AAAa,QAAG,KAAK,QAAQ,qBAAqB;AAAY,WAAE,KAAK,QAAQ;AAAA,aAAkB,AAAU,OAAO,KAAK,QAAQ,aAA9B,UAAwC;AAAC,YAAM,KAAE,SAAS,cAAc,KAAK,QAAQ;AAAW,UAAG,CAAC;AAAE,cAAM,MAAM,wCAAwC,KAAK,QAAQ;AAAa,WAAE;AAAA;AAAE,SAAK,QAAQ,iBAAgB,IAAE,qBAAmB,IAAG,sBAAsB,KAAK,QAAQ,gBAAe,KAAK,mBAAiB,GAAE,YAAY,KAAK,kBAAiB,KAAK,cAAc,KAAK,KAAK,WAAW,GAAG,UAAU,MAAI,KAAK,kBAAoB,EAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,SAAW,SAAO,GAAE,kBAAgB,KAAK,QAAQ,aAAW,KAAK;AAAA;AAAA,EAAe,UAAS;AAAC,SAAK,gBAAgB,UAAS,MAAM;AAAA;AAAA,EAAU,sBAAqB;AAAC,WAAO,EAAE,OAAM,EAAC,MAAK,oBAAmB,OAAM,EAAC,eAAc;AAAA;AAAA,EAAU,oBAAoB,IAAE;AAAC,WAAO,MAAG,KAAG,IAAE,IAAE,MAAG,KAAG,IAAE,KAAG,MAAG,KAAG,KAAG,KAAG,KAAK,KAAK,MAAG;AAAA;AAAA,EAAG,4BAA4B,IAAE;AAAC,WAAO,MAAG,KAAG,KAAG,IAAE,MAAG,KAAG,IAAE;AAAA;AAAA,EAAE,8BAA8B,IAAE;AAAC,WAAO,MAAG,KAAG,IAAE;AAAA;AAAA,EAAE,cAAc,IAAE,IAAE,IAAE;AAAC,QAAI,KAAE;AAAG,UAAM,KAAE,CAAC,IAAE,OAAI;AAAC,UAAG,CAAC,KAAK;AAAW;AAAO,YAAM,KAAE,GAAE,aAAY,IAAE,KAAE,MAAG,KAAE,KAAE;AAAE,YAAI,MAAI,MAAE,GAAE,IAAE,GAAE,YAAY,MAAG,GAAE;AAAA;AAAW,QAAG,CAAC,KAAK;AAAW;AAAO,UAAM,KAAE,KAAK,WAAW,aAAY,IAAE,KAAE,KAAK,WAAW;AAAW,OAAE,IAAE,IAAG,KAAK,cAAc,KAAK,KAAK,WAAW,GAAG,UAAU,CAAC,IAAE,IAAE,IAAE,OAAI;AAAC,SAAE,IAAE;AAAA;AAAA;AAAA,EAAO,eAAc;AAAC,QAAI,IAAE,IAAE,IAAE,IAAE,IAAE,GAAE,GAAE;AAAE,UAAM,IAAE,AAAQ,MAAE,AAAQ,MAAE,AAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,SAAW,SAAO,GAAE,mBAA1D,QAA0E,AAAS,OAAT,SAAW,KAAE,KAAK,QAAQ,cAA9G,QAAyH,AAAS,OAAT,SAAW,KAAE,GAAE,IAAI,EAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,SAAW,SAAO,GAAE,aAAa,gBAAc,KAAK,gBAAgB,eAAa,GAAE,IAAE,AAAQ,MAAE,KAAK,QAAQ,kBAAvB,QAAsC,AAAS,OAAT,SAAW,KAAE,KAAK,oBAAoB,IAAG,IAAE,AAAQ,KAAE,KAAK,QAAQ,0BAAvB,QAA8C,AAAS,MAAT,SAAW,IAAE,KAAK,4BAA4B,IAAG,IAAE,KAAK,QAAQ,qBAAoB,IAAE,AAAQ,KAAE,KAAK,QAAQ,4BAAvB,QAAgD,AAAS,MAAT,SAAW,IAAE,KAAK,8BAA8B,IAAG,IAAE,KAAK,QAAQ,uBAAsB,IAAE,AAAgB,KAAK,QAAQ,mBAA7B,eAA4C,IAAE,EAAE,OAAM,EAAC,OAAM,OAAO,OAAO,EAAC,QAAO,GAAG,KAAK,QAAQ,YAAW,UAAS,UAAS,UAAS,KAAK,QAAQ,SAAO,IAAE,MAAK,YAAW,YAAU,IAAE,EAAC,UAAS,YAAW,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,QAAK,EAAC,UAAS;AAAe,MAAE,aAAa,QAAO,aAAY,AAAU,OAAO,KAAK,QAAQ,SAA9B,WAAoC,EAAE,aAAa,SAAQ,EAAE,aAAa,WAAS,KAAK,QAAQ,SAAO,AAAU,OAAO,KAAK,QAAQ,SAA9B,YAAqC,OAAO,OAAO,EAAE,OAAM,KAAK,QAAQ;AAAO,UAAM,IAAE,EAAE,OAAM,EAAC,OAAM,EAAC,OAAM,KAAI,QAAO,OAAM,SAAQ,QAAO,eAAc,UAAS,gBAAe,IAAE,eAAa,YAAW,KAAI,IAAE,MAAI,QAAO,QAAO,IAAE,SAAO,KAAI,UAAS,WAAU,YAAW,0BAAyB,SAAQ,GAAG,AAAQ,KAAE,KAAK,QAAQ,2BAAvB,QAA+C,AAAS,MAAT,SAAW,IAAE,QAAM,UAAS,YAAW,QAAO;AAAO,aAAQ,KAAE,GAAE,KAAE,GAAE,KAAE,GAAE,MAAG,GAAE,MAAI;AAAC,YAAM,KAAE,EAAE,aAAY,KAAE,KAAK,MAAM,MAAI,MAAG,KAAK,MAAM,MAAI,MAAI,KAAG,KAAG,KAAE,KAAG,GAAE,KAAE,KAAK,MAAM,MAAI,MAAG,KAAK,MAAM,MAAI,MAAI,KAAG,KAAG,KAAE,KAAG;AAAE,MAAC,OAAG,OAAK,IAAE,MAAM,SAAO,QAAO,GAAE,MAAM,aAAW,OAAM,GAAE,cAAY,KAAK,QAAQ,mBAAmB,KAAG,MAAI,IAAE,MAAM,UAAQ;AAAM,YAAM,KAAE,KAAE,YAAU,KAAE,cAAY;AAAO,SAAE,aAAa,QAAO,iCAAiC;AAAK,YAAM,KAAE,KAAK,MAAM,MAAK,MAAE,KAAK,QAAQ,eAAa,MAAI;AAAE,SAAE,MAAM,OAAK,GAAG,QAAM,KAAK,cAAc,IAAE,GAAE;AAAA;AAAG,SAAK,gBAAgB,YAAU,IAAG,KAAK,gBAAgB,YAAY,IAAG,KAAK,KAAK;AAAA;AAAA;;;ACAxrJ,IAAO,iDAAQ;", "names": []}