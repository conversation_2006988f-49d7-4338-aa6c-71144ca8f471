import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'
// import Inspect from 'vite-plugin-inspect'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    base: '/',
    build: {
      chunkSizeWarningLimit: 1600, //文件大于600kb警告
      rollupOptions: {
        output: {
          //文件分割成更小的模块
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString();
            }
          },
          // chunkFileNames: (chunkInfo) => {
          //   const facadeModuleId = chunkInfo.facadeModuleId
          //     ? chunkInfo.facadeModuleId.split('/')
          //     : [];
          //   const fileName =
          //     facadeModuleId[facadeModuleId.length - 2] || '[name]';
          //   return `js/${fileName}/[name].[hash].js`;
          // }
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ],
      },
    },
    //plugins: [createVitePlugins(env, command === 'build'), Inspect()],
    plugins: [createVitePlugins(env, command === 'build')],
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src'),
        'element-plus/lib/locale/lang/zh-cn': 'element-plus/es/locale/lang/zh-cn',
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      host: '0.0.0.0',
      port: 4020,
      open: true,
      proxy: {
        '/dev-api': {
          // target: 'http://*************/prod-api/', // 测试环境
          // target: 'http://**************:8080/', // 个人环境
          target: 'https://assest.amcmj.com/prod-api/', // 演示环境
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, '')
        }
      },
    },
  }
})
