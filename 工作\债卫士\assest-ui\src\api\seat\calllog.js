import request from '@/utils/request'

// 查询参列表
export function calllogs(query) {
  return request({
    url: '/caseManage/call/record/list',
    method: 'get',
    params: query
  })
}

//呼叫类型下拉
export function getCallroters() {
  return request({
    url: '/caseManage/call/record/getCallroters',
    method: 'get',
  })
}

//接听状态
export function getAnswerStatus() {
  return request({
    url: '/caseManage/call/record/getAnswerStatus',
    method: 'get',
  })
}

//创建下载录音任务
export function createDownloadTask(data) {
  return request({
    url: '/caseManage/call/record/createDownloadTask',
    method: 'post',
    data: data
  })
}
//删除通话记录
export function deleteCallRecordApi(data) {
  return request({
    url: '/caseManage/call/record/deleteCallRecord',
    method: 'post',
    data: data
  })
}
//恢复通话记录
export function recoverCallRecordApi(data) {
  return request({
    url: '/caseManage/call/record/recoverCallRecord',
    method: 'post',
    data: data
  })
}
//更新录音记录表
export function updateCallReCordApi(data) {
  return request({
    url: '/caseManage/call/record/updateCallReCord',
    method: 'put',
    data: data
  })
}
//上传录音文件
export function uploadFileListApi(data) {
  return request({
    url: '/caseManage/call/uploadFileList',
    method: 'post',
    data: data
  })
}
//清理过期通话记录
export function clearCallRecordsApi(data) {
  return request({
    url: '/caseManage/call/record/cleanupExpiredCallRecords',
    method: 'post',
    data: data
  })
}
//根据id获取通话记录
export function selectCallRecordByIdApi(params) {
  return request({
    url: '/caseManage/call/record/selectUpdateRecordList',
    method: 'get',
    params: params
  })
}
//根据id获取通话记录
export function qualityOrderApi(data) {
  return request({
    url: '/caseManage/call/record/qualityOrder',
    method: 'post',
    data: data
  })
}