<template>
    <div class="app-container ">
        <el-row v-loading="loading">
            <el-col :span="4">
                <LeftSideTree :data="treeData" :nodeClick="handleTabClick" />
            </el-col>
            <el-col :span="20">
                <el-form :model="queryParams" label-width="auto" inline>
                    <el-form-item prop="batchNums" label="资产包编号">
                        <el-input v-model="queryParams.assetsNo" placeholder="请输入资产包编号" style="width:320px" />
                    </el-form-item>
                    <el-form-item prop="teamIdList" label="资产包名称">
                        <el-cascader v-model="queryParams.teamIdList" :options="teamOption"
                            :props="defaultPropsCascader" collapse-tags collapse-tags-tooltip clearable
                            placeholder="请选择资产包名称" filterable :reserve-keyword="false" style="width:320px" />
                    </el-form-item>
                </el-form>
                <div class="text-center">
                    <el-button :loading="loading" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
                    <el-button :loading="loading" @click="antiShake(resetQuery)">重置</el-button>
                </div>
                <div class="mt10 mb10">
                    <span>可转资产总额：<i class="danger">20,013,853.31</i></span>
                    <span class="ml20">已转资产总额：<i class="danger">16,627,941.28</i></span>
                    <span class="ml20">资产包数量：<i class="danger">72</i></span>
                    <span class="ml20">案件总数：<i class="danger">1398771</i></span>
                </div>
                <el-table v-loading="loading" :data="dataList">
                    <el-table-column label="资产包状态" prop="assetsStatus" align="center" :width="120" />
                    <el-table-column label="资产包编号" prop="batchNum" align="center" min-width="180" />
                    <el-table-column label="资产包名称" prop="packageName" align="center" min-width="180" />
                    <el-table-column label="项目名称" prop="projectName" align="center" min-width="180" />
                    <el-table-column label="案件量" prop="caseNumber" align="center" width="110" />
                    <el-table-column label="债权总金额（元）" prop="entrustMoneyTotal" width="160" align="center">
                        <template #default="{ row }">
                            {{ $toThousands(row.entrustMoneyTotal || 0) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="转让方" prop="ownerName" :width="180" align="center" />
                    <el-table-column label="收购成本（元）" prop="acquisitionCosts" align="center" min-width="140px">
                        <template #default="{ row }">
                            {{ $toThousands(row.acquisitionCosts || 0) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="回款目标金额（元）" prop="targetAmount" align="center" min-width="180px">
                        <template #default="{ row }">
                            {{ $toThousands(row.targetAmount || 0) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="回款目标周期" prop="period" align="center" min-width="200px">
                        <template #default="{ row }">
                            <span>
                                {{ row.beginPeriod ? row.beginPeriod + " / " : "" }}
                                {{ row.endPeriod }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="收购日期" prop="acquisitionDate" align="center" width="100px" />
                    <el-table-column label="费率（%）" prop="rate" align="center" min-width="120px" />
                    <el-table-column label="预期收益（元）" prop="expectedRevenue" align="center" min-width="140px">
                        <template #default="{ row }">
                            {{ $toThousands(row.expectedRevenue || 0) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作人" prop="createBy" align="center" min-width="100px" />
                    <el-table-column label="操作时间" prop="updateTime" align="center" width="200px" />
                    <el-table-column label="状态" prop="importStartInfo" align="center" />
                    <el-table-column label="操作" width="120" align="center" fixed="right">
                        <template #default="{ row }">
                            <el-button type="text" @click="handleDetails(row)">查看详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize" @pagination="getList" />
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import LeftSideTree from '@/components/leftSideTree/index';
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const dataList = ref([
    {
        "createBy": "admin",
        "createTime": "2025-05-08 18:19:45",
        "updateTime": "2025-05-08 18:19:46",
        "assetsStatus": "评估尽调",
        "batchNum": "zsyhfoshan202502CSD0002",
        "caseNumber": 15,
        "activeStep": 1,
        "entrustMoneyTotal": 45000,
        "productName": "测试贷",
        "ownerName": "招商银行佛山分行",
        "autoReduction": "1",
        "importStart": "1",
        "projectName": '华北地区中小企业信贷不良资产一号包',
        "packageName": "招商银行4月第1季度信用不良1号包",
        "openAccount": "**************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-05-08 18:16:57",
        "updateTime": "2025-05-08 18:17:01",
        "assetsStatus": "银登登记",
        "batchNum": "zsyhfoshan202502CSD0001",
        "caseNumber": 0,
        "activeStep": 2,
        "entrustMoneyTotal": 0,
        "productName": "测试贷",
        "ownerName": "招商银行佛山分行",
        "autoReduction": "1",
        "importStart": "2",
        "projectName": '华北地区中小企业信贷不良资产一号包',
        "packageName": "招商银行4月第1季度信用不良2号包",
        "openAccount": "**************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-04-22 18:51:40",
        "updateBy": "admin",
        "activeStep": 2,
        "updateTime": "2025-04-27 20:31:20",
        "assetsStatus": "取消登记",
        "batchNum": "CITIC202502CC0002",
        "caseNumber": 1,
        "entrustMoneyTotal": 10000,
        "productName": "信用卡",
        "ownerName": "中信银行",
        "autoReduction": "1",
        "importStart": "1",
        "projectName": '中西部地区制造业企业不良债权四号包',
        "packageName": "招商银行4月第1季度信用不良3号包",
        "openAccount": "**************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-04-22 17:00:01",
        "updateTime": "2025-04-22 17:00:03",
        "assetsStatus": "银登挂牌",
        "batchNum": "zsyhfoshan202502CC0007",
        "caseNumber": 10,
        "activeStep": 3,
        "entrustMoneyTotal": 25000,
        "productName": "信用卡",
        "ownerName": "招商银行佛山分行",
        "autoReduction": "1",
        "importStart": "1",
        "projectName": '华北地区中小企业信贷不良资产一号包',
        "packageName": "招商银行4月第1季度信用不良4号包",
        "openAccount": "**************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-04-22 16:56:20",
        "updateBy": "admin",
        "activeStep": 3,
        "updateTime": "2025-04-22 17:40:19",
        "assetsStatus": "取消挂牌",
        "batchNum": "YX202502BP0001",
        "caseNumber": 1,
        "entrustMoneyTotal": 10000,
        "productName": "经营贷（个人）",
        "ownerName": "友信",
        "autoReduction": "1",
        "importStart": "1",
        "projectName": '京津冀地区商业地产抵债资产五号包',
        "packageName": "招商银行4月第1季度信用不良5号包",
        "openAccount": "*************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-04-22 16:44:14",
        "updateTime": "2025-04-22 16:44:15",
        "assetsStatus": "银登竞拍",
        "batchNum": "CITIC202502BP0004",
        "caseNumber": 1,
        "activeStep": 4,
        "entrustMoneyTotal": 120000,
        "productName": "经营贷（个人）",
        "ownerName": "中信银行",
        "autoReduction": "1",
        "importStart": "1",
        "projectName": '京津冀地区商业地产抵债资产五号包',
        "packageName": "招商银行4月第1季度信用不良6号包",
        "openAccount": "**************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-04-22 15:47:15",
        "updateBy": "admin",
        "activeStep": 5,
        "updateTime": "2025-04-22 18:32:43",
        "assetsStatus": "协议签署",
        "batchNum": "CITIC202502CC0001",
        "caseNumber": 2,
        "entrustMoneyTotal": 20000,
        "productName": "信用卡",
        "ownerName": "中信银行",
        "autoReduction": "0",
        "importStart": "1",
        "projectName": '海西经济区个人经营贷不良资产七号包',
        "packageName": "招商银行4月第1季度信用不良7号包",
        "openAccount": "**************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-04-22 15:28:23",
        "updateTime": "2025-04-22 15:28:24",
        "assetsStatus": "付款确认",
        "batchNum": "CITIC202502BP0003",
        "caseNumber": 1,
        "activeStep": 6,
        "entrustMoneyTotal": 120000,
        "productName": "经营贷（个人）",
        "ownerName": "中信银行",
        "autoReduction": "1",
        "importStart": "1",
        "projectName": '东北地区国有企业不良债权八号包',
        "packageName": "招商银行4月第1季度信用不良8号包",
        "openAccount": "**************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-04-22 14:36:23",
        "updateTime": "2025-04-22 14:36:24",
        "assetsStatus": "资产交割",
        "batchNum": "CITIC202502BP0002",
        "caseNumber": 1,
        "activeStep": 7,
        "entrustMoneyTotal": 120000,
        "productName": "经营贷（个人）",
        "ownerName": "中信银行",
        "autoReduction": "1",
        "importStart": "1",
        "projectName": '华东地区信用卡透支不良资产九号包',
        "packageName": "招商银行4月第1季度信用不良9号包",
        "openAccount": "*************",
    },
    {
        "createBy": "admin",
        "createTime": "2025-04-22 11:35:15",
        "updateTime": "2025-04-22 11:48:17",
        "assetsStatus": "交割确认",
        "batchNum": "zsyhfoshan202502CC0006",
        "caseNumber": 1,
        "entrustMoneyTotal": 10000,
        "beginPeriod": "2025-04-09",
        "endPeriod": "2025-05-08",
        "productName": "信用卡",
        "ownerName": "招商银行佛山分行",
        "autoReduction": "1",
        "activeStep": 8,
        "importStart": "1",
        "projectName": '粤港澳大湾区跨境贸易不良债权十号包',
        "packageName": "招商银行4月第1季度信用不良10号包",
        "openAccount": "**************",
    }
])
const queryParams = ref({
    allQuery: false,
    pageNum: 1,
    pageSize: 10,
})
const total = ref(10)
const treeData = ref([
    {
        id: 1,
        label: '消费贷',
        children: [
            { id: 11, label: '沃享极速分期', },
            { id: 12, label: '智鹿秒批贷', },
            { id: 13, label: '风云循环贷', },
            { id: 14, label: '信用付 PLUS', },
            { id: 15, label: '零零花青春版', },
            { id: 16, label: '教育智分期', },
            { id: 17, label: '家装无忧贷', },
            { id: 18, label: '旅行白条', },
            { id: 19, label: '医享付', },
            { id: 111, label: '绿享贷', },
            { id: 112, label: '乐享贷' },
        ]
    },
    {
        id: 2,
        label: '信用贷',
        children: [
            { id: 20, label: '白领优享贷' },
            { id: 21, label: '业主尊享贷' },
            { id: 22, label: '新市民安居贷' },
            { id: 23, label: '银发暖心贷' },
            { id: 24, label: '创客启航贷' },
            { id: 25, label: 'AI 智能卡' },
            { id: 26, label: '数据宝贷' },
            { id: 27, label: '仲思健康贷' },
            { id: 28, label: '跨境速汇贷' },
            { id: 29, label: '社区普惠贷' },
        ]
    },
]);

function handleTabClick() {
    loading.value = true
    setTimeout(() => {
        loading.value = false
    }, 1000)
}
function handleDetails(row) {
    const query = { activeStep: row.activeStep, path: route.path }
    router.push({ path: '/transferManage/transferDetails', query })
}
</script>

<style lang="scss" scoped></style>