import request from '@/utils/request'
//预警策略--列表
export function warnStrategyEmailListApi(query) {
    return request({
        url: '/quality/email/list',
        method: 'get',
        params: query
    })
}
//预警策略--新增
export function addEmailWarnStrategyApi(data) {
    return request({
        url: '/quality/email/insert',
        method: 'post',
        data
    })
}
//预警策略--编辑
export function editEmailWarnStrategyApi(data) {
    return request({
        url: '/quality/email/edit',
        method: 'post',
        data
    })
}
