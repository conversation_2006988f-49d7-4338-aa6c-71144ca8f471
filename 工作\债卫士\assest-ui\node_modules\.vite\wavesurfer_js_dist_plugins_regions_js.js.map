{"version": 3, "sources": ["../wavesurfer.js/dist/plugins/regions.js", "dep:wavesurfer_js_dist_plugins_regions_js"], "sourcesContent": ["class t{constructor(){this.listeners={}}on(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),null==i?void 0:i.once){const i=()=>{this.un(t,i),this.un(t,e)};return this.on(t,i),i}return()=>this.un(t,e)}un(t,e){var i;null===(i=this.listeners[t])||void 0===i||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach((t=>t(...e)))}}class e extends t{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}_init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t()))}}function i(t,e,i,n,s=3,r=0,o=100){if(!t)return()=>{};const a=matchMedia(\"(pointer: coarse)\").matches;let l=()=>{};const h=h=>{if(h.button!==r)return;h.preventDefault(),h.stopPropagation();let d=h.clientX,c=h.clientY,u=!1;const v=Date.now(),g=n=>{if(n.preventDefault(),n.stopPropagation(),a&&Date.now()-v<o)return;const r=n.clientX,l=n.clientY,h=r-d,g=l-c;if(u||Math.abs(h)>s||Math.abs(g)>s){const n=t.getBoundingClientRect(),{left:s,top:o}=n;u||(null==i||i(d-s,c-o),u=!0),e(h,g,r-s,l-o),d=r,c=l}},p=e=>{if(u){const i=e.clientX,s=e.clientY,r=t.getBoundingClientRect(),{left:o,top:a}=r;null==n||n(i-o,s-a)}l()},m=t=>{t.relatedTarget&&t.relatedTarget!==document.documentElement||p(t)},f=t=>{u&&(t.stopPropagation(),t.preventDefault())},b=t=>{u&&t.preventDefault()};document.addEventListener(\"pointermove\",g),document.addEventListener(\"pointerup\",p),document.addEventListener(\"pointerout\",m),document.addEventListener(\"pointercancel\",m),document.addEventListener(\"touchmove\",b,{passive:!1}),document.addEventListener(\"click\",f,{capture:!0}),l=()=>{document.removeEventListener(\"pointermove\",g),document.removeEventListener(\"pointerup\",p),document.removeEventListener(\"pointerout\",m),document.removeEventListener(\"pointercancel\",m),document.removeEventListener(\"touchmove\",b),setTimeout((()=>{document.removeEventListener(\"click\",f,{capture:!0})}),10)}};return t.addEventListener(\"pointerdown\",h),()=>{l(),t.removeEventListener(\"pointerdown\",h)}}function n(t,e){const i=e.xmlns?document.createElementNS(e.xmlns,t):document.createElement(t);for(const[t,s]of Object.entries(e))if(\"children\"===t&&s)for(const[t,e]of Object.entries(s))e instanceof Node?i.appendChild(e):\"string\"==typeof e?i.appendChild(document.createTextNode(e)):i.appendChild(n(t,e));else\"style\"===t?Object.assign(i.style,s):\"textContent\"===t?i.textContent=s:i.setAttribute(t,s.toString());return i}function s(t,e,i){const s=n(t,e||{});return null==i||i.appendChild(s),s}class r extends t{constructor(t,e,i=0){var n,s,r,o,a,l,h,d,c,u;super(),this.totalDuration=e,this.numberOfChannels=i,this.element=null,this.minLength=0,this.maxLength=1/0,this.contentEditable=!1,this.subscriptions=[],this.isRemoved=!1,this.subscriptions=[],this.id=t.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(t.start),this.end=this.clampPosition(null!==(n=t.end)&&void 0!==n?n:t.start),this.drag=null===(s=t.drag)||void 0===s||s,this.resize=null===(r=t.resize)||void 0===r||r,this.resizeStart=null===(o=t.resizeStart)||void 0===o||o,this.resizeEnd=null===(a=t.resizeEnd)||void 0===a||a,this.color=null!==(l=t.color)&&void 0!==l?l:\"rgba(0, 0, 0, 0.1)\",this.minLength=null!==(h=t.minLength)&&void 0!==h?h:this.minLength,this.maxLength=null!==(d=t.maxLength)&&void 0!==d?d:this.maxLength,this.channelIdx=null!==(c=t.channelIdx)&&void 0!==c?c:-1,this.contentEditable=null!==(u=t.contentEditable)&&void 0!==u?u:this.contentEditable,this.element=this.initElement(),this.setContent(t.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(t){return Math.max(0,Math.min(this.totalDuration,t))}setPart(){var t;const e=this.start===this.end;null===(t=this.element)||void 0===t||t.setAttribute(\"part\",`${e?\"marker\":\"region\"} ${this.id}`)}addResizeHandles(t){const e={position:\"absolute\",zIndex:\"2\",width:\"6px\",height:\"100%\",top:\"0\",cursor:\"ew-resize\",wordBreak:\"keep-all\"},n=s(\"div\",{part:\"region-handle region-handle-left\",style:Object.assign(Object.assign({},e),{left:\"0\",borderLeft:\"2px solid rgba(0, 0, 0, 0.5)\",borderRadius:\"2px 0 0 2px\"})},t),r=s(\"div\",{part:\"region-handle region-handle-right\",style:Object.assign(Object.assign({},e),{right:\"0\",borderRight:\"2px solid rgba(0, 0, 0, 0.5)\",borderRadius:\"0 2px 2px 0\"})},t);this.subscriptions.push(i(n,(t=>this.onResize(t,\"start\")),(()=>null),(()=>this.onEndResizing()),1),i(r,(t=>this.onResize(t,\"end\")),(()=>null),(()=>this.onEndResizing()),1))}removeResizeHandles(t){const e=t.querySelector('[part*=\"region-handle-left\"]'),i=t.querySelector('[part*=\"region-handle-right\"]');e&&t.removeChild(e),i&&t.removeChild(i)}initElement(){if(this.isRemoved)return null;const t=this.start===this.end;let e=0,i=100;this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(i=100/this.numberOfChannels,e=i*this.channelIdx);const n=s(\"div\",{style:{position:\"absolute\",top:`${e}%`,height:`${i}%`,backgroundColor:t?\"none\":this.color,borderLeft:t?\"2px solid \"+this.color:\"none\",borderRadius:\"2px\",boxSizing:\"border-box\",transition:\"background-color 0.2s ease\",cursor:this.drag?\"grab\":\"default\",pointerEvents:\"all\"}});return!t&&this.resize&&this.addResizeHandles(n),n}renderPosition(){if(!this.element)return;const t=this.start/this.totalDuration,e=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*t+\"%\",this.element.style.right=100*e+\"%\"}toggleCursor(t){var e;this.drag&&(null===(e=this.element)||void 0===e?void 0:e.style)&&(this.element.style.cursor=t?\"grabbing\":\"grab\")}initMouseEvents(){const{element:t}=this;t&&(t.addEventListener(\"click\",(t=>this.emit(\"click\",t))),t.addEventListener(\"mouseenter\",(t=>this.emit(\"over\",t))),t.addEventListener(\"mouseleave\",(t=>this.emit(\"leave\",t))),t.addEventListener(\"dblclick\",(t=>this.emit(\"dblclick\",t))),t.addEventListener(\"pointerdown\",(()=>this.toggleCursor(!0))),t.addEventListener(\"pointerup\",(()=>this.toggleCursor(!1))),this.subscriptions.push(i(t,(t=>this.onMove(t)),(()=>this.toggleCursor(!0)),(()=>{this.toggleCursor(!1),this.drag&&this.emit(\"update-end\")}))),this.contentEditable&&this.content&&(this.content.addEventListener(\"click\",(t=>this.onContentClick(t))),this.content.addEventListener(\"blur\",(()=>this.onContentBlur()))))}_onUpdate(t,e){var i;if(!(null===(i=this.element)||void 0===i?void 0:i.parentElement))return;const{width:n}=this.element.parentElement.getBoundingClientRect(),s=t/n*this.totalDuration,r=e&&\"start\"!==e?this.start:this.start+s,o=e&&\"end\"!==e?this.end:this.end+s,a=o-r;r>=0&&o<=this.totalDuration&&r<=o&&a>=this.minLength&&a<=this.maxLength&&(this.start=r,this.end=o,this.renderPosition(),this.emit(\"update\",e))}onMove(t){this.drag&&this._onUpdate(t)}onResize(t,e){this.resize&&(this.resizeStart||\"start\"!==e)&&(this.resizeEnd||\"end\"!==e)&&this._onUpdate(t,e)}onEndResizing(){this.resize&&this.emit(\"update-end\")}onContentClick(t){t.stopPropagation();t.target.focus(),this.emit(\"click\",t)}onContentBlur(){this.emit(\"update-end\")}_setTotalDuration(t){this.totalDuration=t,this.renderPosition()}play(t){this.emit(\"play\",t&&this.end!==this.start?this.end:void 0)}getContent(t=!1){var e;return t?this.content||void 0:this.element instanceof HTMLElement?(null===(e=this.content)||void 0===e?void 0:e.innerHTML)||void 0:\"\"}setContent(t){var e;if(this.element)if(null===(e=this.content)||void 0===e||e.remove(),t){if(\"string\"==typeof t){const e=this.start===this.end;this.content=s(\"div\",{style:{padding:`0.2em ${e?.2:.4}em`,display:\"inline-block\"},textContent:t})}else this.content=t;this.contentEditable&&(this.content.contentEditable=\"true\"),this.content.setAttribute(\"part\",\"region-content\"),this.element.appendChild(this.content),this.emit(\"content-changed\")}else this.content=void 0}setOptions(t){var e,i;if(this.element){if(t.color&&(this.color=t.color,this.element.style.backgroundColor=this.color),void 0!==t.drag&&(this.drag=t.drag,this.element.style.cursor=this.drag?\"grab\":\"default\"),void 0!==t.start||void 0!==t.end){const n=this.start===this.end;this.start=this.clampPosition(null!==(e=t.start)&&void 0!==e?e:this.start),this.end=this.clampPosition(null!==(i=t.end)&&void 0!==i?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(t.content&&this.setContent(t.content),t.id&&(this.id=t.id,this.setPart()),void 0!==t.resize&&t.resize!==this.resize){const e=this.start===this.end;this.resize=t.resize,this.resize&&!e?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}void 0!==t.resizeStart&&(this.resizeStart=t.resizeStart),void 0!==t.resizeEnd&&(this.resizeEnd=t.resizeEnd)}}remove(){this.isRemoved=!0,this.emit(\"remove\"),this.subscriptions.forEach((t=>t())),this.element&&(this.element.remove(),this.element=null)}}class o extends e{constructor(t){super(t),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(t){return new o(t)}onInit(){if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let t=[];this.subscriptions.push(this.wavesurfer.on(\"timeupdate\",(e=>{const i=this.regions.filter((t=>t.start<=e&&(t.end===t.start?t.start+.05:t.end)>=e));i.forEach((e=>{t.includes(e)||this.emit(\"region-in\",e)})),t.forEach((t=>{i.includes(t)||this.emit(\"region-out\",t)})),t=i})))}initRegionsContainer(){return s(\"div\",{style:{position:\"absolute\",top:\"0\",left:\"0\",width:\"100%\",height:\"100%\",zIndex:\"5\",pointerEvents:\"none\"}})}getRegions(){return this.regions}avoidOverlapping(t){t.content&&setTimeout((()=>{const e=t.content,i=e.getBoundingClientRect(),n=this.regions.map((e=>{if(e===t||!e.content)return 0;const n=e.content.getBoundingClientRect();return i.left<n.left+n.width&&n.left<i.left+i.width?n.height:0})).reduce(((t,e)=>t+e),0);e.style.marginTop=`${n}px`}),10)}adjustScroll(t){var e,i;if(!t.element)return;const n=null===(i=null===(e=this.wavesurfer)||void 0===e?void 0:e.getWrapper())||void 0===i?void 0:i.parentElement;if(!n)return;const{clientWidth:s,scrollWidth:r}=n;if(r<=s)return;const o=n.getBoundingClientRect(),a=t.element.getBoundingClientRect(),l=a.left-o.left,h=a.right-o.left;l<0?n.scrollLeft+=l:h>s&&(n.scrollLeft+=h-s)}virtualAppend(t,e,i){const n=()=>{if(!this.wavesurfer)return;const n=this.wavesurfer.getWidth(),s=this.wavesurfer.getScroll(),r=e.clientWidth,o=this.wavesurfer.getDuration(),a=Math.round(t.start/o*r),l=a+(Math.round((t.end-t.start)/o*r)||1)>s&&a<s+n;l&&!i.parentElement?e.appendChild(i):!l&&i.parentElement&&i.remove()};setTimeout((()=>{if(!this.wavesurfer)return;n();const e=this.wavesurfer.on(\"scroll\",n);this.subscriptions.push(t.once(\"remove\",e),e)}),0)}saveRegion(t){if(!t.element)return;this.virtualAppend(t,this.regionsContainer,t.element),this.avoidOverlapping(t),this.regions.push(t);const e=[t.on(\"update\",(e=>{e||this.adjustScroll(t),this.emit(\"region-update\",t,e)})),t.on(\"update-end\",(()=>{this.avoidOverlapping(t),this.emit(\"region-updated\",t)})),t.on(\"play\",(e=>{var i;null===(i=this.wavesurfer)||void 0===i||i.play(t.start,e)})),t.on(\"click\",(e=>{this.emit(\"region-clicked\",t,e)})),t.on(\"dblclick\",(e=>{this.emit(\"region-double-clicked\",t,e)})),t.on(\"content-changed\",(()=>{this.emit(\"region-content-changed\",t)})),t.once(\"remove\",(()=>{e.forEach((t=>t())),this.regions=this.regions.filter((e=>e!==t)),this.emit(\"region-removed\",t)}))];this.subscriptions.push(...e),this.emit(\"region-created\",t)}addRegion(t){var e,i;if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");const n=this.wavesurfer.getDuration(),s=null===(i=null===(e=this.wavesurfer)||void 0===e?void 0:e.getDecodedData())||void 0===i?void 0:i.numberOfChannels,o=new r(t,n,s);return this.emit(\"region-initialized\",o),n?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once(\"ready\",(t=>{o._setTotalDuration(t),this.saveRegion(o)}))),o}enableDragSelection(t,e=3){var n;const s=null===(n=this.wavesurfer)||void 0===n?void 0:n.getWrapper();if(!(s&&s instanceof HTMLElement))return()=>{};let o=null,a=0;return i(s,((t,e,i)=>{o&&o._onUpdate(t,i>a?\"end\":\"start\")}),(e=>{var i,n;if(a=e,!this.wavesurfer)return;const s=this.wavesurfer.getDuration(),l=null===(n=null===(i=this.wavesurfer)||void 0===i?void 0:i.getDecodedData())||void 0===n?void 0:n.numberOfChannels,{width:h}=this.wavesurfer.getWrapper().getBoundingClientRect(),d=e/h*s,c=(e+5)/h*s;o=new r(Object.assign(Object.assign({},t),{start:d,end:c}),s,l),this.emit(\"region-initialized\",o),o.element&&this.regionsContainer.appendChild(o.element)}),(()=>{o&&(this.saveRegion(o),o=null)}),e)}clearRegions(){this.regions.slice().forEach((t=>t.remove())),this.regions=[]}destroy(){this.clearRegions(),super.destroy(),this.regionsContainer.remove()}}export{o as default};\n", "import d from \"./node_modules/wavesurfer.js/dist/plugins/regions.js\";export default d;"], "mappings": ";;;AAAA,cAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU;AAAA;AAAA,EAAG,GAAG,IAAE,IAAE,IAAE;AAAC,QAAG,KAAK,UAAU,OAAK,MAAK,UAAU,MAAG,IAAI,QAAK,KAAK,UAAU,IAAG,IAAI,KAAG,AAAM,MAAN,OAAQ,SAAO,GAAE,MAAK;AAAC,YAAM,KAAE,MAAI;AAAC,aAAK,GAAG,IAAE,KAAG,KAAK,GAAG,IAAE;AAAA;AAAI,aAAO,KAAK,GAAG,IAAE,KAAG;AAAA;AAAE,WAAM,MAAI,KAAK,GAAG,IAAE;AAAA;AAAA,EAAG,GAAG,IAAE,IAAE;AAAC,QAAI;AAAE,IAAQ,MAAE,KAAK,UAAU,SAAzB,QAA8B,AAAS,OAAT,UAAY,GAAE,OAAO;AAAA;AAAA,EAAG,KAAK,IAAE,IAAE;AAAC,WAAO,KAAK,GAAG,IAAE,IAAE,EAAC,MAAK;AAAA;AAAA,EAAK,QAAO;AAAC,SAAK,YAAU;AAAA;AAAA,EAAG,KAAK,OAAK,IAAE;AAAC,SAAK,UAAU,OAAI,KAAK,UAAU,IAAG,QAAS,QAAG,GAAE,GAAG;AAAA;AAAA;AAAM,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE;AAAC,aAAQ,KAAK,gBAAc,IAAG,KAAK,UAAQ;AAAA;AAAA,EAAE,SAAQ;AAAA;AAAA,EAAE,MAAM,IAAE;AAAC,SAAK,aAAW,IAAE,KAAK;AAAA;AAAA,EAAS,UAAS;AAAC,SAAK,KAAK,YAAW,KAAK,cAAc,QAAS,QAAG;AAAA;AAAA;AAAO,WAAW,IAAE,IAAE,IAAE,IAAE,KAAE,GAAE,KAAE,GAAE,KAAE,KAAI;AAAC,MAAG,CAAC;AAAE,WAAM,MAAI;AAAA;AAAG,QAAM,IAAE,WAAW,qBAAqB;AAAQ,MAAI,IAAE,MAAI;AAAA;AAAG,QAAM,IAAE,QAAG;AAAC,QAAG,GAAE,WAAS;AAAE;AAAO,OAAE,kBAAiB,GAAE;AAAkB,QAAI,IAAE,GAAE,SAAQ,IAAE,GAAE,SAAQ,IAAE;AAAG,UAAM,IAAE,KAAK,OAAM,IAAE,QAAG;AAAC,UAAG,GAAE,kBAAiB,GAAE,mBAAkB,KAAG,KAAK,QAAM,IAAE;AAAE;AAAO,YAAM,KAAE,GAAE,SAAQ,KAAE,GAAE,SAAQ,KAAE,KAAE,GAAE,KAAE,KAAE;AAAE,UAAG,KAAG,KAAK,IAAI,MAAG,MAAG,KAAK,IAAI,MAAG,IAAE;AAAC,cAAM,KAAE,GAAE,yBAAwB,EAAC,MAAK,IAAE,KAAI,OAAG;AAAE,aAAI,CAAM,MAAN,QAAS,GAAE,IAAE,IAAE,IAAE,KAAG,IAAE,OAAI,GAAE,IAAE,IAAE,KAAE,IAAE,KAAE,KAAG,IAAE,IAAE,IAAE;AAAA;AAAA,OAAI,IAAE,QAAG;AAAC,UAAG,GAAE;AAAC,cAAM,KAAE,GAAE,SAAQ,KAAE,GAAE,SAAQ,KAAE,GAAE,yBAAwB,EAAC,MAAK,IAAE,KAAI,OAAG;AAAE,QAAM,MAAN,QAAS,GAAE,KAAE,IAAE,KAAE;AAAA;AAAG;AAAA,OAAK,IAAE,QAAG;AAAC,SAAE,iBAAe,GAAE,kBAAgB,SAAS,mBAAiB,EAAE;AAAA,OAAI,IAAE,QAAG;AAAC,WAAI,IAAE,mBAAkB,GAAE;AAAA,OAAmB,IAAE,QAAG;AAAC,WAAG,GAAE;AAAA;AAAkB,aAAS,iBAAiB,eAAc,IAAG,SAAS,iBAAiB,aAAY,IAAG,SAAS,iBAAiB,cAAa,IAAG,SAAS,iBAAiB,iBAAgB,IAAG,SAAS,iBAAiB,aAAY,GAAE,EAAC,SAAQ,UAAK,SAAS,iBAAiB,SAAQ,GAAE,EAAC,SAAQ,SAAK,IAAE,MAAI;AAAC,eAAS,oBAAoB,eAAc,IAAG,SAAS,oBAAoB,aAAY,IAAG,SAAS,oBAAoB,cAAa,IAAG,SAAS,oBAAoB,iBAAgB,IAAG,SAAS,oBAAoB,aAAY,IAAG,WAAY,MAAI;AAAC,iBAAS,oBAAoB,SAAQ,GAAE,EAAC,SAAQ;AAAA,SAAO;AAAA;AAAA;AAAM,SAAO,GAAE,iBAAiB,eAAc,IAAG,MAAI;AAAC,SAAI,GAAE,oBAAoB,eAAc;AAAA;AAAA;AAAI,WAAW,IAAE,IAAE;AAAC,QAAM,KAAE,GAAE,QAAM,SAAS,gBAAgB,GAAE,OAAM,MAAG,SAAS,cAAc;AAAG,aAAS,CAAC,IAAE,OAAK,OAAO,QAAQ;AAAG,QAAG,AAAa,OAAb,cAAgB;AAAE,iBAAS,CAAC,IAAE,OAAK,OAAO,QAAQ;AAAG,sBAAa,OAAK,GAAE,YAAY,MAAG,AAAU,OAAO,MAAjB,WAAmB,GAAE,YAAY,SAAS,eAAe,OAAI,GAAE,YAAY,EAAE,IAAE;AAAA;AAAQ,MAAU,OAAV,UAAY,OAAO,OAAO,GAAE,OAAM,MAAG,AAAgB,OAAhB,gBAAkB,GAAE,cAAY,KAAE,GAAE,aAAa,IAAE,GAAE;AAAY,SAAO;AAAA;AAAE,WAAW,IAAE,IAAE,IAAE;AAAC,QAAM,KAAE,EAAE,IAAE,MAAG;AAAI,SAAO,AAAM,MAAN,QAAS,GAAE,YAAY,KAAG;AAAA;AAAE,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE,IAAE,KAAE,GAAE;AAAC,QAAI,IAAE,IAAE,IAAE,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAQ,KAAK,gBAAc,IAAE,KAAK,mBAAiB,IAAE,KAAK,UAAQ,MAAK,KAAK,YAAU,GAAE,KAAK,YAAU,IAAE,GAAE,KAAK,kBAAgB,OAAG,KAAK,gBAAc,IAAG,KAAK,YAAU,OAAG,KAAK,gBAAc,IAAG,KAAK,KAAG,GAAE,MAAI,UAAU,KAAK,SAAS,SAAS,IAAI,MAAM,MAAK,KAAK,QAAM,KAAK,cAAc,GAAE,QAAO,KAAK,MAAI,KAAK,cAAc,AAAQ,MAAE,GAAE,SAAZ,QAAkB,AAAS,OAAT,SAAW,KAAE,GAAE,QAAO,KAAK,OAAK,AAAQ,MAAE,GAAE,UAAZ,QAAmB,AAAS,OAAT,UAAY,IAAE,KAAK,SAAO,AAAQ,MAAE,GAAE,YAAZ,QAAqB,AAAS,OAAT,UAAY,IAAE,KAAK,cAAY,AAAQ,MAAE,GAAE,iBAAZ,QAA0B,AAAS,OAAT,UAAY,IAAE,KAAK,YAAU,AAAQ,KAAE,GAAE,eAAZ,QAAwB,AAAS,MAAT,UAAY,GAAE,KAAK,QAAM,AAAQ,KAAE,GAAE,WAAZ,QAAoB,AAAS,MAAT,SAAW,IAAE,sBAAqB,KAAK,YAAU,AAAQ,KAAE,GAAE,eAAZ,QAAwB,AAAS,MAAT,SAAW,IAAE,KAAK,WAAU,KAAK,YAAU,AAAQ,KAAE,GAAE,eAAZ,QAAwB,AAAS,MAAT,SAAW,IAAE,KAAK,WAAU,KAAK,aAAW,AAAQ,KAAE,GAAE,gBAAZ,QAAyB,AAAS,MAAT,SAAW,IAAE,IAAG,KAAK,kBAAgB,AAAQ,KAAE,GAAE,qBAAZ,QAA8B,AAAS,MAAT,SAAW,IAAE,KAAK,iBAAgB,KAAK,UAAQ,KAAK,eAAc,KAAK,WAAW,GAAE,UAAS,KAAK,WAAU,KAAK,kBAAiB,KAAK;AAAA;AAAA,EAAkB,cAAc,IAAE;AAAC,WAAO,KAAK,IAAI,GAAE,KAAK,IAAI,KAAK,eAAc;AAAA;AAAA,EAAI,UAAS;AAAC,QAAI;AAAE,UAAM,KAAE,KAAK,UAAQ,KAAK;AAAI,IAAQ,MAAE,KAAK,aAAf,QAAyB,AAAS,OAAT,UAAY,GAAE,aAAa,QAAO,GAAG,KAAE,WAAS,YAAY,KAAK;AAAA;AAAA,EAAM,iBAAiB,IAAE;AAAC,UAAM,KAAE,EAAC,UAAS,YAAW,QAAO,KAAI,OAAM,OAAM,QAAO,QAAO,KAAI,KAAI,QAAO,aAAY,WAAU,cAAY,KAAE,EAAE,OAAM,EAAC,MAAK,oCAAmC,OAAM,OAAO,OAAO,OAAO,OAAO,IAAG,KAAG,EAAC,MAAK,KAAI,YAAW,gCAA+B,cAAa,oBAAiB,KAAG,KAAE,EAAE,OAAM,EAAC,MAAK,qCAAoC,OAAM,OAAO,OAAO,OAAO,OAAO,IAAG,KAAG,EAAC,OAAM,KAAI,aAAY,gCAA+B,cAAa,oBAAiB;AAAG,SAAK,cAAc,KAAK,EAAE,IAAG,QAAG,KAAK,SAAS,IAAE,UAAW,MAAI,MAAO,MAAI,KAAK,iBAAiB,IAAG,EAAE,IAAG,QAAG,KAAK,SAAS,IAAE,QAAS,MAAI,MAAO,MAAI,KAAK,iBAAiB;AAAA;AAAA,EAAI,oBAAoB,IAAE;AAAC,UAAM,KAAE,GAAE,cAAc,iCAAgC,KAAE,GAAE,cAAc;AAAiC,UAAG,GAAE,YAAY,KAAG,MAAG,GAAE,YAAY;AAAA;AAAA,EAAG,cAAa;AAAC,QAAG,KAAK;AAAU,aAAO;AAAK,UAAM,KAAE,KAAK,UAAQ,KAAK;AAAI,QAAI,KAAE,GAAE,KAAE;AAAI,SAAK,cAAY,KAAG,KAAK,aAAW,KAAK,oBAAmB,MAAE,MAAI,KAAK,kBAAiB,KAAE,KAAE,KAAK;AAAY,UAAM,KAAE,EAAE,OAAM,EAAC,OAAM,EAAC,UAAS,YAAW,KAAI,GAAG,OAAK,QAAO,GAAG,OAAK,iBAAgB,KAAE,SAAO,KAAK,OAAM,YAAW,KAAE,eAAa,KAAK,QAAM,QAAO,cAAa,OAAM,WAAU,cAAa,YAAW,8BAA6B,QAAO,KAAK,OAAK,SAAO,WAAU,eAAc;AAAS,WAAM,CAAC,MAAG,KAAK,UAAQ,KAAK,iBAAiB,KAAG;AAAA;AAAA,EAAE,iBAAgB;AAAC,QAAG,CAAC,KAAK;AAAQ;AAAO,UAAM,KAAE,KAAK,QAAM,KAAK,eAAc,KAAG,MAAK,gBAAc,KAAK,OAAK,KAAK;AAAc,SAAK,QAAQ,MAAM,OAAK,MAAI,KAAE,KAAI,KAAK,QAAQ,MAAM,QAAM,MAAI,KAAE;AAAA;AAAA,EAAI,aAAa,IAAE;AAAC,QAAI;AAAE,SAAK,QAAO,CAAQ,MAAE,KAAK,aAAf,QAAyB,AAAS,OAAT,SAAW,SAAO,GAAE,UAAS,MAAK,QAAQ,MAAM,SAAO,KAAE,aAAW;AAAA;AAAA,EAAQ,kBAAiB;AAAC,UAAK,EAAC,SAAQ,OAAG;AAAK,UAAI,IAAE,iBAAiB,SAAS,QAAG,KAAK,KAAK,SAAQ,MAAK,GAAE,iBAAiB,cAAc,QAAG,KAAK,KAAK,QAAO,MAAK,GAAE,iBAAiB,cAAc,QAAG,KAAK,KAAK,SAAQ,MAAK,GAAE,iBAAiB,YAAY,QAAG,KAAK,KAAK,YAAW,MAAK,GAAE,iBAAiB,eAAe,MAAI,KAAK,aAAa,QAAM,GAAE,iBAAiB,aAAa,MAAI,KAAK,aAAa,SAAM,KAAK,cAAc,KAAK,EAAE,IAAG,QAAG,KAAK,OAAO,KAAK,MAAI,KAAK,aAAa,OAAM,MAAI;AAAC,WAAK,aAAa,QAAI,KAAK,QAAM,KAAK,KAAK;AAAA,SAAkB,KAAK,mBAAiB,KAAK,WAAU,MAAK,QAAQ,iBAAiB,SAAS,QAAG,KAAK,eAAe,MAAK,KAAK,QAAQ,iBAAiB,QAAQ,MAAI,KAAK;AAAA;AAAA,EAAoB,UAAU,IAAE,IAAE;AAAC,QAAI;AAAE,QAAG,CAAE,CAAQ,MAAE,KAAK,aAAf,QAAyB,AAAS,OAAT,SAAW,SAAO,GAAE;AAAe;AAAO,UAAK,EAAC,OAAM,OAAG,KAAK,QAAQ,cAAc,yBAAwB,KAAE,KAAE,KAAE,KAAK,eAAc,KAAE,MAAG,AAAU,OAAV,UAAY,KAAK,QAAM,KAAK,QAAM,IAAE,KAAE,MAAG,AAAQ,OAAR,QAAU,KAAK,MAAI,KAAK,MAAI,IAAE,IAAE,KAAE;AAAE,UAAG,KAAG,MAAG,KAAK,iBAAe,MAAG,MAAG,KAAG,KAAK,aAAW,KAAG,KAAK,aAAY,MAAK,QAAM,IAAE,KAAK,MAAI,IAAE,KAAK,kBAAiB,KAAK,KAAK,UAAS;AAAA;AAAA,EAAI,OAAO,IAAE;AAAC,SAAK,QAAM,KAAK,UAAU;AAAA;AAAA,EAAG,SAAS,IAAE,IAAE;AAAC,SAAK,UAAS,MAAK,eAAa,AAAU,OAAV,YAAe,MAAK,aAAW,AAAQ,OAAR,UAAY,KAAK,UAAU,IAAE;AAAA;AAAA,EAAG,gBAAe;AAAC,SAAK,UAAQ,KAAK,KAAK;AAAA;AAAA,EAAc,eAAe,IAAE;AAAC,OAAE;AAAkB,OAAE,OAAO,SAAQ,KAAK,KAAK,SAAQ;AAAA;AAAA,EAAG,gBAAe;AAAC,SAAK,KAAK;AAAA;AAAA,EAAc,kBAAkB,IAAE;AAAC,SAAK,gBAAc,IAAE,KAAK;AAAA;AAAA,EAAiB,KAAK,IAAE;AAAC,SAAK,KAAK,QAAO,MAAG,KAAK,QAAM,KAAK,QAAM,KAAK,MAAI;AAAA;AAAA,EAAQ,WAAW,KAAE,OAAG;AAAC,QAAI;AAAE,WAAO,KAAE,KAAK,WAAS,SAAO,KAAK,mBAAmB,cAAa,CAAQ,MAAE,KAAK,aAAf,QAAyB,AAAS,OAAT,SAAW,SAAO,GAAE,cAAY,SAAO;AAAA;AAAA,EAAG,WAAW,IAAE;AAAC,QAAI;AAAE,QAAG,KAAK;AAAQ,UAAG,AAAQ,MAAE,KAAK,aAAf,QAAyB,AAAS,OAAT,UAAY,GAAE,UAAS,IAAE;AAAC,YAAG,AAAU,OAAO,MAAjB,UAAmB;AAAC,gBAAM,KAAE,KAAK,UAAQ,KAAK;AAAI,eAAK,UAAQ,EAAE,OAAM,EAAC,OAAM,EAAC,SAAQ,SAAS,KAAE,MAAG,SAAO,SAAQ,kBAAgB,aAAY;AAAA;AAAS,eAAK,UAAQ;AAAE,aAAK,mBAAkB,MAAK,QAAQ,kBAAgB,SAAQ,KAAK,QAAQ,aAAa,QAAO,mBAAkB,KAAK,QAAQ,YAAY,KAAK,UAAS,KAAK,KAAK;AAAA;AAAwB,aAAK,UAAQ;AAAA;AAAA,EAAO,WAAW,IAAE;AAAC,QAAI,IAAE;AAAE,QAAG,KAAK,SAAQ;AAAC,UAAG,GAAE,SAAQ,MAAK,QAAM,GAAE,OAAM,KAAK,QAAQ,MAAM,kBAAgB,KAAK,QAAO,AAAS,GAAE,SAAX,UAAkB,MAAK,OAAK,GAAE,MAAK,KAAK,QAAQ,MAAM,SAAO,KAAK,OAAK,SAAO,YAAW,AAAS,GAAE,UAAX,UAAkB,AAAS,GAAE,QAAX,QAAe;AAAC,cAAM,KAAE,KAAK,UAAQ,KAAK;AAAI,aAAK,QAAM,KAAK,cAAc,AAAQ,MAAE,GAAE,WAAZ,QAAoB,AAAS,OAAT,SAAW,KAAE,KAAK,QAAO,KAAK,MAAI,KAAK,cAAc,AAAQ,MAAE,GAAE,SAAZ,QAAkB,AAAS,OAAT,SAAW,KAAE,KAAE,KAAK,QAAM,KAAK,MAAK,KAAK,kBAAiB,KAAK;AAAA;AAAU,UAAG,GAAE,WAAS,KAAK,WAAW,GAAE,UAAS,GAAE,MAAK,MAAK,KAAG,GAAE,IAAG,KAAK,YAAW,AAAS,GAAE,WAAX,UAAmB,GAAE,WAAS,KAAK,QAAO;AAAC,cAAM,KAAE,KAAK,UAAQ,KAAK;AAAI,aAAK,SAAO,GAAE,QAAO,KAAK,UAAQ,CAAC,KAAE,KAAK,iBAAiB,KAAK,WAAS,KAAK,oBAAoB,KAAK;AAAA;AAAS,MAAS,GAAE,gBAAX,UAAyB,MAAK,cAAY,GAAE,cAAa,AAAS,GAAE,cAAX,UAAuB,MAAK,YAAU,GAAE;AAAA;AAAA;AAAA,EAAY,SAAQ;AAAC,SAAK,YAAU,MAAG,KAAK,KAAK,WAAU,KAAK,cAAc,QAAS,QAAG,OAAM,KAAK,WAAU,MAAK,QAAQ,UAAS,KAAK,UAAQ;AAAA;AAAA;AAAO,sBAAgB,EAAC;AAAA,EAAC,YAAY,IAAE;AAAC,UAAM,KAAG,KAAK,UAAQ,IAAG,KAAK,mBAAiB,KAAK;AAAA;AAAA,SAA8B,OAAO,IAAE;AAAC,WAAO,IAAI,EAAE;AAAA;AAAA,EAAG,SAAQ;AAAC,QAAG,CAAC,KAAK;AAAW,YAAM,MAAM;AAAiC,SAAK,WAAW,aAAa,YAAY,KAAK;AAAkB,QAAI,KAAE;AAAG,SAAK,cAAc,KAAK,KAAK,WAAW,GAAG,cAAc,QAAG;AAAC,YAAM,KAAE,KAAK,QAAQ,OAAQ,QAAG,GAAE,SAAO,MAAI,IAAE,QAAM,GAAE,QAAM,GAAE,QAAM,OAAI,GAAE,QAAM;AAAI,SAAE,QAAS,QAAG;AAAC,WAAE,SAAS,OAAI,KAAK,KAAK,aAAY;AAAA,UAAM,GAAE,QAAS,QAAG;AAAC,WAAE,SAAS,OAAI,KAAK,KAAK,cAAa;AAAA,UAAM,KAAE;AAAA;AAAA;AAAA,EAAM,uBAAsB;AAAC,WAAO,EAAE,OAAM,EAAC,OAAM,EAAC,UAAS,YAAW,KAAI,KAAI,MAAK,KAAI,OAAM,QAAO,QAAO,QAAO,QAAO,KAAI,eAAc;AAAA;AAAA,EAAU,aAAY;AAAC,WAAO,KAAK;AAAA;AAAA,EAAQ,iBAAiB,IAAE;AAAC,OAAE,WAAS,WAAY,MAAI;AAAC,YAAM,KAAE,GAAE,SAAQ,KAAE,GAAE,yBAAwB,KAAE,KAAK,QAAQ,IAAK,QAAG;AAAC,YAAG,OAAI,MAAG,CAAC,GAAE;AAAQ,iBAAO;AAAE,cAAM,KAAE,GAAE,QAAQ;AAAwB,eAAO,GAAE,OAAK,GAAE,OAAK,GAAE,SAAO,GAAE,OAAK,GAAE,OAAK,GAAE,QAAM,GAAE,SAAO;AAAA,SAAK,OAAQ,CAAC,IAAE,OAAI,KAAE,IAAG;AAAG,SAAE,MAAM,YAAU,GAAG;AAAA,OAAQ;AAAA;AAAA,EAAI,aAAa,IAAE;AAAC,QAAI,IAAE;AAAE,QAAG,CAAC,GAAE;AAAQ;AAAO,UAAM,KAAE,AAAQ,MAAE,AAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,SAAW,SAAO,GAAE,kBAA1D,QAAyE,AAAS,OAAT,SAAW,SAAO,GAAE;AAAc,QAAG,CAAC;AAAE;AAAO,UAAK,EAAC,aAAY,IAAE,aAAY,OAAG;AAAE,QAAG,MAAG;AAAE;AAAO,UAAM,KAAE,GAAE,yBAAwB,IAAE,GAAE,QAAQ,yBAAwB,IAAE,EAAE,OAAK,GAAE,MAAK,IAAE,EAAE,QAAM,GAAE;AAAK,QAAE,IAAE,GAAE,cAAY,IAAE,IAAE,MAAI,IAAE,cAAY,IAAE;AAAA;AAAA,EAAG,cAAc,IAAE,IAAE,IAAE;AAAC,UAAM,KAAE,MAAI;AAAC,UAAG,CAAC,KAAK;AAAW;AAAO,YAAM,KAAE,KAAK,WAAW,YAAW,KAAE,KAAK,WAAW,aAAY,KAAE,GAAE,aAAY,KAAE,KAAK,WAAW,eAAc,IAAE,KAAK,MAAM,GAAE,QAAM,KAAE,KAAG,IAAE,IAAG,MAAK,MAAO,IAAE,MAAI,GAAE,SAAO,KAAE,OAAI,KAAG,MAAG,IAAE,KAAE;AAAE,WAAG,CAAC,GAAE,gBAAc,GAAE,YAAY,MAAG,CAAC,KAAG,GAAE,iBAAe,GAAE;AAAA;AAAU,eAAY,MAAI;AAAC,UAAG,CAAC,KAAK;AAAW;AAAO;AAAI,YAAM,KAAE,KAAK,WAAW,GAAG,UAAS;AAAG,WAAK,cAAc,KAAK,GAAE,KAAK,UAAS,KAAG;AAAA,OAAK;AAAA;AAAA,EAAG,WAAW,IAAE;AAAC,QAAG,CAAC,GAAE;AAAQ;AAAO,SAAK,cAAc,IAAE,KAAK,kBAAiB,GAAE,UAAS,KAAK,iBAAiB,KAAG,KAAK,QAAQ,KAAK;AAAG,UAAM,KAAE,CAAC,GAAE,GAAG,UAAU,QAAG;AAAC,YAAG,KAAK,aAAa,KAAG,KAAK,KAAK,iBAAgB,IAAE;AAAA,QAAM,GAAE,GAAG,cAAc,MAAI;AAAC,WAAK,iBAAiB,KAAG,KAAK,KAAK,kBAAiB;AAAA,QAAM,GAAE,GAAG,QAAQ,QAAG;AAAC,UAAI;AAAE,MAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,UAAY,GAAE,KAAK,GAAE,OAAM;AAAA,QAAM,GAAE,GAAG,SAAS,QAAG;AAAC,WAAK,KAAK,kBAAiB,IAAE;AAAA,QAAM,GAAE,GAAG,YAAY,QAAG;AAAC,WAAK,KAAK,yBAAwB,IAAE;AAAA,QAAM,GAAE,GAAG,mBAAmB,MAAI;AAAC,WAAK,KAAK,0BAAyB;AAAA,QAAM,GAAE,KAAK,UAAU,MAAI;AAAC,SAAE,QAAS,QAAG,OAAM,KAAK,UAAQ,KAAK,QAAQ,OAAQ,QAAG,OAAI,KAAI,KAAK,KAAK,kBAAiB;AAAA;AAAO,SAAK,cAAc,KAAK,GAAG,KAAG,KAAK,KAAK,kBAAiB;AAAA;AAAA,EAAG,UAAU,IAAE;AAAC,QAAI,IAAE;AAAE,QAAG,CAAC,KAAK;AAAW,YAAM,MAAM;AAAiC,UAAM,KAAE,KAAK,WAAW,eAAc,KAAE,AAAQ,MAAE,AAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,SAAW,SAAO,GAAE,sBAA1D,QAA6E,AAAS,OAAT,SAAW,SAAO,GAAE,kBAAiB,KAAE,IAAI,EAAE,IAAE,IAAE;AAAG,WAAO,KAAK,KAAK,sBAAqB,KAAG,KAAE,KAAK,WAAW,MAAG,KAAK,cAAc,KAAK,KAAK,WAAW,KAAK,SAAS,QAAG;AAAC,SAAE,kBAAkB,KAAG,KAAK,WAAW;AAAA,SAAO;AAAA;AAAA,EAAE,oBAAoB,IAAE,KAAE,GAAE;AAAC,QAAI;AAAE,UAAM,KAAE,AAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,SAAW,SAAO,GAAE;AAAa,QAAG,CAAE,OAAG,cAAa;AAAa,aAAM,MAAI;AAAA;AAAG,QAAI,KAAE,MAAK,IAAE;AAAE,WAAO,EAAE,IAAG,CAAC,IAAE,IAAE,OAAI;AAAC,YAAG,GAAE,UAAU,IAAE,KAAE,IAAE,QAAM;AAAA,OAAY,QAAG;AAAC,UAAI,IAAE;AAAE,UAAG,IAAE,IAAE,CAAC,KAAK;AAAW;AAAO,YAAM,KAAE,KAAK,WAAW,eAAc,IAAE,AAAQ,MAAE,AAAQ,MAAE,KAAK,gBAAf,QAA4B,AAAS,OAAT,SAAW,SAAO,GAAE,sBAA1D,QAA6E,AAAS,OAAT,SAAW,SAAO,GAAE,kBAAiB,EAAC,OAAM,MAAG,KAAK,WAAW,aAAa,yBAAwB,IAAE,KAAE,IAAE,IAAE,IAAG,MAAE,KAAG,IAAE;AAAE,WAAE,IAAI,EAAE,OAAO,OAAO,OAAO,OAAO,IAAG,KAAG,EAAC,OAAM,GAAE,KAAI,MAAI,IAAE,IAAG,KAAK,KAAK,sBAAqB,KAAG,GAAE,WAAS,KAAK,iBAAiB,YAAY,GAAE;AAAA,OAAY,MAAI;AAAC,YAAI,MAAK,WAAW,KAAG,KAAE;AAAA,OAAQ;AAAA;AAAA,EAAG,eAAc;AAAC,SAAK,QAAQ,QAAQ,QAAS,QAAG,GAAE,WAAW,KAAK,UAAQ;AAAA;AAAA,EAAG,UAAS;AAAC,SAAK,gBAAe,MAAM,WAAU,KAAK,iBAAiB;AAAA;AAAA;;;ACA1xY,IAAO,gDAAQ;", "names": []}