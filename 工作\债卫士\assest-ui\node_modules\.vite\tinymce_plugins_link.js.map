{"version": 3, "sources": ["../tinymce/plugins/link/plugin.js", "../tinymce/plugins/link/index.js", "dep:tinymce_plugins_link"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$7 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var eq = function (t) {\n      return function (a) {\n        return t === a;\n      };\n    };\n    var isString = isType('string');\n    var isArray = isType('array');\n    var isNull = eq(null);\n    var isBoolean = isSimpleType('boolean');\n    var isFunction = isSimpleType('function');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var tripleEquals = function (a, b) {\n      return a === b;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var nativeIndexOf = Array.prototype.indexOf;\n    var nativePush = Array.prototype.push;\n    var rawIndexOf = function (ts, t) {\n      return nativeIndexOf.call(ts, t);\n    };\n    var contains = function (xs, x) {\n      return rawIndexOf(xs, x) > -1;\n    };\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each$1 = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var foldl = function (xs, f, acc) {\n      each$1(xs, function (x, i) {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    var flatten = function (xs) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    var bind = function (xs, f) {\n      return flatten(map(xs, f));\n    };\n    var findMap = function (arr, f) {\n      for (var i = 0; i < arr.length; i++) {\n        var r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    var is = function (lhs, rhs, comparator) {\n      if (comparator === void 0) {\n        comparator = tripleEquals;\n      }\n      return lhs.exists(function (left) {\n        return comparator(left, rhs);\n      });\n    };\n    var cat = function (arr) {\n      var r = [];\n      var push = function (x) {\n        r.push(x);\n      };\n      for (var i = 0; i < arr.length; i++) {\n        arr[i].each(push);\n      }\n      return r;\n    };\n    var someIf = function (b, a) {\n      return b ? Optional.some(a) : Optional.none();\n    };\n\n    var assumeExternalTargets = function (editor) {\n      var externalTargets = editor.getParam('link_assume_external_targets', false);\n      if (isBoolean(externalTargets) && externalTargets) {\n        return 1;\n      } else if (isString(externalTargets) && (externalTargets === 'http' || externalTargets === 'https')) {\n        return externalTargets;\n      }\n      return 0;\n    };\n    var hasContextToolbar = function (editor) {\n      return editor.getParam('link_context_toolbar', false, 'boolean');\n    };\n    var getLinkList = function (editor) {\n      return editor.getParam('link_list');\n    };\n    var getDefaultLinkTarget = function (editor) {\n      return editor.getParam('default_link_target');\n    };\n    var getTargetList = function (editor) {\n      return editor.getParam('target_list', true);\n    };\n    var getRelList = function (editor) {\n      return editor.getParam('rel_list', [], 'array');\n    };\n    var getLinkClassList = function (editor) {\n      return editor.getParam('link_class_list', [], 'array');\n    };\n    var shouldShowLinkTitle = function (editor) {\n      return editor.getParam('link_title', true, 'boolean');\n    };\n    var allowUnsafeLinkTarget = function (editor) {\n      return editor.getParam('allow_unsafe_link_target', false, 'boolean');\n    };\n    var useQuickLink = function (editor) {\n      return editor.getParam('link_quicklink', false, 'boolean');\n    };\n    var getDefaultLinkProtocol = function (editor) {\n      return editor.getParam('link_default_protocol', 'http', 'string');\n    };\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var getValue = function (item) {\n      return isString(item.value) ? item.value : '';\n    };\n    var getText = function (item) {\n      if (isString(item.text)) {\n        return item.text;\n      } else if (isString(item.title)) {\n        return item.title;\n      } else {\n        return '';\n      }\n    };\n    var sanitizeList = function (list, extractValue) {\n      var out = [];\n      global$5.each(list, function (item) {\n        var text = getText(item);\n        if (item.menu !== undefined) {\n          var items = sanitizeList(item.menu, extractValue);\n          out.push({\n            text: text,\n            items: items\n          });\n        } else {\n          var value = extractValue(item);\n          out.push({\n            text: text,\n            value: value\n          });\n        }\n      });\n      return out;\n    };\n    var sanitizeWith = function (extracter) {\n      if (extracter === void 0) {\n        extracter = getValue;\n      }\n      return function (list) {\n        return Optional.from(list).map(function (list) {\n          return sanitizeList(list, extracter);\n        });\n      };\n    };\n    var sanitize = function (list) {\n      return sanitizeWith(getValue)(list);\n    };\n    var createUi = function (name, label) {\n      return function (items) {\n        return {\n          name: name,\n          type: 'listbox',\n          label: label,\n          items: items\n        };\n      };\n    };\n    var ListOptions = {\n      sanitize: sanitize,\n      sanitizeWith: sanitizeWith,\n      createUi: createUi,\n      getValue: getValue\n    };\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var keys = Object.keys;\n    var hasOwnProperty = Object.hasOwnProperty;\n    var each = function (obj, f) {\n      var props = keys(obj);\n      for (var k = 0, len = props.length; k < len; k++) {\n        var i = props[k];\n        var x = obj[i];\n        f(x, i);\n      }\n    };\n    var objAcc = function (r) {\n      return function (x, i) {\n        r[i] = x;\n      };\n    };\n    var internalFilter = function (obj, pred, onTrue, onFalse) {\n      var r = {};\n      each(obj, function (x, i) {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n      return r;\n    };\n    var filter = function (obj, pred) {\n      var t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n    var hasNonNullableKey = function (obj, key) {\n      return has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n    };\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.util.URI');\n\n    var isAnchor = function (elm) {\n      return elm && elm.nodeName.toLowerCase() === 'a';\n    };\n    var isLink = function (elm) {\n      return isAnchor(elm) && !!getHref(elm);\n    };\n    var collectNodesInRange = function (rng, predicate) {\n      if (rng.collapsed) {\n        return [];\n      } else {\n        var contents = rng.cloneContents();\n        var walker = new global$4(contents.firstChild, contents);\n        var elements = [];\n        var current = contents.firstChild;\n        do {\n          if (predicate(current)) {\n            elements.push(current);\n          }\n        } while (current = walker.next());\n        return elements;\n      }\n    };\n    var hasProtocol = function (url) {\n      return /^\\w+:/i.test(url);\n    };\n    var getHref = function (elm) {\n      var href = elm.getAttribute('data-mce-href');\n      return href ? href : elm.getAttribute('href');\n    };\n    var applyRelTargetRules = function (rel, isUnsafe) {\n      var rules = ['noopener'];\n      var rels = rel ? rel.split(/\\s+/) : [];\n      var toString = function (rels) {\n        return global$5.trim(rels.sort().join(' '));\n      };\n      var addTargetRules = function (rels) {\n        rels = removeTargetRules(rels);\n        return rels.length > 0 ? rels.concat(rules) : rules;\n      };\n      var removeTargetRules = function (rels) {\n        return rels.filter(function (val) {\n          return global$5.inArray(rules, val) === -1;\n        });\n      };\n      var newRels = isUnsafe ? addTargetRules(rels) : removeTargetRules(rels);\n      return newRels.length > 0 ? toString(newRels) : '';\n    };\n    var trimCaretContainers = function (text) {\n      return text.replace(/\\uFEFF/g, '');\n    };\n    var getAnchorElement = function (editor, selectedElm) {\n      selectedElm = selectedElm || editor.selection.getNode();\n      if (isImageFigure(selectedElm)) {\n        return editor.dom.select('a[href]', selectedElm)[0];\n      } else {\n        return editor.dom.getParent(selectedElm, 'a[href]');\n      }\n    };\n    var getAnchorText = function (selection, anchorElm) {\n      var text = anchorElm ? anchorElm.innerText || anchorElm.textContent : selection.getContent({ format: 'text' });\n      return trimCaretContainers(text);\n    };\n    var hasLinks = function (elements) {\n      return global$5.grep(elements, isLink).length > 0;\n    };\n    var hasLinksInSelection = function (rng) {\n      return collectNodesInRange(rng, isLink).length > 0;\n    };\n    var isOnlyTextSelected = function (editor) {\n      var inlineTextElements = editor.schema.getTextInlineElements();\n      var isElement = function (elm) {\n        return elm.nodeType === 1 && !isAnchor(elm) && !has(inlineTextElements, elm.nodeName.toLowerCase());\n      };\n      var elements = collectNodesInRange(editor.selection.getRng(), isElement);\n      return elements.length === 0;\n    };\n    var isImageFigure = function (elm) {\n      return elm && elm.nodeName === 'FIGURE' && /\\bimage\\b/i.test(elm.className);\n    };\n    var getLinkAttrs = function (data) {\n      var attrs = [\n        'title',\n        'rel',\n        'class',\n        'target'\n      ];\n      return foldl(attrs, function (acc, key) {\n        data[key].each(function (value) {\n          acc[key] = value.length > 0 ? value : null;\n        });\n        return acc;\n      }, { href: data.href });\n    };\n    var handleExternalTargets = function (href, assumeExternalTargets) {\n      if ((assumeExternalTargets === 'http' || assumeExternalTargets === 'https') && !hasProtocol(href)) {\n        return assumeExternalTargets + '://' + href;\n      }\n      return href;\n    };\n    var applyLinkOverrides = function (editor, linkAttrs) {\n      var newLinkAttrs = __assign({}, linkAttrs);\n      if (!(getRelList(editor).length > 0) && allowUnsafeLinkTarget(editor) === false) {\n        var newRel = applyRelTargetRules(newLinkAttrs.rel, newLinkAttrs.target === '_blank');\n        newLinkAttrs.rel = newRel ? newRel : null;\n      }\n      if (Optional.from(newLinkAttrs.target).isNone() && getTargetList(editor) === false) {\n        newLinkAttrs.target = getDefaultLinkTarget(editor);\n      }\n      newLinkAttrs.href = handleExternalTargets(newLinkAttrs.href, assumeExternalTargets(editor));\n      return newLinkAttrs;\n    };\n    var updateLink = function (editor, anchorElm, text, linkAttrs) {\n      text.each(function (text) {\n        if (has(anchorElm, 'innerText')) {\n          anchorElm.innerText = text;\n        } else {\n          anchorElm.textContent = text;\n        }\n      });\n      editor.dom.setAttribs(anchorElm, linkAttrs);\n      editor.selection.select(anchorElm);\n    };\n    var createLink = function (editor, selectedElm, text, linkAttrs) {\n      if (isImageFigure(selectedElm)) {\n        linkImageFigure(editor, selectedElm, linkAttrs);\n      } else {\n        text.fold(function () {\n          editor.execCommand('mceInsertLink', false, linkAttrs);\n        }, function (text) {\n          editor.insertContent(editor.dom.createHTML('a', linkAttrs, editor.dom.encode(text)));\n        });\n      }\n    };\n    var linkDomMutation = function (editor, attachState, data) {\n      var selectedElm = editor.selection.getNode();\n      var anchorElm = getAnchorElement(editor, selectedElm);\n      var linkAttrs = applyLinkOverrides(editor, getLinkAttrs(data));\n      editor.undoManager.transact(function () {\n        if (data.href === attachState.href) {\n          attachState.attach();\n        }\n        if (anchorElm) {\n          editor.focus();\n          updateLink(editor, anchorElm, data.text, linkAttrs);\n        } else {\n          createLink(editor, selectedElm, data.text, linkAttrs);\n        }\n      });\n    };\n    var unlinkSelection = function (editor) {\n      var dom = editor.dom, selection = editor.selection;\n      var bookmark = selection.getBookmark();\n      var rng = selection.getRng().cloneRange();\n      var startAnchorElm = dom.getParent(rng.startContainer, 'a[href]', editor.getBody());\n      var endAnchorElm = dom.getParent(rng.endContainer, 'a[href]', editor.getBody());\n      if (startAnchorElm) {\n        rng.setStartBefore(startAnchorElm);\n      }\n      if (endAnchorElm) {\n        rng.setEndAfter(endAnchorElm);\n      }\n      selection.setRng(rng);\n      editor.execCommand('unlink');\n      selection.moveToBookmark(bookmark);\n    };\n    var unlinkDomMutation = function (editor) {\n      editor.undoManager.transact(function () {\n        var node = editor.selection.getNode();\n        if (isImageFigure(node)) {\n          unlinkImageFigure(editor, node);\n        } else {\n          unlinkSelection(editor);\n        }\n        editor.focus();\n      });\n    };\n    var unwrapOptions = function (data) {\n      var cls = data.class, href = data.href, rel = data.rel, target = data.target, text = data.text, title = data.title;\n      return filter({\n        class: cls.getOrNull(),\n        href: href,\n        rel: rel.getOrNull(),\n        target: target.getOrNull(),\n        text: text.getOrNull(),\n        title: title.getOrNull()\n      }, function (v, _k) {\n        return isNull(v) === false;\n      });\n    };\n    var sanitizeData = function (editor, data) {\n      var href = data.href;\n      return __assign(__assign({}, data), { href: global$3.isDomSafe(href, 'a', editor.settings) ? href : '' });\n    };\n    var link = function (editor, attachState, data) {\n      var sanitizedData = sanitizeData(editor, data);\n      editor.hasPlugin('rtc', true) ? editor.execCommand('createlink', false, unwrapOptions(sanitizedData)) : linkDomMutation(editor, attachState, sanitizedData);\n    };\n    var unlink = function (editor) {\n      editor.hasPlugin('rtc', true) ? editor.execCommand('unlink') : unlinkDomMutation(editor);\n    };\n    var unlinkImageFigure = function (editor, fig) {\n      var img = editor.dom.select('img', fig)[0];\n      if (img) {\n        var a = editor.dom.getParents(img, 'a[href]', fig)[0];\n        if (a) {\n          a.parentNode.insertBefore(img, a);\n          editor.dom.remove(a);\n        }\n      }\n    };\n    var linkImageFigure = function (editor, fig, attrs) {\n      var img = editor.dom.select('img', fig)[0];\n      if (img) {\n        var a = editor.dom.create('a', attrs);\n        img.parentNode.insertBefore(a, img);\n        a.appendChild(img);\n      }\n    };\n\n    var isListGroup = function (item) {\n      return hasNonNullableKey(item, 'items');\n    };\n    var findTextByValue = function (value, catalog) {\n      return findMap(catalog, function (item) {\n        if (isListGroup(item)) {\n          return findTextByValue(value, item.items);\n        } else {\n          return someIf(item.value === value, item);\n        }\n      });\n    };\n    var getDelta = function (persistentText, fieldName, catalog, data) {\n      var value = data[fieldName];\n      var hasPersistentText = persistentText.length > 0;\n      return value !== undefined ? findTextByValue(value, catalog).map(function (i) {\n        return {\n          url: {\n            value: i.value,\n            meta: {\n              text: hasPersistentText ? persistentText : i.text,\n              attach: noop\n            }\n          },\n          text: hasPersistentText ? persistentText : i.text\n        };\n      }) : Optional.none();\n    };\n    var findCatalog = function (catalogs, fieldName) {\n      if (fieldName === 'link') {\n        return catalogs.link;\n      } else if (fieldName === 'anchor') {\n        return catalogs.anchor;\n      } else {\n        return Optional.none();\n      }\n    };\n    var init = function (initialData, linkCatalog) {\n      var persistentData = {\n        text: initialData.text,\n        title: initialData.title\n      };\n      var getTitleFromUrlChange = function (url) {\n        return someIf(persistentData.title.length <= 0, Optional.from(url.meta.title).getOr(''));\n      };\n      var getTextFromUrlChange = function (url) {\n        return someIf(persistentData.text.length <= 0, Optional.from(url.meta.text).getOr(url.value));\n      };\n      var onUrlChange = function (data) {\n        var text = getTextFromUrlChange(data.url);\n        var title = getTitleFromUrlChange(data.url);\n        if (text.isSome() || title.isSome()) {\n          return Optional.some(__assign(__assign({}, text.map(function (text) {\n            return { text: text };\n          }).getOr({})), title.map(function (title) {\n            return { title: title };\n          }).getOr({})));\n        } else {\n          return Optional.none();\n        }\n      };\n      var onCatalogChange = function (data, change) {\n        var catalog = findCatalog(linkCatalog, change.name).getOr([]);\n        return getDelta(persistentData.text, change.name, catalog, data);\n      };\n      var onChange = function (getData, change) {\n        var name = change.name;\n        if (name === 'url') {\n          return onUrlChange(getData());\n        } else if (contains([\n            'anchor',\n            'link'\n          ], name)) {\n          return onCatalogChange(getData(), change);\n        } else if (name === 'text' || name === 'title') {\n          persistentData[name] = getData()[name];\n          return Optional.none();\n        } else {\n          return Optional.none();\n        }\n      };\n      return { onChange: onChange };\n    };\n    var DialogChanges = {\n      init: init,\n      getDelta: getDelta\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Promise');\n\n    var delayedConfirm = function (editor, message, callback) {\n      var rng = editor.selection.getRng();\n      global$2.setEditorTimeout(editor, function () {\n        editor.windowManager.confirm(message, function (state) {\n          editor.selection.setRng(rng);\n          callback(state);\n        });\n      });\n    };\n    var tryEmailTransform = function (data) {\n      var url = data.href;\n      var suggestMailTo = url.indexOf('@') > 0 && url.indexOf('/') === -1 && url.indexOf('mailto:') === -1;\n      return suggestMailTo ? Optional.some({\n        message: 'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?',\n        preprocess: function (oldData) {\n          return __assign(__assign({}, oldData), { href: 'mailto:' + url });\n        }\n      }) : Optional.none();\n    };\n    var tryProtocolTransform = function (assumeExternalTargets, defaultLinkProtocol) {\n      return function (data) {\n        var url = data.href;\n        var suggestProtocol = assumeExternalTargets === 1 && !hasProtocol(url) || assumeExternalTargets === 0 && /^\\s*www(\\.|\\d\\.)/i.test(url);\n        return suggestProtocol ? Optional.some({\n          message: 'The URL you entered seems to be an external link. Do you want to add the required ' + defaultLinkProtocol + ':// prefix?',\n          preprocess: function (oldData) {\n            return __assign(__assign({}, oldData), { href: defaultLinkProtocol + '://' + url });\n          }\n        }) : Optional.none();\n      };\n    };\n    var preprocess = function (editor, data) {\n      return findMap([\n        tryEmailTransform,\n        tryProtocolTransform(assumeExternalTargets(editor), getDefaultLinkProtocol(editor))\n      ], function (f) {\n        return f(data);\n      }).fold(function () {\n        return global$1.resolve(data);\n      }, function (transform) {\n        return new global$1(function (callback) {\n          delayedConfirm(editor, transform.message, function (state) {\n            callback(state ? transform.preprocess(data) : data);\n          });\n        });\n      });\n    };\n    var DialogConfirms = { preprocess: preprocess };\n\n    var getAnchors = function (editor) {\n      var anchorNodes = editor.dom.select('a:not([href])');\n      var anchors = bind(anchorNodes, function (anchor) {\n        var id = anchor.name || anchor.id;\n        return id ? [{\n            text: id,\n            value: '#' + id\n          }] : [];\n      });\n      return anchors.length > 0 ? Optional.some([{\n          text: 'None',\n          value: ''\n        }].concat(anchors)) : Optional.none();\n    };\n    var AnchorListOptions = { getAnchors: getAnchors };\n\n    var getClasses = function (editor) {\n      var list = getLinkClassList(editor);\n      if (list.length > 0) {\n        return ListOptions.sanitize(list);\n      }\n      return Optional.none();\n    };\n    var ClassListOptions = { getClasses: getClasses };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.XHR');\n\n    var parseJson = function (text) {\n      try {\n        return Optional.some(JSON.parse(text));\n      } catch (err) {\n        return Optional.none();\n      }\n    };\n    var getLinks = function (editor) {\n      var extractor = function (item) {\n        return editor.convertURL(item.value || item.url, 'href');\n      };\n      var linkList = getLinkList(editor);\n      return new global$1(function (callback) {\n        if (isString(linkList)) {\n          global.send({\n            url: linkList,\n            success: function (text) {\n              return callback(parseJson(text));\n            },\n            error: function (_) {\n              return callback(Optional.none());\n            }\n          });\n        } else if (isFunction(linkList)) {\n          linkList(function (output) {\n            return callback(Optional.some(output));\n          });\n        } else {\n          callback(Optional.from(linkList));\n        }\n      }).then(function (optItems) {\n        return optItems.bind(ListOptions.sanitizeWith(extractor)).map(function (items) {\n          if (items.length > 0) {\n            var noneItem = [{\n                text: 'None',\n                value: ''\n              }];\n            return noneItem.concat(items);\n          } else {\n            return items;\n          }\n        });\n      });\n    };\n    var LinkListOptions = { getLinks: getLinks };\n\n    var getRels = function (editor, initialTarget) {\n      var list = getRelList(editor);\n      if (list.length > 0) {\n        var isTargetBlank_1 = is(initialTarget, '_blank');\n        var enforceSafe = allowUnsafeLinkTarget(editor) === false;\n        var safeRelExtractor = function (item) {\n          return applyRelTargetRules(ListOptions.getValue(item), isTargetBlank_1);\n        };\n        var sanitizer = enforceSafe ? ListOptions.sanitizeWith(safeRelExtractor) : ListOptions.sanitize;\n        return sanitizer(list);\n      }\n      return Optional.none();\n    };\n    var RelOptions = { getRels: getRels };\n\n    var fallbacks = [\n      {\n        text: 'Current window',\n        value: ''\n      },\n      {\n        text: 'New window',\n        value: '_blank'\n      }\n    ];\n    var getTargets = function (editor) {\n      var list = getTargetList(editor);\n      if (isArray(list)) {\n        return ListOptions.sanitize(list).orThunk(function () {\n          return Optional.some(fallbacks);\n        });\n      } else if (list === false) {\n        return Optional.none();\n      }\n      return Optional.some(fallbacks);\n    };\n    var TargetOptions = { getTargets: getTargets };\n\n    var nonEmptyAttr = function (dom, elem, name) {\n      var val = dom.getAttrib(elem, name);\n      return val !== null && val.length > 0 ? Optional.some(val) : Optional.none();\n    };\n    var extractFromAnchor = function (editor, anchor) {\n      var dom = editor.dom;\n      var onlyText = isOnlyTextSelected(editor);\n      var text = onlyText ? Optional.some(getAnchorText(editor.selection, anchor)) : Optional.none();\n      var url = anchor ? Optional.some(dom.getAttrib(anchor, 'href')) : Optional.none();\n      var target = anchor ? Optional.from(dom.getAttrib(anchor, 'target')) : Optional.none();\n      var rel = nonEmptyAttr(dom, anchor, 'rel');\n      var linkClass = nonEmptyAttr(dom, anchor, 'class');\n      var title = nonEmptyAttr(dom, anchor, 'title');\n      return {\n        url: url,\n        text: text,\n        title: title,\n        target: target,\n        rel: rel,\n        linkClass: linkClass\n      };\n    };\n    var collect = function (editor, linkNode) {\n      return LinkListOptions.getLinks(editor).then(function (links) {\n        var anchor = extractFromAnchor(editor, linkNode);\n        return {\n          anchor: anchor,\n          catalogs: {\n            targets: TargetOptions.getTargets(editor),\n            rels: RelOptions.getRels(editor, anchor.target),\n            classes: ClassListOptions.getClasses(editor),\n            anchor: AnchorListOptions.getAnchors(editor),\n            link: links\n          },\n          optNode: Optional.from(linkNode),\n          flags: { titleEnabled: shouldShowLinkTitle(editor) }\n        };\n      });\n    };\n    var DialogInfo = { collect: collect };\n\n    var handleSubmit = function (editor, info) {\n      return function (api) {\n        var data = api.getData();\n        if (!data.url.value) {\n          unlink(editor);\n          api.close();\n          return;\n        }\n        var getChangedValue = function (key) {\n          return Optional.from(data[key]).filter(function (value) {\n            return !is(info.anchor[key], value);\n          });\n        };\n        var changedData = {\n          href: data.url.value,\n          text: getChangedValue('text'),\n          target: getChangedValue('target'),\n          rel: getChangedValue('rel'),\n          class: getChangedValue('linkClass'),\n          title: getChangedValue('title')\n        };\n        var attachState = {\n          href: data.url.value,\n          attach: data.url.meta !== undefined && data.url.meta.attach ? data.url.meta.attach : noop\n        };\n        DialogConfirms.preprocess(editor, changedData).then(function (pData) {\n          link(editor, attachState, pData);\n        });\n        api.close();\n      };\n    };\n    var collectData = function (editor) {\n      var anchorNode = getAnchorElement(editor);\n      return DialogInfo.collect(editor, anchorNode);\n    };\n    var getInitialData = function (info, defaultTarget) {\n      var anchor = info.anchor;\n      var url = anchor.url.getOr('');\n      return {\n        url: {\n          value: url,\n          meta: { original: { value: url } }\n        },\n        text: anchor.text.getOr(''),\n        title: anchor.title.getOr(''),\n        anchor: url,\n        link: url,\n        rel: anchor.rel.getOr(''),\n        target: anchor.target.or(defaultTarget).getOr(''),\n        linkClass: anchor.linkClass.getOr('')\n      };\n    };\n    var makeDialog = function (settings, onSubmit, editor) {\n      var urlInput = [{\n          name: 'url',\n          type: 'urlinput',\n          filetype: 'file',\n          label: 'URL'\n        }];\n      var displayText = settings.anchor.text.map(function () {\n        return {\n          name: 'text',\n          type: 'input',\n          label: 'Text to display'\n        };\n      }).toArray();\n      var titleText = settings.flags.titleEnabled ? [{\n          name: 'title',\n          type: 'input',\n          label: 'Title'\n        }] : [];\n      var defaultTarget = Optional.from(getDefaultLinkTarget(editor));\n      var initialData = getInitialData(settings, defaultTarget);\n      var catalogs = settings.catalogs;\n      var dialogDelta = DialogChanges.init(initialData, catalogs);\n      var body = {\n        type: 'panel',\n        items: flatten([\n          urlInput,\n          displayText,\n          titleText,\n          cat([\n            catalogs.anchor.map(ListOptions.createUi('anchor', 'Anchors')),\n            catalogs.rels.map(ListOptions.createUi('rel', 'Rel')),\n            catalogs.targets.map(ListOptions.createUi('target', 'Open link in...')),\n            catalogs.link.map(ListOptions.createUi('link', 'Link list')),\n            catalogs.classes.map(ListOptions.createUi('linkClass', 'Class'))\n          ])\n        ])\n      };\n      return {\n        title: 'Insert/Edit Link',\n        size: 'normal',\n        body: body,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: initialData,\n        onChange: function (api, _a) {\n          var name = _a.name;\n          dialogDelta.onChange(api.getData, { name: name }).each(function (newData) {\n            api.setData(newData);\n          });\n        },\n        onSubmit: onSubmit\n      };\n    };\n    var open$1 = function (editor) {\n      var data = collectData(editor);\n      data.then(function (info) {\n        var onSubmit = handleSubmit(editor, info);\n        return makeDialog(info, onSubmit, editor);\n      }).then(function (spec) {\n        editor.windowManager.open(spec);\n      });\n    };\n\n    var appendClickRemove = function (link, evt) {\n      document.body.appendChild(link);\n      link.dispatchEvent(evt);\n      document.body.removeChild(link);\n    };\n    var open = function (url) {\n      var link = document.createElement('a');\n      link.target = '_blank';\n      link.href = url;\n      link.rel = 'noreferrer noopener';\n      var evt = document.createEvent('MouseEvents');\n      evt.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n      appendClickRemove(link, evt);\n    };\n\n    var getLink = function (editor, elm) {\n      return editor.dom.getParent(elm, 'a[href]');\n    };\n    var getSelectedLink = function (editor) {\n      return getLink(editor, editor.selection.getStart());\n    };\n    var hasOnlyAltModifier = function (e) {\n      return e.altKey === true && e.shiftKey === false && e.ctrlKey === false && e.metaKey === false;\n    };\n    var gotoLink = function (editor, a) {\n      if (a) {\n        var href = getHref(a);\n        if (/^#/.test(href)) {\n          var targetEl = editor.$(href);\n          if (targetEl.length) {\n            editor.selection.scrollIntoView(targetEl[0], true);\n          }\n        } else {\n          open(a.href);\n        }\n      }\n    };\n    var openDialog = function (editor) {\n      return function () {\n        open$1(editor);\n      };\n    };\n    var gotoSelectedLink = function (editor) {\n      return function () {\n        gotoLink(editor, getSelectedLink(editor));\n      };\n    };\n    var setupGotoLinks = function (editor) {\n      editor.on('click', function (e) {\n        var link = getLink(editor, e.target);\n        if (link && global$6.metaKeyPressed(e)) {\n          e.preventDefault();\n          gotoLink(editor, link);\n        }\n      });\n      editor.on('keydown', function (e) {\n        var link = getSelectedLink(editor);\n        if (link && e.keyCode === 13 && hasOnlyAltModifier(e)) {\n          e.preventDefault();\n          gotoLink(editor, link);\n        }\n      });\n    };\n    var toggleState = function (editor, toggler) {\n      editor.on('NodeChange', toggler);\n      return function () {\n        return editor.off('NodeChange', toggler);\n      };\n    };\n    var toggleActiveState = function (editor) {\n      return function (api) {\n        var updateState = function () {\n          return api.setActive(!editor.mode.isReadOnly() && getAnchorElement(editor, editor.selection.getNode()) !== null);\n        };\n        updateState();\n        return toggleState(editor, updateState);\n      };\n    };\n    var toggleEnabledState = function (editor) {\n      return function (api) {\n        var updateState = function () {\n          return api.setDisabled(getAnchorElement(editor, editor.selection.getNode()) === null);\n        };\n        updateState();\n        return toggleState(editor, updateState);\n      };\n    };\n    var toggleUnlinkState = function (editor) {\n      return function (api) {\n        var hasLinks$1 = function (parents) {\n          return hasLinks(parents) || hasLinksInSelection(editor.selection.getRng());\n        };\n        var parents = editor.dom.getParents(editor.selection.getStart());\n        api.setDisabled(!hasLinks$1(parents));\n        return toggleState(editor, function (e) {\n          return api.setDisabled(!hasLinks$1(e.parents));\n        });\n      };\n    };\n\n    var register = function (editor) {\n      editor.addCommand('mceLink', function () {\n        if (useQuickLink(editor)) {\n          editor.fire('contexttoolbar-show', { toolbarKey: 'quicklink' });\n        } else {\n          openDialog(editor)();\n        }\n      });\n    };\n\n    var setup = function (editor) {\n      editor.addShortcut('Meta+K', '', function () {\n        editor.execCommand('mceLink');\n      });\n    };\n\n    var setupButtons = function (editor) {\n      editor.ui.registry.addToggleButton('link', {\n        icon: 'link',\n        tooltip: 'Insert/edit link',\n        onAction: openDialog(editor),\n        onSetup: toggleActiveState(editor)\n      });\n      editor.ui.registry.addButton('openlink', {\n        icon: 'new-tab',\n        tooltip: 'Open link',\n        onAction: gotoSelectedLink(editor),\n        onSetup: toggleEnabledState(editor)\n      });\n      editor.ui.registry.addButton('unlink', {\n        icon: 'unlink',\n        tooltip: 'Remove link',\n        onAction: function () {\n          return unlink(editor);\n        },\n        onSetup: toggleUnlinkState(editor)\n      });\n    };\n    var setupMenuItems = function (editor) {\n      editor.ui.registry.addMenuItem('openlink', {\n        text: 'Open link',\n        icon: 'new-tab',\n        onAction: gotoSelectedLink(editor),\n        onSetup: toggleEnabledState(editor)\n      });\n      editor.ui.registry.addMenuItem('link', {\n        icon: 'link',\n        text: 'Link...',\n        shortcut: 'Meta+K',\n        onAction: openDialog(editor)\n      });\n      editor.ui.registry.addMenuItem('unlink', {\n        icon: 'unlink',\n        text: 'Remove link',\n        onAction: function () {\n          return unlink(editor);\n        },\n        onSetup: toggleUnlinkState(editor)\n      });\n    };\n    var setupContextMenu = function (editor) {\n      var inLink = 'link unlink openlink';\n      var noLink = 'link';\n      editor.ui.registry.addContextMenu('link', {\n        update: function (element) {\n          return hasLinks(editor.dom.getParents(element, 'a')) ? inLink : noLink;\n        }\n      });\n    };\n    var setupContextToolbars = function (editor) {\n      var collapseSelectionToEnd = function (editor) {\n        editor.selection.collapse(false);\n      };\n      var onSetupLink = function (buttonApi) {\n        var node = editor.selection.getNode();\n        buttonApi.setDisabled(!getAnchorElement(editor, node));\n        return noop;\n      };\n      var getLinkText = function (value) {\n        var anchor = getAnchorElement(editor);\n        var onlyText = isOnlyTextSelected(editor);\n        if (!anchor && onlyText) {\n          var text = getAnchorText(editor.selection, anchor);\n          return Optional.some(text.length > 0 ? text : value);\n        } else {\n          return Optional.none();\n        }\n      };\n      editor.ui.registry.addContextForm('quicklink', {\n        launch: {\n          type: 'contextformtogglebutton',\n          icon: 'link',\n          tooltip: 'Link',\n          onSetup: toggleActiveState(editor)\n        },\n        label: 'Link',\n        predicate: function (node) {\n          return !!getAnchorElement(editor, node) && hasContextToolbar(editor);\n        },\n        initValue: function () {\n          var elm = getAnchorElement(editor);\n          return !!elm ? getHref(elm) : '';\n        },\n        commands: [\n          {\n            type: 'contextformtogglebutton',\n            icon: 'link',\n            tooltip: 'Link',\n            primary: true,\n            onSetup: function (buttonApi) {\n              var node = editor.selection.getNode();\n              buttonApi.setActive(!!getAnchorElement(editor, node));\n              return toggleActiveState(editor)(buttonApi);\n            },\n            onAction: function (formApi) {\n              var value = formApi.getValue();\n              var text = getLinkText(value);\n              var attachState = {\n                href: value,\n                attach: noop\n              };\n              link(editor, attachState, {\n                href: value,\n                text: text,\n                title: Optional.none(),\n                rel: Optional.none(),\n                target: Optional.none(),\n                class: Optional.none()\n              });\n              collapseSelectionToEnd(editor);\n              formApi.hide();\n            }\n          },\n          {\n            type: 'contextformbutton',\n            icon: 'unlink',\n            tooltip: 'Remove link',\n            onSetup: onSetupLink,\n            onAction: function (formApi) {\n              unlink(editor);\n              formApi.hide();\n            }\n          },\n          {\n            type: 'contextformbutton',\n            icon: 'new-tab',\n            tooltip: 'Open link',\n            onSetup: onSetupLink,\n            onAction: function (formApi) {\n              gotoSelectedLink(editor)();\n              formApi.hide();\n            }\n          }\n        ]\n      });\n    };\n\n    function Plugin () {\n      global$7.add('link', function (editor) {\n        setupButtons(editor);\n        setupMenuItems(editor);\n        setupContextMenu(editor);\n        setupContextToolbars(editor);\n        setupGotoLinks(editor);\n        register(editor);\n        setup(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"link\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/link')\n//   ES2015:\n//     import 'tinymce/plugins/link'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/link/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,WAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,MAAM;AACjC,eAAO,SAAU,OAAO;AACtB,iBAAO,OAAO,UAAU;AAAA;AAAA;AAG5B,UAAI,KAAK,SAAU,GAAG;AACpB,eAAO,SAAU,GAAG;AAClB,iBAAO,MAAM;AAAA;AAAA;AAGjB,UAAI,WAAW,OAAO;AACtB,UAAI,UAAU,OAAO;AACrB,UAAI,SAAS,GAAG;AAChB,UAAI,YAAY,aAAa;AAC7B,UAAI,aAAa,aAAa;AAE9B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,OAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,GAAG,GAAG;AACjC,eAAO,MAAM;AAAA;AAEf,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,QAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,OAAO;AAC1B,eAAO,UAAU,QAAQ,UAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,gBAAgB,MAAM,UAAU;AACpC,UAAI,aAAa,MAAM,UAAU;AACjC,UAAI,aAAa,SAAU,IAAI,GAAG;AAChC,eAAO,cAAc,KAAK,IAAI;AAAA;AAEhC,UAAI,WAAW,SAAU,IAAI,GAAG;AAC9B,eAAO,WAAW,IAAI,KAAK;AAAA;AAE7B,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,IAAI,GAAG;AAC5B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,QAAQ,SAAU,IAAI,GAAG,KAAK;AAChC,eAAO,IAAI,SAAU,GAAG,GAAG;AACzB,gBAAM,EAAE,KAAK,GAAG;AAAA;AAElB,eAAO;AAAA;AAET,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,KAAK;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;AAAA;AAE3E,qBAAW,MAAM,GAAG,GAAG;AAAA;AAEzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,eAAO,QAAQ,IAAI,IAAI;AAAA;AAEzB,UAAI,UAAU,SAAU,KAAK,GAAG;AAC9B,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,IAAI,EAAE,IAAI,IAAI;AAClB,cAAI,EAAE,UAAU;AACd,mBAAO;AAAA;AAAA;AAGX,eAAO,SAAS;AAAA;AAGlB,UAAI,KAAK,SAAU,KAAK,KAAK,YAAY;AACvC,YAAI,eAAe,QAAQ;AACzB,uBAAa;AAAA;AAEf,eAAO,IAAI,OAAO,SAAU,MAAM;AAChC,iBAAO,WAAW,MAAM;AAAA;AAAA;AAG5B,UAAI,MAAM,SAAU,KAAK;AACvB,YAAI,IAAI;AACR,YAAI,OAAO,SAAU,GAAG;AACtB,YAAE,KAAK;AAAA;AAET,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,GAAG,KAAK;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,GAAG,GAAG;AAC3B,eAAO,IAAI,SAAS,KAAK,KAAK,SAAS;AAAA;AAGzC,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,YAAI,kBAAkB,OAAO,SAAS,gCAAgC;AACtE,YAAI,UAAU,oBAAoB,iBAAiB;AACjD,iBAAO;AAAA,mBACE,SAAS,oBAAqB,qBAAoB,UAAU,oBAAoB,UAAU;AACnG,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,OAAO,SAAS,wBAAwB,OAAO;AAAA;AAExD,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,OAAO,SAAS;AAAA;AAEzB,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,OAAO,SAAS,eAAe;AAAA;AAExC,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,OAAO,SAAS,YAAY,IAAI;AAAA;AAEzC,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,SAAS,mBAAmB,IAAI;AAAA;AAEhD,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,OAAO,SAAS,cAAc,MAAM;AAAA;AAE7C,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,eAAO,OAAO,SAAS,4BAA4B,OAAO;AAAA;AAE5D,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,OAAO,SAAS,kBAAkB,OAAO;AAAA;AAElD,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS,yBAAyB,QAAQ;AAAA;AAG1D,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,SAAU,MAAM;AAC7B,eAAO,SAAS,KAAK,SAAS,KAAK,QAAQ;AAAA;AAE7C,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,KAAK,OAAO;AACvB,iBAAO,KAAK;AAAA,mBACH,SAAS,KAAK,QAAQ;AAC/B,iBAAO,KAAK;AAAA,eACP;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,eAAe,SAAU,MAAM,cAAc;AAC/C,YAAI,MAAM;AACV,iBAAS,KAAK,MAAM,SAAU,MAAM;AAClC,cAAI,OAAO,QAAQ;AACnB,cAAI,KAAK,SAAS,QAAW;AAC3B,gBAAI,QAAQ,aAAa,KAAK,MAAM;AACpC,gBAAI,KAAK;AAAA,cACP;AAAA,cACA;AAAA;AAAA,iBAEG;AACL,gBAAI,QAAQ,aAAa;AACzB,gBAAI,KAAK;AAAA,cACP;AAAA,cACA;AAAA;AAAA;AAAA;AAIN,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,WAAW;AACtC,YAAI,cAAc,QAAQ;AACxB,sBAAY;AAAA;AAEd,eAAO,SAAU,MAAM;AACrB,iBAAO,SAAS,KAAK,MAAM,IAAI,SAAU,OAAM;AAC7C,mBAAO,aAAa,OAAM;AAAA;AAAA;AAAA;AAIhC,UAAI,WAAW,SAAU,MAAM;AAC7B,eAAO,aAAa,UAAU;AAAA;AAEhC,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,eAAO,SAAU,OAAO;AACtB,iBAAO;AAAA,YACL;AAAA,YACA,MAAM;AAAA,YACN;AAAA,YACA;AAAA;AAAA;AAAA;AAIN,UAAI,cAAc;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,OAAO,OAAO;AAClB,UAAI,iBAAiB,OAAO;AAC5B,UAAI,OAAO,SAAU,KAAK,GAAG;AAC3B,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,IAAI;AACZ,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,SAAS,SAAU,GAAG;AACxB,eAAO,SAAU,GAAG,GAAG;AACrB,YAAE,KAAK;AAAA;AAAA;AAGX,UAAI,iBAAiB,SAAU,KAAK,MAAM,QAAQ,SAAS;AACzD,YAAI,IAAI;AACR,aAAK,KAAK,SAAU,GAAG,GAAG;AACxB,UAAC,MAAK,GAAG,KAAK,SAAS,SAAS,GAAG;AAAA;AAErC,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,KAAK,MAAM;AAChC,YAAI,IAAI;AACR,uBAAe,KAAK,MAAM,OAAO,IAAI;AACrC,eAAO;AAAA;AAET,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAElC,UAAI,oBAAoB,SAAU,KAAK,KAAK;AAC1C,eAAO,IAAI,KAAK,QAAQ,IAAI,SAAS,UAAa,IAAI,SAAS;AAAA;AAGjE,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,SAAU,KAAK;AAC5B,eAAO,OAAO,IAAI,SAAS,kBAAkB;AAAA;AAE/C,UAAI,SAAS,SAAU,KAAK;AAC1B,eAAO,SAAS,QAAQ,CAAC,CAAC,QAAQ;AAAA;AAEpC,UAAI,sBAAsB,SAAU,KAAK,WAAW;AAClD,YAAI,IAAI,WAAW;AACjB,iBAAO;AAAA,eACF;AACL,cAAI,WAAW,IAAI;AACnB,cAAI,SAAS,IAAI,SAAS,SAAS,YAAY;AAC/C,cAAI,WAAW;AACf,cAAI,UAAU,SAAS;AACvB,aAAG;AACD,gBAAI,UAAU,UAAU;AACtB,uBAAS,KAAK;AAAA;AAAA,mBAET,UAAU,OAAO;AAC1B,iBAAO;AAAA;AAAA;AAGX,UAAI,cAAc,SAAU,KAAK;AAC/B,eAAO,SAAS,KAAK;AAAA;AAEvB,UAAI,UAAU,SAAU,KAAK;AAC3B,YAAI,OAAO,IAAI,aAAa;AAC5B,eAAO,OAAO,OAAO,IAAI,aAAa;AAAA;AAExC,UAAI,sBAAsB,SAAU,KAAK,UAAU;AACjD,YAAI,QAAQ,CAAC;AACb,YAAI,OAAO,MAAM,IAAI,MAAM,SAAS;AACpC,YAAI,WAAW,SAAU,OAAM;AAC7B,iBAAO,SAAS,KAAK,MAAK,OAAO,KAAK;AAAA;AAExC,YAAI,iBAAiB,SAAU,OAAM;AACnC,kBAAO,kBAAkB;AACzB,iBAAO,MAAK,SAAS,IAAI,MAAK,OAAO,SAAS;AAAA;AAEhD,YAAI,oBAAoB,SAAU,OAAM;AACtC,iBAAO,MAAK,OAAO,SAAU,KAAK;AAChC,mBAAO,SAAS,QAAQ,OAAO,SAAS;AAAA;AAAA;AAG5C,YAAI,UAAU,WAAW,eAAe,QAAQ,kBAAkB;AAClE,eAAO,QAAQ,SAAS,IAAI,SAAS,WAAW;AAAA;AAElD,UAAI,sBAAsB,SAAU,MAAM;AACxC,eAAO,KAAK,QAAQ,WAAW;AAAA;AAEjC,UAAI,mBAAmB,SAAU,QAAQ,aAAa;AACpD,sBAAc,eAAe,OAAO,UAAU;AAC9C,YAAI,cAAc,cAAc;AAC9B,iBAAO,OAAO,IAAI,OAAO,WAAW,aAAa;AAAA,eAC5C;AACL,iBAAO,OAAO,IAAI,UAAU,aAAa;AAAA;AAAA;AAG7C,UAAI,gBAAgB,SAAU,WAAW,WAAW;AAClD,YAAI,OAAO,YAAY,UAAU,aAAa,UAAU,cAAc,UAAU,WAAW,EAAE,QAAQ;AACrG,eAAO,oBAAoB;AAAA;AAE7B,UAAI,WAAW,SAAU,UAAU;AACjC,eAAO,SAAS,KAAK,UAAU,QAAQ,SAAS;AAAA;AAElD,UAAI,sBAAsB,SAAU,KAAK;AACvC,eAAO,oBAAoB,KAAK,QAAQ,SAAS;AAAA;AAEnD,UAAI,qBAAqB,SAAU,QAAQ;AACzC,YAAI,qBAAqB,OAAO,OAAO;AACvC,YAAI,YAAY,SAAU,KAAK;AAC7B,iBAAO,IAAI,aAAa,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI,oBAAoB,IAAI,SAAS;AAAA;AAEvF,YAAI,WAAW,oBAAoB,OAAO,UAAU,UAAU;AAC9D,eAAO,SAAS,WAAW;AAAA;AAE7B,UAAI,gBAAgB,SAAU,KAAK;AACjC,eAAO,OAAO,IAAI,aAAa,YAAY,aAAa,KAAK,IAAI;AAAA;AAEnE,UAAI,eAAe,SAAU,MAAM;AACjC,YAAI,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAEF,eAAO,MAAM,OAAO,SAAU,KAAK,KAAK;AACtC,eAAK,KAAK,KAAK,SAAU,OAAO;AAC9B,gBAAI,OAAO,MAAM,SAAS,IAAI,QAAQ;AAAA;AAExC,iBAAO;AAAA,WACN,EAAE,MAAM,KAAK;AAAA;AAElB,UAAI,wBAAwB,SAAU,MAAM,wBAAuB;AACjE,YAAK,4BAA0B,UAAU,2BAA0B,YAAY,CAAC,YAAY,OAAO;AACjG,iBAAO,yBAAwB,QAAQ;AAAA;AAEzC,eAAO;AAAA;AAET,UAAI,qBAAqB,SAAU,QAAQ,WAAW;AACpD,YAAI,eAAe,SAAS,IAAI;AAChC,YAAI,CAAE,YAAW,QAAQ,SAAS,MAAM,sBAAsB,YAAY,OAAO;AAC/E,cAAI,SAAS,oBAAoB,aAAa,KAAK,aAAa,WAAW;AAC3E,uBAAa,MAAM,SAAS,SAAS;AAAA;AAEvC,YAAI,SAAS,KAAK,aAAa,QAAQ,YAAY,cAAc,YAAY,OAAO;AAClF,uBAAa,SAAS,qBAAqB;AAAA;AAE7C,qBAAa,OAAO,sBAAsB,aAAa,MAAM,sBAAsB;AACnF,eAAO;AAAA;AAET,UAAI,aAAa,SAAU,QAAQ,WAAW,MAAM,WAAW;AAC7D,aAAK,KAAK,SAAU,OAAM;AACxB,cAAI,IAAI,WAAW,cAAc;AAC/B,sBAAU,YAAY;AAAA,iBACjB;AACL,sBAAU,cAAc;AAAA;AAAA;AAG5B,eAAO,IAAI,WAAW,WAAW;AACjC,eAAO,UAAU,OAAO;AAAA;AAE1B,UAAI,aAAa,SAAU,QAAQ,aAAa,MAAM,WAAW;AAC/D,YAAI,cAAc,cAAc;AAC9B,0BAAgB,QAAQ,aAAa;AAAA,eAChC;AACL,eAAK,KAAK,WAAY;AACpB,mBAAO,YAAY,iBAAiB,OAAO;AAAA,aAC1C,SAAU,OAAM;AACjB,mBAAO,cAAc,OAAO,IAAI,WAAW,KAAK,WAAW,OAAO,IAAI,OAAO;AAAA;AAAA;AAAA;AAInF,UAAI,kBAAkB,SAAU,QAAQ,aAAa,MAAM;AACzD,YAAI,cAAc,OAAO,UAAU;AACnC,YAAI,YAAY,iBAAiB,QAAQ;AACzC,YAAI,YAAY,mBAAmB,QAAQ,aAAa;AACxD,eAAO,YAAY,SAAS,WAAY;AACtC,cAAI,KAAK,SAAS,YAAY,MAAM;AAClC,wBAAY;AAAA;AAEd,cAAI,WAAW;AACb,mBAAO;AACP,uBAAW,QAAQ,WAAW,KAAK,MAAM;AAAA,iBACpC;AACL,uBAAW,QAAQ,aAAa,KAAK,MAAM;AAAA;AAAA;AAAA;AAIjD,UAAI,kBAAkB,SAAU,QAAQ;AACtC,YAAI,MAAM,OAAO,KAAK,YAAY,OAAO;AACzC,YAAI,WAAW,UAAU;AACzB,YAAI,MAAM,UAAU,SAAS;AAC7B,YAAI,iBAAiB,IAAI,UAAU,IAAI,gBAAgB,WAAW,OAAO;AACzE,YAAI,eAAe,IAAI,UAAU,IAAI,cAAc,WAAW,OAAO;AACrE,YAAI,gBAAgB;AAClB,cAAI,eAAe;AAAA;AAErB,YAAI,cAAc;AAChB,cAAI,YAAY;AAAA;AAElB,kBAAU,OAAO;AACjB,eAAO,YAAY;AACnB,kBAAU,eAAe;AAAA;AAE3B,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,YAAY,SAAS,WAAY;AACtC,cAAI,OAAO,OAAO,UAAU;AAC5B,cAAI,cAAc,OAAO;AACvB,8BAAkB,QAAQ;AAAA,iBACrB;AACL,4BAAgB;AAAA;AAElB,iBAAO;AAAA;AAAA;AAGX,UAAI,gBAAgB,SAAU,MAAM;AAClC,YAAI,MAAM,KAAK,OAAO,OAAO,KAAK,MAAM,MAAM,KAAK,KAAK,SAAS,KAAK,QAAQ,OAAO,KAAK,MAAM,QAAQ,KAAK;AAC7G,eAAO,OAAO;AAAA,UACZ,OAAO,IAAI;AAAA,UACX;AAAA,UACA,KAAK,IAAI;AAAA,UACT,QAAQ,OAAO;AAAA,UACf,MAAM,KAAK;AAAA,UACX,OAAO,MAAM;AAAA,WACZ,SAAU,GAAG,IAAI;AAClB,iBAAO,OAAO,OAAO;AAAA;AAAA;AAGzB,UAAI,eAAe,SAAU,QAAQ,MAAM;AACzC,YAAI,OAAO,KAAK;AAChB,eAAO,SAAS,SAAS,IAAI,OAAO,EAAE,MAAM,SAAS,UAAU,MAAM,KAAK,OAAO,YAAY,OAAO;AAAA;AAEtG,UAAI,OAAO,SAAU,QAAQ,aAAa,MAAM;AAC9C,YAAI,gBAAgB,aAAa,QAAQ;AACzC,eAAO,UAAU,OAAO,QAAQ,OAAO,YAAY,cAAc,OAAO,cAAc,kBAAkB,gBAAgB,QAAQ,aAAa;AAAA;AAE/I,UAAI,SAAS,SAAU,QAAQ;AAC7B,eAAO,UAAU,OAAO,QAAQ,OAAO,YAAY,YAAY,kBAAkB;AAAA;AAEnF,UAAI,oBAAoB,SAAU,QAAQ,KAAK;AAC7C,YAAI,MAAM,OAAO,IAAI,OAAO,OAAO,KAAK;AACxC,YAAI,KAAK;AACP,cAAI,IAAI,OAAO,IAAI,WAAW,KAAK,WAAW,KAAK;AACnD,cAAI,GAAG;AACL,cAAE,WAAW,aAAa,KAAK;AAC/B,mBAAO,IAAI,OAAO;AAAA;AAAA;AAAA;AAIxB,UAAI,kBAAkB,SAAU,QAAQ,KAAK,OAAO;AAClD,YAAI,MAAM,OAAO,IAAI,OAAO,OAAO,KAAK;AACxC,YAAI,KAAK;AACP,cAAI,IAAI,OAAO,IAAI,OAAO,KAAK;AAC/B,cAAI,WAAW,aAAa,GAAG;AAC/B,YAAE,YAAY;AAAA;AAAA;AAIlB,UAAI,cAAc,SAAU,MAAM;AAChC,eAAO,kBAAkB,MAAM;AAAA;AAEjC,UAAI,kBAAkB,SAAU,OAAO,SAAS;AAC9C,eAAO,QAAQ,SAAS,SAAU,MAAM;AACtC,cAAI,YAAY,OAAO;AACrB,mBAAO,gBAAgB,OAAO,KAAK;AAAA,iBAC9B;AACL,mBAAO,OAAO,KAAK,UAAU,OAAO;AAAA;AAAA;AAAA;AAI1C,UAAI,WAAW,SAAU,gBAAgB,WAAW,SAAS,MAAM;AACjE,YAAI,QAAQ,KAAK;AACjB,YAAI,oBAAoB,eAAe,SAAS;AAChD,eAAO,UAAU,SAAY,gBAAgB,OAAO,SAAS,IAAI,SAAU,GAAG;AAC5E,iBAAO;AAAA,YACL,KAAK;AAAA,cACH,OAAO,EAAE;AAAA,cACT,MAAM;AAAA,gBACJ,MAAM,oBAAoB,iBAAiB,EAAE;AAAA,gBAC7C,QAAQ;AAAA;AAAA;AAAA,YAGZ,MAAM,oBAAoB,iBAAiB,EAAE;AAAA;AAAA,aAE5C,SAAS;AAAA;AAEhB,UAAI,cAAc,SAAU,UAAU,WAAW;AAC/C,YAAI,cAAc,QAAQ;AACxB,iBAAO,SAAS;AAAA,mBACP,cAAc,UAAU;AACjC,iBAAO,SAAS;AAAA,eACX;AACL,iBAAO,SAAS;AAAA;AAAA;AAGpB,UAAI,OAAO,SAAU,aAAa,aAAa;AAC7C,YAAI,iBAAiB;AAAA,UACnB,MAAM,YAAY;AAAA,UAClB,OAAO,YAAY;AAAA;AAErB,YAAI,wBAAwB,SAAU,KAAK;AACzC,iBAAO,OAAO,eAAe,MAAM,UAAU,GAAG,SAAS,KAAK,IAAI,KAAK,OAAO,MAAM;AAAA;AAEtF,YAAI,uBAAuB,SAAU,KAAK;AACxC,iBAAO,OAAO,eAAe,KAAK,UAAU,GAAG,SAAS,KAAK,IAAI,KAAK,MAAM,MAAM,IAAI;AAAA;AAExF,YAAI,cAAc,SAAU,MAAM;AAChC,cAAI,OAAO,qBAAqB,KAAK;AACrC,cAAI,QAAQ,sBAAsB,KAAK;AACvC,cAAI,KAAK,YAAY,MAAM,UAAU;AACnC,mBAAO,SAAS,KAAK,SAAS,SAAS,IAAI,KAAK,IAAI,SAAU,OAAM;AAClE,qBAAO,EAAE,MAAM;AAAA,eACd,MAAM,MAAM,MAAM,IAAI,SAAU,QAAO;AACxC,qBAAO,EAAE,OAAO;AAAA,eACf,MAAM;AAAA,iBACJ;AACL,mBAAO,SAAS;AAAA;AAAA;AAGpB,YAAI,kBAAkB,SAAU,MAAM,QAAQ;AAC5C,cAAI,UAAU,YAAY,aAAa,OAAO,MAAM,MAAM;AAC1D,iBAAO,SAAS,eAAe,MAAM,OAAO,MAAM,SAAS;AAAA;AAE7D,YAAI,WAAW,SAAU,SAAS,QAAQ;AACxC,cAAI,OAAO,OAAO;AAClB,cAAI,SAAS,OAAO;AAClB,mBAAO,YAAY;AAAA,qBACV,SAAS;AAAA,YAChB;AAAA,YACA;AAAA,aACC,OAAO;AACV,mBAAO,gBAAgB,WAAW;AAAA,qBACzB,SAAS,UAAU,SAAS,SAAS;AAC9C,2BAAe,QAAQ,UAAU;AACjC,mBAAO,SAAS;AAAA,iBACX;AACL,mBAAO,SAAS;AAAA;AAAA;AAGpB,eAAO,EAAE;AAAA;AAEX,UAAI,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA;AAGF,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,iBAAiB,SAAU,QAAQ,SAAS,UAAU;AACxD,YAAI,MAAM,OAAO,UAAU;AAC3B,iBAAS,iBAAiB,QAAQ,WAAY;AAC5C,iBAAO,cAAc,QAAQ,SAAS,SAAU,OAAO;AACrD,mBAAO,UAAU,OAAO;AACxB,qBAAS;AAAA;AAAA;AAAA;AAIf,UAAI,oBAAoB,SAAU,MAAM;AACtC,YAAI,MAAM,KAAK;AACf,YAAI,gBAAgB,IAAI,QAAQ,OAAO,KAAK,IAAI,QAAQ,SAAS,MAAM,IAAI,QAAQ,eAAe;AAClG,eAAO,gBAAgB,SAAS,KAAK;AAAA,UACnC,SAAS;AAAA,UACT,YAAY,SAAU,SAAS;AAC7B,mBAAO,SAAS,SAAS,IAAI,UAAU,EAAE,MAAM,YAAY;AAAA;AAAA,aAE1D,SAAS;AAAA;AAEhB,UAAI,uBAAuB,SAAU,wBAAuB,qBAAqB;AAC/E,eAAO,SAAU,MAAM;AACrB,cAAI,MAAM,KAAK;AACf,cAAI,kBAAkB,2BAA0B,KAAK,CAAC,YAAY,QAAQ,2BAA0B,KAAK,oBAAoB,KAAK;AAClI,iBAAO,kBAAkB,SAAS,KAAK;AAAA,YACrC,SAAS,uFAAuF,sBAAsB;AAAA,YACtH,YAAY,SAAU,SAAS;AAC7B,qBAAO,SAAS,SAAS,IAAI,UAAU,EAAE,MAAM,sBAAsB,QAAQ;AAAA;AAAA,eAE5E,SAAS;AAAA;AAAA;AAGlB,UAAI,aAAa,SAAU,QAAQ,MAAM;AACvC,eAAO,QAAQ;AAAA,UACb;AAAA,UACA,qBAAqB,sBAAsB,SAAS,uBAAuB;AAAA,WAC1E,SAAU,GAAG;AACd,iBAAO,EAAE;AAAA,WACR,KAAK,WAAY;AAClB,iBAAO,SAAS,QAAQ;AAAA,WACvB,SAAU,WAAW;AACtB,iBAAO,IAAI,SAAS,SAAU,UAAU;AACtC,2BAAe,QAAQ,UAAU,SAAS,SAAU,OAAO;AACzD,uBAAS,QAAQ,UAAU,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAKtD,UAAI,iBAAiB,EAAE;AAEvB,UAAI,aAAa,SAAU,QAAQ;AACjC,YAAI,cAAc,OAAO,IAAI,OAAO;AACpC,YAAI,UAAU,KAAK,aAAa,SAAU,QAAQ;AAChD,cAAI,KAAK,OAAO,QAAQ,OAAO;AAC/B,iBAAO,KAAK,CAAC;AAAA,YACT,MAAM;AAAA,YACN,OAAO,MAAM;AAAA,eACV;AAAA;AAET,eAAO,QAAQ,SAAS,IAAI,SAAS,KAAK,CAAC;AAAA,UACvC,MAAM;AAAA,UACN,OAAO;AAAA,WACN,OAAO,YAAY,SAAS;AAAA;AAEnC,UAAI,oBAAoB,EAAE;AAE1B,UAAI,aAAa,SAAU,QAAQ;AACjC,YAAI,OAAO,iBAAiB;AAC5B,YAAI,KAAK,SAAS,GAAG;AACnB,iBAAO,YAAY,SAAS;AAAA;AAE9B,eAAO,SAAS;AAAA;AAElB,UAAI,mBAAmB,EAAE;AAEzB,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,YAAY,SAAU,MAAM;AAC9B,YAAI;AACF,iBAAO,SAAS,KAAK,KAAK,MAAM;AAAA,iBACzB,KAAP;AACA,iBAAO,SAAS;AAAA;AAAA;AAGpB,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,YAAY,SAAU,MAAM;AAC9B,iBAAO,OAAO,WAAW,KAAK,SAAS,KAAK,KAAK;AAAA;AAEnD,YAAI,WAAW,YAAY;AAC3B,eAAO,IAAI,SAAS,SAAU,UAAU;AACtC,cAAI,SAAS,WAAW;AACtB,mBAAO,KAAK;AAAA,cACV,KAAK;AAAA,cACL,SAAS,SAAU,MAAM;AACvB,uBAAO,SAAS,UAAU;AAAA;AAAA,cAE5B,OAAO,SAAU,GAAG;AAClB,uBAAO,SAAS,SAAS;AAAA;AAAA;AAAA,qBAGpB,WAAW,WAAW;AAC/B,qBAAS,SAAU,QAAQ;AACzB,qBAAO,SAAS,SAAS,KAAK;AAAA;AAAA,iBAE3B;AACL,qBAAS,SAAS,KAAK;AAAA;AAAA,WAExB,KAAK,SAAU,UAAU;AAC1B,iBAAO,SAAS,KAAK,YAAY,aAAa,YAAY,IAAI,SAAU,OAAO;AAC7E,gBAAI,MAAM,SAAS,GAAG;AACpB,kBAAI,WAAW,CAAC;AAAA,gBACZ,MAAM;AAAA,gBACN,OAAO;AAAA;AAEX,qBAAO,SAAS,OAAO;AAAA,mBAClB;AACL,qBAAO;AAAA;AAAA;AAAA;AAAA;AAKf,UAAI,kBAAkB,EAAE;AAExB,UAAI,UAAU,SAAU,QAAQ,eAAe;AAC7C,YAAI,OAAO,WAAW;AACtB,YAAI,KAAK,SAAS,GAAG;AACnB,cAAI,kBAAkB,GAAG,eAAe;AACxC,cAAI,cAAc,sBAAsB,YAAY;AACpD,cAAI,mBAAmB,SAAU,MAAM;AACrC,mBAAO,oBAAoB,YAAY,SAAS,OAAO;AAAA;AAEzD,cAAI,YAAY,cAAc,YAAY,aAAa,oBAAoB,YAAY;AACvF,iBAAO,UAAU;AAAA;AAEnB,eAAO,SAAS;AAAA;AAElB,UAAI,aAAa,EAAE;AAEnB,UAAI,YAAY;AAAA,QACd;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA;AAAA,QAET;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA;AAAA;AAGX,UAAI,aAAa,SAAU,QAAQ;AACjC,YAAI,OAAO,cAAc;AACzB,YAAI,QAAQ,OAAO;AACjB,iBAAO,YAAY,SAAS,MAAM,QAAQ,WAAY;AACpD,mBAAO,SAAS,KAAK;AAAA;AAAA,mBAEd,SAAS,OAAO;AACzB,iBAAO,SAAS;AAAA;AAElB,eAAO,SAAS,KAAK;AAAA;AAEvB,UAAI,gBAAgB,EAAE;AAEtB,UAAI,eAAe,SAAU,KAAK,MAAM,MAAM;AAC5C,YAAI,MAAM,IAAI,UAAU,MAAM;AAC9B,eAAO,QAAQ,QAAQ,IAAI,SAAS,IAAI,SAAS,KAAK,OAAO,SAAS;AAAA;AAExE,UAAI,oBAAoB,SAAU,QAAQ,QAAQ;AAChD,YAAI,MAAM,OAAO;AACjB,YAAI,WAAW,mBAAmB;AAClC,YAAI,OAAO,WAAW,SAAS,KAAK,cAAc,OAAO,WAAW,WAAW,SAAS;AACxF,YAAI,MAAM,SAAS,SAAS,KAAK,IAAI,UAAU,QAAQ,WAAW,SAAS;AAC3E,YAAI,SAAS,SAAS,SAAS,KAAK,IAAI,UAAU,QAAQ,aAAa,SAAS;AAChF,YAAI,MAAM,aAAa,KAAK,QAAQ;AACpC,YAAI,YAAY,aAAa,KAAK,QAAQ;AAC1C,YAAI,QAAQ,aAAa,KAAK,QAAQ;AACtC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,UAAU,SAAU,QAAQ,UAAU;AACxC,eAAO,gBAAgB,SAAS,QAAQ,KAAK,SAAU,OAAO;AAC5D,cAAI,SAAS,kBAAkB,QAAQ;AACvC,iBAAO;AAAA,YACL;AAAA,YACA,UAAU;AAAA,cACR,SAAS,cAAc,WAAW;AAAA,cAClC,MAAM,WAAW,QAAQ,QAAQ,OAAO;AAAA,cACxC,SAAS,iBAAiB,WAAW;AAAA,cACrC,QAAQ,kBAAkB,WAAW;AAAA,cACrC,MAAM;AAAA;AAAA,YAER,SAAS,SAAS,KAAK;AAAA,YACvB,OAAO,EAAE,cAAc,oBAAoB;AAAA;AAAA;AAAA;AAIjD,UAAI,aAAa,EAAE;AAEnB,UAAI,eAAe,SAAU,QAAQ,MAAM;AACzC,eAAO,SAAU,KAAK;AACpB,cAAI,OAAO,IAAI;AACf,cAAI,CAAC,KAAK,IAAI,OAAO;AACnB,mBAAO;AACP,gBAAI;AACJ;AAAA;AAEF,cAAI,kBAAkB,SAAU,KAAK;AACnC,mBAAO,SAAS,KAAK,KAAK,MAAM,OAAO,SAAU,OAAO;AACtD,qBAAO,CAAC,GAAG,KAAK,OAAO,MAAM;AAAA;AAAA;AAGjC,cAAI,cAAc;AAAA,YAChB,MAAM,KAAK,IAAI;AAAA,YACf,MAAM,gBAAgB;AAAA,YACtB,QAAQ,gBAAgB;AAAA,YACxB,KAAK,gBAAgB;AAAA,YACrB,OAAO,gBAAgB;AAAA,YACvB,OAAO,gBAAgB;AAAA;AAEzB,cAAI,cAAc;AAAA,YAChB,MAAM,KAAK,IAAI;AAAA,YACf,QAAQ,KAAK,IAAI,SAAS,UAAa,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS;AAAA;AAEvF,yBAAe,WAAW,QAAQ,aAAa,KAAK,SAAU,OAAO;AACnE,iBAAK,QAAQ,aAAa;AAAA;AAE5B,cAAI;AAAA;AAAA;AAGR,UAAI,cAAc,SAAU,QAAQ;AAClC,YAAI,aAAa,iBAAiB;AAClC,eAAO,WAAW,QAAQ,QAAQ;AAAA;AAEpC,UAAI,iBAAiB,SAAU,MAAM,eAAe;AAClD,YAAI,SAAS,KAAK;AAClB,YAAI,MAAM,OAAO,IAAI,MAAM;AAC3B,eAAO;AAAA,UACL,KAAK;AAAA,YACH,OAAO;AAAA,YACP,MAAM,EAAE,UAAU,EAAE,OAAO;AAAA;AAAA,UAE7B,MAAM,OAAO,KAAK,MAAM;AAAA,UACxB,OAAO,OAAO,MAAM,MAAM;AAAA,UAC1B,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,KAAK,OAAO,IAAI,MAAM;AAAA,UACtB,QAAQ,OAAO,OAAO,GAAG,eAAe,MAAM;AAAA,UAC9C,WAAW,OAAO,UAAU,MAAM;AAAA;AAAA;AAGtC,UAAI,aAAa,SAAU,UAAU,UAAU,QAAQ;AACrD,YAAI,WAAW,CAAC;AAAA,UACZ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA;AAEX,YAAI,cAAc,SAAS,OAAO,KAAK,IAAI,WAAY;AACrD,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA;AAAA,WAER;AACH,YAAI,YAAY,SAAS,MAAM,eAAe,CAAC;AAAA,UAC3C,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,aACJ;AACP,YAAI,gBAAgB,SAAS,KAAK,qBAAqB;AACvD,YAAI,cAAc,eAAe,UAAU;AAC3C,YAAI,WAAW,SAAS;AACxB,YAAI,cAAc,cAAc,KAAK,aAAa;AAClD,YAAI,OAAO;AAAA,UACT,MAAM;AAAA,UACN,OAAO,QAAQ;AAAA,YACb;AAAA,YACA;AAAA,YACA;AAAA,YACA,IAAI;AAAA,cACF,SAAS,OAAO,IAAI,YAAY,SAAS,UAAU;AAAA,cACnD,SAAS,KAAK,IAAI,YAAY,SAAS,OAAO;AAAA,cAC9C,SAAS,QAAQ,IAAI,YAAY,SAAS,UAAU;AAAA,cACpD,SAAS,KAAK,IAAI,YAAY,SAAS,QAAQ;AAAA,cAC/C,SAAS,QAAQ,IAAI,YAAY,SAAS,aAAa;AAAA;AAAA;AAAA;AAI7D,eAAO;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,UACN;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA;AAAA,YAER;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,UAGb;AAAA,UACA,UAAU,SAAU,KAAK,IAAI;AAC3B,gBAAI,OAAO,GAAG;AACd,wBAAY,SAAS,IAAI,SAAS,EAAE,QAAc,KAAK,SAAU,SAAS;AACxE,kBAAI,QAAQ;AAAA;AAAA;AAAA,UAGhB;AAAA;AAAA;AAGJ,UAAI,SAAS,SAAU,QAAQ;AAC7B,YAAI,OAAO,YAAY;AACvB,aAAK,KAAK,SAAU,MAAM;AACxB,cAAI,WAAW,aAAa,QAAQ;AACpC,iBAAO,WAAW,MAAM,UAAU;AAAA,WACjC,KAAK,SAAU,MAAM;AACtB,iBAAO,cAAc,KAAK;AAAA;AAAA;AAI9B,UAAI,oBAAoB,SAAU,OAAM,KAAK;AAC3C,iBAAS,KAAK,YAAY;AAC1B,cAAK,cAAc;AACnB,iBAAS,KAAK,YAAY;AAAA;AAE5B,UAAI,OAAO,SAAU,KAAK;AACxB,YAAI,QAAO,SAAS,cAAc;AAClC,cAAK,SAAS;AACd,cAAK,OAAO;AACZ,cAAK,MAAM;AACX,YAAI,MAAM,SAAS,YAAY;AAC/B,YAAI,eAAe,SAAS,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,OAAO,OAAO,GAAG;AAC9F,0BAAkB,OAAM;AAAA;AAG1B,UAAI,UAAU,SAAU,QAAQ,KAAK;AACnC,eAAO,OAAO,IAAI,UAAU,KAAK;AAAA;AAEnC,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,QAAQ,QAAQ,OAAO,UAAU;AAAA;AAE1C,UAAI,qBAAqB,SAAU,GAAG;AACpC,eAAO,EAAE,WAAW,QAAQ,EAAE,aAAa,SAAS,EAAE,YAAY,SAAS,EAAE,YAAY;AAAA;AAE3F,UAAI,WAAW,SAAU,QAAQ,GAAG;AAClC,YAAI,GAAG;AACL,cAAI,OAAO,QAAQ;AACnB,cAAI,KAAK,KAAK,OAAO;AACnB,gBAAI,WAAW,OAAO,EAAE;AACxB,gBAAI,SAAS,QAAQ;AACnB,qBAAO,UAAU,eAAe,SAAS,IAAI;AAAA;AAAA,iBAE1C;AACL,iBAAK,EAAE;AAAA;AAAA;AAAA;AAIb,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,WAAY;AACjB,mBAAS,QAAQ,gBAAgB;AAAA;AAAA;AAGrC,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,GAAG,SAAS,SAAU,GAAG;AAC9B,cAAI,QAAO,QAAQ,QAAQ,EAAE;AAC7B,cAAI,SAAQ,SAAS,eAAe,IAAI;AACtC,cAAE;AACF,qBAAS,QAAQ;AAAA;AAAA;AAGrB,eAAO,GAAG,WAAW,SAAU,GAAG;AAChC,cAAI,QAAO,gBAAgB;AAC3B,cAAI,SAAQ,EAAE,YAAY,MAAM,mBAAmB,IAAI;AACrD,cAAE;AACF,qBAAS,QAAQ;AAAA;AAAA;AAAA;AAIvB,UAAI,cAAc,SAAU,QAAQ,SAAS;AAC3C,eAAO,GAAG,cAAc;AACxB,eAAO,WAAY;AACjB,iBAAO,OAAO,IAAI,cAAc;AAAA;AAAA;AAGpC,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,SAAU,KAAK;AACpB,cAAI,cAAc,WAAY;AAC5B,mBAAO,IAAI,UAAU,CAAC,OAAO,KAAK,gBAAgB,iBAAiB,QAAQ,OAAO,UAAU,eAAe;AAAA;AAE7G;AACA,iBAAO,YAAY,QAAQ;AAAA;AAAA;AAG/B,UAAI,qBAAqB,SAAU,QAAQ;AACzC,eAAO,SAAU,KAAK;AACpB,cAAI,cAAc,WAAY;AAC5B,mBAAO,IAAI,YAAY,iBAAiB,QAAQ,OAAO,UAAU,eAAe;AAAA;AAElF;AACA,iBAAO,YAAY,QAAQ;AAAA;AAAA;AAG/B,UAAI,oBAAoB,SAAU,QAAQ;AACxC,eAAO,SAAU,KAAK;AACpB,cAAI,aAAa,SAAU,UAAS;AAClC,mBAAO,SAAS,aAAY,oBAAoB,OAAO,UAAU;AAAA;AAEnE,cAAI,UAAU,OAAO,IAAI,WAAW,OAAO,UAAU;AACrD,cAAI,YAAY,CAAC,WAAW;AAC5B,iBAAO,YAAY,QAAQ,SAAU,GAAG;AACtC,mBAAO,IAAI,YAAY,CAAC,WAAW,EAAE;AAAA;AAAA;AAAA;AAK3C,UAAI,WAAW,SAAU,QAAQ;AAC/B,eAAO,WAAW,WAAW,WAAY;AACvC,cAAI,aAAa,SAAS;AACxB,mBAAO,KAAK,uBAAuB,EAAE,YAAY;AAAA,iBAC5C;AACL,uBAAW;AAAA;AAAA;AAAA;AAKjB,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,YAAY,UAAU,IAAI,WAAY;AAC3C,iBAAO,YAAY;AAAA;AAAA;AAIvB,UAAI,eAAe,SAAU,QAAQ;AACnC,eAAO,GAAG,SAAS,gBAAgB,QAAQ;AAAA,UACzC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,WAAW;AAAA,UACrB,SAAS,kBAAkB;AAAA;AAE7B,eAAO,GAAG,SAAS,UAAU,YAAY;AAAA,UACvC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,iBAAiB;AAAA,UAC3B,SAAS,mBAAmB;AAAA;AAE9B,eAAO,GAAG,SAAS,UAAU,UAAU;AAAA,UACrC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,WAAY;AACpB,mBAAO,OAAO;AAAA;AAAA,UAEhB,SAAS,kBAAkB;AAAA;AAAA;AAG/B,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,GAAG,SAAS,YAAY,YAAY;AAAA,UACzC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,iBAAiB;AAAA,UAC3B,SAAS,mBAAmB;AAAA;AAE9B,eAAO,GAAG,SAAS,YAAY,QAAQ;AAAA,UACrC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,UAAU,WAAW;AAAA;AAEvB,eAAO,GAAG,SAAS,YAAY,UAAU;AAAA,UACvC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,mBAAO,OAAO;AAAA;AAAA,UAEhB,SAAS,kBAAkB;AAAA;AAAA;AAG/B,UAAI,mBAAmB,SAAU,QAAQ;AACvC,YAAI,SAAS;AACb,YAAI,SAAS;AACb,eAAO,GAAG,SAAS,eAAe,QAAQ;AAAA,UACxC,QAAQ,SAAU,SAAS;AACzB,mBAAO,SAAS,OAAO,IAAI,WAAW,SAAS,QAAQ,SAAS;AAAA;AAAA;AAAA;AAItE,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,YAAI,yBAAyB,SAAU,SAAQ;AAC7C,kBAAO,UAAU,SAAS;AAAA;AAE5B,YAAI,cAAc,SAAU,WAAW;AACrC,cAAI,OAAO,OAAO,UAAU;AAC5B,oBAAU,YAAY,CAAC,iBAAiB,QAAQ;AAChD,iBAAO;AAAA;AAET,YAAI,cAAc,SAAU,OAAO;AACjC,cAAI,SAAS,iBAAiB;AAC9B,cAAI,WAAW,mBAAmB;AAClC,cAAI,CAAC,UAAU,UAAU;AACvB,gBAAI,OAAO,cAAc,OAAO,WAAW;AAC3C,mBAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO;AAAA,iBACzC;AACL,mBAAO,SAAS;AAAA;AAAA;AAGpB,eAAO,GAAG,SAAS,eAAe,aAAa;AAAA,UAC7C,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,kBAAkB;AAAA;AAAA,UAE7B,OAAO;AAAA,UACP,WAAW,SAAU,MAAM;AACzB,mBAAO,CAAC,CAAC,iBAAiB,QAAQ,SAAS,kBAAkB;AAAA;AAAA,UAE/D,WAAW,WAAY;AACrB,gBAAI,MAAM,iBAAiB;AAC3B,mBAAO,CAAC,CAAC,MAAM,QAAQ,OAAO;AAAA;AAAA,UAEhC,UAAU;AAAA,YACR;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,cACT,SAAS,SAAU,WAAW;AAC5B,oBAAI,OAAO,OAAO,UAAU;AAC5B,0BAAU,UAAU,CAAC,CAAC,iBAAiB,QAAQ;AAC/C,uBAAO,kBAAkB,QAAQ;AAAA;AAAA,cAEnC,UAAU,SAAU,SAAS;AAC3B,oBAAI,QAAQ,QAAQ;AACpB,oBAAI,OAAO,YAAY;AACvB,oBAAI,cAAc;AAAA,kBAChB,MAAM;AAAA,kBACN,QAAQ;AAAA;AAEV,qBAAK,QAAQ,aAAa;AAAA,kBACxB,MAAM;AAAA,kBACN;AAAA,kBACA,OAAO,SAAS;AAAA,kBAChB,KAAK,SAAS;AAAA,kBACd,QAAQ,SAAS;AAAA,kBACjB,OAAO,SAAS;AAAA;AAElB,uCAAuB;AACvB,wBAAQ;AAAA;AAAA;AAAA,YAGZ;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,cACT,UAAU,SAAU,SAAS;AAC3B,uBAAO;AACP,wBAAQ;AAAA;AAAA;AAAA,YAGZ;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,cACT,UAAU,SAAU,SAAS;AAC3B,iCAAiB;AACjB,wBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAOlB,wBAAmB;AACjB,iBAAS,IAAI,QAAQ,SAAU,QAAQ;AACrC,uBAAa;AACb,yBAAe;AACf,2BAAiB;AACjB,+BAAqB;AACrB,yBAAe;AACf,mBAAS;AACT,gBAAM;AAAA;AAAA;AAIV;AAAA;AAAA;AAAA;;;AC1wCJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,+BAAQ;", "names": []}