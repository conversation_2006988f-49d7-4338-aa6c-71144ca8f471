import {
  EffectScope,
  ITERATE_KEY,
  ReactiveEffect,
  computed,
  customRef,
  deferredComputed,
  effect,
  effectScope,
  enableTracking,
  getCurrentScope,
  init_reactivity_esm_bundler,
  isProxy,
  isReactive,
  isReadonly,
  isRef,
  isShallow,
  markRaw,
  onScopeDispose,
  pauseTracking,
  proxyRefs,
  reactive,
  readonly,
  ref,
  resetTracking,
  shallowReactive,
  shallowReadonly,
  shallowRef,
  stop,
  toRaw,
  toRef,
  toRefs,
  track,
  trigger,
  triggerRef,
  unref
} from "./chunk-XLSXXTZ3.js";
import "./chunk-WC6BDPVA.js";

// dep:@vue_reactivity
init_reactivity_esm_bundler();
export {
  EffectScope,
  ITERATE_KEY,
  ReactiveEffect,
  computed,
  customRef,
  deferredComputed,
  effect,
  effectScope,
  enableTracking,
  getCurrentScope,
  isProxy,
  isReactive,
  isReadonly,
  isRef,
  isShallow,
  markRaw,
  onScopeDispose,
  pauseTracking,
  proxyRefs,
  reactive,
  readonly,
  ref,
  resetTracking,
  shallowReactive,
  shallowReadonly,
  shallowRef,
  stop,
  toRaw,
  toRef,
  toRefs,
  track,
  trigger,
  triggerRef,
  unref
};
//# sourceMappingURL=@vue_reactivity.js.map
