<template>
    <el-dialog title="交割清单" v-model="open" append-to-body width="950px" :before-close="cancel">
        <div class="tip-msg">交割整体进程：未开始 / 进行中 / 已完结</div>
        <el-table :data="dataList" maxheight="50vh">
            <el-table-column label="所属服务" align="center" prop="aa" />
            <el-table-column label="所属模块" align="center" prop="bb" />
            <el-table-column label="状态" align="center" prop="status">
                <template #default="{ row }">
                    <div>
                        <el-icon :color="colorEnum[row.status]">
                            <Close v-if="row.status == 0" />
                            <Check v-if="row.status == 1" />
                            <MoreFilled v-if="row.status == 2" />
                        </el-icon>
                        {{ statusEnum[row.status] }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="时间" align="center" prop="time" />
            <el-table-column label="影响范围/笔" align="center" prop="count" />
        </el-table>
    </el-dialog>
</template>

<script setup>
const statusEnum = { 0: '禁用失败', 1: '已禁用', 2: '禁用中' }
const colorEnum = { 0: '#ff005a', 1: '#24a824', 2: '#527df2' }
const open = ref(false)
const updateStep = ref(null)
const dataList = ref([
    { aa: '回款服务', bb: '主动还款模块', status: 1, time: '2024-01-01 12.32', count: '14890' },
    { aa: '回款服务', bb: '维码还款模块', status: 1, time: '2024-02-01 11:42', count: '29780' },
    { aa: '回款服务', bb: '自动代扣模块', status: 1, time: '2024-03-21 15:32', count: '44670' },
    { aa: '回款服务', bb: '人工代扣模块', status: 1, time: '2024-04-12 17:14', count: '59560' },
    { aa: '回款服务', bb: '线下还款通道', status: 0, time: '2024-05-12 13:16', count: '74450' },
    { aa: '贷后服务', bb: '内部电催模块', status: 1, time: '2024-06-21 14:53', count: '89340' },
    { aa: '贷后服务', bb: '委外悃收模块', status: 1, time: '2024-07-05 13:32', count: '10423' },
    { aa: '贷后服务', bb: '法律诉讼模块', status: 1, time: '2024-07-30 14:54', count: '11912' },
    { aa: '贷后服务', bb: '调解中心模块', status: 1, time: '2025-01-26 12:42', count: '13401' },
    { aa: '贷后服务', bb: '案件仲裁模块', status: 1, time: '2025-01-28 11:54', count: '24890' },
    { aa: '贷后服务', bb: '强制执行模块', status: 2, time: '2025-02-17 14:32', count: '49780' },
    { aa: '数据报送', bb: '征信报送接口', status: 1, time: '2025-03-16 15:43', count: '74670' },
    { aa: '数据报送', bb: '征信异议处理', status: 1, time: '2025-03-17 09:15', count: '99560' },
    { aa: '计费与数据访问', bb: '计息与成本统计', status: 1, time: '2025-06-01 12.00', count: '12445' },
    { aa: '计费与数据访问', bb: '人员数据访问', status: 1, time: '2025-06-01 12:00', count: '14934' },
])
function openDialog(data) {
    open.value = true
    updateStep.value = data.updateStep
}
function cancel() {
    open.value = false
}
defineExpose({ openDialog })
</script>

<style lang="scss" scoped>
.tip-msg {
    margin-bottom: 20px;
    font-weight: bold;
}
</style>