<template>
  <div class="app-container">
    <el-radio-group class="mb20" v-model="activeRadio" @change="resetQuery">
      <el-radio-button v-if="checkPermi(['risk:callLog:callRecord'])" label="通话录音" value="通话录音" />
      <el-radio-button v-if="checkPermi(['risk:callLog:uploadRecord'])" label="上传录音" value="上传录音" />
    </el-radio-group>
    <CallRecord v-if="activeRadio == '通话录音' && checkPermi(['risk:callLog:callRecord'])" />
    <UploadRecord v-if="activeRadio == '上传录音' && checkPermi(['risk:callLog:uploadRecord'])" />
  </div>
</template>

<script setup name="Calllog">
import { checkPermi } from "@/utils/permission";
import CallRecord from './tabCom/callRecord';
import UploadRecord from './tabCom/uploadRecord';
const activeRadio = ref('通话录音')
</script>

<style lang="scss" scoped></style>
