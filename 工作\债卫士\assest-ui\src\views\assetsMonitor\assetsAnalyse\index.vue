<template>
    <div class="app-container">
        <el-tabs v-model="activeTab" class="boder-tab" @tab-click="handleTabClick" type="border-card">
            <el-tab-pane label="已转让资产" name="0" />
            <el-tab-pane label="可转让资产" name="1" />
        </el-tabs>
        <div class="main-content mt20" v-loading="loading">
            <LeftSideTree :data="treeData" :nodeClick="handleTabClick" />
            <div class="content-container">
                <div class="query-area">
                    <el-form :model="queryParams" label-width="auto" inline>
                        <el-form-item prop="batchNums" label="资产包名称">
                            <el-tree-select v-model="queryParams.batchNums" :data="batchNumsOption" multiple
                                collapse-tags :props="defaultProps" collapse-tags-tooltip :render-after-expand="false"
                                show-checkbox node-key="batchNum" @check="handleCheck" style="width:320px"
                                placeholder="请选择资产包名称" />
                        </el-form-item>
                        <el-form-item prop="recordDate" label="项目名称">
                            <el-select v-model="queryParams.recordDate" style="width:320px" placeholder="请选择项目名称">
                                <el-option />
                            </el-select>
                        </el-form-item>
                        <el-form-item prop="teamIdList" label="机构">
                            <el-select v-model="queryParams.teamIdList" style="width:320px" placeholder="请选择机构">
                                <el-option />
                            </el-select>
                        </el-form-item>
                    </el-form>
                    <div class="text-center">
                        <el-button :loading="loading" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
                        <el-button :loading="loading" @click="antiShake(resetQuery)">重置</el-button>
                    </div>
                </div>
                <DataTable v-loading="loading" :dataTable="dataTable" :key="dataTable" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { overviewAsset } from '@/api/assetAnalysis/analysisReport';
import LeftSideTree from '@/components/leftSideTree/index';
import DataTable from '@/views/assetAnalysis/analysisReport/components/dataTable';
const dataTable = ref({})
const queryParams = ref({})
const loading = ref(false)
const treeData = ref([
    {
        id: 1,
        label: '消费贷',
        children: [
            { id: 11, label: '沃享极速分期', },
            { id: 12, label: '智鹿秒批贷', },
            { id: 13, label: '风云循环贷', },
            { id: 14, label: '信用付 PLUS', },
            { id: 15, label: '零零花青春版', },
            { id: 16, label: '教育智分期', },
            { id: 17, label: '家装无忧贷', },
            { id: 18, label: '旅行白条', },
            { id: 19, label: '医享付', },
            { id: 111, label: '绿享贷', },
            { id: 112, label: '乐享贷' },
        ]
    },
    {
        id: 2,
        label: '信用贷',
        children: [
            { id: 20, label: '白领优享贷' },
            { id: 21, label: '业主尊享贷' },
            { id: 22, label: '新市民安居贷' },
            { id: 23, label: '银发暖心贷' },
            { id: 24, label: '创客启航贷' },
            { id: 25, label: 'AI 智能卡' },
            { id: 26, label: '数据宝贷' },
            { id: 27, label: '仲思健康贷' },
            { id: 28, label: '跨境速汇贷' },
            { id: 29, label: '社区普惠贷' },
        ]
    },
]);
getData()
function getData() {
    const batchNums = ["CIB202501JR0001", "CIB202501cc0001", "CIB202404MS0008", "CIB202404MS0007", "CIB202404MS0006", "CIB202404MS0005", "CIB202404MS0004", "CIB202404MS0003", "CIB202404MS0002", "CIB202404BP0015", "CIB202404BP0014", "CIB202404BP0013", "CIB202404BP0011", "COMM202501CL0003", "COMM202501CL0002", "COMM202501CL0001", "CMB202501JR0003", "CMB202501JR0002", "CMB202501JR0001", "CMB202403BP0039", "CMB202403BP0036", "CMB202403BP0032", "CMB202403BP0031", "CMB202403BP0030", "CMB202403BP0029", "CMB202403BP0028", "CMB202403BP0027", "CMB202403BP0026", "CMB202403BP0025", "CMB202403BP0024", "CMB202403BP0023", "CMB202403BP0008", "CMB202403BP0007", "CZBANK202501CL0004", "CZBANK202501CL0003", "CZBANK202501CL0002", "CZBANK202501CL0001", "zgyh202501BP0002", "ABC202403CL0007", "ABC202403CC0001", "ABC202403CL0005", "ABCD202403CL0001", "zsyhfoshan202502CSD0002", "zsyhfoshan202502CSD0001", "zsyhfoshan202502CC0007", "zsyhfoshan202502CC0006", "zsyhfoshan202502CC0005", "zsyhfoshan202502CC0004", "CITIC202502CC0002", "CITIC202502BP0004", "CITIC202502CC0001", "CITIC202502BP0003", "CITIC202502BP0002", "CITIC202502BP0001", "CITIC202501CC0006", "CITIC202501CC0004", "CITIC202501CC0003", "CITIC202403CC0002", "CITIC202402CC0001", "gzyh202501CL0006", "gzyh202501CL0005", "gzyh202501CL0004", "gzyh202501CL0003", "gzyh202501MS0003", "gzyh202501MS0002", "gzyh202501MS0001", "gzyh202501CC0004", "gzyh202501CC0003", "gzyh202501CC0002", "gzyh202501CC0001", "gzyh202501CL0002", "CMBC202502CSD0006", "EGBANK202403CL0014", "EGBANK202403CL0013", "EGBANK202403CL0012", "EGBANK202403CL0011", "EGBANK202403CL0003", "CEB202404CL0001", "BOHAIB202501CL0005", "BOHAIB202501CL0004", "BOHAIB202501CL0001", "YX202502BP0001", "YX202501CL0002"]
    overviewAsset({ batchNums }).then(res => {
        dataTable.value = res.data
    }).finally(() => loading.value = false)
}

function handleTabClick() {
    loading.value = true
    setTimeout(() => {
        loading.value = false
    }, 1000)
}
</script>

<style lang="scss" scoped>
.main-content {
    display: flex;
    gap: 20px;

    .tree-container {
        flex: 1;
    }

    .content-container {
        flex: 5;
    }
}

.boder-tab {
    border-bottom: none;

    :deep(.el-tabs__content) {
        padding: 0;
    }
}
</style>