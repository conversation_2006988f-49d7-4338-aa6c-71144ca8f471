{"version": 3, "sources": ["../tinymce/plugins/emoticons/plugin.js", "../tinymce/plugins/emoticons/index.js", "dep:tinymce_plugins_emoticons"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var eq = function (t) {\n      return function (a) {\n        return t === a;\n      };\n    };\n    var isNull = eq(null);\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOr<PERSON>ie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var exists = function (xs, pred) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    var map$1 = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each$1 = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var last = function (fn, rate) {\n      var timer = null;\n      var cancel = function () {\n        if (!isNull(timer)) {\n          clearTimeout(timer);\n          timer = null;\n        }\n      };\n      var throttle = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        cancel();\n        timer = setTimeout(function () {\n          timer = null;\n          fn.apply(null, args);\n        }, rate);\n      };\n      return {\n        cancel: cancel,\n        throttle: throttle\n      };\n    };\n\n    var insertEmoticon = function (editor, ch) {\n      editor.insertContent(ch);\n    };\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var keys = Object.keys;\n    var hasOwnProperty = Object.hasOwnProperty;\n    var each = function (obj, f) {\n      var props = keys(obj);\n      for (var k = 0, len = props.length; k < len; k++) {\n        var i = props[k];\n        var x = obj[i];\n        f(x, i);\n      }\n    };\n    var map = function (obj, f) {\n      return tupleMap(obj, function (x, i) {\n        return {\n          k: i,\n          v: f(x, i)\n        };\n      });\n    };\n    var tupleMap = function (obj, f) {\n      var r = {};\n      each(obj, function (x, i) {\n        var tuple = f(x, i);\n        r[tuple.k] = tuple.v;\n      });\n      return r;\n    };\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n\n    var shallow = function (old, nu) {\n      return nu;\n    };\n    var baseMerge = function (merger) {\n      return function () {\n        var objects = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          objects[_i] = arguments[_i];\n        }\n        if (objects.length === 0) {\n          throw new Error('Can\\'t merge zero objects');\n        }\n        var ret = {};\n        for (var j = 0; j < objects.length; j++) {\n          var curObject = objects[j];\n          for (var key in curObject) {\n            if (has(curObject, key)) {\n              ret[key] = merger(ret[key], curObject[key]);\n            }\n          }\n        }\n        return ret;\n      };\n    };\n    var merge = baseMerge(shallow);\n\n    var singleton = function (doRevoke) {\n      var subject = Cell(Optional.none());\n      var revoke = function () {\n        return subject.get().each(doRevoke);\n      };\n      var clear = function () {\n        revoke();\n        subject.set(Optional.none());\n      };\n      var isSet = function () {\n        return subject.get().isSome();\n      };\n      var get = function () {\n        return subject.get();\n      };\n      var set = function (s) {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear: clear,\n        isSet: isSet,\n        get: get,\n        set: set\n      };\n    };\n    var value = function () {\n      var subject = singleton(noop);\n      var on = function (f) {\n        return subject.get().each(f);\n      };\n      return __assign(__assign({}, subject), { on: on });\n    };\n\n    var checkRange = function (str, substr, start) {\n      return substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    };\n    var contains = function (str, substr) {\n      return str.indexOf(substr) !== -1;\n    };\n    var startsWith = function (str, prefix) {\n      return checkRange(str, prefix, 0);\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.Resource');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Promise');\n\n    var DEFAULT_ID = 'tinymce.plugins.emoticons';\n    var getEmoticonDatabase = function (editor) {\n      return editor.getParam('emoticons_database', 'emojis', 'string');\n    };\n    var getEmoticonDatabaseUrl = function (editor, pluginUrl) {\n      var database = getEmoticonDatabase(editor);\n      return editor.getParam('emoticons_database_url', pluginUrl + '/js/' + database + editor.suffix + '.js', 'string');\n    };\n    var getEmoticonDatabaseId = function (editor) {\n      return editor.getParam('emoticons_database_id', DEFAULT_ID, 'string');\n    };\n    var getAppendedEmoticons = function (editor) {\n      return editor.getParam('emoticons_append', {}, 'object');\n    };\n    var getEmotionsImageUrl = function (editor) {\n      return editor.getParam('emoticons_images_url', 'https://twemoji.maxcdn.com/v/13.0.1/72x72/', 'string');\n    };\n\n    var ALL_CATEGORY = 'All';\n    var categoryNameMap = {\n      symbols: 'Symbols',\n      people: 'People',\n      animals_and_nature: 'Animals and Nature',\n      food_and_drink: 'Food and Drink',\n      activity: 'Activity',\n      travel_and_places: 'Travel and Places',\n      objects: 'Objects',\n      flags: 'Flags',\n      user: 'User Defined'\n    };\n    var translateCategory = function (categories, name) {\n      return has(categories, name) ? categories[name] : name;\n    };\n    var getUserDefinedEmoticons = function (editor) {\n      var userDefinedEmoticons = getAppendedEmoticons(editor);\n      return map(userDefinedEmoticons, function (value) {\n        return __assign({\n          keywords: [],\n          category: 'user'\n        }, value);\n      });\n    };\n    var initDatabase = function (editor, databaseUrl, databaseId) {\n      var categories = value();\n      var all = value();\n      var emojiImagesUrl = getEmotionsImageUrl(editor);\n      var getEmoji = function (lib) {\n        if (startsWith(lib.char, '<img')) {\n          return lib.char.replace(/src=\"([^\"]+)\"/, function (match, url) {\n            return 'src=\"' + emojiImagesUrl + url + '\"';\n          });\n        } else {\n          return lib.char;\n        }\n      };\n      var processEmojis = function (emojis) {\n        var cats = {};\n        var everything = [];\n        each(emojis, function (lib, title) {\n          var entry = {\n            title: title,\n            keywords: lib.keywords,\n            char: getEmoji(lib),\n            category: translateCategory(categoryNameMap, lib.category)\n          };\n          var current = cats[entry.category] !== undefined ? cats[entry.category] : [];\n          cats[entry.category] = current.concat([entry]);\n          everything.push(entry);\n        });\n        categories.set(cats);\n        all.set(everything);\n      };\n      editor.on('init', function () {\n        global$2.load(databaseId, databaseUrl).then(function (emojis) {\n          var userEmojis = getUserDefinedEmoticons(editor);\n          processEmojis(merge(emojis, userEmojis));\n        }, function (err) {\n          console.log('Failed to load emoticons: ' + err);\n          categories.set({});\n          all.set([]);\n        });\n      });\n      var listCategory = function (category) {\n        if (category === ALL_CATEGORY) {\n          return listAll();\n        }\n        return categories.get().bind(function (cats) {\n          return Optional.from(cats[category]);\n        }).getOr([]);\n      };\n      var listAll = function () {\n        return all.get().getOr([]);\n      };\n      var listCategories = function () {\n        return [ALL_CATEGORY].concat(keys(categories.get().getOr({})));\n      };\n      var waitForLoad = function () {\n        if (hasLoaded()) {\n          return global.resolve(true);\n        } else {\n          return new global(function (resolve, reject) {\n            var numRetries = 15;\n            var interval = global$1.setInterval(function () {\n              if (hasLoaded()) {\n                global$1.clearInterval(interval);\n                resolve(true);\n              } else {\n                numRetries--;\n                if (numRetries < 0) {\n                  console.log('Could not load emojis from url: ' + databaseUrl);\n                  global$1.clearInterval(interval);\n                  reject(false);\n                }\n              }\n            }, 100);\n          });\n        }\n      };\n      var hasLoaded = function () {\n        return categories.isSet() && all.isSet();\n      };\n      return {\n        listCategories: listCategories,\n        hasLoaded: hasLoaded,\n        waitForLoad: waitForLoad,\n        listAll: listAll,\n        listCategory: listCategory\n      };\n    };\n\n    var emojiMatches = function (emoji, lowerCasePattern) {\n      return contains(emoji.title.toLowerCase(), lowerCasePattern) || exists(emoji.keywords, function (k) {\n        return contains(k.toLowerCase(), lowerCasePattern);\n      });\n    };\n    var emojisFrom = function (list, pattern, maxResults) {\n      var matches = [];\n      var lowerCasePattern = pattern.toLowerCase();\n      var reachedLimit = maxResults.fold(function () {\n        return never;\n      }, function (max) {\n        return function (size) {\n          return size >= max;\n        };\n      });\n      for (var i = 0; i < list.length; i++) {\n        if (pattern.length === 0 || emojiMatches(list[i], lowerCasePattern)) {\n          matches.push({\n            value: list[i].char,\n            text: list[i].title,\n            icon: list[i].char\n          });\n          if (reachedLimit(matches.length)) {\n            break;\n          }\n        }\n      }\n      return matches;\n    };\n\n    var patternName = 'pattern';\n    var open = function (editor, database) {\n      var initialState = {\n        pattern: '',\n        results: emojisFrom(database.listAll(), '', Optional.some(300))\n      };\n      var currentTab = Cell(ALL_CATEGORY);\n      var scan = function (dialogApi) {\n        var dialogData = dialogApi.getData();\n        var category = currentTab.get();\n        var candidates = database.listCategory(category);\n        var results = emojisFrom(candidates, dialogData[patternName], category === ALL_CATEGORY ? Optional.some(300) : Optional.none());\n        dialogApi.setData({ results: results });\n      };\n      var updateFilter = last(function (dialogApi) {\n        scan(dialogApi);\n      }, 200);\n      var searchField = {\n        label: 'Search',\n        type: 'input',\n        name: patternName\n      };\n      var resultsField = {\n        type: 'collection',\n        name: 'results'\n      };\n      var getInitialState = function () {\n        var body = {\n          type: 'tabpanel',\n          tabs: map$1(database.listCategories(), function (cat) {\n            return {\n              title: cat,\n              name: cat,\n              items: [\n                searchField,\n                resultsField\n              ]\n            };\n          })\n        };\n        return {\n          title: 'Emoticons',\n          size: 'normal',\n          body: body,\n          initialData: initialState,\n          onTabChange: function (dialogApi, details) {\n            currentTab.set(details.newTabName);\n            updateFilter.throttle(dialogApi);\n          },\n          onChange: updateFilter.throttle,\n          onAction: function (dialogApi, actionData) {\n            if (actionData.name === 'results') {\n              insertEmoticon(editor, actionData.value);\n              dialogApi.close();\n            }\n          },\n          buttons: [{\n              type: 'cancel',\n              text: 'Close',\n              primary: true\n            }]\n        };\n      };\n      var dialogApi = editor.windowManager.open(getInitialState());\n      dialogApi.focus(patternName);\n      if (!database.hasLoaded()) {\n        dialogApi.block('Loading emoticons...');\n        database.waitForLoad().then(function () {\n          dialogApi.redial(getInitialState());\n          updateFilter.throttle(dialogApi);\n          dialogApi.focus(patternName);\n          dialogApi.unblock();\n        }).catch(function (_err) {\n          dialogApi.redial({\n            title: 'Emoticons',\n            body: {\n              type: 'panel',\n              items: [{\n                  type: 'alertbanner',\n                  level: 'error',\n                  icon: 'warning',\n                  text: '<p>Could not load emoticons</p>'\n                }]\n            },\n            buttons: [{\n                type: 'cancel',\n                text: 'Close',\n                primary: true\n              }],\n            initialData: {\n              pattern: '',\n              results: []\n            }\n          });\n          dialogApi.focus(patternName);\n          dialogApi.unblock();\n        });\n      }\n    };\n\n    var register$1 = function (editor, database) {\n      editor.addCommand('mceEmoticons', function () {\n        return open(editor, database);\n      });\n    };\n\n    var setup = function (editor) {\n      editor.on('PreInit', function () {\n        editor.parser.addAttributeFilter('data-emoticon', function (nodes) {\n          each$1(nodes, function (node) {\n            node.attr('data-mce-resize', 'false');\n            node.attr('data-mce-placeholder', '1');\n          });\n        });\n      });\n    };\n\n    var init = function (editor, database) {\n      editor.ui.registry.addAutocompleter('emoticons', {\n        ch: ':',\n        columns: 'auto',\n        minChars: 2,\n        fetch: function (pattern, maxResults) {\n          return database.waitForLoad().then(function () {\n            var candidates = database.listAll();\n            return emojisFrom(candidates, pattern, Optional.some(maxResults));\n          });\n        },\n        onAction: function (autocompleteApi, rng, value) {\n          editor.selection.setRng(rng);\n          editor.insertContent(value);\n          autocompleteApi.hide();\n        }\n      });\n    };\n\n    var register = function (editor) {\n      var onAction = function () {\n        return editor.execCommand('mceEmoticons');\n      };\n      editor.ui.registry.addButton('emoticons', {\n        tooltip: 'Emoticons',\n        icon: 'emoji',\n        onAction: onAction\n      });\n      editor.ui.registry.addMenuItem('emoticons', {\n        text: 'Emoticons...',\n        icon: 'emoji',\n        onAction: onAction\n      });\n    };\n\n    function Plugin () {\n      global$3.add('emoticons', function (editor, pluginUrl) {\n        var databaseUrl = getEmoticonDatabaseUrl(editor, pluginUrl);\n        var databaseId = getEmoticonDatabaseId(editor);\n        var database = initDatabase(editor, databaseUrl, databaseId);\n        register$1(editor, database);\n        register(editor);\n        init(editor, database);\n        setup(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"emoticons\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/emoticons')\n//   ES2015:\n//     import 'tinymce/plugins/emoticons'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/emoticons/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,KAAK,SAAU,GAAG;AACpB,eAAO,SAAU,GAAG;AAClB,iBAAO,MAAM;AAAA;AAAA;AAGjB,UAAI,SAAS,GAAG;AAEhB,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,QAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,QAAO;AAC1B,eAAO,WAAU,QAAQ,WAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,QAAQ,SAAU,IAAI,GAAG;AAC3B,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,IAAI,GAAG;AAC5B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAIT,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,SAAQ;AACZ,YAAI,MAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,mBAAQ;AAAA;AAEV,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,OAAO,SAAU,IAAI,MAAM;AAC7B,YAAI,QAAQ;AACZ,YAAI,SAAS,WAAY;AACvB,cAAI,CAAC,OAAO,QAAQ;AAClB,yBAAa;AACb,oBAAQ;AAAA;AAAA;AAGZ,YAAI,WAAW,WAAY;AACzB,cAAI,OAAO;AACX,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,iBAAK,MAAM,UAAU;AAAA;AAEvB;AACA,kBAAQ,WAAW,WAAY;AAC7B,oBAAQ;AACR,eAAG,MAAM,MAAM;AAAA,aACd;AAAA;AAEL,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,iBAAiB,SAAU,QAAQ,IAAI;AACzC,eAAO,cAAc;AAAA;AAGvB,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,OAAO,OAAO;AAClB,UAAI,iBAAiB,OAAO;AAC5B,UAAI,OAAO,SAAU,KAAK,GAAG;AAC3B,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,IAAI;AACZ,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,MAAM,SAAU,KAAK,GAAG;AAC1B,eAAO,SAAS,KAAK,SAAU,GAAG,GAAG;AACnC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,GAAG,EAAE,GAAG;AAAA;AAAA;AAAA;AAId,UAAI,WAAW,SAAU,KAAK,GAAG;AAC/B,YAAI,IAAI;AACR,aAAK,KAAK,SAAU,GAAG,GAAG;AACxB,cAAI,QAAQ,EAAE,GAAG;AACjB,YAAE,MAAM,KAAK,MAAM;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAGlC,UAAI,UAAU,SAAU,KAAK,IAAI;AAC/B,eAAO;AAAA;AAET,UAAI,YAAY,SAAU,QAAQ;AAChC,eAAO,WAAY;AACjB,cAAI,UAAU;AACd,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,oBAAQ,MAAM,UAAU;AAAA;AAE1B,cAAI,QAAQ,WAAW,GAAG;AACxB,kBAAM,IAAI,MAAM;AAAA;AAElB,cAAI,MAAM;AACV,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAI,YAAY,QAAQ;AACxB,qBAAS,OAAO,WAAW;AACzB,kBAAI,IAAI,WAAW,MAAM;AACvB,oBAAI,OAAO,OAAO,IAAI,MAAM,UAAU;AAAA;AAAA;AAAA;AAI5C,iBAAO;AAAA;AAAA;AAGX,UAAI,QAAQ,UAAU;AAEtB,UAAI,YAAY,SAAU,UAAU;AAClC,YAAI,UAAU,KAAK,SAAS;AAC5B,YAAI,SAAS,WAAY;AACvB,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,YAAI,QAAQ,WAAY;AACtB;AACA,kBAAQ,IAAI,SAAS;AAAA;AAEvB,YAAI,QAAQ,WAAY;AACtB,iBAAO,QAAQ,MAAM;AAAA;AAEvB,YAAI,MAAM,WAAY;AACpB,iBAAO,QAAQ;AAAA;AAEjB,YAAI,MAAM,SAAU,GAAG;AACrB;AACA,kBAAQ,IAAI,SAAS,KAAK;AAAA;AAE5B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,QAAQ,WAAY;AACtB,YAAI,UAAU,UAAU;AACxB,YAAI,KAAK,SAAU,GAAG;AACpB,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,eAAO,SAAS,SAAS,IAAI,UAAU,EAAE;AAAA;AAG3C,UAAI,aAAa,SAAU,KAAK,QAAQ,OAAO;AAC7C,eAAO,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,YAAY;AAAA;AAEtG,UAAI,WAAW,SAAU,KAAK,QAAQ;AACpC,eAAO,IAAI,QAAQ,YAAY;AAAA;AAEjC,UAAI,aAAa,SAAU,KAAK,QAAQ;AACtC,eAAO,WAAW,KAAK,QAAQ;AAAA;AAGjC,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,aAAa;AACjB,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,OAAO,SAAS,sBAAsB,UAAU;AAAA;AAEzD,UAAI,yBAAyB,SAAU,QAAQ,WAAW;AACxD,YAAI,WAAW,oBAAoB;AACnC,eAAO,OAAO,SAAS,0BAA0B,YAAY,SAAS,WAAW,OAAO,SAAS,OAAO;AAAA;AAE1G,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,eAAO,OAAO,SAAS,yBAAyB,YAAY;AAAA;AAE9D,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,OAAO,SAAS,oBAAoB,IAAI;AAAA;AAEjD,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,OAAO,SAAS,wBAAwB,8CAA8C;AAAA;AAG/F,UAAI,eAAe;AACnB,UAAI,kBAAkB;AAAA,QACpB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,mBAAmB;AAAA,QACnB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,MAAM;AAAA;AAER,UAAI,oBAAoB,SAAU,YAAY,MAAM;AAClD,eAAO,IAAI,YAAY,QAAQ,WAAW,QAAQ;AAAA;AAEpD,UAAI,0BAA0B,SAAU,QAAQ;AAC9C,YAAI,uBAAuB,qBAAqB;AAChD,eAAO,IAAI,sBAAsB,SAAU,QAAO;AAChD,iBAAO,SAAS;AAAA,YACd,UAAU;AAAA,YACV,UAAU;AAAA,aACT;AAAA;AAAA;AAGP,UAAI,eAAe,SAAU,QAAQ,aAAa,YAAY;AAC5D,YAAI,aAAa;AACjB,YAAI,MAAM;AACV,YAAI,iBAAiB,oBAAoB;AACzC,YAAI,WAAW,SAAU,KAAK;AAC5B,cAAI,WAAW,IAAI,MAAM,SAAS;AAChC,mBAAO,IAAI,KAAK,QAAQ,iBAAiB,SAAU,OAAO,KAAK;AAC7D,qBAAO,UAAU,iBAAiB,MAAM;AAAA;AAAA,iBAErC;AACL,mBAAO,IAAI;AAAA;AAAA;AAGf,YAAI,gBAAgB,SAAU,QAAQ;AACpC,cAAI,OAAO;AACX,cAAI,aAAa;AACjB,eAAK,QAAQ,SAAU,KAAK,OAAO;AACjC,gBAAI,QAAQ;AAAA,cACV;AAAA,cACA,UAAU,IAAI;AAAA,cACd,MAAM,SAAS;AAAA,cACf,UAAU,kBAAkB,iBAAiB,IAAI;AAAA;AAEnD,gBAAI,UAAU,KAAK,MAAM,cAAc,SAAY,KAAK,MAAM,YAAY;AAC1E,iBAAK,MAAM,YAAY,QAAQ,OAAO,CAAC;AACvC,uBAAW,KAAK;AAAA;AAElB,qBAAW,IAAI;AACf,cAAI,IAAI;AAAA;AAEV,eAAO,GAAG,QAAQ,WAAY;AAC5B,mBAAS,KAAK,YAAY,aAAa,KAAK,SAAU,QAAQ;AAC5D,gBAAI,aAAa,wBAAwB;AACzC,0BAAc,MAAM,QAAQ;AAAA,aAC3B,SAAU,KAAK;AAChB,oBAAQ,IAAI,+BAA+B;AAC3C,uBAAW,IAAI;AACf,gBAAI,IAAI;AAAA;AAAA;AAGZ,YAAI,eAAe,SAAU,UAAU;AACrC,cAAI,aAAa,cAAc;AAC7B,mBAAO;AAAA;AAET,iBAAO,WAAW,MAAM,KAAK,SAAU,MAAM;AAC3C,mBAAO,SAAS,KAAK,KAAK;AAAA,aACzB,MAAM;AAAA;AAEX,YAAI,UAAU,WAAY;AACxB,iBAAO,IAAI,MAAM,MAAM;AAAA;AAEzB,YAAI,iBAAiB,WAAY;AAC/B,iBAAO,CAAC,cAAc,OAAO,KAAK,WAAW,MAAM,MAAM;AAAA;AAE3D,YAAI,cAAc,WAAY;AAC5B,cAAI,aAAa;AACf,mBAAO,OAAO,QAAQ;AAAA,iBACjB;AACL,mBAAO,IAAI,OAAO,SAAU,SAAS,QAAQ;AAC3C,kBAAI,aAAa;AACjB,kBAAI,WAAW,SAAS,YAAY,WAAY;AAC9C,oBAAI,aAAa;AACf,2BAAS,cAAc;AACvB,0BAAQ;AAAA,uBACH;AACL;AACA,sBAAI,aAAa,GAAG;AAClB,4BAAQ,IAAI,qCAAqC;AACjD,6BAAS,cAAc;AACvB,2BAAO;AAAA;AAAA;AAAA,iBAGV;AAAA;AAAA;AAAA;AAIT,YAAI,YAAY,WAAY;AAC1B,iBAAO,WAAW,WAAW,IAAI;AAAA;AAEnC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,eAAe,SAAU,OAAO,kBAAkB;AACpD,eAAO,SAAS,MAAM,MAAM,eAAe,qBAAqB,OAAO,MAAM,UAAU,SAAU,GAAG;AAClG,iBAAO,SAAS,EAAE,eAAe;AAAA;AAAA;AAGrC,UAAI,aAAa,SAAU,MAAM,SAAS,YAAY;AACpD,YAAI,UAAU;AACd,YAAI,mBAAmB,QAAQ;AAC/B,YAAI,eAAe,WAAW,KAAK,WAAY;AAC7C,iBAAO;AAAA,WACN,SAAU,KAAK;AAChB,iBAAO,SAAU,MAAM;AACrB,mBAAO,QAAQ;AAAA;AAAA;AAGnB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,QAAQ,WAAW,KAAK,aAAa,KAAK,IAAI,mBAAmB;AACnE,oBAAQ,KAAK;AAAA,cACX,OAAO,KAAK,GAAG;AAAA,cACf,MAAM,KAAK,GAAG;AAAA,cACd,MAAM,KAAK,GAAG;AAAA;AAEhB,gBAAI,aAAa,QAAQ,SAAS;AAChC;AAAA;AAAA;AAAA;AAIN,eAAO;AAAA;AAGT,UAAI,cAAc;AAClB,UAAI,OAAO,SAAU,QAAQ,UAAU;AACrC,YAAI,eAAe;AAAA,UACjB,SAAS;AAAA,UACT,SAAS,WAAW,SAAS,WAAW,IAAI,SAAS,KAAK;AAAA;AAE5D,YAAI,aAAa,KAAK;AACtB,YAAI,OAAO,SAAU,YAAW;AAC9B,cAAI,aAAa,WAAU;AAC3B,cAAI,WAAW,WAAW;AAC1B,cAAI,aAAa,SAAS,aAAa;AACvC,cAAI,UAAU,WAAW,YAAY,WAAW,cAAc,aAAa,eAAe,SAAS,KAAK,OAAO,SAAS;AACxH,qBAAU,QAAQ,EAAE;AAAA;AAEtB,YAAI,eAAe,KAAK,SAAU,YAAW;AAC3C,eAAK;AAAA,WACJ;AACH,YAAI,cAAc;AAAA,UAChB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA;AAER,YAAI,eAAe;AAAA,UACjB,MAAM;AAAA,UACN,MAAM;AAAA;AAER,YAAI,kBAAkB,WAAY;AAChC,cAAI,OAAO;AAAA,YACT,MAAM;AAAA,YACN,MAAM,MAAM,SAAS,kBAAkB,SAAU,KAAK;AACpD,qBAAO;AAAA,gBACL,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,OAAO;AAAA,kBACL;AAAA,kBACA;AAAA;AAAA;AAAA;AAAA;AAKR,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,MAAM;AAAA,YACN;AAAA,YACA,aAAa;AAAA,YACb,aAAa,SAAU,YAAW,SAAS;AACzC,yBAAW,IAAI,QAAQ;AACvB,2BAAa,SAAS;AAAA;AAAA,YAExB,UAAU,aAAa;AAAA,YACvB,UAAU,SAAU,YAAW,YAAY;AACzC,kBAAI,WAAW,SAAS,WAAW;AACjC,+BAAe,QAAQ,WAAW;AAClC,2BAAU;AAAA;AAAA;AAAA,YAGd,SAAS,CAAC;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA;AAAA;AAAA;AAIjB,YAAI,YAAY,OAAO,cAAc,KAAK;AAC1C,kBAAU,MAAM;AAChB,YAAI,CAAC,SAAS,aAAa;AACzB,oBAAU,MAAM;AAChB,mBAAS,cAAc,KAAK,WAAY;AACtC,sBAAU,OAAO;AACjB,yBAAa,SAAS;AACtB,sBAAU,MAAM;AAChB,sBAAU;AAAA,aACT,MAAM,SAAU,MAAM;AACvB,sBAAU,OAAO;AAAA,cACf,OAAO;AAAA,cACP,MAAM;AAAA,gBACJ,MAAM;AAAA,gBACN,OAAO,CAAC;AAAA,kBACJ,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,MAAM;AAAA;AAAA;AAAA,cAGZ,SAAS,CAAC;AAAA,gBACN,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA;AAAA,cAEb,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,SAAS;AAAA;AAAA;AAGb,sBAAU,MAAM;AAChB,sBAAU;AAAA;AAAA;AAAA;AAKhB,UAAI,aAAa,SAAU,QAAQ,UAAU;AAC3C,eAAO,WAAW,gBAAgB,WAAY;AAC5C,iBAAO,KAAK,QAAQ;AAAA;AAAA;AAIxB,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,GAAG,WAAW,WAAY;AAC/B,iBAAO,OAAO,mBAAmB,iBAAiB,SAAU,OAAO;AACjE,mBAAO,OAAO,SAAU,MAAM;AAC5B,mBAAK,KAAK,mBAAmB;AAC7B,mBAAK,KAAK,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAM1C,UAAI,OAAO,SAAU,QAAQ,UAAU;AACrC,eAAO,GAAG,SAAS,iBAAiB,aAAa;AAAA,UAC/C,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,OAAO,SAAU,SAAS,YAAY;AACpC,mBAAO,SAAS,cAAc,KAAK,WAAY;AAC7C,kBAAI,aAAa,SAAS;AAC1B,qBAAO,WAAW,YAAY,SAAS,SAAS,KAAK;AAAA;AAAA;AAAA,UAGzD,UAAU,SAAU,iBAAiB,KAAK,QAAO;AAC/C,mBAAO,UAAU,OAAO;AACxB,mBAAO,cAAc;AACrB,4BAAgB;AAAA;AAAA;AAAA;AAKtB,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,UAAU,aAAa;AAAA,UACxC,SAAS;AAAA,UACT,MAAM;AAAA,UACN;AAAA;AAEF,eAAO,GAAG,SAAS,YAAY,aAAa;AAAA,UAC1C,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA;AAAA;AAIJ,wBAAmB;AACjB,iBAAS,IAAI,aAAa,SAAU,QAAQ,WAAW;AACrD,cAAI,cAAc,uBAAuB,QAAQ;AACjD,cAAI,aAAa,sBAAsB;AACvC,cAAI,WAAW,aAAa,QAAQ,aAAa;AACjD,qBAAW,QAAQ;AACnB,mBAAS;AACT,eAAK,QAAQ;AACb,gBAAM;AAAA;AAAA;AAIV;AAAA;AAAA;AAAA;;;ACznBJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,oCAAQ;", "names": []}