<template>
    <div>
        <el-table :data="dataList" v-loading="loading">
            <el-table-column label="操作人" align="center" min-width="120" prop="createBy" />
            <el-table-column label="资产编号" align="center" min-width="120" prop="assetsNo" />
            <el-table-column label="提交时间" align="center" min-width="120" prop="createTime" />
            <el-table-column label="操作功能" align="center" min-width="120" prop="file" />
            <el-table-column label="操作补充" align="center" min-width="120" prop="remarks" />
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>
const dataList = ref([
    {
        assetsNo: 'CITIC202502CC0002',
        opt: '转让管理',
        createTime: '2025-06-09 09:47:40',
        createBy: 'Root Test',
        remarks: '流转至“交割确认”',
    }
])
const loading = ref(false)

const queryParams = ref({
    allQuery: false,
    pageNum: 1,
    pageSize: 10,
})
const total = ref(1)
</script>

<style lang="scss" scoped></style>