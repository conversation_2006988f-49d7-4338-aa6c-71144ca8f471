import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/tabfocus/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/tabfocus/plugin.js"() {
    (function() {
      "use strict";
      var global$6 = tinymce.util.Tools.resolve("tinymce.PluginManager");
      var global$5 = tinymce.util.Tools.resolve("tinymce.dom.DOMUtils");
      var global$4 = tinymce.util.Tools.resolve("tinymce.EditorManager");
      var global$3 = tinymce.util.Tools.resolve("tinymce.Env");
      var global$2 = tinymce.util.Tools.resolve("tinymce.util.Delay");
      var global$1 = tinymce.util.Tools.resolve("tinymce.util.Tools");
      var global = tinymce.util.Tools.resolve("tinymce.util.VK");
      var getTabFocusElements = function(editor) {
        return editor.getParam("tabfocus_elements", ":prev,:next");
      };
      var getTabFocus = function(editor) {
        return editor.getParam("tab_focus", getTabFocusElements(editor));
      };
      var DOM = global$5.DOM;
      var tabCancel = function(e) {
        if (e.keyCode === global.TAB && !e.ctrlKey && !e.altKey && !e.metaKey) {
          e.preventDefault();
        }
      };
      var setup = function(editor) {
        var tabHandler = function(e) {
          var x;
          if (e.keyCode !== global.TAB || e.ctrlKey || e.altKey || e.metaKey || e.isDefaultPrevented()) {
            return;
          }
          var find = function(direction) {
            var el2 = DOM.select(":input:enabled,*[tabindex]:not(iframe)");
            var canSelectRecursive = function(e2) {
              var castElem = e2;
              return e2.nodeName === "BODY" || castElem.type !== "hidden" && castElem.style.display !== "none" && castElem.style.visibility !== "hidden" && canSelectRecursive(e2.parentNode);
            };
            var canSelect = function(el3) {
              return /INPUT|TEXTAREA|BUTTON/.test(el3.tagName) && global$4.get(e.id) && el3.tabIndex !== -1 && canSelectRecursive(el3);
            };
            global$1.each(el2, function(e2, i2) {
              if (e2.id === editor.id) {
                x = i2;
                return false;
              }
            });
            if (direction > 0) {
              for (var i = x + 1; i < el2.length; i++) {
                if (canSelect(el2[i])) {
                  return el2[i];
                }
              }
            } else {
              for (var i = x - 1; i >= 0; i--) {
                if (canSelect(el2[i])) {
                  return el2[i];
                }
              }
            }
            return null;
          };
          var v = global$1.explode(getTabFocus(editor));
          if (v.length === 1) {
            v[1] = v[0];
            v[0] = ":prev";
          }
          var el;
          if (e.shiftKey) {
            if (v[0] === ":prev") {
              el = find(-1);
            } else {
              el = DOM.get(v[0]);
            }
          } else {
            if (v[1] === ":next") {
              el = find(1);
            } else {
              el = DOM.get(v[1]);
            }
          }
          if (el) {
            var focusEditor = global$4.get(el.id || el.name);
            if (el.id && focusEditor) {
              focusEditor.focus();
            } else {
              global$2.setTimeout(function() {
                if (!global$3.webkit) {
                  window.focus();
                }
                el.focus();
              }, 10);
            }
            e.preventDefault();
          }
        };
        editor.on("init", function() {
          if (editor.inline) {
            DOM.setAttrib(editor.getBody(), "tabIndex", null);
          }
          editor.on("keyup", tabCancel);
          if (global$3.gecko) {
            editor.on("keypress keydown", tabHandler);
          } else {
            editor.on("keydown", tabHandler);
          }
        });
      };
      function Plugin() {
        global$6.add("tabfocus", function(editor) {
          setup(editor);
        });
      }
      Plugin();
    })();
  }
});

// node_modules/tinymce/plugins/tabfocus/index.js
var require_tabfocus = __commonJS({
  "node_modules/tinymce/plugins/tabfocus/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_tabfocus
var tinymce_plugins_tabfocus_default = require_tabfocus();
export {
  tinymce_plugins_tabfocus_default as default
};
//# sourceMappingURL=tinymce_plugins_tabfocus.js.map
