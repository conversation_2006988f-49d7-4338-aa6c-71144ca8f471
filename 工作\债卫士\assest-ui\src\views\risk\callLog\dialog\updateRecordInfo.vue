<template>
    <el-dialog title="修改录音" v-model="open" append-to-body width="950px" :before-close="cancel">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item label="录音名称" prop="recordingName">
                <el-input v-model="form.recordingName" style="width: 95%;" placeholder="请输入录音名称" />
                <div class="tip-msg">
                    注意：<span>1.录音名称请以 外显号码_被叫号码_录音时间.MP3 命名；以下划线隔开。
                        <br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2.文件名不能包含下列任何字符
                        ：\ / : * ＜ ＞ | 。</span>
                </div>
            </el-form-item>
            <el-form-item label="录音类型" prop="recordingType">
                <el-radio-group v-model="form.recordingType">
                    <el-radio v-for="(v, i) in recordingTypeEnum" :key="i" :label="+i">{{ v }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" :rows="5" show-word-limit :maxlength="500"
                    style="width: 95%;" placeholder="请输入备注" />
            </el-form-item>
            <el-form-item label="修改记录">
                <div style="width: 95%;">
                    <el-table v-loading="loading" :data="dataList" max-height="200px">
                        <el-table-column label="修改时间" prop="operationTime" align="center" width="160" />
                        <el-table-column label="修改类型" prop="modifiedField" align="center" width="100" />
                        <el-table-column label="修改前信息" prop="oldValue" align="center" show-overflow-tooltip />
                        <el-table-column label="修改后信息" prop="newValue" align="center" show-overflow-tooltip />
                        <el-table-column label="操作人" prop="operator" align="center" width="120" />
                    </el-table>
                    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize" @pagination="selectCallRecordByIdFun" />
                </div>
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button :loading="loading" @click="cancel">取消</el-button>
                <el-button :loading="loading" type="primary" @click="sumbit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { selectCallRecordByIdApi, updateCallReCordApi } from '@/api/seat/calllog'
import { recordingTypeEnum } from '@/utils/enum'

const { proxy } = getCurrentInstance()
const props = defineProps({
    getList: { type: Function }
})
const open = ref(false)
const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const data = reactive({
    form: {},
    rules: {
        recordingName: [{ required: true, message: '请输入录音名称', trigger: 'blur' }],
        recordingType: [{ required: true, message: '请选择录音类型', trigger: 'blur' }],
    },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },
})
const { form, rules, queryParams } = toRefs(data)
function sumbit() {
    proxy.$refs['formRef'].validate((valid) => {
        if (valid) {
            const content = '确认修改当前录音信息吗？'
            proxy.$modal.confirm(content).then(() => {
                const reqForm = JSON.parse(JSON.stringify(form.value))
                loading.value = true
                updateCallReCordApi(reqForm).then(() => {
                    cancel()
                    proxy.$modal.msgSuccess("操作成功！")
                    props.getList && props.getList()
                }).finally(() => loading.value = false)
            })
        }

    })
}
function openDialog(data) {
    open.value = true
    form.value = data
    queryParams.value.callId = data.id
    selectCallRecordByIdFun()
}

function selectCallRecordByIdFun() {
    const reqForm = JSON.parse(JSON.stringify(queryParams.value))
    loading.value = true
    selectCallRecordByIdApi(reqForm).then(res => {
        dataList.value = res.rows
        total.value = res.total
    }).finally(() => loading.value = false)
}

function cancel() {
    open.value = false
    form.value = {}
}
defineExpose({ openDialog })
</script>

<style lang="scss" scoped>
.tip-msg {
    color: red;
    font-size: 12px;
}
</style>