{"version": 3, "sources": ["../tinymce/plugins/autosave/plugin.js", "../tinymce/plugins/autosave/index.js", "dep:tinymce_plugins_autosave"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var eq = function (t) {\n      return function (a) {\n        return t === a;\n      };\n    };\n    var isUndefined = eq(undefined);\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.LocalStorage');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var fireRestoreDraft = function (editor) {\n      return editor.fire('RestoreDraft');\n    };\n    var fireStoreDraft = function (editor) {\n      return editor.fire('StoreDraft');\n    };\n    var fireRemoveDraft = function (editor) {\n      return editor.fire('RemoveDraft');\n    };\n\n    var parse = function (timeString, defaultTime) {\n      var multiples = {\n        s: 1000,\n        m: 60000\n      };\n      var toParse = timeString || defaultTime;\n      var parsedTime = /^(\\d+)([ms]?)$/.exec('' + toParse);\n      return (parsedTime[2] ? multiples[parsedTime[2]] : 1) * parseInt(toParse, 10);\n    };\n\n    var shouldAskBeforeUnload = function (editor) {\n      return editor.getParam('autosave_ask_before_unload', true);\n    };\n    var getAutoSavePrefix = function (editor) {\n      var location = document.location;\n      return editor.getParam('autosave_prefix', 'tinymce-autosave-{path}{query}{hash}-{id}-').replace(/{path}/g, location.pathname).replace(/{query}/g, location.search).replace(/{hash}/g, location.hash).replace(/{id}/g, editor.id);\n    };\n    var shouldRestoreWhenEmpty = function (editor) {\n      return editor.getParam('autosave_restore_when_empty', false);\n    };\n    var getAutoSaveInterval = function (editor) {\n      return parse(editor.getParam('autosave_interval'), '30s');\n    };\n    var getAutoSaveRetention = function (editor) {\n      return parse(editor.getParam('autosave_retention'), '20m');\n    };\n\n    var isEmpty = function (editor, html) {\n      if (isUndefined(html)) {\n        return editor.dom.isEmpty(editor.getBody());\n      } else {\n        var trimmedHtml = global$1.trim(html);\n        if (trimmedHtml === '') {\n          return true;\n        } else {\n          var fragment = new DOMParser().parseFromString(trimmedHtml, 'text/html');\n          return editor.dom.isEmpty(fragment);\n        }\n      }\n    };\n    var hasDraft = function (editor) {\n      var time = parseInt(global$2.getItem(getAutoSavePrefix(editor) + 'time'), 10) || 0;\n      if (new Date().getTime() - time > getAutoSaveRetention(editor)) {\n        removeDraft(editor, false);\n        return false;\n      }\n      return true;\n    };\n    var removeDraft = function (editor, fire) {\n      var prefix = getAutoSavePrefix(editor);\n      global$2.removeItem(prefix + 'draft');\n      global$2.removeItem(prefix + 'time');\n      if (fire !== false) {\n        fireRemoveDraft(editor);\n      }\n    };\n    var storeDraft = function (editor) {\n      var prefix = getAutoSavePrefix(editor);\n      if (!isEmpty(editor) && editor.isDirty()) {\n        global$2.setItem(prefix + 'draft', editor.getContent({\n          format: 'raw',\n          no_events: true\n        }));\n        global$2.setItem(prefix + 'time', new Date().getTime().toString());\n        fireStoreDraft(editor);\n      }\n    };\n    var restoreDraft = function (editor) {\n      var prefix = getAutoSavePrefix(editor);\n      if (hasDraft(editor)) {\n        editor.setContent(global$2.getItem(prefix + 'draft'), { format: 'raw' });\n        fireRestoreDraft(editor);\n      }\n    };\n    var startStoreDraft = function (editor) {\n      var interval = getAutoSaveInterval(editor);\n      global$3.setEditorInterval(editor, function () {\n        storeDraft(editor);\n      }, interval);\n    };\n    var restoreLastDraft = function (editor) {\n      editor.undoManager.transact(function () {\n        restoreDraft(editor);\n        removeDraft(editor);\n      });\n      editor.focus();\n    };\n\n    var get = function (editor) {\n      return {\n        hasDraft: function () {\n          return hasDraft(editor);\n        },\n        storeDraft: function () {\n          return storeDraft(editor);\n        },\n        restoreDraft: function () {\n          return restoreDraft(editor);\n        },\n        removeDraft: function (fire) {\n          return removeDraft(editor, fire);\n        },\n        isEmpty: function (html) {\n          return isEmpty(editor, html);\n        }\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.EditorManager');\n\n    var setup = function (editor) {\n      editor.editorManager.on('BeforeUnload', function (e) {\n        var msg;\n        global$1.each(global.get(), function (editor) {\n          if (editor.plugins.autosave) {\n            editor.plugins.autosave.storeDraft();\n          }\n          if (!msg && editor.isDirty() && shouldAskBeforeUnload(editor)) {\n            msg = editor.translate('You have unsaved changes are you sure you want to navigate away?');\n          }\n        });\n        if (msg) {\n          e.preventDefault();\n          e.returnValue = msg;\n        }\n      });\n    };\n\n    var makeSetupHandler = function (editor) {\n      return function (api) {\n        api.setDisabled(!hasDraft(editor));\n        var editorEventCallback = function () {\n          return api.setDisabled(!hasDraft(editor));\n        };\n        editor.on('StoreDraft RestoreDraft RemoveDraft', editorEventCallback);\n        return function () {\n          return editor.off('StoreDraft RestoreDraft RemoveDraft', editorEventCallback);\n        };\n      };\n    };\n    var register = function (editor) {\n      startStoreDraft(editor);\n      editor.ui.registry.addButton('restoredraft', {\n        tooltip: 'Restore last draft',\n        icon: 'restore-draft',\n        onAction: function () {\n          restoreLastDraft(editor);\n        },\n        onSetup: makeSetupHandler(editor)\n      });\n      editor.ui.registry.addMenuItem('restoredraft', {\n        text: 'Restore last draft',\n        icon: 'restore-draft',\n        onAction: function () {\n          restoreLastDraft(editor);\n        },\n        onSetup: makeSetupHandler(editor)\n      });\n    };\n\n    function Plugin () {\n      global$4.add('autosave', function (editor) {\n        setup(editor);\n        register(editor);\n        editor.on('init', function () {\n          if (shouldRestoreWhenEmpty(editor) && editor.dom.isEmpty(editor.getBody())) {\n            restoreDraft(editor);\n          }\n        });\n        return get(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"autosave\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/autosave')\n//   ES2015:\n//     import 'tinymce/plugins/autosave'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/autosave/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,KAAK,SAAU,GAAG;AACpB,eAAO,SAAU,GAAG;AAClB,iBAAO,MAAM;AAAA;AAAA;AAGjB,UAAI,cAAc,GAAG;AAErB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,OAAO,KAAK;AAAA;AAErB,UAAI,iBAAiB,SAAU,QAAQ;AACrC,eAAO,OAAO,KAAK;AAAA;AAErB,UAAI,kBAAkB,SAAU,QAAQ;AACtC,eAAO,OAAO,KAAK;AAAA;AAGrB,UAAI,QAAQ,SAAU,YAAY,aAAa;AAC7C,YAAI,YAAY;AAAA,UACd,GAAG;AAAA,UACH,GAAG;AAAA;AAEL,YAAI,UAAU,cAAc;AAC5B,YAAI,aAAa,iBAAiB,KAAK,KAAK;AAC5C,eAAQ,YAAW,KAAK,UAAU,WAAW,MAAM,KAAK,SAAS,SAAS;AAAA;AAG5E,UAAI,wBAAwB,SAAU,QAAQ;AAC5C,eAAO,OAAO,SAAS,8BAA8B;AAAA;AAEvD,UAAI,oBAAoB,SAAU,QAAQ;AACxC,YAAI,WAAW,SAAS;AACxB,eAAO,OAAO,SAAS,mBAAmB,8CAA8C,QAAQ,WAAW,SAAS,UAAU,QAAQ,YAAY,SAAS,QAAQ,QAAQ,WAAW,SAAS,MAAM,QAAQ,SAAS,OAAO;AAAA;AAE/N,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,eAAO,OAAO,SAAS,+BAA+B;AAAA;AAExD,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,MAAM,OAAO,SAAS,sBAAsB;AAAA;AAErD,UAAI,uBAAuB,SAAU,QAAQ;AAC3C,eAAO,MAAM,OAAO,SAAS,uBAAuB;AAAA;AAGtD,UAAI,UAAU,SAAU,QAAQ,MAAM;AACpC,YAAI,YAAY,OAAO;AACrB,iBAAO,OAAO,IAAI,QAAQ,OAAO;AAAA,eAC5B;AACL,cAAI,cAAc,SAAS,KAAK;AAChC,cAAI,gBAAgB,IAAI;AACtB,mBAAO;AAAA,iBACF;AACL,gBAAI,WAAW,IAAI,YAAY,gBAAgB,aAAa;AAC5D,mBAAO,OAAO,IAAI,QAAQ;AAAA;AAAA;AAAA;AAIhC,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,OAAO,SAAS,SAAS,QAAQ,kBAAkB,UAAU,SAAS,OAAO;AACjF,YAAI,IAAI,OAAO,YAAY,OAAO,qBAAqB,SAAS;AAC9D,sBAAY,QAAQ;AACpB,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,cAAc,SAAU,QAAQ,MAAM;AACxC,YAAI,SAAS,kBAAkB;AAC/B,iBAAS,WAAW,SAAS;AAC7B,iBAAS,WAAW,SAAS;AAC7B,YAAI,SAAS,OAAO;AAClB,0BAAgB;AAAA;AAAA;AAGpB,UAAI,aAAa,SAAU,QAAQ;AACjC,YAAI,SAAS,kBAAkB;AAC/B,YAAI,CAAC,QAAQ,WAAW,OAAO,WAAW;AACxC,mBAAS,QAAQ,SAAS,SAAS,OAAO,WAAW;AAAA,YACnD,QAAQ;AAAA,YACR,WAAW;AAAA;AAEb,mBAAS,QAAQ,SAAS,QAAQ,IAAI,OAAO,UAAU;AACvD,yBAAe;AAAA;AAAA;AAGnB,UAAI,eAAe,SAAU,QAAQ;AACnC,YAAI,SAAS,kBAAkB;AAC/B,YAAI,SAAS,SAAS;AACpB,iBAAO,WAAW,SAAS,QAAQ,SAAS,UAAU,EAAE,QAAQ;AAChE,2BAAiB;AAAA;AAAA;AAGrB,UAAI,kBAAkB,SAAU,QAAQ;AACtC,YAAI,WAAW,oBAAoB;AACnC,iBAAS,kBAAkB,QAAQ,WAAY;AAC7C,qBAAW;AAAA,WACV;AAAA;AAEL,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,YAAY,SAAS,WAAY;AACtC,uBAAa;AACb,sBAAY;AAAA;AAEd,eAAO;AAAA;AAGT,UAAI,MAAM,SAAU,QAAQ;AAC1B,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO,SAAS;AAAA;AAAA,UAElB,YAAY,WAAY;AACtB,mBAAO,WAAW;AAAA;AAAA,UAEpB,cAAc,WAAY;AACxB,mBAAO,aAAa;AAAA;AAAA,UAEtB,aAAa,SAAU,MAAM;AAC3B,mBAAO,YAAY,QAAQ;AAAA;AAAA,UAE7B,SAAS,SAAU,MAAM;AACvB,mBAAO,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAK7B,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,cAAc,GAAG,gBAAgB,SAAU,GAAG;AACnD,cAAI;AACJ,mBAAS,KAAK,OAAO,OAAO,SAAU,SAAQ;AAC5C,gBAAI,QAAO,QAAQ,UAAU;AAC3B,sBAAO,QAAQ,SAAS;AAAA;AAE1B,gBAAI,CAAC,OAAO,QAAO,aAAa,sBAAsB,UAAS;AAC7D,oBAAM,QAAO,UAAU;AAAA;AAAA;AAG3B,cAAI,KAAK;AACP,cAAE;AACF,cAAE,cAAc;AAAA;AAAA;AAAA;AAKtB,UAAI,mBAAmB,SAAU,QAAQ;AACvC,eAAO,SAAU,KAAK;AACpB,cAAI,YAAY,CAAC,SAAS;AAC1B,cAAI,sBAAsB,WAAY;AACpC,mBAAO,IAAI,YAAY,CAAC,SAAS;AAAA;AAEnC,iBAAO,GAAG,uCAAuC;AACjD,iBAAO,WAAY;AACjB,mBAAO,OAAO,IAAI,uCAAuC;AAAA;AAAA;AAAA;AAI/D,UAAI,WAAW,SAAU,QAAQ;AAC/B,wBAAgB;AAChB,eAAO,GAAG,SAAS,UAAU,gBAAgB;AAAA,UAC3C,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,6BAAiB;AAAA;AAAA,UAEnB,SAAS,iBAAiB;AAAA;AAE5B,eAAO,GAAG,SAAS,YAAY,gBAAgB;AAAA,UAC7C,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,WAAY;AACpB,6BAAiB;AAAA;AAAA,UAEnB,SAAS,iBAAiB;AAAA;AAAA;AAI9B,wBAAmB;AACjB,iBAAS,IAAI,YAAY,SAAU,QAAQ;AACzC,gBAAM;AACN,mBAAS;AACT,iBAAO,GAAG,QAAQ,WAAY;AAC5B,gBAAI,uBAAuB,WAAW,OAAO,IAAI,QAAQ,OAAO,YAAY;AAC1E,2BAAa;AAAA;AAAA;AAGjB,iBAAO,IAAI;AAAA;AAAA;AAIf;AAAA;AAAA;AAAA;;;ACjNJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,mCAAQ;", "names": []}