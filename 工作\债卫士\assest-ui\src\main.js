import { createApp } from 'vue'

import Cookies from 'js-cookie'

import ElementPlus from 'element-plus'
import locale from 'element-plus/es/locale/lang/zh-cn' // 中文语言

import '@/assets/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import * as echarts from "echarts";


// 注册指令
import plugins from './plugins' // plugins
import { download, downloadforjson, myAxios } from '@/utils/request'
import { ExportSavePdf } from '@/utils/htmlToPdf'


// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import './permission' // permission control

import { useDict } from '@/utils/dict'
import { orderEnum } from '@/utils/enum'
import { parseTime, formatMultipleField, resetForm, addDateRange, addFieldsRange, handleTree, selectDictLabel, copyUrl, antiShake, newAddFieldsRange, toThousands } from '@/utils/ruoyi'
import { numFilter,formatAmountWithComma } from '@/utils/common'

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'
// 自定义消息确认框组件
import myconfirm from '@/components/MessageBox/index.js'
// 自定义消息确认框组件
import ToolTipBoxDialog from '@/components/ToolTipBoxDialog/index.vue'
// 气泡弹窗框组件
import Tooltip from '@/components/Tooltip/index.vue'
// 富文本组件
import myEditor from "@/components/myEditor";
//导出弹窗
import exportTip from "@/components/exportTip/index.vue";
// 进度加载组件
import divisionalLoad from "@/components/divisionalLoad";
// 本页选中组件
import SelectedAll from "@/components/SelectedAll";
// 数字输入框
import NumberInput from '@/components/InputNum'
// 表格
import ZwsTable from '@/components/ZwsTable'
// 表单
import ZwsForm from '@/components/ZwsForm'
// 上传组件
import FileUpload from '@/components/FileUpload'
// 数字输入
import InputNum from '@/components/InputNum'
// 多选模糊匹配下拉框
import MultiSelect from '@/components/MultiSelect'

import { handleSelectedAll, handleSelected } from './utils/tool'

const app = createApp(App)


// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.downloadforjson = downloadforjson
app.config.globalProperties.myAxios = myAxios
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.addFieldsRange = addFieldsRange
app.config.globalProperties.newAddFieldsRange = newAddFieldsRange
app.config.globalProperties.$toThousands = toThousands;
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.copyUrl = copyUrl
app.config.globalProperties.$myconfirm = myconfirm
app.config.globalProperties.ExportSavePdf = ExportSavePdf
app.config.globalProperties.antiShake = antiShake
app.config.globalProperties.formatMultipleField = formatMultipleField
app.config.globalProperties.handleSelected = handleSelected
app.config.globalProperties.handleSelectedAll = handleSelectedAll
app.config.globalProperties.numFilter = numFilter
app.config.globalProperties.formatAmountWithComma = formatAmountWithComma
app.config.globalProperties.orderEnum = orderEnum


// 全局组件挂载
app.component('DictTag', DictTag)
app.component('Pagination', Pagination)
app.component('TreeSelect', TreeSelect)
app.component('RightToolbar', RightToolbar)
app.component('ToolTipBoxDialog', ToolTipBoxDialog)
app.component('Tooltip', Tooltip)
app.component('myEditor', myEditor)
app.component('divisionalLoad', divisionalLoad)
app.component('exportTip', exportTip)
app.component('SelectedAll', SelectedAll)
app.component('NumberInput', NumberInput)
app.component('ZwsTable', ZwsTable)
app.component('ZwsForm', ZwsForm)
app.component('FileUpload', FileUpload)
app.component('InputNum', InputNum)
app.component('MultiSelect', MultiSelect)

app.use(router)
app.use(store)
app.use(echarts)
app.use(plugins)
app.use(elementIcons)
app.component('svg-icon', SvgIcon)

directive(app)

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  size: Cookies.get('size') || 'default'
})

app.mount('#app')
if (window.location.href?.indexOf('checkFilelist') < 0) {
  document.title = '资产管理中心';
  if (window.location.href.indexOf('login') > 0) {
    var obj = document.querySelector(".loader-wrapper-back");
    obj.setAttribute("id", "loader-wrapper");
    setTimeout(() => {
      obj.remove();
    }, 2000)
  }

}

