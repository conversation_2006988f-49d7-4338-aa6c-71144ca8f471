{"version": 3, "sources": ["../insert-css/index.js", "dep:insert-css"], "sourcesContent": ["var containers = []; // will store container HTMLElement references\nvar styleElements = []; // will store {prepend: HTMLElement, append: HTMLElement}\n\nvar usage = 'insert-css: You need to provide a CSS string. Usage: insertCss(cssString[, options]).';\n\nfunction insertCss(css, options) {\n    options = options || {};\n\n    if (css === undefined) {\n        throw new Error(usage);\n    }\n\n    var position = options.prepend === true ? 'prepend' : 'append';\n    var container = options.container !== undefined ? options.container : document.querySelector('head');\n    var containerId = containers.indexOf(container);\n\n    // first time we see this container, create the necessary entries\n    if (containerId === -1) {\n        containerId = containers.push(container) - 1;\n        styleElements[containerId] = {};\n    }\n\n    // try to get the correponding container + position styleElement, create it otherwise\n    var styleElement;\n\n    if (styleElements[containerId] !== undefined && styleElements[containerId][position] !== undefined) {\n        styleElement = styleElements[containerId][position];\n    } else {\n        styleElement = styleElements[containerId][position] = createStyleElement();\n\n        if (position === 'prepend') {\n            container.insertBefore(styleElement, container.childNodes[0]);\n        } else {\n            container.appendChild(styleElement);\n        }\n    }\n\n    // strip potential UTF-8 BOM if css was read from a file\n    if (css.charCodeAt(0) === 0xFEFF) { css = css.substr(1, css.length); }\n\n    // actually add the stylesheet\n    if (styleElement.styleSheet) {\n        styleElement.styleSheet.cssText += css\n    } else {\n        styleElement.textContent += css;\n    }\n\n    return styleElement;\n};\n\nfunction createStyleElement() {\n    var styleElement = document.createElement('style');\n    styleElement.setAttribute('type', 'text/css');\n    return styleElement;\n}\n\nmodule.exports = insertCss;\nmodule.exports.insertCss = insertCss;\n", "export default require(\"./node_modules/insert-css/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAI,aAAa;AACjB,QAAI,gBAAgB;AAEpB,QAAI,QAAQ;AAEZ,uBAAmB,KAAK,SAAS;AAC7B,gBAAU,WAAW;AAErB,UAAI,QAAQ,QAAW;AACnB,cAAM,IAAI,MAAM;AAAA;AAGpB,UAAI,WAAW,QAAQ,YAAY,OAAO,YAAY;AACtD,UAAI,YAAY,QAAQ,cAAc,SAAY,QAAQ,YAAY,SAAS,cAAc;AAC7F,UAAI,cAAc,WAAW,QAAQ;AAGrC,UAAI,gBAAgB,IAAI;AACpB,sBAAc,WAAW,KAAK,aAAa;AAC3C,sBAAc,eAAe;AAAA;AAIjC,UAAI;AAEJ,UAAI,cAAc,iBAAiB,UAAa,cAAc,aAAa,cAAc,QAAW;AAChG,uBAAe,cAAc,aAAa;AAAA,aACvC;AACH,uBAAe,cAAc,aAAa,YAAY;AAEtD,YAAI,aAAa,WAAW;AACxB,oBAAU,aAAa,cAAc,UAAU,WAAW;AAAA,eACvD;AACH,oBAAU,YAAY;AAAA;AAAA;AAK9B,UAAI,IAAI,WAAW,OAAO,OAAQ;AAAE,cAAM,IAAI,OAAO,GAAG,IAAI;AAAA;AAG5D,UAAI,aAAa,YAAY;AACzB,qBAAa,WAAW,WAAW;AAAA,aAChC;AACH,qBAAa,eAAe;AAAA;AAGhC,aAAO;AAAA;AAGX,kCAA8B;AAC1B,UAAI,eAAe,SAAS,cAAc;AAC1C,mBAAa,aAAa,QAAQ;AAClC,aAAO;AAAA;AAGX,WAAO,UAAU;AACjB,WAAO,QAAQ,YAAY;AAAA;AAAA;;;ACzD3B,IAAO,qBAAQ;", "names": []}