{"version": 3, "sources": ["../tinymce/plugins/textpattern/plugin.js", "../tinymce/plugins/textpattern/index.js", "dep:tinymce_plugins_textpattern"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n    function __spreadArray(to, from, pack) {\n      if (pack || arguments.length === 2)\n        for (var i = 0, l = from.length, ar; i < l; i++) {\n          if (ar || !(i in from)) {\n            if (!ar)\n              ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n          }\n        }\n      return to.concat(ar || Array.prototype.slice.call(from));\n    }\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isString = isType('string');\n    var isObject = isType('object');\n    var isArray = isType('array');\n\n    var noop = function () {\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    var die = function (msg) {\n      return function () {\n        throw new Error(msg);\n      };\n    };\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var nativeSlice = Array.prototype.slice;\n    var nativeIndexOf = Array.prototype.indexOf;\n    var rawIndexOf = function (ts, t) {\n      return nativeIndexOf.call(ts, t);\n    };\n    var contains = function (xs, x) {\n      return rawIndexOf(xs, x) > -1;\n    };\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var eachr = function (xs, f) {\n      for (var i = xs.length - 1; i >= 0; i--) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var filter = function (xs, pred) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    var foldr = function (xs, f, acc) {\n      eachr(xs, function (x, i) {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    var foldl = function (xs, f, acc) {\n      each(xs, function (x, i) {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    var findUntil = function (xs, pred, until) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    var find = function (xs, pred) {\n      return findUntil(xs, pred, never);\n    };\n    var forall = function (xs, pred) {\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        var x = xs[i];\n        if (pred(x, i) !== true) {\n          return false;\n        }\n      }\n      return true;\n    };\n    var sort = function (xs, comparator) {\n      var copy = nativeSlice.call(xs, 0);\n      copy.sort(comparator);\n      return copy;\n    };\n    var get$1 = function (xs, i) {\n      return i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    };\n    var head = function (xs) {\n      return get$1(xs, 0);\n    };\n\n    var keys = Object.keys;\n    var hasOwnProperty = Object.hasOwnProperty;\n    var has = function (obj, key) {\n      return hasOwnProperty.call(obj, key);\n    };\n\n    var generate$1 = function (cases) {\n      if (!isArray(cases)) {\n        throw new Error('cases must be an array');\n      }\n      if (cases.length === 0) {\n        throw new Error('there must be at least one case');\n      }\n      var constructors = [];\n      var adt = {};\n      each(cases, function (acase, count) {\n        var keys$1 = keys(acase);\n        if (keys$1.length !== 1) {\n          throw new Error('one and only one name per case');\n        }\n        var key = keys$1[0];\n        var value = acase[key];\n        if (adt[key] !== undefined) {\n          throw new Error('duplicate key detected:' + key);\n        } else if (key === 'cata') {\n          throw new Error('cannot have a case named cata (sorry)');\n        } else if (!isArray(value)) {\n          throw new Error('case arguments must be an array');\n        }\n        constructors.push(key);\n        adt[key] = function () {\n          var args = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n          }\n          var argLength = args.length;\n          if (argLength !== value.length) {\n            throw new Error('Wrong number of arguments to case ' + key + '. Expected ' + value.length + ' (' + value + '), got ' + argLength);\n          }\n          var match = function (branches) {\n            var branchKeys = keys(branches);\n            if (constructors.length !== branchKeys.length) {\n              throw new Error('Wrong number of arguments to match. Expected: ' + constructors.join(',') + '\\nActual: ' + branchKeys.join(','));\n            }\n            var allReqd = forall(constructors, function (reqKey) {\n              return contains(branchKeys, reqKey);\n            });\n            if (!allReqd) {\n              throw new Error('Not all branches were specified when using match. Specified: ' + branchKeys.join(', ') + '\\nRequired: ' + constructors.join(', '));\n            }\n            return branches[key].apply(null, args);\n          };\n          return {\n            fold: function () {\n              var foldArgs = [];\n              for (var _i = 0; _i < arguments.length; _i++) {\n                foldArgs[_i] = arguments[_i];\n              }\n              if (foldArgs.length !== cases.length) {\n                throw new Error('Wrong number of arguments to fold. Expected ' + cases.length + ', got ' + foldArgs.length);\n              }\n              var target = foldArgs[count];\n              return target.apply(null, args);\n            },\n            match: match,\n            log: function (label) {\n              console.log(label, {\n                constructors: constructors,\n                constructor: key,\n                params: args\n              });\n            }\n          };\n        };\n      });\n      return adt;\n    };\n    var Adt = { generate: generate$1 };\n\n    Adt.generate([\n      {\n        bothErrors: [\n          'error1',\n          'error2'\n        ]\n      },\n      {\n        firstError: [\n          'error1',\n          'value2'\n        ]\n      },\n      {\n        secondError: [\n          'value1',\n          'error2'\n        ]\n      },\n      {\n        bothValues: [\n          'value1',\n          'value2'\n        ]\n      }\n    ]);\n    var partition = function (results) {\n      var errors = [];\n      var values = [];\n      each(results, function (result) {\n        result.fold(function (err) {\n          errors.push(err);\n        }, function (value) {\n          values.push(value);\n        });\n      });\n      return {\n        errors: errors,\n        values: values\n      };\n    };\n\n    var value = function (o) {\n      var or = function (_opt) {\n        return value(o);\n      };\n      var orThunk = function (_f) {\n        return value(o);\n      };\n      var map = function (f) {\n        return value(f(o));\n      };\n      var mapError = function (_f) {\n        return value(o);\n      };\n      var each = function (f) {\n        f(o);\n      };\n      var bind = function (f) {\n        return f(o);\n      };\n      var fold = function (_, onValue) {\n        return onValue(o);\n      };\n      var exists = function (f) {\n        return f(o);\n      };\n      var forall = function (f) {\n        return f(o);\n      };\n      var toOptional = function () {\n        return Optional.some(o);\n      };\n      return {\n        isValue: always,\n        isError: never,\n        getOr: constant(o),\n        getOrThunk: constant(o),\n        getOrDie: constant(o),\n        or: or,\n        orThunk: orThunk,\n        fold: fold,\n        map: map,\n        mapError: mapError,\n        each: each,\n        bind: bind,\n        exists: exists,\n        forall: forall,\n        toOptional: toOptional\n      };\n    };\n    var error$1 = function (message) {\n      var getOrThunk = function (f) {\n        return f();\n      };\n      var getOrDie = function () {\n        return die(String(message))();\n      };\n      var or = identity;\n      var orThunk = function (f) {\n        return f();\n      };\n      var map = function (_f) {\n        return error$1(message);\n      };\n      var mapError = function (f) {\n        return error$1(f(message));\n      };\n      var bind = function (_f) {\n        return error$1(message);\n      };\n      var fold = function (onError, _) {\n        return onError(message);\n      };\n      return {\n        isValue: never,\n        isError: always,\n        getOr: identity,\n        getOrThunk: getOrThunk,\n        getOrDie: getOrDie,\n        or: or,\n        orThunk: orThunk,\n        fold: fold,\n        map: map,\n        mapError: mapError,\n        each: noop,\n        bind: bind,\n        exists: never,\n        forall: always,\n        toOptional: Optional.none\n      };\n    };\n    var fromOption = function (opt, err) {\n      return opt.fold(function () {\n        return error$1(err);\n      }, value);\n    };\n    var Result = {\n      value: value,\n      error: error$1,\n      fromOption: fromOption\n    };\n\n    var isInlinePattern = function (pattern) {\n      return pattern.type === 'inline-command' || pattern.type === 'inline-format';\n    };\n    var isBlockPattern = function (pattern) {\n      return pattern.type === 'block-command' || pattern.type === 'block-format';\n    };\n    var sortPatterns = function (patterns) {\n      return sort(patterns, function (a, b) {\n        if (a.start.length === b.start.length) {\n          return 0;\n        }\n        return a.start.length > b.start.length ? -1 : 1;\n      });\n    };\n    var normalizePattern = function (pattern) {\n      var err = function (message) {\n        return Result.error({\n          message: message,\n          pattern: pattern\n        });\n      };\n      var formatOrCmd = function (name, onFormat, onCommand) {\n        if (pattern.format !== undefined) {\n          var formats = void 0;\n          if (isArray(pattern.format)) {\n            if (!forall(pattern.format, isString)) {\n              return err(name + ' pattern has non-string items in the `format` array');\n            }\n            formats = pattern.format;\n          } else if (isString(pattern.format)) {\n            formats = [pattern.format];\n          } else {\n            return err(name + ' pattern has non-string `format` parameter');\n          }\n          return Result.value(onFormat(formats));\n        } else if (pattern.cmd !== undefined) {\n          if (!isString(pattern.cmd)) {\n            return err(name + ' pattern has non-string `cmd` parameter');\n          }\n          return Result.value(onCommand(pattern.cmd, pattern.value));\n        } else {\n          return err(name + ' pattern is missing both `format` and `cmd` parameters');\n        }\n      };\n      if (!isObject(pattern)) {\n        return err('Raw pattern is not an object');\n      }\n      if (!isString(pattern.start)) {\n        return err('Raw pattern is missing `start` parameter');\n      }\n      if (pattern.end !== undefined) {\n        if (!isString(pattern.end)) {\n          return err('Inline pattern has non-string `end` parameter');\n        }\n        if (pattern.start.length === 0 && pattern.end.length === 0) {\n          return err('Inline pattern has empty `start` and `end` parameters');\n        }\n        var start_1 = pattern.start;\n        var end_1 = pattern.end;\n        if (end_1.length === 0) {\n          end_1 = start_1;\n          start_1 = '';\n        }\n        return formatOrCmd('Inline', function (format) {\n          return {\n            type: 'inline-format',\n            start: start_1,\n            end: end_1,\n            format: format\n          };\n        }, function (cmd, value) {\n          return {\n            type: 'inline-command',\n            start: start_1,\n            end: end_1,\n            cmd: cmd,\n            value: value\n          };\n        });\n      } else if (pattern.replacement !== undefined) {\n        if (!isString(pattern.replacement)) {\n          return err('Replacement pattern has non-string `replacement` parameter');\n        }\n        if (pattern.start.length === 0) {\n          return err('Replacement pattern has empty `start` parameter');\n        }\n        return Result.value({\n          type: 'inline-command',\n          start: '',\n          end: pattern.start,\n          cmd: 'mceInsertContent',\n          value: pattern.replacement\n        });\n      } else {\n        if (pattern.start.length === 0) {\n          return err('Block pattern has empty `start` parameter');\n        }\n        return formatOrCmd('Block', function (formats) {\n          return {\n            type: 'block-format',\n            start: pattern.start,\n            format: formats[0]\n          };\n        }, function (command, commandValue) {\n          return {\n            type: 'block-command',\n            start: pattern.start,\n            cmd: command,\n            value: commandValue\n          };\n        });\n      }\n    };\n    var denormalizePattern = function (pattern) {\n      if (pattern.type === 'block-command') {\n        return {\n          start: pattern.start,\n          cmd: pattern.cmd,\n          value: pattern.value\n        };\n      } else if (pattern.type === 'block-format') {\n        return {\n          start: pattern.start,\n          format: pattern.format\n        };\n      } else if (pattern.type === 'inline-command') {\n        if (pattern.cmd === 'mceInsertContent' && pattern.start === '') {\n          return {\n            start: pattern.end,\n            replacement: pattern.value\n          };\n        } else {\n          return {\n            start: pattern.start,\n            end: pattern.end,\n            cmd: pattern.cmd,\n            value: pattern.value\n          };\n        }\n      } else if (pattern.type === 'inline-format') {\n        return {\n          start: pattern.start,\n          end: pattern.end,\n          format: pattern.format.length === 1 ? pattern.format[0] : pattern.format\n        };\n      }\n    };\n    var createPatternSet = function (patterns) {\n      return {\n        inlinePatterns: filter(patterns, isInlinePattern),\n        blockPatterns: sortPatterns(filter(patterns, isBlockPattern))\n      };\n    };\n\n    var get = function (patternsState) {\n      var setPatterns = function (newPatterns) {\n        var normalized = partition(map(newPatterns, normalizePattern));\n        if (normalized.errors.length > 0) {\n          var firstError = normalized.errors[0];\n          throw new Error(firstError.message + ':\\n' + JSON.stringify(firstError.pattern, null, 2));\n        }\n        patternsState.set(createPatternSet(normalized.values));\n      };\n      var getPatterns = function () {\n        return __spreadArray(__spreadArray([], map(patternsState.get().inlinePatterns, denormalizePattern), true), map(patternsState.get().blockPatterns, denormalizePattern), true);\n      };\n      return {\n        setPatterns: setPatterns,\n        getPatterns: getPatterns\n      };\n    };\n\n    var Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var error = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      var console = Global.console;\n      if (console) {\n        if (console.error) {\n          console.error.apply(console, args);\n        } else {\n          console.log.apply(console, args);\n        }\n      }\n    };\n    var defaultPatterns = [\n      {\n        start: '*',\n        end: '*',\n        format: 'italic'\n      },\n      {\n        start: '**',\n        end: '**',\n        format: 'bold'\n      },\n      {\n        start: '#',\n        format: 'h1'\n      },\n      {\n        start: '##',\n        format: 'h2'\n      },\n      {\n        start: '###',\n        format: 'h3'\n      },\n      {\n        start: '####',\n        format: 'h4'\n      },\n      {\n        start: '#####',\n        format: 'h5'\n      },\n      {\n        start: '######',\n        format: 'h6'\n      },\n      {\n        start: '1. ',\n        cmd: 'InsertOrderedList'\n      },\n      {\n        start: '* ',\n        cmd: 'InsertUnorderedList'\n      },\n      {\n        start: '- ',\n        cmd: 'InsertUnorderedList'\n      }\n    ];\n    var getPatternSet = function (editor) {\n      var patterns = editor.getParam('textpattern_patterns', defaultPatterns, 'array');\n      if (!isArray(patterns)) {\n        error('The setting textpattern_patterns should be an array');\n        return {\n          inlinePatterns: [],\n          blockPatterns: []\n        };\n      }\n      var normalized = partition(map(patterns, normalizePattern));\n      each(normalized.errors, function (err) {\n        return error(err.message, err.pattern);\n      });\n      return createPatternSet(normalized.values);\n    };\n    var getForcedRootBlock = function (editor) {\n      var block = editor.getParam('forced_root_block', 'p');\n      if (block === false) {\n        return '';\n      } else if (block === true) {\n        return 'p';\n      } else {\n        return block;\n      }\n    };\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    var zeroWidth = '\\uFEFF';\n    var nbsp = '\\xA0';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global = tinymce.util.Tools.resolve('tinymce.dom.TextSeeker');\n\n    var point = function (container, offset) {\n      return {\n        container: container,\n        offset: offset\n      };\n    };\n\n    var isText = function (node) {\n      return node.nodeType === Node.TEXT_NODE;\n    };\n    var cleanEmptyNodes = function (dom, node, isRoot) {\n      if (node && dom.isEmpty(node) && !isRoot(node)) {\n        var parent_1 = node.parentNode;\n        dom.remove(node);\n        cleanEmptyNodes(dom, parent_1, isRoot);\n      }\n    };\n    var deleteRng = function (dom, rng, isRoot, clean) {\n      if (clean === void 0) {\n        clean = true;\n      }\n      var startParent = rng.startContainer.parentNode;\n      var endParent = rng.endContainer.parentNode;\n      rng.deleteContents();\n      if (clean && !isRoot(rng.startContainer)) {\n        if (isText(rng.startContainer) && rng.startContainer.data.length === 0) {\n          dom.remove(rng.startContainer);\n        }\n        if (isText(rng.endContainer) && rng.endContainer.data.length === 0) {\n          dom.remove(rng.endContainer);\n        }\n        cleanEmptyNodes(dom, startParent, isRoot);\n        if (startParent !== endParent) {\n          cleanEmptyNodes(dom, endParent, isRoot);\n        }\n      }\n    };\n    var isBlockFormatName = function (name, formatter) {\n      var formatSet = formatter.get(name);\n      return isArray(formatSet) && head(formatSet).exists(function (format) {\n        return has(format, 'block');\n      });\n    };\n    var isReplacementPattern = function (pattern) {\n      return pattern.start.length === 0;\n    };\n    var getParentBlock = function (editor, rng) {\n      var parentBlockOpt = Optional.from(editor.dom.getParent(rng.startContainer, editor.dom.isBlock));\n      if (getForcedRootBlock(editor) === '') {\n        return parentBlockOpt.orThunk(function () {\n          return Optional.some(editor.getBody());\n        });\n      } else {\n        return parentBlockOpt;\n      }\n    };\n\n    var DOM = global$1.DOM;\n    var alwaysNext = function (startNode) {\n      return function (node) {\n        return startNode === node ? -1 : 0;\n      };\n    };\n    var isBoundary = function (dom) {\n      return function (node) {\n        return dom.isBlock(node) || contains([\n          'BR',\n          'IMG',\n          'HR',\n          'INPUT'\n        ], node.nodeName) || dom.getContentEditable(node) === 'false';\n      };\n    };\n    var textBefore = function (node, offset, rootNode) {\n      if (isText(node) && offset >= 0) {\n        return Optional.some(point(node, offset));\n      } else {\n        var textSeeker = global(DOM);\n        return Optional.from(textSeeker.backwards(node, offset, alwaysNext(node), rootNode)).map(function (prev) {\n          return point(prev.container, prev.container.data.length);\n        });\n      }\n    };\n    var textAfter = function (node, offset, rootNode) {\n      if (isText(node) && offset >= node.length) {\n        return Optional.some(point(node, offset));\n      } else {\n        var textSeeker = global(DOM);\n        return Optional.from(textSeeker.forwards(node, offset, alwaysNext(node), rootNode)).map(function (prev) {\n          return point(prev.container, 0);\n        });\n      }\n    };\n    var scanLeft = function (node, offset, rootNode) {\n      if (!isText(node)) {\n        return Optional.none();\n      }\n      var text = node.textContent;\n      if (offset >= 0 && offset <= text.length) {\n        return Optional.some(point(node, offset));\n      } else {\n        var textSeeker = global(DOM);\n        return Optional.from(textSeeker.backwards(node, offset, alwaysNext(node), rootNode)).bind(function (prev) {\n          var prevText = prev.container.data;\n          return scanLeft(prev.container, offset + prevText.length, rootNode);\n        });\n      }\n    };\n    var scanRight = function (node, offset, rootNode) {\n      if (!isText(node)) {\n        return Optional.none();\n      }\n      var text = node.textContent;\n      if (offset <= text.length) {\n        return Optional.some(point(node, offset));\n      } else {\n        var textSeeker = global(DOM);\n        return Optional.from(textSeeker.forwards(node, offset, alwaysNext(node), rootNode)).bind(function (next) {\n          return scanRight(next.container, offset - text.length, rootNode);\n        });\n      }\n    };\n    var repeatLeft = function (dom, node, offset, process, rootNode) {\n      var search = global(dom, isBoundary(dom));\n      return Optional.from(search.backwards(node, offset, process, rootNode));\n    };\n\n    var generatePath = function (root, node, offset) {\n      if (isText(node) && (offset < 0 || offset > node.data.length)) {\n        return [];\n      }\n      var p = [offset];\n      var current = node;\n      while (current !== root && current.parentNode) {\n        var parent_1 = current.parentNode;\n        for (var i = 0; i < parent_1.childNodes.length; i++) {\n          if (parent_1.childNodes[i] === current) {\n            p.push(i);\n            break;\n          }\n        }\n        current = parent_1;\n      }\n      return current === root ? p.reverse() : [];\n    };\n    var generatePathRange = function (root, startNode, startOffset, endNode, endOffset) {\n      var start = generatePath(root, startNode, startOffset);\n      var end = generatePath(root, endNode, endOffset);\n      return {\n        start: start,\n        end: end\n      };\n    };\n    var resolvePath = function (root, path) {\n      var nodePath = path.slice();\n      var offset = nodePath.pop();\n      var resolvedNode = foldl(nodePath, function (optNode, index) {\n        return optNode.bind(function (node) {\n          return Optional.from(node.childNodes[index]);\n        });\n      }, Optional.some(root));\n      return resolvedNode.bind(function (node) {\n        if (isText(node) && (offset < 0 || offset > node.data.length)) {\n          return Optional.none();\n        } else {\n          return Optional.some({\n            node: node,\n            offset: offset\n          });\n        }\n      });\n    };\n    var resolvePathRange = function (root, range) {\n      return resolvePath(root, range.start).bind(function (_a) {\n        var startNode = _a.node, startOffset = _a.offset;\n        return resolvePath(root, range.end).map(function (_a) {\n          var endNode = _a.node, endOffset = _a.offset;\n          var rng = document.createRange();\n          rng.setStart(startNode, startOffset);\n          rng.setEnd(endNode, endOffset);\n          return rng;\n        });\n      });\n    };\n    var generatePathRangeFromRange = function (root, range) {\n      return generatePathRange(root, range.startContainer, range.startOffset, range.endContainer, range.endOffset);\n    };\n\n    var stripPattern = function (dom, block, pattern) {\n      var firstTextNode = textAfter(block, 0, block);\n      firstTextNode.each(function (spot) {\n        var node = spot.container;\n        scanRight(node, pattern.start.length, block).each(function (end) {\n          var rng = dom.createRng();\n          rng.setStart(node, 0);\n          rng.setEnd(end.container, end.offset);\n          deleteRng(dom, rng, function (e) {\n            return e === block;\n          });\n        });\n      });\n    };\n    var applyPattern$1 = function (editor, match) {\n      var dom = editor.dom;\n      var pattern = match.pattern;\n      var rng = resolvePathRange(dom.getRoot(), match.range).getOrDie('Unable to resolve path range');\n      getParentBlock(editor, rng).each(function (block) {\n        if (pattern.type === 'block-format') {\n          if (isBlockFormatName(pattern.format, editor.formatter)) {\n            editor.undoManager.transact(function () {\n              stripPattern(editor.dom, block, pattern);\n              editor.formatter.apply(pattern.format);\n            });\n          }\n        } else if (pattern.type === 'block-command') {\n          editor.undoManager.transact(function () {\n            stripPattern(editor.dom, block, pattern);\n            editor.execCommand(pattern.cmd, false, pattern.value);\n          });\n        }\n      });\n      return true;\n    };\n    var findPattern$1 = function (patterns, text) {\n      var nuText = text.replace(nbsp, ' ');\n      return find(patterns, function (pattern) {\n        return text.indexOf(pattern.start) === 0 || nuText.indexOf(pattern.start) === 0;\n      });\n    };\n    var findPatterns$1 = function (editor, patterns) {\n      var dom = editor.dom;\n      var rng = editor.selection.getRng();\n      return getParentBlock(editor, rng).filter(function (block) {\n        var forcedRootBlock = getForcedRootBlock(editor);\n        var matchesForcedRootBlock = forcedRootBlock === '' && dom.is(block, 'body') || dom.is(block, forcedRootBlock);\n        return block !== null && matchesForcedRootBlock;\n      }).bind(function (block) {\n        var blockText = block.textContent;\n        var matchedPattern = findPattern$1(patterns, blockText);\n        return matchedPattern.map(function (pattern) {\n          if (global$2.trim(blockText).length === pattern.start.length) {\n            return [];\n          }\n          return [{\n              pattern: pattern,\n              range: generatePathRange(dom.getRoot(), block, 0, block, 0)\n            }];\n        });\n      }).getOr([]);\n    };\n    var applyMatches$1 = function (editor, matches) {\n      if (matches.length === 0) {\n        return;\n      }\n      var bookmark = editor.selection.getBookmark();\n      each(matches, function (match) {\n        return applyPattern$1(editor, match);\n      });\n      editor.selection.moveToBookmark(bookmark);\n    };\n\n    var unique = 0;\n    var generate = function (prefix) {\n      var date = new Date();\n      var time = date.getTime();\n      var random = Math.floor(Math.random() * 1000000000);\n      unique++;\n      return prefix + '_' + random + unique + String(time);\n    };\n\n    var checkRange = function (str, substr, start) {\n      return substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    };\n    var endsWith = function (str, suffix) {\n      return checkRange(str, suffix, str.length - suffix.length);\n    };\n\n    var newMarker = function (dom, id) {\n      return dom.create('span', {\n        'data-mce-type': 'bookmark',\n        id: id\n      });\n    };\n    var rangeFromMarker = function (dom, marker) {\n      var rng = dom.createRng();\n      rng.setStartAfter(marker.start);\n      rng.setEndBefore(marker.end);\n      return rng;\n    };\n    var createMarker = function (dom, markerPrefix, pathRange) {\n      var rng = resolvePathRange(dom.getRoot(), pathRange).getOrDie('Unable to resolve path range');\n      var startNode = rng.startContainer;\n      var endNode = rng.endContainer;\n      var textEnd = rng.endOffset === 0 ? endNode : endNode.splitText(rng.endOffset);\n      var textStart = rng.startOffset === 0 ? startNode : startNode.splitText(rng.startOffset);\n      return {\n        prefix: markerPrefix,\n        end: textEnd.parentNode.insertBefore(newMarker(dom, markerPrefix + '-end'), textEnd),\n        start: textStart.parentNode.insertBefore(newMarker(dom, markerPrefix + '-start'), textStart)\n      };\n    };\n    var removeMarker = function (dom, marker, isRoot) {\n      cleanEmptyNodes(dom, dom.get(marker.prefix + '-end'), isRoot);\n      cleanEmptyNodes(dom, dom.get(marker.prefix + '-start'), isRoot);\n    };\n\n    var matchesPattern = function (dom, block, patternContent) {\n      return function (element, offset) {\n        var text = element.data;\n        var searchText = text.substring(0, offset);\n        var startEndIndex = searchText.lastIndexOf(patternContent.charAt(patternContent.length - 1));\n        var startIndex = searchText.lastIndexOf(patternContent);\n        if (startIndex !== -1) {\n          return startIndex + patternContent.length;\n        } else if (startEndIndex !== -1) {\n          return startEndIndex + 1;\n        } else {\n          return -1;\n        }\n      };\n    };\n    var findPatternStartFromSpot = function (dom, pattern, block, spot) {\n      var startPattern = pattern.start;\n      var startSpot = repeatLeft(dom, spot.container, spot.offset, matchesPattern(dom, block, startPattern), block);\n      return startSpot.bind(function (spot) {\n        if (spot.offset >= startPattern.length) {\n          var rng = dom.createRng();\n          rng.setStart(spot.container, spot.offset - startPattern.length);\n          rng.setEnd(spot.container, spot.offset);\n          return Optional.some(rng);\n        } else {\n          var offset = spot.offset - startPattern.length;\n          return scanLeft(spot.container, offset, block).map(function (nextSpot) {\n            var rng = dom.createRng();\n            rng.setStart(nextSpot.container, nextSpot.offset);\n            rng.setEnd(spot.container, spot.offset);\n            return rng;\n          }).filter(function (rng) {\n            return rng.toString() === startPattern;\n          }).orThunk(function () {\n            return findPatternStartFromSpot(dom, pattern, block, point(spot.container, 0));\n          });\n        }\n      });\n    };\n    var findPatternStart = function (dom, pattern, node, offset, block, requireGap) {\n      if (requireGap === void 0) {\n        requireGap = false;\n      }\n      if (pattern.start.length === 0 && !requireGap) {\n        var rng = dom.createRng();\n        rng.setStart(node, offset);\n        rng.setEnd(node, offset);\n        return Optional.some(rng);\n      }\n      return textBefore(node, offset, block).bind(function (spot) {\n        var start = findPatternStartFromSpot(dom, pattern, block, spot);\n        return start.bind(function (startRange) {\n          if (requireGap) {\n            if (startRange.endContainer === spot.container && startRange.endOffset === spot.offset) {\n              return Optional.none();\n            } else if (spot.offset === 0 && startRange.endContainer.textContent.length === startRange.endOffset) {\n              return Optional.none();\n            }\n          }\n          return Optional.some(startRange);\n        });\n      });\n    };\n    var findPattern = function (editor, block, details) {\n      var dom = editor.dom;\n      var root = dom.getRoot();\n      var pattern = details.pattern;\n      var endNode = details.position.container;\n      var endOffset = details.position.offset;\n      return scanLeft(endNode, endOffset - details.pattern.end.length, block).bind(function (spot) {\n        var endPathRng = generatePathRange(root, spot.container, spot.offset, endNode, endOffset);\n        if (isReplacementPattern(pattern)) {\n          return Optional.some({\n            matches: [{\n                pattern: pattern,\n                startRng: endPathRng,\n                endRng: endPathRng\n              }],\n            position: spot\n          });\n        } else {\n          var resultsOpt = findPatternsRec(editor, details.remainingPatterns, spot.container, spot.offset, block);\n          var results_1 = resultsOpt.getOr({\n            matches: [],\n            position: spot\n          });\n          var pos = results_1.position;\n          var start = findPatternStart(dom, pattern, pos.container, pos.offset, block, resultsOpt.isNone());\n          return start.map(function (startRng) {\n            var startPathRng = generatePathRangeFromRange(root, startRng);\n            return {\n              matches: results_1.matches.concat([{\n                  pattern: pattern,\n                  startRng: startPathRng,\n                  endRng: endPathRng\n                }]),\n              position: point(startRng.startContainer, startRng.startOffset)\n            };\n          });\n        }\n      });\n    };\n    var findPatternsRec = function (editor, patterns, node, offset, block) {\n      var dom = editor.dom;\n      return textBefore(node, offset, dom.getRoot()).bind(function (endSpot) {\n        var rng = dom.createRng();\n        rng.setStart(block, 0);\n        rng.setEnd(node, offset);\n        var text = rng.toString();\n        for (var i = 0; i < patterns.length; i++) {\n          var pattern = patterns[i];\n          if (!endsWith(text, pattern.end)) {\n            continue;\n          }\n          var patternsWithoutCurrent = patterns.slice();\n          patternsWithoutCurrent.splice(i, 1);\n          var result = findPattern(editor, block, {\n            pattern: pattern,\n            remainingPatterns: patternsWithoutCurrent,\n            position: endSpot\n          });\n          if (result.isSome()) {\n            return result;\n          }\n        }\n        return Optional.none();\n      });\n    };\n    var applyPattern = function (editor, pattern, patternRange) {\n      editor.selection.setRng(patternRange);\n      if (pattern.type === 'inline-format') {\n        each(pattern.format, function (format) {\n          editor.formatter.apply(format);\n        });\n      } else {\n        editor.execCommand(pattern.cmd, false, pattern.value);\n      }\n    };\n    var applyReplacementPattern = function (editor, pattern, marker, isRoot) {\n      var markerRange = rangeFromMarker(editor.dom, marker);\n      deleteRng(editor.dom, markerRange, isRoot);\n      applyPattern(editor, pattern, markerRange);\n    };\n    var applyPatternWithContent = function (editor, pattern, startMarker, endMarker, isRoot) {\n      var dom = editor.dom;\n      var markerEndRange = rangeFromMarker(dom, endMarker);\n      var markerStartRange = rangeFromMarker(dom, startMarker);\n      deleteRng(dom, markerStartRange, isRoot);\n      deleteRng(dom, markerEndRange, isRoot);\n      var patternMarker = {\n        prefix: startMarker.prefix,\n        start: startMarker.end,\n        end: endMarker.start\n      };\n      var patternRange = rangeFromMarker(dom, patternMarker);\n      applyPattern(editor, pattern, patternRange);\n    };\n    var addMarkers = function (dom, matches) {\n      var markerPrefix = generate('mce_textpattern');\n      var matchesWithEnds = foldr(matches, function (acc, match) {\n        var endMarker = createMarker(dom, markerPrefix + ('_end' + acc.length), match.endRng);\n        return acc.concat([__assign(__assign({}, match), { endMarker: endMarker })]);\n      }, []);\n      return foldr(matchesWithEnds, function (acc, match) {\n        var idx = matchesWithEnds.length - acc.length - 1;\n        var startMarker = isReplacementPattern(match.pattern) ? match.endMarker : createMarker(dom, markerPrefix + ('_start' + idx), match.startRng);\n        return acc.concat([__assign(__assign({}, match), { startMarker: startMarker })]);\n      }, []);\n    };\n    var findPatterns = function (editor, patterns, space) {\n      var rng = editor.selection.getRng();\n      if (rng.collapsed === false) {\n        return [];\n      }\n      return getParentBlock(editor, rng).bind(function (block) {\n        var offset = rng.startOffset - (space ? 1 : 0);\n        return findPatternsRec(editor, patterns, rng.startContainer, offset, block);\n      }).fold(function () {\n        return [];\n      }, function (result) {\n        return result.matches;\n      });\n    };\n    var applyMatches = function (editor, matches) {\n      if (matches.length === 0) {\n        return;\n      }\n      var dom = editor.dom;\n      var bookmark = editor.selection.getBookmark();\n      var matchesWithMarkers = addMarkers(dom, matches);\n      each(matchesWithMarkers, function (match) {\n        var block = dom.getParent(match.startMarker.start, dom.isBlock);\n        var isRoot = function (node) {\n          return node === block;\n        };\n        if (isReplacementPattern(match.pattern)) {\n          applyReplacementPattern(editor, match.pattern, match.endMarker, isRoot);\n        } else {\n          applyPatternWithContent(editor, match.pattern, match.startMarker, match.endMarker, isRoot);\n        }\n        removeMarker(dom, match.endMarker, isRoot);\n        removeMarker(dom, match.startMarker, isRoot);\n      });\n      editor.selection.moveToBookmark(bookmark);\n    };\n\n    var handleEnter = function (editor, patternSet) {\n      if (!editor.selection.isCollapsed()) {\n        return false;\n      }\n      var inlineMatches = findPatterns(editor, patternSet.inlinePatterns, false);\n      var blockMatches = findPatterns$1(editor, patternSet.blockPatterns);\n      if (blockMatches.length > 0 || inlineMatches.length > 0) {\n        editor.undoManager.add();\n        editor.undoManager.extra(function () {\n          editor.execCommand('mceInsertNewLine');\n        }, function () {\n          editor.insertContent(zeroWidth, { preserve_zwsp: true });\n          applyMatches(editor, inlineMatches);\n          applyMatches$1(editor, blockMatches);\n          var range = editor.selection.getRng();\n          var spot = textBefore(range.startContainer, range.startOffset, editor.dom.getRoot());\n          editor.execCommand('mceInsertNewLine');\n          spot.each(function (s) {\n            var node = s.container;\n            if (node.data.charAt(s.offset - 1) === zeroWidth) {\n              node.deleteData(s.offset - 1, 1);\n              cleanEmptyNodes(editor.dom, node.parentNode, function (e) {\n                return e === editor.dom.getRoot();\n              });\n            }\n          });\n        });\n        return true;\n      }\n      return false;\n    };\n    var handleInlineKey = function (editor, patternSet) {\n      var inlineMatches = findPatterns(editor, patternSet.inlinePatterns, true);\n      if (inlineMatches.length > 0) {\n        editor.undoManager.transact(function () {\n          applyMatches(editor, inlineMatches);\n        });\n      }\n    };\n    var checkKeyEvent = function (codes, event, predicate) {\n      for (var i = 0; i < codes.length; i++) {\n        if (predicate(codes[i], event)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    var checkKeyCode = function (codes, event) {\n      return checkKeyEvent(codes, event, function (code, event) {\n        return code === event.keyCode && global$3.modifierPressed(event) === false;\n      });\n    };\n    var checkCharCode = function (chars, event) {\n      return checkKeyEvent(chars, event, function (chr, event) {\n        return chr.charCodeAt(0) === event.charCode;\n      });\n    };\n\n    var setup = function (editor, patternsState) {\n      var charCodes = [\n        ',',\n        '.',\n        ';',\n        ':',\n        '!',\n        '?'\n      ];\n      var keyCodes = [32];\n      editor.on('keydown', function (e) {\n        if (e.keyCode === 13 && !global$3.modifierPressed(e)) {\n          if (handleEnter(editor, patternsState.get())) {\n            e.preventDefault();\n          }\n        }\n      }, true);\n      editor.on('keyup', function (e) {\n        if (checkKeyCode(keyCodes, e)) {\n          handleInlineKey(editor, patternsState.get());\n        }\n      });\n      editor.on('keypress', function (e) {\n        if (checkCharCode(charCodes, e)) {\n          global$4.setEditorTimeout(editor, function () {\n            handleInlineKey(editor, patternsState.get());\n          });\n        }\n      });\n    };\n\n    function Plugin () {\n      global$5.add('textpattern', function (editor) {\n        var patternsState = Cell(getPatternSet(editor));\n        setup(editor, patternsState);\n        return get(patternsState);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"textpattern\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/textpattern')\n//   ES2015:\n//     import 'tinymce/plugins/textpattern'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/textpattern/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,SAAQ;AACZ,YAAI,OAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,MAAM,SAAU,GAAG;AACrB,mBAAQ;AAAA;AAEV,eAAO;AAAA,UACL,KAAK;AAAA,UACL;AAAA;AAAA;AAIJ,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAE9B,6BAAuB,IAAI,OAAM,MAAM;AACrC,YAAI,QAAQ,UAAU,WAAW;AAC/B,mBAAS,IAAI,GAAG,IAAI,MAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AAC/C,gBAAI,MAAM,CAAE,MAAK,QAAO;AACtB,kBAAI,CAAC;AACH,qBAAK,MAAM,UAAU,MAAM,KAAK,OAAM,GAAG;AAC3C,iBAAG,KAAK,MAAK;AAAA;AAAA;AAGnB,eAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK;AAAA;AAGpD,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,YAAW;AAAA;AAAA;AAG7B,UAAI,WAAW,OAAO;AACtB,UAAI,WAAW,OAAO;AACtB,UAAI,UAAU,OAAO;AAErB,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,WAAW,SAAU,QAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,UAAI,MAAM,SAAU,KAAK;AACvB,eAAO,WAAY;AACjB,gBAAM,IAAI,MAAM;AAAA;AAAA;AAGpB,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,QAAO;AAC1B,eAAO,WAAU,QAAQ,WAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,cAAc,MAAM,UAAU;AAClC,UAAI,gBAAgB,MAAM,UAAU;AACpC,UAAI,aAAa,SAAU,IAAI,GAAG;AAChC,eAAO,cAAc,KAAK,IAAI;AAAA;AAEhC,UAAI,WAAW,SAAU,IAAI,GAAG;AAC9B,eAAO,WAAW,IAAI,KAAK;AAAA;AAE7B,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,IAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,YAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,GAAG;AAC1B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,QAAQ,SAAU,IAAI,GAAG;AAC3B,iBAAS,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,cAAE,KAAK;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,QAAQ,SAAU,IAAI,GAAG,KAAK;AAChC,cAAM,IAAI,SAAU,GAAG,GAAG;AACxB,gBAAM,EAAE,KAAK,GAAG;AAAA;AAElB,eAAO;AAAA;AAET,UAAI,QAAQ,SAAU,IAAI,GAAG,KAAK;AAChC,aAAK,IAAI,SAAU,GAAG,GAAG;AACvB,gBAAM,EAAE,KAAK,GAAG;AAAA;AAElB,eAAO;AAAA;AAET,UAAI,YAAY,SAAU,IAAI,MAAM,OAAO;AACzC,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,mBAAO,SAAS,KAAK;AAAA,qBACZ,MAAM,GAAG,IAAI;AACtB;AAAA;AAAA;AAGJ,eAAO,SAAS;AAAA;AAElB,UAAI,OAAO,SAAU,IAAI,MAAM;AAC7B,eAAO,UAAU,IAAI,MAAM;AAAA;AAE7B,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,OAAO,MAAM;AACvB,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,IAAI,YAAY;AACnC,YAAI,OAAO,YAAY,KAAK,IAAI;AAChC,aAAK,KAAK;AACV,eAAO;AAAA;AAET,UAAI,QAAQ,SAAU,IAAI,GAAG;AAC3B,eAAO,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,MAAM,SAAS;AAAA;AAEnE,UAAI,OAAO,SAAU,IAAI;AACvB,eAAO,MAAM,IAAI;AAAA;AAGnB,UAAI,OAAO,OAAO;AAClB,UAAI,iBAAiB,OAAO;AAC5B,UAAI,MAAM,SAAU,KAAK,KAAK;AAC5B,eAAO,eAAe,KAAK,KAAK;AAAA;AAGlC,UAAI,aAAa,SAAU,OAAO;AAChC,YAAI,CAAC,QAAQ,QAAQ;AACnB,gBAAM,IAAI,MAAM;AAAA;AAElB,YAAI,MAAM,WAAW,GAAG;AACtB,gBAAM,IAAI,MAAM;AAAA;AAElB,YAAI,eAAe;AACnB,YAAI,MAAM;AACV,aAAK,OAAO,SAAU,OAAO,OAAO;AAClC,cAAI,SAAS,KAAK;AAClB,cAAI,OAAO,WAAW,GAAG;AACvB,kBAAM,IAAI,MAAM;AAAA;AAElB,cAAI,MAAM,OAAO;AACjB,cAAI,SAAQ,MAAM;AAClB,cAAI,IAAI,SAAS,QAAW;AAC1B,kBAAM,IAAI,MAAM,4BAA4B;AAAA,qBACnC,QAAQ,QAAQ;AACzB,kBAAM,IAAI,MAAM;AAAA,qBACP,CAAC,QAAQ,SAAQ;AAC1B,kBAAM,IAAI,MAAM;AAAA;AAElB,uBAAa,KAAK;AAClB,cAAI,OAAO,WAAY;AACrB,gBAAI,OAAO;AACX,qBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,mBAAK,MAAM,UAAU;AAAA;AAEvB,gBAAI,YAAY,KAAK;AACrB,gBAAI,cAAc,OAAM,QAAQ;AAC9B,oBAAM,IAAI,MAAM,uCAAuC,MAAM,gBAAgB,OAAM,SAAS,OAAO,SAAQ,YAAY;AAAA;AAEzH,gBAAI,QAAQ,SAAU,UAAU;AAC9B,kBAAI,aAAa,KAAK;AACtB,kBAAI,aAAa,WAAW,WAAW,QAAQ;AAC7C,sBAAM,IAAI,MAAM,mDAAmD,aAAa,KAAK,OAAO,eAAe,WAAW,KAAK;AAAA;AAE7H,kBAAI,UAAU,OAAO,cAAc,SAAU,QAAQ;AACnD,uBAAO,SAAS,YAAY;AAAA;AAE9B,kBAAI,CAAC,SAAS;AACZ,sBAAM,IAAI,MAAM,kEAAkE,WAAW,KAAK,QAAQ,iBAAiB,aAAa,KAAK;AAAA;AAE/I,qBAAO,SAAS,KAAK,MAAM,MAAM;AAAA;AAEnC,mBAAO;AAAA,cACL,MAAM,WAAY;AAChB,oBAAI,WAAW;AACf,yBAAS,MAAK,GAAG,MAAK,UAAU,QAAQ,OAAM;AAC5C,2BAAS,OAAM,UAAU;AAAA;AAE3B,oBAAI,SAAS,WAAW,MAAM,QAAQ;AACpC,wBAAM,IAAI,MAAM,iDAAiD,MAAM,SAAS,WAAW,SAAS;AAAA;AAEtG,oBAAI,SAAS,SAAS;AACtB,uBAAO,OAAO,MAAM,MAAM;AAAA;AAAA,cAE5B;AAAA,cACA,KAAK,SAAU,OAAO;AACpB,wBAAQ,IAAI,OAAO;AAAA,kBACjB;AAAA,kBACA,aAAa;AAAA,kBACb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAMlB,eAAO;AAAA;AAET,UAAI,MAAM,EAAE,UAAU;AAEtB,UAAI,SAAS;AAAA,QACX;AAAA,UACE,YAAY;AAAA,YACV;AAAA,YACA;AAAA;AAAA;AAAA,QAGJ;AAAA,UACE,YAAY;AAAA,YACV;AAAA,YACA;AAAA;AAAA;AAAA,QAGJ;AAAA,UACE,aAAa;AAAA,YACX;AAAA,YACA;AAAA;AAAA;AAAA,QAGJ;AAAA,UACE,YAAY;AAAA,YACV;AAAA,YACA;AAAA;AAAA;AAAA;AAIN,UAAI,YAAY,SAAU,SAAS;AACjC,YAAI,SAAS;AACb,YAAI,SAAS;AACb,aAAK,SAAS,SAAU,QAAQ;AAC9B,iBAAO,KAAK,SAAU,KAAK;AACzB,mBAAO,KAAK;AAAA,aACX,SAAU,QAAO;AAClB,mBAAO,KAAK;AAAA;AAAA;AAGhB,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,QAAQ,SAAU,GAAG;AACvB,YAAI,KAAK,SAAU,MAAM;AACvB,iBAAO,MAAM;AAAA;AAEf,YAAI,UAAU,SAAU,IAAI;AAC1B,iBAAO,MAAM;AAAA;AAEf,YAAI,OAAM,SAAU,GAAG;AACrB,iBAAO,MAAM,EAAE;AAAA;AAEjB,YAAI,WAAW,SAAU,IAAI;AAC3B,iBAAO,MAAM;AAAA;AAEf,YAAI,QAAO,SAAU,GAAG;AACtB,YAAE;AAAA;AAEJ,YAAI,OAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,OAAO,SAAU,GAAG,SAAS;AAC/B,iBAAO,QAAQ;AAAA;AAEjB,YAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,EAAE;AAAA;AAEX,YAAI,UAAS,SAAU,GAAG;AACxB,iBAAO,EAAE;AAAA;AAEX,YAAI,aAAa,WAAY;AAC3B,iBAAO,SAAS,KAAK;AAAA;AAEvB,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO,SAAS;AAAA,UAChB,YAAY,SAAS;AAAA,UACrB,UAAU,SAAS;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR;AAAA;AAAA;AAGJ,UAAI,UAAU,SAAU,SAAS;AAC/B,YAAI,aAAa,SAAU,GAAG;AAC5B,iBAAO;AAAA;AAET,YAAI,WAAW,WAAY;AACzB,iBAAO,IAAI,OAAO;AAAA;AAEpB,YAAI,KAAK;AACT,YAAI,UAAU,SAAU,GAAG;AACzB,iBAAO;AAAA;AAET,YAAI,OAAM,SAAU,IAAI;AACtB,iBAAO,QAAQ;AAAA;AAEjB,YAAI,WAAW,SAAU,GAAG;AAC1B,iBAAO,QAAQ,EAAE;AAAA;AAEnB,YAAI,OAAO,SAAU,IAAI;AACvB,iBAAO,QAAQ;AAAA;AAEjB,YAAI,OAAO,SAAU,SAAS,GAAG;AAC/B,iBAAO,QAAQ;AAAA;AAEjB,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA,MAAM;AAAA,UACN;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,YAAY,SAAS;AAAA;AAAA;AAGzB,UAAI,aAAa,SAAU,KAAK,KAAK;AACnC,eAAO,IAAI,KAAK,WAAY;AAC1B,iBAAO,QAAQ;AAAA,WACd;AAAA;AAEL,UAAI,SAAS;AAAA,QACX;AAAA,QACA,OAAO;AAAA,QACP;AAAA;AAGF,UAAI,kBAAkB,SAAU,SAAS;AACvC,eAAO,QAAQ,SAAS,oBAAoB,QAAQ,SAAS;AAAA;AAE/D,UAAI,iBAAiB,SAAU,SAAS;AACtC,eAAO,QAAQ,SAAS,mBAAmB,QAAQ,SAAS;AAAA;AAE9D,UAAI,eAAe,SAAU,UAAU;AACrC,eAAO,KAAK,UAAU,SAAU,GAAG,GAAG;AACpC,cAAI,EAAE,MAAM,WAAW,EAAE,MAAM,QAAQ;AACrC,mBAAO;AAAA;AAET,iBAAO,EAAE,MAAM,SAAS,EAAE,MAAM,SAAS,KAAK;AAAA;AAAA;AAGlD,UAAI,mBAAmB,SAAU,SAAS;AACxC,YAAI,MAAM,SAAU,SAAS;AAC3B,iBAAO,OAAO,MAAM;AAAA,YAClB;AAAA,YACA;AAAA;AAAA;AAGJ,YAAI,cAAc,SAAU,MAAM,UAAU,WAAW;AACrD,cAAI,QAAQ,WAAW,QAAW;AAChC,gBAAI,UAAU;AACd,gBAAI,QAAQ,QAAQ,SAAS;AAC3B,kBAAI,CAAC,OAAO,QAAQ,QAAQ,WAAW;AACrC,uBAAO,IAAI,OAAO;AAAA;AAEpB,wBAAU,QAAQ;AAAA,uBACT,SAAS,QAAQ,SAAS;AACnC,wBAAU,CAAC,QAAQ;AAAA,mBACd;AACL,qBAAO,IAAI,OAAO;AAAA;AAEpB,mBAAO,OAAO,MAAM,SAAS;AAAA,qBACpB,QAAQ,QAAQ,QAAW;AACpC,gBAAI,CAAC,SAAS,QAAQ,MAAM;AAC1B,qBAAO,IAAI,OAAO;AAAA;AAEpB,mBAAO,OAAO,MAAM,UAAU,QAAQ,KAAK,QAAQ;AAAA,iBAC9C;AACL,mBAAO,IAAI,OAAO;AAAA;AAAA;AAGtB,YAAI,CAAC,SAAS,UAAU;AACtB,iBAAO,IAAI;AAAA;AAEb,YAAI,CAAC,SAAS,QAAQ,QAAQ;AAC5B,iBAAO,IAAI;AAAA;AAEb,YAAI,QAAQ,QAAQ,QAAW;AAC7B,cAAI,CAAC,SAAS,QAAQ,MAAM;AAC1B,mBAAO,IAAI;AAAA;AAEb,cAAI,QAAQ,MAAM,WAAW,KAAK,QAAQ,IAAI,WAAW,GAAG;AAC1D,mBAAO,IAAI;AAAA;AAEb,cAAI,UAAU,QAAQ;AACtB,cAAI,QAAQ,QAAQ;AACpB,cAAI,MAAM,WAAW,GAAG;AACtB,oBAAQ;AACR,sBAAU;AAAA;AAEZ,iBAAO,YAAY,UAAU,SAAU,QAAQ;AAC7C,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,cACP,KAAK;AAAA,cACL;AAAA;AAAA,aAED,SAAU,KAAK,QAAO;AACvB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,cACP,KAAK;AAAA,cACL;AAAA,cACA,OAAO;AAAA;AAAA;AAAA,mBAGF,QAAQ,gBAAgB,QAAW;AAC5C,cAAI,CAAC,SAAS,QAAQ,cAAc;AAClC,mBAAO,IAAI;AAAA;AAEb,cAAI,QAAQ,MAAM,WAAW,GAAG;AAC9B,mBAAO,IAAI;AAAA;AAEb,iBAAO,OAAO,MAAM;AAAA,YAClB,MAAM;AAAA,YACN,OAAO;AAAA,YACP,KAAK,QAAQ;AAAA,YACb,KAAK;AAAA,YACL,OAAO,QAAQ;AAAA;AAAA,eAEZ;AACL,cAAI,QAAQ,MAAM,WAAW,GAAG;AAC9B,mBAAO,IAAI;AAAA;AAEb,iBAAO,YAAY,SAAS,SAAU,SAAS;AAC7C,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,OAAO,QAAQ;AAAA,cACf,QAAQ,QAAQ;AAAA;AAAA,aAEjB,SAAU,SAAS,cAAc;AAClC,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,OAAO,QAAQ;AAAA,cACf,KAAK;AAAA,cACL,OAAO;AAAA;AAAA;AAAA;AAAA;AAKf,UAAI,qBAAqB,SAAU,SAAS;AAC1C,YAAI,QAAQ,SAAS,iBAAiB;AACpC,iBAAO;AAAA,YACL,OAAO,QAAQ;AAAA,YACf,KAAK,QAAQ;AAAA,YACb,OAAO,QAAQ;AAAA;AAAA,mBAER,QAAQ,SAAS,gBAAgB;AAC1C,iBAAO;AAAA,YACL,OAAO,QAAQ;AAAA,YACf,QAAQ,QAAQ;AAAA;AAAA,mBAET,QAAQ,SAAS,kBAAkB;AAC5C,cAAI,QAAQ,QAAQ,sBAAsB,QAAQ,UAAU,IAAI;AAC9D,mBAAO;AAAA,cACL,OAAO,QAAQ;AAAA,cACf,aAAa,QAAQ;AAAA;AAAA,iBAElB;AACL,mBAAO;AAAA,cACL,OAAO,QAAQ;AAAA,cACf,KAAK,QAAQ;AAAA,cACb,KAAK,QAAQ;AAAA,cACb,OAAO,QAAQ;AAAA;AAAA;AAAA,mBAGV,QAAQ,SAAS,iBAAiB;AAC3C,iBAAO;AAAA,YACL,OAAO,QAAQ;AAAA,YACf,KAAK,QAAQ;AAAA,YACb,QAAQ,QAAQ,OAAO,WAAW,IAAI,QAAQ,OAAO,KAAK,QAAQ;AAAA;AAAA;AAAA;AAIxE,UAAI,mBAAmB,SAAU,UAAU;AACzC,eAAO;AAAA,UACL,gBAAgB,OAAO,UAAU;AAAA,UACjC,eAAe,aAAa,OAAO,UAAU;AAAA;AAAA;AAIjD,UAAI,MAAM,SAAU,eAAe;AACjC,YAAI,cAAc,SAAU,aAAa;AACvC,cAAI,aAAa,UAAU,IAAI,aAAa;AAC5C,cAAI,WAAW,OAAO,SAAS,GAAG;AAChC,gBAAI,aAAa,WAAW,OAAO;AACnC,kBAAM,IAAI,MAAM,WAAW,UAAU,QAAQ,KAAK,UAAU,WAAW,SAAS,MAAM;AAAA;AAExF,wBAAc,IAAI,iBAAiB,WAAW;AAAA;AAEhD,YAAI,cAAc,WAAY;AAC5B,iBAAO,cAAc,cAAc,IAAI,IAAI,cAAc,MAAM,gBAAgB,qBAAqB,OAAO,IAAI,cAAc,MAAM,eAAe,qBAAqB;AAAA;AAEzK,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,SAAS,OAAO,WAAW,cAAc,SAAS,SAAS;AAE/D,UAAI,QAAQ,WAAY;AACtB,YAAI,OAAO;AACX,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,eAAK,MAAM,UAAU;AAAA;AAEvB,YAAI,WAAU,OAAO;AACrB,YAAI,UAAS;AACX,cAAI,SAAQ,OAAO;AACjB,qBAAQ,MAAM,MAAM,UAAS;AAAA,iBACxB;AACL,qBAAQ,IAAI,MAAM,UAAS;AAAA;AAAA;AAAA;AAIjC,UAAI,kBAAkB;AAAA,QACpB;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA;AAAA,QAEV;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA;AAAA,QAEP;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA;AAAA,QAEP;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA;AAAA;AAGT,UAAI,gBAAgB,SAAU,QAAQ;AACpC,YAAI,WAAW,OAAO,SAAS,wBAAwB,iBAAiB;AACxE,YAAI,CAAC,QAAQ,WAAW;AACtB,gBAAM;AACN,iBAAO;AAAA,YACL,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA;AAGnB,YAAI,aAAa,UAAU,IAAI,UAAU;AACzC,aAAK,WAAW,QAAQ,SAAU,KAAK;AACrC,iBAAO,MAAM,IAAI,SAAS,IAAI;AAAA;AAEhC,eAAO,iBAAiB,WAAW;AAAA;AAErC,UAAI,qBAAqB,SAAU,QAAQ;AACzC,YAAI,QAAQ,OAAO,SAAS,qBAAqB;AACjD,YAAI,UAAU,OAAO;AACnB,iBAAO;AAAA,mBACE,UAAU,MAAM;AACzB,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAIX,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,YAAY;AAChB,UAAI,OAAO;AAEX,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,QAAQ,SAAU,WAAW,QAAQ;AACvC,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAIJ,UAAI,SAAS,SAAU,MAAM;AAC3B,eAAO,KAAK,aAAa,KAAK;AAAA;AAEhC,UAAI,kBAAkB,SAAU,KAAK,MAAM,QAAQ;AACjD,YAAI,QAAQ,IAAI,QAAQ,SAAS,CAAC,OAAO,OAAO;AAC9C,cAAI,WAAW,KAAK;AACpB,cAAI,OAAO;AACX,0BAAgB,KAAK,UAAU;AAAA;AAAA;AAGnC,UAAI,YAAY,SAAU,KAAK,KAAK,QAAQ,OAAO;AACjD,YAAI,UAAU,QAAQ;AACpB,kBAAQ;AAAA;AAEV,YAAI,cAAc,IAAI,eAAe;AACrC,YAAI,YAAY,IAAI,aAAa;AACjC,YAAI;AACJ,YAAI,SAAS,CAAC,OAAO,IAAI,iBAAiB;AACxC,cAAI,OAAO,IAAI,mBAAmB,IAAI,eAAe,KAAK,WAAW,GAAG;AACtE,gBAAI,OAAO,IAAI;AAAA;AAEjB,cAAI,OAAO,IAAI,iBAAiB,IAAI,aAAa,KAAK,WAAW,GAAG;AAClE,gBAAI,OAAO,IAAI;AAAA;AAEjB,0BAAgB,KAAK,aAAa;AAClC,cAAI,gBAAgB,WAAW;AAC7B,4BAAgB,KAAK,WAAW;AAAA;AAAA;AAAA;AAItC,UAAI,oBAAoB,SAAU,MAAM,WAAW;AACjD,YAAI,YAAY,UAAU,IAAI;AAC9B,eAAO,QAAQ,cAAc,KAAK,WAAW,OAAO,SAAU,QAAQ;AACpE,iBAAO,IAAI,QAAQ;AAAA;AAAA;AAGvB,UAAI,uBAAuB,SAAU,SAAS;AAC5C,eAAO,QAAQ,MAAM,WAAW;AAAA;AAElC,UAAI,iBAAiB,SAAU,QAAQ,KAAK;AAC1C,YAAI,iBAAiB,SAAS,KAAK,OAAO,IAAI,UAAU,IAAI,gBAAgB,OAAO,IAAI;AACvF,YAAI,mBAAmB,YAAY,IAAI;AACrC,iBAAO,eAAe,QAAQ,WAAY;AACxC,mBAAO,SAAS,KAAK,OAAO;AAAA;AAAA,eAEzB;AACL,iBAAO;AAAA;AAAA;AAIX,UAAI,MAAM,SAAS;AACnB,UAAI,aAAa,SAAU,WAAW;AACpC,eAAO,SAAU,MAAM;AACrB,iBAAO,cAAc,OAAO,KAAK;AAAA;AAAA;AAGrC,UAAI,aAAa,SAAU,KAAK;AAC9B,eAAO,SAAU,MAAM;AACrB,iBAAO,IAAI,QAAQ,SAAS,SAAS;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,aACC,KAAK,aAAa,IAAI,mBAAmB,UAAU;AAAA;AAAA;AAG1D,UAAI,aAAa,SAAU,MAAM,QAAQ,UAAU;AACjD,YAAI,OAAO,SAAS,UAAU,GAAG;AAC/B,iBAAO,SAAS,KAAK,MAAM,MAAM;AAAA,eAC5B;AACL,cAAI,aAAa,OAAO;AACxB,iBAAO,SAAS,KAAK,WAAW,UAAU,MAAM,QAAQ,WAAW,OAAO,WAAW,IAAI,SAAU,MAAM;AACvG,mBAAO,MAAM,KAAK,WAAW,KAAK,UAAU,KAAK;AAAA;AAAA;AAAA;AAIvD,UAAI,YAAY,SAAU,MAAM,QAAQ,UAAU;AAChD,YAAI,OAAO,SAAS,UAAU,KAAK,QAAQ;AACzC,iBAAO,SAAS,KAAK,MAAM,MAAM;AAAA,eAC5B;AACL,cAAI,aAAa,OAAO;AACxB,iBAAO,SAAS,KAAK,WAAW,SAAS,MAAM,QAAQ,WAAW,OAAO,WAAW,IAAI,SAAU,MAAM;AACtG,mBAAO,MAAM,KAAK,WAAW;AAAA;AAAA;AAAA;AAInC,UAAI,WAAW,SAAU,MAAM,QAAQ,UAAU;AAC/C,YAAI,CAAC,OAAO,OAAO;AACjB,iBAAO,SAAS;AAAA;AAElB,YAAI,OAAO,KAAK;AAChB,YAAI,UAAU,KAAK,UAAU,KAAK,QAAQ;AACxC,iBAAO,SAAS,KAAK,MAAM,MAAM;AAAA,eAC5B;AACL,cAAI,aAAa,OAAO;AACxB,iBAAO,SAAS,KAAK,WAAW,UAAU,MAAM,QAAQ,WAAW,OAAO,WAAW,KAAK,SAAU,MAAM;AACxG,gBAAI,WAAW,KAAK,UAAU;AAC9B,mBAAO,SAAS,KAAK,WAAW,SAAS,SAAS,QAAQ;AAAA;AAAA;AAAA;AAIhE,UAAI,YAAY,SAAU,MAAM,QAAQ,UAAU;AAChD,YAAI,CAAC,OAAO,OAAO;AACjB,iBAAO,SAAS;AAAA;AAElB,YAAI,OAAO,KAAK;AAChB,YAAI,UAAU,KAAK,QAAQ;AACzB,iBAAO,SAAS,KAAK,MAAM,MAAM;AAAA,eAC5B;AACL,cAAI,aAAa,OAAO;AACxB,iBAAO,SAAS,KAAK,WAAW,SAAS,MAAM,QAAQ,WAAW,OAAO,WAAW,KAAK,SAAU,MAAM;AACvG,mBAAO,UAAU,KAAK,WAAW,SAAS,KAAK,QAAQ;AAAA;AAAA;AAAA;AAI7D,UAAI,aAAa,SAAU,KAAK,MAAM,QAAQ,SAAS,UAAU;AAC/D,YAAI,SAAS,OAAO,KAAK,WAAW;AACpC,eAAO,SAAS,KAAK,OAAO,UAAU,MAAM,QAAQ,SAAS;AAAA;AAG/D,UAAI,eAAe,SAAU,MAAM,MAAM,QAAQ;AAC/C,YAAI,OAAO,SAAU,UAAS,KAAK,SAAS,KAAK,KAAK,SAAS;AAC7D,iBAAO;AAAA;AAET,YAAI,IAAI,CAAC;AACT,YAAI,UAAU;AACd,eAAO,YAAY,QAAQ,QAAQ,YAAY;AAC7C,cAAI,WAAW,QAAQ;AACvB,mBAAS,IAAI,GAAG,IAAI,SAAS,WAAW,QAAQ,KAAK;AACnD,gBAAI,SAAS,WAAW,OAAO,SAAS;AACtC,gBAAE,KAAK;AACP;AAAA;AAAA;AAGJ,oBAAU;AAAA;AAEZ,eAAO,YAAY,OAAO,EAAE,YAAY;AAAA;AAE1C,UAAI,oBAAoB,SAAU,MAAM,WAAW,aAAa,SAAS,WAAW;AAClF,YAAI,QAAQ,aAAa,MAAM,WAAW;AAC1C,YAAI,MAAM,aAAa,MAAM,SAAS;AACtC,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,cAAc,SAAU,MAAM,MAAM;AACtC,YAAI,WAAW,KAAK;AACpB,YAAI,SAAS,SAAS;AACtB,YAAI,eAAe,MAAM,UAAU,SAAU,SAAS,OAAO;AAC3D,iBAAO,QAAQ,KAAK,SAAU,MAAM;AAClC,mBAAO,SAAS,KAAK,KAAK,WAAW;AAAA;AAAA,WAEtC,SAAS,KAAK;AACjB,eAAO,aAAa,KAAK,SAAU,MAAM;AACvC,cAAI,OAAO,SAAU,UAAS,KAAK,SAAS,KAAK,KAAK,SAAS;AAC7D,mBAAO,SAAS;AAAA,iBACX;AACL,mBAAO,SAAS,KAAK;AAAA,cACnB;AAAA,cACA;AAAA;AAAA;AAAA;AAAA;AAKR,UAAI,mBAAmB,SAAU,MAAM,OAAO;AAC5C,eAAO,YAAY,MAAM,MAAM,OAAO,KAAK,SAAU,IAAI;AACvD,cAAI,YAAY,GAAG,MAAM,cAAc,GAAG;AAC1C,iBAAO,YAAY,MAAM,MAAM,KAAK,IAAI,SAAU,KAAI;AACpD,gBAAI,UAAU,IAAG,MAAM,YAAY,IAAG;AACtC,gBAAI,MAAM,SAAS;AACnB,gBAAI,SAAS,WAAW;AACxB,gBAAI,OAAO,SAAS;AACpB,mBAAO;AAAA;AAAA;AAAA;AAIb,UAAI,6BAA6B,SAAU,MAAM,OAAO;AACtD,eAAO,kBAAkB,MAAM,MAAM,gBAAgB,MAAM,aAAa,MAAM,cAAc,MAAM;AAAA;AAGpG,UAAI,eAAe,SAAU,KAAK,OAAO,SAAS;AAChD,YAAI,gBAAgB,UAAU,OAAO,GAAG;AACxC,sBAAc,KAAK,SAAU,MAAM;AACjC,cAAI,OAAO,KAAK;AAChB,oBAAU,MAAM,QAAQ,MAAM,QAAQ,OAAO,KAAK,SAAU,KAAK;AAC/D,gBAAI,MAAM,IAAI;AACd,gBAAI,SAAS,MAAM;AACnB,gBAAI,OAAO,IAAI,WAAW,IAAI;AAC9B,sBAAU,KAAK,KAAK,SAAU,GAAG;AAC/B,qBAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAKrB,UAAI,iBAAiB,SAAU,QAAQ,OAAO;AAC5C,YAAI,MAAM,OAAO;AACjB,YAAI,UAAU,MAAM;AACpB,YAAI,MAAM,iBAAiB,IAAI,WAAW,MAAM,OAAO,SAAS;AAChE,uBAAe,QAAQ,KAAK,KAAK,SAAU,OAAO;AAChD,cAAI,QAAQ,SAAS,gBAAgB;AACnC,gBAAI,kBAAkB,QAAQ,QAAQ,OAAO,YAAY;AACvD,qBAAO,YAAY,SAAS,WAAY;AACtC,6BAAa,OAAO,KAAK,OAAO;AAChC,uBAAO,UAAU,MAAM,QAAQ;AAAA;AAAA;AAAA,qBAG1B,QAAQ,SAAS,iBAAiB;AAC3C,mBAAO,YAAY,SAAS,WAAY;AACtC,2BAAa,OAAO,KAAK,OAAO;AAChC,qBAAO,YAAY,QAAQ,KAAK,OAAO,QAAQ;AAAA;AAAA;AAAA;AAIrD,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,UAAU,MAAM;AAC5C,YAAI,SAAS,KAAK,QAAQ,MAAM;AAChC,eAAO,KAAK,UAAU,SAAU,SAAS;AACvC,iBAAO,KAAK,QAAQ,QAAQ,WAAW,KAAK,OAAO,QAAQ,QAAQ,WAAW;AAAA;AAAA;AAGlF,UAAI,iBAAiB,SAAU,QAAQ,UAAU;AAC/C,YAAI,MAAM,OAAO;AACjB,YAAI,MAAM,OAAO,UAAU;AAC3B,eAAO,eAAe,QAAQ,KAAK,OAAO,SAAU,OAAO;AACzD,cAAI,kBAAkB,mBAAmB;AACzC,cAAI,yBAAyB,oBAAoB,MAAM,IAAI,GAAG,OAAO,WAAW,IAAI,GAAG,OAAO;AAC9F,iBAAO,UAAU,QAAQ;AAAA,WACxB,KAAK,SAAU,OAAO;AACvB,cAAI,YAAY,MAAM;AACtB,cAAI,iBAAiB,cAAc,UAAU;AAC7C,iBAAO,eAAe,IAAI,SAAU,SAAS;AAC3C,gBAAI,SAAS,KAAK,WAAW,WAAW,QAAQ,MAAM,QAAQ;AAC5D,qBAAO;AAAA;AAET,mBAAO,CAAC;AAAA,cACJ;AAAA,cACA,OAAO,kBAAkB,IAAI,WAAW,OAAO,GAAG,OAAO;AAAA;AAAA;AAAA,WAG9D,MAAM;AAAA;AAEX,UAAI,iBAAiB,SAAU,QAAQ,SAAS;AAC9C,YAAI,QAAQ,WAAW,GAAG;AACxB;AAAA;AAEF,YAAI,WAAW,OAAO,UAAU;AAChC,aAAK,SAAS,SAAU,OAAO;AAC7B,iBAAO,eAAe,QAAQ;AAAA;AAEhC,eAAO,UAAU,eAAe;AAAA;AAGlC,UAAI,SAAS;AACb,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,OAAO,IAAI;AACf,YAAI,OAAO,KAAK;AAChB,YAAI,SAAS,KAAK,MAAM,KAAK,WAAW;AACxC;AACA,eAAO,SAAS,MAAM,SAAS,SAAS,OAAO;AAAA;AAGjD,UAAI,aAAa,SAAU,KAAK,QAAQ,OAAO;AAC7C,eAAO,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,YAAY;AAAA;AAEtG,UAAI,WAAW,SAAU,KAAK,QAAQ;AACpC,eAAO,WAAW,KAAK,QAAQ,IAAI,SAAS,OAAO;AAAA;AAGrD,UAAI,YAAY,SAAU,KAAK,IAAI;AACjC,eAAO,IAAI,OAAO,QAAQ;AAAA,UACxB,iBAAiB;AAAA,UACjB;AAAA;AAAA;AAGJ,UAAI,kBAAkB,SAAU,KAAK,QAAQ;AAC3C,YAAI,MAAM,IAAI;AACd,YAAI,cAAc,OAAO;AACzB,YAAI,aAAa,OAAO;AACxB,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,KAAK,cAAc,WAAW;AACzD,YAAI,MAAM,iBAAiB,IAAI,WAAW,WAAW,SAAS;AAC9D,YAAI,YAAY,IAAI;AACpB,YAAI,UAAU,IAAI;AAClB,YAAI,UAAU,IAAI,cAAc,IAAI,UAAU,QAAQ,UAAU,IAAI;AACpE,YAAI,YAAY,IAAI,gBAAgB,IAAI,YAAY,UAAU,UAAU,IAAI;AAC5E,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,KAAK,QAAQ,WAAW,aAAa,UAAU,KAAK,eAAe,SAAS;AAAA,UAC5E,OAAO,UAAU,WAAW,aAAa,UAAU,KAAK,eAAe,WAAW;AAAA;AAAA;AAGtF,UAAI,eAAe,SAAU,KAAK,QAAQ,QAAQ;AAChD,wBAAgB,KAAK,IAAI,IAAI,OAAO,SAAS,SAAS;AACtD,wBAAgB,KAAK,IAAI,IAAI,OAAO,SAAS,WAAW;AAAA;AAG1D,UAAI,iBAAiB,SAAU,KAAK,OAAO,gBAAgB;AACzD,eAAO,SAAU,SAAS,QAAQ;AAChC,cAAI,OAAO,QAAQ;AACnB,cAAI,aAAa,KAAK,UAAU,GAAG;AACnC,cAAI,gBAAgB,WAAW,YAAY,eAAe,OAAO,eAAe,SAAS;AACzF,cAAI,aAAa,WAAW,YAAY;AACxC,cAAI,eAAe,IAAI;AACrB,mBAAO,aAAa,eAAe;AAAA,qBAC1B,kBAAkB,IAAI;AAC/B,mBAAO,gBAAgB;AAAA,iBAClB;AACL,mBAAO;AAAA;AAAA;AAAA;AAIb,UAAI,2BAA2B,SAAU,KAAK,SAAS,OAAO,MAAM;AAClE,YAAI,eAAe,QAAQ;AAC3B,YAAI,YAAY,WAAW,KAAK,KAAK,WAAW,KAAK,QAAQ,eAAe,KAAK,OAAO,eAAe;AACvG,eAAO,UAAU,KAAK,SAAU,OAAM;AACpC,cAAI,MAAK,UAAU,aAAa,QAAQ;AACtC,gBAAI,MAAM,IAAI;AACd,gBAAI,SAAS,MAAK,WAAW,MAAK,SAAS,aAAa;AACxD,gBAAI,OAAO,MAAK,WAAW,MAAK;AAChC,mBAAO,SAAS,KAAK;AAAA,iBAChB;AACL,gBAAI,SAAS,MAAK,SAAS,aAAa;AACxC,mBAAO,SAAS,MAAK,WAAW,QAAQ,OAAO,IAAI,SAAU,UAAU;AACrE,kBAAI,OAAM,IAAI;AACd,mBAAI,SAAS,SAAS,WAAW,SAAS;AAC1C,mBAAI,OAAO,MAAK,WAAW,MAAK;AAChC,qBAAO;AAAA,eACN,OAAO,SAAU,MAAK;AACvB,qBAAO,KAAI,eAAe;AAAA,eACzB,QAAQ,WAAY;AACrB,qBAAO,yBAAyB,KAAK,SAAS,OAAO,MAAM,MAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAKnF,UAAI,mBAAmB,SAAU,KAAK,SAAS,MAAM,QAAQ,OAAO,YAAY;AAC9E,YAAI,eAAe,QAAQ;AACzB,uBAAa;AAAA;AAEf,YAAI,QAAQ,MAAM,WAAW,KAAK,CAAC,YAAY;AAC7C,cAAI,MAAM,IAAI;AACd,cAAI,SAAS,MAAM;AACnB,cAAI,OAAO,MAAM;AACjB,iBAAO,SAAS,KAAK;AAAA;AAEvB,eAAO,WAAW,MAAM,QAAQ,OAAO,KAAK,SAAU,MAAM;AAC1D,cAAI,QAAQ,yBAAyB,KAAK,SAAS,OAAO;AAC1D,iBAAO,MAAM,KAAK,SAAU,YAAY;AACtC,gBAAI,YAAY;AACd,kBAAI,WAAW,iBAAiB,KAAK,aAAa,WAAW,cAAc,KAAK,QAAQ;AACtF,uBAAO,SAAS;AAAA,yBACP,KAAK,WAAW,KAAK,WAAW,aAAa,YAAY,WAAW,WAAW,WAAW;AACnG,uBAAO,SAAS;AAAA;AAAA;AAGpB,mBAAO,SAAS,KAAK;AAAA;AAAA;AAAA;AAI3B,UAAI,cAAc,SAAU,QAAQ,OAAO,SAAS;AAClD,YAAI,MAAM,OAAO;AACjB,YAAI,OAAO,IAAI;AACf,YAAI,UAAU,QAAQ;AACtB,YAAI,UAAU,QAAQ,SAAS;AAC/B,YAAI,YAAY,QAAQ,SAAS;AACjC,eAAO,SAAS,SAAS,YAAY,QAAQ,QAAQ,IAAI,QAAQ,OAAO,KAAK,SAAU,MAAM;AAC3F,cAAI,aAAa,kBAAkB,MAAM,KAAK,WAAW,KAAK,QAAQ,SAAS;AAC/E,cAAI,qBAAqB,UAAU;AACjC,mBAAO,SAAS,KAAK;AAAA,cACnB,SAAS,CAAC;AAAA,gBACN;AAAA,gBACA,UAAU;AAAA,gBACV,QAAQ;AAAA;AAAA,cAEZ,UAAU;AAAA;AAAA,iBAEP;AACL,gBAAI,aAAa,gBAAgB,QAAQ,QAAQ,mBAAmB,KAAK,WAAW,KAAK,QAAQ;AACjG,gBAAI,YAAY,WAAW,MAAM;AAAA,cAC/B,SAAS;AAAA,cACT,UAAU;AAAA;AAEZ,gBAAI,MAAM,UAAU;AACpB,gBAAI,QAAQ,iBAAiB,KAAK,SAAS,IAAI,WAAW,IAAI,QAAQ,OAAO,WAAW;AACxF,mBAAO,MAAM,IAAI,SAAU,UAAU;AACnC,kBAAI,eAAe,2BAA2B,MAAM;AACpD,qBAAO;AAAA,gBACL,SAAS,UAAU,QAAQ,OAAO,CAAC;AAAA,kBAC/B;AAAA,kBACA,UAAU;AAAA,kBACV,QAAQ;AAAA;AAAA,gBAEZ,UAAU,MAAM,SAAS,gBAAgB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAM5D,UAAI,kBAAkB,SAAU,QAAQ,UAAU,MAAM,QAAQ,OAAO;AACrE,YAAI,MAAM,OAAO;AACjB,eAAO,WAAW,MAAM,QAAQ,IAAI,WAAW,KAAK,SAAU,SAAS;AACrE,cAAI,MAAM,IAAI;AACd,cAAI,SAAS,OAAO;AACpB,cAAI,OAAO,MAAM;AACjB,cAAI,OAAO,IAAI;AACf,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAI,UAAU,SAAS;AACvB,gBAAI,CAAC,SAAS,MAAM,QAAQ,MAAM;AAChC;AAAA;AAEF,gBAAI,yBAAyB,SAAS;AACtC,mCAAuB,OAAO,GAAG;AACjC,gBAAI,SAAS,YAAY,QAAQ,OAAO;AAAA,cACtC;AAAA,cACA,mBAAmB;AAAA,cACnB,UAAU;AAAA;AAEZ,gBAAI,OAAO,UAAU;AACnB,qBAAO;AAAA;AAAA;AAGX,iBAAO,SAAS;AAAA;AAAA;AAGpB,UAAI,eAAe,SAAU,QAAQ,SAAS,cAAc;AAC1D,eAAO,UAAU,OAAO;AACxB,YAAI,QAAQ,SAAS,iBAAiB;AACpC,eAAK,QAAQ,QAAQ,SAAU,QAAQ;AACrC,mBAAO,UAAU,MAAM;AAAA;AAAA,eAEpB;AACL,iBAAO,YAAY,QAAQ,KAAK,OAAO,QAAQ;AAAA;AAAA;AAGnD,UAAI,0BAA0B,SAAU,QAAQ,SAAS,QAAQ,QAAQ;AACvE,YAAI,cAAc,gBAAgB,OAAO,KAAK;AAC9C,kBAAU,OAAO,KAAK,aAAa;AACnC,qBAAa,QAAQ,SAAS;AAAA;AAEhC,UAAI,0BAA0B,SAAU,QAAQ,SAAS,aAAa,WAAW,QAAQ;AACvF,YAAI,MAAM,OAAO;AACjB,YAAI,iBAAiB,gBAAgB,KAAK;AAC1C,YAAI,mBAAmB,gBAAgB,KAAK;AAC5C,kBAAU,KAAK,kBAAkB;AACjC,kBAAU,KAAK,gBAAgB;AAC/B,YAAI,gBAAgB;AAAA,UAClB,QAAQ,YAAY;AAAA,UACpB,OAAO,YAAY;AAAA,UACnB,KAAK,UAAU;AAAA;AAEjB,YAAI,eAAe,gBAAgB,KAAK;AACxC,qBAAa,QAAQ,SAAS;AAAA;AAEhC,UAAI,aAAa,SAAU,KAAK,SAAS;AACvC,YAAI,eAAe,SAAS;AAC5B,YAAI,kBAAkB,MAAM,SAAS,SAAU,KAAK,OAAO;AACzD,cAAI,YAAY,aAAa,KAAK,eAAgB,UAAS,IAAI,SAAS,MAAM;AAC9E,iBAAO,IAAI,OAAO,CAAC,SAAS,SAAS,IAAI,QAAQ,EAAE;AAAA,WAClD;AACH,eAAO,MAAM,iBAAiB,SAAU,KAAK,OAAO;AAClD,cAAI,MAAM,gBAAgB,SAAS,IAAI,SAAS;AAChD,cAAI,cAAc,qBAAqB,MAAM,WAAW,MAAM,YAAY,aAAa,KAAK,eAAgB,YAAW,MAAM,MAAM;AACnI,iBAAO,IAAI,OAAO,CAAC,SAAS,SAAS,IAAI,QAAQ,EAAE;AAAA,WAClD;AAAA;AAEL,UAAI,eAAe,SAAU,QAAQ,UAAU,OAAO;AACpD,YAAI,MAAM,OAAO,UAAU;AAC3B,YAAI,IAAI,cAAc,OAAO;AAC3B,iBAAO;AAAA;AAET,eAAO,eAAe,QAAQ,KAAK,KAAK,SAAU,OAAO;AACvD,cAAI,SAAS,IAAI,cAAe,SAAQ,IAAI;AAC5C,iBAAO,gBAAgB,QAAQ,UAAU,IAAI,gBAAgB,QAAQ;AAAA,WACpE,KAAK,WAAY;AAClB,iBAAO;AAAA,WACN,SAAU,QAAQ;AACnB,iBAAO,OAAO;AAAA;AAAA;AAGlB,UAAI,eAAe,SAAU,QAAQ,SAAS;AAC5C,YAAI,QAAQ,WAAW,GAAG;AACxB;AAAA;AAEF,YAAI,MAAM,OAAO;AACjB,YAAI,WAAW,OAAO,UAAU;AAChC,YAAI,qBAAqB,WAAW,KAAK;AACzC,aAAK,oBAAoB,SAAU,OAAO;AACxC,cAAI,QAAQ,IAAI,UAAU,MAAM,YAAY,OAAO,IAAI;AACvD,cAAI,SAAS,SAAU,MAAM;AAC3B,mBAAO,SAAS;AAAA;AAElB,cAAI,qBAAqB,MAAM,UAAU;AACvC,oCAAwB,QAAQ,MAAM,SAAS,MAAM,WAAW;AAAA,iBAC3D;AACL,oCAAwB,QAAQ,MAAM,SAAS,MAAM,aAAa,MAAM,WAAW;AAAA;AAErF,uBAAa,KAAK,MAAM,WAAW;AACnC,uBAAa,KAAK,MAAM,aAAa;AAAA;AAEvC,eAAO,UAAU,eAAe;AAAA;AAGlC,UAAI,cAAc,SAAU,QAAQ,YAAY;AAC9C,YAAI,CAAC,OAAO,UAAU,eAAe;AACnC,iBAAO;AAAA;AAET,YAAI,gBAAgB,aAAa,QAAQ,WAAW,gBAAgB;AACpE,YAAI,eAAe,eAAe,QAAQ,WAAW;AACrD,YAAI,aAAa,SAAS,KAAK,cAAc,SAAS,GAAG;AACvD,iBAAO,YAAY;AACnB,iBAAO,YAAY,MAAM,WAAY;AACnC,mBAAO,YAAY;AAAA,aAClB,WAAY;AACb,mBAAO,cAAc,WAAW,EAAE,eAAe;AACjD,yBAAa,QAAQ;AACrB,2BAAe,QAAQ;AACvB,gBAAI,QAAQ,OAAO,UAAU;AAC7B,gBAAI,OAAO,WAAW,MAAM,gBAAgB,MAAM,aAAa,OAAO,IAAI;AAC1E,mBAAO,YAAY;AACnB,iBAAK,KAAK,SAAU,GAAG;AACrB,kBAAI,OAAO,EAAE;AACb,kBAAI,KAAK,KAAK,OAAO,EAAE,SAAS,OAAO,WAAW;AAChD,qBAAK,WAAW,EAAE,SAAS,GAAG;AAC9B,gCAAgB,OAAO,KAAK,KAAK,YAAY,SAAU,GAAG;AACxD,yBAAO,MAAM,OAAO,IAAI;AAAA;AAAA;AAAA;AAAA;AAKhC,iBAAO;AAAA;AAET,eAAO;AAAA;AAET,UAAI,kBAAkB,SAAU,QAAQ,YAAY;AAClD,YAAI,gBAAgB,aAAa,QAAQ,WAAW,gBAAgB;AACpE,YAAI,cAAc,SAAS,GAAG;AAC5B,iBAAO,YAAY,SAAS,WAAY;AACtC,yBAAa,QAAQ;AAAA;AAAA;AAAA;AAI3B,UAAI,gBAAgB,SAAU,OAAO,OAAO,WAAW;AACrD,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,UAAU,MAAM,IAAI,QAAQ;AAC9B,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,eAAe,SAAU,OAAO,OAAO;AACzC,eAAO,cAAc,OAAO,OAAO,SAAU,MAAM,QAAO;AACxD,iBAAO,SAAS,OAAM,WAAW,SAAS,gBAAgB,YAAW;AAAA;AAAA;AAGzE,UAAI,gBAAgB,SAAU,OAAO,OAAO;AAC1C,eAAO,cAAc,OAAO,OAAO,SAAU,KAAK,QAAO;AACvD,iBAAO,IAAI,WAAW,OAAO,OAAM;AAAA;AAAA;AAIvC,UAAI,QAAQ,SAAU,QAAQ,eAAe;AAC3C,YAAI,YAAY;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAEF,YAAI,WAAW,CAAC;AAChB,eAAO,GAAG,WAAW,SAAU,GAAG;AAChC,cAAI,EAAE,YAAY,MAAM,CAAC,SAAS,gBAAgB,IAAI;AACpD,gBAAI,YAAY,QAAQ,cAAc,QAAQ;AAC5C,gBAAE;AAAA;AAAA;AAAA,WAGL;AACH,eAAO,GAAG,SAAS,SAAU,GAAG;AAC9B,cAAI,aAAa,UAAU,IAAI;AAC7B,4BAAgB,QAAQ,cAAc;AAAA;AAAA;AAG1C,eAAO,GAAG,YAAY,SAAU,GAAG;AACjC,cAAI,cAAc,WAAW,IAAI;AAC/B,qBAAS,iBAAiB,QAAQ,WAAY;AAC5C,8BAAgB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAM9C,wBAAmB;AACjB,iBAAS,IAAI,eAAe,SAAU,QAAQ;AAC5C,cAAI,gBAAgB,KAAK,cAAc;AACvC,gBAAM,QAAQ;AACd,iBAAO,IAAI;AAAA;AAAA;AAIf;AAAA;AAAA;AAAA;;;AC31CJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,sCAAQ;", "names": []}