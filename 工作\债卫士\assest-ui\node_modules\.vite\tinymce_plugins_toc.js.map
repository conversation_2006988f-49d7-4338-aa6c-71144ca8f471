{"version": 3, "sources": ["../tinymce/plugins/toc/plugin.js", "../tinymce/plugins/toc/index.js", "dep:tinymce_plugins_toc"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.I18n');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var getTocClass = function (editor) {\n      return editor.getParam('toc_class', 'mce-toc');\n    };\n    var getTocHeader = function (editor) {\n      var tagName = editor.getParam('toc_header', 'h2');\n      return /^h[1-6]$/.test(tagName) ? tagName : 'h2';\n    };\n    var getTocDepth = function (editor) {\n      var depth = parseInt(editor.getParam('toc_depth', '3'), 10);\n      return depth >= 1 && depth <= 9 ? depth : 3;\n    };\n\n    var create = function (prefix) {\n      var counter = 0;\n      return function () {\n        var guid = new Date().getTime().toString(32);\n        return prefix + guid + (counter++).toString(32);\n      };\n    };\n\n    var tocId = create('mcetoc_');\n    var generateSelector = function (depth) {\n      var i;\n      var selector = [];\n      for (i = 1; i <= depth; i++) {\n        selector.push('h' + i);\n      }\n      return selector.join(',');\n    };\n    var hasHeaders = function (editor) {\n      return readHeaders(editor).length > 0;\n    };\n    var readHeaders = function (editor) {\n      var tocClass = getTocClass(editor);\n      var headerTag = getTocHeader(editor);\n      var selector = generateSelector(getTocDepth(editor));\n      var headers = editor.$(selector);\n      if (headers.length && /^h[1-9]$/i.test(headerTag)) {\n        headers = headers.filter(function (i, el) {\n          return !editor.dom.hasClass(el.parentNode, tocClass);\n        });\n      }\n      return global.map(headers, function (h) {\n        var id = h.id;\n        return {\n          id: id ? id : tocId(),\n          level: parseInt(h.nodeName.replace(/^H/i, ''), 10),\n          title: editor.$.text(h),\n          element: h\n        };\n      });\n    };\n    var getMinLevel = function (headers) {\n      var minLevel = 9;\n      for (var i = 0; i < headers.length; i++) {\n        if (headers[i].level < minLevel) {\n          minLevel = headers[i].level;\n        }\n        if (minLevel === 1) {\n          return minLevel;\n        }\n      }\n      return minLevel;\n    };\n    var generateTitle = function (tag, title) {\n      var openTag = '<' + tag + ' contenteditable=\"true\">';\n      var closeTag = '</' + tag + '>';\n      return openTag + global$2.DOM.encode(title) + closeTag;\n    };\n    var generateTocHtml = function (editor) {\n      var html = generateTocContentHtml(editor);\n      return '<div class=\"' + editor.dom.encode(getTocClass(editor)) + '\" contenteditable=\"false\">' + html + '</div>';\n    };\n    var generateTocContentHtml = function (editor) {\n      var html = '';\n      var headers = readHeaders(editor);\n      var prevLevel = getMinLevel(headers) - 1;\n      if (!headers.length) {\n        return '';\n      }\n      html += generateTitle(getTocHeader(editor), global$1.translate('Table of Contents'));\n      for (var i = 0; i < headers.length; i++) {\n        var h = headers[i];\n        h.element.id = h.id;\n        var nextLevel = headers[i + 1] && headers[i + 1].level;\n        if (prevLevel === h.level) {\n          html += '<li>';\n        } else {\n          for (var ii = prevLevel; ii < h.level; ii++) {\n            html += '<ul><li>';\n          }\n        }\n        html += '<a href=\"#' + h.id + '\">' + h.title + '</a>';\n        if (nextLevel === h.level || !nextLevel) {\n          html += '</li>';\n          if (!nextLevel) {\n            html += '</ul>';\n          }\n        } else {\n          for (var ii = h.level; ii > nextLevel; ii--) {\n            if (ii === nextLevel + 1) {\n              html += '</li></ul><li>';\n            } else {\n              html += '</li></ul>';\n            }\n          }\n        }\n        prevLevel = h.level;\n      }\n      return html;\n    };\n    var isEmptyOrOffscreen = function (editor, nodes) {\n      return !nodes.length || editor.dom.getParents(nodes[0], '.mce-offscreen-selection').length > 0;\n    };\n    var insertToc = function (editor) {\n      var tocClass = getTocClass(editor);\n      var $tocElm = editor.$('.' + tocClass);\n      if (isEmptyOrOffscreen(editor, $tocElm)) {\n        editor.insertContent(generateTocHtml(editor));\n      } else {\n        updateToc(editor);\n      }\n    };\n    var updateToc = function (editor) {\n      var tocClass = getTocClass(editor);\n      var $tocElm = editor.$('.' + tocClass);\n      if ($tocElm.length) {\n        editor.undoManager.transact(function () {\n          $tocElm.html(generateTocContentHtml(editor));\n        });\n      }\n    };\n\n    var register$1 = function (editor) {\n      editor.addCommand('mceInsertToc', function () {\n        insertToc(editor);\n      });\n      editor.addCommand('mceUpdateToc', function () {\n        updateToc(editor);\n      });\n    };\n\n    var setup = function (editor) {\n      var $ = editor.$, tocClass = getTocClass(editor);\n      editor.on('PreProcess', function (e) {\n        var $tocElm = $('.' + tocClass, e.node);\n        if ($tocElm.length) {\n          $tocElm.removeAttr('contentEditable');\n          $tocElm.find('[contenteditable]').removeAttr('contentEditable');\n        }\n      });\n      editor.on('SetContent', function () {\n        var $tocElm = $('.' + tocClass);\n        if ($tocElm.length) {\n          $tocElm.attr('contentEditable', false);\n          $tocElm.children(':first-child').attr('contentEditable', true);\n        }\n      });\n    };\n\n    var toggleState = function (editor) {\n      return function (api) {\n        var toggleDisabledState = function () {\n          return api.setDisabled(editor.mode.isReadOnly() || !hasHeaders(editor));\n        };\n        toggleDisabledState();\n        editor.on('LoadContent SetContent change', toggleDisabledState);\n        return function () {\n          return editor.on('LoadContent SetContent change', toggleDisabledState);\n        };\n      };\n    };\n    var isToc = function (editor) {\n      return function (elm) {\n        return elm && editor.dom.is(elm, '.' + getTocClass(editor)) && editor.getBody().contains(elm);\n      };\n    };\n    var register = function (editor) {\n      var insertTocAction = function () {\n        return editor.execCommand('mceInsertToc');\n      };\n      editor.ui.registry.addButton('toc', {\n        icon: 'toc',\n        tooltip: 'Table of contents',\n        onAction: insertTocAction,\n        onSetup: toggleState(editor)\n      });\n      editor.ui.registry.addButton('tocupdate', {\n        icon: 'reload',\n        tooltip: 'Update',\n        onAction: function () {\n          return editor.execCommand('mceUpdateToc');\n        }\n      });\n      editor.ui.registry.addMenuItem('toc', {\n        icon: 'toc',\n        text: 'Table of contents',\n        onAction: insertTocAction,\n        onSetup: toggleState(editor)\n      });\n      editor.ui.registry.addContextToolbar('toc', {\n        items: 'tocupdate',\n        predicate: isToc(editor),\n        scope: 'node',\n        position: 'node'\n      });\n    };\n\n    function Plugin () {\n      global$3.add('toc', function (editor) {\n        register$1(editor);\n        register(editor);\n        setup(editor);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"toc\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/toc')\n//   ES2015:\n//     import 'tinymce/plugins/toc'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/toc/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,OAAO,SAAS,aAAa;AAAA;AAEtC,UAAI,eAAe,SAAU,QAAQ;AACnC,YAAI,UAAU,OAAO,SAAS,cAAc;AAC5C,eAAO,WAAW,KAAK,WAAW,UAAU;AAAA;AAE9C,UAAI,cAAc,SAAU,QAAQ;AAClC,YAAI,QAAQ,SAAS,OAAO,SAAS,aAAa,MAAM;AACxD,eAAO,SAAS,KAAK,SAAS,IAAI,QAAQ;AAAA;AAG5C,UAAI,SAAS,SAAU,QAAQ;AAC7B,YAAI,UAAU;AACd,eAAO,WAAY;AACjB,cAAI,OAAO,IAAI,OAAO,UAAU,SAAS;AACzC,iBAAO,SAAS,OAAQ,YAAW,SAAS;AAAA;AAAA;AAIhD,UAAI,QAAQ,OAAO;AACnB,UAAI,mBAAmB,SAAU,OAAO;AACtC,YAAI;AACJ,YAAI,WAAW;AACf,aAAK,IAAI,GAAG,KAAK,OAAO,KAAK;AAC3B,mBAAS,KAAK,MAAM;AAAA;AAEtB,eAAO,SAAS,KAAK;AAAA;AAEvB,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,YAAY,QAAQ,SAAS;AAAA;AAEtC,UAAI,cAAc,SAAU,QAAQ;AAClC,YAAI,WAAW,YAAY;AAC3B,YAAI,YAAY,aAAa;AAC7B,YAAI,WAAW,iBAAiB,YAAY;AAC5C,YAAI,UAAU,OAAO,EAAE;AACvB,YAAI,QAAQ,UAAU,YAAY,KAAK,YAAY;AACjD,oBAAU,QAAQ,OAAO,SAAU,GAAG,IAAI;AACxC,mBAAO,CAAC,OAAO,IAAI,SAAS,GAAG,YAAY;AAAA;AAAA;AAG/C,eAAO,OAAO,IAAI,SAAS,SAAU,GAAG;AACtC,cAAI,KAAK,EAAE;AACX,iBAAO;AAAA,YACL,IAAI,KAAK,KAAK;AAAA,YACd,OAAO,SAAS,EAAE,SAAS,QAAQ,OAAO,KAAK;AAAA,YAC/C,OAAO,OAAO,EAAE,KAAK;AAAA,YACrB,SAAS;AAAA;AAAA;AAAA;AAIf,UAAI,cAAc,SAAU,SAAS;AACnC,YAAI,WAAW;AACf,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,QAAQ,GAAG,QAAQ,UAAU;AAC/B,uBAAW,QAAQ,GAAG;AAAA;AAExB,cAAI,aAAa,GAAG;AAClB,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,gBAAgB,SAAU,KAAK,OAAO;AACxC,YAAI,UAAU,MAAM,MAAM;AAC1B,YAAI,WAAW,OAAO,MAAM;AAC5B,eAAO,UAAU,SAAS,IAAI,OAAO,SAAS;AAAA;AAEhD,UAAI,kBAAkB,SAAU,QAAQ;AACtC,YAAI,OAAO,uBAAuB;AAClC,eAAO,iBAAiB,OAAO,IAAI,OAAO,YAAY,WAAW,+BAA+B,OAAO;AAAA;AAEzG,UAAI,yBAAyB,SAAU,QAAQ;AAC7C,YAAI,OAAO;AACX,YAAI,UAAU,YAAY;AAC1B,YAAI,YAAY,YAAY,WAAW;AACvC,YAAI,CAAC,QAAQ,QAAQ;AACnB,iBAAO;AAAA;AAET,gBAAQ,cAAc,aAAa,SAAS,SAAS,UAAU;AAC/D,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,IAAI,QAAQ;AAChB,YAAE,QAAQ,KAAK,EAAE;AACjB,cAAI,YAAY,QAAQ,IAAI,MAAM,QAAQ,IAAI,GAAG;AACjD,cAAI,cAAc,EAAE,OAAO;AACzB,oBAAQ;AAAA,iBACH;AACL,qBAAS,KAAK,WAAW,KAAK,EAAE,OAAO,MAAM;AAC3C,sBAAQ;AAAA;AAAA;AAGZ,kBAAQ,eAAe,EAAE,KAAK,OAAO,EAAE,QAAQ;AAC/C,cAAI,cAAc,EAAE,SAAS,CAAC,WAAW;AACvC,oBAAQ;AACR,gBAAI,CAAC,WAAW;AACd,sBAAQ;AAAA;AAAA,iBAEL;AACL,qBAAS,KAAK,EAAE,OAAO,KAAK,WAAW,MAAM;AAC3C,kBAAI,OAAO,YAAY,GAAG;AACxB,wBAAQ;AAAA,qBACH;AACL,wBAAQ;AAAA;AAAA;AAAA;AAId,sBAAY,EAAE;AAAA;AAEhB,eAAO;AAAA;AAET,UAAI,qBAAqB,SAAU,QAAQ,OAAO;AAChD,eAAO,CAAC,MAAM,UAAU,OAAO,IAAI,WAAW,MAAM,IAAI,4BAA4B,SAAS;AAAA;AAE/F,UAAI,YAAY,SAAU,QAAQ;AAChC,YAAI,WAAW,YAAY;AAC3B,YAAI,UAAU,OAAO,EAAE,MAAM;AAC7B,YAAI,mBAAmB,QAAQ,UAAU;AACvC,iBAAO,cAAc,gBAAgB;AAAA,eAChC;AACL,oBAAU;AAAA;AAAA;AAGd,UAAI,YAAY,SAAU,QAAQ;AAChC,YAAI,WAAW,YAAY;AAC3B,YAAI,UAAU,OAAO,EAAE,MAAM;AAC7B,YAAI,QAAQ,QAAQ;AAClB,iBAAO,YAAY,SAAS,WAAY;AACtC,oBAAQ,KAAK,uBAAuB;AAAA;AAAA;AAAA;AAK1C,UAAI,aAAa,SAAU,QAAQ;AACjC,eAAO,WAAW,gBAAgB,WAAY;AAC5C,oBAAU;AAAA;AAEZ,eAAO,WAAW,gBAAgB,WAAY;AAC5C,oBAAU;AAAA;AAAA;AAId,UAAI,QAAQ,SAAU,QAAQ;AAC5B,YAAI,IAAI,OAAO,GAAG,WAAW,YAAY;AACzC,eAAO,GAAG,cAAc,SAAU,GAAG;AACnC,cAAI,UAAU,EAAE,MAAM,UAAU,EAAE;AAClC,cAAI,QAAQ,QAAQ;AAClB,oBAAQ,WAAW;AACnB,oBAAQ,KAAK,qBAAqB,WAAW;AAAA;AAAA;AAGjD,eAAO,GAAG,cAAc,WAAY;AAClC,cAAI,UAAU,EAAE,MAAM;AACtB,cAAI,QAAQ,QAAQ;AAClB,oBAAQ,KAAK,mBAAmB;AAChC,oBAAQ,SAAS,gBAAgB,KAAK,mBAAmB;AAAA;AAAA;AAAA;AAK/D,UAAI,cAAc,SAAU,QAAQ;AAClC,eAAO,SAAU,KAAK;AACpB,cAAI,sBAAsB,WAAY;AACpC,mBAAO,IAAI,YAAY,OAAO,KAAK,gBAAgB,CAAC,WAAW;AAAA;AAEjE;AACA,iBAAO,GAAG,iCAAiC;AAC3C,iBAAO,WAAY;AACjB,mBAAO,OAAO,GAAG,iCAAiC;AAAA;AAAA;AAAA;AAIxD,UAAI,QAAQ,SAAU,QAAQ;AAC5B,eAAO,SAAU,KAAK;AACpB,iBAAO,OAAO,OAAO,IAAI,GAAG,KAAK,MAAM,YAAY,YAAY,OAAO,UAAU,SAAS;AAAA;AAAA;AAG7F,UAAI,WAAW,SAAU,QAAQ;AAC/B,YAAI,kBAAkB,WAAY;AAChC,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,UAAU,OAAO;AAAA,UAClC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,SAAS,YAAY;AAAA;AAEvB,eAAO,GAAG,SAAS,UAAU,aAAa;AAAA,UACxC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,WAAY;AACpB,mBAAO,OAAO,YAAY;AAAA;AAAA;AAG9B,eAAO,GAAG,SAAS,YAAY,OAAO;AAAA,UACpC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,YAAY;AAAA;AAEvB,eAAO,GAAG,SAAS,kBAAkB,OAAO;AAAA,UAC1C,OAAO;AAAA,UACP,WAAW,MAAM;AAAA,UACjB,OAAO;AAAA,UACP,UAAU;AAAA;AAAA;AAId,wBAAmB;AACjB,iBAAS,IAAI,OAAO,SAAU,QAAQ;AACpC,qBAAW;AACX,mBAAS;AACT,gBAAM;AAAA;AAAA;AAIV;AAAA;AAAA;AAAA;;;AC3OJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,8BAAQ;", "names": []}