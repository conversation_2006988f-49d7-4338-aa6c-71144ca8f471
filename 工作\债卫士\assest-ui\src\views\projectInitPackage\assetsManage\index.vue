<template>
    <div class="app-container ">
        <el-row :getter="24" v-loading="loading">
            <el-col :span="4">
                <LeftSideTree :data="treeData" :nodeClick="handleTabClick" />
            </el-col>
            <el-col :span="20" class="pl20">
                <el-button type="primary" @click="toPage">组建资产包</el-button>
                <div class="card-list">
                    <div class="card-item" v-for="v in dataList" :key="v">
                        <div class="card-item-header df-jc-sb">
                            <span>{{ v.assetsName }}</span>
                            <el-button icon="download" type="text" @click="handleDownload()" />
                        </div>
                        <div class="card-item-main df-jc-sb">
                            <div class="card-item-main-left">
                                <div><span>资产包ID：</span>{{ v.assetsId }}</div>
                                <div><span>项目名称：</span>{{ v.projectName }}</div>
                                <div><span>创建时间：</span>{{ v.createTime }}</div>
                                <div><span>价值评估结果：</span>{{ v.result }}</div>
                                <div><span>备注信息：</span>{{ v.remarks }}</div>
                            </div>
                            <div class="card-item-main-right">
                                <div>资产状态：</div>
                                <el-button type="text">{{ v.assetsStatus }}</el-button>
                            </div>
                        </div>
                        <div class="card-item-footer">
                            <span v-if="v.assetsStatus == '未封包'" class="df-jc-sb">
                                <el-button type="primary" @click="toPage">继续编辑</el-button>
                                <el-button type="primary" @click="handlePacket">封包</el-button>
                            </span>
                            <span v-else class="df-jc-sb">
                                <el-button type="info" disabled>不可编辑</el-button>
                                <el-button type="primary" @click="handleCancelPacket">取消封包</el-button>
                            </span>
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import LeftSideTree from '@/components/leftSideTree/index';
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const { proxy } = getCurrentInstance()
const dataList = ref([
    { assetsName: '华北地区中小企业信贷不良资产一号包', assetsId: 'ZCB314124098721', projectName: '项目2-1', createTime: '2024-10-24 10:18:11', assetsStatus: '未封包', result: '4521万（历史回收率）', remarks: '中小企业信息' },
    { assetsName: '长三角个人消费贷次级资产组合转让二号包', assetsId: 'ZCB314124098321', projectName: '项目2-2', createTime: '2024-10-24 10:18:11', assetsStatus: '已封包', result: '1345万（现金流折现法）', remarks: '个人消费贷刺激资产' },
    { assetsName: '珠三角房地产抵押不良债权三号包', assetsId: 'ZCB314124098545', projectName: '项目2-3', createTime: '2024-10-24 10:18:11', assetsStatus: '未封包', result: '2712万（市场比较法）', remarks: '房地产抵押不良' },
    { assetsName: '中西部地区制造业企业不良债权四号包', assetsId: 'ZCB314124098322', projectName: '项目2-3', createTime: '2024-10-24 10:18:11', assetsStatus: '未封包', result: '1100万（资产价值基础法）', remarks: '经济区专用不良' },
    { assetsName: '京津冀地区商业地产抵债资产五号包', assetsId: 'ZCB314124098434', projectName: '项目2-4', createTime: '2024-10-24 10:18:11', assetsStatus: '已封包', result: '2819万（专家打分法）', remarks: '国有企业不良' },
    { assetsName: '京津冀地区商业地产抵债资产五号包', assetsId: 'ZCB314124098956', projectName: '项目2-5', createTime: '2024-10-24 10:18:11', assetsStatus: '未封包', result: '未评估', remarks: '大湾区跨境贸易' },
])
const treeData = ref([
    {
        id: 1,
        label: '消费贷',
        children: [
            { id: 11, label: '沃享极速分期', },
            { id: 12, label: '智鹿秒批贷', },
            { id: 13, label: '风云循环贷', },
            { id: 14, label: '信用付 PLUS', },
            { id: 15, label: '零零花青春版', },
            { id: 16, label: '教育智分期', },
            { id: 17, label: '家装无忧贷', },
            { id: 18, label: '旅行白条', },
            { id: 19, label: '医享付', },
            { id: 111, label: '绿享贷', },
            { id: 112, label: '乐享贷' },
        ]
    },
    {
        id: 2,
        label: '信用贷',
        children: [
            { id: 20, label: '白领优享贷' },
            { id: 21, label: '业主尊享贷' },
            { id: 22, label: '新市民安居贷' },
            { id: 23, label: '银发暖心贷' },
            { id: 24, label: '创客启航贷' },
            { id: 25, label: 'AI 智能卡' },
            { id: 26, label: '数据宝贷' },
            { id: 27, label: '仲思健康贷' },
            { id: 28, label: '跨境速汇贷' },
            { id: 29, label: '社区普惠贷' },
        ]
    },
]);

function toPage() {
    router.push({ path: '/transferManage/createAssets', query: { path: route.path } })
}

// 取消分包
function handleCancelPacket() {
    const title = '取消封包'
    const tipMsg = '确认取消封包吗，将会通知对应业务系统，并取消登记'
    proxy.$modal.confirm(tipMsg, title)
}

function handlePacket() {
    const title = '封包确认'
    const tipMsg = '确认封包吗，将不可再修改，同时通知业务系统停止处置，并向银登申请预登记'
    proxy.$modal.confirm(tipMsg, title)
}
function handleDownload() {
    const title = '下载基础资料'
    const tipMsg = '即将下载基础资料：资产清单、合规证明、初步估值材料'
    proxy.$modal.confirm(tipMsg, title).then(() => {
        const fileName = `${+new Date()}.xlsx`
        exportFile('https://assest.amcmj.com/resource/preview/2025/06/18/d07d05ec0b6f40b99cff7fb833c46a0c_错误的案件.xlsx', fileName)
    })
}

function handleTabClick() {
    loading.value = true
    setTimeout(() => {
        loading.value = false
    }, 1000)
}
function exportFile(data, fileName) {
    // 地址不存在时，禁止操作
    if (!data) return;
    proxy.$modal.notify('正在下载中...')
    // 下载文件并保存到本地
    const callback = (data) => {
        // 创建a标签，使用 html5 download 属性下载，
        const link = document.createElement('a');
        // 创建url对象
        const objectUrl = window.URL.createObjectURL(new Blob([data]));
        link.style.display = 'none';
        link.href = objectUrl;
        // 自定义文件名称， fileName
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        // 适当释放url
        window.URL.revokeObjectURL(objectUrl);
    };
    // 把接口返回的url地址转换为 blob
    const xhr = new XMLHttpRequest();
    xhr.open('get', data, true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
        // 返回文件流，进行下载处理
        callback(xhr.response);
        proxy.$modal.msgSuccess('操作成功！')
    };
    xhr.send(); // 不要忘记发送
};
</script>

<style lang="scss" scoped>
.card-list {
    display: grid;
    gap: 20px;
    margin-top: 20px;
    grid-template-columns: repeat(3, 1fr);
    font-size: 14px;

    &>div {
        border-radius: 5px;
        padding: 10px;
        box-shadow: 1px 1px 5px #ccc;
    }

    .card-item-main {
        border: 1px solid #cccccc4d;
        border-left: none;
        border-right: none;
        margin-bottom: 10px;
        padding: 20px 0 0;
    }

    .card-item-main-left {

        span {
            color: #999;
        }

        &>div {
            margin-bottom: 10px;
        }
    }

    .card-item-footer {
        padding: 0 20px;
    }
}
</style>