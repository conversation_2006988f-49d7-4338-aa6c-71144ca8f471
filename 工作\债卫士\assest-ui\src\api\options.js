

import request from '@/utils/request'
//查询任务名称
export function getTaskNameOption(query) {
    return request({
        url: '/caseManage/aiVoiceTask/getTaskNameOption',
        method: 'get',
        params: query
    })
}
//查询话术模板名称
export function getTemplateOption(query) {
    return request({
        url: '/caseManage/aiVoiceTask/getTemplateOption',
        method: 'get',
        params: query
    })
}
// 获取案件阶段
export const getStateTreeNode = () => {
    return request({
        url: '/sign/letter/item/getTreeNode',
        method: 'get',
        gateway: 'sign'
    })
}

//坐席模型名称下拉
export function getAgentOptionApi(data) {
    return request({
        url: '/quality/model/agentOption',
        method: 'post',
        data: data
    })
}
//客户模型名称下拉
export function getCustomerOptionApi(data) {
    return request({
        url: '/quality/model/customerOption',
        method: 'post',
        data: data
    })
}
//质检状态列表
export function getQualityStatusApi(query) {
    return request({
        url: '/caseManage/call/record/getQualityStatusList',
        method: 'get',
        params: query
    })
}
//录音导入批次列表
export function getInfoBatchBumOptionApi(query) {
    return request({
        url: '/caseManage/call/record/selectInfoByBatchBum',
        method: 'get',
        params: query
    })
}