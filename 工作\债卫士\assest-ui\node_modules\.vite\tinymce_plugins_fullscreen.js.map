{"version": 3, "sources": ["../tinymce/plugins/fullscreen/plugin.js", "../tinymce/plugins/fullscreen/index.js", "dep:tinymce_plugins_fullscreen"], "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n *\n * Version: 5.10.9 (2023-11-15)\n */\n(function () {\n    'use strict';\n\n    var Cell = function (initial) {\n      var value = initial;\n      var get = function () {\n        return value;\n      };\n      var set = function (v) {\n        value = v;\n      };\n      return {\n        get: get,\n        set: set\n      };\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var get$5 = function (fullscreenState) {\n      return {\n        isFullscreen: function () {\n          return fullscreenState.get() !== null;\n        }\n      };\n    };\n\n    var typeOf = function (x) {\n      var t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {\n        return 'array';\n      } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    var isType$1 = function (type) {\n      return function (value) {\n        return typeOf(value) === type;\n      };\n    };\n    var isSimpleType = function (type) {\n      return function (value) {\n        return typeof value === type;\n      };\n    };\n    var isString = isType$1('string');\n    var isArray = isType$1('array');\n    var isBoolean = isSimpleType('boolean');\n    var isNullable = function (a) {\n      return a === null || a === undefined;\n    };\n    var isNonNullable = function (a) {\n      return !isNullable(a);\n    };\n    var isFunction = isSimpleType('function');\n    var isNumber = isSimpleType('number');\n\n    var noop = function () {\n    };\n    var compose = function (fa, fb) {\n      return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return fa(fb.apply(null, args));\n      };\n    };\n    var compose1 = function (fbc, fab) {\n      return function (a) {\n        return fbc(fab(a));\n      };\n    };\n    var constant = function (value) {\n      return function () {\n        return value;\n      };\n    };\n    var identity = function (x) {\n      return x;\n    };\n    function curry(fn) {\n      var initialArgs = [];\n      for (var _i = 1; _i < arguments.length; _i++) {\n        initialArgs[_i - 1] = arguments[_i];\n      }\n      return function () {\n        var restArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          restArgs[_i] = arguments[_i];\n        }\n        var all = initialArgs.concat(restArgs);\n        return fn.apply(null, all);\n      };\n    }\n    var never = constant(false);\n    var always = constant(true);\n\n    var none = function () {\n      return NONE;\n    };\n    var NONE = function () {\n      var call = function (thunk) {\n        return thunk();\n      };\n      var id = identity;\n      var me = {\n        fold: function (n, _s) {\n          return n();\n        },\n        isSome: never,\n        isNone: always,\n        getOr: id,\n        getOrThunk: call,\n        getOrDie: function (msg) {\n          throw new Error(msg || 'error: getOrDie called on none.');\n        },\n        getOrNull: constant(null),\n        getOrUndefined: constant(undefined),\n        or: id,\n        orThunk: call,\n        map: none,\n        each: noop,\n        bind: none,\n        exists: never,\n        forall: always,\n        filter: function () {\n          return none();\n        },\n        toArray: function () {\n          return [];\n        },\n        toString: constant('none()')\n      };\n      return me;\n    }();\n    var some = function (a) {\n      var constant_a = constant(a);\n      var self = function () {\n        return me;\n      };\n      var bind = function (f) {\n        return f(a);\n      };\n      var me = {\n        fold: function (n, s) {\n          return s(a);\n        },\n        isSome: always,\n        isNone: never,\n        getOr: constant_a,\n        getOrThunk: constant_a,\n        getOrDie: constant_a,\n        getOrNull: constant_a,\n        getOrUndefined: constant_a,\n        or: self,\n        orThunk: self,\n        map: function (f) {\n          return some(f(a));\n        },\n        each: function (f) {\n          f(a);\n        },\n        bind: bind,\n        exists: bind,\n        forall: bind,\n        filter: function (f) {\n          return f(a) ? me : NONE;\n        },\n        toArray: function () {\n          return [a];\n        },\n        toString: function () {\n          return 'some(' + a + ')';\n        }\n      };\n      return me;\n    };\n    var from = function (value) {\n      return value === null || value === undefined ? NONE : some(value);\n    };\n    var Optional = {\n      some: some,\n      none: none,\n      from: from\n    };\n\n    var __assign = function () {\n      __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s)\n            if (Object.prototype.hasOwnProperty.call(s, p))\n              t[p] = s[p];\n        }\n        return t;\n      };\n      return __assign.apply(this, arguments);\n    };\n\n    var singleton = function (doRevoke) {\n      var subject = Cell(Optional.none());\n      var revoke = function () {\n        return subject.get().each(doRevoke);\n      };\n      var clear = function () {\n        revoke();\n        subject.set(Optional.none());\n      };\n      var isSet = function () {\n        return subject.get().isSome();\n      };\n      var get = function () {\n        return subject.get();\n      };\n      var set = function (s) {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear: clear,\n        isSet: isSet,\n        get: get,\n        set: set\n      };\n    };\n    var unbindable = function () {\n      return singleton(function (s) {\n        return s.unbind();\n      });\n    };\n    var value = function () {\n      var subject = singleton(noop);\n      var on = function (f) {\n        return subject.get().each(f);\n      };\n      return __assign(__assign({}, subject), { on: on });\n    };\n\n    var nativePush = Array.prototype.push;\n    var map = function (xs, f) {\n      var len = xs.length;\n      var r = new Array(len);\n      for (var i = 0; i < len; i++) {\n        var x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    var each$1 = function (xs, f) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        f(x, i);\n      }\n    };\n    var filter$1 = function (xs, pred) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    var findUntil = function (xs, pred, until) {\n      for (var i = 0, len = xs.length; i < len; i++) {\n        var x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    var find$1 = function (xs, pred) {\n      return findUntil(xs, pred, never);\n    };\n    var flatten = function (xs) {\n      var r = [];\n      for (var i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    var bind$3 = function (xs, f) {\n      return flatten(map(xs, f));\n    };\n    var get$4 = function (xs, i) {\n      return i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    };\n    var head = function (xs) {\n      return get$4(xs, 0);\n    };\n    var findMap = function (arr, f) {\n      for (var i = 0; i < arr.length; i++) {\n        var r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    var keys = Object.keys;\n    var each = function (obj, f) {\n      var props = keys(obj);\n      for (var k = 0, len = props.length; k < len; k++) {\n        var i = props[k];\n        var x = obj[i];\n        f(x, i);\n      }\n    };\n\n    var contains = function (str, substr) {\n      return str.indexOf(substr) !== -1;\n    };\n\n    var isSupported$1 = function (dom) {\n      return dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n    };\n\n    var fromHtml = function (html, scope) {\n      var doc = scope || document;\n      var div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        console.error('HTML does not have a single root node', html);\n        throw new Error('HTML must have a single root node');\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    var fromTag = function (tag, scope) {\n      var doc = scope || document;\n      var node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    var fromText = function (text, scope) {\n      var doc = scope || document;\n      var node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    var fromDom = function (node) {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    var fromPoint = function (docElm, x, y) {\n      return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    };\n    var SugarElement = {\n      fromHtml: fromHtml,\n      fromTag: fromTag,\n      fromText: fromText,\n      fromDom: fromDom,\n      fromPoint: fromPoint\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    var DOCUMENT = 9;\n    var DOCUMENT_FRAGMENT = 11;\n    var ELEMENT = 1;\n    var TEXT = 3;\n\n    var type = function (element) {\n      return element.dom.nodeType;\n    };\n    var isType = function (t) {\n      return function (element) {\n        return type(element) === t;\n      };\n    };\n    var isElement = isType(ELEMENT);\n    var isText = isType(TEXT);\n    var isDocument = isType(DOCUMENT);\n    var isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n\n    var cached = function (f) {\n      var called = false;\n      var r;\n      return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        if (!called) {\n          called = true;\n          r = f.apply(null, args);\n        }\n        return r;\n      };\n    };\n\n    var DeviceType = function (os, browser, userAgent, mediaMatch) {\n      var isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;\n      var isiPhone = os.isiOS() && !isiPad;\n      var isMobile = os.isiOS() || os.isAndroid();\n      var isTouch = isMobile || mediaMatch('(pointer:coarse)');\n      var isTablet = isiPad || !isiPhone && isMobile && mediaMatch('(min-device-width:768px)');\n      var isPhone = isiPhone || isMobile && !isTablet;\n      var iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;\n      var isDesktop = !isPhone && !isTablet && !iOSwebview;\n      return {\n        isiPad: constant(isiPad),\n        isiPhone: constant(isiPhone),\n        isTablet: constant(isTablet),\n        isPhone: constant(isPhone),\n        isTouch: constant(isTouch),\n        isAndroid: os.isAndroid,\n        isiOS: os.isiOS,\n        isWebView: constant(iOSwebview),\n        isDesktop: constant(isDesktop)\n      };\n    };\n\n    var firstMatch = function (regexes, s) {\n      for (var i = 0; i < regexes.length; i++) {\n        var x = regexes[i];\n        if (x.test(s)) {\n          return x;\n        }\n      }\n      return undefined;\n    };\n    var find = function (regexes, agent) {\n      var r = firstMatch(regexes, agent);\n      if (!r) {\n        return {\n          major: 0,\n          minor: 0\n        };\n      }\n      var group = function (i) {\n        return Number(agent.replace(r, '$' + i));\n      };\n      return nu$2(group(1), group(2));\n    };\n    var detect$3 = function (versionRegexes, agent) {\n      var cleanedAgent = String(agent).toLowerCase();\n      if (versionRegexes.length === 0) {\n        return unknown$2();\n      }\n      return find(versionRegexes, cleanedAgent);\n    };\n    var unknown$2 = function () {\n      return nu$2(0, 0);\n    };\n    var nu$2 = function (major, minor) {\n      return {\n        major: major,\n        minor: minor\n      };\n    };\n    var Version = {\n      nu: nu$2,\n      detect: detect$3,\n      unknown: unknown$2\n    };\n\n    var detectBrowser$1 = function (browsers, userAgentData) {\n      return findMap(userAgentData.brands, function (uaBrand) {\n        var lcBrand = uaBrand.brand.toLowerCase();\n        return find$1(browsers, function (browser) {\n          var _a;\n          return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase());\n        }).map(function (info) {\n          return {\n            current: info.name,\n            version: Version.nu(parseInt(uaBrand.version, 10), 0)\n          };\n        });\n      });\n    };\n\n    var detect$2 = function (candidates, userAgent) {\n      var agent = String(userAgent).toLowerCase();\n      return find$1(candidates, function (candidate) {\n        return candidate.search(agent);\n      });\n    };\n    var detectBrowser = function (browsers, userAgent) {\n      return detect$2(browsers, userAgent).map(function (browser) {\n        var version = Version.detect(browser.versionRegexes, userAgent);\n        return {\n          current: browser.name,\n          version: version\n        };\n      });\n    };\n    var detectOs = function (oses, userAgent) {\n      return detect$2(oses, userAgent).map(function (os) {\n        var version = Version.detect(os.versionRegexes, userAgent);\n        return {\n          current: os.name,\n          version: version\n        };\n      });\n    };\n\n    var normalVersionRegex = /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/;\n    var checkContains = function (target) {\n      return function (uastring) {\n        return contains(uastring, target);\n      };\n    };\n    var browsers = [\n      {\n        name: 'Edge',\n        versionRegexes: [/.*?edge\\/ ?([0-9]+)\\.([0-9]+)$/],\n        search: function (uastring) {\n          return contains(uastring, 'edge/') && contains(uastring, 'chrome') && contains(uastring, 'safari') && contains(uastring, 'applewebkit');\n        }\n      },\n      {\n        name: 'Chrome',\n        brand: 'Chromium',\n        versionRegexes: [\n          /.*?chrome\\/([0-9]+)\\.([0-9]+).*/,\n          normalVersionRegex\n        ],\n        search: function (uastring) {\n          return contains(uastring, 'chrome') && !contains(uastring, 'chromeframe');\n        }\n      },\n      {\n        name: 'IE',\n        versionRegexes: [\n          /.*?msie\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*?rv:([0-9]+)\\.([0-9]+).*/\n        ],\n        search: function (uastring) {\n          return contains(uastring, 'msie') || contains(uastring, 'trident');\n        }\n      },\n      {\n        name: 'Opera',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?opera\\/([0-9]+)\\.([0-9]+).*/\n        ],\n        search: checkContains('opera')\n      },\n      {\n        name: 'Firefox',\n        versionRegexes: [/.*?firefox\\/\\ ?([0-9]+)\\.([0-9]+).*/],\n        search: checkContains('firefox')\n      },\n      {\n        name: 'Safari',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?cpu os ([0-9]+)_([0-9]+).*/\n        ],\n        search: function (uastring) {\n          return (contains(uastring, 'safari') || contains(uastring, 'mobile/')) && contains(uastring, 'applewebkit');\n        }\n      }\n    ];\n    var oses = [\n      {\n        name: 'Windows',\n        search: checkContains('win'),\n        versionRegexes: [/.*?windows\\ nt\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'iOS',\n        search: function (uastring) {\n          return contains(uastring, 'iphone') || contains(uastring, 'ipad');\n        },\n        versionRegexes: [\n          /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*cpu os ([0-9]+)_([0-9]+).*/,\n          /.*cpu iphone os ([0-9]+)_([0-9]+).*/\n        ]\n      },\n      {\n        name: 'Android',\n        search: checkContains('android'),\n        versionRegexes: [/.*?android\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'OSX',\n        search: checkContains('mac os x'),\n        versionRegexes: [/.*?mac\\ os\\ x\\ ?([0-9]+)_([0-9]+).*/]\n      },\n      {\n        name: 'Linux',\n        search: checkContains('linux'),\n        versionRegexes: []\n      },\n      {\n        name: 'Solaris',\n        search: checkContains('sunos'),\n        versionRegexes: []\n      },\n      {\n        name: 'FreeBSD',\n        search: checkContains('freebsd'),\n        versionRegexes: []\n      },\n      {\n        name: 'ChromeOS',\n        search: checkContains('cros'),\n        versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/]\n      }\n    ];\n    var PlatformInfo = {\n      browsers: constant(browsers),\n      oses: constant(oses)\n    };\n\n    var edge = 'Edge';\n    var chrome = 'Chrome';\n    var ie = 'IE';\n    var opera = 'Opera';\n    var firefox = 'Firefox';\n    var safari = 'Safari';\n    var unknown$1 = function () {\n      return nu$1({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    var nu$1 = function (info) {\n      var current = info.current;\n      var version = info.version;\n      var isBrowser = function (name) {\n        return function () {\n          return current === name;\n        };\n      };\n      return {\n        current: current,\n        version: version,\n        isEdge: isBrowser(edge),\n        isChrome: isBrowser(chrome),\n        isIE: isBrowser(ie),\n        isOpera: isBrowser(opera),\n        isFirefox: isBrowser(firefox),\n        isSafari: isBrowser(safari)\n      };\n    };\n    var Browser = {\n      unknown: unknown$1,\n      nu: nu$1,\n      edge: constant(edge),\n      chrome: constant(chrome),\n      ie: constant(ie),\n      opera: constant(opera),\n      firefox: constant(firefox),\n      safari: constant(safari)\n    };\n\n    var windows = 'Windows';\n    var ios = 'iOS';\n    var android = 'Android';\n    var linux = 'Linux';\n    var osx = 'OSX';\n    var solaris = 'Solaris';\n    var freebsd = 'FreeBSD';\n    var chromeos = 'ChromeOS';\n    var unknown = function () {\n      return nu({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    var nu = function (info) {\n      var current = info.current;\n      var version = info.version;\n      var isOS = function (name) {\n        return function () {\n          return current === name;\n        };\n      };\n      return {\n        current: current,\n        version: version,\n        isWindows: isOS(windows),\n        isiOS: isOS(ios),\n        isAndroid: isOS(android),\n        isOSX: isOS(osx),\n        isLinux: isOS(linux),\n        isSolaris: isOS(solaris),\n        isFreeBSD: isOS(freebsd),\n        isChromeOS: isOS(chromeos)\n      };\n    };\n    var OperatingSystem = {\n      unknown: unknown,\n      nu: nu,\n      windows: constant(windows),\n      ios: constant(ios),\n      android: constant(android),\n      linux: constant(linux),\n      osx: constant(osx),\n      solaris: constant(solaris),\n      freebsd: constant(freebsd),\n      chromeos: constant(chromeos)\n    };\n\n    var detect$1 = function (userAgent, userAgentDataOpt, mediaMatch) {\n      var browsers = PlatformInfo.browsers();\n      var oses = PlatformInfo.oses();\n      var browser = userAgentDataOpt.bind(function (userAgentData) {\n        return detectBrowser$1(browsers, userAgentData);\n      }).orThunk(function () {\n        return detectBrowser(browsers, userAgent);\n      }).fold(Browser.unknown, Browser.nu);\n      var os = detectOs(oses, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);\n      var deviceType = DeviceType(os, browser, userAgent, mediaMatch);\n      return {\n        browser: browser,\n        os: os,\n        deviceType: deviceType\n      };\n    };\n    var PlatformDetection = { detect: detect$1 };\n\n    var mediaMatch = function (query) {\n      return window.matchMedia(query).matches;\n    };\n    var platform = cached(function () {\n      return PlatformDetection.detect(navigator.userAgent, Optional.from(navigator.userAgentData), mediaMatch);\n    });\n    var detect = function () {\n      return platform();\n    };\n\n    var is = function (element, selector) {\n      var dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        var elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n    var bypassSelector = function (dom) {\n      return dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    };\n    var all$1 = function (selector, scope) {\n      var base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n\n    var eq = function (e1, e2) {\n      return e1.dom === e2.dom;\n    };\n\n    var owner = function (element) {\n      return SugarElement.fromDom(element.dom.ownerDocument);\n    };\n    var documentOrOwner = function (dos) {\n      return isDocument(dos) ? dos : owner(dos);\n    };\n    var parent = function (element) {\n      return Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    };\n    var parents = function (element, isRoot) {\n      var stop = isFunction(isRoot) ? isRoot : never;\n      var dom = element.dom;\n      var ret = [];\n      while (dom.parentNode !== null && dom.parentNode !== undefined) {\n        var rawParent = dom.parentNode;\n        var p = SugarElement.fromDom(rawParent);\n        ret.push(p);\n        if (stop(p) === true) {\n          break;\n        } else {\n          dom = rawParent;\n        }\n      }\n      return ret;\n    };\n    var siblings$2 = function (element) {\n      var filterSelf = function (elements) {\n        return filter$1(elements, function (x) {\n          return !eq(element, x);\n        });\n      };\n      return parent(element).map(children).map(filterSelf).getOr([]);\n    };\n    var children = function (element) {\n      return map(element.dom.childNodes, SugarElement.fromDom);\n    };\n\n    var isShadowRoot = function (dos) {\n      return isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    };\n    var supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    var isSupported = constant(supported);\n    var getRootNode = supported ? function (e) {\n      return SugarElement.fromDom(e.dom.getRootNode());\n    } : documentOrOwner;\n    var getShadowRoot = function (e) {\n      var r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    var getShadowHost = function (e) {\n      return SugarElement.fromDom(e.dom.host);\n    };\n    var getOriginalEventTarget = function (event) {\n      if (isSupported() && isNonNullable(event.target)) {\n        var el = SugarElement.fromDom(event.target);\n        if (isElement(el) && isOpenShadowHost(el)) {\n          if (event.composed && event.composedPath) {\n            var composedPath = event.composedPath();\n            if (composedPath) {\n              return head(composedPath);\n            }\n          }\n        }\n      }\n      return Optional.from(event.target);\n    };\n    var isOpenShadowHost = function (element) {\n      return isNonNullable(element.dom.shadowRoot);\n    };\n\n    var inBody = function (element) {\n      var dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      var doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(function () {\n        return doc.body.contains(dom);\n      }, compose1(inBody, getShadowHost));\n    };\n    var getBody = function (doc) {\n      var b = doc.dom.body;\n      if (b === null || b === undefined) {\n        throw new Error('Body is not available yet');\n      }\n      return SugarElement.fromDom(b);\n    };\n\n    var rawSet = function (dom, key, value) {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    var set = function (element, key, value) {\n      rawSet(element.dom, key, value);\n    };\n    var get$3 = function (element, key) {\n      var v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    var remove = function (element, key) {\n      element.dom.removeAttribute(key);\n    };\n\n    var internalSet = function (dom, property, value) {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported$1(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    var setAll = function (element, css) {\n      var dom = element.dom;\n      each(css, function (v, k) {\n        internalSet(dom, k, v);\n      });\n    };\n    var get$2 = function (element, property) {\n      var dom = element.dom;\n      var styles = window.getComputedStyle(dom);\n      var r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    var getUnsafeProperty = function (dom, property) {\n      return isSupported$1(dom) ? dom.style.getPropertyValue(property) : '';\n    };\n\n    var mkEvent = function (target, x, y, stop, prevent, kill, raw) {\n      return {\n        target: target,\n        x: x,\n        y: y,\n        stop: stop,\n        prevent: prevent,\n        kill: kill,\n        raw: raw\n      };\n    };\n    var fromRawEvent = function (rawEvent) {\n      var target = SugarElement.fromDom(getOriginalEventTarget(rawEvent).getOr(rawEvent.target));\n      var stop = function () {\n        return rawEvent.stopPropagation();\n      };\n      var prevent = function () {\n        return rawEvent.preventDefault();\n      };\n      var kill = compose(prevent, stop);\n      return mkEvent(target, rawEvent.clientX, rawEvent.clientY, stop, prevent, kill, rawEvent);\n    };\n    var handle = function (filter, handler) {\n      return function (rawEvent) {\n        if (filter(rawEvent)) {\n          handler(fromRawEvent(rawEvent));\n        }\n      };\n    };\n    var binder = function (element, event, filter, handler, useCapture) {\n      var wrapped = handle(filter, handler);\n      element.dom.addEventListener(event, wrapped, useCapture);\n      return { unbind: curry(unbind, element, event, wrapped, useCapture) };\n    };\n    var bind$2 = function (element, event, filter, handler) {\n      return binder(element, event, filter, handler, false);\n    };\n    var unbind = function (element, event, handler, useCapture) {\n      element.dom.removeEventListener(event, handler, useCapture);\n    };\n\n    var filter = always;\n    var bind$1 = function (element, event, handler) {\n      return bind$2(element, event, filter, handler);\n    };\n\n    var r = function (left, top) {\n      var translate = function (x, y) {\n        return r(left + x, top + y);\n      };\n      return {\n        left: left,\n        top: top,\n        translate: translate\n      };\n    };\n    var SugarPosition = r;\n\n    var get$1 = function (_DOC) {\n      var doc = _DOC !== undefined ? _DOC.dom : document;\n      var x = doc.body.scrollLeft || doc.documentElement.scrollLeft;\n      var y = doc.body.scrollTop || doc.documentElement.scrollTop;\n      return SugarPosition(x, y);\n    };\n\n    var get = function (_win) {\n      var win = _win === undefined ? window : _win;\n      if (detect().browser.isFirefox()) {\n        return Optional.none();\n      } else {\n        return Optional.from(win['visualViewport']);\n      }\n    };\n    var bounds = function (x, y, width, height) {\n      return {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        right: x + width,\n        bottom: y + height\n      };\n    };\n    var getBounds = function (_win) {\n      var win = _win === undefined ? window : _win;\n      var doc = win.document;\n      var scroll = get$1(SugarElement.fromDom(doc));\n      return get(win).fold(function () {\n        var html = win.document.documentElement;\n        var width = html.clientWidth;\n        var height = html.clientHeight;\n        return bounds(scroll.left, scroll.top, width, height);\n      }, function (visualViewport) {\n        return bounds(Math.max(visualViewport.pageLeft, scroll.left), Math.max(visualViewport.pageTop, scroll.top), visualViewport.width, visualViewport.height);\n      });\n    };\n    var bind = function (name, callback, _win) {\n      return get(_win).map(function (visualViewport) {\n        var handler = function (e) {\n          return callback(fromRawEvent(e));\n        };\n        visualViewport.addEventListener(name, handler);\n        return {\n          unbind: function () {\n            return visualViewport.removeEventListener(name, handler);\n          }\n        };\n      }).getOrThunk(function () {\n        return { unbind: noop };\n      });\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    var fireFullscreenStateChanged = function (editor, state) {\n      editor.fire('FullscreenStateChanged', { state: state });\n      editor.fire('ResizeEditor');\n    };\n\n    var getFullscreenNative = function (editor) {\n      return editor.getParam('fullscreen_native', false, 'boolean');\n    };\n\n    var getFullscreenRoot = function (editor) {\n      var elem = SugarElement.fromDom(editor.getElement());\n      return getShadowRoot(elem).map(getShadowHost).getOrThunk(function () {\n        return getBody(owner(elem));\n      });\n    };\n    var getFullscreenElement = function (root) {\n      if (root.fullscreenElement !== undefined) {\n        return root.fullscreenElement;\n      } else if (root.msFullscreenElement !== undefined) {\n        return root.msFullscreenElement;\n      } else if (root.webkitFullscreenElement !== undefined) {\n        return root.webkitFullscreenElement;\n      } else {\n        return null;\n      }\n    };\n    var getFullscreenchangeEventName = function () {\n      if (document.fullscreenElement !== undefined) {\n        return 'fullscreenchange';\n      } else if (document.msFullscreenElement !== undefined) {\n        return 'MSFullscreenChange';\n      } else if (document.webkitFullscreenElement !== undefined) {\n        return 'webkitfullscreenchange';\n      } else {\n        return 'fullscreenchange';\n      }\n    };\n    var requestFullscreen = function (sugarElem) {\n      var elem = sugarElem.dom;\n      if (elem.requestFullscreen) {\n        elem.requestFullscreen();\n      } else if (elem.msRequestFullscreen) {\n        elem.msRequestFullscreen();\n      } else if (elem.webkitRequestFullScreen) {\n        elem.webkitRequestFullScreen();\n      }\n    };\n    var exitFullscreen = function (sugarDoc) {\n      var doc = sugarDoc.dom;\n      if (doc.exitFullscreen) {\n        doc.exitFullscreen();\n      } else if (doc.msExitFullscreen) {\n        doc.msExitFullscreen();\n      } else if (doc.webkitCancelFullScreen) {\n        doc.webkitCancelFullScreen();\n      }\n    };\n    var isFullscreenElement = function (elem) {\n      return elem.dom === getFullscreenElement(owner(elem).dom);\n    };\n\n    var ancestors$1 = function (scope, predicate, isRoot) {\n      return filter$1(parents(scope, isRoot), predicate);\n    };\n    var siblings$1 = function (scope, predicate) {\n      return filter$1(siblings$2(scope), predicate);\n    };\n\n    var all = function (selector) {\n      return all$1(selector);\n    };\n    var ancestors = function (scope, selector, isRoot) {\n      return ancestors$1(scope, function (e) {\n        return is(e, selector);\n      }, isRoot);\n    };\n    var siblings = function (scope, selector) {\n      return siblings$1(scope, function (e) {\n        return is(e, selector);\n      });\n    };\n\n    var attr = 'data-ephox-mobile-fullscreen-style';\n    var siblingStyles = 'display:none!important;';\n    var ancestorPosition = 'position:absolute!important;';\n    var ancestorStyles = 'top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;';\n    var bgFallback = 'background-color:rgb(255,255,255)!important;';\n    var isAndroid = global$1.os.isAndroid();\n    var matchColor = function (editorBody) {\n      var color = get$2(editorBody, 'background-color');\n      return color !== undefined && color !== '' ? 'background-color:' + color + '!important' : bgFallback;\n    };\n    var clobberStyles = function (dom, container, editorBody) {\n      var gatherSiblings = function (element) {\n        return siblings(element, '*:not(.tox-silver-sink)');\n      };\n      var clobber = function (clobberStyle) {\n        return function (element) {\n          var styles = get$3(element, 'style');\n          var backup = styles === undefined ? 'no-styles' : styles.trim();\n          if (backup === clobberStyle) {\n            return;\n          } else {\n            set(element, attr, backup);\n            setAll(element, dom.parseStyle(clobberStyle));\n          }\n        };\n      };\n      var ancestors$1 = ancestors(container, '*');\n      var siblings$1 = bind$3(ancestors$1, gatherSiblings);\n      var bgColor = matchColor(editorBody);\n      each$1(siblings$1, clobber(siblingStyles));\n      each$1(ancestors$1, clobber(ancestorPosition + ancestorStyles + bgColor));\n      var containerStyles = isAndroid === true ? '' : ancestorPosition;\n      clobber(containerStyles + ancestorStyles + bgColor)(container);\n    };\n    var restoreStyles = function (dom) {\n      var clobberedEls = all('[' + attr + ']');\n      each$1(clobberedEls, function (element) {\n        var restore = get$3(element, attr);\n        if (restore !== 'no-styles') {\n          setAll(element, dom.parseStyle(restore));\n        } else {\n          remove(element, 'style');\n        }\n        remove(element, attr);\n      });\n    };\n\n    var DOM = global$2.DOM;\n    var getScrollPos = function () {\n      return getBounds(window);\n    };\n    var setScrollPos = function (pos) {\n      return window.scrollTo(pos.x, pos.y);\n    };\n    var viewportUpdate = get().fold(function () {\n      return {\n        bind: noop,\n        unbind: noop\n      };\n    }, function (visualViewport) {\n      var editorContainer = value();\n      var resizeBinder = unbindable();\n      var scrollBinder = unbindable();\n      var refreshScroll = function () {\n        document.body.scrollTop = 0;\n        document.documentElement.scrollTop = 0;\n      };\n      var refreshVisualViewport = function () {\n        window.requestAnimationFrame(function () {\n          editorContainer.on(function (container) {\n            return setAll(container, {\n              top: visualViewport.offsetTop + 'px',\n              left: visualViewport.offsetLeft + 'px',\n              height: visualViewport.height + 'px',\n              width: visualViewport.width + 'px'\n            });\n          });\n        });\n      };\n      var update = global.throttle(function () {\n        refreshScroll();\n        refreshVisualViewport();\n      }, 50);\n      var bind$1 = function (element) {\n        editorContainer.set(element);\n        update();\n        resizeBinder.set(bind('resize', update));\n        scrollBinder.set(bind('scroll', update));\n      };\n      var unbind = function () {\n        editorContainer.on(function () {\n          resizeBinder.clear();\n          scrollBinder.clear();\n        });\n        editorContainer.clear();\n      };\n      return {\n        bind: bind$1,\n        unbind: unbind\n      };\n    });\n    var toggleFullscreen = function (editor, fullscreenState) {\n      var body = document.body;\n      var documentElement = document.documentElement;\n      var editorContainer = editor.getContainer();\n      var editorContainerS = SugarElement.fromDom(editorContainer);\n      var fullscreenRoot = getFullscreenRoot(editor);\n      var fullscreenInfo = fullscreenState.get();\n      var editorBody = SugarElement.fromDom(editor.getBody());\n      var isTouch = global$1.deviceType.isTouch();\n      var editorContainerStyle = editorContainer.style;\n      var iframe = editor.iframeElement;\n      var iframeStyle = iframe.style;\n      var handleClasses = function (handler) {\n        handler(body, 'tox-fullscreen');\n        handler(documentElement, 'tox-fullscreen');\n        handler(editorContainer, 'tox-fullscreen');\n        getShadowRoot(editorContainerS).map(function (root) {\n          return getShadowHost(root).dom;\n        }).each(function (host) {\n          handler(host, 'tox-fullscreen');\n          handler(host, 'tox-shadowhost');\n        });\n      };\n      var cleanup = function () {\n        if (isTouch) {\n          restoreStyles(editor.dom);\n        }\n        handleClasses(DOM.removeClass);\n        viewportUpdate.unbind();\n        Optional.from(fullscreenState.get()).each(function (info) {\n          return info.fullscreenChangeHandler.unbind();\n        });\n      };\n      if (!fullscreenInfo) {\n        var fullscreenChangeHandler = bind$1(owner(fullscreenRoot), getFullscreenchangeEventName(), function (_evt) {\n          if (getFullscreenNative(editor)) {\n            if (!isFullscreenElement(fullscreenRoot) && fullscreenState.get() !== null) {\n              toggleFullscreen(editor, fullscreenState);\n            }\n          }\n        });\n        var newFullScreenInfo = {\n          scrollPos: getScrollPos(),\n          containerWidth: editorContainerStyle.width,\n          containerHeight: editorContainerStyle.height,\n          containerTop: editorContainerStyle.top,\n          containerLeft: editorContainerStyle.left,\n          iframeWidth: iframeStyle.width,\n          iframeHeight: iframeStyle.height,\n          fullscreenChangeHandler: fullscreenChangeHandler\n        };\n        if (isTouch) {\n          clobberStyles(editor.dom, editorContainerS, editorBody);\n        }\n        iframeStyle.width = iframeStyle.height = '100%';\n        editorContainerStyle.width = editorContainerStyle.height = '';\n        handleClasses(DOM.addClass);\n        viewportUpdate.bind(editorContainerS);\n        editor.on('remove', cleanup);\n        fullscreenState.set(newFullScreenInfo);\n        if (getFullscreenNative(editor)) {\n          requestFullscreen(fullscreenRoot);\n        }\n        fireFullscreenStateChanged(editor, true);\n      } else {\n        fullscreenInfo.fullscreenChangeHandler.unbind();\n        if (getFullscreenNative(editor) && isFullscreenElement(fullscreenRoot)) {\n          exitFullscreen(owner(fullscreenRoot));\n        }\n        iframeStyle.width = fullscreenInfo.iframeWidth;\n        iframeStyle.height = fullscreenInfo.iframeHeight;\n        editorContainerStyle.width = fullscreenInfo.containerWidth;\n        editorContainerStyle.height = fullscreenInfo.containerHeight;\n        editorContainerStyle.top = fullscreenInfo.containerTop;\n        editorContainerStyle.left = fullscreenInfo.containerLeft;\n        cleanup();\n        setScrollPos(fullscreenInfo.scrollPos);\n        fullscreenState.set(null);\n        fireFullscreenStateChanged(editor, false);\n        editor.off('remove', cleanup);\n      }\n    };\n\n    var register$1 = function (editor, fullscreenState) {\n      editor.addCommand('mceFullScreen', function () {\n        toggleFullscreen(editor, fullscreenState);\n      });\n    };\n\n    var makeSetupHandler = function (editor, fullscreenState) {\n      return function (api) {\n        api.setActive(fullscreenState.get() !== null);\n        var editorEventCallback = function (e) {\n          return api.setActive(e.state);\n        };\n        editor.on('FullscreenStateChanged', editorEventCallback);\n        return function () {\n          return editor.off('FullscreenStateChanged', editorEventCallback);\n        };\n      };\n    };\n    var register = function (editor, fullscreenState) {\n      var onAction = function () {\n        return editor.execCommand('mceFullScreen');\n      };\n      editor.ui.registry.addToggleMenuItem('fullscreen', {\n        text: 'Fullscreen',\n        icon: 'fullscreen',\n        shortcut: 'Meta+Shift+F',\n        onAction: onAction,\n        onSetup: makeSetupHandler(editor, fullscreenState)\n      });\n      editor.ui.registry.addToggleButton('fullscreen', {\n        tooltip: 'Fullscreen',\n        icon: 'fullscreen',\n        onAction: onAction,\n        onSetup: makeSetupHandler(editor, fullscreenState)\n      });\n    };\n\n    function Plugin () {\n      global$3.add('fullscreen', function (editor) {\n        var fullscreenState = Cell(null);\n        if (editor.inline) {\n          return get$5(fullscreenState);\n        }\n        register$1(editor, fullscreenState);\n        register(editor, fullscreenState);\n        editor.addShortcut('Meta+Shift+F', '', 'mceFullScreen');\n        return get$5(fullscreenState);\n      });\n    }\n\n    Plugin();\n\n}());\n", "// Exports the \"fullscreen\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/fullscreen')\n//   ES2015:\n//     import 'tinymce/plugins/fullscreen'\nrequire('./plugin.js');", "export default require(\"./node_modules/tinymce/plugins/fullscreen/index.js\");"], "mappings": ";;;;;AAAA;AAAA;AAQA,IAAC,YAAY;AACT;AAEA,UAAI,OAAO,SAAU,SAAS;AAC5B,YAAI,SAAQ;AACZ,YAAI,OAAM,WAAY;AACpB,iBAAO;AAAA;AAET,YAAI,OAAM,SAAU,GAAG;AACrB,mBAAQ;AAAA;AAEV,eAAO;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA;AAAA;AAIT,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,QAAQ,SAAU,iBAAiB;AACrC,eAAO;AAAA,UACL,cAAc,WAAY;AACxB,mBAAO,gBAAgB,UAAU;AAAA;AAAA;AAAA;AAKvC,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,mBACE,MAAM,YAAa,OAAM,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,UAAU;AAClH,iBAAO;AAAA,mBACE,MAAM,YAAa,QAAO,UAAU,cAAc,MAAM,EAAE,eAAe,EAAE,YAAY,SAAS,WAAW;AACpH,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,OAAM;AAC7B,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,YAAW;AAAA;AAAA;AAG7B,UAAI,eAAe,SAAU,OAAM;AACjC,eAAO,SAAU,QAAO;AACtB,iBAAO,OAAO,WAAU;AAAA;AAAA;AAG5B,UAAI,WAAW,SAAS;AACxB,UAAI,UAAU,SAAS;AACvB,UAAI,YAAY,aAAa;AAC7B,UAAI,aAAa,SAAU,GAAG;AAC5B,eAAO,MAAM,QAAQ,MAAM;AAAA;AAE7B,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,CAAC,WAAW;AAAA;AAErB,UAAI,aAAa,aAAa;AAC9B,UAAI,WAAW,aAAa;AAE5B,UAAI,OAAO,WAAY;AAAA;AAEvB,UAAI,UAAU,SAAU,IAAI,IAAI;AAC9B,eAAO,WAAY;AACjB,cAAI,OAAO;AACX,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,iBAAK,MAAM,UAAU;AAAA;AAEvB,iBAAO,GAAG,GAAG,MAAM,MAAM;AAAA;AAAA;AAG7B,UAAI,WAAW,SAAU,KAAK,KAAK;AACjC,eAAO,SAAU,GAAG;AAClB,iBAAO,IAAI,IAAI;AAAA;AAAA;AAGnB,UAAI,WAAW,SAAU,QAAO;AAC9B,eAAO,WAAY;AACjB,iBAAO;AAAA;AAAA;AAGX,UAAI,WAAW,SAAU,GAAG;AAC1B,eAAO;AAAA;AAET,qBAAe,IAAI;AACjB,YAAI,cAAc;AAClB,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,sBAAY,KAAK,KAAK,UAAU;AAAA;AAElC,eAAO,WAAY;AACjB,cAAI,WAAW;AACf,mBAAS,MAAK,GAAG,MAAK,UAAU,QAAQ,OAAM;AAC5C,qBAAS,OAAM,UAAU;AAAA;AAE3B,cAAI,OAAM,YAAY,OAAO;AAC7B,iBAAO,GAAG,MAAM,MAAM;AAAA;AAAA;AAG1B,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,SAAS;AAEtB,UAAI,OAAO,WAAY;AACrB,eAAO;AAAA;AAET,UAAI,OAAO,WAAY;AACrB,YAAI,OAAO,SAAU,OAAO;AAC1B,iBAAO;AAAA;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,IAAI;AACrB,mBAAO;AAAA;AAAA,UAET,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU,SAAU,KAAK;AACvB,kBAAM,IAAI,MAAM,OAAO;AAAA;AAAA,UAEzB,WAAW,SAAS;AAAA,UACpB,gBAAgB,SAAS;AAAA,UACzB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,WAAY;AAClB,mBAAO;AAAA;AAAA,UAET,SAAS,WAAY;AACnB,mBAAO;AAAA;AAAA,UAET,UAAU,SAAS;AAAA;AAErB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,GAAG;AACtB,YAAI,aAAa,SAAS;AAC1B,YAAI,OAAO,WAAY;AACrB,iBAAO;AAAA;AAET,YAAI,QAAO,SAAU,GAAG;AACtB,iBAAO,EAAE;AAAA;AAEX,YAAI,KAAK;AAAA,UACP,MAAM,SAAU,GAAG,GAAG;AACpB,mBAAO,EAAE;AAAA;AAAA,UAEX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK,SAAU,GAAG;AAChB,mBAAO,KAAK,EAAE;AAAA;AAAA,UAEhB,MAAM,SAAU,GAAG;AACjB,cAAE;AAAA;AAAA,UAEJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAU,GAAG;AACnB,mBAAO,EAAE,KAAK,KAAK;AAAA;AAAA,UAErB,SAAS,WAAY;AACnB,mBAAO,CAAC;AAAA;AAAA,UAEV,UAAU,WAAY;AACpB,mBAAO,UAAU,IAAI;AAAA;AAAA;AAGzB,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,QAAO;AAC1B,eAAO,WAAU,QAAQ,WAAU,SAAY,OAAO,KAAK;AAAA;AAE7D,UAAI,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAGF,UAAI,WAAW,WAAY;AACzB,mBAAW,OAAO,UAAU,mBAAkB,GAAG;AAC/C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAI,UAAU;AACd,qBAAS,KAAK;AACZ,kBAAI,OAAO,UAAU,eAAe,KAAK,GAAG;AAC1C,kBAAE,KAAK,EAAE;AAAA;AAEf,iBAAO;AAAA;AAET,eAAO,SAAS,MAAM,MAAM;AAAA;AAG9B,UAAI,YAAY,SAAU,UAAU;AAClC,YAAI,UAAU,KAAK,SAAS;AAC5B,YAAI,SAAS,WAAY;AACvB,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,YAAI,QAAQ,WAAY;AACtB;AACA,kBAAQ,IAAI,SAAS;AAAA;AAEvB,YAAI,QAAQ,WAAY;AACtB,iBAAO,QAAQ,MAAM;AAAA;AAEvB,YAAI,OAAM,WAAY;AACpB,iBAAO,QAAQ;AAAA;AAEjB,YAAI,OAAM,SAAU,GAAG;AACrB;AACA,kBAAQ,IAAI,SAAS,KAAK;AAAA;AAE5B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA;AAAA;AAGT,UAAI,aAAa,WAAY;AAC3B,eAAO,UAAU,SAAU,GAAG;AAC5B,iBAAO,EAAE;AAAA;AAAA;AAGb,UAAI,QAAQ,WAAY;AACtB,YAAI,UAAU,UAAU;AACxB,YAAI,KAAK,SAAU,GAAG;AACpB,iBAAO,QAAQ,MAAM,KAAK;AAAA;AAE5B,eAAO,SAAS,SAAS,IAAI,UAAU,EAAE;AAAA;AAG3C,UAAI,aAAa,MAAM,UAAU;AACjC,UAAI,MAAM,SAAU,IAAI,GAAG;AACzB,YAAI,MAAM,GAAG;AACb,YAAI,KAAI,IAAI,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,IAAI,GAAG;AACX,aAAE,KAAK,EAAE,GAAG;AAAA;AAEd,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,IAAI,GAAG;AAC5B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,YAAE,GAAG;AAAA;AAAA;AAGT,UAAI,WAAW,SAAU,IAAI,MAAM;AACjC,YAAI,KAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,eAAE,KAAK;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,YAAY,SAAU,IAAI,MAAM,OAAO;AACzC,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAI,IAAI,GAAG;AACX,cAAI,KAAK,GAAG,IAAI;AACd,mBAAO,SAAS,KAAK;AAAA,qBACZ,MAAM,GAAG,IAAI;AACtB;AAAA;AAAA;AAGJ,eAAO,SAAS;AAAA;AAElB,UAAI,SAAS,SAAU,IAAI,MAAM;AAC/B,eAAO,UAAU,IAAI,MAAM;AAAA;AAE7B,UAAI,UAAU,SAAU,IAAI;AAC1B,YAAI,KAAI;AACR,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,KAAK;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B;AAAA;AAE3E,qBAAW,MAAM,IAAG,GAAG;AAAA;AAEzB,eAAO;AAAA;AAET,UAAI,SAAS,SAAU,IAAI,GAAG;AAC5B,eAAO,QAAQ,IAAI,IAAI;AAAA;AAEzB,UAAI,QAAQ,SAAU,IAAI,GAAG;AAC3B,eAAO,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,MAAM,SAAS;AAAA;AAEnE,UAAI,OAAO,SAAU,IAAI;AACvB,eAAO,MAAM,IAAI;AAAA;AAEnB,UAAI,UAAU,SAAU,KAAK,GAAG;AAC9B,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,KAAI,EAAE,IAAI,IAAI;AAClB,cAAI,GAAE,UAAU;AACd,mBAAO;AAAA;AAAA;AAGX,eAAO,SAAS;AAAA;AAGlB,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO,SAAU,KAAK,GAAG;AAC3B,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,IAAI;AACZ,YAAE,GAAG;AAAA;AAAA;AAIT,UAAI,WAAW,SAAU,KAAK,QAAQ;AACpC,eAAO,IAAI,QAAQ,YAAY;AAAA;AAGjC,UAAI,gBAAgB,SAAU,KAAK;AACjC,eAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM;AAAA;AAGzD,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,IAAI,cAAc;AAC5B,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,mBAAmB,IAAI,WAAW,SAAS,GAAG;AACrD,kBAAQ,MAAM,yCAAyC;AACvD,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,QAAQ,IAAI,WAAW;AAAA;AAEhC,UAAI,UAAU,SAAU,KAAK,OAAO;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,cAAc;AAC7B,eAAO,QAAQ;AAAA;AAEjB,UAAI,WAAW,SAAU,MAAM,OAAO;AACpC,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,IAAI,eAAe;AAC9B,eAAO,QAAQ;AAAA;AAEjB,UAAI,UAAU,SAAU,MAAM;AAC5B,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,EAAE,KAAK;AAAA;AAEhB,UAAI,YAAY,SAAU,QAAQ,GAAG,GAAG;AACtC,eAAO,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,IAAI,IAAI;AAAA;AAE9D,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAGF,aAAO,WAAW,cAAc,SAAS,SAAS;AAElD,UAAI,WAAW;AACf,UAAI,oBAAoB;AACxB,UAAI,UAAU;AACd,UAAI,OAAO;AAEX,UAAI,OAAO,SAAU,SAAS;AAC5B,eAAO,QAAQ,IAAI;AAAA;AAErB,UAAI,SAAS,SAAU,GAAG;AACxB,eAAO,SAAU,SAAS;AACxB,iBAAO,KAAK,aAAa;AAAA;AAAA;AAG7B,UAAI,YAAY,OAAO;AACvB,UAAI,SAAS,OAAO;AACpB,UAAI,aAAa,OAAO;AACxB,UAAI,qBAAqB,OAAO;AAEhC,UAAI,SAAS,SAAU,GAAG;AACxB,YAAI,SAAS;AACb,YAAI;AACJ,eAAO,WAAY;AACjB,cAAI,OAAO;AACX,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,iBAAK,MAAM,UAAU;AAAA;AAEvB,cAAI,CAAC,QAAQ;AACX,qBAAS;AACT,iBAAI,EAAE,MAAM,MAAM;AAAA;AAEpB,iBAAO;AAAA;AAAA;AAIX,UAAI,aAAa,SAAU,IAAI,SAAS,WAAW,aAAY;AAC7D,YAAI,SAAS,GAAG,WAAW,QAAQ,KAAK,eAAe;AACvD,YAAI,WAAW,GAAG,WAAW,CAAC;AAC9B,YAAI,WAAW,GAAG,WAAW,GAAG;AAChC,YAAI,UAAU,YAAY,YAAW;AACrC,YAAI,WAAW,UAAU,CAAC,YAAY,YAAY,YAAW;AAC7D,YAAI,UAAU,YAAY,YAAY,CAAC;AACvC,YAAI,aAAa,QAAQ,cAAc,GAAG,WAAW,UAAU,KAAK,eAAe;AACnF,YAAI,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;AAC1C,eAAO;AAAA,UACL,QAAQ,SAAS;AAAA,UACjB,UAAU,SAAS;AAAA,UACnB,UAAU,SAAS;AAAA,UACnB,SAAS,SAAS;AAAA,UAClB,SAAS,SAAS;AAAA,UAClB,WAAW,GAAG;AAAA,UACd,OAAO,GAAG;AAAA,UACV,WAAW,SAAS;AAAA,UACpB,WAAW,SAAS;AAAA;AAAA;AAIxB,UAAI,aAAa,SAAU,SAAS,GAAG;AACrC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,IAAI,QAAQ;AAChB,cAAI,EAAE,KAAK,IAAI;AACb,mBAAO;AAAA;AAAA;AAGX,eAAO;AAAA;AAET,UAAI,OAAO,SAAU,SAAS,OAAO;AACnC,YAAI,KAAI,WAAW,SAAS;AAC5B,YAAI,CAAC,IAAG;AACN,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO;AAAA;AAAA;AAGX,YAAI,QAAQ,SAAU,GAAG;AACvB,iBAAO,OAAO,MAAM,QAAQ,IAAG,MAAM;AAAA;AAEvC,eAAO,KAAK,MAAM,IAAI,MAAM;AAAA;AAE9B,UAAI,WAAW,SAAU,gBAAgB,OAAO;AAC9C,YAAI,eAAe,OAAO,OAAO;AACjC,YAAI,eAAe,WAAW,GAAG;AAC/B,iBAAO;AAAA;AAET,eAAO,KAAK,gBAAgB;AAAA;AAE9B,UAAI,YAAY,WAAY;AAC1B,eAAO,KAAK,GAAG;AAAA;AAEjB,UAAI,OAAO,SAAU,OAAO,OAAO;AACjC,eAAO;AAAA,UACL;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,UAAU;AAAA,QACZ,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA;AAGX,UAAI,kBAAkB,SAAU,WAAU,eAAe;AACvD,eAAO,QAAQ,cAAc,QAAQ,SAAU,SAAS;AACtD,cAAI,UAAU,QAAQ,MAAM;AAC5B,iBAAO,OAAO,WAAU,SAAU,SAAS;AACzC,gBAAI;AACJ,mBAAO,YAAc,OAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,aAChF,IAAI,SAAU,MAAM;AACrB,mBAAO;AAAA,cACL,SAAS,KAAK;AAAA,cACd,SAAS,QAAQ,GAAG,SAAS,QAAQ,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAM3D,UAAI,WAAW,SAAU,YAAY,WAAW;AAC9C,YAAI,QAAQ,OAAO,WAAW;AAC9B,eAAO,OAAO,YAAY,SAAU,WAAW;AAC7C,iBAAO,UAAU,OAAO;AAAA;AAAA;AAG5B,UAAI,gBAAgB,SAAU,WAAU,WAAW;AACjD,eAAO,SAAS,WAAU,WAAW,IAAI,SAAU,SAAS;AAC1D,cAAI,UAAU,QAAQ,OAAO,QAAQ,gBAAgB;AACrD,iBAAO;AAAA,YACL,SAAS,QAAQ;AAAA,YACjB;AAAA;AAAA;AAAA;AAIN,UAAI,WAAW,SAAU,OAAM,WAAW;AACxC,eAAO,SAAS,OAAM,WAAW,IAAI,SAAU,IAAI;AACjD,cAAI,UAAU,QAAQ,OAAO,GAAG,gBAAgB;AAChD,iBAAO;AAAA,YACL,SAAS,GAAG;AAAA,YACZ;AAAA;AAAA;AAAA;AAKN,UAAI,qBAAqB;AACzB,UAAI,gBAAgB,SAAU,QAAQ;AACpC,eAAO,SAAU,UAAU;AACzB,iBAAO,SAAS,UAAU;AAAA;AAAA;AAG9B,UAAI,WAAW;AAAA,QACb;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB,CAAC;AAAA,UACjB,QAAQ,SAAU,UAAU;AAC1B,mBAAO,SAAS,UAAU,YAAY,SAAS,UAAU,aAAa,SAAS,UAAU,aAAa,SAAS,UAAU;AAAA;AAAA;AAAA,QAG7H;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ,SAAU,UAAU;AAC1B,mBAAO,SAAS,UAAU,aAAa,CAAC,SAAS,UAAU;AAAA;AAAA;AAAA,QAG/D;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ,SAAU,UAAU;AAC1B,mBAAO,SAAS,UAAU,WAAW,SAAS,UAAU;AAAA;AAAA;AAAA,QAG5D;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ,cAAc;AAAA;AAAA,QAExB;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB,CAAC;AAAA,UACjB,QAAQ,cAAc;AAAA;AAAA,QAExB;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA;AAAA,UAEF,QAAQ,SAAU,UAAU;AAC1B,mBAAQ,UAAS,UAAU,aAAa,SAAS,UAAU,eAAe,SAAS,UAAU;AAAA;AAAA;AAAA;AAInG,UAAI,OAAO;AAAA,QACT;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB,CAAC;AAAA;AAAA,QAEnB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,SAAU,UAAU;AAC1B,mBAAO,SAAS,UAAU,aAAa,SAAS,UAAU;AAAA;AAAA,UAE5D,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA;AAAA;AAAA,QAGJ;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB,CAAC;AAAA;AAAA,QAEnB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB,CAAC;AAAA;AAAA,QAEnB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB;AAAA;AAAA,QAElB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB;AAAA;AAAA,QAElB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB;AAAA;AAAA,QAElB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,UACtB,gBAAgB,CAAC;AAAA;AAAA;AAGrB,UAAI,eAAe;AAAA,QACjB,UAAU,SAAS;AAAA,QACnB,MAAM,SAAS;AAAA;AAGjB,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,KAAK;AACT,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,SAAS;AACb,UAAI,YAAY,WAAY;AAC1B,eAAO,KAAK;AAAA,UACV,SAAS;AAAA,UACT,SAAS,QAAQ;AAAA;AAAA;AAGrB,UAAI,OAAO,SAAU,MAAM;AACzB,YAAI,UAAU,KAAK;AACnB,YAAI,UAAU,KAAK;AACnB,YAAI,YAAY,SAAU,MAAM;AAC9B,iBAAO,WAAY;AACjB,mBAAO,YAAY;AAAA;AAAA;AAGvB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ,UAAU;AAAA,UAClB,UAAU,UAAU;AAAA,UACpB,MAAM,UAAU;AAAA,UAChB,SAAS,UAAU;AAAA,UACnB,WAAW,UAAU;AAAA,UACrB,UAAU,UAAU;AAAA;AAAA;AAGxB,UAAI,UAAU;AAAA,QACZ,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,MAAM,SAAS;AAAA,QACf,QAAQ,SAAS;AAAA,QACjB,IAAI,SAAS;AAAA,QACb,OAAO,SAAS;AAAA,QAChB,SAAS,SAAS;AAAA,QAClB,QAAQ,SAAS;AAAA;AAGnB,UAAI,UAAU;AACd,UAAI,MAAM;AACV,UAAI,UAAU;AACd,UAAI,QAAQ;AACZ,UAAI,MAAM;AACV,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,UAAU,WAAY;AACxB,eAAO,GAAG;AAAA,UACR,SAAS;AAAA,UACT,SAAS,QAAQ;AAAA;AAAA;AAGrB,UAAI,KAAK,SAAU,MAAM;AACvB,YAAI,UAAU,KAAK;AACnB,YAAI,UAAU,KAAK;AACnB,YAAI,OAAO,SAAU,MAAM;AACzB,iBAAO,WAAY;AACjB,mBAAO,YAAY;AAAA;AAAA;AAGvB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,WAAW,KAAK;AAAA,UAChB,OAAO,KAAK;AAAA,UACZ,WAAW,KAAK;AAAA,UAChB,OAAO,KAAK;AAAA,UACZ,SAAS,KAAK;AAAA,UACd,WAAW,KAAK;AAAA,UAChB,WAAW,KAAK;AAAA,UAChB,YAAY,KAAK;AAAA;AAAA;AAGrB,UAAI,kBAAkB;AAAA,QACpB;AAAA,QACA;AAAA,QACA,SAAS,SAAS;AAAA,QAClB,KAAK,SAAS;AAAA,QACd,SAAS,SAAS;AAAA,QAClB,OAAO,SAAS;AAAA,QAChB,KAAK,SAAS;AAAA,QACd,SAAS,SAAS;AAAA,QAClB,SAAS,SAAS;AAAA,QAClB,UAAU,SAAS;AAAA;AAGrB,UAAI,WAAW,SAAU,WAAW,kBAAkB,aAAY;AAChE,YAAI,YAAW,aAAa;AAC5B,YAAI,QAAO,aAAa;AACxB,YAAI,UAAU,iBAAiB,KAAK,SAAU,eAAe;AAC3D,iBAAO,gBAAgB,WAAU;AAAA,WAChC,QAAQ,WAAY;AACrB,iBAAO,cAAc,WAAU;AAAA,WAC9B,KAAK,QAAQ,SAAS,QAAQ;AACjC,YAAI,KAAK,SAAS,OAAM,WAAW,KAAK,gBAAgB,SAAS,gBAAgB;AACjF,YAAI,aAAa,WAAW,IAAI,SAAS,WAAW;AACpD,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,oBAAoB,EAAE,QAAQ;AAElC,UAAI,aAAa,SAAU,OAAO;AAChC,eAAO,OAAO,WAAW,OAAO;AAAA;AAElC,UAAI,WAAW,OAAO,WAAY;AAChC,eAAO,kBAAkB,OAAO,UAAU,WAAW,SAAS,KAAK,UAAU,gBAAgB;AAAA;AAE/F,UAAI,SAAS,WAAY;AACvB,eAAO;AAAA;AAGT,UAAI,KAAK,SAAU,SAAS,UAAU;AACpC,YAAI,MAAM,QAAQ;AAClB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,eACF;AACL,cAAI,OAAO;AACX,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ;AAAA,qBACX,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB;AAAA,qBACrB,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB;AAAA,qBACzB,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB;AAAA,iBAC1B;AACL,kBAAM,IAAI,MAAM;AAAA;AAAA;AAAA;AAItB,UAAI,iBAAiB,SAAU,KAAK;AAClC,eAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AAAA;AAElI,UAAI,QAAQ,SAAU,UAAU,OAAO;AACrC,YAAI,OAAO,UAAU,SAAY,WAAW,MAAM;AAClD,eAAO,eAAe,QAAQ,KAAK,IAAI,KAAK,iBAAiB,WAAW,aAAa;AAAA;AAGvF,UAAI,KAAK,SAAU,IAAI,IAAI;AACzB,eAAO,GAAG,QAAQ,GAAG;AAAA;AAGvB,UAAI,QAAQ,SAAU,SAAS;AAC7B,eAAO,aAAa,QAAQ,QAAQ,IAAI;AAAA;AAE1C,UAAI,kBAAkB,SAAU,KAAK;AACnC,eAAO,WAAW,OAAO,MAAM,MAAM;AAAA;AAEvC,UAAI,SAAS,SAAU,SAAS;AAC9B,eAAO,SAAS,KAAK,QAAQ,IAAI,YAAY,IAAI,aAAa;AAAA;AAEhE,UAAI,UAAU,SAAU,SAAS,QAAQ;AACvC,YAAI,OAAO,WAAW,UAAU,SAAS;AACzC,YAAI,MAAM,QAAQ;AAClB,YAAI,MAAM;AACV,eAAO,IAAI,eAAe,QAAQ,IAAI,eAAe,QAAW;AAC9D,cAAI,YAAY,IAAI;AACpB,cAAI,IAAI,aAAa,QAAQ;AAC7B,cAAI,KAAK;AACT,cAAI,KAAK,OAAO,MAAM;AACpB;AAAA,iBACK;AACL,kBAAM;AAAA;AAAA;AAGV,eAAO;AAAA;AAET,UAAI,aAAa,SAAU,SAAS;AAClC,YAAI,aAAa,SAAU,UAAU;AACnC,iBAAO,SAAS,UAAU,SAAU,GAAG;AACrC,mBAAO,CAAC,GAAG,SAAS;AAAA;AAAA;AAGxB,eAAO,OAAO,SAAS,IAAI,UAAU,IAAI,YAAY,MAAM;AAAA;AAE7D,UAAI,WAAW,SAAU,SAAS;AAChC,eAAO,IAAI,QAAQ,IAAI,YAAY,aAAa;AAAA;AAGlD,UAAI,eAAe,SAAU,KAAK;AAChC,eAAO,mBAAmB,QAAQ,cAAc,IAAI,IAAI;AAAA;AAE1D,UAAI,YAAY,WAAW,QAAQ,UAAU,iBAAiB,WAAW,KAAK,UAAU;AACxF,UAAI,cAAc,SAAS;AAC3B,UAAI,cAAc,YAAY,SAAU,GAAG;AACzC,eAAO,aAAa,QAAQ,EAAE,IAAI;AAAA,UAChC;AACJ,UAAI,gBAAgB,SAAU,GAAG;AAC/B,YAAI,KAAI,YAAY;AACpB,eAAO,aAAa,MAAK,SAAS,KAAK,MAAK,SAAS;AAAA;AAEvD,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,aAAa,QAAQ,EAAE,IAAI;AAAA;AAEpC,UAAI,yBAAyB,SAAU,OAAO;AAC5C,YAAI,iBAAiB,cAAc,MAAM,SAAS;AAChD,cAAI,KAAK,aAAa,QAAQ,MAAM;AACpC,cAAI,UAAU,OAAO,iBAAiB,KAAK;AACzC,gBAAI,MAAM,YAAY,MAAM,cAAc;AACxC,kBAAI,eAAe,MAAM;AACzB,kBAAI,cAAc;AAChB,uBAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAKpB,eAAO,SAAS,KAAK,MAAM;AAAA;AAE7B,UAAI,mBAAmB,SAAU,SAAS;AACxC,eAAO,cAAc,QAAQ,IAAI;AAAA;AAGnC,UAAI,SAAS,SAAU,SAAS;AAC9B,YAAI,MAAM,OAAO,WAAW,QAAQ,IAAI,aAAa,QAAQ;AAC7D,YAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,iBAAO;AAAA;AAET,YAAI,MAAM,IAAI;AACd,eAAO,cAAc,aAAa,QAAQ,MAAM,KAAK,WAAY;AAC/D,iBAAO,IAAI,KAAK,SAAS;AAAA,WACxB,SAAS,QAAQ;AAAA;AAEtB,UAAI,UAAU,SAAU,KAAK;AAC3B,YAAI,IAAI,IAAI,IAAI;AAChB,YAAI,MAAM,QAAQ,MAAM,QAAW;AACjC,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,aAAa,QAAQ;AAAA;AAG9B,UAAI,SAAS,SAAU,KAAK,KAAK,QAAO;AACtC,YAAI,SAAS,WAAU,UAAU,WAAU,SAAS,SAAQ;AAC1D,cAAI,aAAa,KAAK,SAAQ;AAAA,eACzB;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAa,QAAO,eAAe;AAC7F,gBAAM,IAAI,MAAM;AAAA;AAAA;AAGpB,UAAI,MAAM,SAAU,SAAS,KAAK,QAAO;AACvC,eAAO,QAAQ,KAAK,KAAK;AAAA;AAE3B,UAAI,QAAQ,SAAU,SAAS,KAAK;AAClC,YAAI,IAAI,QAAQ,IAAI,aAAa;AACjC,eAAO,MAAM,OAAO,SAAY;AAAA;AAElC,UAAI,SAAS,SAAU,SAAS,KAAK;AACnC,gBAAQ,IAAI,gBAAgB;AAAA;AAG9B,UAAI,cAAc,SAAU,KAAK,UAAU,QAAO;AAChD,YAAI,CAAC,SAAS,SAAQ;AACpB,kBAAQ,MAAM,sCAAsC,UAAU,aAAa,QAAO,eAAe;AACjG,gBAAM,IAAI,MAAM,iCAAiC;AAAA;AAEnD,YAAI,cAAc,MAAM;AACtB,cAAI,MAAM,YAAY,UAAU;AAAA;AAAA;AAGpC,UAAI,SAAS,SAAU,SAAS,KAAK;AACnC,YAAI,MAAM,QAAQ;AAClB,aAAK,KAAK,SAAU,GAAG,GAAG;AACxB,sBAAY,KAAK,GAAG;AAAA;AAAA;AAGxB,UAAI,QAAQ,SAAU,SAAS,UAAU;AACvC,YAAI,MAAM,QAAQ;AAClB,YAAI,SAAS,OAAO,iBAAiB;AACrC,YAAI,KAAI,OAAO,iBAAiB;AAChC,eAAO,OAAM,MAAM,CAAC,OAAO,WAAW,kBAAkB,KAAK,YAAY;AAAA;AAE3E,UAAI,oBAAoB,SAAU,KAAK,UAAU;AAC/C,eAAO,cAAc,OAAO,IAAI,MAAM,iBAAiB,YAAY;AAAA;AAGrE,UAAI,UAAU,SAAU,QAAQ,GAAG,GAAG,MAAM,SAAS,MAAM,KAAK;AAC9D,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,eAAe,SAAU,UAAU;AACrC,YAAI,SAAS,aAAa,QAAQ,uBAAuB,UAAU,MAAM,SAAS;AAClF,YAAI,OAAO,WAAY;AACrB,iBAAO,SAAS;AAAA;AAElB,YAAI,UAAU,WAAY;AACxB,iBAAO,SAAS;AAAA;AAElB,YAAI,OAAO,QAAQ,SAAS;AAC5B,eAAO,QAAQ,QAAQ,SAAS,SAAS,SAAS,SAAS,MAAM,SAAS,MAAM;AAAA;AAElF,UAAI,SAAS,SAAU,SAAQ,SAAS;AACtC,eAAO,SAAU,UAAU;AACzB,cAAI,QAAO,WAAW;AACpB,oBAAQ,aAAa;AAAA;AAAA;AAAA;AAI3B,UAAI,SAAS,SAAU,SAAS,OAAO,SAAQ,SAAS,YAAY;AAClE,YAAI,UAAU,OAAO,SAAQ;AAC7B,gBAAQ,IAAI,iBAAiB,OAAO,SAAS;AAC7C,eAAO,EAAE,QAAQ,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA;AAE1D,UAAI,SAAS,SAAU,SAAS,OAAO,SAAQ,SAAS;AACtD,eAAO,OAAO,SAAS,OAAO,SAAQ,SAAS;AAAA;AAEjD,UAAI,SAAS,SAAU,SAAS,OAAO,SAAS,YAAY;AAC1D,gBAAQ,IAAI,oBAAoB,OAAO,SAAS;AAAA;AAGlD,UAAI,SAAS;AACb,UAAI,SAAS,SAAU,SAAS,OAAO,SAAS;AAC9C,eAAO,OAAO,SAAS,OAAO,QAAQ;AAAA;AAGxC,UAAI,IAAI,SAAU,MAAM,KAAK;AAC3B,YAAI,YAAY,SAAU,GAAG,GAAG;AAC9B,iBAAO,EAAE,OAAO,GAAG,MAAM;AAAA;AAE3B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAGJ,UAAI,gBAAgB;AAEpB,UAAI,QAAQ,SAAU,MAAM;AAC1B,YAAI,MAAM,SAAS,SAAY,KAAK,MAAM;AAC1C,YAAI,IAAI,IAAI,KAAK,cAAc,IAAI,gBAAgB;AACnD,YAAI,IAAI,IAAI,KAAK,aAAa,IAAI,gBAAgB;AAClD,eAAO,cAAc,GAAG;AAAA;AAG1B,UAAI,MAAM,SAAU,MAAM;AACxB,YAAI,MAAM,SAAS,SAAY,SAAS;AACxC,YAAI,SAAS,QAAQ,aAAa;AAChC,iBAAO,SAAS;AAAA,eACX;AACL,iBAAO,SAAS,KAAK,IAAI;AAAA;AAAA;AAG7B,UAAI,SAAS,SAAU,GAAG,GAAG,OAAO,QAAQ;AAC1C,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,IAAI;AAAA,UACX,QAAQ,IAAI;AAAA;AAAA;AAGhB,UAAI,YAAY,SAAU,MAAM;AAC9B,YAAI,MAAM,SAAS,SAAY,SAAS;AACxC,YAAI,MAAM,IAAI;AACd,YAAI,SAAS,MAAM,aAAa,QAAQ;AACxC,eAAO,IAAI,KAAK,KAAK,WAAY;AAC/B,cAAI,OAAO,IAAI,SAAS;AACxB,cAAI,QAAQ,KAAK;AACjB,cAAI,SAAS,KAAK;AAClB,iBAAO,OAAO,OAAO,MAAM,OAAO,KAAK,OAAO;AAAA,WAC7C,SAAU,gBAAgB;AAC3B,iBAAO,OAAO,KAAK,IAAI,eAAe,UAAU,OAAO,OAAO,KAAK,IAAI,eAAe,SAAS,OAAO,MAAM,eAAe,OAAO,eAAe;AAAA;AAAA;AAGrJ,UAAI,OAAO,SAAU,MAAM,UAAU,MAAM;AACzC,eAAO,IAAI,MAAM,IAAI,SAAU,gBAAgB;AAC7C,cAAI,UAAU,SAAU,GAAG;AACzB,mBAAO,SAAS,aAAa;AAAA;AAE/B,yBAAe,iBAAiB,MAAM;AACtC,iBAAO;AAAA,YACL,QAAQ,WAAY;AAClB,qBAAO,eAAe,oBAAoB,MAAM;AAAA;AAAA;AAAA,WAGnD,WAAW,WAAY;AACxB,iBAAO,EAAE,QAAQ;AAAA;AAAA;AAIrB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ;AAE1C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExC,UAAI,6BAA6B,SAAU,QAAQ,OAAO;AACxD,eAAO,KAAK,0BAA0B,EAAE;AACxC,eAAO,KAAK;AAAA;AAGd,UAAI,sBAAsB,SAAU,QAAQ;AAC1C,eAAO,OAAO,SAAS,qBAAqB,OAAO;AAAA;AAGrD,UAAI,oBAAoB,SAAU,QAAQ;AACxC,YAAI,OAAO,aAAa,QAAQ,OAAO;AACvC,eAAO,cAAc,MAAM,IAAI,eAAe,WAAW,WAAY;AACnE,iBAAO,QAAQ,MAAM;AAAA;AAAA;AAGzB,UAAI,uBAAuB,SAAU,MAAM;AACzC,YAAI,KAAK,sBAAsB,QAAW;AACxC,iBAAO,KAAK;AAAA,mBACH,KAAK,wBAAwB,QAAW;AACjD,iBAAO,KAAK;AAAA,mBACH,KAAK,4BAA4B,QAAW;AACrD,iBAAO,KAAK;AAAA,eACP;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,+BAA+B,WAAY;AAC7C,YAAI,SAAS,sBAAsB,QAAW;AAC5C,iBAAO;AAAA,mBACE,SAAS,wBAAwB,QAAW;AACrD,iBAAO;AAAA,mBACE,SAAS,4BAA4B,QAAW;AACzD,iBAAO;AAAA,eACF;AACL,iBAAO;AAAA;AAAA;AAGX,UAAI,oBAAoB,SAAU,WAAW;AAC3C,YAAI,OAAO,UAAU;AACrB,YAAI,KAAK,mBAAmB;AAC1B,eAAK;AAAA,mBACI,KAAK,qBAAqB;AACnC,eAAK;AAAA,mBACI,KAAK,yBAAyB;AACvC,eAAK;AAAA;AAAA;AAGT,UAAI,iBAAiB,SAAU,UAAU;AACvC,YAAI,MAAM,SAAS;AACnB,YAAI,IAAI,gBAAgB;AACtB,cAAI;AAAA,mBACK,IAAI,kBAAkB;AAC/B,cAAI;AAAA,mBACK,IAAI,wBAAwB;AACrC,cAAI;AAAA;AAAA;AAGR,UAAI,sBAAsB,SAAU,MAAM;AACxC,eAAO,KAAK,QAAQ,qBAAqB,MAAM,MAAM;AAAA;AAGvD,UAAI,cAAc,SAAU,OAAO,WAAW,QAAQ;AACpD,eAAO,SAAS,QAAQ,OAAO,SAAS;AAAA;AAE1C,UAAI,aAAa,SAAU,OAAO,WAAW;AAC3C,eAAO,SAAS,WAAW,QAAQ;AAAA;AAGrC,UAAI,MAAM,SAAU,UAAU;AAC5B,eAAO,MAAM;AAAA;AAEf,UAAI,YAAY,SAAU,OAAO,UAAU,QAAQ;AACjD,eAAO,YAAY,OAAO,SAAU,GAAG;AACrC,iBAAO,GAAG,GAAG;AAAA,WACZ;AAAA;AAEL,UAAI,WAAW,SAAU,OAAO,UAAU;AACxC,eAAO,WAAW,OAAO,SAAU,GAAG;AACpC,iBAAO,GAAG,GAAG;AAAA;AAAA;AAIjB,UAAI,OAAO;AACX,UAAI,gBAAgB;AACpB,UAAI,mBAAmB;AACvB,UAAI,iBAAiB;AACrB,UAAI,aAAa;AACjB,UAAI,YAAY,SAAS,GAAG;AAC5B,UAAI,aAAa,SAAU,YAAY;AACrC,YAAI,QAAQ,MAAM,YAAY;AAC9B,eAAO,UAAU,UAAa,UAAU,KAAK,sBAAsB,QAAQ,eAAe;AAAA;AAE5F,UAAI,gBAAgB,SAAU,KAAK,WAAW,YAAY;AACxD,YAAI,iBAAiB,SAAU,SAAS;AACtC,iBAAO,SAAS,SAAS;AAAA;AAE3B,YAAI,UAAU,SAAU,cAAc;AACpC,iBAAO,SAAU,SAAS;AACxB,gBAAI,SAAS,MAAM,SAAS;AAC5B,gBAAI,SAAS,WAAW,SAAY,cAAc,OAAO;AACzD,gBAAI,WAAW,cAAc;AAC3B;AAAA,mBACK;AACL,kBAAI,SAAS,MAAM;AACnB,qBAAO,SAAS,IAAI,WAAW;AAAA;AAAA;AAAA;AAIrC,YAAI,eAAc,UAAU,WAAW;AACvC,YAAI,cAAa,OAAO,cAAa;AACrC,YAAI,UAAU,WAAW;AACzB,eAAO,aAAY,QAAQ;AAC3B,eAAO,cAAa,QAAQ,mBAAmB,iBAAiB;AAChE,YAAI,kBAAkB,cAAc,OAAO,KAAK;AAChD,gBAAQ,kBAAkB,iBAAiB,SAAS;AAAA;AAEtD,UAAI,gBAAgB,SAAU,KAAK;AACjC,YAAI,eAAe,IAAI,MAAM,OAAO;AACpC,eAAO,cAAc,SAAU,SAAS;AACtC,cAAI,UAAU,MAAM,SAAS;AAC7B,cAAI,YAAY,aAAa;AAC3B,mBAAO,SAAS,IAAI,WAAW;AAAA,iBAC1B;AACL,mBAAO,SAAS;AAAA;AAElB,iBAAO,SAAS;AAAA;AAAA;AAIpB,UAAI,MAAM,SAAS;AACnB,UAAI,eAAe,WAAY;AAC7B,eAAO,UAAU;AAAA;AAEnB,UAAI,eAAe,SAAU,KAAK;AAChC,eAAO,OAAO,SAAS,IAAI,GAAG,IAAI;AAAA;AAEpC,UAAI,iBAAiB,MAAM,KAAK,WAAY;AAC1C,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA;AAAA,SAET,SAAU,gBAAgB;AAC3B,YAAI,kBAAkB;AACtB,YAAI,eAAe;AACnB,YAAI,eAAe;AACnB,YAAI,gBAAgB,WAAY;AAC9B,mBAAS,KAAK,YAAY;AAC1B,mBAAS,gBAAgB,YAAY;AAAA;AAEvC,YAAI,wBAAwB,WAAY;AACtC,iBAAO,sBAAsB,WAAY;AACvC,4BAAgB,GAAG,SAAU,WAAW;AACtC,qBAAO,OAAO,WAAW;AAAA,gBACvB,KAAK,eAAe,YAAY;AAAA,gBAChC,MAAM,eAAe,aAAa;AAAA,gBAClC,QAAQ,eAAe,SAAS;AAAA,gBAChC,OAAO,eAAe,QAAQ;AAAA;AAAA;AAAA;AAAA;AAKtC,YAAI,SAAS,OAAO,SAAS,WAAY;AACvC;AACA;AAAA,WACC;AACH,YAAI,UAAS,SAAU,SAAS;AAC9B,0BAAgB,IAAI;AACpB;AACA,uBAAa,IAAI,KAAK,UAAU;AAChC,uBAAa,IAAI,KAAK,UAAU;AAAA;AAElC,YAAI,UAAS,WAAY;AACvB,0BAAgB,GAAG,WAAY;AAC7B,yBAAa;AACb,yBAAa;AAAA;AAEf,0BAAgB;AAAA;AAElB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA;AAAA;AAGZ,UAAI,mBAAmB,SAAU,QAAQ,iBAAiB;AACxD,YAAI,OAAO,SAAS;AACpB,YAAI,kBAAkB,SAAS;AAC/B,YAAI,kBAAkB,OAAO;AAC7B,YAAI,mBAAmB,aAAa,QAAQ;AAC5C,YAAI,iBAAiB,kBAAkB;AACvC,YAAI,iBAAiB,gBAAgB;AACrC,YAAI,aAAa,aAAa,QAAQ,OAAO;AAC7C,YAAI,UAAU,SAAS,WAAW;AAClC,YAAI,uBAAuB,gBAAgB;AAC3C,YAAI,SAAS,OAAO;AACpB,YAAI,cAAc,OAAO;AACzB,YAAI,gBAAgB,SAAU,SAAS;AACrC,kBAAQ,MAAM;AACd,kBAAQ,iBAAiB;AACzB,kBAAQ,iBAAiB;AACzB,wBAAc,kBAAkB,IAAI,SAAU,MAAM;AAClD,mBAAO,cAAc,MAAM;AAAA,aAC1B,KAAK,SAAU,MAAM;AACtB,oBAAQ,MAAM;AACd,oBAAQ,MAAM;AAAA;AAAA;AAGlB,YAAI,UAAU,WAAY;AACxB,cAAI,SAAS;AACX,0BAAc,OAAO;AAAA;AAEvB,wBAAc,IAAI;AAClB,yBAAe;AACf,mBAAS,KAAK,gBAAgB,OAAO,KAAK,SAAU,MAAM;AACxD,mBAAO,KAAK,wBAAwB;AAAA;AAAA;AAGxC,YAAI,CAAC,gBAAgB;AACnB,cAAI,0BAA0B,OAAO,MAAM,iBAAiB,gCAAgC,SAAU,MAAM;AAC1G,gBAAI,oBAAoB,SAAS;AAC/B,kBAAI,CAAC,oBAAoB,mBAAmB,gBAAgB,UAAU,MAAM;AAC1E,iCAAiB,QAAQ;AAAA;AAAA;AAAA;AAI/B,cAAI,oBAAoB;AAAA,YACtB,WAAW;AAAA,YACX,gBAAgB,qBAAqB;AAAA,YACrC,iBAAiB,qBAAqB;AAAA,YACtC,cAAc,qBAAqB;AAAA,YACnC,eAAe,qBAAqB;AAAA,YACpC,aAAa,YAAY;AAAA,YACzB,cAAc,YAAY;AAAA,YAC1B;AAAA;AAEF,cAAI,SAAS;AACX,0BAAc,OAAO,KAAK,kBAAkB;AAAA;AAE9C,sBAAY,QAAQ,YAAY,SAAS;AACzC,+BAAqB,QAAQ,qBAAqB,SAAS;AAC3D,wBAAc,IAAI;AAClB,yBAAe,KAAK;AACpB,iBAAO,GAAG,UAAU;AACpB,0BAAgB,IAAI;AACpB,cAAI,oBAAoB,SAAS;AAC/B,8BAAkB;AAAA;AAEpB,qCAA2B,QAAQ;AAAA,eAC9B;AACL,yBAAe,wBAAwB;AACvC,cAAI,oBAAoB,WAAW,oBAAoB,iBAAiB;AACtE,2BAAe,MAAM;AAAA;AAEvB,sBAAY,QAAQ,eAAe;AACnC,sBAAY,SAAS,eAAe;AACpC,+BAAqB,QAAQ,eAAe;AAC5C,+BAAqB,SAAS,eAAe;AAC7C,+BAAqB,MAAM,eAAe;AAC1C,+BAAqB,OAAO,eAAe;AAC3C;AACA,uBAAa,eAAe;AAC5B,0BAAgB,IAAI;AACpB,qCAA2B,QAAQ;AACnC,iBAAO,IAAI,UAAU;AAAA;AAAA;AAIzB,UAAI,aAAa,SAAU,QAAQ,iBAAiB;AAClD,eAAO,WAAW,iBAAiB,WAAY;AAC7C,2BAAiB,QAAQ;AAAA;AAAA;AAI7B,UAAI,mBAAmB,SAAU,QAAQ,iBAAiB;AACxD,eAAO,SAAU,KAAK;AACpB,cAAI,UAAU,gBAAgB,UAAU;AACxC,cAAI,sBAAsB,SAAU,GAAG;AACrC,mBAAO,IAAI,UAAU,EAAE;AAAA;AAEzB,iBAAO,GAAG,0BAA0B;AACpC,iBAAO,WAAY;AACjB,mBAAO,OAAO,IAAI,0BAA0B;AAAA;AAAA;AAAA;AAIlD,UAAI,WAAW,SAAU,QAAQ,iBAAiB;AAChD,YAAI,WAAW,WAAY;AACzB,iBAAO,OAAO,YAAY;AAAA;AAE5B,eAAO,GAAG,SAAS,kBAAkB,cAAc;AAAA,UACjD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV;AAAA,UACA,SAAS,iBAAiB,QAAQ;AAAA;AAEpC,eAAO,GAAG,SAAS,gBAAgB,cAAc;AAAA,UAC/C,SAAS;AAAA,UACT,MAAM;AAAA,UACN;AAAA,UACA,SAAS,iBAAiB,QAAQ;AAAA;AAAA;AAItC,wBAAmB;AACjB,iBAAS,IAAI,cAAc,SAAU,QAAQ;AAC3C,cAAI,kBAAkB,KAAK;AAC3B,cAAI,OAAO,QAAQ;AACjB,mBAAO,MAAM;AAAA;AAEf,qBAAW,QAAQ;AACnB,mBAAS,QAAQ;AACjB,iBAAO,YAAY,gBAAgB,IAAI;AACvC,iBAAO,MAAM;AAAA;AAAA;AAIjB;AAAA;AAAA;AAAA;;;ACh0CJ;AAAA;AAMA;AAAA;AAAA;;;ACNA,IAAO,qCAAQ;", "names": []}