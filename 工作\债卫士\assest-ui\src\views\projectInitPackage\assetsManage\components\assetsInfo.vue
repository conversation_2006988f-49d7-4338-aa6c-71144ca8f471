<template>
    <div class="content-area">
        <el-form :model="form" :rules="rules" label-width="auto">
            <el-form-item label="资产包名称" prop="assetsName">
                <el-input v-model="form.assetsName" style="width:450px" placeholder="请输入资产包名称" />
            </el-form-item>
            <el-form-item label="关联项目" prop="projectName">
                <el-select v-model="form.projectName" style="width:450px" placeholder="请选择关联项目">
                    <el-option v-for="v in projectOption" :key="v" :label="v.label" :value="v.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="备注信息" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" placeholder="请输入备注信息" maxlength="500" :rows="5" style="width:450px" />
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup>
const data = reactive({
    form: {},
    rules: {
        assetsName: [{ required: true, message: '请输入资产包名称', trigger: 'blur' }],
        projectName: [{ required: true, message: '请选择关联项目', trigger: 'blur' }],
    },
})
const projectOption = ref([
    { label: '湖消第二批不良转让项目001009' },
    { label: '湖消第二批不良转让项目001019' },
])
const { form, rules } = toRefs(data)
</script>

<style lang="scss" scoped>
.content-area {
    width: 45%;
    margin: 0 auto;
}
</style>