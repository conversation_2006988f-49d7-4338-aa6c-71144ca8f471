import {
  __commonJS
} from "./chunk-WC6BDPVA.js";

// node_modules/tinymce/plugins/directionality/plugin.js
var require_plugin = __commonJS({
  "node_modules/tinymce/plugins/directionality/plugin.js"() {
    (function() {
      "use strict";
      var global = tinymce.util.Tools.resolve("tinymce.PluginManager");
      var typeOf = function(x) {
        var t = typeof x;
        if (x === null) {
          return "null";
        } else if (t === "object" && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === "Array")) {
          return "array";
        } else if (t === "object" && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === "String")) {
          return "string";
        } else {
          return t;
        }
      };
      var isType$1 = function(type2) {
        return function(value) {
          return typeOf(value) === type2;
        };
      };
      var isSimpleType = function(type2) {
        return function(value) {
          return typeof value === type2;
        };
      };
      var isString = isType$1("string");
      var isBoolean = isSimpleType("boolean");
      var isNullable = function(a) {
        return a === null || a === void 0;
      };
      var isNonNullable = function(a) {
        return !isNullable(a);
      };
      var isFunction = isSimpleType("function");
      var isNumber = isSimpleType("number");
      var noop = function() {
      };
      var compose1 = function(fbc, fab) {
        return function(a) {
          return fbc(fab(a));
        };
      };
      var constant = function(value) {
        return function() {
          return value;
        };
      };
      var identity = function(x) {
        return x;
      };
      var never = constant(false);
      var always = constant(true);
      var none = function() {
        return NONE;
      };
      var NONE = function() {
        var call = function(thunk) {
          return thunk();
        };
        var id = identity;
        var me = {
          fold: function(n, _s) {
            return n();
          },
          isSome: never,
          isNone: always,
          getOr: id,
          getOrThunk: call,
          getOrDie: function(msg) {
            throw new Error(msg || "error: getOrDie called on none.");
          },
          getOrNull: constant(null),
          getOrUndefined: constant(void 0),
          or: id,
          orThunk: call,
          map: none,
          each: noop,
          bind: none,
          exists: never,
          forall: always,
          filter: function() {
            return none();
          },
          toArray: function() {
            return [];
          },
          toString: constant("none()")
        };
        return me;
      }();
      var some = function(a) {
        var constant_a = constant(a);
        var self = function() {
          return me;
        };
        var bind = function(f) {
          return f(a);
        };
        var me = {
          fold: function(n, s) {
            return s(a);
          },
          isSome: always,
          isNone: never,
          getOr: constant_a,
          getOrThunk: constant_a,
          getOrDie: constant_a,
          getOrNull: constant_a,
          getOrUndefined: constant_a,
          or: self,
          orThunk: self,
          map: function(f) {
            return some(f(a));
          },
          each: function(f) {
            f(a);
          },
          bind,
          exists: bind,
          forall: bind,
          filter: function(f) {
            return f(a) ? me : NONE;
          },
          toArray: function() {
            return [a];
          },
          toString: function() {
            return "some(" + a + ")";
          }
        };
        return me;
      };
      var from = function(value) {
        return value === null || value === void 0 ? NONE : some(value);
      };
      var Optional = {
        some,
        none,
        from
      };
      var map = function(xs, f) {
        var len = xs.length;
        var r = new Array(len);
        for (var i = 0; i < len; i++) {
          var x = xs[i];
          r[i] = f(x, i);
        }
        return r;
      };
      var each = function(xs, f) {
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          f(x, i);
        }
      };
      var filter = function(xs, pred) {
        var r = [];
        for (var i = 0, len = xs.length; i < len; i++) {
          var x = xs[i];
          if (pred(x, i)) {
            r.push(x);
          }
        }
        return r;
      };
      var DOCUMENT = 9;
      var DOCUMENT_FRAGMENT = 11;
      var ELEMENT = 1;
      var TEXT = 3;
      var fromHtml = function(html, scope) {
        var doc = scope || document;
        var div = doc.createElement("div");
        div.innerHTML = html;
        if (!div.hasChildNodes() || div.childNodes.length > 1) {
          console.error("HTML does not have a single root node", html);
          throw new Error("HTML must have a single root node");
        }
        return fromDom(div.childNodes[0]);
      };
      var fromTag = function(tag, scope) {
        var doc = scope || document;
        var node = doc.createElement(tag);
        return fromDom(node);
      };
      var fromText = function(text, scope) {
        var doc = scope || document;
        var node = doc.createTextNode(text);
        return fromDom(node);
      };
      var fromDom = function(node) {
        if (node === null || node === void 0) {
          throw new Error("Node cannot be null or undefined");
        }
        return { dom: node };
      };
      var fromPoint = function(docElm, x, y) {
        return Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);
      };
      var SugarElement = {
        fromHtml,
        fromTag,
        fromText,
        fromDom,
        fromPoint
      };
      var is = function(element, selector) {
        var dom = element.dom;
        if (dom.nodeType !== ELEMENT) {
          return false;
        } else {
          var elem = dom;
          if (elem.matches !== void 0) {
            return elem.matches(selector);
          } else if (elem.msMatchesSelector !== void 0) {
            return elem.msMatchesSelector(selector);
          } else if (elem.webkitMatchesSelector !== void 0) {
            return elem.webkitMatchesSelector(selector);
          } else if (elem.mozMatchesSelector !== void 0) {
            return elem.mozMatchesSelector(selector);
          } else {
            throw new Error("Browser lacks native selectors");
          }
        }
      };
      typeof window !== "undefined" ? window : Function("return this;")();
      var name = function(element) {
        var r = element.dom.nodeName;
        return r.toLowerCase();
      };
      var type = function(element) {
        return element.dom.nodeType;
      };
      var isType = function(t) {
        return function(element) {
          return type(element) === t;
        };
      };
      var isElement = isType(ELEMENT);
      var isText = isType(TEXT);
      var isDocument = isType(DOCUMENT);
      var isDocumentFragment = isType(DOCUMENT_FRAGMENT);
      var isTag = function(tag) {
        return function(e) {
          return isElement(e) && name(e) === tag;
        };
      };
      var owner = function(element) {
        return SugarElement.fromDom(element.dom.ownerDocument);
      };
      var documentOrOwner = function(dos) {
        return isDocument(dos) ? dos : owner(dos);
      };
      var parent = function(element) {
        return Optional.from(element.dom.parentNode).map(SugarElement.fromDom);
      };
      var children$2 = function(element) {
        return map(element.dom.childNodes, SugarElement.fromDom);
      };
      var rawSet = function(dom, key, value) {
        if (isString(value) || isBoolean(value) || isNumber(value)) {
          dom.setAttribute(key, value + "");
        } else {
          console.error("Invalid call to Attribute.set. Key ", key, ":: Value ", value, ":: Element ", dom);
          throw new Error("Attribute value was not simple");
        }
      };
      var set = function(element, key, value) {
        rawSet(element.dom, key, value);
      };
      var remove = function(element, key) {
        element.dom.removeAttribute(key);
      };
      var isShadowRoot = function(dos) {
        return isDocumentFragment(dos) && isNonNullable(dos.dom.host);
      };
      var supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);
      var getRootNode = supported ? function(e) {
        return SugarElement.fromDom(e.dom.getRootNode());
      } : documentOrOwner;
      var getShadowRoot = function(e) {
        var r = getRootNode(e);
        return isShadowRoot(r) ? Optional.some(r) : Optional.none();
      };
      var getShadowHost = function(e) {
        return SugarElement.fromDom(e.dom.host);
      };
      var inBody = function(element) {
        var dom = isText(element) ? element.dom.parentNode : element.dom;
        if (dom === void 0 || dom === null || dom.ownerDocument === null) {
          return false;
        }
        var doc = dom.ownerDocument;
        return getShadowRoot(SugarElement.fromDom(dom)).fold(function() {
          return doc.body.contains(dom);
        }, compose1(inBody, getShadowHost));
      };
      var ancestor$1 = function(scope, predicate, isRoot) {
        var element = scope.dom;
        var stop = isFunction(isRoot) ? isRoot : never;
        while (element.parentNode) {
          element = element.parentNode;
          var el = SugarElement.fromDom(element);
          if (predicate(el)) {
            return Optional.some(el);
          } else if (stop(el)) {
            break;
          }
        }
        return Optional.none();
      };
      var ancestor = function(scope, selector, isRoot) {
        return ancestor$1(scope, function(e) {
          return is(e, selector);
        }, isRoot);
      };
      var isSupported = function(dom) {
        return dom.style !== void 0 && isFunction(dom.style.getPropertyValue);
      };
      var get = function(element, property) {
        var dom = element.dom;
        var styles = window.getComputedStyle(dom);
        var r = styles.getPropertyValue(property);
        return r === "" && !inBody(element) ? getUnsafeProperty(dom, property) : r;
      };
      var getUnsafeProperty = function(dom, property) {
        return isSupported(dom) ? dom.style.getPropertyValue(property) : "";
      };
      var getDirection = function(element) {
        return get(element, "direction") === "rtl" ? "rtl" : "ltr";
      };
      var children$1 = function(scope, predicate) {
        return filter(children$2(scope), predicate);
      };
      var children = function(scope, selector) {
        return children$1(scope, function(e) {
          return is(e, selector);
        });
      };
      var getParentElement = function(element) {
        return parent(element).filter(isElement);
      };
      var getNormalizedBlock = function(element, isListItem2) {
        var normalizedElement = isListItem2 ? ancestor(element, "ol,ul") : Optional.some(element);
        return normalizedElement.getOr(element);
      };
      var isListItem = isTag("li");
      var setDir = function(editor, dir) {
        var selectedBlocks = editor.selection.getSelectedBlocks();
        if (selectedBlocks.length > 0) {
          each(selectedBlocks, function(block) {
            var blockElement = SugarElement.fromDom(block);
            var isBlockElementListItem = isListItem(blockElement);
            var normalizedBlock = getNormalizedBlock(blockElement, isBlockElementListItem);
            var normalizedBlockParent = getParentElement(normalizedBlock);
            normalizedBlockParent.each(function(parent2) {
              var parentDirection = getDirection(parent2);
              if (parentDirection !== dir) {
                set(normalizedBlock, "dir", dir);
              } else if (getDirection(normalizedBlock) !== dir) {
                remove(normalizedBlock, "dir");
              }
              if (isBlockElementListItem) {
                var listItems = children(normalizedBlock, "li[dir]");
                each(listItems, function(listItem) {
                  return remove(listItem, "dir");
                });
              }
            });
          });
          editor.nodeChanged();
        }
      };
      var register$1 = function(editor) {
        editor.addCommand("mceDirectionLTR", function() {
          setDir(editor, "ltr");
        });
        editor.addCommand("mceDirectionRTL", function() {
          setDir(editor, "rtl");
        });
      };
      var getNodeChangeHandler = function(editor, dir) {
        return function(api) {
          var nodeChangeHandler = function(e) {
            var element = SugarElement.fromDom(e.element);
            api.setActive(getDirection(element) === dir);
          };
          editor.on("NodeChange", nodeChangeHandler);
          return function() {
            return editor.off("NodeChange", nodeChangeHandler);
          };
        };
      };
      var register = function(editor) {
        editor.ui.registry.addToggleButton("ltr", {
          tooltip: "Left to right",
          icon: "ltr",
          onAction: function() {
            return editor.execCommand("mceDirectionLTR");
          },
          onSetup: getNodeChangeHandler(editor, "ltr")
        });
        editor.ui.registry.addToggleButton("rtl", {
          tooltip: "Right to left",
          icon: "rtl",
          onAction: function() {
            return editor.execCommand("mceDirectionRTL");
          },
          onSetup: getNodeChangeHandler(editor, "rtl")
        });
      };
      function Plugin() {
        global.add("directionality", function(editor) {
          register$1(editor);
          register(editor);
        });
      }
      Plugin();
    })();
  }
});

// node_modules/tinymce/plugins/directionality/index.js
var require_directionality = __commonJS({
  "node_modules/tinymce/plugins/directionality/index.js"() {
    require_plugin();
  }
});

// dep:tinymce_plugins_directionality
var tinymce_plugins_directionality_default = require_directionality();
export {
  tinymce_plugins_directionality_default as default
};
//# sourceMappingURL=tinymce_plugins_directionality.js.map
